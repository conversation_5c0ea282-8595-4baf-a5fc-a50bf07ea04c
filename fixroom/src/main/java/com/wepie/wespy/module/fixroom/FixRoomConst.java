package com.wepie.wespy.module.fixroom;

import com.wepie.wespy.net.tcp.packet.FixRoomPackets;

// Created by <PERSON>wen on 2018/7/17.
public class FixRoomConst {

    public static final int ADD_MEMBER = 1;
    public static final int KICK_MEMBER = 2;
    public static final int APPLY_MEMBER = 3;

    public static final int ROOM_ALLOW = 0;
    public static final int ROOM_UNALLOW = 1;

    public static final int ROOM_STATUS_READY = FixRoomPackets.FixGameState.INIT_VALUE;
    public static final int ROOM_STATUS_GAMING = FixRoomPackets.FixGameState.GAME_STARTED_VALUE;

    public static final int FREE_TALK_ON = 0;
    public static final int FREE_TALK_OFF = 1;

    public static final int UNITY_OP_MATCH = 1;
    public static final int UNITY_OP_SEARCH = 2;
    public static final int UNITY_OP_NEW_ROOM = 3;
    public static final int UNITY_OP_EXIT_ROOM = 4;
    public static final int UNITY_OP_SIT_DOWN = 5;
    public static final int UNITY_OP_READY = 6;
    public static final int UNITY_OP_PICK_UP = 7;
    public static final int UNITY_OP_STAND_UP = 8;
    public static final int UNITY_OP_KICK = 9;
    public static final int UNITY_OP_UNREADY = 10;
}
