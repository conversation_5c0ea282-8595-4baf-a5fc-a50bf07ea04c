package com.wepie.wespy.module.fixroom;

import android.content.res.Configuration;

import androidx.annotation.Nullable;

import com.huiwan.component.gift.show.GiftShowInfo;
import com.wepie.lib.api.plugins.voice.SpeakerInfo;
import com.wepie.wespy.model.entity.fixroom.FixRoomInfo;
import com.wepie.wespy.model.entity.fixroom.FixRoomMsg;
import com.wepie.wespy.module.spy.SocialGameMsgCallback;

import java.util.List;

/**
 *  Created by bigwen on 2018/10/23.
 *  社交游戏GameView，实现本接口
 */
public interface ISocialGameView {
    void onPushRoomMsg(List<FixRoomMsg> chatMsgs);
    void onSelfSendMsg(FixRoomMsg msg);
    void updateRoomInfo(FixRoomInfo fixRoomInfo);
    void setCallback(SocialGameMsgCallback socialGameMsgCallback);
    void hideDialog();
    void onResume();
    void onPause(boolean isFinish);
    boolean consumeGift(GiftShowInfo showInfo);
    void listRefresh();
    void onSpeak(List<SpeakerInfo> speakerInfos);
    List<FixRoomMsg> getPublicScreenMsg();
    @Nullable
    Object getGameInfo();
    void onConfigurationChanged(Configuration newConfig);
    void onClear();
    void addInParentView(boolean isOnCreate);

    boolean onBackPress();
}
