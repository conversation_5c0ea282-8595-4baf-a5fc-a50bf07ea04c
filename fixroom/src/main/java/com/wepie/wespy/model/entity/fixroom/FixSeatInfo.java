package com.wepie.wespy.model.entity.fixroom;

import com.wepie.wespy.net.tcp.packet.FixRoomPackets;

import java.util.ArrayList;
import java.util.List;

// Created by <PERSON>wen on 2018/7/16.
public class FixSeatInfo {

    private int uid;
    private int seat_state;
    private int bean_count;

    public static final int SEAT_DEFAULT = FixRoomPackets.SeatState.SEAT_STATE_UNDEFINED_VALUE; //0
    public static final int SEAT_UNREADY = FixRoomPackets.SeatState.STATE_UNREADY_VALUE; //1
    public static final int SEAT_READY = FixRoomPackets.SeatState.STATE_READY_VALUE; //2
    public static final int SEAT_QUIT_GAME = FixRoomPackets.SeatState.STATE_QUIT_GAME_VALUE; //3

    public static FixSeatInfo parse(FixRoomPackets.FixSeatInfo fixSeatInfo) {
        FixSeatInfo seatInfo = new FixSeatInfo();
        seatInfo.setUid(fixSeatInfo.getUid());
        seatInfo.setSeat_state(fixSeatInfo.getSeatState());
        seatInfo.setBean_count(fixSeatInfo.getBeanCount());
        return seatInfo;
    }

    public static List<FixSeatInfo> parseList(List<FixRoomPackets.FixSeatInfo> fixSeatInfoList) {
        List<FixSeatInfo> seatInfoList = new ArrayList<>();
        for (FixRoomPackets.FixSeatInfo seatInfo : fixSeatInfoList) {
            seatInfoList.add(parse(seatInfo));
        }
        return seatInfoList;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public void setSeat_state(int seat_state) {
        this.seat_state = seat_state;
    }

    public int getUid() {
        return uid;
    }

    public boolean isEmpty() {
        return uid <= 0;
    }

    public boolean isUnReady() {
        return seat_state == SEAT_UNREADY && !isEmpty();
    }

    public boolean isReady() {
        return seat_state == SEAT_READY && !isEmpty();
    }

    public boolean isUidSeat(int targetUid) {
        return targetUid == uid;
    }

    public int getBean_count() {
        return bean_count;
    }

    public void setBean_count(int bean_count) {
        this.bean_count = bean_count;
    }

    public boolean isQuitGame() {
        return seat_state == SEAT_QUIT_GAME && !isEmpty();
    }
}
