package com.wepie.wespy.model.entity.fixroom;

import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.google.gson.reflect.TypeToken;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.StringUtil;
import com.huiwan.constants.GameType;
import com.huiwan.constants.GameTypeUtil;
import com.huiwan.fixroom.avatarwolf.AvatarWolfUtil;
import com.huiwan.store.database.SmartModel;
import com.huiwan.user.LoginHelper;
import com.wepie.wespy.module.fixroom.FixRoomConst;
import com.wepie.wespy.net.tcp.packet.FixRoomPackets;
import com.wepie.wespy.net.tcp.packet.ProtoDrawPackets;

import java.util.ArrayList;
import java.util.List;

// Created by bigwen on 2018/7/16.
public class FixRoomInfo extends SmartModel {

    public static final String FIELD_MUSIC_CATE = "music_cate";
    public static final String FIELD_VOICE_TYPE = "voice_type";
    public static final String FIELD_HIGH_QUALITY = "high_quality";

    public static final int GAME_TYPE_LANDLORD = GameType.GAME_TYPE_LANDLORD;
    public static final int GAME_TYPE_AB_SPY_TEXTING = GameType.GAME_TYPE_AB_SPY_TEXTING;
    public static final int GAME_TYPE_AB_SPY_VOICING = GameType.GAME_TYPE_AB_SPY_VOICING;
    public static final int GAME_TYPE_BLANK_SPY_TEXTING = GameType.GAME_TYPE_BLANK_SPY_TEXTING;
    public static final int GAME_TYPE_BLANK_SPY_VOICING = GameType.GAME_TYPE_BLANK_SPY_VOICING;
    public static final int GAME_TYPE_BLANK_SPY_QA = GameType.GAME_TYPE_BLANK_SPY_QA;
    public static final int GAME_TYPE_DRAW_GUESS = GameType.GAME_TYPE_DRAW_GUESS;
    public static final int GAME_TYPE_MUSIC_HUM = GameType.GAME_TYPE_MUSIC_HUM;
    public static final int GAME_TYPE_GUESS_ANSWERS_VOICE = GameType.GAME_TYPE_GUESS_ANSWERS_VOICE;
    public static final int GAME_TYPE_GUESS_ANSWERS_TEXT = GameType.GAME_TYPE_GUESS_ANSWERS_TEXT;
    public static final int GAME_TYPE_AVATAR_WOLF_SIMPLE = GameType.GAME_TYPE_AVATAR_WOLF_SIMPLE;
    public static final int GAME_TYPE_AVATAR_WOLF_STANDARD = GameType.GAME_TYPE_AVATAR_WOLF_STANDARD;

    public static final int LEASE_DEFAULT = FixRoomPackets.FixLeaseType.LEASE_TYPE_UNDEFINED_VALUE;
    public static final int LEASE_TWO_MONTH = FixRoomPackets.FixLeaseType.TWO_MONTH_VALUE;
    public static final int LEASE_ONE_YEAR = FixRoomPackets.FixLeaseType.ONE_YEAR_VALUE;
    public static final int LEASE_ONE_MOUTH = FixRoomPackets.FixLeaseType.ONE_MONTH_VALUE;

    public static final int ROOM_TYPE_DEFAULE = FixRoomPackets.FixRoomType.RTYPE_UNDEFINED_VALUE;
    public static final int ROOM_TYPE_NORMAL = FixRoomPackets.FixRoomType.RTYP_NORMAL_VALUE;
    public static final int ROOM_TYPE_VIP = FixRoomPackets.FixRoomType.RTYPE_VIP_VALUE;//vip房，过期变成普通房
    public static final int ROOM_TYPE_CARD = FixRoomPackets.FixRoomType.RTYPE_CARD_VALUE;//棋牌房，过期解散

    public static final int WORD_CATE_ALL = FixRoomPackets.FixSpyWordCate.ALL_VALUE;
    public static final int WORD_CATE_MIXTURE = FixRoomPackets.FixSpyWordCate.MIXTURE_VALUE;
    public static final int WORD_CATE_ADULT = FixRoomPackets.FixSpyWordCate.ADULT_VALUE;
    public static final int WORD_CATE_AMUSE = FixRoomPackets.FixSpyWordCate.AMUSE_VALUE;
    public static final int WORD_CATE_POP = FixRoomPackets.FixSpyWordCate.POP_VALUE;
    public static final int WORD_CATE_HUMAN = FixRoomPackets.FixSpyWordCate.HUMAN_VALUE;

    public static final int DRAW_WORD_CATE_ALL = ProtoDrawPackets.DrawWordCate.CATE_ALL_VALUE;
    public static final int DRAW_WORD_CATE_POP = ProtoDrawPackets.DrawWordCate.CATE_POP_VALUE;
    public static final int DRAW_WORD_CATE_DAILY = ProtoDrawPackets.DrawWordCate.CATE_DAILY_LIFE_VALUE;
    public static final int DRAW_WORD_CATE_NATURE = ProtoDrawPackets.DrawWordCate.CATE_NATURE_VALUE;
    public static final int DRAW_WORD_CATE_MIX = ProtoDrawPackets.DrawWordCate.CATE_MIXTURE_VALUE;

    @SmartModel.DBPrimaryKey
    @SmartModel.DBField
    private int rid = 0;
    @SmartModel.DBField
    private int version = 0;
    @SmartModel.DBField
    private int owner = 0;
    @SmartModel.DBField
    private String name = "";
    @SmartModel.DBField
    private String adminListStr = "";
    private List<Integer> admin_list = new ArrayList<>();
    @SmartModel.DBField
    private String residentListStr = "";
    private List<Integer> resident_list = new ArrayList<>();
    @SmartModel.DBField
    private int enter_level = 0;
    @SmartModel.DBField
    private String bg_url = "";
    @SmartModel.DBField
    private String note = "";
    @SmartModel.DBField
    private final int lease_type = 0;//FixLeaseType 无用字段
    @SmartModel.DBField
    private int game_type = 0;//FixGameType
    @SmartModel.DBField
    private int gamerNum;
    @SmartModel.DBField
    private int allow_enter;
    @SmartModel.DBField
    private String seatListStr = "";
    private List<FixSeatInfo> seats = new ArrayList<>();
    private boolean tempRoom = false;// true : 本地没有的房间
    @SmartModel.DBField
    private int game_state;
    @SmartModel.DBField
    private int free_talk;
    @SmartModel.DBField
    private long expire_time;
    @SmartModel.DBField
    private int bet_level;
    @SmartModel.DBField
    private int room_level;
    @SmartModel.DBField
    private int room_type;
    @SmartModel.DBField
    private int word_cate;
    @SmartModel.DBField
    private int gold_box;
    @SmartModel.DBField
    private int music_cate;//曲库
    @SmartModel.DBField
    private int voice_type = 1;
    @SmartModel.DBField
    private int high_quality = 0;
    @SmartModel.DBField
    private String pwd;

    private int scene;

    public static FixRoomInfo parse(FixRoomPackets.FixRoomInfo fixRoomInfo) {
        FixRoomInfo fixRoom = new FixRoomInfo();
        fixRoom.setVersion(fixRoomInfo.getVersion());
        fixRoom.setRid(fixRoomInfo.getRid());
        fixRoom.setOwner(fixRoomInfo.getOwner());
        fixRoom.setName(fixRoomInfo.getName());
        fixRoom.setAdmin_list(new ArrayList<>(fixRoomInfo.getAdminListList()));
        fixRoom.setResident_list(new ArrayList<>(fixRoomInfo.getResidentListList()));
        fixRoom.setEnter_level(fixRoomInfo.getEnterLevel());
        fixRoom.setBg_url(fixRoomInfo.getBgUrl());
        fixRoom.setNote(fixRoomInfo.getNote());
        fixRoom.setGame_type(fixRoomInfo.getGameType());
        fixRoom.setSeats(FixSeatInfo.parseList(fixRoomInfo.getSeatsList()));
        fixRoom.setGamerNum(fixRoomInfo.getGamerNum());
        fixRoom.setAllow_enter(fixRoomInfo.getAllowEnter());
        fixRoom.setGame_state(fixRoomInfo.getGameState());
        fixRoom.setFree_talk(fixRoomInfo.getFreeTalk());
        fixRoom.setExpire_time(fixRoomInfo.getExpireTime());
        fixRoom.setBet_level(fixRoomInfo.getBetLevel());
        fixRoom.setRoom_level(fixRoomInfo.getRoomLevel());
        fixRoom.setRoom_type(fixRoomInfo.getRoomType());
        fixRoom.setWord_cate(fixRoomInfo.getWordCate());
        fixRoom.setGold_box(fixRoomInfo.getGoldBox());
        fixRoom.setMusicCate(fixRoomInfo.getMusicCate());
        fixRoom.setHighQuality(fixRoomInfo.getHighQuality());
        fixRoom.setVoiceType(fixRoomInfo.getVoiceType());
        fixRoom.setPwd(fixRoomInfo.getPasswd());
        return fixRoom;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getOwner() {
        return owner;
    }

    public boolean isSelfOwner() {
        return owner == LoginHelper.getLoginUid();
    }

    public void setOwner(int owner) {
        this.owner = owner;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<Integer> getAdmin_list() {
        if (admin_list.size() == 0) {
            admin_list = StringUtil.uidString2Array(adminListStr);
        }
        return admin_list;
    }

    public void setAdmin_list(List<Integer> admin_list) {
        this.admin_list = admin_list;
        this.adminListStr = StringUtil.uidArray2String(admin_list);
    }

    public boolean isSelfAdmin() {
        int size = admin_list.size();
        for (int i = 0; i < size; i++) {
            if (admin_list.get(i) == LoginHelper.getLoginUid()) {
                return true;
            }
        }
        return false;
    }

    public List<Integer> getResident_list() {
        if (resident_list.size() == 0) {
            resident_list = StringUtil.uidString2Array(residentListStr);
        }
        return resident_list;
    }

    public void setResident_list(List<Integer> resident_list) {
        this.resident_list = resident_list;
        this.residentListStr = StringUtil.uidArray2String(resident_list);
    }

    public boolean isSelfResident() {
        int size = resident_list.size();
        for (int i = 0; i < size; i++) {
            if (resident_list.get(i) == LoginHelper.getLoginUid()) {
                return true;
            }
        }
        return false;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public int getEnter_level() {
        return enter_level;
    }

    public void setEnter_level(int enter_level) {
        this.enter_level = enter_level;
    }

    public String getBg_url() {
        return bg_url;
    }

    public void setBg_url(String bg_url) {
        this.bg_url = bg_url;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public int getGame_type() {
        return game_type;
    }

    public void setGame_type(int game_type) {
        this.game_type = game_type;
    }

    public List<FixSeatInfo> getSeats() {
        if (seats.size() == 0 && !TextUtils.isEmpty(seatListStr)) {
            try {
                List<FixSeatInfo> seatInfoList = JsonUtil.fromJson(seatListStr, new TypeToken<List<FixSeatInfo>>() {
                }.getType());
                if (seatInfoList != null) seats = seatInfoList;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return seats;
    }

    public void setSeats(List<FixSeatInfo> seats) {
        this.seats = seats;
        this.seatListStr = JsonUtil.toJson(seats);
    }

    public static boolean isFixRoomGame(int game_type) {
        boolean isFixWithoutWolf = game_type == GAME_TYPE_LANDLORD
                || game_type == GAME_TYPE_AB_SPY_TEXTING
                || game_type == GAME_TYPE_AB_SPY_VOICING
                || game_type == GAME_TYPE_BLANK_SPY_TEXTING
                || game_type == GAME_TYPE_BLANK_SPY_VOICING
                || game_type == GAME_TYPE_BLANK_SPY_QA
                || game_type == GAME_TYPE_DRAW_GUESS
                || game_type == GAME_TYPE_MUSIC_HUM
                || game_type == GAME_TYPE_GUESS_ANSWERS_VOICE
                || game_type == GAME_TYPE_GUESS_ANSWERS_TEXT;
        if (AvatarWolfUtil.activated()) {
            return isFixWithoutWolf || game_type == GAME_TYPE_AVATAR_WOLF_SIMPLE
                    || game_type == GAME_TYPE_AVATAR_WOLF_STANDARD;
        } else {
            return isFixWithoutWolf;
        }
    }

    public int getGamerNum() {
        return gamerNum;
    }

    public void setGamerNum(int gamerNum) {
        this.gamerNum = gamerNum;
    }

    public boolean isTempRoom() {
        return tempRoom;
    }

    public void setTempRoom(boolean tempRoom) {
        this.tempRoom = tempRoom;
    }

    public boolean isAllowEnter() {
        return allow_enter == FixRoomConst.ROOM_ALLOW;
    }

    public void setAllow_enter(boolean allow_enter) {
        if (allow_enter) {
            this.allow_enter = FixRoomConst.ROOM_ALLOW;
        } else {
            this.allow_enter = FixRoomConst.ROOM_UNALLOW;
        }
    }

    public int getGame_state() {
        return game_state;
    }

    public void setGame_state(int game_state) {
        this.game_state = game_state;
    }

    public boolean isFreeTalkOn() {
        return free_talk == FixRoomConst.FREE_TALK_ON;
    }

    public void setFree_talk(boolean free_talk) {
        this.free_talk = free_talk ? FixRoomConst.FREE_TALK_ON : FixRoomConst.FREE_TALK_OFF;
    }

    public long getExpire_time() {
        return expire_time;
    }

    public void setExpire_time(long expire_time) {
        this.expire_time = expire_time;
    }

    public int getBet_level() {
        return bet_level;
    }

    public void setBet_level(int bet_level) {
        this.bet_level = bet_level;
    }

    public int getRoom_level() {
        return room_level;
    }

    public void setRoom_level(int room_level) {
        this.room_level = room_level;
    }

    public int getRoom_type() {
        return room_type;
    }

    public void setRoom_type(int room_type) {
        this.room_type = room_type;
    }

    public int getWord_cate() {
        return word_cate;
    }

    public void setWord_cate(int word_cate) {
        this.word_cate = word_cate;
    }

    public int getGold_box() {
        return gold_box;
    }

    public void setGold_box(int gold_box) {
        this.gold_box = gold_box;
    }

    public int getMusicCate() {
        return music_cate;
    }

    public void setMusicCate(int musicCate) {
        this.music_cate = musicCate;
    }

    public boolean isReady(int uid) {
        for (FixSeatInfo seatInfo : getSeats()) {
            if (seatInfo.isUidSeat(uid) && seatInfo.isReady()) {
                return true;
            }
        }
        return false;
    }

    public int getReadyNum() {
        int index = 0;
        for (FixSeatInfo seatInfo : getSeats()) {
            if (seatInfo.isReady()) {
                index++;
            }
        }
        return index;
    }

    public boolean isInSit(int uid) {
        for (FixSeatInfo seatInfo : getSeats()) {
            if (seatInfo.isUidSeat(uid)) {
                return true;
            }
        }
        return false;
    }

    public boolean isResident(int uid) {
        for (int param : getResident_list()) {
            if (uid == param) return true;
        }
        return false;
    }

    public boolean isAdmin(int uid) {
        for (int param : getAdmin_list()) {
            if (uid == param) return true;
        }
        return false;
    }

    public boolean isLandlord() {
        return game_type == GAME_TYPE_LANDLORD;
    }

    public boolean isDrawGuess() {
        return game_type == GAME_TYPE_DRAW_GUESS;
    }

    public boolean isGuessAnswersVoice() {
        return game_type == GAME_TYPE_GUESS_ANSWERS_VOICE;
    }
    public boolean isGuessAnswersText() {
        return game_type == GAME_TYPE_GUESS_ANSWERS_TEXT;
    }

    public boolean isMusicHum() {
        return game_type == GAME_TYPE_MUSIC_HUM;
    }

    public boolean isVipRoom() {
        return room_type == ROOM_TYPE_CARD || room_type == ROOM_TYPE_VIP;
    }

    public boolean isGameing() {
        return isGameing(getGame_state());
    }

    public static boolean isGameing(int game_state) {
        return game_state == FixRoomConst.ROOM_STATUS_GAMING;
    }

    public void setHighQuality(boolean highQuality) {
        this.high_quality = highQuality ? 1 : 0;
    }

    public void setVoiceType(int voiceType) {
        this.voice_type = voiceType;
    }

    public int getVoiceType() {
        return voice_type;
    }

    public boolean isHighQuality() {
        return high_quality != 0;
    }

    public boolean isSeatAllEmpty() {
        for (FixSeatInfo seatInfo : getSeats()) {
            if (!seatInfo.isEmpty()) {
                return false;
            }
        }
        return true;
    }

    public int getSitNum() {
        int num = 0;
        for (FixSeatInfo seatInfo : getSeats()) {
            if (!seatInfo.isEmpty()) {
                num++;
            }
        }
        return num;
    }

    public FixSeatInfo getSeatInfo(int uid) {
        for (FixSeatInfo seatInfo : getSeats()) {
            if (seatInfo.getUid() == uid) {
                return seatInfo;
            }
        }
        return new FixSeatInfo();
    }

    @Nullable
    public FixSeatInfo getSeatInfoByNum(int seatNum) {
        if (seatNum >= 1 && seatNum <= getSeats().size()) {
            return getSeats().get(seatNum - 1);
        }
        return null;
    }

    public boolean isSpyGame() {
        return isSpyGame(game_type);
    }

    public static boolean isSpyGame(int game_type) {
        return GameTypeUtil.isSpyGame(game_type);
    }

    public boolean isWolfGame() {
        return isWolfGame(game_type);
    }

    public boolean isWolfGame(int game_type) {
        return GameTypeUtil.isAvatarWolf(game_type);
    }

    public boolean isGTAGame(int game_type) {
        return game_type == GameType.GAME_TYPE_GUESS_ANSWERS_VOICE || game_type == GameType.GAME_TYPE_GUESS_ANSWERS_TEXT;
    }

    public boolean isVoiceSpyGame() {
        return isVoiceSpyGame(game_type);
    }

    public boolean isSpyQaMode() {
        return game_type == GameType.GAME_TYPE_BLANK_SPY_QA;
    }

    public static boolean isVoiceSpyGame(int gameType) {
        return gameType == FixRoomInfo.GAME_TYPE_AB_SPY_VOICING
                || gameType == FixRoomInfo.GAME_TYPE_BLANK_SPY_VOICING
                || gameType == FixRoomInfo.GAME_TYPE_BLANK_SPY_QA;
    }

    public static int getRoomType(int gameType, boolean isVip) {
        if (gameType == FixRoomInfo.GAME_TYPE_LANDLORD) {
            return ROOM_TYPE_CARD;
        }
        if (isVip) {
            return ROOM_TYPE_VIP;
        } else {
            return ROOM_TYPE_NORMAL;
        }
    }

    public boolean isBlankSpyGame() {
        return game_type == FixRoomInfo.GAME_TYPE_BLANK_SPY_TEXTING
                || game_type == FixRoomInfo.GAME_TYPE_BLANK_SPY_VOICING
                || game_type == GameType.GAME_TYPE_BLANK_SPY_QA;
    }

    public int getUserSeatNumber(int uid) {
        List<FixSeatInfo> seatInfoList = getSeats();
        for (int i = 0; i < seatInfoList.size(); i++) {
            FixSeatInfo seatInfo = seatInfoList.get(i);
            if (seatInfo.getUid() == uid) {
                return i + 1;
            }
        }
        return -1;
    }

    /**
     * 座位是否坐满
     *
     * @return
     */
    public boolean isSeatFull() {
        for (FixSeatInfo seatInfo : getSeats()) {
            if (seatInfo.isEmpty()) {
                return false;
            }
        }
        return true;
    }

    public int getScene() {
        return scene;
    }

    public void setScene(int scene) {
        this.scene = scene;
    }

    @Override
    public String toString() {
        return "FixRoomInfo{" +
                "rid=" + rid +
                ", version=" + version +
                ", name='" + name + '\'' +
                ", game_state='" + game_state +
                ", game_type=" + game_type +
                ", allow_enter=" + allow_enter +
                ", tempRoom=" + tempRoom +
                ", room_level=" + room_level +
                ", room_type=" + room_type +
                ", music_cate=" + music_cate +
                ", voice_type=" + voice_type +
                ", seatListStr=" + seatListStr +
                '}';
    }
}
