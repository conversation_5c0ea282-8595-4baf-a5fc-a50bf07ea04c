package com.huiwan.fixroom;

import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.module.spy.SocialGameMsgCallback;
import com.wepie.wespy.model.entity.fixroom.FixRoomInfo;

/**
 * 涉及FixRoomActivity及其presenter相关操作的接口，主工程由FixRoomPresenter实现并传进来
 * created by <PERSON><PERSON><PERSON> on 2021/4/30
 */
public interface IFixRoomHolder {
    void onUnityExit();
    void onUnityEnterRoom(int rid);
    void gotoFixRoomSetting();
    void onUnityExitRoom();
    void sendChatMsg(int rid, int seat, String content);
    SocialGameMsgCallback getSocialCallback();
    FixRoomInfo curFixRoomInfo();
    void updateMsg2MemoryDB(ChatMsg msg);
    void reportFixRoomChatMsg(int rid, int uid, String mid);
    void killFixRoomActivity();
    void setFixRoomActivityVisible();
}
