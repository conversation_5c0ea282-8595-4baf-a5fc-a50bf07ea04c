package com.huiwan.fixroom;

import static com.huiwan.constants.BaseConstants.SERVER_NO;

import android.app.Activity;
import android.app.Service;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.core.content.ContextCompat;
import androidx.core.widget.PopupWindowCompat;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.constants.GameType;
import com.huiwan.constants.GameTypeUtil;
import com.huiwan.fixroom.entity.FixRoomUnityInfo;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.WebApi;
import com.huiwan.store.PrefUtil;
import com.huiwan.user.LoginHelper;
import com.huiwan.voiceservice.VoiceManager;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.unity.NativeCallUnityManager;
import com.wepie.unity.UnityUtil;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.fixroom.FixRoomInfo;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class FixRoomUtil {

    public static boolean supportUnity(int gameType) {
        return isAvatarSpy(gameType) || isAvatarWolf(gameType);
    }

    public static boolean needVoice(int gameType) {
        if (gameType == 0) return true; //如果异常默认需要
        return gameType == GameType.GAME_TYPE_AVATAR_WOLF_STANDARD
                || gameType == GameType.GAME_TYPE_AB_SPY_VOICING
                || gameType == GameType.GAME_TYPE_AVATAR_WOLF_SIMPLE
                || gameType == GameType.GAME_TYPE_BLANK_SPY_VOICING
                || gameType == GameType.GAME_TYPE_GUESS_ANSWERS_VOICE;
    }

    public static boolean isAvatarWolf(int gameType) {
        return GameTypeUtil.isAvatarWolf(gameType);
    }

    public static boolean isAvatarSpy(int gameType) {
        return GameTypeUtil.isAvatarSpy(gameType);
    }

    public static boolean isMatchUnityScene(int scene, int gameType) {
        if (scene == UnityUtil.SCENE_AVATAR_WOLF) return isAvatarWolf(gameType);
        else if (scene == UnityUtil.SCENE_AVATAR_SPY) return isAvatarSpy(gameType);
        return false;
    }

    public static void gotoAvatarWolf(Context context, FixRoomInfo fixRoomInfo) {
        FixRoomStateManager.getInstance().gotoUnity(context, UnityUtil.SCENE_AVATAR_WOLF, fixRoomInfo);
    }

    public static void gotoAvatarSpy(Context context, FixRoomInfo fixRoomInfo) {
        FixRoomStateManager.getInstance().gotoUnity(context, UnityUtil.SCENE_AVATAR_SPY, fixRoomInfo);
    }

    public static void gotoUnity(Context context, int scene, FixRoomInfo fixRoomInfo) {
        FixRoomStateManager.getInstance().gotoUnity(context, scene, fixRoomInfo);
    }

    public static void unityEnterRoom(FixRoomInfo fixRoomInfo) {
        if (supportUnity(fixRoomInfo.getGame_type())) {
            FixRoomUnityInfo unityInfo = FixRoomUnityInfo.parse(fixRoomInfo);
            NativeCallUnityManager.getInstance().enterFixRoom(UnityUtil.getGson().toJson(unityInfo), getService());
        }
        FixRoomStateManager.getInstance().onUnityEnterRoom(fixRoomInfo.getRid());
    }

    public static void inviteFixRoom(Activity activity, int rid) {
        FixRoomStateManager.getInstance().showInviteDialog(activity, rid);
    }

    public static void showUserDialog(Activity activity, int uid, int gameType, String subSource) {
        FixRoomStateManager.getInstance().showUserDialog(activity, uid, gameType, subSource);
    }

    public static void showSendGift(Activity activity, int uid, int gameType, String subSource) {
        FixRoomStateManager.getInstance().showSendGift(activity, uid, gameType, subSource);
    }

    public static void showMagicFaceDialog(Activity activity, RelativeLayout root, int rid, long delay) {
        FixRoomStateManager.getInstance().showMagicFaceDialog(activity, root, rid, delay);
    }

    public static String getGameName(int gameType) {
        return GameType.getRoomTypeName(gameType);
    }

    public static int getSceneFromGameType(int gameType) {
        if (isAvatarSpy(gameType)) return UnityUtil.SCENE_AVATAR_SPY;
        if (isAvatarWolf(gameType)) return UnityUtil.SCENE_AVATAR_WOLF;
        return UnityUtil.SCENE_UNDEFINED;
    }

    public static String getVoiceChannel(FixRoomInfo fixRoomInfo) {
        int gameType = fixRoomInfo.getGame_type();
        int rid = fixRoomInfo.getRid();
        String game = "";
        if (isAvatarSpy(gameType)) game = VoiceManager.SPY;
        else if (isAvatarWolf(gameType)) game = VoiceManager.AVATAR_WOLF;
        else return "";
        String debug = isUrlDebug() ? VoiceManager.VOICE_DEV : "";
        return debug + game + rid;
    }

    public static Class<? extends Service> getService() {
        return FixRoomStateManager.getInstance().getService();
    }

    public static void exit(@NonNull Class<? extends Service> service, int type) {
        NativeCallUnityManager.getInstance().remoteExit(service, type);
    }

    private static boolean isUrlDebug() {
        int serverConfig = PrefUtil.getInstance().getInt(PrefUtil.SERVICECINFIG, 0);
        //默认
        if (serverConfig == 0) {
            return LibBaseUtil.buildDebug();
        }
        //测试
        if (serverConfig == 1) {
            return true;
        }
        //线上
        if (serverConfig == 2) {
            return false;
        }
        //qa
        if (serverConfig == 3) {
            return false;
        }
        //需要改为测试环境，全部返回true
        return LibBaseUtil.buildDebug();
    }


    //抄写自 GiftInfoTransformHelper
    public static GiftShowInfo fromChatMessage(ChatMsg chatMsg) {
        if (chatMsg.getMediaType() != ChatMsg.MEDIA_TYPE_GIFT) {
            return null;
        }
        String json = chatMsg.getExt();
        if (TextUtils.isEmpty(json)) {
            return null;
        }
        try {
            int sender = chatMsg.getSend_uid();
            int receiver = 0;
            int giftId = 0;
            int giftNum = 0;
            int comboTimes = 0;
            int charmType = Gift.GIFT_CHARM_TYPE_NONE;
            int animIndex = -1;
            boolean isPrivate = false;
            int isGiftCard = 0;
            JSONObject jo = new JSONObject(json);
            List<Integer> uidList = new ArrayList<>();
            if (jo.has("gift_id")) {
                giftId = jo.getInt("gift_id");
            }
            if (jo.has("gift_num")) {
                giftNum = jo.getInt("gift_num");
            }
            if (jo.has("combo_times")) {
                comboTimes = jo.getInt("combo_times");
            }
            if (jo.has("charm_type")) {
                charmType = jo.getInt("charm_type");
            }
            if (jo.has("animation_index")) {
                animIndex = jo.getInt("animation_index");
            }
            if (jo.has("recv_uid")) {
                receiver = jo.getInt("recv_uid");
            }
            if (jo.has("is_public")) {
                isPrivate = jo.getInt("is_public") != SERVER_NO;
            }
            if (jo.has("is_gift_card")) {
                isGiftCard = jo.getInt("is_gift_card");
            }
            if (jo.has("show_img_uid_list")) {
                JSONArray jsonArray = jo.optJSONArray("show_img_uid_list");
                if (null != jsonArray) {
                    for (int i = 0; i < jsonArray.length(); i++) {
                        uidList.add(jsonArray.getInt(i));
                    }
                }
            }
            GiftShowInfo giftShowInfo = new GiftShowInfo(sender, receiver, giftId, giftNum, comboTimes, charmType,
                    animIndex, isPrivate, isGiftCard);
            giftShowInfo.addUids(uidList);
            return giftShowInfo;
        } catch (Exception e) {
            TimeLogger.err("error parseFromPushMsg wp message to gift show info");
            return null;
        }
    }

    public static void showEnergyNotEnoughTips(Context context, String msg, int gameType, String scene) {
        ApiService.of(WebApi.class).showWebDialog(
                context, ConfigHelper.getInstance().getConstV3Info().gtaGameEnergyUrl, 0, 0F
        );
        ToastUtil.show(msg);
        ArrayMap<String, Object> map = new ArrayMap<>();
        map.put("scene", scene);
        map.put("is_auto", true);
        map.put("game_type", gameType);
        map.put("vip_level", LoginHelper.getVipLevel());
        TrackUtil.appViewScreen(TrackScreenName.ENERGY_DIALOG, map);
    }

    public static PopupWindow showMenuPop(View menuIv, FixRoomInfo info, IFixMenuCallback callback) {
        Context context = menuIv.getContext();
        PopupWindow popup = new PopupWindow(context);
        ViewGroup layout = (ViewGroup) LayoutInflater.from(context).inflate(R.layout.fix_room_menu, null);
        View gameExplain = layout.findViewById(R.id.menu_game_explain_tv);
        View gameExit = layout.findViewById(R.id.menu_game_exit_tv);
        View gameVoice = layout.findViewById(R.id.menu_game_voice_tv);
        View standUp = layout.findViewById(R.id.menu_stand_up_tv);
        if (info.isInSit(LoginHelper.getLoginUid()) && !info.isGameing()) {
            standUp.setVisibility(View.VISIBLE);
        } else {
            standUp.setVisibility(View.GONE);
        }
        List<View> viewList = Arrays.asList(gameExit, gameExplain, gameVoice, standUp);
        View.OnClickListener listener = v -> {
            if (v == gameExplain) {
                callback.onShowRule();
            } else if (v == gameExit) {
                callback.onExit();
            } else if (v == gameVoice) {
                callback.onShowVolume();
            } else if (v == standUp) {
                callback.onStandUp();
            }
            popup.dismiss();
        };
        for (int i = 0, count = viewList.size(); i < count; i++) {
            View v = layout.getChildAt(i);
            v.setOnClickListener(listener);
            if (i == 0) {
                v.setBackground(ContextCompat.getDrawable(context, R.drawable.selector_menu_bg_lt_rt));
            } else if (i == count - 1) {
                v.setBackground(ContextCompat.getDrawable(context, R.drawable.selector_menu_bg_lb_rb));
            } else {
                v.setBackground(ContextCompat.getDrawable(context, R.drawable.selector_menu_bg));
            }
        }

        popup.setContentView(layout);
        popup.setHeight(WindowManager.LayoutParams.WRAP_CONTENT);
        popup.setWidth(WindowManager.LayoutParams.WRAP_CONTENT);
        popup.setOutsideTouchable(true);
        popup.setFocusable(true);
        popup.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        if (ScreenUtil.isRtl()) {
            layout.measure(-1, -1);
            int width = layout.getMeasuredWidth();
            int xOff = ScreenUtil.getScreenWidth() - width - ScreenUtil.dip2px(10) - menuIv.getLeft();
            PopupWindowCompat.showAsDropDown(popup, menuIv, xOff, -menuIv.getPaddingBottom(), Gravity.BOTTOM);
        } else {
            PopupWindowCompat.showAsDropDown(popup, menuIv, ScreenUtil.dip2px(10F) - menuIv.getLeft(), -menuIv.getPaddingBottom(), Gravity.BOTTOM);
        }
        return popup;
    }

    public interface IFixMenuCallback {
        void onStandUp();

        void onShowVolume();

        void onShowRule();

        void onExit();
    }
}
