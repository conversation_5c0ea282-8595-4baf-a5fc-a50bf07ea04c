package com.huiwan.fixroom.entity;

import com.google.gson.annotations.SerializedName;
import com.huiwan.configservice.constentity.propextra.ChatBubbleItem;

public class UnityBubbleItem {
    @SerializedName("text_color")
    public String textColor = "#000000";
    @SerializedName("deep_link_color")
    public String deepLinkColor = "#24c572";
    @SerializedName("bg_img_url")
    public String bgImgUrl = "";
    @SerializedName("frame_anim_lt")
    public String frameAnimLt = "";
    @SerializedName("frame_anim_rt")
    public String frameAnimRt = "";
    @SerializedName("frame_anim_lb")
    public String frameAnimLb = "";
    @SerializedName("frame_anim_rb")
    public String frameAnimRb = "";

    public static UnityBubbleItem parse(ChatBubbleItem item) {
        UnityBubbleItem unityBubbleItem = new UnityBubbleItem();
        if (item != null) {
            unityBubbleItem.textColor = item.textColor;
            unityBubbleItem.deepLinkColor = item.deepLinkColor;
            unityBubbleItem.frameAnimLt = item.frameAnimLt;
            unityBubbleItem.frameAnimLb = item.frameAnimLb;
            unityBubbleItem.frameAnimRt = item.frameAnimRt;
            unityBubbleItem.frameAnimRb = item.frameAnimRb;
            unityBubbleItem.bgImgUrl = item.bgImgUrl;
        }

        return unityBubbleItem;
    }
}
