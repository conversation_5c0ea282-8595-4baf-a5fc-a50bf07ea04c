package com.huiwan.fixroom.entity;

import com.google.gson.annotations.SerializedName;
import com.huiwan.configservice.model.voiceroom.FaceInfo;

public class UnityFaceInfo {
    @SerializedName("id")
    public int id;
    @SerializedName("anim_url")
    public String anim_url;
    @SerializedName("result_url")
    public String result_url;
    @SerializedName("anim_path")
    public String anim_path;
    @SerializedName("anim_time")
    public long anim_time = 0;
    @SerializedName("static_time")
    public long static_time = 0;

    public static UnityFaceInfo parse(FaceInfo info, int result) {
        if (!info.isGif()) return null; //非gif不传给unity
        UnityFaceInfo unityFaceInfo = new UnityFaceInfo();
        unityFaceInfo.id = info.getFaceId();
        unityFaceInfo.anim_url = info.getAnimUri();
        unityFaceInfo.result_url = info.getResultUrl(result);
        unityFaceInfo.anim_path = info.getLocalGifPath();
        unityFaceInfo.anim_time = info.getAnimTime();
        unityFaceInfo.static_time = info.getStaticTime();

        return unityFaceInfo;
    }
}
