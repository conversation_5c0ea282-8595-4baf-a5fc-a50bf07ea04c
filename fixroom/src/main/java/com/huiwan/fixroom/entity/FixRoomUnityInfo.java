package com.huiwan.fixroom.entity;

import com.google.gson.annotations.SerializedName;
import com.huiwan.fixroom.FixRoomUtil;
import com.wepie.wespy.model.entity.fixroom.FixRoomInfo;
import com.wepie.wespy.model.entity.fixroom.FixSeatInfo;

import java.util.ArrayList;
import java.util.List;

public class FixRoomUnityInfo {
    @SerializedName("version")
    private int version = 0; //房间信息版本
    @SerializedName("rid")
    private int rid = 0;
    @SerializedName("owner")
    private int owner = 0; //房主uid
    @SerializedName("name")
    private String name = ""; //房间名
    @SerializedName("admin_list")
    private List<Integer> admin_list = new ArrayList<>(); //管理员列表
    @SerializedName("note")
    private String note = ""; //房间公告
    @SerializedName("game_type")
    private int game_type = 0; //卧底文字31， 卧底语音32， 空白文字33， 空白语音34
    @SerializedName("gamer_num")
    private int gamer_num;
    @SerializedName("allow_enter")
    private boolean allow_enter = false; //是否加锁
    @SerializedName("seats")
    private List<FixSeatInfo> seats = new ArrayList<>();
    @SerializedName("game_state")
    private int game_state; //准备阶段1， 游戏阶段2
    @SerializedName("voice_type")
    private int voice_type = 1; //使用语音sdk情况，目前暂时只用zego
    @SerializedName("high_quality")
    private boolean high_quality = false; //是否高音质
    @SerializedName("pwd")
    private String pwd;
    @SerializedName("voice_channel_name")
    private String voice_channel_name;
    @SerializedName("is_high_level_room")
    private boolean is_high_level_room = false;

    @SerializedName("scene")
    private int scene;

    public static FixRoomUnityInfo parse(FixRoomInfo fixRoomInfo) {
        if (fixRoomInfo == null) return null;
        FixRoomUnityInfo unityInfo = new FixRoomUnityInfo();
        unityInfo.setVersion(fixRoomInfo.getVersion());
        unityInfo.setRid(fixRoomInfo.getRid());
        unityInfo.setOwner(fixRoomInfo.getOwner());
        unityInfo.setName(fixRoomInfo.getName());
        unityInfo.setAdmin_list(fixRoomInfo.getAdmin_list());
        unityInfo.setNote(fixRoomInfo.getNote());
        unityInfo.setScene(fixRoomInfo.getScene());
        unityInfo.setGame_type(fixRoomInfo.getGame_type());
        unityInfo.setGamer_num(fixRoomInfo.getGamerNum());
        unityInfo.setAllow_enter(fixRoomInfo.isAllowEnter());
        unityInfo.setSeats(fixRoomInfo.getSeats());
        unityInfo.setGame_state(fixRoomInfo.getGame_state());
        unityInfo.setVoice_type(fixRoomInfo.getVoiceType());
        unityInfo.setHigh_quality(fixRoomInfo.isHighQuality());
        unityInfo.setPwd(fixRoomInfo.getPwd());
        unityInfo.setVoice_channel_name(FixRoomUtil.getVoiceChannel(fixRoomInfo));
        unityInfo.setIs_high_level_room(fixRoomInfo.isVipRoom());
        return unityInfo;
    }

    public String getVoice_channel_name() {
        return voice_channel_name;
    }

    public void setVoice_channel_name(String voice_channel_name) {
        this.voice_channel_name = voice_channel_name;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getOwner() {
        return owner;
    }

    public void setOwner(int owner) {
        this.owner = owner;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<Integer> getAdmin_list() {
        return admin_list;
    }

    public void setAdmin_list(List<Integer> admin_list) {
        this.admin_list = admin_list;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public int getGame_type() {
        return game_type;
    }

    public void setGame_type(int game_type) {
        this.game_type = game_type;
    }

    public int getGamer_num() {
        return gamer_num;
    }

    public void setGamer_num(int gamer_num) {
        this.gamer_num = gamer_num;
    }

    public boolean getAllow_enter() {
        return allow_enter;
    }

    public void setAllow_enter(boolean allow_enter) {
        this.allow_enter = allow_enter;
    }

    public List<FixSeatInfo> getSeats() {
        return seats;
    }

    public void setSeats(List<FixSeatInfo> seats) {
        this.seats = seats;
    }

    public int getGame_state() {
        return game_state;
    }

    public void setGame_state(int game_state) {
        this.game_state = game_state;
    }

    public int getVoice_type() {
        return voice_type;
    }

    public void setVoice_type(int voice_type) {
        this.voice_type = voice_type;
    }

    public boolean getHigh_quality() {
        return high_quality;
    }

    public void setHigh_quality(boolean high_quality) {
        this.high_quality = high_quality;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public int getScene() {
        return scene;
    }

    public void setScene(int scene) {
        this.scene = scene;
    }

    public boolean isIs_high_level_room() {
        return is_high_level_room;
    }

    public void setIs_high_level_room(boolean is_high_level_room) {
        this.is_high_level_room = is_high_level_room;
    }
}
