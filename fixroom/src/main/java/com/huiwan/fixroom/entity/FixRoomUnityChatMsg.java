package com.huiwan.fixroom.entity;

import android.text.TextUtils;

import com.huiwan.base.str.ResUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.fixroom.R;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.fixroom.FixRoomMsg;

import static com.wepie.wespy.model.entity.ChatMsg.MEDIA_TYPE_UNDEFINED;

public class FixRoomUnityChatMsg {
    public static final int JUDGE_UID = 1;
    private int media_type = ChatMsg.MEDIA_TYPE_TEXT;
    private String content = "";
    private int uid = 0;
    private int recv_uid = 0;
    private int seat_num = -1;
    private boolean is_judge = false;
    private String mid = "";

    public static FixRoomUnityChatMsg parse(FixRoomMsg fixRoomMsg) {
        FixRoomUnityChatMsg chatMsg = new FixRoomUnityChatMsg();
        chatMsg.setMedia_type(fixRoomMsg.getMediaType());
        chatMsg.setContent(fixRoomMsg.getContent());
        chatMsg.setUid(fixRoomMsg.getSend_uid());
        chatMsg.setSeat_num(fixRoomMsg.getUserNumberFromExt());
        chatMsg.setJudge(fixRoomMsg.getSend_uid() == JUDGE_UID);
        chatMsg.setMid(fixRoomMsg.getMid());
        return chatMsg;
    }

    public static FixRoomUnityChatMsg parseGiftMsg(FixRoomMsg fixRoomMsg) {
        FixRoomUnityChatMsg chatMsg = new FixRoomUnityChatMsg();
        chatMsg.setMedia_type(MEDIA_TYPE_UNDEFINED);
        int giftId = fixRoomMsg.getGiftId();
        int giftNum = fixRoomMsg.getGiftNum();
        String content = fixRoomMsg.getContent();
        String giftAwayCoin = fixRoomMsg.getGiftDesc();
        Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(giftId);
        if (gift != null)  {
            String unit = gift.getUnit();
            String giftName = gift.getName();

            String text = "";
            if (TextUtils.isEmpty(giftAwayCoin)) {
                text = ResUtil.getResource().getString(R.string.gift_common_send_tip_item_num_no_ext,
                        giftNum, unit, giftName, content);
            } else {
                text = ResUtil.getResource().getString(R.string.gift_common_send_tip_item_num,
                        giftNum, unit, giftName, content, giftAwayCoin);
            }

            chatMsg.setMedia_type(fixRoomMsg.getMediaType());
            chatMsg.setContent(text);
            chatMsg.setUid(fixRoomMsg.getSend_uid());
            chatMsg.setRecv_uid(fixRoomMsg.getGiftRecvUid());
            chatMsg.setMid(fixRoomMsg.getMid());
        }

        return chatMsg;
    }

    public int getMedia_type() {
        return media_type;
    }

    public void setMedia_type(int media_type) {
        this.media_type = media_type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getSeat_num() {
        return seat_num;
    }

    public void setSeat_num(int seat_num) {
        this.seat_num = seat_num;
    }

    public boolean isJudge() {
        return is_judge;
    }

    public void setJudge(boolean judge) {
        is_judge = judge;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public int getRecv_uid() {
        return recv_uid;
    }

    public void setRecv_uid(int recv_uid) {
        this.recv_uid = recv_uid;
    }
}
