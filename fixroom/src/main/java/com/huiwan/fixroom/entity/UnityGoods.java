package com.huiwan.fixroom.entity;

import com.google.gson.annotations.SerializedName;
import com.huiwan.configservice.model.WespyGoods;

public class UnityGoods {
    @SerializedName("goods_id")
    public int goodsId = 0;
    @SerializedName("goods_coin")
    public String goodsCoin  = "";
    @SerializedName("goods_price")
    public String goodsPrice = "";
    @SerializedName("goods_name")
    public String goodsName = "";
    @SerializedName("apple_product_id")
    public String appleProductId = ""; //苹果使用的商品id，用于unity校验
    @SerializedName("google_product_id")
    public String googleProductId = "";
    @SerializedName("currency")
    public String currency = "";

    public WespyGoods getWespyGoods() {
        WespyGoods wespyGoods = new WespyGoods();
        wespyGoods.goods_id = goodsId;
        wespyGoods.goods_coin = goodsCoin;
        wespyGoods.goods_price = goodsPrice;
        wespyGoods.goods_name = goodsName;
        wespyGoods.apple_product_id = appleProductId;
        wespyGoods.gpProductId = googleProductId;
        wespyGoods.currency = currency;
        return wespyGoods;
    }
}
