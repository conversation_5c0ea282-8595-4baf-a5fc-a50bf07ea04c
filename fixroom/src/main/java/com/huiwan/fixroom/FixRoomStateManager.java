package com.huiwan.fixroom;

import android.Manifest;
import android.app.Activity;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.widget.RelativeLayout;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ProcessUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.constants.Common;
import com.huiwan.constants.GameType;
import com.huiwan.constants.IntentConfig;
import com.huiwan.fixroom.callback.ReqPermissionCallback;
import com.huiwan.fixroom.entity.UnityGoods;
import com.huiwan.fixroom.tcp.FixRoomPacketSender;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.platform.ThreadUtil;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.liblog.LogUtil;
import com.wepie.liblog.main.HLog;
import com.wepie.libpermission.PermissionUtil;
import com.wepie.unity.NativeCallUnityManager;
import com.wepie.unity.UnitySceneCallback;
import com.wepie.unity.UnityUtil;
import com.wepie.wespy.helper.dialog.DialogCallback;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.fixroom.FixRoomInfo;
import com.wepie.wespy.module.spy.SocialGameMsgCallback;
import com.wepie.wespy.net.tcp.packet.FixRoomPackets;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class FixRoomStateManager {
    private static final String TAG = "FixRoomStateManager";
    private static FixRoomStateManager instance;
    private FixRoomStateManager(){

    }
    public static FixRoomStateManager getInstance() {
        if (instance == null) instance = new FixRoomStateManager();
        return instance;
    }

    /**主进程**/
    private WeakReference<Activity> activityRef;

    private IFixRoomOperator operator;
    private WeakReference<IFixRoomHolder> holderRef;
    private final Map<Integer, UnitySceneCallback> sceneReadyMap = new ConcurrentHashMap<>();
    private final MultiCallChecker multiCallChecker = new MultiCallChecker();

    private SeqCallback getSeqCb(int rid) {
        return new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {

            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        };
    }

    private ReqPermissionCallback permissionCallback;

    public void clearPermissionTask() {
        permissionCallback = null;
    }

    public void invokePermissionTask(boolean has) {
        if (permissionCallback == null) return;
        if (has) permissionCallback.hasPermission();
        else permissionCallback.noPermission();
        permissionCallback = null;
    }

    //此处fixroom相关的弱引用可能未初始化，需检查
    private boolean checkInit() {
        if (activityRef == null || holderRef == null) {
            LogUtil.upload(AliNetLogUtil.PORT.unity, AliNetLogUtil.TYPE.err,
                    "ref is null! process is "  + ProcessUtil.getCurrentProcessName(LibBaseUtil.getApplication()));
            return false;
        }
        if (activityRef.get() == null || holderRef.get() == null) {
            LogUtil.upload(AliNetLogUtil.PORT.unity, AliNetLogUtil.TYPE.err,
                    "ref is empty! process is "  + ProcessUtil.getCurrentProcessName(LibBaseUtil.getApplication()));
            return false;
        }
        return true;
    }


    //相对独立的任务走这里，实现类为FixRoomOperatorImpl
    public void setOperator(IFixRoomOperator operator) {
        this.operator = operator;
    }

    public IFixRoomOperator getOperator() {
        return operator;
    }

    //由主进程的FixRoomActivity负责的任务走这里，主要为房间相关，实现类为FixRoomPresenter
    public void setHolder(IFixRoomHolder holder) {
        this.holderRef = new WeakReference<>(holder);
    }

    public void setActivityRef(Activity activity) {
        this.activityRef = new WeakReference<>(activity);
    }

    public void registerSceneReadyCallback(int gameType, UnitySceneCallback callback) {
        if (FixRoomUtil.isAvatarSpy(gameType)) {
            sceneReadyMap.put(UnityUtil.SCENE_AVATAR_SPY, callback);
        } else if (FixRoomUtil.isAvatarWolf(gameType)) {
            sceneReadyMap.put(UnityUtil.SCENE_AVATAR_WOLF, callback);
        }
    }

    public void invokeSceneReadyCallback(int scene) {
        UnitySceneCallback callback = sceneReadyMap.remove(scene);
        if (callback != null) callback.onSceneReady();
    }

    public void unregisterAllSceneReadyCallback() {
        sceneReadyMap.clear();
    }

    public void onUnityExit() {
        if (checkInit()) {
            holderRef.get().onUnityExit();
        }
    }

    public void onUnityEnterRoom(int rid) {
        if (checkInit()) {
            holderRef.get().onUnityEnterRoom(rid);
        }
    }

    public void onUnityExitRoom() {
        if (checkInit()) {
            holderRef.get().onUnityExitRoom();
        }
    }

    public void gotoFixRoomSetting() {
        if (checkInit()) {
            holderRef.get().gotoFixRoomSetting();
        }
    }

    public void searchRoom(String ridStr, int scene, boolean force, String pwd) {
        if (!checkInit()) return;
        if (operator != null) {
            operator.searchRoom(activityRef.get(), ridStr, scene, force, pwd);
        }
    }

    public void showInviteDialog(Activity activity, int rid) {
        if (operator != null) {
            operator.showInviteDialog(activity, rid);
        }
    }

    public void showInviteFixRoomActivity(int rid) {
        if (!checkInit()) return;
        Context context = activityRef.get();
        Intent intent = new Intent(context, FixRoomUnityFuncActivity.class);
        intent.putExtra(IntentConfig.FIX_ROOM_UNITY_FUNC, FixRoomUnityFuncActivity.INVITE);
        intent.putExtra(IntentConfig.INVITE_EXTRA_RID, rid);
        NativeCallUnityManager.getInstance().setUnityNeedPause(false, getService());
        context.startActivity(intent);
    }

    public void setFixRoomActivityVisible() {
        if (checkInit()) {
            holderRef.get().setFixRoomActivityVisible();
        }
    }

    public void showMagicFaceActivity(int rid, long delay) {
        if (!checkInit()) return;
        Context context = activityRef.get();
        Intent intent = new Intent(context, FixRoomUnityFuncActivity.class);
        intent.putExtra(IntentConfig.FIX_ROOM_UNITY_FUNC, FixRoomUnityFuncActivity.MAGIC_FACE);
        intent.putExtra(IntentConfig.INT_ROOM_ID, rid);
        intent.putExtra(IntentConfig.MAGIC_FACE_DELAY, delay);
        NativeCallUnityManager.getInstance().setUnityNeedPause(false, getService());
        context.startActivity(intent);
    }

    public void showUserDialogActivity(int uid, int gameType, String subSource) {
        if (!checkInit()) return;
        if (!multiCallChecker.check(MultiCallChecker.KEY_USER_DIALOG)) return;
        Context context = activityRef.get();
        Intent intent = new Intent(context, FixRoomUnityFuncActivity.class);
        intent.putExtra(IntentConfig.FIX_ROOM_UNITY_FUNC, FixRoomUnityFuncActivity.USER_DIALOG);
        intent.putExtra(IntentConfig.TARGET_UID, uid);
        intent.putExtra(IntentConfig.SUB_SOURCE, subSource);
        intent.putExtra(FixRoomUnityFuncActivity.INTENT_GAME_TYPE, gameType);
        NativeCallUnityManager.getInstance().setUnityNeedPause(false, getService());
        context.startActivity(intent);
    }

    public void showSendGiftActivity(int uid, int gameType, String subSource) {
        showSendGiftActivity(uid, gameType, subSource, 0);
    }

    public void showSendGiftActivity(int uid, int delay) {
        showSendGiftActivity(uid, GameType.GAME_TYPE_UN_DEFINED, "", delay);
    }

    public void showSendGiftActivity(int uid, int gameType, String subSource, int delay) {
        ThreadUtil.runOnUiThreadDelay(delay, () -> {
            if (!checkInit()) return;
            Context context = activityRef.get();
            Intent intent = new Intent(context, FixRoomUnityFuncActivity.class);
            intent.putExtra(IntentConfig.FIX_ROOM_UNITY_FUNC, FixRoomUnityFuncActivity.SEND_GIFT);
            intent.putExtra(IntentConfig.SUB_SOURCE, subSource);
            intent.putExtra(FixRoomUnityFuncActivity.INTENT_GAME_TYPE, gameType);
            intent.putExtra(IntentConfig.TARGET_UID, uid);
            NativeCallUnityManager.getInstance().setUnityNeedPause(false, getService());
            context.startActivity(intent);
        });
    }

    public void requestBuyGoods(int goodsId, String goodsInfo) {
        HLog.d(TAG, "requestBuyGoods, goodsInfo: " + goodsInfo);
        if (!checkInit()) return;
        Context context = activityRef.get();
        Intent intent = new Intent(context, FixRoomUnityFuncActivity.class);
        intent.putExtra(IntentConfig.FIX_ROOM_UNITY_FUNC, FixRoomUnityFuncActivity.PAY);
        intent.putExtra(IntentConfig.GOODS_ID, goodsId);
        intent.putExtra(IntentConfig.GOODS_JSON, goodsInfo);
        NativeCallUnityManager.getInstance().setUnityNeedPause(false, getService());
        context.startActivity(intent);
    }

    public void showUserDialog(Activity activity, int uid, int gameType, String subSource) {
        if (operator != null) {
            operator.showUserDialog(activity, uid, gameType, subSource);
        }
    }

    public void showSendGift(Activity activity, int uid, int gameType, String subSource) {
        if (operator != null) {
            operator.showSendGift(activity, uid, gameType, subSource);
        }
    }

    public void sendChatMsg(int rid, int seat, String content) {
        if (checkInit()) {
            holderRef.get().sendChatMsg(rid, seat, content);
        }
    }

    private void reqVoice2Match(String tip, ReqPermissionCallback callback) {
        if (!checkInit()) return;
        Context context = activityRef.get();
        Intent intent = new Intent(context, FixRoomUnityFuncActivity.class);
        intent.putExtra(IntentConfig.FIX_ROOM_UNITY_FUNC, FixRoomUnityFuncActivity.REQ_PERMISSION);
        intent.putExtra(IntentConfig.PERMISSION_TIP, tip);
        intent.putExtra(IntentConfig.PERMISSION_TYPE, Manifest.permission.RECORD_AUDIO);
        NativeCallUnityManager.getInstance().setUnityNeedPause(false, getService());
        permissionCallback = callback;
        context.startActivity(intent);
    }

    public void showTypeNotSupportDialog(int rid) {
        if (!checkInit()) return;
        if (FixRoomUnityFuncActivity.isIsDialogShowing()) return; // 防重入
        if (holderRef.get().curFixRoomInfo().getRid() != rid) return;
        Context context = activityRef.get();
        Intent intent = new Intent(context, FixRoomUnityFuncActivity.class);
        intent.putExtra(IntentConfig.FIX_ROOM_UNITY_FUNC, FixRoomUnityFuncActivity.TYPE_NOT_SUPPORT);
        NativeCallUnityManager.getInstance().setUnityNeedPause(false, getService());
        context.startActivity(intent);
    }

    private String getReqVoiceTip(int gameType) {
        if (FixRoomUtil.isAvatarWolf(gameType)) {
            return ResUtil.getStr(R.string.need_voice_permission_wolf);
        } else {
            return ResUtil.getStr(R.string.game_request_mic_permission_tips);
        }
    }

    public void check2QuickMatch(int gameType) {
        if (!checkInit()) return;
        if (gameType == GameType.GAME_TYPE_MUSIC_HUM) {
            quickMatch(gameType);
            return;
        }
        if (FixRoomUtil.needVoice(gameType) && !PermissionUtil.hasPermission(activityRef.get(), Manifest.permission.RECORD_AUDIO)) {
            reqVoice2Match(getReqVoiceTip(gameType), new ReqPermissionCallback() {
                @Override
                public void hasPermission() {
                    quickMatch(gameType);
                }

                @Override
                public void noPermission() {
                    ToastUtil.show(ResUtil.getStr(R.string.no_permission_to_match));
                }
            });
        } else {
            quickMatch(gameType);
        }
    }

    private void quickMatch(int gameType) {
        FixRoomPacketSender.fixQuickMatchReq(gameType, Common.BET_LEVEL_PRIMARY, 0,
                new SeqCallback() {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        FixRoomPackets.FixQuickMatchRsp quickMatchRsp = (FixRoomPackets.FixQuickMatchRsp) head.message;
                        FixRoomInfo fixRoomInfo = FixRoomInfo.parse(quickMatchRsp.getAllInfo().getRoomInfo());
                        FixRoomUtil.unityEnterRoom(fixRoomInfo);
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                });
    }

    public void check2SitDown(int rid, int seatNum, int gameType) {
        if (!checkInit()) return;
        FixRoomPacketSender.fixSitReq(rid, seatNum, getSeqCb(rid));
    }

    public void ready(int rid) {
        if (operator != null) {
            operator.ready(rid);
        }
    }

    public void unready(int rid) {
        FixRoomPacketSender.fixUnreadyReq(rid, false, getSeqCb(rid));
    }

    public void pickUp(int rid, int uid, int seatNum) {
        FixRoomPacketSender.fixForceUnSeat(rid, uid, seatNum, getSeqCb(rid));
    }

    public void unseat(int rid) {
        FixRoomPacketSender.fixUnseatReq(rid, getSeqCb(rid));
    }

    public void kick(int rid, int uid) {
        List<Integer> uidList = new ArrayList<>();
        uidList.add(uid);
        FixRoomPacketSender.fixKickOutReq(rid, uidList, getSeqCb(rid));
    }

    public void createRoom(int gameType) {
        if (!checkInit()) return;
        if (operator != null) {
            operator.createRoom(activityRef.get(), gameType);
        }
    }

    public void exitRoom(int rid) {
        if (operator != null) {
            operator.exitRoom(rid);
        }
    }

    public void syncFixRoomInfo(int rid) {
        if (operator != null) {
//            FixRoomUnityInfo info = FixRoomUnityInfo.parse(operator.getFixRoomInfo(rid));
//            NativeCallUnityManager.getInstance().syncFixRoom(UnityUtil.getGson().toJson(info), getService());
            operator.syncFixRoomInfo(rid);
        }
    }

    public void reportFixRoomChatMsg(int rid, int uid, String mid) {
        if (checkInit()) {
            holderRef.get().reportFixRoomChatMsg(rid, uid, mid);
        }
    }

    public void sendFixRoomSpeechGift(int sendUid, int receiveUid, int gift, int rid) {
        if (operator != null) {
            operator.sendFixRoomSpeechGift(sendUid, receiveUid, gift, rid);
        }
    }

    public void changeFixRoomLockState(int rid, int gameType, boolean lock) {
        if (!checkInit()) return;
        Context context = activityRef.get();
        if (operator != null && context != null) {
            operator.changeFixRoomLockState(context, rid, gameType, lock);
        }
    }

    public void showMagicFaceDialog(Activity activity, RelativeLayout root, int rid, long delay) {
        if (operator != null) {
            operator.showMagicFaceDialog(activity, root, rid, delay);
        }
    }

    public SocialGameMsgCallback getSocialCallback() {
        if (checkInit()) {
            return holderRef.get().getSocialCallback();
        }
        return null;
    }

    public FixRoomInfo getFixRoomInfo(int rid) {
        if (operator != null) {
            return operator.getFixRoomInfo(rid);
        }
        return new FixRoomInfo();
    }

    public void clear() {

    }

    public FixRoomInfo curFixRoomInfo() {
        if (checkInit()) return holderRef.get().curFixRoomInfo();
        return null;
    }

    public void gotoUnity(Context context, int scene, FixRoomInfo fixRoomInfo) {
        if (operator != null) {
            operator.gotoUnity(context, scene, fixRoomInfo);
        }
    }

    public void updateMsg2MemoryDB(ChatMsg msg) {
        if (checkInit()) {
            holderRef.get().updateMsg2MemoryDB(msg);
        }
    }

    public void killActivity() {
        if (checkInit()) {
            holderRef.get().killFixRoomActivity();
        }
    }

    //unity改为直接传wespygoods信息，故不再使用goodsID再获取
    @Deprecated
    public void requestPay(Context context, int goodsId, DialogCallback dialogCallback) {
        if (operator != null) {
            operator.requestPay(context, goodsId, dialogCallback);
        }
    }

    public void requestPay(Context context, String goodsInfo, DialogCallback dialogCallback) {
        if (operator != null) {
            UnityGoods unityGoods = UnityUtil.getGson().fromJson(goodsInfo, UnityGoods.class);
            operator.requestPay(context, unityGoods.getWespyGoods(), dialogCallback);
        }
    }

    public Class<? extends Service> getService() {
        if (operator != null) {
            return operator.getService();
        } else {
            return null;
        }
    }

    /**子进程**/
    private IUnityChat unityChat;
    //由子进程的XRoomUnityActivity负责的任务走这里，主要为聊天相关，实现类为XRoomUnityPresenter
    public void setUnityChat(IUnityChat unityChat) {
        this.unityChat = unityChat;
    }

    public void showChatSendView(int rid, int seat) {
        if (unityChat != null) {
            unityChat.showSendView(rid, seat);
        }
    }

    public void showBarrage(String content, int type, int uid, int gameType) {
        if (unityChat != null) {
            unityChat.showBarrage(content, type, uid, gameType);
        }
    }

    public void showGiftAnim(GiftShowInfo info) {
        if (unityChat != null) {
            unityChat.showGiftAnim(info);
        }
    }

    public void onSpyWordFeedback(String spy, String citizen, boolean good) {
        if (operator != null) {
            operator.onSpyWordFeedback(spy, citizen, good);
        }
    }

    public void gotoContributeWord(Activity activity, boolean blank) {
        if (operator != null) {
            operator.gotoContributeWord(activity, blank);
        }
    }

    public boolean isAvatarWolfActivated() {
        if (operator != null) {
            return operator.isAvatarWolfActivated();
        }
        return false;
    }

    public boolean isAvatarSpyActivated() {
        if (operator != null) {
            return operator.isAvatarSpyActivated();
        }
        return false;
    }
}
