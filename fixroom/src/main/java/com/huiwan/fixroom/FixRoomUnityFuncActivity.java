package com.huiwan.fixroom;

import android.content.DialogInterface;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import com.huiwan.base.str.ResUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.GameType;
import com.huiwan.constants.IntentConfig;
import com.huiwan.fixroom.entity.UnityGoods;
import com.wepie.liblog.main.HLog;
import com.wepie.libpermission.PermissionCallback;
import com.wepie.libpermission.WPPermission;
import com.wepie.unity.NativeCallUnityManager;
import com.wepie.unity.UnityUtil;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.dialog.DialogCallback;
import com.wepie.wespy.helper.dialog.PermissionDialog;

import java.util.List;

public class FixRoomUnityFuncActivity extends BaseActivity {
    private static final String TAG = "FixRoomUnityFuncActivity";
    public static final String INTENT_GAME_TYPE = "intent_game_type";
    public static final int NONE = 0;
    public static final int INVITE = 1;
    public static final int USER_DIALOG = 2;
    public static final int SEND_GIFT = 3;
    public static final int MAGIC_FACE = 4;
    public static final int REQ_PERMISSION = 5;
    public static final int PAY = 6;
    public static final int TYPE_NOT_SUPPORT = 7;

    private static boolean isDialogShowing = false;

    private boolean cleared = false;
    private RelativeLayout rootLay;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        super.onCreate(savedInstanceState);

//        getWindow().getDecorView().setSystemUiVisibility(
//                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
//                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
//                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
//                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
//                        | View.SYSTEM_UI_FLAG_FULLSCREEN
//                        | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);

        rootLay = new RelativeLayout(this);
        rootLay.setBackgroundColor(0x80000000);
        setContentView(rootLay);
        rootLay.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        initUnityStatusBar();
        int type = getIntent().getIntExtra(IntentConfig.FIX_ROOM_UNITY_FUNC, NONE);
        if (type == INVITE) {
            invite();
        } else if (type == USER_DIALOG) {
            showUserDialog();
        } else if (type == SEND_GIFT) {
            sendGift();
        } else if (type == MAGIC_FACE) {
            showMagicFace();
        } else if (type == REQ_PERMISSION) {
            showReqPermission();
        } else if (type == PAY) {
            requestPay();
        } else if (type == TYPE_NOT_SUPPORT) {
            showTypeNotSupportDialog();
        } else {
            finish();
        }
    }

    private void initUnityStatusBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Window mWindow = getWindow();
            mWindow.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            View decorView = mWindow.getDecorView();
            int option = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN //全屏
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE; //防止系统栏隐藏时内容区域大小发生变化
            decorView.setSystemUiVisibility(option);
            mWindow.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);  //需要设置这个才能设置状态栏颜色
            mWindow.setStatusBarColor(Color.TRANSPARENT);//设置状态栏颜色
            mWindow.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        NativeCallUnityManager.getInstance().pauseOrResumeUnity(false, FixRoomStateManager.getInstance().getService());
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (isFinishing()) {
            clear();
        } else {
            NativeCallUnityManager.getInstance().pauseOrResumeUnity(true, FixRoomStateManager.getInstance().getService());
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (!cleared) clear();
    }

    private void clear() {
        // NativeCallUnityManager.getInstance().pauseOrResumeUnity(false, FixRoomStateManager.getInstance().getService());
        NativeCallUnityManager.getInstance().setUnityNeedPause(true, FixRoomUtil.getService());
        cleared = true;
    }

    public void requestPay() {
        int goodsId = getIntent().getIntExtra(IntentConfig.GOODS_ID, 0);
        String goodsInfo = getIntent().getStringExtra(IntentConfig.GOODS_JSON);
        String appleProductId = UnityUtil.getGson().fromJson(goodsInfo, UnityGoods.class).appleProductId;
        HLog.d(TAG, "requestPay, goodsInfo: " + goodsInfo);
        if (goodsId > 0) {
            FixRoomStateManager.getInstance().requestPay(this, goodsInfo, new DialogCallback() {
                @Override
                public void onCancel() {
                    NativeCallUnityManager.getInstance().purchaseResult(false, "", FixRoomStateManager.getInstance().getService());
                    finish();
                }

                @Override
                public void onEnter() {
                    NativeCallUnityManager.getInstance().purchaseResult(true, appleProductId, FixRoomStateManager.getInstance().getService());
                    finish();
                }
            });
        } else {
            finish();
        }
    }

    private void invite() {
        int rid = getIntent().getIntExtra(IntentConfig.INVITE_EXTRA_RID, 0);
        if (rid > 0) {
            FixRoomUtil.inviteFixRoom(this, rid);
        } else {
            finish();
        }
    }

    private void showUserDialog() {
        int uid = getIntent().getIntExtra(IntentConfig.TARGET_UID, 0);
        int gameType = getIntent().getIntExtra(FixRoomUnityFuncActivity.INTENT_GAME_TYPE, GameType.GAME_TYPE_UN_DEFINED);
        String subSource = getIntent().getStringExtra(IntentConfig.SUB_SOURCE);
        if (subSource == null) subSource = "";
        if (uid > 0) {
            FixRoomUtil.showUserDialog(this, uid, gameType, subSource);
        } else {
            finish();
        }
    }

    private void sendGift() {
        int uid = getIntent().getIntExtra(IntentConfig.TARGET_UID, 0);
        String subSource = getIntent().getStringExtra(IntentConfig.SUB_SOURCE);
        int gameType = getIntent().getIntExtra(FixRoomUnityFuncActivity.INTENT_GAME_TYPE, GameType.GAME_TYPE_UN_DEFINED);
        if (uid > 0) {
            FixRoomUtil.showSendGift(this, uid, gameType, subSource);
        } else {
            finish();
        }
    }

    private void showMagicFace() {
        int rid = getIntent().getIntExtra(IntentConfig.INT_ROOM_ID, 0);
        long delay = getIntent().getLongExtra(IntentConfig.MAGIC_FACE_DELAY, -1);
        if (rid > 0 && delay >= 0) {
            rootLay.setBackgroundColor(0x00000000);
            FixRoomUtil.showMagicFaceDialog(this, rootLay, rid, delay);
        } else {
            finish();
        }
    }

    private void showReqPermission() {
        String tip = getIntent().getStringExtra(IntentConfig.PERMISSION_TIP);
        String permission = getIntent().getStringExtra(IntentConfig.PERMISSION_TYPE);
        if (TextUtils.isEmpty(permission)) return;
        rootLay.setBackgroundColor(0x00000000);
        WPPermission.with(this)
                .requestDialogTip(tip)
                .permission(permission)
                .request(new PermissionCallback() {
                    @Override
                    public void hasPermission(List<String> granted, boolean isAll, boolean alreadyHas) {
                        finish();
                        FixRoomStateManager.getInstance().invokePermissionTask(true);
                    }

                    @Override
                    public void noPermission(List<String> denied, boolean quick) {
                        if (quick) {
                            PermissionDialog.showJumpPermissionDialog(FixRoomUnityFuncActivity.this,
                                    ResUtil.getStr(R.string.permission_denied_tip), new DialogCallback() {
                                        @Override
                                        public void onCancel() {
                                            finish();
                                        }

                                        @Override
                                        public void onEnter() {
                                            finish();
                                        }
                                    });
                        } else {
                            finish();
                        }
                        FixRoomStateManager.getInstance().invokePermissionTask(false);
                    }
                });
    }

    private void showTypeNotSupportDialog() {
        DialogBuild.newBuilder(this).setContent(R.string.type_not_support)
                .setSingleBtn(true)
                .setSureTx(R.string.ok)
                .setCanCancel(false)
                .setDialogCallback(new DialogBuild.DialogCallback() {
                    @Override
                    public void onClickSure() {
                        finish();
                        NativeCallUnityManager.getInstance().remoteExit(FixRoomStateManager.getInstance().getService());
                        FixRoomStateManager.getInstance().killActivity();
                    }
                }).
                setDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        finish();
                    }
                }).show();
        isDialogShowing = true;
    }


    @Override
    public void finish() {
        isDialogShowing = false;
        super.finish();
        overridePendingTransition(0, 0);
    }

    public static boolean isIsDialogShowing() {
        return isDialogShowing;
    }

    @Override
    public int supportFloatView() {
        return 0;
    }
}