package com.huiwan.fixroom.avatarwolf;

import android.content.Context;
import android.text.TextUtils;

import com.huiwan.barrage.BarrageAnimView;
import com.huiwan.base.str.ResUtil;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.fixroom.FixRoomStateManager;
import com.huiwan.fixroom.FixRoomUtil;
import com.huiwan.fixroom.R;
import com.huiwan.fixroom.UnityGameMockView;
import com.huiwan.ipc.ReqCallback;
import com.huiwan.ipc.model.IPCMsg;
import com.huiwan.user.UserService;
import com.huiwan.user.UserSimpleInfoCallback;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.liblog.main.HLog;
import com.wepie.unity.NativeCallUnityManager;
import com.wepie.unity.UnityUtil;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.fixroom.FixRoomInfo;
import com.wepie.wespy.model.entity.fixroom.FixRoomMsg;

import org.json.JSONException;
import org.json.JSONObject;

public class AvatarWolfMockView extends UnityGameMockView {
    public AvatarWolfMockView(int gameType) {
        this.gameType = gameType;
    }

    public AvatarWolfMockView(){}

    @Override
    public void initUnity(Context context, FixRoomInfo roomInfo) {
        FixRoomUtil.gotoAvatarWolf(context, roomInfo);
    }

    @Override
    protected void handleRoomMsg(FixRoomMsg chatMsg) {
        if (chatMsg.getMediaType() == ChatMsg.MEDIA_TYPE_GIFT) {
            handleGiftMsg(chatMsg);
        } else if (chatMsg.getMediaType() == ChatMsg.MEDIA_TYPE_SYSTEM) {
            sendBarrage(chatMsg);
        } else if (chatMsg.getMediaType() == ChatMsg.MEDIA_TYPE_TEXT) {
            NativeCallUnityManager.getInstance().canShowWolfKillBarrage(chatMsg.getSend_uid(),
                    FixRoomStateManager.getInstance().getService(), new ReqCallback() {
                        @Override
                        public void onSuccess(IPCMsg rspMsg) {
                            try {
                                JSONObject jsonObject = new JSONObject(rspMsg.getRspData().msg);
                                boolean show = jsonObject.getBoolean("show");
                                if (show) {
                                    sendBarrage(chatMsg);
                                }
                            } catch (JSONException e) {
                                e.printStackTrace();
                            }
                        }

                        @Override
                        public void onError(int code, String s) {

                        }
                    });
        }
    }

    private void sendBarrage(FixRoomMsg chatMsg) {
        UserService.get().getCacheSimpleUser(chatMsg.getSend_uid(), new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                String text = "<font color='#f19f17'>" + simpleInfo.getNickname() + "</font>" + ": " + chatMsg.getContent();
                NativeCallUnityManager.getInstance().showBarrage(text, BarrageAnimView.TYPE_DEFAULT, chatMsg.getSend_uid(), gameType,
                        FixRoomStateManager.getInstance().getService());
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });
    }

    @Override
    protected void handleGiftMsg(FixRoomMsg msg) {
        if (msg.getMediaType() != ChatMsg.MEDIA_TYPE_GIFT) return;
        boolean animShown = checkShowInUnity(msg);
        UserService.get().getCacheSimpleUser(msg.getSend_uid(), new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(final UserSimpleInfo sender) {
                UserService.get().getCacheSimpleUser(msg.getGiftRecvUid(), new UserSimpleInfoCallback() {
                    @Override
                    public void onUserInfoSuccess(UserSimpleInfo receiver) {
                        int giftId = msg.getGiftId();
                        int giftNum = msg.getGiftNum();
                        Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(giftId);
                        if (gift == null || receiver == null || sender == null) return;
                        String head = "<font color='#f19f17'>" + sender.getNickname() + "</font>"
                                + ResUtil.getStr(R.string.gift_msg_part_send) +
                                "<font color='#f19f17'>" + receiver.getNickname() + "</font> ";
                        String body = giftNum + gift.getUnit() + gift.getName() + "，" + msg.getContent();
                        if (!TextUtils.isEmpty(msg.getGiftDesc())) {
                            String giftAwayCoin = "<font color='#f19f17'>" + msg.getGiftDesc() + "</font>";
                            body = body + ResUtil.getStr(R.string.gift_msg_part_receiver_get) + giftAwayCoin + ResUtil.getStr(R.string.gift_msg_part_full_stop);
                        }
                        msg.setStatus(ChatMsg.STATUS_VIEWED);
                        FixRoomStateManager.getInstance().updateMsg2MemoryDB(msg);

                        NativeCallUnityManager.getInstance().showBarrage(head + body, BarrageAnimView.TYPE_GIFT, 0, gameType,
                                FixRoomUtil.getService());
                        if (!animShown) {
                            GiftShowInfo giftShowInfo = FixRoomUtil.fromChatMessage(msg);
                            NativeCallUnityManager.getInstance().showGiftAnim(UnityUtil.getGson().toJson(giftShowInfo),
                                    FixRoomUtil.getService());
                        }
                    }

                    @Override
                    public void onUserInfoFailed(String description) {
                        HLog.e(TAG, HLog.USR, description);
                    }
                });
            }

            @Override
            public void onUserInfoFailed(String description) {
                HLog.e(TAG, HLog.USR, description);
            }
        });
    }

    @Override
    public boolean onBackPress() {
        return false;
    }
}
