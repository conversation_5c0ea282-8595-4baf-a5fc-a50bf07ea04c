package com.huiwan.fixroom.tcp;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.Language;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.voiceroom.FaceInfo;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.constants.GameType;
import com.huiwan.libtcp.PacketSendHelper;
import com.huiwan.libtcp.sender.TcpBaseSender;
import com.huiwan.component.gift.send.GiftSendInfo;
import com.huiwan.user.LoginHelper;
import com.huiwan.libtcp.config.ClientVersionUtil;
import com.huiwan.libtcp.config.ProtoConfig;
import com.huiwan.libtcp.callback.SeqCallback;
import com.wepie.wespy.net.tcp.packet.FixRoomPackets;
import com.wepie.wespy.net.tcp.packet.HeadPackets;

import java.util.List;

// Created by bigwen on 2018/7/16.
public class FixRoomPacketSender {

    public static void fixGetRoomListReq(SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(0, FixRoomPackets.FixRoomOpType.GET_ROOM_LIST_VALUE);

        FixRoomPackets.FixGetRoomListReq fixGetRoomListReq = FixRoomPackets.FixGetRoomListReq.newBuilder().build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setRoomList(fixGetRoomListReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixSyncRoomReq(int rid, int version, int msgseq, int visitorVer, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.SYNC_ROOM_VALUE);

        FixRoomPackets.FixSyncRoomReq fixSyncRoomReq = FixRoomPackets.FixSyncRoomReq.newBuilder()
                .setVersion(version)
                .setMsgseq(msgseq)
                .setVisitorVer(visitorVer)
                .setMaxGtype(GameType.MAX_GAME_TYPE)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setSyncRoom(fixSyncRoomReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixForceUnSeat(int rid, int uid, int seatNum, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.FORCE_UNSEAT_VALUE);

        FixRoomPackets.FixForceUnseatReq req = FixRoomPackets.FixForceUnseatReq.newBuilder()
                .setSeatNum(seatNum)
                .setUnseatUser(uid)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setForceUnseat(req).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);

    }

    public static void fixQuickMatchReq(int gameType, int betlevel, int musicCate, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(0, FixRoomPackets.FixRoomOpType.QUICK_MATCH_VALUE);

        FixRoomPackets.FixQuickMatchReq fixQuickMatchReq = FixRoomPackets.FixQuickMatchReq.newBuilder()
                .setBetLevel(betlevel)
                .setGameType(gameType)
                .setMusicCate(musicCate)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setQuickMatch(fixQuickMatchReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixCreateRoomReq(int gameType, String name, int gamerNum, int lease_type, List<Integer> invited_list, int roomType, boolean allowEnter, String password, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(0, FixRoomPackets.FixRoomOpType.CREATE_ROOM_VALUE);

        FixRoomPackets.FixCreateRoomReq fixCreateRoomReq = FixRoomPackets.FixCreateRoomReq.newBuilder()
                .setGameType(gameType)
                .setName(name)
                .setGamerNum(gamerNum)
                .setLeaseType(lease_type)
                .addAllInvitedList(invited_list)
                .setRoomType(roomType)
                .setAllowEnter(allowEnter)
                .setPasswd(password)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setCreate(fixCreateRoomReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixEnterRoomReq(int rid, int followUid, int gameType, String pwd, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.ENTER_ROOM_VALUE);

        FixRoomPackets.FixEnterRoomReq fixEnterRoomReq = FixRoomPackets.FixEnterRoomReq.newBuilder()
                .setMaxGtype(GameType.MAX_GAME_TYPE)
                .setFollowUid(followUid)
                .setGameType(gameType)
                .setPasswd(pwd).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setEnter(fixEnterRoomReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixExitRoomReq(int rid, boolean isDismiss, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.EXIT_ROOM_VALUE);

        FixRoomPackets.FixExitRoomReq fixExitRoomReq = FixRoomPackets.FixExitRoomReq.newBuilder()
                .setDismiss(isDismiss ? 1 : 0)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setExit(fixExitRoomReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixSitReq(int rid, int seatNum, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.SIT_VALUE);

        FixRoomPackets.FixSitReq fixSitReq = FixRoomPackets.FixSitReq.newBuilder()
                .setSeatNum(seatNum).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setSit(fixSitReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixUnseatReq(int rid, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.UNSEAT_VALUE);

        FixRoomPackets.FixUnseatReq fixUnseatReq = FixRoomPackets.FixUnseatReq.newBuilder().build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setUnseat(fixUnseatReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    /**
     * @param rid
     * @param withSit  聊天界面，准备和坐下同时进行
     * @param callback
     */
    public static void fixReadyReq(int rid, boolean withSit, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.READY_VALUE);

        FixRoomPackets.FixReadyReq fixReadyReq = FixRoomPackets.FixReadyReq.newBuilder()
                .setSitFirst(withSit)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setReady(fixReadyReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    /**
     * @param rid
     * @param withUnSit 聊天界面，准备和坐下同时进行
     * @param callback
     */
    public static void fixUnreadyReq(int rid, boolean withUnSit, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.UNREADY_VALUE);

        FixRoomPackets.FixUnreadyReq fixUnreadyReq = FixRoomPackets.FixUnreadyReq.newBuilder()
                .setUnseatMeantime(withUnSit)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setUnready(fixUnreadyReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixSendRoomMsgReq(int rid, int sender, int msgType, int msg_subtype, String content, String ext, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.SEND_ROOM_MSG_VALUE);

        FixRoomPackets.FixSendRoomMsgReq fixSendRoomMsgReq = FixRoomPackets.FixSendRoomMsgReq.newBuilder()
                .setSender(sender)
                .setMsgTypeValue(msgType)
                .setMsgSubtype(msg_subtype)
                .setContent(content)
                .setExt(ext)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setSendMsg(fixSendRoomMsgReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixRoomFaceReq(int rid, FaceInfo faceInfo, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.MAGIC_EMOTION_VALUE);

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setMagicEmotion(FixRoomPackets.FixMagicEmotionReq.newBuilder()
                .setFaceId(faceInfo.getFaceId())
                .setRandRange(faceInfo.getResSize())).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixInviteFriendReq(int rid, List<Integer> inviteList, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.INVITE_FRIEND_VALUE);

        FixRoomPackets.FixInviteFriendReq fixInviteFriendReq = FixRoomPackets.FixInviteFriendReq.newBuilder().addAllInviteList(inviteList).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setInviteFriend(fixInviteFriendReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixKickOutReq(int rid, List<Integer> kickUser, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.KICK_OUT_VALUE);

        FixRoomPackets.FixKickOutReq fixKickOutReq = FixRoomPackets.FixKickOutReq.newBuilder().addAllKickedUser(kickUser).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setKickOut(fixKickOutReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixAssembleReq(int rid, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.ASSEMBLE_VALUE);

        FixRoomPackets.FixAssembleReq fixAssembleReq = FixRoomPackets.FixAssembleReq.newBuilder().build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setAssemble(fixAssembleReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixModRoomNameReq(int rid, String name, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.MOD_ROOM_NAME_VALUE);

        FixRoomPackets.FixModRoomNameReq fixModRoomNameReq = FixRoomPackets.FixModRoomNameReq.newBuilder().setNewName(name).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setModName(fixModRoomNameReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixModRoomNoteReq(int rid, String note, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.MOD_ROOM_NOTE_VALUE);

        FixRoomPackets.FixModRoomNoteReq fixModRoomNoteReq = FixRoomPackets.FixModRoomNoteReq.newBuilder().setNewNote(note).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setModNote(fixModRoomNoteReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixModEnterLevelReq(int rid, int enter_level, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.MOD_ENTER_LEVEL_VALUE);

        FixRoomPackets.FixModEnterLevelReq fixModEnterLevelReq = FixRoomPackets.FixModEnterLevelReq.newBuilder().setEnterLevel(enter_level).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setModEnterLevel(fixModEnterLevelReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixModAllowEnterReq(int rid, boolean allowEnter, String pwd, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.MOD_ALLOW_ENTER_VALUE);

        FixRoomPackets.FixModAllowEnterReq fixModAllowEnterReq = FixRoomPackets.FixModAllowEnterReq.newBuilder().setAllowEnter(allowEnter).setPasswd(pwd).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setModAllowEnter(fixModAllowEnterReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixModBgUrlReq(int rid, String bgUrl, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.MOD_BG_URL_VALUE);

        FixRoomPackets.FixModBgUrlReq fixModBgUrlReq = FixRoomPackets.FixModBgUrlReq.newBuilder().setNewBgUrl(bgUrl).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setModBgUrl(fixModBgUrlReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixSetAdminListReq(int rid, List<Integer> adminList, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.SET_ADMIN_LIST_VALUE);

        FixRoomPackets.FixSetAdminListReq fixSetAdminListReq = FixRoomPackets.FixSetAdminListReq.newBuilder().addAllAdminList(adminList).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setSetAdminList(fixSetAdminListReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixDonateLeaseReq(int rid, int donate_lease_type, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.DONATE_LEASE_VALUE);

        FixRoomPackets.FixDonateLeaseReq fixDonateLeaseReq = FixRoomPackets.FixDonateLeaseReq.newBuilder().setDonateLeaseType(donate_lease_type).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setDonateLease(fixDonateLeaseReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixApplyStayReq(int rid, String reason, int rec_uid, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.APPLY_STAY_VALUE);

        FixRoomPackets.FixApplyStayReq fixApplyStayReq = FixRoomPackets.FixApplyStayReq.newBuilder()
                .setReason(reason)
                .setRecUid(rec_uid)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setApplyStay(fixApplyStayReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixApplyListReq(int rid, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.APPLY_LIST_VALUE);

        FixRoomPackets.FixApplyListReq fixApplyListReq = FixRoomPackets.FixApplyListReq.newBuilder().build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setApplyList(fixApplyListReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixAgreeApplyReq(int rid, int seq, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.AGREE_APPLY_VALUE);

        FixRoomPackets.FixAgreeApplyReq fixAgreeApplyReq = FixRoomPackets.FixAgreeApplyReq.newBuilder().setSeq(seq).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setAgreeApply(fixAgreeApplyReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixIgnoreApplyReq(int rid, int seq, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.IGNORE_APPLY_VALUE);

        FixRoomPackets.FixIgnoreApplyReq fixIgnoreApplyReq = FixRoomPackets.FixIgnoreApplyReq.newBuilder().setSeq(seq).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setIgnoreApply(fixIgnoreApplyReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixClearVisitorsReq(int rid, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.CLEAR_VISITORS_VALUE);

        FixRoomPackets.FixClearVisitorsReq fixClearVisitorsReq = FixRoomPackets.FixClearVisitorsReq.newBuilder().build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setClearVisitors(fixClearVisitorsReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixSendGiftReq(GiftSendInfo sendInfo, SeqCallback callback) {
        Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(sendInfo.giftId);
        int giftType = 0;
        int broadcast = 0;
        if (gift != null) {
            giftType = gift.getGiftType();
            broadcast = gift.broadcast;
        }
        String content = Gift.getGiftSendText(sendInfo.giftId, sendInfo.giftNum);
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(sendInfo.rid, FixRoomPackets.FixRoomOpType.SEND_GIFT_VALUE);

        FixRoomPackets.FixSendGiftReq fixSendGiftReq = FixRoomPackets.FixSendGiftReq.newBuilder()
                .setRecvUid(sendInfo.recUid)
                .setGiftId(sendInfo.giftId)
                .setGiftNum(sendInfo.giftNum)
                .setIsPublic(sendInfo.isPrivate ? 0 : 1)
                .setMsgContent(content)
                .setGiftType(giftType)
                .setIsGiftCard(sendInfo.isGiftCard ? 1 : 0)
                .setBroadContent(sendInfo.extMsg)
                .setBroadcast(broadcast)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setSendGift(fixSendGiftReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixInvitePlayReq(int rid, List<Integer> invite_list, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.INVITE_PLAY_VALUE);

        FixRoomPackets.FixInvitePlayReq fixInvitePlayReq = FixRoomPackets.FixInvitePlayReq.newBuilder()
                .addAllInviteList(invite_list)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setInvitePlay(fixInvitePlayReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixSendRedPacket(int rid, int stuff_id, int num, String message, String password, int type, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.SEND_RED_PACKET_VALUE);

        FixRoomPackets.FixSendRedPacketReq fixSendRedPacketReq = FixRoomPackets.FixSendRedPacketReq.newBuilder()
                .setStuffId(stuff_id)
                .setNum(num)
                .setMessage(message)
                .setPassword(password)
                .setType(type)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setSendRedPacket(fixSendRedPacketReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixGrabRedPacketReq(int rid, String rp_id, String password, int rpSkinId, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.GRAB_RED_PACKET_VALUE);

        FixRoomPackets.FixGrabRedPacketReq fixGrabRedPacketReq = FixRoomPackets.FixGrabRedPacketReq.newBuilder()
                .setRpId(rp_id)
                .setPassword(password)
                .setRpSkinId(rpSkinId)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setGrabRedPacket(fixGrabRedPacketReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }


    public static void fixRecallMsgReq(int rid, long recallTime, int msgSeq, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.RECALL_MSG_VALUE);

        FixRoomPackets.FixRecallMsgReq fixRecallMsgReq = FixRoomPackets.FixRecallMsgReq.newBuilder()
                .setRecalledTimestamp(recallTime)
                .setRecalledMsgseq(msgSeq)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setRecallMsg(fixRecallMsgReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixCloseFreeTalkReq(int rid, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.CLOSE_FREE_TALK_VALUE);

        FixRoomPackets.FixCloseFreeTalkReq fixCloseFreeTalkReq = FixRoomPackets.FixCloseFreeTalkReq.newBuilder().build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setCloseFreeTalk(fixCloseFreeTalkReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixApplyFreeTalkReq(int rid, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.APPLY_FREE_TALK_VALUE);

        FixRoomPackets.FixApplyFreeTalkReq fixApplyFreeTalkReq = FixRoomPackets.FixApplyFreeTalkReq.newBuilder().build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setApplyFreeTalk(fixApplyFreeTalkReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixAgreeFreeTalkReq(int rid, boolean isAgree, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.AGREE_FREE_TALK_VALUE);

        FixRoomPackets.FixAgreeFreeTalkReq fixAgreeFreeTalkReq = FixRoomPackets.FixAgreeFreeTalkReq.newBuilder()
                .setIsAgree(isAgree)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setAgreeFreeTalk(fixAgreeFreeTalkReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixModBetLevelReq(int rid, int betlevel, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.MOD_BET_LEVEL_VALUE);

        FixRoomPackets.FixModBetLevelReq fixModBetLevelReq = FixRoomPackets.FixModBetLevelReq.newBuilder()
                .setBetLevel(betlevel)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setModBetLevel(fixModBetLevelReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixDonateRecordsReq(int rid, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.DONATE_RECORDS_VALUE);

        FixRoomPackets.FixDonateRecordsReq fixDonateRecordsReq = FixRoomPackets.FixDonateRecordsReq.newBuilder().build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setDonateRecords(fixDonateRecordsReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixModGamerNumReq(int rid, int gamerNum, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.MOD_GAMER_NUM_VALUE);

        FixRoomPackets.FixModGamerNumReq fixModGamerNumReq = FixRoomPackets.FixModGamerNumReq.newBuilder().setGamerNum(gamerNum).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setModGamerNum(fixModGamerNumReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixModGameType(int rid, int gameType, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.MOD_GAME_TYPE_VALUE);

        FixRoomPackets.FixModGameTypeReq fixModGameTypeReq = FixRoomPackets.FixModGameTypeReq.newBuilder().setNewGameType(gameType).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setModGameType(fixModGameTypeReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixRoomModWord(int rid, int care, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.MOD_WORD_CATE_VALUE);

        FixRoomPackets.FixModWordCateReq fixModWordCateReq = FixRoomPackets.FixModWordCateReq.newBuilder().setCate(care).build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setModWordCate(fixModWordCateReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixRoomMigrateOldRoom(int rid, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.MIGRATE_OLD_ROOM_VALUE);

        FixRoomPackets.FixMigrateReq fixMigrateReq = FixRoomPackets.FixMigrateReq.newBuilder().build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setMigrate(fixMigrateReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixRoomModMusicCate(int rid, int musicLibType, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(rid, FixRoomPackets.FixRoomOpType.MOD_MUSIC_CATE_VALUE);

        FixRoomPackets.FixModMusicCateReq fixModMusicCateReq = FixRoomPackets.FixModMusicCateReq
                .newBuilder()
                .setCate(musicLibType)
                .build();

        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setModMusicCate(fixModMusicCateReq).build();

        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    public static void fixRoomMigrateOldWolf(List<Integer> ridList, SeqCallback callback) {
        HeadPackets.ReqHead reqHead = buildFixRoomReqHead(0, FixRoomPackets.FixRoomOpType.MIGRATE_OLD_WOLF_VALUE);
        FixRoomPackets.FixMigrateOldWolfReq fixMigrateOldWolfReq = FixRoomPackets.FixMigrateOldWolfReq
                .newBuilder()
                .addAllRids(ridList)
                .build();
        FixRoomPackets.FixRoomReqBody reqBody = FixRoomPackets.FixRoomReqBody.newBuilder().setMigrateOldWolf(fixMigrateOldWolfReq).build();
        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback);
    }

    private static HeadPackets.ReqHead buildFixRoomReqHead(int rid, int type) {
        HeadPackets.ReqHead reqHead = HeadPackets.ReqHead.newBuilder()
                .setAppVersion(LibBaseUtil.getBaseConfig().versionName)
                .setCommand(HeadPackets.CommandType.FIXROOM_VALUE)
                .setType(type)
                .setUid(LoginHelper.getLoginUid())
                .setRid(rid)
                .setSeq(TcpBaseSender.getSeq())
                .setMaxGameType(GameType.MAX_GAME_TYPE)
                .setGameVersion(ClientVersionUtil.VERSION)
                .setSupportVoiceType(ProtoConfig.SUPPORT_ALL_TYPE)
                .setLang(LibBaseUtil.getLang().value)
                .build();
        return reqHead;
    }

}
