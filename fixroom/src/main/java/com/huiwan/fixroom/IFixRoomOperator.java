package com.huiwan.fixroom;

import android.app.Activity;
import android.app.Service;
import android.content.Context;
import android.widget.RelativeLayout;

import com.huiwan.configservice.model.WespyGoods;
import com.wepie.wespy.helper.dialog.DialogCallback;
import com.wepie.wespy.model.entity.fixroom.FixRoomInfo;

/**
 * 涉及FixRoom进房/退房等一系列操作的接口，主工程由FixRoomOperatorImpl实现并传进来
 * created by <PERSON><PERSON><PERSON> on 2021/4/30
 */
public interface IFixRoomOperator {

    void searchRoom(Context context, String ridStr, int scene, boolean force, String pwd);

    void gotoUnity(Context context, int scene, FixRoomInfo fixRoomInfo);

    Class<? extends Service> getService();

    void createRoom(Context context, int gameType);

    void exitRoom(int rid);

    void ready(int rid);

    void showInviteDialog(Activity activity, int rid);

    void showUserDialog(Activity activity, int uid, int gameType, String subSource);

    void showSendGift(Activity activity, int uid, int gameType, String subSource);

    void onSpyWordFeedback(String spy, String citizen, boolean good);

    void gotoContributeWord(Activity activity, boolean blank);

    void sendFixRoomSpeechGift(int sendUid, int receiveUid, int gift, int rid);

    void changeFixRoomLockState(Context context, int rid, int gameType, boolean lock);

    void showMagicFaceDialog(Activity activity, RelativeLayout root, int rid, long delay);

    FixRoomInfo getFixRoomInfo(int rid);

    boolean isAvatarWolfActivated();

    boolean isAvatarSpyActivated();

    @Deprecated
    void requestPay(Context context, int goodsId, DialogCallback dialogCallback);

    void requestPay(Context context, WespyGoods wespyGoods, DialogCallback dialogCallback);

    void syncFixRoomInfo(int rid);
}
