package com.huiwan.fixroom;

import android.text.TextUtils;

import java.util.HashMap;
import java.util.Map;

public class MultiCallChecker {
    public static final String KEY_USER_DIALOG = "key_user_dialog";
    static final Long interval = 1000L;
    private Map<String, Long> timeMap = new HashMap<>();

    public boolean check(String key) {
        Long lastCheck = timeMap.containsKey(key) ? timeMap.get(key) : 0L;
        if (lastCheck == null) return false;
        if (System.currentTimeMillis() - lastCheck >= interval) {
            timeMap.put(key, System.currentTimeMillis());
            return true;
        } else {
            return false;
        }
    }
}
