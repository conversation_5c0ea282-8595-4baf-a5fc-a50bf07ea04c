package com.huiwan.fixroom.avatarspy;

import android.content.Context;

import com.huiwan.barrage.BarrageAnimView;
import com.huiwan.component.gift.GiftAnimUtil;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.fixroom.FixRoomStateManager;
import com.huiwan.fixroom.FixRoomUtil;
import com.huiwan.fixroom.UnityGameMockView;
import com.huiwan.fixroom.entity.FixRoomUnityChatMsg;
import com.wepie.unity.NativeCallUnityManager;
import com.wepie.unity.UnityUtil;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.fixroom.FixRoomInfo;
import com.wepie.wespy.model.entity.fixroom.FixRoomMsg;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

public class AvatarSpyMockView extends UnityGameMockView {
    public AvatarSpyMockView(int gameType) {
        this.gameType = gameType;
    }

    public AvatarSpyMockView() {
    }

    @Override
    public void initUnity(Context context, FixRoomInfo roomInfo) {
        FixRoomUtil.gotoAvatarSpy(context, roomInfo);
    }

    @Override
    protected void handleRoomMsg(FixRoomMsg chatMsg) {
        if (chatMsg.getMediaType() == ChatMsg.MEDIA_TYPE_GIFT) {
            handleGiftMsg(chatMsg);
        } else if (chatMsg.getMediaType() == ChatMsg.MEDIA_TYPE_TEXT
                || chatMsg.getMediaType() == ChatMsg.MEDIA_TYPE_SYSTEM) {
            FixRoomUnityChatMsg unityChatMsg = FixRoomUnityChatMsg.parse(chatMsg);
            String json = UnityUtil.getGson().toJson(unityChatMsg);
            NativeCallUnityManager.getInstance().receiveFixRoomChatMsg(json,
                    FixRoomUtil.getService());
        }
    }

    @Override
    protected void handleGiftMsg(FixRoomMsg msg) {
        if (msg.getMediaType() != ChatMsg.MEDIA_TYPE_GIFT) return;
        boolean animShown = checkShowInUnity(msg);

        msg.setStatus(ChatMsg.STATUS_VIEWED);
        FixRoomStateManager.getInstance().updateMsg2MemoryDB(msg);

        FixRoomUnityChatMsg unityChatMsg = FixRoomUnityChatMsg.parseGiftMsg(msg);
        String json = UnityUtil.getGson().toJson(unityChatMsg);
        NativeCallUnityManager.getInstance().receiveFixRoomChatMsg(json,
                FixRoomUtil.getService());

        //如果不是鲜花鸡蛋，则需要原生播放礼物动画，并且拼出弹幕在原生显示
        if (!animShown) {
            GiftShowInfo giftShowInfo = FixRoomUtil.fromChatMessage(msg);
            if (giftShowInfo == null) return;
            NativeCallUnityManager.getInstance().showGiftAnim(UnityUtil.getGson().toJson(giftShowInfo),
                    FixRoomUtil.getService());
            GiftAnimUtil.getGiftApi().getBarrageContent(giftShowInfo, new Function1<String, Unit>() {
                @Override
                public Unit invoke(String s) {
                    NativeCallUnityManager.getInstance().showBarrage(s, BarrageAnimView.TYPE_GIFT, giftShowInfo.sender, gameType,
                            FixRoomUtil.getService());
                    return Unit.INSTANCE;
                }
            });
        }
    }

    @Override
    public boolean onBackPress() {
        return false;
    }
}
