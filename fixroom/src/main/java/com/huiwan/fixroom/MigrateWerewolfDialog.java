package com.huiwan.fixroom;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.dialog.DialogCallback;

public class MigrateWerewolfDialog extends RelativeLayout {
    private Context mContext;
    private TextView sureTv;
    private TextView cancelTv;
    private DialogCallback callback;

    public MigrateWerewolfDialog(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public MigrateWerewolfDialog(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    public void setCallBack(DialogCallback dialogCallback) {
        callback = dialogCallback;
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.migrate_werewolf_dialog, this);
        sureTv = findViewById(R.id.certificate_sure_btn);
        cancelTv = findViewById(R.id.certificate_cancel_btn);

        sureTv.setOnClickListener( v -> {
            if (callback != null) callback.onEnter();
        });
        cancelTv.setOnClickListener( v -> {
            if (callback != null) callback.onCancel();
        });
    }


    public static void showDialog(Context context, DialogCallback callback) {
        BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_background_80);
        MigrateWerewolfDialog dialogView = new MigrateWerewolfDialog(context);
        dialog.setContentView(dialogView);
        dialogView.setCallBack(new DialogCallback() {
            @Override
            public void onCancel() {
                callback.onCancel();
                dialog.dismiss();
            }

            @Override
            public void onEnter() {
                callback.onEnter();
                dialog.dismiss();
            }
        });
        dialog.setCanceledOnTouchOutside(false);
        dialog.initFullWidth();
        dialog.show();
    }
}
