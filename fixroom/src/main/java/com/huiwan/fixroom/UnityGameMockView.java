package com.huiwan.fixroom;

import android.app.Service;
import android.content.Context;
import android.content.res.Configuration;

import androidx.annotation.Nullable;

import com.huiwan.barrage.BarrageAnimView;
import com.huiwan.component.gift.GiftAnimUtil;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.configservice.model.voiceroom.FaceInfo;
import com.huiwan.fixroom.entity.FixRoomUnityInfo;
import com.huiwan.fixroom.entity.UnityFaceInfo;
import com.wepie.download.DownloadUtil;
import com.wepie.download.SimpleDownloadCallback;
import com.wepie.lib.api.plugins.voice.SpeakerInfo;
import com.wepie.unity.NativeCallUnityManager;
import com.wepie.unity.UnityUtil;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.fixroom.FixRoomInfo;
import com.wepie.wespy.model.entity.fixroom.FixRoomMsg;
import com.wepie.wespy.module.fixroom.ISocialGameView;
import com.wepie.wespy.module.spy.SocialGameMsgCallback;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;

public abstract class UnityGameMockView implements ISocialGameView, IFaceView {
    public abstract void initUnity(Context context, FixRoomInfo roomInfo);

    protected abstract void handleRoomMsg(FixRoomMsg chatMsg);

    protected static final String TAG = UnityGameMockView.class.getName();

    private final List<FixRoomMsg> msgList = new ArrayList<>();
    protected int gameType = 0;

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    @Override
    public void showUserFace(int uid, FaceInfo faceInfo, int resultId) {
        DownloadUtil.downloadFile(faceInfo.getAnimUri(), faceInfo.getLocalGifPath(), true, new SimpleDownloadCallback() {
            @Override
            public void onSuccess(String url, String path) {
                UnityFaceInfo unityFaceInfo = UnityFaceInfo.parse(faceInfo, resultId);
                if (unityFaceInfo != null) {
                    NativeCallUnityManager.getInstance().receiveFixRoomMagicFace(uid,
                            UnityUtil.getGson().toJson(unityFaceInfo), FixRoomUtil.getService());
                }
            }
        });
    }

    @Override
    public void onPushRoomMsg(List<FixRoomMsg> chatMsgs) {
        for (FixRoomMsg msg : chatMsgs) {
            msgList.add(msg);
            handleRoomMsg(msg);
        }
    }

    @Override
    public void onSelfSendMsg(FixRoomMsg msg) {
        msgList.add(msg);
        handleRoomMsg(msg);
    }

    @Override
    public void updateRoomInfo(FixRoomInfo fixRoomInfo) {
        FixRoomUnityInfo unityInfo = FixRoomUnityInfo.parse(fixRoomInfo);
        Class<? extends Service> service = FixRoomUtil.getService();
        if (unityInfo != null && service != null) {
            NativeCallUnityManager.getInstance().syncFixRoom(UnityUtil.getGson().toJson(unityInfo), service);
        }
    }

    @Override
    public void setCallback(SocialGameMsgCallback socialGameMsgCallback) {

    }

    @Override
    public void hideDialog() {

    }

    @Override
    public void onResume() {

    }

    @Override
    public void onPause(boolean isFinish) {

    }

    @Override
    public boolean consumeGift(GiftShowInfo showInfo) {
        //此处通过FixRoomPresenter的onGiftAnimPlayed无法正常调用到，故将展示礼物放在onPushRoomMsg里处理
//        String info = UnityUtil.getGson().toJson(showInfo);
//        NativeCallUnityManager.getInstance().showGiftAnim(info, FixRoomUtil.getService());
        return true;
    }

    @Override
    public void listRefresh() {

    }

    @Override
    public void onSpeak(List<SpeakerInfo> speakerInfos) {

    }

    @Override
    public List<FixRoomMsg> getPublicScreenMsg() {
        return new ArrayList<>(msgList);
    }

    @Nullable
    @Override
    public Object getGameInfo() {
        return null;
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {

    }

    @Override
    public void onClear() {

    }

    @Override
    public void addInParentView(boolean isOnCreate) {

    }

    protected boolean checkShowInUnity(FixRoomMsg msg) {
        if (msg.getGiftId() == Gift.GIFT_ID_EGG_D || msg.getGiftId() == Gift.GIFT_ID_FLOWER_D) {
            NativeCallUnityManager.getInstance().receiveFixRoomSpeechGift(msg.getSend_uid(),
                    msg.getGiftRecvUid(), Gift.getUnityGiftId(msg.getGiftId()), FixRoomUtil.getService());
            return true;
        }
        return false;
    }

    protected void handleGiftMsg(FixRoomMsg msg) {
        if (msg.getMediaType() != ChatMsg.MEDIA_TYPE_GIFT) return;
        if (checkShowInUnity(msg)) return;
        GiftShowInfo giftShowInfo = FixRoomUtil.fromChatMessage(msg);
        if (giftShowInfo == null) {
            return;
        }
        GiftAnimUtil.getGiftApi().getBarrageContent(giftShowInfo, s -> {
            NativeCallUnityManager.getInstance().showBarrage(s, BarrageAnimView.TYPE_GIFT, msg.getSend_uid(), gameType,
                    FixRoomUtil.getService());
            msg.setStatus(ChatMsg.STATUS_VIEWED);
            FixRoomStateManager.getInstance().updateMsg2MemoryDB(msg);
            NativeCallUnityManager.getInstance().showGiftAnim(UnityUtil.getGson().toJson(giftShowInfo),
                    FixRoomUtil.getService());
            return Unit.INSTANCE;
        });
    }

}
