package com.huiwan.fixroom.unity;

import android.text.TextUtils;

import com.huiwan.base.util.ToastUtil;
import com.huiwan.constants.GameType;
import com.huiwan.fixroom.FixRoomStateManager;
import com.huiwan.ipc.model.IPCMsg;
import com.wepie.unity.ProcessCallBack;
import com.wepie.unity.main.IRoomLocal;
import com.wepie.wespy.module.fixroom.FixRoomConst;

import org.json.JSONException;
import org.json.JSONObject;

public class LocalFixRoomProcessor implements IRoomLocal {


    private void quickMatch(String data) {
        try {
            JSONObject jsonObject = new JSONObject(data);
            int gameType = jsonObject.getInt("game_type");
            if (gameType > 0) {
                FixRoomStateManager.getInstance().check2QuickMatch(gameType);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

    }

    @Override
    public void gotoFixRoomSetting(IPCMsg msg) {
        FixRoomStateManager.getInstance().gotoFixRoomSetting();
    }

    @Override
    public void sendFixRoomPacket(IPCMsg msg, ProcessCallBack callBack) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            int type = jsonObject.getInt("type");
            String data = jsonObject.getString("data");
            switch (type) {
                case FixRoomConst.UNITY_OP_MATCH:
                    quickMatch(data);
                    break;
                case FixRoomConst.UNITY_OP_SEARCH:
                    searchRoom(data);
                    break;
                case FixRoomConst.UNITY_OP_NEW_ROOM:
                    createRoom(data);
                    break;
                case FixRoomConst.UNITY_OP_EXIT_ROOM:
                    exitRoom(data);
                    break;
                case FixRoomConst.UNITY_OP_SIT_DOWN:
                    sitDown(data);
                    break;
                case FixRoomConst.UNITY_OP_READY:
                    ready(data);
                    break;
                case FixRoomConst.UNITY_OP_PICK_UP:
                    pickUp(data);
                    break;
                case FixRoomConst.UNITY_OP_STAND_UP:
                    unseat(data);
                    break;
                case FixRoomConst.UNITY_OP_KICK:
                    kick(data);
                    break;
                case FixRoomConst.UNITY_OP_UNREADY:
                    unready(data);
                    break;

            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void inviteFixRoom(IPCMsg msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            int rid = jsonObject.getInt("rid");
            FixRoomStateManager.getInstance().showInviteFixRoomActivity(rid);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void sendFixRoomChatMsg(IPCMsg msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            int rid = jsonObject.getInt("rid");
            int seat = jsonObject.getInt("seat");
            String content = jsonObject.getString("content");
            FixRoomStateManager.getInstance().sendChatMsg(rid, seat, content);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    //用于处理在原生点击准备时，先拉起unity载入，载入完毕收到该消息再发准备的包
    @Override
    public void onSceneReady(IPCMsg msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            int scene = jsonObject.getInt("scene");
            FixRoomStateManager.getInstance().invokeSceneReadyCallback(scene);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showFixRoomUserDialog(IPCMsg msg) {
        int uid = 0;
        String subSource = "";
        int gameType = GameType.GAME_TYPE_UN_DEFINED;
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            uid = jsonObject.getInt("uid");
            subSource = jsonObject.getString("sub_source");
            gameType = jsonObject.optInt("game_type", gameType);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        if (uid > 0) FixRoomStateManager.getInstance().showUserDialogActivity(uid, gameType, subSource);
    }

    @Override
    public void reportFixRoomChatMsg(IPCMsg msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            int uid = jsonObject.getInt("send_uid");
            int rid = jsonObject.getInt("rid");
            String mid = jsonObject.getString("mid");
            FixRoomStateManager.getInstance().reportFixRoomChatMsg(rid, uid, mid);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void sendFixRoomSpeechGift(IPCMsg msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            int sendUid = jsonObject.getInt("send_uid");
            int receiveUid = jsonObject.getInt("recv_uid");
            int gift = jsonObject.getInt("gift");
            int rid = jsonObject.getInt("rid");
            FixRoomStateManager.getInstance().sendFixRoomSpeechGift(sendUid, receiveUid, gift, rid);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void changeFixRoomLockState(IPCMsg msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            boolean lock = jsonObject.getBoolean("lock");
            int gameType = jsonObject.getInt("game_type");
            int rid = jsonObject.getInt("rid");
            FixRoomStateManager.getInstance().changeFixRoomLockState(rid, gameType, lock);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showFixRoomMagicFace(IPCMsg msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            long delay = jsonObject.getLong("delay");
            int rid = jsonObject.getInt("rid");
            FixRoomStateManager.getInstance().showMagicFaceActivity(rid, delay);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void syncFixRoomInfo(IPCMsg msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            int rid = jsonObject.getInt("rid");
            FixRoomStateManager.getInstance().syncFixRoomInfo(rid);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void requestBuyGoods(IPCMsg msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            int goodsId = jsonObject.getInt("goods_id");
            String goodsInfo = jsonObject.getString("goods_info");
            FixRoomStateManager.getInstance().requestBuyGoods(goodsId, goodsInfo);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void setFixRoomActivityVisible() {
        FixRoomStateManager.getInstance().setFixRoomActivityVisible();
    }

    private void sitDown(String data) {
        try {
            JSONObject jsonObject = new JSONObject(data);
            int rid = jsonObject.getInt("rid");
            int seatNum = jsonObject.getInt("seat_num");
            int gameType = jsonObject.getInt("game_type");
            if (rid > 0) {
                FixRoomStateManager.getInstance().check2SitDown(rid, seatNum, gameType);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void unready(String data) {
        try {
            JSONObject jsonObject = new JSONObject(data);
            int rid = jsonObject.getInt("rid");
            if (rid > 0) {
                FixRoomStateManager.getInstance().unready(rid);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void ready(String data) {
        try {
            JSONObject jsonObject = new JSONObject(data);
            int rid = jsonObject.getInt("rid");
            if (rid > 0) {
                FixRoomStateManager.getInstance().ready(rid);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    //抱起
    private void pickUp(String data) {
        try {
            JSONObject jsonObject = new JSONObject(data);
            int rid = jsonObject.getInt("rid");
            int uid = jsonObject.getInt("uid");
            int seatNum = jsonObject.getInt("seat_num");
            if (rid > 0 && uid > 0) {
                FixRoomStateManager.getInstance().pickUp(rid, uid, seatNum);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void unseat(String data) {
        try {
            JSONObject jsonObject = new JSONObject(data);
            int rid = jsonObject.getInt("rid");
            if (rid > 0) {
                FixRoomStateManager.getInstance().unseat(rid);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void kick(String data) {
        try {
            JSONObject jsonObject = new JSONObject(data);
            int rid = jsonObject.getInt("rid");
            int uid = jsonObject.getInt("uid");
            if (rid > 0 && uid > 0) {
                FixRoomStateManager.getInstance().kick(rid, uid);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    private void searchRoom(String data) {
        try {
            JSONObject jsonObject = new JSONObject(data);
            String ridStr = jsonObject.getString("rid");
            int scene = jsonObject.getInt("scene");
            boolean force = jsonObject.getBoolean("force");
            String pwd = jsonObject.getString("pwd");
            if (!TextUtils.isEmpty(ridStr)) {
                FixRoomStateManager.getInstance().searchRoom(ridStr, scene, force, pwd);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            ToastUtil.debugShow(e.getLocalizedMessage());
        }
    }

    private void createRoom(String data) {
        try {
            JSONObject jsonObject = new JSONObject(data);
            int gameType = jsonObject.getInt("game_type");
            if (gameType > 0) {
                FixRoomStateManager.getInstance().createRoom(gameType);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void exitRoom(String data) {
        try {
            JSONObject jsonObject = new JSONObject(data);
            int rid = jsonObject.getInt("rid");
            if (rid > 0) {
                FixRoomStateManager.getInstance().exitRoom(rid);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

}
