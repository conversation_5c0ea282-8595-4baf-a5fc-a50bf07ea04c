package com.huiwan.fixroom.unity;

import android.app.Activity;

import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.constants.GameType;
import com.huiwan.fixroom.FixRoomStateManager;
import com.huiwan.fixroom.FixRoomUtil;
import com.huiwan.ipc.model.IPCMsg;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserPopWindow;
import com.three.http.callback.DataCallback;
import com.three.http.callback.Result;
import com.wepie.unity.ProcessCallBack;
import com.wepie.unity.UnityUtil;
import com.wepie.unity.unity.IRoomRemote;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;

public class RemoteFixRoomProcessor implements IRoomRemote {
    private WeakReference<Activity> contextRef;

    @Override
    public void setContextRef(Activity ref) {
        contextRef = new WeakReference<>(ref);
    }

    @Override
    public void showChatMsgSendView(IPCMsg msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            int rid = jsonObject.getInt("rid");
            int seat = jsonObject.getInt("seat_num");
            FixRoomStateManager.getInstance().showChatSendView(rid, seat);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showBarrage(IPCMsg msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            String content = jsonObject.getString("content");
            int type = jsonObject.getInt("type");
            int uid = jsonObject.getInt("uid");
            int gameType = jsonObject.getInt("game_type");
            FixRoomStateManager.getInstance().showBarrage(content, type, uid, gameType);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void showGiftAnim(IPCMsg msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            String gift = jsonObject.getString("gift");
            GiftShowInfo giftShowInfo = UnityUtil.getGson().fromJson(gift, GiftShowInfo.class);
            FixRoomStateManager.getInstance().showGiftAnim(giftShowInfo);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void gotoContributeWord(IPCMsg msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            boolean blank = jsonObject.getBoolean("is_blank");
            if (contextRef != null && contextRef.get() != null) {
                FixRoomStateManager.getInstance().gotoContributeWord(contextRef.get(), blank);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onSpyWordFeedback(IPCMsg msg) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            String spy = jsonObject.getString("spy_word");
            String citizen = jsonObject.getString("citizen_word");
            boolean good = jsonObject.getBoolean("good");
            FixRoomStateManager.getInstance().onSpyWordFeedback(spy, citizen, good);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void getGameLevel(IPCMsg msg, ProcessCallBack callBack) {
        try {
            JSONObject jsonObject = new JSONObject(msg.getReqData());
            int uid = jsonObject.getInt("uid");
            int gameType = jsonObject.getInt("game_type");
            if (FixRoomUtil.isAvatarWolf(gameType)) {
                gameType = GameType.GAME_TYPE_WEREWOLF_SIMPLE;
            }
            UserService.get().getPopWindowData(uid, gameType, new DataCallback<UserPopWindow>() {
                @Override
                public void onSuccess(Result<UserPopWindow> result) {
                    int level = result.data.gameInfo.level;
                    JSONObject json = new JSONObject();
                    try {
                        json.put("level", level);
                        msg.setRspData(IPCMsg.CODE_SUCCESS, json.toString());
                        callBack.onFinish(msg);
                    } catch (JSONException e) {
                        msg.setRspData(IPCMsg.CODE_NORMAL_ERROR, "");
                        callBack.onFinish(msg);
                        e.printStackTrace();
                    }

                }

                @Override
                public void onFail(int code, String err) {
                    msg.setRspData(IPCMsg.CODE_NORMAL_ERROR, err);
                    callBack.onFinish(msg);
                }
            });
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
