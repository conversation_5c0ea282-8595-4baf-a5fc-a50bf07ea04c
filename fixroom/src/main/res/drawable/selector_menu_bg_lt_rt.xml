<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:drawable="@android:color/transparent" android:state_pressed="false" />
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#ECEDEF" />
            <corners android:topLeftRadius="8dp" android:topRightRadius="8dp" />
        </shape>
    </item>
</selector>