package com.huiwan.sdk.jarloader;

import android.content.Context;

import com.huiwan.base.util.ToastUtil;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.WebApi;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

// test http://wespynextpic.afunapp.com/o_1esfbg9pn1tio1n861htq1lmq89p9.jar
public class IPluginImpl implements IPlugin {

    @Override
    public void init(Context context) {

    }

    @Override
    public void doTask(ICallback callback) {
        ToastUtil.show("Hello, I'm a toast from plugin");
        WebApi beforeApi = ApiService.of(WebApi.class);
        WebApi cur = (WebApi) Proxy.newProxyInstance(IPluginImpl.class.getClassLoader(), new Class[]{WebApi.class}, new InvocationHandler() {
            @Override
            public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                ToastUtil.show("plugin invoke " + method);
                return method.invoke(beforeApi, args);
            }
        });
        ApiService.register(WebApi.class, cur);
        callback.onSuccess();
    }
}
