import java.util.zip.ZipEntry
import java.util.zip.ZipFile

plugins {
    id 'com.android.library'
}


android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        buildToolsVersion rootProject.ext.android.buildToolsVersion
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation project(":sdk:jar-loader")
    compileOnly project(":sdk:baseutil")
    compileOnly project(":lib:api")
}

task jar {
    dependsOn(assemble)
    doLast {
        def aarPath = "${buildDir.absolutePath}/outputs/aar/jar-loader-impl-release.aar"
        def jarPath = "${buildDir.absolutePath}/outputs/jar/plugin-impl.jar"
        def dexJarPath = "${buildDir.absolutePath}/outputs/jar/plugin-dex-impl.jar"
        ZipFile zipFile = new ZipFile(aarPath)
        ZipEntry jarEntry = zipFile.getEntry("classes.jar")
        def buffer = zipFile.getInputStream(jarEntry).bytes
        File jarFile = new File(jarPath)
        jarFile.getParentFile().mkdirs()

        OutputStream os = jarFile.newDataOutputStream()
        os.write(buffer, 0, buffer.length)
        os.close()

        Properties properties = new Properties()
        properties.load(rootProject.file("local.properties").newInputStream())
        def sdkPath = properties.getProperty("sdk.dir")
        def relativeDxPath = "/build-tools/${rootProject.ext.android.buildToolsVersion}/dx"
        def cmd = sdkPath + relativeDxPath
        println(cmd)
        exec {
            commandLine([cmd, "--dex", "--output=$dexJarPath", jarPath])
        }
    }
}