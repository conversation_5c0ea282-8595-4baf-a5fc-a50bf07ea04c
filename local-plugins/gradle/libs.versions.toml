[versions]
androidGradlePlugin = "8.7.2"
kotlin = "2.0.20"
okhttp = "4.12.0"
ktor = "2.0.0"
moshi = "1.14.0"
[libraries]
android-gradlePlugin-api = { group = "com.android.tools.build", name = "gradle-api", version.ref = "androidGradlePlugin" }
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
ktor-core = { module = "io.ktor:ktor-client-core", version.ref = "ktor" }
ktor-cio = { module = "io.ktor:ktor-client-cio", version.ref = "ktor" }
moshi = { module = "com.squareup.moshi:moshi-kotlin", version.ref = "moshi" }
[plugins]
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
