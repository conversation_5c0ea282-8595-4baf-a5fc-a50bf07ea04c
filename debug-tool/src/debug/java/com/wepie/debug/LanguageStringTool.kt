package com.wepie.debug

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Button
import androidx.compose.material3.Checkbox
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.google.gson.JsonObject
import com.huiwan.base.LibBaseUtil
import com.huiwan.base.ktx.isNotEmpty
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.WPUIText
import com.huiwan.base.util.JsonUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.baseservice.HwServiceManager
import com.huiwan.baseservice.IHwService
import com.huiwan.store.PrefUtil
import com.wepie.liblog.main.HLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.commons.text.StringEscapeUtils
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL
import java.util.concurrent.ConcurrentHashMap

object LanguageStringTool {

    private const val KEY_TRANSLATE_VERSION = "KEY_TRANSLATE_VERSION"
    private const val KEY_TRANSLATE_ID = "KEY_TRANSLATE_ID"
    private const val KEY_ADVANCE_MODE = "KEY_ADVANCE_MODE"
    private const val KEY_LANGUAGE_TRANSLATE = "KEY_LANGUAGE_TRANSLATE"

    private const val HOST = "https://translate.weplayapp.com/api/public/export_trans_multi"

    private var packageName: String = ""

    private val languageStringCache = ConcurrentHashMap<Int, String>()
    private val languageStringArrayCache = ConcurrentHashMap<Int, Array<String>>()

    fun init(context: Context) {
        packageName = context.packageName
        val versionName = getVersion(context)
        // 检查版本号，如果版本不一致则清除翻译ID
        checkVersionAndClearIfNeeded(versionName)

        ResUtil.addResourceFilter { resources ->
            LanguageStringResources(resources, languageStringCache, languageStringArrayCache)
        }
        WPUIText.textFilter = {
            languageStringCache[it] ?: ""
        }
        HwServiceManager.getServiceManager().registerService(object : IHwService {
            override fun languageChangeClear() {
                super.languageChangeClear()
                loadFromFile()
            }
        })
        loadFromFile()
    }

    private fun getVersion(context: Context): String {
        try {
            val packageManager = context.packageManager
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            return packageInfo.versionName ?: ""
        } catch (e: Exception) {
            HLog.e("LanguageStringTool", "Failed to get version: ${e.message}")
            return ""
        }
    }

    private fun checkVersionAndClearIfNeeded(currentVersion: String) {
        val pref = PrefUtil.getInstance()
        val savedVersion = pref.getString(KEY_TRANSLATE_VERSION, "")

        // 如果版本号不一致，清除翻译ID
        if (currentVersion != savedVersion) {
            pref.clear(KEY_TRANSLATE_ID)
            pref.setString(KEY_LANGUAGE_TRANSLATE, "{}")
            pref.setString(KEY_TRANSLATE_VERSION, currentVersion)
        }
    }

    fun genView(context: Context, activity: AppCompatActivity): View {
        val util = PrefUtil.getInstance()
        val defaultTranslationId = util.getString(KEY_TRANSLATE_ID, BuildConfig.TRANSLATION_ID)
        val defaultAdvanceMode = util.getBoolean(KEY_ADVANCE_MODE, true)
        val composeView = ComposeView(context)
        composeView.setContent {
            var translationId by remember { mutableStateOf(defaultTranslationId) }
            var advanceMode by remember { mutableStateOf(defaultAdvanceMode) }
            LanguageStringToolContent(
                translationId,
                advanceMode = advanceMode,
                onValueChange = {
                    translationId = it
                },
                onAction = {
                    when (it) {
                        is LanguageIntent.LoadString -> {
                            util.setString(KEY_TRANSLATE_ID, it.id)
                            loadLanguageAndRecreate(it.id, activity, advanceMode)
                        }

                        is LanguageIntent.Reset -> {
                            clear()
                            recreate(activity, advanceMode)
                        }

                        is LanguageIntent.RecreateActivity -> {
                            recreate(activity, advanceMode)
                        }
                    }
                },
                onCheckedChange = {
                    advanceMode = it
                    util.setBoolean(KEY_ADVANCE_MODE, advanceMode)
                }
            )
        }
        return composeView
    }

    private fun loadLanguageAndRecreate(id: String, activity: Activity, advanceMode: Boolean) {
        CoroutineScope(Dispatchers.IO).launch {
            if (loadFromServer(id)) {
                ToastUtil.debugShow("加载成功,正重启当前页面")
                withContext(Dispatchers.Main) {
                    recreate(activity, advanceMode)
                }
            }
        }
    }

    suspend fun loadFromServer(id: String): Boolean {
        val rsp: String?
        try {
            rsp = post(id) ?: return false
        } catch (e: Exception) {
            return false
        }
        PrefUtil.getInstance().setString(KEY_LANGUAGE_TRANSLATE, rsp)
        loadStrings(rsp)
        return true
    }

    fun loadFromFile(autoRefresh: Boolean = true) {
        val pref = PrefUtil.getInstance()
        val s = pref.getString(KEY_LANGUAGE_TRANSLATE, "")
        loadStrings(s)
        if (autoRefresh && s.isNotEmpty()) {
            CoroutineScope(Dispatchers.IO).launch {
                val id = pref.getString(KEY_TRANSLATE_ID, BuildConfig.TRANSLATION_ID)
                loadFromServer(id)
            }
        }
    }

    fun clear() {
        languageStringCache.clear()
        languageStringArrayCache.clear()
        PrefUtil.getInstance().clear(KEY_LANGUAGE_TRANSLATE)
    }

    private fun recreate(activity: Activity, advanceMode: Boolean) {
        if (advanceMode) {
            activity.overridePendingTransition(0, 0)
            activity.recreate()
        } else {
            val intent = Intent(activity.intent)
            activity.finishAfterTransition()
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            LibBaseUtil.getApplication().startActivity(intent)
        }
    }

    private suspend fun post(id: String): String? {
        val url = URL(HOST + "?file_id=${id}&file_type=5&operator_email=panrunqiu%40wepie.com")
        val connection = (url.openConnection() as? HttpURLConnection) ?: return null
        connection.setRequestMethod("POST")
        connection.setConnectTimeout(10000) // 连接超时
        connection.setReadTimeout(15000) // 读取超时
        connection.setDoOutput(true) // 允许输出
        connection.setDoInput(true) // 允许输入
//        connection.setRequestProperty("Content-Type", "application/json") // 设置请求头

        val response = StringBuilder()
        val responseCode: Int = connection.getResponseCode()
        if (responseCode != HttpURLConnection.HTTP_OK) {
            // 处理错误码
            HLog.e("NetworkError", "Response Code: $responseCode")
            return null
        }
        connection.getInputStream().use { inputStream ->
            val reader = BufferedReader(InputStreamReader(inputStream))
            var line: String?
            while ((reader.readLine().also { line = it }) != null) {
                response.append(line)
            }
        }
        return response.toString()
    }

    private fun getLanguageMap(obj: JsonObject, langName: String): Map<String, String> {
        val languageObj = obj.getAsJsonObject(langName) ?: return emptyMap()
        val map = mutableMapOf<String, String>()
        languageObj.asMap().forEach { (k, v) ->
            map.put(k, v.asString)
        }
        return map
    }

    private fun replaceFormat(old: String, hans: String): String {
        var s = old
        var i = 1
        var startIndex = hans.indexOf("{#")
        do {
            val endIndex = hans.indexOf("#}", startIndex)
            if (endIndex < 0) {
                return old
            }
            val format = hans.substring(startIndex, endIndex + 2)
            val newFormat = "%$i\$s"
            s = s.replace(format, newFormat)
            i++
            startIndex = hans.indexOf("{#", endIndex)
        } while (startIndex >= 0)
        return s
    }

    private fun loadStrings(rsp: String?) {
        if (rsp.isNullOrEmpty()) {
            return
        }
        val obj = JsonUtil.toObject(rsp)
        val langMap = getLanguageMap(obj, LibBaseUtil.getLang().name)
        val hansMap = getLanguageMap(obj, "zh-Hans")
        val map = mutableMapOf<Int, String>()
        val res = ResUtil.getResource()

        val stringModifyList = listOf(
            StringFormatModify(hansMap),
            UnescapeModify()
        )

        langMap.entries.forEach { (k, v) ->
            val id = res.getIdentifier(k, "string", packageName)
            if (id == 0) {
                return@forEach
            }
            var newS = v
            for (modify in stringModifyList) {
                newS = modify.modify(k, newS)
            }
            map[id] = newS
        }
        languageStringCache.putAll(map)
    }

    interface IStringModify {
        fun modify(key: String, value: String): String
    }

    class StringFormatModify(private val hansMap: Map<String, String>) : IStringModify {
        override fun modify(key: String, value: String): String {
            if (value.contains("{#")) {
                val hans = hansMap[key] ?: return value
                return replaceFormat(value, hans)
            }
            return value
        }

    }

    class UnescapeModify : IStringModify {
        override fun modify(key: String, value: String): String {
            try {
                return StringEscapeUtils.unescapeJava(value)
            } catch (thr: Throwable) {
                thr.printStackTrace()
                return value.replace("\\n", "\n").replace("\\t", "\t")
            }
        }
    }
}

sealed interface LanguageIntent {
    class LoadString(val id: String) : LanguageIntent
    object Reset : LanguageIntent
    object RecreateActivity : LanguageIntent
}

/**
 * 语言字符串工具的Compose实现
 */
@SuppressLint("NoChineseForJava")
@Composable
fun LanguageStringToolContent(
    translationId: String,
    advanceMode: Boolean,
    onValueChange: (String) -> Unit = {},
    onAction: (LanguageIntent) -> Unit = {},
    onCheckedChange: (Boolean) -> Unit = {},
    modify: Modifier = Modifier
) {
    Column(
        modifier = modify
            .padding(end = 16.dp)
            .fillMaxWidth()
    ) {
        // 翻译ID输入框
        LanguageIdInput(
            translationId = translationId,
            onValueChange = onValueChange,
        )
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
        ) {
            Checkbox(
                checked = advanceMode,
                onCheckedChange = onCheckedChange,
            )
            Text("点击下方按钮出现白屏或闪退去掉勾选")
        }
        // 操作按钮组
        LanguageActionButtons(
            translationId = translationId,
            onAction = onAction,
        )
    }
}

/**
 * 翻译ID输入组件
 */
@SuppressLint("NoChineseForJava")
@Composable
fun LanguageIdInput(
    translationId: String,
    onValueChange: (String) -> Unit,
) {
    OutlinedTextField(
        value = translationId,
        onValueChange = onValueChange,
        label = { Text("翻译ID") },
        placeholder = { Text("输入翻译ID") },
        keyboardOptions = KeyboardOptions(
            keyboardType = KeyboardType.Number
        ),
        singleLine = true,
        modifier = Modifier.fillMaxWidth()
    )
}

/**
 * 操作按钮组组件
 */
@SuppressLint("NoChineseForJava")
@Composable
fun LanguageActionButtons(
    translationId: String,
    onAction: (LanguageIntent) -> Unit = {},
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 加载翻译按钮
        Button(
            contentPadding = PaddingValues.Absolute(8.dp, 0.dp, 8.dp, 0.dp),
            onClick = {
                if (translationId.isNotEmpty()) {
                    onAction.invoke(LanguageIntent.LoadString(translationId))
                }
            },
            enabled = translationId.isNotEmpty(),
        ) {
            Text("加载翻译")
        }

        // 恢复默认按钮
        OutlinedButton(
            contentPadding = PaddingValues.Absolute(8.dp, 0.dp, 8.dp, 0.dp),
            onClick = { onAction.invoke(LanguageIntent.Reset) },
        ) {
            Text("恢复默认")
        }

        // 重建页面按钮
        OutlinedButton(
            contentPadding = PaddingValues.Absolute(8.dp, 0.dp, 8.dp, 0.dp),
            onClick = { onAction.invoke(LanguageIntent.RecreateActivity) },
        ) {
            Text("重建页面")
        }
    }
}

@Preview
@Composable
fun LanguageStringToolContentTest() {
    LanguageStringToolContent(
        translationId = "111",
        advanceMode = false,
        modify = Modifier.background(Color.White)
    )
}