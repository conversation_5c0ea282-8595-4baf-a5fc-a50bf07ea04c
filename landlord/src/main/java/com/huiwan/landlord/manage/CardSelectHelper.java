package com.huiwan.landlord.manage;

import com.huiwan.landlord.model.Card;
import com.huiwan.landlord.view.CardView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class CardSelectHelper {
    public static List<CardView> handleSmartSelectCard(List<CardView> selectedCardViews, List<CardView> upCardViews,
                                                       List<CardView> myHandCardViewList, List<CardView> cardViewsToPut,
                                                       List<Card> selectedCards, List<List<Card>> promptCardList) {
        // 先手出牌时
        if (promptCardList.size() == 0) {
            if (selectedCardViews.size() > 5) {
                // 如果选择的已经是可以出的牌型，则不要干预
                int cardType = CardTypeManager.getCardType(selectedCards);
                if (cardType != CardTypeManager.CARD_TYPE_UNDEFINED) {
                    return upCardViews;
                }

                CardTypeManager.CardSelectResult feijiResult = CardTypeManager.findFeiji(selectedCards);
                CardTypeManager.CardSelectResult lianduiResult = CardTypeManager.findLiandui(selectedCards);
                CardTypeManager.CardSelectResult shunziResult = CardTypeManager.findShunzi(selectedCards);
                List<Card> targetCards = null;

                int feijiRange = feijiResult.range;
                int  lianduiRange = lianduiResult.range;
                int shunziRange = shunziResult.range;
                if (feijiRange != 0 || lianduiRange != 0 || shunziRange != 0) {
                    if (feijiRange >= lianduiRange && feijiRange >= shunziRange) {
                        targetCards = feijiResult.selectCards;
                    } else {
                        if (lianduiRange > feijiRange && lianduiRange >= shunziRange) {
                            targetCards = lianduiResult.selectCards;
                        } else {
                            targetCards = shunziResult.selectCards;
                        }
                    }
                }
                if (targetCards != null) {
                    upCardViews.clear();
                    for (CardView cardView : selectedCardViews) {
                        for (Card card : targetCards) {
                            if (card.value == cardView.getValue()) {
                                upCardViews.add(cardView);
                            }
                        }
                    }
                }
            }
        } else {
            List<List<CardView>> tempPromptCardViewList = new ArrayList<>();
            for (int i = 0; i < promptCardList.size(); i++) {
                tempPromptCardViewList.add(pollPromptCardsOnce(promptCardList, myHandCardViewList));
            }

            if (cardViewsToPut.size() == 1 && selectedCardViews.size() == 1) {
                List<CardView> tempCardViewToPut = new ArrayList<>(cardViewsToPut);
                tempCardViewToPut.addAll(selectedCardViews);
                List<Card> tempCardToPut = CardTypeManager.parseFromBytes(getCardsValueFromView(tempCardViewToPut));
                for (List<CardView> promptCardView : tempPromptCardViewList) {
                    List<Card> promptCard = CardTypeManager.parseFromBytes(getCardsValueFromView(promptCardView));
                    if (isCardContainedWithGrade(tempCardToPut, promptCard) && promptCard.size() > 2) {
                        upCardViews.add(cardViewsToPut.get(0));
                        addPromptCardView(upCardViews, promptCardView, cardViewsToPut, myHandCardViewList);
                        break;
                    }
                }
            } else {
                if (cardViewsToPut.size() > 0) return upCardViews;
                if (isPromptCardDuizi(promptCardList)) {
                    if (selectedCardViews.size() == 1) {
                        for (List<Card> promptCard : promptCardList) {
                            if (promptCard.get(0).grade == selectedCards.get(0).grade) {
                                for (CardView cardView : myHandCardViewList) {
                                    List<CardView> tempCardView = new ArrayList<>();
                                    tempCardView.add(cardView);
                                    List<Card> card = CardTypeManager.parseFromBytes(getCardsValueFromView(tempCardView));
                                    if (card.get(0).grade == selectedCards.get(0).grade &&
                                            card.get(0).value != selectedCards.get(0).value) {
                                        upCardViews.add(cardView);
                                        break;
                                    }
                                }
                                break;
                            }
                        }
                    }
                } else {
                    // 先从手动选择的牌中找可以出的牌
                    boolean findPromptInSelected = false;
                    for (List<CardView> promptCardView : tempPromptCardViewList) {
                        List<Card> promptCard = CardTypeManager.parseFromBytes(getCardsValueFromView(promptCardView));
                        if (isCardContainedWithValue(selectedCards, promptCard)) {
                            // 只找大于等于4张的提示
                            if (promptCardView.size() >= 4) {
                                upCardViews = promptCardView;
                            }
                            findPromptInSelected = true;
                            break;
                        }
                    }
                    // 没找到的话，判断手动选择的牌是否是可以出的牌的一部分
                    if (!findPromptInSelected && selectedCardViews.size() > 1) {
                        for (List<CardView> promptCardView : tempPromptCardViewList) {
                            List<Card> promptCard = CardTypeManager.parseFromBytes(getCardsValueFromView(promptCardView));
                            if (isCardContainedWithGrade(selectedCards, promptCard)) {
                                addPromptCardView(upCardViews, promptCardView, cardViewsToPut, myHandCardViewList);
                                break;
                            }
                        }
                    }
                }
            }
        }
        return upCardViews;
    }

    private static void addPromptCardView(List<CardView> upCardViews, List<CardView> promptCardView,
                                          List<CardView> cardViewsToPut, List<CardView> myHandCardViewList) {
        List<Card> upCardList = CardTypeManager.parseFromBytes(getCardsValueFromView(upCardViews));
        List<Card> promptCardList = CardTypeManager.parseFromBytes(getCardsValueFromView(promptCardView));
        HashMap<Integer, Integer> promptCardsGradeMap = new HashMap<>();
        HashMap<Integer, Integer> upCardsGradeMap = new HashMap<>();

        for (Card moreCard: promptCardList) {
            int i = 0;
            if (promptCardsGradeMap.containsKey(moreCard.grade)) {
                Integer integer = promptCardsGradeMap.get(moreCard.grade);
                if (integer != null) i = integer;
            }
            promptCardsGradeMap.put(moreCard.grade, i+1);
        }
        for (Card lessCard: upCardList) {
            int i = 0;
            if (upCardsGradeMap.containsKey(lessCard.grade)) {
                Integer integer = upCardsGradeMap.get(lessCard.grade);
                if (integer != null) i = integer;
            }
            upCardsGradeMap.put(lessCard.grade, i+1);
        }

        for (CardView cardView : promptCardView) {
            byte cardViewsToPutValue = 0;
            if (cardViewsToPut.size() > 0) cardViewsToPutValue = cardViewsToPut.get(0).getValue();
            Card card = new Card(cardView.getValue());
            List<Card> cardList = new ArrayList<>();
            cardList.add(card);

            if (!isCardContainedWithGrade(cardList, upCardList)) {
                if (cardView.getValue() != cardViewsToPutValue) {
                    upCardViews.add(cardView);
                }
            } else {
                int upCardGradeNum = 0;
                if (upCardsGradeMap.containsKey(card.grade)) {
                    Integer integer = upCardsGradeMap.get(card.grade);
                    if (integer != null) upCardGradeNum = integer;
                }
                int promptCardGradeNum = 0;
                Integer integer = promptCardsGradeMap.get(card.grade);
                if (integer != null) promptCardGradeNum = integer;

                if (upCardGradeNum < promptCardGradeNum) {
                    if (upCardViews.contains(cardView)) {
                        CardView anotherGradeEqualCardView = findCardViewWithSameGradeButNotInUpViews(upCardViews, card.grade, myHandCardViewList);
                        if (anotherGradeEqualCardView != null) upCardViews.add(anotherGradeEqualCardView);
                    } else {
                        upCardViews.add(cardView);
                    }
                    upCardsGradeMap.put(card.grade, upCardGradeNum+1);
                }
            }
        }
    }

    private static CardView findCardViewWithSameGradeButNotInUpViews(List<CardView> cardViews, int grade,
                                                                     List<CardView> myHandCardViewList) {
        for (CardView cardView : myHandCardViewList) {
            Card card = new Card(cardView.getValue());
            if (card.grade == grade) {
                boolean isContained = false;
                for (CardView upCardView : cardViews) {
                    if (upCardView.getValue() == cardView.getValue()) {
                        isContained = true;
                        break;
                    }
                }
                if (!isContained) {
                    return cardView;
                }
            }
        }
        return null;
    }

    private static List<CardView> pollPromptCardsOnce(List<List<Card>> promptCardList, List<CardView> myHandCardViewList) {
        List<CardView> promptCardView = new ArrayList<>();
        List<Card> promptCards = promptCardList.remove(0);
        promptCardList.add(promptCards);

        for (Card card : promptCards) {
            for (CardView cardView : myHandCardViewList) {
                if (cardView.getValue() == card.value) {
                    promptCardView.add(cardView);
                    break;
                }
            }
        }
        return promptCardView;
    }

    private static boolean isPromptCardDuizi(List<List<Card>> promptCardList) {
        if (promptCardList.size() == 0) return false;
        int i = 0;
        for (List<Card> promptCards : promptCardList) {
            if (promptCards.size() == 2) {
                i++;
            }
        }
        return i >= 2 || i == promptCardList.size();
    }

    private static boolean isCardContainedWithValue(List<Card> moreCards, List<Card> lessCards) {
        for (Card promptCard : lessCards) {
            boolean isContained = false;
            for (Card selectedCard : moreCards) {
                if (promptCard.value == selectedCard.value) {
                    isContained = true;
                    break;
                }
            }
            if (!isContained) return false;
        }
        return true;
    }

    private static boolean isCardContainedWithGrade(List<Card> lessCards, List<Card> moreCards) {
        HashMap<Integer, Integer> moreCardsGradeMap = new HashMap<>();
        HashMap<Integer, Integer> lessCardsGradeMap = new HashMap<>();
        for (Card moreCard: moreCards) {
            int i = 0;
            if (moreCardsGradeMap.containsKey(moreCard.grade)) {
                Integer integer = moreCardsGradeMap.get(moreCard.grade);
                if (integer != null) i = integer;
            }
            moreCardsGradeMap.put(moreCard.grade, i+1);
        }
        for (Card lessCard: lessCards) {
            int i = 0;
            if (lessCardsGradeMap.containsKey(lessCard.grade)) {
                Integer integer = lessCardsGradeMap.get(lessCard.grade);
                if (integer != null) i = integer;
            }
            lessCardsGradeMap.put(lessCard.grade, i+1);
        }

        for (Card lessCard : lessCards) {
            boolean isContained = false;
            for (Card moreCard : moreCards) {
                if (lessCard.grade == moreCard.grade) {
                    int lessGradeNum = 0;
                    Integer lessInteger = lessCardsGradeMap.get(lessCard.grade);
                    if (lessInteger != null) lessGradeNum = lessInteger;

                    int moreGradeNum = 0;
                    Integer moreInteger = moreCardsGradeMap.get(moreCard.grade);
                    if (moreInteger != null) moreGradeNum = moreInteger;
                    if (lessGradeNum > moreGradeNum) {
                        return false;
                    }

                    isContained = true;
                    break;
                }
            }
            if (!isContained) return false;
        }
        return true;
    }

    private static byte[] getCardsValueFromView(List<CardView> viewList) {
        byte[] cards = new byte[viewList.size()];
        for (int i = 0; i < viewList.size(); i++) {
            cards[i] = viewList.get(i).getValue();
        }
        return cards;
    }
}
