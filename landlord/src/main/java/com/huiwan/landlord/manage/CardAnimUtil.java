package com.huiwan.landlord.manage;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.view.View;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.huiwan.landlord.i.ILandlordGameView;
import com.huiwan.landlord.model.Card;
import com.huiwan.landlord.view.CardView;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class CardAnimUtil {
    private static boolean isAnimingCardUpDown = false;

    public static void showPutCardAnim(byte [] cards, boolean isSelfHosted,
                                       List<CardView> myHandCardViewList, final List<CardView> cardViewsToPut,
                                       ILandlordGameView view, final List<CardView> myAlreadyPutCardViewList) {
        if (cards.length == 0) return;

        final List<CardView> prepareToPutCardViews = new ArrayList<>();
        final List<CardView> animatingCardViewList = new ArrayList<>();
        if (isSelfHosted) {
            for (byte value : cards) {
                for (CardView cardView : myHandCardViewList) {
                    if (cardView.getValue() == value) {
                        prepareToPutCardViews.add(cardView);
                        break;
                    }
                }
            }
        } else {
            prepareToPutCardViews.addAll(cardViewsToPut);
        }
        List<Card> cardList = CardTypeManager.parseFromBytes(cards);
        cardList = CardTypeManager.getSortedPutCards(cardList);
        final int cardSize = cardList.size();

        for (int i = 0; i < cardSize; i++) {
            for (CardView cardView : prepareToPutCardViews) {
                if (cardList.get(i).value == cardView.getValue()) {
                    RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) cardView.getLayoutParams();
                    view.removeHandCardView(cardView);
                    view.addCardToPut(cardView, layoutParams);
                    cardView.setToPutXCord(CardResUtil.getCardsPutPosition(cardSize, i));
                    cardView.setIsAnimating(true);

                    animatingCardViewList.add(cardView);
                    break;
                }
            }
        }

        List<AnimatorSet> putCardAnimatorSetList = new ArrayList<>();
        for (int i = prepareToPutCardViews.size() - 1; i >= 0; i--) {
            CardView cardView = prepareToPutCardViews.get(i);
            cardView.setIsAnimating(true);
            ObjectAnimator xAnimator = ObjectAnimator.ofFloat(cardView, "translationX",
                    cardView.getToPutXCord() - cardView.getX() + cardView.getTranslationX());
            ObjectAnimator yAnimator = ObjectAnimator.ofFloat(cardView, "translationY",
                    view.getPutLayY() - cardView.getY() + cardView.getTranslationY());
            ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(cardView, "scaleX", 0.625f);
            ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(cardView, "scaleY", 0.625f);

            AnimatorSet animatorSet = new AnimatorSet();
            animatorSet.playTogether(xAnimator, yAnimator, scaleXAnimator, scaleYAnimator);
            animatorSet.setDuration(200);
            putCardAnimatorSetList.add(animatorSet);

            animatingCardViewList.add(cardView);
        }

        myHandCardViewList.removeAll(prepareToPutCardViews);

        byte[] myCards = new byte[myHandCardViewList.size()];
        for (int i = 0; i < myHandCardViewList.size(); i++) {
            myCards[i] = myHandCardViewList.get(i).getValue();
        }

        List<Card> myCardList = CardTypeManager.parseSorted(myCards);
        Collections.reverse(myCardList);
        for (CardView cardView : myHandCardViewList) {
            for (int i = 0; i < myCardList.size(); i++) {
                if (myCardList.get(i).value == cardView.getValue()) {
                    cardView.setHandCardCord(CardResUtil.getHandCardsPostion(myCardList.size(), i));
                    break;
                }
            }
        }

        List<AnimatorSet> moveCardAnimatorSetList = new ArrayList<>();
        for (int i = 0; i < myHandCardViewList.size(); i++) {
            CardView cardView = myHandCardViewList.get(i);
            ObjectAnimator xAnimator = ObjectAnimator.ofFloat(cardView, "translationX",
                    (cardView.getHandCardCord().x - cardView.getX()) + cardView.getTranslationX());
            ObjectAnimator yAnimator = ObjectAnimator.ofFloat(cardView, "translationY",
                    (cardView.getHandCardCord().y - cardView.getY()) + cardView.getTranslationY());

            AnimatorSet animatorSet = new AnimatorSet();
            animatorSet.playTogether(xAnimator, yAnimator);
            animatorSet.setDuration(100);
            moveCardAnimatorSetList.add(animatorSet);
        }

        AnimatorSet totalAnimatorSet = new AnimatorSet();
        for (AnimatorSet animatorSet : putCardAnimatorSetList) {
            totalAnimatorSet.playTogether(animatorSet);
        }
        for (AnimatorSet animatorSet : moveCardAnimatorSetList) {
            totalAnimatorSet.playTogether(animatorSet);
        }
        totalAnimatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                for (CardView cardView: myAlreadyPutCardViewList) {
                    cardView.setVisibility(View.GONE);
                }
                myAlreadyPutCardViewList.clear();

                myAlreadyPutCardViewList.addAll(prepareToPutCardViews);
                cardViewsToPut.clear();
                for (CardView cardView: animatingCardViewList) {
                    cardView.setIsAnimating(false);
                }
            }
        });
        totalAnimatorSet.start();
    }

    public static void animSelectedCards(List<CardView> moveCards, List<ObjectAnimator> animatorList, boolean isUp,
                                   boolean shouldMoveFromPreparePutList, int upDistance, boolean force, AnimEndCallback callback) {
        if (isAnimingCardUpDown) return;
        while (moveCards.size() > animatorList.size()) {
            ObjectAnimator objectAnimator = new ObjectAnimator();
            objectAnimator.setPropertyName("translationY");
            animatorList.add(objectAnimator);
        }

        List<ObjectAnimator> objectAnimators = new ArrayList<>(moveCards.size());
        List<CardView> needAnimatingCardViewList = new ArrayList<>();
        for (int i = 0; i < moveCards.size(); i++) {
            CardView cardView = moveCards.get(i);
            if (cardView.isAnimating()) continue;
            if (isUp) {
                if (!cardView.isUp() || force) {
                    cardView.upCard();
                } else {
                    continue;
                }
            } else {
                if (cardView.isUp() || force) {
                    cardView.downCard(shouldMoveFromPreparePutList);
                } else {
                    continue;
                }
            }
            cardView.setIsAnimating(true);
            ObjectAnimator objectAnimator = animatorList.get(i);
            objectAnimator.setTarget(cardView);
            objectAnimator.setFloatValues(isUp ? -upDistance + cardView.getTranslationY(): cardView.getTranslationY() + upDistance);

            objectAnimators.add(objectAnimator);
            needAnimatingCardViewList.add(cardView);
        }
        playAnimSet(objectAnimators, callback, needAnimatingCardViewList);
    }

    private static void playAnimSet(List<ObjectAnimator> animatorList, final AnimEndCallback callback, final List<CardView> needAnimatingCardViewList) {
        AnimatorSet animatorSet = new AnimatorSet();
        for (ObjectAnimator animator : animatorList) {
            animatorSet.playTogether(animator);
        }
        animatorSet.setInterpolator(new DecelerateInterpolator());
        animatorSet.setDuration(150);
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationStart(Animator animation) {
                isAnimingCardUpDown = true;
                super.onAnimationStart(animation);
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                isAnimingCardUpDown = false;
                for (CardView cardView : needAnimatingCardViewList) {
                    cardView.setIsAnimating(false);
                }
                if (callback != null) callback.onAnimEnd();
            }
        });
        animatorSet.start();
    }

    public static void showLandLordLogoAnim(final ImageView centerLandlordMark, ImageView landlordLightAnim,
                                            ImageView landlordLightBottom, float translationX, float translationY, final AnimEndCallback callback) {
        centerLandlordMark.setScaleX(0);
        centerLandlordMark.setScaleY(0);
        landlordLightAnim.setScaleX(0);
        landlordLightAnim.setScaleY(0);
        centerLandlordMark.setTranslationX(0);
        centerLandlordMark.setTranslationY(0);
        centerLandlordMark.setRotation(0);

        ObjectAnimator hatAlphaAnimator = ObjectAnimator.ofFloat(centerLandlordMark, "alpha", 1.0f).setDuration(1000);
        ObjectAnimator hatZoomInXAnimator = ObjectAnimator.ofFloat(centerLandlordMark, "scaleX", 1.0f).setDuration(1000);
        ObjectAnimator hatZoomInYAnimator = ObjectAnimator.ofFloat(centerLandlordMark, "scaleY", 1.0f).setDuration(1000);
        ObjectAnimator hatRotateAnimator = ObjectAnimator.ofFloat(centerLandlordMark, "rotation", -15, 15, -15, 0).setDuration(830);
        ObjectAnimator hatZoomOutXAnimator = ObjectAnimator.ofFloat(centerLandlordMark, "scaleX", 0.4f).setDuration(420);
        ObjectAnimator hatZoomOutYAnimator = ObjectAnimator.ofFloat(centerLandlordMark, "scaleY", 0.4f).setDuration(420);
        ObjectAnimator hatSkewAnimator = ObjectAnimator.ofFloat(centerLandlordMark, "rotation", -15).setDuration(420);
        AnimatorSet hatAnimatorSet1 = new AnimatorSet();
        hatAnimatorSet1.playTogether(hatAlphaAnimator, hatZoomInXAnimator, hatZoomInYAnimator);
        AnimatorSet hatAnimatorSet3 = new AnimatorSet();
        hatAnimatorSet3.playTogether(hatZoomOutXAnimator, hatZoomOutYAnimator, hatSkewAnimator);
        AnimatorSet hatTotalAnimator = new AnimatorSet();
        hatTotalAnimator.playSequentially(hatAnimatorSet1, hatRotateAnimator, hatAnimatorSet3);

        ObjectAnimator bottomLightFadeInAnimator = ObjectAnimator.ofFloat(landlordLightBottom, "alpha", 1.0f).setDuration(170);
        ObjectAnimator bottomLightFadeOutAnimator = ObjectAnimator.ofFloat(landlordLightBottom, "alpha", 0).setDuration(125);
        bottomLightFadeOutAnimator.setStartDelay(830);
        AnimatorSet bottomLightAnimator = new AnimatorSet();
        bottomLightAnimator.playTogether(bottomLightFadeInAnimator, bottomLightFadeOutAnimator);

        ObjectAnimator lightScaleXAnimator = ObjectAnimator.ofFloat(landlordLightAnim, "scaleX", 1.0f).setDuration(340);
        ObjectAnimator lightScaleYAnimator = ObjectAnimator.ofFloat(landlordLightAnim, "scaleY", 1.0f).setDuration(340);
        ObjectAnimator lightRotateAnimator = ObjectAnimator.ofFloat(landlordLightAnim, "rotation", 20).setDuration(670);
        ObjectAnimator lightAlphatAnimator = ObjectAnimator.ofFloat(landlordLightAnim, "alpha", 1, 1, 0).setDuration(670);
        AnimatorSet lightScaleAnimator = new AnimatorSet();
        lightScaleAnimator.playTogether(lightScaleXAnimator, lightScaleYAnimator);
        AnimatorSet lightAnimator1 = new AnimatorSet();
        lightAnimator1.playSequentially(lightScaleAnimator, lightRotateAnimator);
        AnimatorSet lightTotalAnimator = new AnimatorSet();
        lightTotalAnimator.playTogether(lightAnimator1, lightAlphatAnimator);

        AnimatorSet totalFadeInAnimator = new AnimatorSet();
        totalFadeInAnimator.playTogether(hatTotalAnimator, bottomLightAnimator, lightTotalAnimator);

        ObjectAnimator translateXAnimator = ObjectAnimator.ofFloat(centerLandlordMark, "translationX", translationX).setDuration(500);
        ObjectAnimator translateYAnimator = ObjectAnimator.ofFloat(centerLandlordMark, "translationY", translationY).setDuration(500);
        AnimatorSet translateAnimator = new AnimatorSet();
        translateAnimator.playTogether(translateXAnimator, translateYAnimator);

        AnimatorSet totalAnimator = new AnimatorSet();
        totalAnimator.playSequentially(totalFadeInAnimator, translateAnimator);
        totalAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                centerLandlordMark.setAlpha(0f);
                if (callback != null) callback.onAnimEnd();
            }
        });
        totalAnimator.start();
    }

    public interface AnimEndCallback {
        void onAnimEnd();
    }
}
