package com.huiwan.landlord.manage;

import android.graphics.Point;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.landlord.R;

public class CardResUtil {
    private static int[] colorResArray = {R.drawable.diamond_card, R.drawable.club_card, R.drawable.heart_card
            , R.drawable.spade_card, R.drawable.small_joker, R.drawable.big_joker};
    private static int[][] valueResArray = {{R.drawable.red_2, R.drawable.red_3, R.drawable.red_4, R.drawable.red_5,
            R.drawable.red_6, R.drawable.red_7, R.drawable.red_8, R.drawable.red_9, R.drawable.red_10,
            R.drawable.red_j, R.drawable.red_q, R.drawable.red_k, R.drawable.red_a},
            {R.drawable.black_2, R.drawable.black_3, R.drawable.black_4, R.drawable.black_5,
                    R.drawable.black_6, <PERSON>.drawable.black_7, R.drawable.black_8, <PERSON><PERSON>drawable.black_9,
                    <PERSON>.drawable.black_10, <PERSON>.drawable.black_j, R.drawable.black_q, R.drawable.black_k, R.drawable.black_a}};
    private static int screenWidth = ScreenUtil.getScreenWidth();
    private static int screenHeight = ScreenUtil.getScreenHeight();

    public static void setScale(float scale) {
        CardResUtil.scale = scale;
    }

    private static float scale = 1.0f; //小屏上尺寸需要缩放

    public static float getFirstLineY() {
        return sFirstLineY;
    }

    public static float getSecondLineY() {
        return sSecondLineY;
    }

    private static float sFirstLineY;
    private static float sSecondLineY;

    public static CardRes getCardRes(byte value) {
        String valueString = Integer.toHexString(value & 0xFF);
        if (valueString.length() == 1) {
            StringBuilder sb = new StringBuilder();
            valueString = sb.append("0").append(valueString).toString();
        }
        int colorRes = 0;
        int valueRes = 0;

        if (valueString.equals("4f")) {
            colorRes = R.drawable.small_joker;
        } else if (valueString.equals("5f")) {
            colorRes = R.drawable.big_joker;
        } else {
            int colorIndex = Integer.parseInt(valueString.substring(0, 1));
            colorRes = colorResArray[colorIndex];

            int valueColorIndex;
            if (colorRes == R.drawable.diamond_card || colorRes == R.drawable.heart_card) {
                valueColorIndex = 0;
            } else {
                valueColorIndex = 1;
            }

            int valueIndex = Integer.parseInt(valueString.substring(1, 2), 16) - 2;
            valueRes = valueResArray[valueColorIndex][valueIndex];
        }

        return new CardRes(colorRes, valueRes);
    }

    public static boolean isJokerCard(int colorRes) {
        return colorRes == CardResUtil.colorResArray[4] || colorRes == CardResUtil.colorResArray[5];
    }

    public static void setFirstLineY(float firstLineY) {
        sFirstLineY = firstLineY;
    }

    public static void setSecondLineY(float secondLineY) {
        sSecondLineY = secondLineY;
    }

    public static class CardRes {
        public int corlorRes;
        public int valueRes;

        public CardRes(int corlorRes, int valueRes) {
            this.corlorRes = corlorRes;
            this.valueRes = valueRes;
        }
    }

    public static int getCardsPutPosition(int totalSize, int index) {
        int cardTotalLength = ScreenUtil.dip2px((45 + (totalSize - 1) * 16) * scale);

        int rightMargin = (screenWidth -cardTotalLength) / 2;
        int x = screenWidth - rightMargin - ScreenUtil.dip2px( (45 + (totalSize - index) * 16) * scale);
        return x;
    }

    public static Point getHandCardsPostion(int size, int index) {
        Point point = new Point();
        int cardTotalLength = 0;
        int rightMargin = 0;
        if (size > 10) {
            if (index > 9) {
                cardTotalLength = ScreenUtil.dip2px((72 + (size - 10 - 1) * 30) * scale);
                point.y = (int) sSecondLineY;
                rightMargin = (screenWidth -cardTotalLength) / 2;
                point.x = screenWidth - rightMargin - ScreenUtil.dip2px((72 + (size - index - 1) * 30) * scale);
            } else {
                cardTotalLength = ScreenUtil.dip2px(343 * scale);
                point.y = (int) sFirstLineY;
                rightMargin = (screenWidth -cardTotalLength) / 2;
                point.x = screenWidth - rightMargin - ScreenUtil.dip2px((72 + (10 - index - 1) * 30) * scale);
            }
        } else {
            cardTotalLength = ScreenUtil.dip2px((72 + (size - 1) * 30) * scale);
            point.y = (int) sFirstLineY;
            rightMargin = (screenWidth -cardTotalLength) / 2;
            point.x = screenWidth - rightMargin - ScreenUtil.dip2px((72 + (size - index - 1) * 30) * scale);
        }

        return point;
    }
}
