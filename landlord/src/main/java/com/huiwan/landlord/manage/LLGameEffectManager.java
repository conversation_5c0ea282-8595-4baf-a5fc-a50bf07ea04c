package com.huiwan.landlord.manage;

import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.SurfaceView;
import android.view.View;
import android.widget.ImageView;

import com.huiwan.anim.FrameAnimation;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.ApplicationUtil;
import com.huiwan.base.util.SoundPoolUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.landlord.R;
import com.huiwan.landlord.SilkyAnimation;
import com.huiwan.landlord.model.Card;
import com.huiwan.base.util.ThreadUtil;
import com.huiwan.base.util.FileUtil;
import com.huiwan.user.UserService;
import com.huiwan.user.UserSimpleInfoCallback;
import com.huiwan.user.entity.User;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.download.DownloadCallback;
import com.wepie.download.Downloader;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

public class LLGameEffectManager {
    private static final String LL_DIR = LibBaseUtil.getApplication().getCacheDir() + File.separator + "land_lord_res";
    public static final String RES_FILE_PATH_PREFIX = LL_DIR + File.separator + "audio";
    private static final String LL_RES_VERSION_FILE = LL_DIR + File.separator + "ll_res_version.properties";
    private static final String VERSION_KEY = "version";
    private static Handler sHandler = new Handler(Looper.getMainLooper());
    private static SilkyAnimation animation;
    private static int retryNum;
    private static List<String> shunziAnimPath;
    private static List<String> lianduiAnimPath;
    private static List<String> feijiAnimPath;
    private static List<String> zhadanAnimPath;
    private static List<String> wangzhaAnimPath;
    private static List<String> chuntianAnimPath;
    private static List<String> fanchuntianAnimPath;
    private static boolean sEnableCardAnim = true;
    private static int[] shunziRes = {R.drawable.shunzi_00, R.drawable.shunzi_01, R.drawable.shunzi_02, R.drawable.shunzi_03, R.drawable.shunzi_04, R.drawable.shunzi_05, R.drawable.shunzi_06, R.drawable.shunzi_07, R.drawable.shunzi_08, R.drawable.shunzi_09,
            R.drawable.shunzi_10, R.drawable.shunzi_11, R.drawable.shunzi_12, R.drawable.shunzi_18,
            R.drawable.shunzi_20, R.drawable.shunzi_22, R.drawable.shunzi_24, R.drawable.shunzi_26, R.drawable.shunzi_28,
            R.drawable.shunzi_30, R.drawable.shunzi_32, R.drawable.shunzi_33};
    private static int[] lianduiRes = {R.drawable.liandui_00, R.drawable.liandui_01, R.drawable.liandui_02, R.drawable.liandui_03, R.drawable.liandui_04, R.drawable.liandui_05, R.drawable.liandui_06, R.drawable.liandui_07, R.drawable.liandui_08, R.drawable.liandui_09,
            R.drawable.liandui_10, R.drawable.liandui_11, R.drawable.liandui_12, R.drawable.liandui_18,
            R.drawable.liandui_20,  R.drawable.liandui_22,  R.drawable.liandui_24,R.drawable.liandui_26,  R.drawable.liandui_28,
            R.drawable.liandui_30, R.drawable.liandui_32, R.drawable.liandui_33};
    private static int[] chuntianRes = {R.drawable.chuntian_00, R.drawable.chuntian_01, R.drawable.chuntian_02, R.drawable.chuntian_03, R.drawable.chuntian_04, R.drawable.chuntian_05, R.drawable.chuntian_06, R.drawable.chuntian_07, R.drawable.chuntian_08, R.drawable.chuntian_09,
            R.drawable.chuntian_10, R.drawable.chuntian_11, R.drawable.chuntian_12, R.drawable.chuntian_13, R.drawable.chuntian_14, R.drawable.chuntian_15, R.drawable.chuntian_16, R.drawable.chuntian_17, R.drawable.chuntian_18,
            R.drawable.chuntian_19, R.drawable.chuntian_20,R.drawable.chuntian_21,  R.drawable.chuntian_22, R.drawable.chuntian_23,  R.drawable.chuntian_24,R.drawable.chuntian_25, R.drawable.chuntian_26, R.drawable.chuntian_27,  R.drawable.chuntian_28,
            R.drawable.chuntian_29, R.drawable.chuntian_30};
    private static int[] fanchuntianRes = {R.drawable.fanchuntian_00, R.drawable.fanchuntian_01, R.drawable.fanchuntian_02, R.drawable.fanchuntian_03, R.drawable.fanchuntian_04, R.drawable.fanchuntian_05, R.drawable.fanchuntian_06, R.drawable.fanchuntian_07, R.drawable.fanchuntian_08, R.drawable.fanchuntian_09,
            R.drawable.fanchuntian_10, R.drawable.fanchuntian_11, R.drawable.fanchuntian_12, R.drawable.fanchuntian_13, R.drawable.fanchuntian_14, R.drawable.fanchuntian_15, R.drawable.fanchuntian_16, R.drawable.fanchuntian_17, R.drawable.fanchuntian_18,
            R.drawable.fanchuntian_19, R.drawable.fanchuntian_20,R.drawable.fanchuntian_21,  R.drawable.fanchuntian_22, R.drawable.fanchuntian_23,  R.drawable.fanchuntian_24,R.drawable.fanchuntian_25, R.drawable.fanchuntian_26, R.drawable.fanchuntian_27,  R.drawable.fanchuntian_28,
            R.drawable.fanchuntian_29};

    public static void clearAnimation() {
        if (animation != null) animation.stop();
        animation = null;
    }

    private static List<String> initAnimPathListWithType(int type) {
        List<String> pathList = new ArrayList<>();
        File animEffectDirectory = new File(RES_FILE_PATH_PREFIX + File.separator + "dynamicEffect");
        String [] allAnimFileName = animEffectDirectory.list();
        if (allAnimFileName == null) return null;

        for (int i = 0; i < allAnimFileName.length - 1; i++) {
            String name = allAnimFileName[i];
            if (name.startsWith(type + "_")) {
                File file = new File(animEffectDirectory, name);
                pathList.add(file.getPath());
            }
        }

        return pathList;
    }

    public static void setSurfaceView(final SurfaceView mSurfaceView) {
        animation = new SilkyAnimation.Builder(mSurfaceView)
                //设置常驻内存的缓存数量, 默认5.
                .setCacheCount(5)
                //设置帧间隔, 默认100
                .setFrameInterval(45)
                //设置缩放类型, 默认fit center，与ImageView的缩放模式通用
                .setScaleType(SilkyAnimation.SCALE_TYPE_CENTER_CROP)
                //设置动画开始结束状态监听
                .setAnimationListener(new SilkyAnimation.AnimationStateListener() {
                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onFinish() {
                    }
                })
                //设置是否支持bitmap复用，默认为true
                .setSupportInBitmap(true)
                //设置循环模式, 默认不循环
                .build();
    }

    public static void playCardAudioAndAnim(int uid, final byte[] cards, final boolean isPass, final ImageView cardAnimView) {
        UserService.get().getCacheSimpleUser(uid, new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                boolean isMale = simpleInfo.gender != User.GENDER_FEMALE;

                if (isPass) {
                    playPassAudio(isMale);
                } else {
                    List<Card> cardList = CardTypeManager.parseFromBytes(cards);
                    int cardType = CardTypeManager.getCardType(cardList);

                    if (cardType == CardTypeManager.CARD_TYPE_DAN) {
                        playDanAudio(isMale, cardList.get(0).grade);
                    } else if (cardType == CardTypeManager.CARD_TYPE_DUIZI) {
                        playDuiziAudio(isMale, cardList.get(0).grade);
                    } else if (cardType == CardTypeManager.CARD_TYPE_SANZHANG) {
                        playSanzhangAudio(isMale, cardList.get(0).grade);
                    } else {
                        if (cardType > 0) {
                            playAudioAndAnimWithCardType(cardType, isMale, cardAnimView);
                        }
                    }
                }
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });
    }

    public static void playDoubleSound(int uid, final boolean isDouble) {
        UserService.get().getCacheSimpleUser(uid, new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                boolean isMale = simpleInfo.gender != User.GENDER_FEMALE;
                int cardType = isDouble ? 107 : 106;

                playCardTypeAudio(cardType, isMale);
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });
    }

    private static void playAudioAndAnimWithCardType(int cardType, boolean isMale, ImageView cardAnimView) {
        cardType = playCardTypeAudio(cardType, isMale);

        if (!sEnableCardAnim) return;

        if (cardType == CardTypeManager.CARD_TYPE_SHUNZI) {
            playShunziAnim(cardAnimView);
        } else if (cardType == CardTypeManager.CARD_TYPE_WANGZHA) {
            playWangzhaAnim();
        } else if (cardType == CardTypeManager.CARD_TYPE_FEIJIDAI || cardType == CardTypeManager.CARD_TYPE_FEIJI ||
                cardType == CardTypeManager.CARD_TYPE_FEIJIDAIDUI) {
            playFeijiAnim();
        } else if (cardType == CardTypeManager.CARD_TYPE_ZHADAN) {
            playZhadanAnim();
        } else if (cardType == CardTypeManager.CARD_TYPE_LIANDUI) {
            playLianduiAnim(cardAnimView);
        }
    }

    private static int playCardTypeAudio(int cardType, boolean isMale) {
        if (cardType == CardTypeManager.CARD_TYPE_FEIJI || cardType == CardTypeManager.CARD_TYPE_FEIJIDAIDUI
                || cardType == CardTypeManager.CARD_TYPE_FEIJIDAI) {
            cardType = CardTypeManager.CARD_TYPE_FEIJI;
        }

        int genderIndex = isMale ? 1 : 2;
        int cardTypeIndex = cardType;
        int cardValueIndex = 0;
        int randomIndex = (int) (Math.random() * 2);
        playAudio(genderIndex, cardTypeIndex, cardValueIndex, randomIndex, true);
        return cardType;
    }

    private static void playShunziAnim(final ImageView cardAnimView) {
        cardAnimView.setVisibility(View.VISIBLE);
        FrameAnimation cardAnim = new FrameAnimation(cardAnimView, shunziRes, 34, false);
        cardAnim.setAnimationListener(new FrameAnimation.AnimationListener() {
            @Override
            public void onAnimationStart() {}

            @Override
            public void onAnimationEnd() {
                cardAnimView.setBackground(null);
                cardAnimView.setVisibility(View.GONE);}

            @Override
            public void onAnimationRepeat() { }
        });
    }

    private static void playLianduiAnim(final ImageView cardAnimView) {
        cardAnimView.setVisibility(View.VISIBLE);
        FrameAnimation cardAnim = new FrameAnimation(cardAnimView, lianduiRes, 34, false);
        cardAnim.setAnimationListener(new FrameAnimation.AnimationListener() {
            @Override
            public void onAnimationStart() {}

            @Override
            public void onAnimationEnd() {
                cardAnimView.setBackground(null);
                cardAnimView.setVisibility(View.GONE);}

            @Override
            public void onAnimationRepeat() { }
        });
    }

    private static void playZhadanAnim() {
        if (zhadanAnimPath == null || zhadanAnimPath.size() == 0) {
            zhadanAnimPath = initAnimPathListWithType(13);
        }
        if (zhadanAnimPath == null || zhadanAnimPath.size() == 0) return;
        animation.startWithFilePathList(zhadanAnimPath);
    }

    private static void playFeijiAnim() {
        if (feijiAnimPath == null || feijiAnimPath.size() == 0) {
            feijiAnimPath = initAnimPathListWithType(8);
        }
        if (feijiAnimPath == null || feijiAnimPath.size() == 0) return;
        animation.startWithFilePathList(feijiAnimPath);
    }

    private static void playWangzhaAnim() {
        if (wangzhaAnimPath == null || wangzhaAnimPath.size() == 0) {
            wangzhaAnimPath = initAnimPathListWithType(14);
        }
        if (wangzhaAnimPath == null || wangzhaAnimPath.size() == 0) return;
        animation.startWithFilePathList(wangzhaAnimPath);
    }

    public static void playChuntian(final ImageView gameOverAnimView) {
        gameOverAnimView.setVisibility(View.VISIBLE);
        FrameAnimation gameOverAnim = new FrameAnimation(gameOverAnimView, chuntianRes, 25, false);
        gameOverAnim.setAnimationListener(new FrameAnimation.AnimationListener() {
            @Override
            public void onAnimationStart() {}

            @Override
            public void onAnimationEnd() {
                gameOverAnimView.setBackground(null);
                gameOverAnimView.setVisibility(View.GONE);}

            @Override
            public void onAnimationRepeat() { }
        });
    }

    public static void playFanChuntian(final ImageView gameOverAnimView) {
        gameOverAnimView.setVisibility(View.VISIBLE);
        FrameAnimation gameOverAnim = new FrameAnimation(gameOverAnimView, fanchuntianRes, 25, false);
        gameOverAnim.setAnimationListener(new FrameAnimation.AnimationListener() {
            @Override
            public void onAnimationStart() {}

            @Override
            public void onAnimationEnd() {
                gameOverAnimView.setBackground(null);
                gameOverAnimView.setVisibility(View.GONE);}

            @Override
            public void onAnimationRepeat() { }
        });
    }

    private static void playPassAudio(boolean isMale) {
        int genderIndex = isMale ? 1 : 2;
        int cardTypeIndex = 0;
        int cardValueIndex = 0;
        int randomIndex = (int) (Math.random() * 5);
        playAudio(genderIndex, cardTypeIndex, cardValueIndex, randomIndex, true);
    }

    private static void playDanAudio(boolean isMale, int grade) {
        int audioGrade = grade + 2;
        int genderIndex = isMale ? 1 : 2;
        int cardTypeIndex = CardTypeManager.CARD_TYPE_DAN;
        int cardValueIndex = audioGrade;
        int randomIndex = (int) (Math.random() * 2);
        playAudio(genderIndex, cardTypeIndex, cardValueIndex, randomIndex, true);
    }

    private static void playDuiziAudio(boolean isMale, int grade) {
        int audioGrade = grade + 2;
        int genderIndex = isMale ? 1 : 2;
        int cardTypeIndex = CardTypeManager.CARD_TYPE_DUIZI;
        int cardValueIndex = audioGrade;
        int randomIndex = (int) (Math.random() * 2);
        playAudio(genderIndex, cardTypeIndex, cardValueIndex, randomIndex, true);
    }

    private static void playSanzhangAudio(boolean isMale, int grade) {
        int audioGrade = grade + 2;
        int genderIndex = isMale ? 1 : 2;
        int cardTypeIndex = CardTypeManager.CARD_TYPE_SANZHANG;
        int cardValueIndex = audioGrade;
        int randomIndex = (int) (Math.random() * 2);
        playAudio(genderIndex, cardTypeIndex, cardValueIndex, randomIndex, true);
    }

    private static void playAudio(int genderIndex, int cardTypeIndex, int cardValueIndex, int randomIndex, boolean isWav) {
        String suffix;

        suffix = ".mp3";
        String fileName = RES_FILE_PATH_PREFIX + File.separator + "soundEffect" + File.separator + genderIndex + "_"
                + cardTypeIndex + "_" + cardValueIndex + "_" + randomIndex + suffix;
        if (!FileUtil.fileExists(fileName)) {
            fileName = RES_FILE_PATH_PREFIX + File.separator + "soundEffect" + File.separator + genderIndex + "_"
                    + cardTypeIndex + "_" + cardValueIndex + "_" + 0 + suffix;
        }

        if (ApplicationUtil.isApplicationAtTop()) {
            SoundPoolUtil.getInstance().playAndLoadLocalFile(fileName);
        }
    }

    public static void playWinAlarmAudio(int uid, final int size) {
        UserService.get().getCacheSimpleUser(uid, new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                int genderIndex = simpleInfo.gender != User.GENDER_FEMALE ? 1 : 2;
                int cardTypeIndex = 101;
                int cardValueIndex = size;
                int randomIndex = (int) (Math.random() * 2);
                playAudio(genderIndex, cardTypeIndex, cardValueIndex, randomIndex, true);
            }

            @Override
            public void onUserInfoFailed(String description) {}
        });
    }

    public static void playCallLordAudio(int uid, final boolean isWantLandLord, final boolean isGrab) {
        UserService.get().getCacheSimpleUser(uid, new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                int genderIndex = simpleInfo.gender != User.GENDER_FEMALE ? 1 : 2;
                int cardTypeIndex = 0;
                int cardValueIndex = 0;
                int randomIndex = 0;
                if (!isWantLandLord && !isGrab) {
                    cardTypeIndex = 102;
                } else if (!isWantLandLord && isGrab) {
                    cardTypeIndex = 103;
                } else if (isWantLandLord && !isGrab) {
                    cardTypeIndex = 104;
                } else {
                    cardTypeIndex = 105;
                    randomIndex = (int) (Math.random() * 2);
                }
                playAudio(genderIndex, cardTypeIndex, cardValueIndex, randomIndex, true);
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });
    }

    public static void playHotWordsSound(final String msg, int uid) {
        UserService.get().getCacheSimpleUser(uid, new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                boolean isMale = simpleInfo.gender != User.GENDER_FEMALE;
                int type = ConfigHelper.getInstance().getLandLordAudioResIdByHotMsg(msg);
                int genderIndex = isMale ? 1 : 2;
                playAudio(genderIndex, type, 0, 0, false);
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });
    }

    public static void checkDownloadLandLordRes() {
        int serverVersion = ConfigHelper.getInstance().getLandLordResVersion();

        if (serverVersion == 0) {
            // 防止因为网络延迟还没来得及获取配置，重试一次
            sHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    doCheckResdownload(ConfigHelper.getInstance().getLandLordResVersion());
                }
            }, 8 * 1000);
        } else {
            doCheckResdownload(serverVersion);
        }
    }

    private static void doCheckResdownload(int serverVersion) {
        if (getLandLordVoiceResVersion() < serverVersion) {
            retryNum++;
            if (retryNum > 5) return;

            ThreadUtil.runInOtherThread(new Runnable() {
                @Override
                public void run() {
                    File resDir = new File(RES_FILE_PATH_PREFIX);
                    try {
                        FileUtil.deleteDirectory(resDir);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    FileUtil.makeDirs(new File(RES_FILE_PATH_PREFIX));
                    final String fileName = getEffectZipFileName();
                    Downloader.newBuilder().setUrl(ConfigHelper.getInstance().getLandLordEffectsUrl())
                            .setCacheFilePath(fileName)
                            .setRetryTimes(3)
                            .download(new DownloadCallback() {
                                @Override
                                public void onSuccess(String url, String path) {
                                    ThreadUtil.runInOtherThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            ZipInputStream zipIs = null;
                                            try {
                                                InputStream gameInputStream = new FileInputStream(fileName);
                                                zipIs = new ZipInputStream(gameInputStream);
                                                ZipEntry ze;
                                                String path = RES_FILE_PATH_PREFIX;
                                                while ((ze = zipIs.getNextEntry()) != null) {
                                                    if (ze.isDirectory()) {
                                                        FileUtil.makeDirs(new File(path + File.separator + ze.getName()));
                                                    } else {
                                                        cpyEntry(zipIs, path + File.separator + ze.getName());
                                                        zipIs.closeEntry();
                                                    }
                                                }

                                                storeProperties(ConfigHelper.getInstance().getLandLordResVersion());
                                            } catch (Exception e) {
                                                sHandler.postDelayed(new Runnable() {
                                                    @Override
                                                    public void run() {
                                                        checkDownloadLandLordRes();
                                                    }
                                                }, 3 * 1000 * retryNum);
                                            } finally {
                                                if (zipIs != null) {
                                                    try {
                                                        zipIs.close();
                                                    } catch (IOException e) {
                                                        e.printStackTrace();
                                                    }
                                                }
                                            }
                                        }
                                    });
                                }

                                @Override
                                public void onFail(String msg) {

                                }

                                @Override
                                public void onPercent(int percent) {

                                }
                            });
                }
            });
        }
    }

    private static void storeProperties(int version) {
        Properties properties = new Properties();
        properties.put(VERSION_KEY, "" + version);
        String fileName = LL_RES_VERSION_FILE;
        if (!FileUtil.fileExists(fileName)) {
            FileUtil.safeCreateFile(fileName);
        }
        OutputStream os = null;
        try {
            os = new FileOutputStream(fileName);
            properties.store(os, new Date().toString());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private static int getLandLordVoiceResVersion() {
        File file = new File(LL_RES_VERSION_FILE);
        if (file.exists()) {
            String version = getLocalProperty(VERSION_KEY);
            if (TextUtils.isEmpty(version)) {
                return 0;
            }
            int ver = 0;
            try {
                ver=  Integer.parseInt(version);
            }catch (Exception e){
                e.printStackTrace();
            }
            return ver;
        } else {
            return 0;
        }
    }

    private static String getLocalProperty(String key) {
        try {
            Properties properties = new Properties();
            InputStream is = new FileInputStream(LL_RES_VERSION_FILE);
            properties.load(is);
            is.close();
            return properties.getProperty(key, "");
        } catch (IOException e) {
            return "";
        }
    }

    private static void cpyEntry(InputStream is, String destPath) throws Exception {
        FileOutputStream fos = null;
        try {
            FileUtil.createFile(destPath);
            fos = new FileOutputStream(new File(destPath));
            byte[] buffer = new byte[1024];
            int readByte;
            while ((readByte = is.read(buffer)) != -1) {
                fos.write(buffer, 0, readByte);
            }

        } catch (Exception e) {
            FileUtil.safeDeleteFile(destPath);
            throw new Exception("copy failed, " + e.getMessage());
        } finally {
            try {
                if (fos != null) {
                    fos.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static String getEffectZipFileName() {
        String zipName = "ll_effects.zip";
        try {
            return LibBaseUtil.getApplication().getExternalFilesDir(null).getAbsolutePath() + File.separator + zipName;
        } catch (Exception e) {
            e.printStackTrace();
            return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS) + File.separator + "wepie" + File.separator + zipName;
        }
    }

    public static void disableCardAnim() {
        sEnableCardAnim = false;
    }
}
