package com.huiwan.landlord.manage;

import com.huiwan.landlord.model.Card;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class CardTypeManager {
    public static final int CARD_TYPE_DAN = 1;
    public static final int CARD_TYPE_DUIZI = 2;
    public static final int CARD_TYPE_SANZHANG = 3;
    public static final int CARD_TYPE_SANDAIYI = 4;
    public static final int CARD_TYPE_SANDAIER = 5;
    public static final int CARD_TYPE_SHUNZI = 6;
    public static final int CARD_TYPE_LIANDUI = 7;
    public static final int CARD_TYPE_FEIJI = 8;
    public static final int CARD_TYPE_FEIJIDAI = 9;
    public static final int CARD_TYPE_FEIJIDAIDUI = 10;
    public static final int CARD_TYPE_SIDAIER = 11;
    public static final int CARD_TYPE_SIDAIDUI = 12;
    public static final int CARD_TYPE_ZHADAN = 13;
    public static final int CARD_TYPE_WANGZHA = 14;
    public static final int CARD_TYPE_UNDEFINED = -1;

    public static List<Card> parseFromBytes(byte[] cards) {
        List<Card> cardList = new ArrayList<>();
        for (byte value : cards) {
            Card cardModel = new Card(value);
            cardList.add(cardModel);
        }
        return cardList;
    }

    public static List<Card> parseSorted(byte[] cards) {
        List<Card> cardList = parseFromBytes(cards);
        sortCards(cardList);

        return cardList;
    }

    public static CardSelectResult findFeiji(List<Card> selectedCards) {
        CardSelectResult cardSelectResult = new CardSelectResult();
        sortCards(selectedCards);

        for (int i = 0; i < selectedCards.size(); i++) {
            List<Card> scannedCards = new ArrayList<>();
            int lastInnerGrade = 0;
            int equalTimes = 0;
            for (int j = i; j < selectedCards.size(); j++) {
                Card card = selectedCards.get(j);
                if (j == selectedCards.size() - 1) {
                    if (card.grade == lastInnerGrade && card.grade < 13) {
                        scannedCards.add(card);
                        if (scannedCards.size() >= 6 && scannedCards.size() % 3 == 0) {
                            cardSelectResult.selectCards = scannedCards;
                            cardSelectResult.range = scannedCards.size() / 3;

                            return cardSelectResult;
                        }
                        break;
                    } else {
                        if (scannedCards.size() >= 6 && scannedCards.size() % 3 == 0) {
                            cardSelectResult.selectCards = scannedCards;
                            cardSelectResult.range = scannedCards.size() / 3;

                            return cardSelectResult;
                        }
                        break;
                    }
                }

                if (lastInnerGrade > 0 && card.grade > lastInnerGrade + 1) {
                    if (scannedCards.size() >= 6 && scannedCards.size() % 3 == 0) {
                        cardSelectResult.selectCards = scannedCards;
                        cardSelectResult.range = scannedCards.size() / 3;

                        return cardSelectResult;
                    }
                    break;
                }
                if (card.grade == lastInnerGrade) {
                    equalTimes++;
                } else {
                    if (equalTimes < 2 && lastInnerGrade != 0) break;
                    equalTimes = 0;
                }
                if (equalTimes > 2) {
                    continue;
                }

                if (card.grade < 13) {
                    scannedCards.add(card);
                }
                lastInnerGrade = card.grade;
            }
        }
        return cardSelectResult;
    }

    public static CardSelectResult findLiandui(List<Card> selectedCards) {
        sortCards(selectedCards);
        CardSelectResult cardSelectResult = new CardSelectResult();

        for (int i = 0; i < selectedCards.size(); i++) {
            List<Card> scannedCards = new ArrayList<>();
            int lastInnerGrade = 0;
            int equalTimes = 0;
            for (int j = i; j < selectedCards.size(); j++) {
                Card card = selectedCards.get(j);
                if (j == selectedCards.size() - 1) {
                    if (card.grade == lastInnerGrade && card.grade < 13 && scannedCards.size() % 2 != 0) {
                        scannedCards.add(card);
                        if (scannedCards.size() >= 6 && scannedCards.size() % 2 == 0) {
                            cardSelectResult.selectCards = scannedCards;
                            cardSelectResult.range = scannedCards.size() / 2;

                            return cardSelectResult;
                        }
                        break;
                    } else {
                        if (scannedCards.size() >= 6 && scannedCards.size() % 2 == 0) {
                            cardSelectResult.selectCards = scannedCards;
                            cardSelectResult.range = scannedCards.size() / 2;

                            return cardSelectResult;
                        }
                        break;
                    }
                }

                if (lastInnerGrade > 0 && card.grade > lastInnerGrade + 1) {
                    if (scannedCards.size() >= 6 && scannedCards.size() % 2 == 0) {
                        cardSelectResult.selectCards = scannedCards;
                        cardSelectResult.range = scannedCards.size() / 2;

                        return cardSelectResult;
                    }
                    break;
                }
                if (card.grade == lastInnerGrade) {
                    equalTimes++;
                } else {
                    if (equalTimes < 1 && lastInnerGrade != 0) {
                        break;
                    }
                    equalTimes = 0;
                }
                if (equalTimes > 1) {
                    continue;
                }

                if (card.grade < 13) {
                    scannedCards.add(card);
                }
                lastInnerGrade = card.grade;
            }
        }
        return cardSelectResult;
    }

    public static CardSelectResult findShunzi(List<Card> selectedCards) {
        sortCards(selectedCards);
        CardSelectResult cardSelectResult = new CardSelectResult();

        int lastOutterGrade = 0;
        for (int i = 0; i < selectedCards.size(); i++) {
            List<Card> scannedCards = new ArrayList<>();
            if (lastOutterGrade == selectedCards.get(i).grade) continue;

            int lastInnerGrade = 0;
            for (int j = i; j < selectedCards.size(); j++) {
                Card card = selectedCards.get(j);
                if (j == selectedCards.size() - 1) {
                    if (card.grade == lastInnerGrade + 1 && card.grade < 13) {
                        scannedCards.add(card);
                    }
                    if (scannedCards.size() >= 5) {
                        cardSelectResult.selectCards = scannedCards;
                        cardSelectResult.range = scannedCards.size();

                        return cardSelectResult;
                    }
                }
                if (lastInnerGrade != 0 && card.grade > lastInnerGrade + 1) {
                    if (scannedCards.size() >= 5) {
                        cardSelectResult.selectCards = scannedCards;
                        cardSelectResult.range = scannedCards.size();

                        return cardSelectResult;
                    }
                    break;
                }
                if (card.grade == lastInnerGrade) {
                    continue;
                }
                if (card.grade < 13) {
                    scannedCards.add(card);
                }
                lastInnerGrade = card.grade;
            }

            lastOutterGrade = selectedCards.get(i).grade;
        }

        return cardSelectResult;
    }

    public static class CardComparator implements Comparator<Card> {

        public int compare(Card card1, Card card2) {
            int result = -1;

            int grade1 = card1.grade;
            int grade2 = card2.grade;

            if (grade1 > grade2) {
                result = 1;
            } else if (grade1 < grade2) {
                result = -1;
            } else {
                // 等级相同的情况，比如都是9
                Card.CardBigType bigType1 = card1.bigType;
                Card.CardBigType bigType2 = card2.bigType;
                // 从左到右，黑桃、红桃、梅花、方块
                if (bigType1.equals(Card.CardBigType.HEI_TAO)) {
                    result = 1;
                } else if (bigType1.equals(Card.CardBigType.HONG_TAO)) {
                    if (bigType2.equals(Card.CardBigType.MEI_HUA)
                            || bigType2.equals(Card.CardBigType.FANG_KUAI)) {
                        result = 1;
                    } else {
                        result = -1;
                    }
                } else if (bigType1.equals(Card.CardBigType.MEI_HUA)) {
                    if (bigType2.equals(Card.CardBigType.FANG_KUAI)) {
                        result = 1;
                    } else {
                        result = -1;
                    }
                }
                // 2张牌的等级不可能完全相同,程序内部采用这种设计
                else {
                    result = -1;
                }
            }

            return result;
        }

    }

    /**
     * 对牌进行排序，从小到大，比较器为CardComparator
     *
     * @param cards 牌的集合
     */
    public static void sortCards(List<Card> cards) {
        Collections.sort(cards, new CardComparator());
    }

    public static int getCardType(List<Card> cards) {
        if (isDan(cards)) {
            return CARD_TYPE_DAN;
        } else if (isDuiZi(cards)) {
            return CARD_TYPE_DUIZI;
        } else if (isSanBuDai(cards)) {
            return CARD_TYPE_SANZHANG;
        } else if (isSanDaiYi(cards) != -1) {
            return CARD_TYPE_SANDAIYI;
        } else if (isSanDaiEr(cards)) {
            return CARD_TYPE_SANDAIER;
        } else if (isZhaDan(cards)) {
            return CARD_TYPE_ZHADAN;
        } else if (isSiDaiEr(cards)) {
            return CARD_TYPE_SIDAIER;
        } else if (isSiDaiDui(cards)) {
            return CARD_TYPE_SIDAIDUI;
        } else if (isShunZi(cards)) {
            return CARD_TYPE_SHUNZI;
        } else if (isLianDui(cards)) {
            return CARD_TYPE_LIANDUI;
        } else if (isFeiJiBuDai(cards)) {
            return CARD_TYPE_FEIJI;
        } else if (isFeiJiDai(cards)) {
            return CARD_TYPE_FEIJIDAI;
        } else if (isFeiJiDaiDui(cards)) {
            return CARD_TYPE_FEIJIDAIDUI;
        } else if (isDuiWang(cards)) {
            return CARD_TYPE_WANGZHA;
        }

        return CARD_TYPE_UNDEFINED;
    }

    public static boolean canMatchPrevCard(List<Card> myCards,
                                           List<Card> prevCards, int prevCardType) {
        // 我的牌和上家的牌都不能为null
        if (myCards == null || prevCards == null) {
            return false;
        }

        if (prevCardType == CARD_TYPE_UNDEFINED) {
            return false;
        }

        // 默认情况：上家和自己想出的牌都符合规则
        sortCards(myCards);// 对牌排序
        sortCards(prevCards);// 对牌排序

        // 上一首牌的个数
        int prevSize = prevCards.size();
        int mySize = myCards.size();

        // 我先出牌，上家没有牌
        if (prevSize == 0 && mySize != 0) {
            return true;
        }

        // 集中判断是否王炸，免得多次判断王炸
        if (prevCardType == CARD_TYPE_WANGZHA) {
            return false;
        }

        if (mySize >= 2) {
            List<Card> cards = new ArrayList<Card>();
            cards.add(new Card(myCards.get(mySize - 1).value));
            cards.add(new Card(myCards.get(mySize - 2).value));
            if (isDuiWang(cards)) {
                return true;
            }
        }

        // 集中判断对方不是炸弹，我出炸弹的情况
        if (prevCardType != CARD_TYPE_ZHADAN) {
            if (mySize >= 4) {
                for (int i = 0; i < mySize - 3; i++) {
                    int grade0 = myCards.get(i).grade;
                    int grade1 = myCards.get(i + 1).grade;
                    int grade2 = myCards.get(i + 2).grade;
                    int grade3 = myCards.get(i + 3).grade;

                    if (grade1 == grade0 && grade2 == grade0
                            && grade3 == grade0) {
                        return true;
                    }
                }
            }

        }

        int prevGrade = prevCards.get(0).grade;

        // 比较2家的牌，主要有2种情况，1.我出和上家一种类型的牌，即对子管对子；
        // 2.我出炸弹，此时，和上家的牌的类型可能不同
        // 王炸的情况已经排除

        // 上家出单
        if (prevCardType == CARD_TYPE_DAN) {
            // 一张牌可以大过上家的牌
            for (int i = mySize - 1; i >= 0; i--) {
                int grade = myCards.get(i).grade;
                if (grade > prevGrade) {
                    // 只要有1张牌可以大过上家，则返回true
                    return true;
                }
            }

        }
        // 上家出对子
        else if (prevCardType == CARD_TYPE_DUIZI) {
            // 2张牌可以大过上家的牌
            for (int i = mySize - 1; i >= 1; i--) {
                int grade0 = myCards.get(i).grade;
                int grade1 = myCards.get(i - 1).grade;

                if (grade0 == grade1) {
                    if (grade0 > prevGrade) {
                        // 只要有1对牌可以大过上家，则返回true
                        return true;
                    }
                }
            }

        }
        // 上家出3不带
        else if (prevCardType == CARD_TYPE_SANZHANG) {
            // 3张牌可以大过上家的牌
            for (int i = mySize - 1; i >= 2; i--) {
                int grade0 = myCards.get(i).grade;
                int grade1 = myCards.get(i - 1).grade;
                int grade2 = myCards.get(i - 2).grade;

                if (grade0 == grade1 && grade0 == grade2) {
                    if (grade0 > prevGrade) {
                        // 只要3张牌可以大过上家，则返回true
                        return true;
                    }
                }
            }

        }
        // 上家出3带1
        else if (prevCardType == CARD_TYPE_SANDAIYI) {
            // 3带1 3不带 比较只多了一个判断条件
            if (mySize < 4) {
                return false;
            }

            // 3张牌可以大过上家的牌
            for (int i = mySize - 1; i >= 2; i--) {
                int grade0 = myCards.get(i).grade;
                int grade1 = myCards.get(i - 1).grade;
                int grade2 = myCards.get(i - 2).grade;

                if (grade0 == grade1 && grade0 == grade2) {
                    int sanzhangGrade;
                    int sanzhangIndex = getFirstSanzhangIndex(prevCards);
                    if (sanzhangIndex > -1) {
                        sanzhangGrade = prevCards.get(sanzhangIndex).grade;
                        if (grade0 > sanzhangGrade) {
                            // 只要3张牌可以大过上家，则返回true
                            return true;
                        }
                    } else {
                        return false;
                    }

                }
            }

        }
        // 3带2
        else if (prevCardType == CARD_TYPE_SANDAIER) {
            if (mySize < 5) {
                return false;
            }

            // 3张牌可以大过上家的牌
            for (int i = mySize - 1; i >= 2; i--) {
                int grade0 = myCards.get(i).grade;
                int grade1 = myCards.get(i - 1).grade;
                int grade2 = myCards.get(i - 2).grade;

                if (grade0 == grade1 && grade0 == grade2) {
                    int sanzhangGrade;
                    int sanzhangIndex = getFirstSanzhangIndex(prevCards);
                    if (sanzhangIndex > -1) {
                        sanzhangGrade = prevCards.get(sanzhangIndex).grade;
                        if (grade0 > sanzhangGrade) {
                            // 只要3张牌可以大过上家，则返回true
                            return true;
                        }
                    } else {
                        return false;
                    }

                }
            }

        }
        // 上家出炸弹
        else if (prevCardType == CARD_TYPE_ZHADAN) {
            // 4张牌可以大过上家的牌
            for (int i = mySize - 1; i >= 3; i--) {
                int grade0 = myCards.get(i).grade;
                int grade1 = myCards.get(i - 1).grade;
                int grade2 = myCards.get(i - 2).grade;
                int grade3 = myCards.get(i - 3).grade;

                if (grade0 == grade1 && grade0 == grade2 && grade0 == grade3) {
                    if (grade0 > prevGrade) {
                        // 只要有4张牌可以大过上家，则返回true
                        return true;
                    }
                }
            }

        }
        // 上家出顺子
        else if (prevCardType == CARD_TYPE_SHUNZI) {
            if (mySize < prevSize) {
                return false;
            } else {
                for (int i = mySize - 1; i >= prevSize - 1; i--) {
                    List<Card> cards = new ArrayList<Card>();
                    int lastGrade = 0;
                    for (int j = 0; j <= i; j++) {
                        if (myCards.get(i - j).grade != lastGrade) {
                            cards.add(new Card(myCards.get(i - j).value));
                            lastGrade = myCards.get(i - j).grade;
                            if (cards.size() == prevSize) break;
                        }
                    }

                    int myCardType = getCardType(cards);
                    if (myCardType == CARD_TYPE_SHUNZI) {
                        int myGrade2 = cards.get(cards.size() - 1).grade;// 最大的牌在最后
                        int prevGrade2 = prevCards.get(prevSize - 1).grade;// 最大的牌在最后

                        if (myGrade2 > prevGrade2) {
                            return true;
                        }
                    }
                }
            }

        }
        // 上家出连对
        else if (prevCardType == CARD_TYPE_LIANDUI) {
            if (mySize < prevSize) {
                return false;
            } else {
                for (int i = mySize - 1; i >= prevSize - 1; i--) {
                    List<Card> cards = new ArrayList<Card>();

                    int gradeEqualTimes = 0;
                    int lastGrade = 0;
                    for (int j = 0; j <= i; j++) {
                        if (myCards.get(i - j).grade != lastGrade) {
                            lastGrade = myCards.get(i - j).grade;
                            gradeEqualTimes = 0;
                        } else {
                            gradeEqualTimes++;
                        }
                        // 连续出现三张牌值相等的，跳过这张牌
                        if (gradeEqualTimes > 1) continue;

                        cards.add(new Card(myCards.get(i - j).value));
                        if (cards.size() == prevSize) break;
                    }

                    int myCardType = getCardType(cards);
                    if (myCardType == CARD_TYPE_LIANDUI) {
                        int myGrade2 = cards.get(cards.size() - 1).grade;// 最大的牌在最后,getCardType会对列表排序
                        int prevGrade2 = prevCards.get(prevSize - 1).grade;// 最大的牌在最后

                        if (myGrade2 > prevGrade2) {
                            return true;
                        }
                    }
                }
            }

        }
        // 上家出飞机
        else if (prevCardType == CARD_TYPE_FEIJI) {
            if (mySize < prevSize) {
                return false;
            } else {
                for (int i = mySize - 1; i >= prevSize - 1; i--) {
                    List<Card> cards = new ArrayList<Card>();
                    for (int j = 0; j < prevSize; j++) {
                        cards.add(new Card(myCards.get(i - j).value));
                    }

                    int myCardType = getCardType(cards);
                    if (myCardType == CARD_TYPE_FEIJI) {
                        int myGrade4 = cards.get(4).grade;//
                        int prevGrade4 = prevCards.get(4).grade;//

                        if (myGrade4 > prevGrade4) {
                            return true;
                        }
                    }
                }
            }
        }
        else if (prevCardType == CARD_TYPE_FEIJIDAI) {
            if (mySize < prevSize) {
                return false;
            } else {
                for (int i = mySize - 1; i >= prevSize - 1; i--) {
                    List<Card> cards = new ArrayList<Card>();
                    for (int j = 0; j < prevSize; j++) {
                        cards.add(new Card(myCards.get(i - j).value));
                    }

                    int myCardType = getCardType(cards);
                    if (myCardType == CARD_TYPE_FEIJIDAI) {
                        int mySanZhangIndex = getFirstSanzhangIndex(myCards);
                        int prevSanZhangIndex = getFirstSanzhangIndex(prevCards);
                        if (mySanZhangIndex != -1 && prevSanZhangIndex != -1) {
                            int myGrade4 = myCards.get(mySanZhangIndex).grade;
                            int prevGrade4 = prevCards.get(prevSanZhangIndex).grade;

                            if (myGrade4 > prevGrade4) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        else if (prevCardType == CARD_TYPE_FEIJIDAIDUI) {
            if (mySize < prevSize) {
                return false;
            } else {
                for (int i = mySize - 1; i >= prevSize - 1; i--) {
                    List<Card> cards = new ArrayList<Card>();
                    for (int j = 0; j < prevSize; j++) {
                        cards.add(new Card(myCards.get(i - j).value));
                    }

                    int myCardType = getCardType(cards);
                    if (myCardType == CARD_TYPE_FEIJIDAIDUI) {
                        int mySanZhangIndex = getFirstSanzhangIndex(myCards);
                        int prevSanZhangIndex = getFirstSanzhangIndex(prevCards);
                        if (mySanZhangIndex != -1 && prevSanZhangIndex != -1) {
                            int myGrade4 = myCards.get(mySanZhangIndex).grade;
                            int prevGrade4 = prevCards.get(prevSanZhangIndex).grade;

                            if (myGrade4 > prevGrade4) {
                                return true;
                            }
                        }
                    }
                }
            }
        }

        return false;
    }

    public static List<List<Integer>> getPromptCards(List<Card> myCards,
                                                     List<Card> prevCards, int prevCardType) {
        if (myCards == null || prevCards == null || myCards.size() == 0 || prevCards.size() == 0)
            return new ArrayList<>();
        if (prevCardType == CARD_TYPE_UNDEFINED) return new ArrayList<>();

        List<List<Integer>> promptCards = new ArrayList<>();
        sortCards(myCards);
        sortCards(prevCards);

        int prevGrade = prevCards.get(0).grade;
        int mySize = myCards.size();
        int prevSize = prevCards.size();
        int lastGrade = 0;
        boolean hasSameTypeMatch = false;

        if (prevCardType == CARD_TYPE_DAN) {
            hasSameTypeMatch = promptDan(myCards, promptCards, prevGrade, mySize, lastGrade, hasSameTypeMatch);
        }
        else if (prevCardType == CARD_TYPE_DUIZI) {
            hasSameTypeMatch = promptDuizi(myCards, promptCards, prevGrade, mySize, lastGrade, hasSameTypeMatch);
        }
        else if (prevCardType == CARD_TYPE_SANZHANG) {
            hasSameTypeMatch = promptSanzhang(myCards, promptCards, prevGrade, mySize, lastGrade, hasSameTypeMatch);
        }
        else if (prevCardType == CARD_TYPE_ZHADAN) {
            hasSameTypeMatch = promptZhadan(myCards, promptCards, prevGrade, mySize, lastGrade, hasSameTypeMatch);
        }
        else if (prevCardType == CARD_TYPE_SIDAIDUI || prevCardType == CARD_TYPE_SIDAIER) {
            hasSameTypeMatch = promptSidai(myCards, promptCards, mySize, lastGrade, hasSameTypeMatch);
        }
        else if (prevCardType == CARD_TYPE_SANDAIYI) {
            hasSameTypeMatch = promptSanDaiYi(myCards, prevCards, promptCards, mySize, lastGrade, hasSameTypeMatch);
        }
        else if (prevCardType == CARD_TYPE_SANDAIER) {
            hasSameTypeMatch = promptSanDaiEr(myCards, prevCards, promptCards, mySize, lastGrade, hasSameTypeMatch);
        }
        else if (prevCardType == CARD_TYPE_SHUNZI) {
            hasSameTypeMatch = promptShunZi(myCards, prevCards, promptCards, mySize, prevSize, hasSameTypeMatch);
        }
        else if (prevCardType == CARD_TYPE_LIANDUI) {
            hasSameTypeMatch = promptLianDui(myCards, prevCards, promptCards, mySize, prevSize, hasSameTypeMatch);
        }
        else if (prevCardType == CARD_TYPE_FEIJI) {
            hasSameTypeMatch = promptFeiji(myCards, prevCards, promptCards, mySize, prevSize, hasSameTypeMatch);
        }
        else if (prevCardType == CARD_TYPE_FEIJIDAI) {
            hasSameTypeMatch = promptFeijiDaiYi(myCards, prevCards, promptCards, mySize, prevSize, hasSameTypeMatch);
        }
        else if (prevCardType == CARD_TYPE_FEIJIDAIDUI) {
            hasSameTypeMatch = promptFeijiDaiDui(myCards, prevCards, promptCards, mySize, prevSize, hasSameTypeMatch);
        }

        // 相同牌型的管不上，找炸弹
        if (!hasSameTypeMatch && prevCardType != CARD_TYPE_ZHADAN) {
            for (int i = 0; i < mySize - 3; i++) {
                int grade1 = myCards.get(i).grade;
                int grade2 = myCards.get(i + 1).grade;
                int grade3 = myCards.get(i + 2).grade;
                int grade4 = myCards.get(i + 3).grade;

                if (grade1 == grade2 && grade2 == grade3 && grade3 == grade4) {
                    List<Integer> indexList = new ArrayList<>();
                    indexList.add(i);
                    indexList.add(i + 1);
                    indexList.add(i + 2);
                    indexList.add(i + 3);

                    promptCards.add(indexList);
                }
            }

            if (myCards.get(mySize - 1).grade + myCards.get(mySize - 2).grade == 29) {
                List<Integer> indexList = new ArrayList<>();
                indexList.add(mySize - 1);
                indexList.add(mySize - 2);

                promptCards.add(indexList);
            }
        }

        return promptCards;
    }

    private static boolean promptLianDui(List<Card> myCards, List<Card> prevCards, List<List<Integer>> promptCards, int mySize, int prevSize, boolean hasSameTypeMatch) {
        for (int i = 0; i <= mySize - prevSize; i++) {
            List<Card> cards = new ArrayList<Card>();
            List<Integer> indexList = new ArrayList<>();

            int gradeEqualTimes = 0;
            int lastGrade = 0;
            for (int j = 0; j < mySize - i; j++) {
                if (myCards.get(i + j).grade != lastGrade) {
                    lastGrade = myCards.get(i + j).grade;
                    gradeEqualTimes = 0;
                } else {
                    gradeEqualTimes++;
                }
                // 连续出现三张牌值相等的，跳过这张牌
                if (gradeEqualTimes > 1) continue;

                cards.add(new Card(myCards.get(i + j).value));
                indexList.add(i + j);
                if (cards.size() == prevSize) break;
            }

            int myCardType = getCardType(cards);
            if (myCardType == CARD_TYPE_LIANDUI) {
                int myGrade = cards.get(cards.size() - 1).grade;
                int otherGrade = prevCards.get(prevCards.size() - 1).grade;

                if (myGrade > otherGrade) {
                    hasSameTypeMatch = true;
                    promptCards.add(indexList);
                }
            }
        }
        return hasSameTypeMatch;
    }

    private static boolean promptShunZi(List<Card> myCards, List<Card> prevCards, List<List<Integer>> promptCards, int mySize, int prevSize, boolean hasSameTypeMatch) {
        for (int i = 0; i <= mySize - prevSize; i++) {
            List<Card> cards = new ArrayList<Card>();
            List<Integer> indexList = new ArrayList<>();

            int lastGrade = 0;
            for (int j = 0; j < mySize - i; j++) {
                if (myCards.get(i + j).grade != lastGrade) {
                    cards.add(new Card(myCards.get(i + j).value));
                    indexList.add(i + j);
                    lastGrade = myCards.get(i + j).grade;

                    if (cards.size() == prevSize) break;
                }
            }

            int myCardType = getCardType(cards);
            if (myCardType == CARD_TYPE_SHUNZI) {
                int myGrade = cards.get(cards.size() - 1).grade;
                int otherGrade = prevCards.get(prevCards.size() - 1).grade;

                if (myGrade > otherGrade) {
                    hasSameTypeMatch = true;
                    promptCards.add(indexList);
                }
            }
        }
        return hasSameTypeMatch;
    }

    private static boolean promptSanDaiEr(List<Card> myCards, List<Card> prevCards, List<List<Integer>> promptCards, int mySize, int lastGrade, boolean hasSameTypeMatch) {
        for (int i = 0; i < mySize - 2; i++) {
            int grade1 = myCards.get(i).grade;
            int grade2 = myCards.get(i + 1).grade;
            int grade3 = myCards.get(i + 2).grade;

            int prevSanzhangIndex = getFirstSanzhangIndex(prevCards);
            int prevSanzhangGrade = prevCards.get(prevSanzhangIndex).grade;

            if ((grade1 == grade2) && (grade2 == grade3)) {
                if (grade1 > prevSanzhangGrade && myCards.get(i).grade != lastGrade) {
                    lastGrade = myCards.get(i).grade;

                    for (int j = 0; j < mySize - 1; j++) {
                        if (myCards.get(j).grade !=  grade1) {
                            if (myCards.get(j).grade == myCards.get(j+1).grade) {
                                hasSameTypeMatch = true;
                                List<Integer> cardIndex = new ArrayList<>();
                                cardIndex.add(i);
                                cardIndex.add(i + 1);
                                cardIndex.add(i + 2);
                                cardIndex.add(j);
                                cardIndex.add(j + 1);

                                promptCards.add(cardIndex);
                                break;
                            }
                        }
                    }
                }
            }
        }
        return hasSameTypeMatch;
    }

    private static boolean promptSanDaiYi(List<Card> myCards, List<Card> prevCards, List<List<Integer>> promptCards, int mySize, int lastGrade, boolean hasSameTypeMatch) {
        for (int i = 0; i < mySize - 2; i++) {
            int grade1 = myCards.get(i).grade;
            int grade2 = myCards.get(i + 1).grade;
            int grade3 = myCards.get(i + 2).grade;

            int prevSanzhangIndex = getFirstSanzhangIndex(prevCards);
            int prevSanzhangGrade = prevCards.get(prevSanzhangIndex).grade;

            if ((grade1 == grade2) && (grade2 == grade3)) {
                if (grade1 > prevSanzhangGrade && myCards.get(i).grade != lastGrade) {
                    lastGrade = myCards.get(i).grade;

                    for (int j = 0; j < mySize; j++) {
                        // 这里只找3带1的牌型，因此要选一张和其他3张不一样的牌
                        if (myCards.get(j).grade != grade1) {
                            hasSameTypeMatch = true;
                            List<Integer> cardIndex = new ArrayList<>();
                            cardIndex.add(i);
                            cardIndex.add(i + 1);
                            cardIndex.add(i + 2);
                            cardIndex.add(j);

                            promptCards.add(cardIndex);
                            break;
                        }
                    }
                }
            }
        }
        return hasSameTypeMatch;
    }

    private static boolean promptSidai(List<Card> myCards, List<List<Integer>> promptCards, int mySize, int lastGrade, boolean hasSameTypeMatch) {
        for (int i = 0; i < mySize - 3; i++) {
            int grade1 = myCards.get(i).grade;
            int grade2 = myCards.get(i + 1).grade;
            int grade3 = myCards.get(i + 2).grade;
            int grade4 = myCards.get(i + 3).grade;

            if ((grade1 == grade2) && (grade2 == grade3) && (grade3 == grade4)) {
                if (myCards.get(i).grade != lastGrade) {
                    lastGrade = myCards.get(i).grade;
                    hasSameTypeMatch = true;

                    List<Integer> cardIndex = new ArrayList<>();
                    cardIndex.add(i);
                    cardIndex.add(i + 1);
                    cardIndex.add(i + 2);
                    cardIndex.add(i + 3);
                    promptCards.add(cardIndex);
                }
            }
        }
        return hasSameTypeMatch;
    }

    private static boolean promptZhadan(List<Card> myCards, List<List<Integer>> promptCards, int prevGrade, int mySize, int lastGrade, boolean hasSameTypeMatch) {
        for (int i = 0; i < mySize - 3; i++) {
            int grade1 = myCards.get(i).grade;
            int grade2 = myCards.get(i + 1).grade;
            int grade3 = myCards.get(i + 2).grade;
            int grade4 = myCards.get(i + 3).grade;

            if ((grade1 == grade2) && (grade2 == grade3) && (grade3 == grade4)) {
                if (grade1 > prevGrade && myCards.get(i).grade != lastGrade) {
                    lastGrade = myCards.get(i).grade;
                    hasSameTypeMatch = true;

                    List<Integer> cardIndex = new ArrayList<>();
                    cardIndex.add(i);
                    cardIndex.add(i + 1);
                    cardIndex.add(i + 2);
                    cardIndex.add(i + 3);
                    promptCards.add(cardIndex);
                }
            }
        }

        if (myCards.get(mySize - 1).grade + myCards.get(mySize - 2).grade == 29) {
            List<Integer> indexList = new ArrayList<>();
            indexList.add(mySize - 1);
            indexList.add(mySize - 2);

            promptCards.add(indexList);
        }
        return hasSameTypeMatch;
    }

    private static boolean promptSanzhang(List<Card> myCards, List<List<Integer>> promptCards, int prevGrade, int mySize, int lastGrade, boolean hasSameTypeMatch) {
        for (int i = 0; i < mySize - 2; i++) {
            int grade1 = myCards.get(i).grade;
            int grade2 = myCards.get(i + 1).grade;
            int grade3 = myCards.get(i + 2).grade;

            if ((grade1 == grade2) && (grade2 == grade3)) {
                if (grade1 > prevGrade && myCards.get(i).grade != lastGrade) {
                    lastGrade = myCards.get(i).grade;
                    hasSameTypeMatch = true;

                    List<Integer> cardIndex = new ArrayList<>();
                    cardIndex.add(i);
                    cardIndex.add(i + 1);
                    cardIndex.add(i + 2);
                    promptCards.add(cardIndex);
                }
            }
        }
        return hasSameTypeMatch;
    }

    private static boolean promptDuizi(List<Card> myCards, List<List<Integer>> promptCards, int prevGrade, int mySize, int lastGrade, boolean hasSameTypeMatch) {
        for (int i = 0; i < mySize - 1; i++) {
            int grade1 = myCards.get(i).grade;
            int grade2 = myCards.get(i + 1).grade;

            if (grade1 == grade2) {
                if (grade1 > prevGrade && myCards.get(i).grade != lastGrade) {
                    lastGrade = myCards.get(i).grade;
                    hasSameTypeMatch = true;

                    List<Integer> cardIndex = new ArrayList<>();
                    cardIndex.add(i);
                    cardIndex.add(i + 1);
                    promptCards.add(cardIndex);
                }
            }
        }
        return hasSameTypeMatch;
    }

    private static boolean promptDan(List<Card> myCards, List<List<Integer>> promptCards, int prevGrade, int mySize, int lastGrade, boolean hasSameTypeMatch) {
        for (int i = 0; i < mySize; i++) {
            if (myCards.get(i).grade > prevGrade && myCards.get(i).grade != lastGrade) {
                lastGrade = myCards.get(i).grade;
                hasSameTypeMatch = true;

                List<Integer> cardIndex = new ArrayList<>();
                cardIndex.add(i);
                promptCards.add(cardIndex);
            }
        }
        return hasSameTypeMatch;
    }

    private static boolean promptFeiji(List<Card> myCards, List<Card> prevCards, List<List<Integer>> promptCards, int mySize, int prevSize, boolean hasSameTypeMatch) {
        for (int i = 0; i <= mySize - prevSize; i++) {
            List<Card> cards = new ArrayList<Card>();
            List<Integer> indexList = new ArrayList<>();

            int gradeEqualTimes = 0;
            int lastGrade = 0;
            for (int j = 0; j < mySize - i; j++) {
                if (myCards.get(i + j).grade != lastGrade) {
                    lastGrade = myCards.get(i + j).grade;
                    gradeEqualTimes = 0;
                } else {
                    gradeEqualTimes++;
                }
                // 连续出现4张牌值相等的，跳过这张牌
                if (gradeEqualTimes > 2) continue;

                cards.add(new Card(myCards.get(i + j).value));
                indexList.add(i + j);
                if (cards.size() == prevSize) break;
            }

            int myCardType = getCardType(cards);
            if (myCardType == CARD_TYPE_FEIJI) {
                int myGrade = cards.get(cards.size() - 1).grade;
                int otherGrade = prevCards.get(prevCards.size() - 1).grade;

                if (myGrade > otherGrade) {
                    hasSameTypeMatch = true;
                    promptCards.add(indexList);
                }
            }
        }
        return hasSameTypeMatch;
    }

    private static boolean promptFeijiDaiYi(List<Card> myCards, List<Card> prevCards, List<List<Integer>> promptCards, int mySize, int prevSize, boolean hasSameTypeMatch) {
        int danpaiNum = prevSize / 4;

        for (int i = 0; i <= mySize - prevSize; i++) {
            List<Card> cards = new ArrayList<Card>();
            List<Integer> indexList = new ArrayList<>();

            int gradeEqualTimes = 0;
            int lastGrade = 0;
            for (int j = 0; j < mySize - i; j++) {
                if (myCards.get(i + j).grade != lastGrade) {
                    lastGrade = myCards.get(i + j).grade;
                    gradeEqualTimes = 0;
                } else {
                    gradeEqualTimes++;
                }
                // 连续出现4张牌值相等的，跳过这张牌
                if (gradeEqualTimes > 2) continue;

                cards.add(new Card(myCards.get(i + j).value));
                indexList.add(i + j);
                if (cards.size() == prevSize - danpaiNum) break;
            }

            int myCardType = getCardType(cards);
            if (myCardType == CARD_TYPE_FEIJI) {
                int myGrade = cards.get(cards.size() - 1).grade;
                int otherGrade = prevCards.get(prevCards.size() - 1).grade;

                if (myGrade > otherGrade) {
                    for (int k = 0; k < mySize; k++) {
                        int danpaiGrade = myCards.get(k).grade;
                        boolean hasNoZhadan = true;

                        for (Card card: cards) {
                            if (card.grade == danpaiGrade) {
                                hasNoZhadan = false;
                                break;
                            }
                        }
                        if (hasNoZhadan) {
                            hasSameTypeMatch = true;
                            indexList.add(k);
                            promptCards.add(indexList);
                        }
                    }
                }
            }
        }
        return hasSameTypeMatch;
    }

    private static boolean promptFeijiDaiDui(List<Card> myCards, List<Card> prevCards, List<List<Integer>> promptCards, int mySize, int prevSize, boolean hasSameTypeMatch) {
        int duiziNum = prevSize / 5;

        for (int i = 0; i <= mySize - prevSize; i++) {
            List<Card> cards = new ArrayList<Card>();
            List<Integer> indexList = new ArrayList<>();

            int gradeEqualTimes = 0;
            int lastGrade = 0;
            for (int j = 0; j < mySize - i; j++) {
                if (myCards.get(i + j).grade != lastGrade) {
                    lastGrade = myCards.get(i + j).grade;
                    gradeEqualTimes = 0;
                } else {
                    gradeEqualTimes++;
                }
                // 连续出现4张牌值相等的，跳过这张牌
                if (gradeEqualTimes > 2) continue;

                cards.add(new Card(myCards.get(i + j).value));
                indexList.add(i + j);
                if (cards.size() == prevSize - duiziNum) break;
            }

            int myCardType = getCardType(cards);
            if (myCardType == CARD_TYPE_FEIJI) {
                int myGrade = cards.get(cards.size() - 1).grade;
                int otherGrade = prevCards.get(prevCards.size() - 1).grade;

                if (myGrade > otherGrade) {
                    for (int k = 0; k < mySize - 1; k++) {
                        int grade1 = myCards.get(k).grade;
                        int grade2 = myCards.get(k + 1).grade;
                        if (grade1 != grade2) continue;

                        boolean hasNoZhadan = true;

                        for (Card card: cards) {
                            if (card.grade == grade1) {
                                hasNoZhadan = false;
                                break;
                            }
                        }
                        if (hasNoZhadan) {
                            hasSameTypeMatch = true;
                            indexList.add(k);
                            indexList.add(k + 1);
                            promptCards.add(indexList);
                        }
                    }
                }
            }
        }
        return hasSameTypeMatch;
    }

    /**
     * 找出一组排好序的飞机牌中出现的第1组三张相同的牌,的第一个index
     *
     * @param cards
     * @return
     */
    private static int getFirstSanzhangIndex(List<Card> cards) {
        for (int i = 0; i + 2 < cards.size(); i++) {
            int grade1 = cards.get(i).grade;
            int grade2 = cards.get(i + 1).grade;
            int grade3 = cards.get(i + 2).grade;
            if ((grade1 == grade2) && (grade2 == grade3)) {
                return i;
            }
        }
        return -1;
    }


    /**
     * 判断牌是否为单
     *
     * @param myCards 牌的集合
     * @return 如果为单，返回true；否则，返回false。
     */
    public static boolean isDan(List<Card> myCards) {
        // 默认不是单
        boolean flag = false;
        if (myCards != null && myCards.size() == 1) {
            flag = true;
        }
        return flag;
    }


    /**
     * 判断牌是否为对子
     *
     * @param myCards 牌的集合
     * @return 如果为对子，返回true；否则，返回false。
     */
    public static boolean isDuiZi(List<Card> myCards) {
        // 默认不是对子
        boolean flag = false;

        if (myCards != null && myCards.size() == 2) {

            int grade1 = myCards.get(0).grade;
            int grade2 = myCards.get(1).grade;
            if (grade1 == grade2) {
                flag = true;
            }
        }

        return flag;

    }

    /**
     * 判断牌是否为3带1
     *
     * @param myCards 牌的集合
     * @return 如果为3带1，被带牌的位置，0或3，否则返回-1。炸弹返回-1。
     */
    public static int isSanDaiYi(List<Card> myCards) {
        int flag = -1;
        // 默认不是3带1
        if (myCards != null && myCards.size() == 4) {
            // 对牌进行排序
            sortCards(myCards);

            int[] grades = new int[4];
            grades[0] = myCards.get(0).grade;
            grades[1] = myCards.get(1).grade;
            grades[2] = myCards.get(2).grade;
            grades[3] = myCards.get(3).grade;

            // 炸弹不为3带1
            if ((grades[1] == grades[0]) && (grades[2] == grades[0])
                    && (grades[3] == grades[0])) {
                return -1;
            }
            // 3带1，被带的牌在牌头
            else if ((grades[1] == grades[0] && grades[2] == grades[0])) {
                return 0;
            }
            // 3带1，被带的牌在牌尾
            else if (grades[1] == grades[3] && grades[2] == grades[3]) {
                return 3;
            }
        }
        return flag;
    }

    /**
     * 判断牌是否为3带2
     *
     * @param myCards 牌的集合
     * @return
     */
    public static boolean isSanDaiEr(List<Card> myCards) {
        boolean flag = false;
        // 默认不是3带2
        if (myCards != null && myCards.size() == 5) {
            // 对牌进行排序
            sortCards(myCards);

            int[] grades = new int[5];
            grades[0] = myCards.get(0).grade;
            grades[1] = myCards.get(1).grade;
            grades[2] = myCards.get(2).grade;
            grades[3] = myCards.get(3).grade;
            grades[4] = myCards.get(4).grade;

            // 4带1排除
            if (((grades[1] == grades[0]) && (grades[2] == grades[0])
                    && (grades[3] == grades[0])) ||
                    ((grades[1] == grades[2]) && (grades[2] == grades[3])
                            && (grades[3] == grades[4]))) {
                flag = false;
            }
            // 3带2，被带的牌在牌头
            else if ((grades[1] == grades[0] && grades[2] == grades[0])) {
                if (grades[3] == grades[4]) {
                    flag = true;
                }
            }
            // 3带2，被带的牌在牌尾
            else if (grades[2] == grades[3] && grades[3] == grades[4]) {
                if (grades[0] == grades[1]) {
                    flag = true;
                }
            }
        }
        return flag;
    }

    /**
     * 判断牌是否为3不带
     *
     * @param myCards 牌的集合
     * @return 如果为3不带，返回true；否则，返回false。
     */
    public static boolean isSanBuDai(List<Card> myCards) {
        // 默认不是3不带
        boolean flag = false;

        if (myCards != null && myCards.size() == 3) {
            int grade0 = myCards.get(0).grade;
            int grade1 = myCards.get(1).grade;
            int grade2 = myCards.get(2).grade;

            if (grade0 == grade1 && grade2 == grade0) {
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 判断牌是否为顺子
     *
     * @param myCards 牌的集合
     * @return 如果为顺子，返回true；否则，返回false。
     */
    public static boolean isShunZi(List<Card> myCards) {
        // 默认是顺子
        boolean flag = true;

        if (myCards != null) {

            int size = myCards.size();
            // 顺子牌的个数在5到12之间
            if (size < 5 || size > 12) {
                return false;
            }

            // 对牌进行排序
            sortCards(myCards);

            for (int n = 0; n < size - 1; n++) {
                int prev = myCards.get(n).grade;
                int next = myCards.get(n + 1).grade;
                // 小王、大王、2不能加入顺子
                if (prev == 15 || prev == 14 || prev == 13 || next == 15
                        || next == 14 || next == 13) {
                    flag = false;
                    break;
                } else {
                    if (prev - next != -1) {
                        flag = false;
                        break;
                    }
                }
            }
        }
        return flag;
    }

    /**
     * 判断牌是否为炸弹
     *
     * @param myCards 牌的集合
     * @return 如果为炸弹，返回true；否则，返回false。
     */
    public static boolean isZhaDan(List<Card> myCards) {
        // 默认不是炸弹
        boolean flag = false;
        if (myCards != null && myCards.size() == 4) {

            int[] grades = new int[4];
            grades[0] = myCards.get(0).grade;
            grades[1] = myCards.get(1).grade;
            grades[2] = myCards.get(2).grade;
            grades[3] = myCards.get(3).grade;
            if ((grades[1] == grades[0]) && (grades[2] == grades[0])
                    && (grades[3] == grades[0])) {
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 判断牌是否为4带2
     *
     * @param myCards 牌的集合
     * @return 如果为4带2，返回true；否则，返回false。
     */
    public static boolean isSiDaiEr(List<Card> myCards) {
        boolean flag = false;
        if (myCards != null && myCards.size() == 6) {

            // 对牌进行排序
            sortCards(myCards);
            for (int i = 0; i < 3; i++) {
                int grade1 = myCards.get(i).grade;
                int grade2 = myCards.get(i + 1).grade;
                int grade3 = myCards.get(i + 2).grade;
                int grade4 = myCards.get(i + 3).grade;

                if (grade2 == grade1 && grade3 == grade1 && grade4 == grade1) {
                    flag = true;
                }
            }
        }
        return flag;
    }

    /**
     * 判断是否为4带2对
     *
     * @param myCards
     * @return
     */
    public static boolean isSiDaiDui(List<Card> myCards) {
        boolean flag = false;
        if (myCards != null && myCards.size() == 8) {

            // 对牌进行排序
            sortCards(myCards);
            List<Card> duiziCards = new ArrayList<>();
            List<Card> sizhangCards = new ArrayList<>();
            boolean hasSizhang = false;

            for (int i = 0; i < 5; i++) {
                int grade1 = myCards.get(i).grade;
                int grade2 = myCards.get(i + 1).grade;
                int grade3 = myCards.get(i + 2).grade;
                int grade4 = myCards.get(i + 3).grade;

                if (grade2 == grade1 && grade3 == grade1 && grade4 == grade1) {
                    hasSizhang = true;
                    sizhangCards.add(myCards.get(i));
                    sizhangCards.add(myCards.get(i + 1));
                    sizhangCards.add(myCards.get(i + 2));
                    sizhangCards.add(myCards.get(i + 3));
                }
            }

            if (hasSizhang) {
                for (Card card : myCards) {
                    if (!sizhangCards.contains(card)) {
                        duiziCards.add(card);
                    }
                }
                if (isAllDuizi(duiziCards, 2)) {
                    flag = true;
                }
            }
        }
        return flag;
    }

    /**
     * 判断牌是否为王炸
     *
     * @param myCards 牌的集合
     * @return 如果为王炸，返回true；否则，返回false。
     */
    public static boolean isDuiWang(List<Card> myCards) {
        // 默认不是对王
        boolean flag = false;

        if (myCards != null && myCards.size() == 2) {

            int gradeOne = myCards.get(0).grade;
            int gradeTwo = myCards.get(1).grade;

            // 只有小王和大王的等级之和才可能是29
            if (gradeOne + gradeTwo == 29) {
                flag = true;
            }
        }
        return flag;
    }

    /**
     * 判断牌是否为连对
     *
     * @param myCards 牌的集合
     * @return 如果为连对，返回true；否则，返回false。
     */
    public static boolean isLianDui(List<Card> myCards) {
        // 默认是连对
        boolean flag = true;
        if (myCards == null) {
            return false;
        }

        int size = myCards.size();
        if (size < 6 || size % 2 != 0) {
            flag = false;
        } else {
            // 对牌进行排序
            sortCards(myCards);
            for (int i = 0; i < size; i = i + 2) {
                if (myCards.get(i).grade != myCards.get(i + 1).grade) {
                    flag = false;
                    break;
                }

                if (i < size - 2) {
                    if (myCards.get(i).grade - myCards.get(i + 2).grade != -1) {
                        flag = false;
                        break;
                    }
                }
            }

            if (myCards.get(0).grade == 13) flag = false; // 2不能算入
        }

        return flag;
    }

    /**
     * 判断牌是否为飞机带1张
     *
     * @param myCards 牌的集合
     * @return 如果为飞机带，返回true；否则，返回false。
     */
    public static boolean isFeiJiDai(List<Card> myCards) {
        int size = myCards.size();
        if (size < 8 || size % 4 != 0) return false;

        sortCards(myCards);
        if (isContainSizhang(myCards)) return false; // 包含四张相同牌时不属于飞机

        int n = size / 4;
        for (int i = 0; i + 2 < size; i++) {
            int grade1 = myCards.get(i).grade;
            int grade2 = myCards.get(i + 1).grade;
            int grade3 = myCards.get(i + 2).grade;
            if (grade1 == grade2 && grade3 == grade1) {

                ArrayList<Card> cards = new ArrayList<Card>();
                for (int j = i; j < i + 3 * n; j++) {// 取字串
                    if (j > myCards.size() - 1) break;
                    cards.add(myCards.get(j));
                }
                boolean hasFeijiBudai = isFeiJiBuDai(cards);
                if (hasFeijiBudai) {
                    if (myCards.size() - cards.size() == n) {
                        return true;
                    }
                }
            }

        }

        return false;
    }

    /**
     * 判断牌是否为飞机带1对
     *
     * @param myCards 牌的集合
     * @return 如果为飞机带，返回true；否则，返回false。
     */
    public static boolean isFeiJiDaiDui(List<Card> myCards) {
        int size = myCards.size();
        if (size < 10 || size % 5 != 0) return false;

        sortCards(myCards);
        if (isContainSizhang(myCards)) return false; // 包含四张相同牌时不属于飞机

        int n = size / 5;
        int i = 0;
        for (i = 0; i + 2 < size; i++) {
            int grade1 = myCards.get(i).grade;
            int grade2 = myCards.get(i + 1).grade;
            int grade3 = myCards.get(i + 2).grade;
            if (grade1 == grade2 && grade3 == grade1) {

                ArrayList<Card> sanzhangCards = new ArrayList<Card>();
                for (int j = i; j < i + 3 * n; j++) {// 取字串
                    if (j > myCards.size() - 1) break;
                    sanzhangCards.add(myCards.get(j));
                }

                ArrayList<Card> duiziCards = new ArrayList<>();
                for (int k = 0; k < myCards.size(); k++) {
                    Card card = myCards.get(k);
                    if (!sanzhangCards.contains(card)) {
                        duiziCards.add(card);
                    }
                }
                return isFeiJiBuDai(sanzhangCards) && isAllDuizi(duiziCards, n);
            }

        }

        return false;
    }

    /**
     * 判断是否都是对子,且对子数量匹配
     *
     * @param myCards
     * @return
     */
    public static boolean isAllDuizi(List<Card> myCards, int size) {
        if (myCards.size() % 2 != 0) return false;

        int duiziNum = 0;
        sortCards(myCards);

        if (isContainSizhang(myCards)) return false; // 四张牌都一样时不属于对子

        for (int i = 0; i + 1 < myCards.size(); i = i + 2) {
            int grade1 = myCards.get(i).grade;
            int grade2 = myCards.get(i + 1).grade;

            if (grade1 == grade2) {
                duiziNum++;
            }
        }

        return duiziNum == size;
    }

    /**
     * 判断牌是否为飞机不带
     *
     * @param myCards 牌的集合
     * @return 如果为飞机不带，返回true；否则，返回false。
     */
    public static boolean isFeiJiBuDai(List<Card> myCards) {
        if (myCards == null) {
            return false;
        }

        int size = myCards.size();
        if (size < 6 || size % 3 != 0) return false;

        sortCards(myCards);
        int n = size / 3;

        int[] grades = new int[n];

        if (size % 3 != 0) {
            return false;
        } else {
            if (isContainSizhang(myCards)) return false; // 包含四张相同牌时不属于飞机

            for (int i = 0; i < n; i++) {
                if (!isSanBuDai(myCards.subList(i * 3, i * 3 + 3))) {
                    return false;
                } else {
                    // 如果连续的3张牌是一样的，记录其中一张牌的grade
                    grades[i] = myCards.get(i * 3).grade;
                }
            }
        }

        for (int i = 0; i < n - 1; i++) {
            if (grades[i] == 13) {// 不允许出现2
                return false;
            }

            if (grades[i + 1] - grades[i] != 1) {
                return false;// grade必须连续,如 333444
            }
        }

        return true;
    }

    private static boolean isContainSizhang(List<Card> myCards) {
        if (myCards.size() < 4) return false;

        for (int i = 0; i < myCards.size() - 3; i++) {
            int grade1 = myCards.get(i).grade;
            int grade2 = myCards.get(i + 1).grade;
            int grade3 = myCards.get(i + 2).grade;
            int grade4 = myCards.get(i + 3).grade;
            if ((grade1 == grade2) && (grade2 == grade3) && (grade3 == grade4)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 待出的牌排序，主要针对3带1、四带2、飞机这种牌，将带的牌排在后面
     * @param cards
     * @return
     */
    public static List<Card> getSortedPutCards(List<Card> cards) {
        int cardType = CardTypeManager.getCardType(cards);
        sortCards(cards);
        Collections.reverse(cards);
        int size = cards.size();

        if (cardType == CARD_TYPE_SANDAIYI) {
            sortSanZhangPutCards(cards, 1);
        } else if (cardType == CARD_TYPE_SANDAIER) {
            sortSanZhangPutCards(cards, 2);
        } else if (cardType == CARD_TYPE_SIDAIER) {
            sortSiZhangPutCards(cards, 2);
        } else if (cardType == CARD_TYPE_SIDAIDUI) {
            sortSiZhangPutCards(cards, 4);
        } else if (cardType == CARD_TYPE_FEIJIDAI) {
            sortSanZhangPutCards(cards, size / 4);
        } else if (cardType == CARD_TYPE_FEIJIDAIDUI) {
            sortSanZhangPutCards(cards, size / 5 * 2);
        }

        return cards;
    }

    private static void sortSiZhangPutCards(List<Card> cards, int daipaiMaxIndex) {
        List<Card> needMoveCards = new ArrayList<>();
        for (int i = 0; i < daipaiMaxIndex; i++) {
            int grade1 = cards.get(i).grade;
            int grade2 = cards.get(i + 1).grade;
            int grade3 = cards.get(i + 2).grade;
            int grade4 = cards.get(i + 3).grade;

            if (!(grade1 == grade2 && grade2 == grade3 && grade3 == grade4)) {
                needMoveCards.add(cards.get(i));
            } else {
                if (i == 0) return; // 已经排好序了
            }
        }
        cards.removeAll(needMoveCards);
        cards.addAll(needMoveCards);
    }

    private static void sortSanZhangPutCards(List<Card> cards, int daipaiMaxIndex) {
        List<Card> needMoveCards = new ArrayList<>();
        for (int i = 0; i < daipaiMaxIndex; i++) {
            int grade1 = cards.get(i).grade;
            int grade2 = cards.get(i + 1).grade;
            int grade3 = cards.get(i + 2).grade;

            if (!(grade1 == grade2 && grade2 == grade3)) {
                needMoveCards.add(cards.get(i));
            } else {
                if (i == 0) return; // 已经排好序了
            }
        }
        cards.removeAll(needMoveCards);
        cards.addAll(needMoveCards);
    }

    public static class CardSelectResult {
        public List<Card> selectCards;
        public int range;
    }

//    public static void main(String [] args) {
//        byte [] cards = {0x06, 0x16, 0x26, 0x07, 0x17, 0x27, 0x08, 0x18, 0x28, 0x04, 0x14, 0x15, 0x05, 0x23, 0x19};
//
//        System.out.print(CardTypeManager.getCardType(CardTypeManager.parseFromBytes(cards)));
////        System.out.print(CardManager.canMatchPrevCard(CardManager.parseFromBytes(cards), CardManager.parseFromBytes(prevCards), CardManager.getCardType(CardManager.parseFromBytes(prevCards))));
//    }
}

