package com.huiwan.landlord.presenter;

import android.animation.ObjectAnimator;
import android.graphics.Rect;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Handler;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;

import com.huiwan.base.util.ApplicationUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.landlord.i.ILandlordPresenter;
import com.huiwan.landlord.manage.CardAnimUtil;
import com.huiwan.landlord.manage.CardSelectHelper;
import com.huiwan.landlord.manage.CardTypeManager;
import com.huiwan.landlord.manage.LLGameEffectManager;
import com.huiwan.landlord.model.Card;
import com.huiwan.landlord.model.LLGameOverData;
import com.huiwan.landlord.model.LLGameState;
import com.huiwan.landlord.view.CardView;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.UserSimpleInfoCallback;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.landlord.view.LandLordGameView;


import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class LandLordPresenter extends BaseSocialGamePresenter implements ILandlordPresenter {
    private MediaPlayer bgmPlayer;
    private LandLordGameView view;
    private List<CardView> cardViewsToPut = new ArrayList<>();  // 准备出的牌
    private List<CardView> myHandCardViewList = new ArrayList<>(); // 手牌
    private List<CardView> myAlreadyPutCardViewList = new ArrayList<>(); // 已经出了的牌
    private List<Integer> selectedIndexList = new ArrayList<>();
    private int currentGameState = -1;

    private List<ObjectAnimator> cardPopAnimatorList = new ArrayList<>();
    private List<ObjectAnimator> cardReleaseAnimatorList = new ArrayList<>();
    private Rect cardRect = new Rect();
    private int selectStartIndex = -1;
    private int upDistance = ScreenUtil.dip2px(8);
    private boolean canSelectCard = true;
    private boolean isSelfHosted = false;
    private List<List<Card>> promptCardList = new ArrayList<>();
    private Handler mHandler = new Handler();
    private int multiple;
    private int winRole;
    private List<LandLordPackets.LLGameCheckout> checkoutList = new ArrayList<>();
    private List<LandLordPackets.LLRoleType> roleTypeList = new ArrayList<>();
    private byte[] bottomCards;
    private int selfUid;
    private int prevUid;
    private int nextUid;
    private FixRoomInfo roomInfo;
    private boolean isSelfLastTurnSeated; // 自己是否上一次房间信息更新前是坐着的，用来判断自己是否站起了
    private LLGameOverData gameOverData;
    private boolean isUserPuttingCard;
    private int lastEmptyNum;
    private float scale = 1;

    public boolean isWatcher() {
        return isWatcher;
    }

    private boolean isWatcher;

    public void setAnimingChangeSeat(boolean animingChangeSeat) {
        isAnimingChangeSeat = animingChangeSeat;
    }

    private boolean isAnimingChangeSeat;

    public LandLordPresenter(final LandLordGameView view, int selfUid) {
        this.view = view;
        setSelfUid(selfUid);
        EventBus.getDefault().register(this);
        scale = ScreenUtil.isHdpiDevice(view.getContext()) ? 0.85f : 1.0f;
        upDistance *= scale;
        IceSoundUtil.getInstance();
    }

    private void startBgm() {
        if (bgmPlayer != null) return;
        File bgFile = new File(LLGameEffectManager.RES_FILE_PATH_PREFIX + File.separator + "soundEffect" + File.separator + "landlord_bgm.mp3");
        if (!bgFile.exists()) return;

        bgmPlayer = MediaPlayer.create(WPApplication.getInstance(), Uri.fromFile(bgFile));
        if (bgmPlayer == null) return;
        bgmPlayer.setLooping(true);
        if (ApplicationUtil.isActivityAtTop(FixRoomActivity.class)) {
            bgmPlayer.start();
        }
    }

    public void setSelfUid(int selfUid) {
        this.selfUid = selfUid;
    }

    public int getPrevUid() {
        return prevUid;
    }

    public int getNextUid() {
        return nextUid;
    }

    public void destroy() {
        EventBus.getDefault().unregister(this);
        LLGameEffectManager.clearAnimation();

        if (bgmPlayer != null) {
            bgmPlayer.stop();
            bgmPlayer.release();
            bgmPlayer = null;
        }

        mHandler.removeCallbacksAndMessages(null);
    }

    public void pauseBgm() {
        if (bgmPlayer != null && bgmPlayer.isPlaying()) {
            bgmPlayer.pause();
        }
    }

    public void resumeBgm() {
        if (bgmPlayer != null && !bgmPlayer.isPlaying()) {
            bgmPlayer.start();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onStartBgm(AgoraJoinChannelSuccessEvent event) {
        startBgm();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onGameNotExist(LLGameNotExistEvent event) {
        handleGameNotExist();
    }

    @Override
    public void syncGame(boolean bindSync) {
        LLPacketSender.getInstance().syncGame(new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {}

            @Override
            public void onFail(RspHeadInfo head) {
                if (head.code == LandLordPackets.LLRspErrCode.CODE_GAME_NOT_FOUND_VALUE) {
                    handleGameNotExist();
                } else {
                    ToastUtil.show(head.desc);
                }
            }
        });
    }

    private void handleGameNotExist() {
        ToastUtil.show("游戏不存在或已结束");
        currentGameState = -1;
        view.clearGameScene();
        if (roomInfo != null) updateRoomInfo(roomInfo);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onGameCancel(LLGameCancelEvent event) {
        currentGameState = LandLordPackets.LLGameState.STATE_GAME_END_VALUE;
        ToastUtil.show("游戏取消");
        view.clearGameScene();
    }

    public void onPushGameMsg(List<FixRoomMsg> array) {
        if (array.size() == 0) return;

        ArrayList<ChatMsg> chatMsgs = new ArrayList<>();
        int size = array.size();
        for (int i = 0; i < size; i++) {
            FixRoomMsg msg = array.get(i);
            if (msg.getRid() == roomInfo.getRid() && (msg.getMediaType() == ChatMsg.MEDIA_TYPE_TEXT ||
                    msg.getMediaType() == ChatMsg.MEDIA_TYPE_GIFT)) {
                chatMsgs.add(msg);
            }
        }

        if (chatMsgs.size() > 0) {
            String prevMsg = null;
            String selfMsg = null;
            String nextMsg = null;

            boolean isHotWordMsg = false;
            for (ChatMsg chatMsg : chatMsgs) {
                if (chatMsg.getMediaType() == ChatMsg.MEDIA_TYPE_TEXT) {
                    isHotWordMsg = chatMsg.getSubType() == ChatMsg.SUBTYPE_HOT_WORDS;

                    if (chatMsg.getSend_uid() == prevUid) {
                        prevMsg = chatMsg.getContent();
                    } else if (chatMsg.getSend_uid() == nextUid) {
                        nextMsg = chatMsg.getContent();
                    } else if (chatMsg.getSend_uid() == selfUid) {
                        selfMsg = chatMsg.getContent();
                    } else {
                        isHotWordMsg = false;
                        handleShowBarrage(chatMsg);
                    }
                } else {
                    handleShowBarrage(chatMsg);
                }
            }
            if (prevMsg != null) view.showPrevChatMsg(prevMsg, isHotWordMsg);
            if (nextMsg != null) view.showNextChatMsg(nextMsg, isHotWordMsg);
            if (selfMsg != null) view.showMyChatMsg(selfMsg, isHotWordMsg);
        }
    }

    public void onSendMsg(FixRoomMsg msg) {
        if (roomInfo.isInSit(LoginHelper.getLoginUid())) {
            view.showMyChatMsg(msg.getContent(), msg.getSubType() == ChatMsg.SUBTYPE_HOT_WORDS);
        } else {
            handleShowBarrage(msg);
        }
    }

    private void handleShowBarrage(final ChatMsg msg) {
        final boolean isGiftMsg = msg.getMediaType() == ChatMsg.MEDIA_TYPE_GIFT;
        if (isGiftMsg) {
            UserService.get().getCacheSimpleUser(msg.getGiftRecvUid(), new UserSimpleInfoCallback() {
                @Override
                public void onUserInfoSuccess(final UserSimpleInfo recUser) {
                    UserService.get().getCacheSimpleUser(msg.getSend_uid(), new UserSimpleInfoCallback() {
                        @Override
                        public void onUserInfoSuccess(UserSimpleInfo sendUser) {
                            Gift gift = CommonUtil.getGift(msg.getGiftId());
                            if (gift == null || recUser == null || sendUser == null) return;
                            String text = sendUser.getNickname()
                                    + "送给" + recUser.getNickname() +
                                    msg.getGiftNum() + "个"
                                    + gift.getName();
                            String desc = msg.getGiftDesc();
                            if (!TextUtils.isEmpty(desc)) {
                                text = text + "，收礼人获得" + desc;
                            }
                            view.showBarrageView(text, BarrageAnimView.TYPE_GIFT, sendUser.uid);
                        }

                        @Override
                        public void onUserInfoFailed(String description) {

                        }
                    });
                }

                @Override
                public void onUserInfoFailed(String description) {
                }
            });
            return;
        }

        UserService.get().getCacheSimpleUser(msg.getSend_uid(), new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo userInfo) {
                String nick = "";
                if (userInfo != null) nick = userInfo.getRemarkName();
                if (!TextUtils.isEmpty(nick)) nick = nick + "：";

                String content = nick + msg.getContent();
                view.showBarrageView(content, BarrageAnimView.TYPE_DEFAULT, msg.getSend_uid());
            }

            @Override
            public void onUserInfoFailed(String description) {
                view.showBarrageView(msg.getContent(), BarrageAnimView.TYPE_DEFAULT, msg.getSend_uid());
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUserReady(LLUserReadyEvent event) {
        ready();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUserDoudouUpdate(RefreshSelfSuccessEvent event) {
        view.updateSelfDoudouNum();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSyncGame(LLSyncGameEvent event) {
        final LLGameState gameState = event.gameState;

        int state = gameState.gameState.getNumber();
        final List<LandLordPackets.LLGamerInfo> gamerInfos = gameState.gamerInfos;
        final LandLordPackets.LLGamerInfo selfGamer = getSelfGamer(gamerInfos);
        final LandLordPackets.LLGamerInfo prevGamer = getPrevGamer(gamerInfos);
        final LandLordPackets.LLGamerInfo nextGamer = getNextGamer(gamerInfos);
        if (selfGamer == null || nextGamer == null || prevGamer == null) return;

        final byte[] myHandCards = selfGamer.getHandcards().toByteArray();
        final byte[] prevHandCards = prevGamer.getHandcards().toByteArray();
        final byte[] nextHandCards = nextGamer.getHandcards().toByteArray();
        bottomCards = gameState.bottomCards;
        final int currentGamer = gameState.currentGamer;
        boolean isPrevHost = prevGamer.getInTrust();
        boolean isNextHost = nextGamer.getInTrust();
        this.isSelfHosted = selfGamer.getInTrust();
        this.multiple = (int) selfGamer.getMultiple();
        this.checkoutList.clear();
        this.checkoutList.addAll(gameState.checkout);
        final int actionUid = gameState.actionUid;
        final int stateChangeCause = gameState.stateChangeCause;
        final long timeLeft = gameState.timeLeft;
        boolean isGameStart = false;
        boolean isLandLordCardDisplay = false;
        final boolean isHostAction = (stateChangeCause == LandLordPushPackets.LandlordsPush.LLPushType.CANCEL_TRUST_VALUE
                || stateChangeCause == LandLordPushPackets.LandlordsPush.LLPushType.TRUST_VALUE);

        currentGameState = state;

        roleTypeList.clear();
        view.hideTicketLay();
        view.hidePrepareBtn();
        view.hideGameOverDialog();
        view.hideFinishPutCardImv();
        view.updateHandCardNum(prevHandCards.length, nextHandCards.length);
        if (isWatcher) {
            view.updateWatcherHandCardNum(myHandCards.length);
        }
        if (isSelfHosted) {
            showCardCannotSelectLayer();
        } else {
            hideCardCannotSelectLayer();
        }
        view.updateHostStatus(isSelfHosted, isPrevHost, isNextHost);
        promptCardList.clear();
        promptCardList.addAll(gameState.promptCards);
        if (state != LandLordPackets.LLGameState.STATE_PUT_CARDS_VALUE &&
                state != LandLordPackets.LLGameState.STATE_GAME_END_VALUE && state != LandLordPackets.LLGameState.STATE_DOUBLE_VALUE) {
            view.showBottomCardBack();
            view.hideLandLordMark();
        }
        if (gameState.isMultiple && state != LandLordPackets.LLGameState.STATE_GAME_END_VALUE &&
                stateChangeCause == LandLordPushPackets.LandlordsPush.LLPushType.PUT_CARDS_VALUE) {
            long animDelayTime = getAnimDelayTime(actionUid, gamerInfos);
            view.showMultipleAnim(animDelayTime, multiple);
        } else {
            view.updateMultipleNum(multiple);
        }

        // 更新角色信息
        if (state == LandLordPackets.LLGameState.STATE_DOUBLE_VALUE || state == LandLordPackets.LLGameState.STATE_PUT_CARDS_VALUE
                || state == LandLordPackets.LLGameState.STATE_GAME_END_VALUE) {
            updateRoleTypeList(gamerInfos);
        }
        // 更新底牌
        if (state == LandLordPackets.LLGameState.STATE_DOUBLE_VALUE || state == LandLordPackets.LLGameState.STATE_PUT_CARDS_VALUE) {
            view.updateBottomCard(bottomCards);
        }
        if (state != LandLordPackets.LLGameState.STATE_DOUBLE_VALUE) {
            view.hideDoubleBtn();
        }
        if (state != LandLordPackets.LLGameState.STATE_CALL_LORD_VALUE && state != LandLordPackets.LLGameState.STATE_GRAB_LORD_VALUE) {
            view.hideCallLordBtn();
        }
        if (state != LandLordPackets.LLGameState.STATE_PUT_CARDS_VALUE && state != LandLordPackets.LLGameState.STATE_GAME_END_VALUE) {
            clearMyPutCardView();
            view.clearOthersPutCardView();
        }

        // 游戏开始
        if (state == LandLordPackets.LLGameState.STATE_GAME_START_VALUE) {
            isGameStart = true;
            view.clearAllCallLordAction();
            view.hideAllAction();
        }
        // 叫地主阶段
        else if (state == LandLordPackets.LLGameState.STATE_CALL_LORD_VALUE) {
            handleCallLord(actionUid, currentGamer, false, gamerInfos, false,
                    timeLeft, isHostAction, false);
        }
        // 抢地主阶段
        else if (state == LandLordPackets.LLGameState.STATE_GRAB_LORD_VALUE) {
            handleCallLord(actionUid, currentGamer, true, gamerInfos, false,
                    timeLeft, isHostAction, false);
        }
        // 三人都不叫，流转到下一局
        else if (state == LandLordPackets.LLGameState.STATE_GAME_PRE_RESTART_VALUE) {
            handleCallLord(actionUid, currentGamer, true, gamerInfos, false,
                    timeLeft, isHostAction, true);
        }
        // 加倍阶段
        else if (state == LandLordPackets.LLGameState.STATE_DOUBLE_VALUE) {
            if (!isSelfHosted) canSelectCard = true;
            int landLordIndex = getLandLordIndex();
            boolean isLastCallLordAction = stateChangeCause == LandLordPushPackets.LandlordsPush.LLPushType.CALL_LOAD_VALUE;
            if (isLastCallLordAction) {
                view.showLandLordLogoAnim(landLordIndex);
                if (isLandLordRole(0)) {
                    isLandLordCardDisplay = true;
                }
                handleCallLord(actionUid, currentGamer, true, gamerInfos, true, -1,
                        false, false);

                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        handleDouble(selfGamer, prevGamer, nextGamer, actionUid, timeLeft, stateChangeCause);
                    }
                }, 200);
            } else {
                view.updateLandLordLogo(landLordIndex);
                handleDouble(selfGamer, prevGamer, nextGamer, actionUid, timeLeft, stateChangeCause);
            }
        }
        // 出牌阶段
        else if (state == LandLordPackets.LLGameState.STATE_PUT_CARDS_VALUE) {
            if (!isSelfHosted) canSelectCard = true;
            boolean isLastDoubleAction = stateChangeCause == LandLordPushPackets.LandlordsPush.LLPushType.DOUBLE_VALUE;

            int landLordIndex = getLandLordIndex();
            view.updateLandLordLogo(landLordIndex);

            if (isLastDoubleAction) {
                handleDouble(selfGamer, prevGamer, nextGamer, actionUid, timeLeft, stateChangeCause);

                view.hideAllAction();
                handlePutCard(selfUid, prevUid, nextUid, gamerInfos, myHandCards,
                        currentGamer, isSelfHosted, prevHandCards.length, nextHandCards.length,
                        timeLeft - 1500, actionUid, isHostAction);
            } else {
                handlePutCard(selfUid, prevUid, nextUid, gamerInfos, myHandCards, currentGamer,
                        isSelfHosted, prevHandCards.length, nextHandCards.length, timeLeft,
                        actionUid, isHostAction);
            }
        }
        // 游戏结束
        else if (state == LandLordPackets.LLGameState.STATE_GAME_END_VALUE) {
            winRole = gameState.winRole;
            boolean isCurrentActionUser = !isHostAction;

            if (actionUid == selfUid) {
                if (isWatcher) {
                    byte[] myPutCards = getSelfGamer(gamerInfos).getLastPutCards().toByteArray();
                    view.showMyPutCard(myPutCards, getSelfGamer(gamerInfos).getLastIsPass(), isCurrentActionUser);
                } else {
                    if (isSelfHosted) {
                        onSelfHostPutCard(gamerInfos, false, true, isCurrentActionUser);
                    }
                }
            } else {
                view.hideActionBtn();
                byte[] prevPutCards = prevGamer.getLastPutCards().toByteArray();
                byte[] nextPutCards = nextGamer.getLastPutCards().toByteArray();
                byte[] myPutCards = selfGamer.getLastPutCards().toByteArray();
                if (isPrevGamer(gamerInfos, actionUid)) {
                    view.onPrevUserPutCard(prevUid, prevPutCards, nextPutCards,false, nextGamer.getLastIsPass(),
                            false, isSelfHosted, true, timeLeft, isCurrentActionUser);
                } else {
                    view.onNextUserPutCard(nextUid, nextPutCards, myPutCards,
                            false, selfGamer.getLastIsPass(), true, timeLeft, isCurrentActionUser);
                }
            }

            // 先播放最后一手出牌，延迟展示结束界面
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    boolean isChuntian = false;
                    boolean isFanChuntian = false;
                    if (gameState.isSpring) {
                        if (winRole == LandLordPackets.LLRoleType.LANDLORD_VALUE) {
                            isChuntian = true;
                        } else {
                            isFanChuntian = true;
                        }
                    }
                    view.showGameEndPutCard(prevHandCards, nextHandCards, isChuntian, isFanChuntian);
                }
            }, 300);

            updateGameOverData(gamerInfos);
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    if (gameOverData != null) {
                        view.showGameEndDialog();
                        gameOverData = null;
                    }
                    view.clearGameScene();
                }
            }, 5300);
        }

        if (!isWatcher && isMyCardUpdated(myHandCards)) {
            cardViewsToPut.clear();
            view.updateMyHandCard(myHandCards, isGameStart, isLandLordCardDisplay);
        }
    }

    private void handleDouble(LandLordPackets.LLGamerInfo selfGamer, LandLordPackets.LLGamerInfo prevGamer, LandLordPackets.LLGamerInfo nextGamer, int actionUid, long timeLeft, int stateChangeCause) {
        boolean isSelfActioned = selfGamer.getDoubleTypeValue() != LandLordPackets.LLDoubleType.DT_UNDEFINED_VALUE;
        boolean isSelfDouble = selfGamer.getDoubleTypeValue() == LandLordPackets.LLDoubleType.DT_YES_VALUE;
        boolean isNextActioned = nextGamer.getDoubleTypeValue() != LandLordPackets.LLDoubleType.DT_UNDEFINED_VALUE;
        boolean isNextDouble = nextGamer.getDoubleTypeValue() == LandLordPackets.LLDoubleType.DT_YES_VALUE;
        boolean isPrevActioned = prevGamer.getDoubleTypeValue() != LandLordPackets.LLDoubleType.DT_UNDEFINED_VALUE;
        boolean isPrevDouble = prevGamer.getDoubleTypeValue() == LandLordPackets.LLDoubleType.DT_YES_VALUE;

        boolean isCurrentSelfAction = actionUid == selfUid && stateChangeCause == LandLordPushPackets.LandlordsPush.LLPushType.DOUBLE_VALUE;
        boolean isCurrentPrevAction = actionUid == prevUid && stateChangeCause == LandLordPushPackets.LandlordsPush.LLPushType.DOUBLE_VALUE;
        boolean isCurrentNextAction = actionUid == nextUid && stateChangeCause == LandLordPushPackets.LandlordsPush.LLPushType.DOUBLE_VALUE;

        view.updateDoubleStatus(isSelfActioned, isSelfDouble, isNextActioned, isNextDouble,
                isPrevActioned, isPrevDouble, timeLeft, isCurrentSelfAction, isCurrentNextAction, isCurrentPrevAction);
    }

    private long getAnimDelayTime(int actionUid, List<LandLordPackets.LLGamerInfo> gamerInfos) {
        LandLordPackets.LLGamerInfo multipleActionGamerInfo = null;
        for (LandLordPackets.LLGamerInfo gamerInfo : gamerInfos) {
            if (gamerInfo.getUid() == actionUid) {
                multipleActionGamerInfo = gamerInfo;
                break;
            }
        }
        if (multipleActionGamerInfo == null) return 0;

        List<Card> actionCards = CardTypeManager.parseFromBytes(multipleActionGamerInfo.getLastPutCards().toByteArray());
        int cardType = CardTypeManager.getCardType(actionCards);
        if (cardType == CardTypeManager.CARD_TYPE_ZHADAN) {
            return 1000;
        } else{
            return 2250;
        }
    }

    private int getLandLordIndex() {
        for (int i = 0; i < roleTypeList.size(); i++) {
            LandLordPackets.LLRoleType roleType = roleTypeList.get(i);
            if (roleType.getNumber() == LandLordPackets.LLRoleType.LANDLORD_VALUE) {
                return i;
            }
        }
        return -1;
    }

    private void updateRoleTypeList(List<LandLordPackets.LLGamerInfo> gamerInfos) {
        roleTypeList.clear();
        roleTypeList.add(getSelfGamer(gamerInfos).getRole());
        roleTypeList.add(getNextGamer(gamerInfos).getRole());
        roleTypeList.add(getPrevGamer(gamerInfos).getRole());
    }

    public boolean isBottomCard(byte value) {
        for (byte cardValue : bottomCards) {
            if (value == cardValue) return true;
        }
        return false;
    }

    private void onSelfHostPutCard(final List<LandLordPackets.LLGamerInfo> gamerInfos, final boolean isPass, final boolean isGameEnd, final boolean isCurrentActionUser) {
        for (CardView cardView : cardViewsToPut) {
            cardView.downCard(false);
        }
        byte[] myPutCards = getSelfGamer(gamerInfos).getLastPutCards().toByteArray();
        byte[] prevPutCards = getPrevGamer(gamerInfos).getLastPutCards().toByteArray();
        showPutCardAnim(myPutCards, true);
        if (myPutCards.length > 0) {
            view.checkShowMyWinAlarm(selfUid, myHandCardViewList.size());
        }
        view.onUserPutCard(selfUid, myPutCards, isPass, isGameEnd, isCurrentActionUser);
        view.showPrevPutCard(prevPutCards, getPrevGamer(gamerInfos).getLastIsPass());

        promptCardList.clear();
    }

    private void handlePutCard(int selfUid, int prevUid, int nextUid, List<LandLordPackets.LLGamerInfo> gamerInfos, byte[] myCards,
                               int currentGamer, boolean isSelfHost, int prevHandCardSize, int nextHandCardSize, long timeLeft, int actionUid, boolean isHostAction) {
        byte[] prevPutCards = getPrevGamer(gamerInfos).getLastPutCards().toByteArray();
        byte[] nextPutCards = getNextGamer(gamerInfos).getLastPutCards().toByteArray();
        byte[] myPutCards = getSelfGamer(gamerInfos).getLastPutCards().toByteArray();
        boolean isPrevPass = getPrevGamer(gamerInfos).getLastIsPass();
        boolean isNextPass = getNextGamer(gamerInfos).getLastIsPass();
        boolean isSelfPass = getSelfGamer(gamerInfos).getLastIsPass();
        if (currentGamer == selfUid) {
            boolean isCurrentActionUser = (actionUid == prevUid && !isHostAction);
            view.onPrevUserPutCard(prevUid, prevPutCards, nextPutCards, isPrevPass, isNextPass,
                    myCards.length == 20, isSelfHost, false, timeLeft, isCurrentActionUser);
            if (!isPrevPass) {
                view.checkShowPrevWinAlarm(prevUid, prevHandCardSize);
            }

            clearMyPutCardView();
        } else {
            view.hideActionBtn();
            if (isPrevGamer(gamerInfos, currentGamer)) {
                boolean isCurrentActionUser = (actionUid == nextUid && !isHostAction);
                view.onNextUserPutCard(nextUid, nextPutCards, myPutCards, getNextGamer(gamerInfos).getLastIsPass(),
                        isSelfPass, false, timeLeft, isCurrentActionUser);
                if (!isNextPass) {
                    view.checkShowNextWinAlarm(nextUid, nextHandCardSize);
                }
            } else {
                boolean isCurrentActionUser = (actionUid == selfUid && !isHostAction);
                if (isSelfHost && isCurrentActionUser) {
                    onSelfHostPutCard(gamerInfos, isSelfPass, false, isCurrentActionUser);
                }
                if (!isUserPuttingCard && !isWatcher) {
                    view.showMyPutCard(myPutCards, isSelfPass, isCurrentActionUser);
                }

                view.showPrevPutCard(prevPutCards, isPrevPass);
                view.updateNextPlayerTimer(timeLeft);
                if (isWatcher) {
                    view.showMyPutCard(myPutCards, isSelfPass, isCurrentActionUser);
                    if (!isSelfPass) {
                        view.checkShowMyWinAlarm(selfUid, getSelfGamer(gamerInfos).getHandcards().toByteArray().length);
                    }
                }
            }
        }
    }

    public void clearMyPutCardView() {
        for (CardView cardView : myAlreadyPutCardViewList) {
            cardView.setVisibility(View.GONE);
        }
        myAlreadyPutCardViewList.clear();
    }

    private boolean isMyCardUpdated(byte[] serverCards) {
        byte[] localCards = getCardsValueFromView(myHandCardViewList);

        List<Card> localCardList = CardTypeManager.parseSorted(localCards);
        List<Card> serverCardList = CardTypeManager.parseSorted(serverCards);

        if (localCardList.size() == serverCardList.size()) {
            for (int i = 0; i < localCardList.size(); i++) {
                if (localCardList.get(i).value != serverCardList.get(i).value) {
                    return true;
                }
            }
            return false;
        }
        return true;
    }

    private byte[] getCardsValueFromView(List<CardView> viewList) {
        byte[] cards = new byte[viewList.size()];
        for (int i = 0; i < viewList.size(); i++) {
            cards[i] = viewList.get(i).getValue();
        }
        return cards;
    }

    private void handleCallLord(int actionUid, int currentGamer, boolean isCurrentStateGrab,
                                List<LandLordPackets.LLGamerInfo> gamerInfos, boolean isLandLordConfirmed,
                                long timeLeft, boolean isCancelHostAction, boolean isRestart) {
        view.hideAllAction();

        int prevCallType = getPrevGamer(gamerInfos).getLastCallTypeValue();
        int nextCallType = getNextGamer(gamerInfos).getLastCallTypeValue();
        int myCallType = getSelfGamer(gamerInfos).getLastCallTypeValue();
        if (isRestart) {
            prevCallType = LandLordPackets.LLCallLordType.CT_CALL_GIVE_UP_VALUE;
            nextCallType = prevCallType;
            myCallType = prevCallType;
        }
        showCallLordAction(prevCallType, getPrevGamer(gamerInfos).getUid(), actionUid, isCancelHostAction);
        showCallLordAction(nextCallType, getNextGamer(gamerInfos).getUid(), actionUid, isCancelHostAction);
        showCallLordAction(myCallType, getSelfGamer(gamerInfos).getUid(), actionUid, isCancelHostAction);
        if (isRestart) return;

        if (currentGamer == selfUid) {
            if (!isLandLordConfirmed) {
                view.showCallLordBtn(isCurrentStateGrab, timeLeft);
            }
        } else {
            view.hideCallLordBtn();
            if (isPrevGamer(gamerInfos, currentGamer)) {
                // 地主已经确定时，不要再隐藏叫地主View
                if (!isLandLordConfirmed) {
                    view.hidePrevAction(timeLeft);
                }
            } else {
                if (!isLandLordConfirmed) {
                    view.hideNextAction(timeLeft);
                }
            }
        }
    }

    private void showCallLordAction(int callType, int uid, int actionUid, boolean isCancelHostAction) {
        if (callType != LandLordPackets.LLCallLordType.CT_UNDEFINED_VALUE) {
            boolean isUserGrab = false;
            if (callType == LandLordPackets.LLCallLordType.CT_GRAB_LORD_VALUE ||
                    callType == LandLordPackets.LLCallLordType.CT_GRAB_GIVE_UP_VALUE) {
                isUserGrab = true;
            }
            boolean isUserWant = false;
            if (callType == LandLordPackets.LLCallLordType.CT_CALL_LORD_VALUE ||
                    callType == LandLordPackets.LLCallLordType.CT_GRAB_LORD_VALUE) {
                isUserWant = true;
            }

            if (uid == selfUid) {
                view.showMyCallLordAction(uid, isUserGrab, isUserWant, actionUid == selfUid && !isCancelHostAction);
            } else if (uid == prevUid) {
                view.showPrevCallLordAction(prevUid, isUserWant, isUserGrab, actionUid == prevUid && !isCancelHostAction);
            } else {
                view.showNextCallLordAction(nextUid, isUserWant, isUserGrab, actionUid == nextUid && !isCancelHostAction);
            }
        }
    }

    private boolean isPrevGamer(List<LandLordPackets.LLGamerInfo> gamerInfos, int currentGamer) {
        return getPrevGamer(gamerInfos).getUid() == currentGamer;
    }

    private LandLordPackets.LLGamerInfo getPrevGamer(List<LandLordPackets.LLGamerInfo> gamerInfos) {
        for (LandLordPackets.LLGamerInfo gamerInfo : gamerInfos) {
            if (gamerInfo.getUid() == prevUid) {
                return gamerInfo;
            }
        }
        return null;
    }

    private LandLordPackets.LLGamerInfo getNextGamer(List<LandLordPackets.LLGamerInfo> gamerInfos) {
        for (LandLordPackets.LLGamerInfo gamerInfo : gamerInfos) {
            if (gamerInfo.getUid() == nextUid) {
                return gamerInfo;
            }
        }
        return null;
    }

    private LandLordPackets.LLGamerInfo getSelfGamer(List<LandLordPackets.LLGamerInfo> gamerInfos) {
        for (LandLordPackets.LLGamerInfo gamerInfo : gamerInfos) {
            if (gamerInfo.getUid() == selfUid) {
                return gamerInfo;
            }
        }
        return null;
    }

    public void callLord() {
        int callState = currentGameState == LandLordPackets.LLGameState.STATE_CALL_LORD_VALUE ?
                LandLordPackets.LLCallLordType.CT_CALL_LORD_VALUE : LandLordPackets.LLCallLordType.CT_GRAB_LORD_VALUE;
        LLPacketSender.getInstance().callLord(LandLordPackets.LLCallLordType.forNumber(callState), new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public void noCallLord() {
        int callState = currentGameState == LandLordPackets.LLGameState.STATE_CALL_LORD_VALUE ?
                LandLordPackets.LLCallLordType.CT_CALL_GIVE_UP_VALUE : LandLordPackets.LLCallLordType.CT_GRAB_GIVE_UP_VALUE;
        LLPacketSender.getInstance().callLord(LandLordPackets.LLCallLordType.forNumber(callState), new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public void pass() {
        LLPacketSender.getInstance().pass(new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                view.onUserPutCard(selfUid, new byte[0], true, false, true);
                if (cardViewsToPut.size() > 0) {
                    downAllPreparedCards(cardViewsToPut, new CardAnimUtil.AnimEndCallback() {
                        @Override
                        public void onAnimEnd() {
                            cardViewsToPut.clear();
                        }
                    });
                }
                promptCardList.clear();

                isUserPuttingCard = true;
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        isUserPuttingCard = false;
                    }
                }, 200);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
                if (cardViewsToPut.size() > 0) {
                    downAllPreparedCards(cardViewsToPut, new CardAnimUtil.AnimEndCallback() {
                        @Override
                        public void onAnimEnd() {
                            cardViewsToPut.clear();
                        }
                    });
                }
                promptCardList.clear();
            }
        });
    }

    public void cancelHost() {
        LLPacketSender.getInstance().cancelHost(new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                isSelfHosted = false;
                hideCardCannotSelectLayer();
                view.hideCancelHost();
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public void putCard() {
        final byte[] cards = new byte[cardViewsToPut.size()];
        for (int i = 0; i < cardViewsToPut.size(); i++) {
            cards[i] = cardViewsToPut.get(i).getValue();
        }
        LLPacketSender.getInstance().putCard(cards, new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                showPutCardAnim(cards, false);

                view.checkShowMyWinAlarm(selfUid, myHandCardViewList.size());
                view.onUserPutCard(selfUid, cards, false, false, true);
                promptCardList.clear();
            }

            @Override
            public void onFail(RspHeadInfo head) {
                if (head.code == LandLordPackets.LLRspErrCode.CODE_PUT_CARDS_ERR_VALUE) {
                    view.showPutCardError();
                    downAllPreparedCards(cardViewsToPut, new CardAnimUtil.AnimEndCallback() {
                        @Override
                        public void onAnimEnd() {
                            cardViewsToPut.clear();
                        }
                    });
                } else {
                    ToastUtil.show(head.desc);
                    if (cardViewsToPut.size() > 0) {
                        downAllPreparedCards(cardViewsToPut, new CardAnimUtil.AnimEndCallback() {
                            @Override
                            public void onAnimEnd() {
                                cardViewsToPut.clear();
                            }
                        });
                    }
                }
            }
        });
    }

    public void doubleGame(final boolean isDouble) {
        LandLordPackets.LLDoubleType doubleType = isDouble ? LandLordPackets.LLDoubleType
                .forNumber(LandLordPackets.LLDoubleType.DT_YES_VALUE) : LandLordPackets.LLDoubleType
                .forNumber(LandLordPackets.LLDoubleType.DT_NO_VALUE);
        LLPacketSender.getInstance().doubleGame(doubleType, new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                if (isDouble) {
                    view.showMultipleAnim(0, multiple * 2);
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private void showPutCardAnim(byte[] cards, boolean isSelfHosted) {
        isUserPuttingCard = true;
        CardAnimUtil.showPutCardAnim(cards, isSelfHosted, myHandCardViewList, cardViewsToPut,
                view, myAlreadyPutCardViewList);
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                isUserPuttingCard = false;
            }
        }, 200);
    }

    public void removeCardFromPut(CardView view) {
        cardViewsToPut.remove(view);
    }

    public void addCardForPut(CardView view) {
        if (!cardViewsToPut.contains(view)) {
            cardViewsToPut.add(view);
        }
    }

    public void clearMyHandCardViews() {
        myHandCardViewList.clear();
    }

    public void addMyCardView(CardView cardView) {
        myHandCardViewList.add(cardView);
    }

    public void showCardCannotSelectLayer() {
        for (CardView cardView : myHandCardViewList) {
            cardView.showBlackLayer();
        }
        canSelectCard = false;
    }

    public void hideCardCannotSelectLayer() {
        if (isSelfHosted) return;

        for (CardView cardView : myHandCardViewList) {
            cardView.hideBlackLayer();
        }
        canSelectCard = true;
    }

    public void doPrompt() {
        if (!canSelectCard) return; // 防止快速点击提示牌的位置不对
        if (promptCardList.size() > 0) {
            if (cardViewsToPut.size() > 0) {
                downAllPreparedCards(cardViewsToPut, new CardAnimUtil.AnimEndCallback() {
                    @Override
                    public void onAnimEnd() {
                        cardViewsToPut.clear();
                        upPromptCard();
                    }
                });
            } else {
                upPromptCard();
            }
        }
    }

    private void upPromptCard() {
        if (promptCardList.size() == 0) return;
        List<CardView> promptCardView = pollPromptCardsOnce();
        upSelectedCards(promptCardView);
    }

    private List<CardView> pollPromptCardsOnce() {
        List<CardView> promptCardView = new ArrayList<>();
        List<Card> promptCards = promptCardList.remove(0);
        promptCardList.add(promptCards);

        for (Card card : promptCards) {
            for (CardView cardView : myHandCardViewList) {
                if (cardView.getValue() == card.value) {
                    promptCardView.add(cardView);
                    break;
                }
            }
        }
        return promptCardView;
    }

    public boolean canMatch() {
        return promptCardList.size() > 0;
    }

    public void addHasPutCardView(CardView cardView) {
        myAlreadyPutCardViewList.add(cardView);
    }

    public LLGameOverData getGameOverData() {
        return gameOverData;
    }

    public void updateGameOverData(List<LandLordPackets.LLGamerInfo> gamerInfos) {
        gameOverData = new LLGameOverData();
        gameOverData.gamerInfos = gamerInfos;
        gameOverData.winRole = winRole;

        List<Integer> uidList = new ArrayList<>();
        uidList.add(selfUid);
        uidList.add(nextUid);
        uidList.add(prevUid);
        List<LandLordPackets.LLRoleType> roleTypeList = new ArrayList<>(this.roleTypeList);

        // 农民排在一起，保证list中第二个是农民
        if (roleTypeList.get(1).getNumber() != LandLordPackets.LLRoleType.FARMER_VALUE) {
            LandLordPackets.LLRoleType roleType = roleTypeList.remove(1);
            roleTypeList.add(roleType);
            uidList.add(uidList.remove(1));
        }

        List<LandLordPackets.LLGameCheckout> orderedCheckoutList = new ArrayList<>();
        for (Integer uid : uidList) {
            for (LandLordPackets.LLGameCheckout checkout : checkoutList) {
                if (checkout.getUid() == uid) {
                    orderedCheckoutList.add(checkout);
                }
            }
        }

        gameOverData.checkoutList = orderedCheckoutList;
        gameOverData.roleTypeList = roleTypeList;
        gameOverData.isSelfWin = winRole == roleTypeList.get(0).getNumber();
        gameOverData.points = ConfigHelper.getInstance().getLandlordBetlevelConfig()
                .get(roomInfo.getBet_level() - 1).base_score;
        gameOverData.isWatcher = isWatcher;
    }

    public void delayDownBottomCards() {
        canSelectCard = false;
        view.hidePutCardBtn();
        final List<CardView> bottomCardViews = new ArrayList<>();
        for (CardView cardView : myHandCardViewList) {
            if (isBottomCard(cardView.getValue())) {
                bottomCardViews.add(cardView);
            }
        }
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                downLandlordCards(bottomCardViews, ScreenUtil.dip2px(12 * scale));
                canSelectCard = true;
            }
        }, 2400);
    }

    public boolean isLandLordRole(int index) {
        if (roleTypeList == null || roleTypeList.size() < 3) return false;
        return roleTypeList.get(index).getNumber() == LandLordPackets.LLRoleType.LANDLORD_VALUE;
    }

    private void selectSingleCard(int index) {
        CardView cardView = myHandCardViewList.get(index);
        cardView.setSelect(true);

        if (!selectedIndexList.contains(index)) selectedIndexList.add(index);
    }

    private int getTouchedCardIndex(MotionEvent event) {
        for (int i = myHandCardViewList.size() - 1; i >= 0; i--) {
            CardView cardView = myHandCardViewList.get(i);

            cardView.getGlobalVisibleRect(cardRect);
            if (cardRect.contains(((int) event.getRawX()), (int) event.getRawY())) {
                return i;
            }
        }
        return -1;
    }

    private void selectMultiCardsWithIndex(int selectStartIndex, int selectEndIndex) {
        selectedIndexList.clear();
        if (selectStartIndex > selectEndIndex) {
            for (int i = selectEndIndex; i <= selectStartIndex; i++) {
                selectedIndexList.add(i);
            }
        } else if (selectStartIndex < selectEndIndex) {
            for (int i = selectStartIndex; i <= selectEndIndex; i++) {
                selectedIndexList.add(i);
            }
        } else {
            selectedIndexList.add(selectEndIndex);
        }

        for (int i = 0; i < myHandCardViewList.size(); i++) {
            CardView cardView = myHandCardViewList.get(i);
            cardView.setSelect(selectedIndexList.contains(i));
        }
    }

    public boolean onTouchDown(MotionEvent e) {
        if (!canSelectCard || isWatcher) return false;

        int index = getTouchedCardIndex(e);
        if (index > -1) {
            selectStartIndex = index;
            selectSingleCard(index);
        } else {
            if (cardViewsToPut.size() > 0) {
                downAllPreparedCards(cardViewsToPut, new CardAnimUtil.AnimEndCallback() {
                    @Override
                    public void onAnimEnd() {
                        cardViewsToPut.clear();
                    }
                });
            }
        }
        return false;
    }

    public void onScroll(MotionEvent currEvent) {
        int index = getTouchedCardIndex(currEvent);
        if (index > -1 && selectStartIndex > -1) {
            selectMultiCardsWithIndex(selectStartIndex, index);
        }
    }

    public void onTouchUp(MotionEvent event) {
        if (selectStartIndex > -1) {
            finishSelectCards();
        }
        selectStartIndex = -1;
    }

    private void finishSelectCards() {
        for (CardView cardView : myHandCardViewList) {
            cardView.setSelect(false);
        }

        reverseSelectedCardsState(selectedIndexList);
        selectedIndexList.clear();
    }

    private void reverseSelectedCardsState(List<Integer> selectedIndexList) {
        List<CardView> upCards = new ArrayList<>();
        List<CardView> downCards = new ArrayList<>();

        for (Integer index : selectedIndexList) {
            if (index > myHandCardViewList.size() - 1) continue;
            CardView cardView = myHandCardViewList.get(index);
            if (cardView.isUp()) {
                downCards.add(cardView);
            } else {
                upCards.add(cardView);
            }
        }

        upSelectedCards(upCards);
        downSelectedCards(downCards);
    }

    private void downAllPreparedCards(List<CardView> cardViews, CardAnimUtil.AnimEndCallback callback) {
        CardAnimUtil.animSelectedCards(cardViews, cardReleaseAnimatorList, false, false, upDistance, false, callback);
    }

    private void upSelectedCards(List<CardView> selectedCardViews) {
        List<Card> selectedCards = CardTypeManager.parseFromBytes(getCardsValueFromView(selectedCardViews));
        List<CardView> upCardViews = new ArrayList<>(selectedCardViews);

        upCardViews = CardSelectHelper.handleSmartSelectCard(selectedCardViews, upCardViews,
                myHandCardViewList, cardViewsToPut, selectedCards, promptCardList);
        CardAnimUtil.animSelectedCards(upCardViews, cardPopAnimatorList, true, true, upDistance, false, null);
    }

    private void downSelectedCards(List<CardView> cardViews) {
        CardAnimUtil.animSelectedCards(cardViews, cardReleaseAnimatorList, false, true, upDistance, false, null);
    }

    private void downLandlordCards(List<CardView> cardViews, int distance) {
        CardAnimUtil.animSelectedCards(cardViews, cardReleaseAnimatorList, false, true, distance, true, null);
    }

    public void setRoomInfo(FixRoomInfo roomInfo) {
        this.roomInfo = roomInfo;

        updateSeatUid(roomInfo);
    }

    private void updateSeatUid(FixRoomInfo roomInfo) {
        int size = roomInfo.getSeats().size();
        int emptyNum = 0;
        if (roomInfo.getSeats().get(0).getUid() == 0) emptyNum++;
        if (roomInfo.getSeats().get(1).getUid() == 0) emptyNum++;
        if (roomInfo.getSeats().get(2).getUid() == 0) emptyNum++;

        // 座位已经坐满，且自己处于围观状态
        if (emptyNum == 0 && !roomInfo.isInSit(LoginHelper.getLoginUid())) {
            isWatcher = true;

            if (prevUid != 0 && nextUid != 0) {
                for (FixSeatInfo seatInfo : roomInfo.getSeats()) {
                    int uid = seatInfo.getUid();
                    if (uid != prevUid && uid != nextUid) {
                        selfUid = uid;
                        break;
                    }
                }
            } else {
                selfUid = roomInfo.getSeats().get(0).getUid();
                nextUid = roomInfo.getSeats().get(1).getUid();
                prevUid = roomInfo.getSeats().get(2).getUid();
            }
        } else {
            // 之前因为座位坐满了处于围观状态，此时selfUid需要重新置为自己
            if (isWatcher) {
                setSelfUid(LoginHelper.getLoginUid());
            }
            isWatcher = false;
            for (int i = 0; i < size; i++) {
                int currUid = roomInfo.getSeats().get(i).getUid();
                int anchorUid = 0;
                if (roomInfo.isInSit(selfUid)) {
                    anchorUid = selfUid;
                } else {
                    if (emptyNum == 2) {
                        // 由于自己站起导致空位由一个变成两个，不要改变其他人的座位顺序
                        if (isSelfLastTurnSeated && lastEmptyNum == 1) {
                            break;
                        } else {
                            nextUid = 0;
                            if (currUid > 0) {
                                prevUid = currUid;
                                break;
                            }
                        }
                    } else if (emptyNum == 3) {
                        prevUid = 0;
                        nextUid = 0;
                        break;
                    }
                }

                if (currUid == anchorUid) {
                    if (i == 0) {
                        prevUid = roomInfo.getSeats().get(2).getUid();
                        nextUid = roomInfo.getSeats().get(1).getUid();
                    } else if (i == 2) {
                        prevUid = roomInfo.getSeats().get(1).getUid();
                        nextUid = roomInfo.getSeats().get(0).getUid();
                    } else {
                        prevUid = roomInfo.getSeats().get(0).getUid();
                        nextUid = roomInfo.getSeats().get(2).getUid();
                    }
                }
            }
            lastEmptyNum = emptyNum;
            isSelfLastTurnSeated = roomInfo.isInSit(selfUid);
        }
    }

    public String getRoomName() {
        return roomInfo.getName();
    }

    public void updateUserInfo() {
        // 游戏开始时、换座动画播放时不刷新UserInfo
        if (isAnimingChangeSeat) return;

        if (isPrevSeated()) {
            FixSeatInfo prevSeatInfo = roomInfo.getSeatInfo(prevUid);
            view.updatePrevUserInfo(prevUid, roomInfo.isReady(prevUid), prevSeatInfo.getBean_count());
        } else {
            view.showPrevUserEmptySeat();
        }
        if (isNextSeated()) {
            FixSeatInfo nextSeatInfo = roomInfo.getSeatInfo(nextUid);
            view.updateNextUserInfo(nextUid, roomInfo.isReady(nextUid), nextSeatInfo.getBean_count());
        } else {
            view.showNextUserEmptySeat();
        }
        if (!isSelfSeated()) {
            view.showMyEmptySeat();
        } else {
            FixSeatInfo selfSeatInfo = roomInfo.getSeatInfo(selfUid);
            view.updateSelfUserInfo(selfUid, roomInfo.isReady(selfUid), selfSeatInfo.getBean_count());
        }
    }

    public void ready() {
        FixRoomLogic.fixReadyReq(roomInfo.getRid(), false, new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                view.finishReady();
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public void unready() {
        FixRoomLogic.fixUnreadyReq(roomInfo.getRid(), false, new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                view.finishUnReady();
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public String getLevelName() {
        return GameConfig.getLevelName(roomInfo.getBet_level()) + "（" + roomInfo.getRoom_level() + "级房）>";
    }

    @Override
    protected void updateRoomInfoInternal(FixRoomInfo roomInfo) {
        setRoomInfo(roomInfo);

        updateUserInfo();
        view.updateBottomView(roomInfo);

        view.updateRoomInfoView(isGaming());
    }

    public void standUp() {
        FixRoomPacketSender.fixUnseatReq(roomInfo.getRid(), new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public boolean isPrevSeated() {
        return prevUid != 0;
    }

    public boolean isNextSeated() {
        return nextUid != 0;
    }

    public void sitPrev() {
        changeSeat(true);
    }

    public void sitNext() {
        changeSeat(false);
    }

    private void changeSeat(final boolean changeToPrev) {
        if (isAnimingChangeSeat) return;
        int seatNum = 0;
        List<FixSeatInfo> seatInfos = roomInfo.getSeats();
        int seatedUid = changeToPrev ? nextUid : prevUid;
        for (int i = 0; i < seatInfos.size(); i++) {
            FixSeatInfo seatInfo = seatInfos.get(i);
            if (seatInfo.getUid() == seatedUid) {
                if (changeToPrev) {
                    if (i == seatInfos.size() - 1) {
                        seatNum = 1;
                    } else {
                        seatNum = i + 2;
                    }
                } else {
                    if (i == 0) {
                        seatNum = seatInfos.size();
                    } else {
                        seatNum = i;
                    }
                }
                break;
            }
        }

        FixRoomPacketSender.fixSitReq(roomInfo.getRid(), seatNum, new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                isAnimingChangeSeat = true;
                if (changeToPrev) {
                    view.showSitToPrevAnim(selfUid);
                } else {
                    view.showSitToNextAnim(selfUid);
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public void sitLast() {
        int emptySeat = 0;
        for (int i = 0; i < roomInfo.getSeats().size(); i++) {
            if (roomInfo.getSeats().get(i).getUid() == 0) {
                emptySeat = i + 1;
                break;
            }
        }

        FixRoomPacketSender.fixSitReq(roomInfo.getRid(), emptySeat, new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public boolean isPrevReady() {
        return roomInfo.isReady(prevUid);
    }

    public boolean isNextReady() {
        return roomInfo.isReady(nextUid);
    }

    public int getSelfUid() {
        return selfUid;
    }

    public boolean isSelfReady() {
        return roomInfo.isReady(selfUid);
    }

    @Override
    public boolean isGaming() {
        if (currentGameState == -1) {
            return roomInfo.isGameing();
        } else {
            if (currentGameState == LandLordPackets.LLGameState.STATE_GAME_END_VALUE && isWatcher) {
                return roomInfo.isGameing();
            }
            return currentGameState != LandLordPackets.LLGameState.STATE_GAME_END_VALUE
                    && currentGameState != LandLordPackets.LLGameState.STATE_UNDEFINED_VALUE;
        }
    }

    public int getRoomId() {
        return roomInfo.getRid();
    }

    public List<CardView> getMyHandCardViews() {
        return myHandCardViewList;
    }

    public String getTicketNum() {
        return String.valueOf(ConfigHelper.getInstance().getLandlordBetlevelConfig()
                .get(roomInfo.getBet_level() - 1).ticket);
    }

    public int getMostWin() {
        return ConfigHelper.getInstance().getLandlordBetlevelConfig()
                .get(roomInfo.getBet_level() - 1).limit;
    }

    public int getBaseScore() {
        return ConfigHelper.getInstance().getLandlordBetlevelConfig()
                .get(roomInfo.getBet_level() - 1).base_score;
    }

    public FixSeatInfo getPrevSeatInfo() {
        return roomInfo.getSeatInfo(prevUid);
    }

    public FixSeatInfo getNextSeatInfo() {
        return roomInfo.getSeatInfo(nextUid);
    }

    public FixSeatInfo getSelfSeatInfo() {
        return roomInfo.getSeatInfo(selfUid);
    }

    public byte[] getMyHandCardBytes() {
        return getCardsValueFromView(myHandCardViewList);
    }

}
