package com.huiwan.landlord.presenter;

import android.os.Handler;
import android.view.View;

import com.huiwan.base.util.ToastUtil;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserInfoLoadCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.User;import com.wepie.lib.api.plugins.voice.VoiceConfig;
import com.wepie.wespy.config.UrlConfig;
import com.wepie.wespy.model.entity.fixroom.FixRoomInfo;
import com.wepie.wespy.model.event.FreeTalkPushActionEvent;
import com.huiwan.landlord.view.LLBottomView;
import com.wepie.wespy.module.media.VoiceConfigHelper;
import com.wepie.wespy.module.media.VoiceManager;
import com.wepie.wespy.module.spy.SocialGameMsgCallback;
import com.wepie.wespy.net.tcp.sender.FixRoomPacketSender;
import com.wepie.wespy.net.tcp.sender.LLPacketSender;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class LLBottomControlPresenter {
    private final LLBottomView view;
    private FixRoomInfo roomInfo;
    private int lastFreeTalkState = -1;
    private Handler handler = new Handler();

    public boolean isFreeTalkOn() {
        return isFreeTalkOn;
    }

    private boolean isFreeTalkOn;
    private SocialGameMsgCallback callback;

    public LLBottomControlPresenter(LLBottomView llBottomView) {
        this.view = llBottomView;
        EventBus.getDefault().register(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPushFreeTalk(FreeTalkPushActionEvent event) {
        final int action = event.actionType;
        final int uid = event.uid;
        if (!roomInfo.isInSit(uid)) return;

        UserService.get().getCacheUser(uid, new UserInfoLoadCallback() {
            @Override
            public void onUserInfoSuccess(User userInfo) {
                if (action == FreeTalkPushActionEvent.APPLY) {
                    if (uid == LoginHelper.getLoginUid() || !isSelfInSit()) return;

                    view.showFreeTalkApplyDialog(userInfo.nickname);
                } else if (action == FreeTalkPushActionEvent.DISAGREE) {
                    if (uid == LoginHelper.getLoginUid()) return;
                    ToastUtil.show(userInfo.nickname + "拒绝了自由发言");
                    view.hideApplyFreeTalkDialog();
                } else {
                    if (uid == LoginHelper.getLoginUid()) return;
                    ToastUtil.show(userInfo.nickname + "关闭了自由发言");
                }
            }

            @Override
            public void onUserInfoFailed(String description) {
            }
        });
    }

    public void sendMsg(final String word, int voice_id) {
        if (callback != null) callback.onSendMsg(word, voice_id);

        view.hideHotWordsLay();
        view.hideKeyboard();
    }

    public void toggleFreeTalk() {
        if (!roomInfo.isFreeTalkOn()) {
            FixRoomPacketSender.fixApplyFreeTalkReq(roomInfo.getRid(), new SeqCallback() {
                @Override
                public void onSuccess(RspHeadInfo head) {
                    ToastUtil.show("你已申请房间自由发言功能");
                }

                @Override
                public void onFail(RspHeadInfo head) {
                    ToastUtil.show(head.desc);
                }
            });
        } else {
            if (!roomInfo.isGameing()) return;

            view.showFreeTalkCloseDialog(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    FixRoomPacketSender.fixCloseFreeTalkReq(roomInfo.getRid(), new SeqCallback() {
                        @Override
                        public void onSuccess(RspHeadInfo head) {
                            ToastUtil.show("房间自由发言已关闭");
                        }

                        @Override
                        public void onFail(RspHeadInfo head) {
                            ToastUtil.show(head.desc);
                        }
                    });
                }
            });
        }
    }

    public void toggleAudio() {
        if (!isFreeTalkOn()) return;

        if (VoiceManager.getInstance().isAgoraOn()) {
            VoiceManager.getInstance().setAgroaOn(false);
        } else {
            VoiceManager.getInstance().setAgroaOn(true);
        }
        if (VoiceManager.getInstance().isAgoraOn()) {
            VoiceManager.getInstance().muteAllRemoteAudioStreams(false);
            openMicIfMicOnAndInSit();
        } else {
            muteAudioAndMic();
        }

        view.updateAudioBtn(VoiceManager.getInstance().isAgoraOn(), isFreeTalkOn);
        view.updateMicBtn(VoiceManager.getInstance().isAgoraOn()
                && VoiceManager.getInstance().isMicOn() && isSelfInSit(), isFreeTalkOn);
    }

    private void muteAudioAndMic() {
        VoiceManager.getInstance().muteAllRemoteAudioStreams(true);
        VoiceManager.getInstance().closeMic();
    }

    public void toggleMic() {
        if (!isSelfInSit() || !isFreeTalkOn()) return;

        if (VoiceManager.getInstance().isMicOn()) {
            VoiceManager.getInstance().setMicOn(false);
        } else {
            VoiceManager.getInstance().setMicOn(true);
        }
        VoiceManager.getInstance().updateMic();

        view.updateMicBtn(VoiceManager.getInstance().isAgoraOn() && VoiceManager.getInstance().isMicOn(), isFreeTalkOn);
    }

    public void setRoomInfo(FixRoomInfo roomInfo) {
        this.roomInfo = roomInfo;
    }

    public void joinChannelWithFreeTalkStatus(boolean isFreeTalkOn) {
        joinChannel();

        if (VoiceManager.getInstance().isAgoraOn() && isFreeTalkOn) {
            VoiceManager.getInstance().muteAllRemoteAudioStreams(false);
        } else {
            muteAudioAndMic();
        }
    }

    private void openMicIfMicOnAndInSit() {
        if (VoiceManager.getInstance().isMicOn() && isSelfInSit()) {
            VoiceManager.getInstance().openMic();
        }
    }

    private void joinChannel() {
        handler.removeCallbacksAndMessages(null);

        String rid = VoiceManager.LAND_LORD + roomInfo.getRid();
        if (UrlConfig.isDebug()) {
            rid = VoiceManager.VOICE_DEV + rid;
        }

        VoiceConfig config = VoiceConfig.newBuilder()
                .setType(VoiceConfig.AGORA_VOICE)
                .setChannelName(rid)
                .setOpenMic(VoiceManager.getInstance().isMicOn() && isSelfInSit())
                .setTmpRoom(false)
                .setConfigSelector(new VoiceConfigHelper.ConfigSelector())
                .build();
        VoiceManager.getInstance().joinChannel(config);

        if (isSelfInSit() && VoiceManager.getInstance().isMicOn() && VoiceManager.getInstance().isAgoraOn()) {
            VoiceManager.getInstance().openMic();
        } else {
            VoiceManager.getInstance().closeMic();
        }
    }

    public void leaveChannel() {
        VoiceManager.getInstance().leaveChannel();
    }

    public void updateRoomTalkState() {
        if (!roomInfo.isGameing()) {
            joinChannelWithFreeTalkStatus(true);
            view.showFreeTalkView(false);
            isFreeTalkOn = true;
        } else {
            if (roomInfo.isFreeTalkOn()) {
                if (lastFreeTalkState == 0) {
                    ToastUtil.show("自由发言已开启");
                }
                joinChannelWithFreeTalkStatus(true);
                view.showFreeTalkView(true);
                isFreeTalkOn = true;
            } else {
                joinChannelWithFreeTalkStatus(false);
                view.hideFreeTalkView();
                isFreeTalkOn = false;
            }
        }
        lastFreeTalkState = roomInfo.isFreeTalkOn() ? 1 : 0;

        view.updateAudioBtn(VoiceManager.getInstance().isAgoraOn(), isFreeTalkOn);
        view.updateMicBtn(VoiceManager.getInstance().isAgoraOn()
                && VoiceManager.getInstance().isMicOn() && isSelfInSit(), isFreeTalkOn);
        view.updateHotWordsImv(isSelfInSit());
        view.updateHostIconVisibility(roomInfo.isGameing() && isSelfInSit());
    }

    public void agreeFreeTalk() {
        FixRoomPacketSender.fixAgreeFreeTalkReq(roomInfo.getRid(), true, new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                view.hideApplyFreeTalkDialog();
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public void declineFreeTalk() {
        FixRoomPacketSender.fixAgreeFreeTalkReq(roomInfo.getRid(), false, new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                view.hideApplyFreeTalkDialog();
                ToastUtil.show("你拒绝了自由发言");
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public boolean isSelfInSit() {
        return roomInfo.isInSit(LoginHelper.getLoginUid());
    }

    public void clear() {
        EventBus.getDefault().unregister(this);
    }

    public void host() {
        LLPacketSender.getInstance().trust(new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public void setCallback(SocialGameMsgCallback callback) {
        this.callback = callback;
    }
}
