package com.huiwan.landlord.view;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.InsetDrawable;
import android.os.Handler;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.huiwan.landlord.LLDialogView;
import com.wepie.wespy.R;
import com.huiwan.configservice.ConfigHelper;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.model.entity.fixroom.FixRoomInfo;
import com.huiwan.configservice.model.TopPhraseInfo;
import com.wepie.wespy.module.game.game.werewolf.view.WerewolfSendView;
import com.wepie.wespy.module.landlord.presenter.LLBottomControlPresenter;
import com.wepie.wespy.module.spy.SocialGameMsgCallback;
import com.wepie.wespy.module.voiceroom.util.DividerItemDecoration;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TouchEffectUtil;

import java.util.ArrayList;
import java.util.List;

public class LLBottomView extends RelativeLayout implements View.OnClickListener {
    private ImageView micImv;
    private ImageView audioImv;
    private ImageView msgImv;
    private ImageView lockImv;
    private ImageView hotWordsImv;
    private LLBottomControlPresenter presenter;
    private RecyclerView hotWordsRv;
    private Rect rect = new Rect();
    private HotWordsRvAdapter adapter = new HotWordsRvAdapter();
    private RelativeLayout applyFreeTalkLay;
    private TextView applyName;
    private TextView declineFreeTalkBtn;
    private TextView agreeFreeTalkBtn;
    private Handler mainHandler = new Handler();
    private WerewolfSendView gameSendView;
    private ImageView hotWordsBgImv;
    private ImageView hostImv;
    private BaseFullScreenDialog dialog;

    public LLBottomView(Context context) {
        super(context);
        init();
    }

    public LLBottomView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        presenter = new LLBottomControlPresenter(this);
        inflate(getContext(), R.layout.ll_bottom_view, this);
        initView();
    }

    private void initView() {
        micImv = (ImageView) findViewById(R.id.mic_imv);
        audioImv = (ImageView) findViewById(R.id.audio_imv);
        msgImv = (ImageView) findViewById(R.id.msg_imv);
        lockImv = (ImageView) findViewById(R.id.lock_imv);
        hotWordsImv = (ImageView) findViewById(R.id.hot_words_imv);
        hotWordsRv = (RecyclerView) findViewById(R.id.hot_words_rv);
        applyFreeTalkLay = (RelativeLayout) findViewById(R.id.apply_free_talk_lay);
        applyName = (TextView) findViewById(R.id.apply_name);
        declineFreeTalkBtn = (TextView) findViewById(R.id.decline_btn_tv);
        agreeFreeTalkBtn = (TextView) findViewById(R.id.agree_btn_tv);
        gameSendView = (WerewolfSendView) findViewById(R.id.game_send_view);
        hotWordsBgImv = (ImageView) findViewById(R.id.hot_words_bg_imv);
        hostImv = (ImageView) findViewById(R.id.host_imv);

        hotWordsRv.setPivotX(ScreenUtil.dip2px(270));
        hotWordsRv.setPivotY(ScreenUtil.dip2px(252));
        hotWordsBgImv.setPivotX(ScreenUtil.dip2px(270));
        hotWordsBgImv.setPivotY(ScreenUtil.dip2px(252));
        hotWordsRv.setAdapter(adapter);
        androidx.recyclerview.widget.DividerItemDecoration itemDecoration = new
                androidx.recyclerview.widget.DividerItemDecoration(getContext(), DividerItemDecoration.VERTICAL_LIST);
        InsetDrawable drawable = new InsetDrawable(getContext().getResources().getDrawable(R.drawable.shape_white_line),
                ScreenUtil.dip2px(10), 0, ScreenUtil.dip2px(15), 0);
        itemDecoration.setDrawable(drawable);
        hotWordsRv.addItemDecoration(itemDecoration);
        hotWordsRv.setLayoutManager(new LinearLayoutManager(getContext()));

        List<TopPhraseInfo> hotWordInfo = new ArrayList<>(ConfigHelper.getInstance().getLandlordPhrase());
        adapter.update(hotWordInfo);

        TouchEffectUtil.addTouchEffect(micImv);
        TouchEffectUtil.addTouchEffect(audioImv);
        TouchEffectUtil.addTouchEffect(msgImv);
        TouchEffectUtil.addTouchEffect(lockImv);
        TouchEffectUtil.addTouchEffect(hotWordsImv);
        TouchEffectUtil.addTouchEffect(agreeFreeTalkBtn);
        TouchEffectUtil.addTouchEffect(declineFreeTalkBtn);
        TouchEffectUtil.addTouchEffect(hostImv);

        micImv.setOnClickListener(this);
        audioImv.setOnClickListener(this);
        msgImv.setOnClickListener(this);
        lockImv.setOnClickListener(this);
        hotWordsImv.setOnClickListener(this);
        declineFreeTalkBtn.setOnClickListener(this);
        agreeFreeTalkBtn.setOnClickListener(this);
        hostImv.setOnClickListener(this);
        gameSendView.registerLandlordSendCallback(new WerewolfSendView.LandlordMsgSendCallback() {
            @Override
            public void onSendMsg(String content) {
                presenter.sendMsg(content, 0);
            }
        });
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        presenter.clear();
    }

    @Override
    public void onClick(View v) {
        if (v == micImv) {
            presenter.toggleMic();
        } else if (v == audioImv) {
            presenter.toggleAudio();
        } else if (v == msgImv) {
            if (!presenter.isFreeTalkOn()) return;
            showKeyboardView();
        } else if (v == lockImv) {
            presenter.toggleFreeTalk();
        } else if (v == hotWordsImv) {
            if (hotWordsRv.getVisibility() == GONE) {
                hotWordsRv.setScaleX(0);
                hotWordsRv.setScaleY(0);
                hotWordsBgImv.setScaleX(0);
                hotWordsBgImv.setScaleY(0);
                hotWordsRv.setVisibility(VISIBLE);
                hotWordsBgImv.setVisibility(VISIBLE);
                scaleAnim(hotWordsRv);
                scaleAnim(hotWordsBgImv);
            } else {
                hideHotWordsLay();
            }
        } else if (v == declineFreeTalkBtn) {
            presenter.declineFreeTalk();
        } else if (v == agreeFreeTalkBtn) {
            presenter.agreeFreeTalk();
        } else if (v == hostImv) {
            presenter.host();
        }
    }

    private void scaleAnim(View view) {
        ObjectAnimator animatorX = ObjectAnimator.ofFloat(view, "scaleX", 1);
        ObjectAnimator animatorY = ObjectAnimator.ofFloat(view, "scaleY", 1);
        animatorX.setDuration(150);
        animatorY.setDuration(150);
        animatorX.setInterpolator(new AccelerateDecelerateInterpolator());
        animatorY.setInterpolator(new AccelerateDecelerateInterpolator());
        animatorX.start();
        animatorY.start();
    }

    private void showKeyboardView() {
        gameSendView.setVisibility(VISIBLE);
        gameSendView.showSoftInput();
    }

    public void checkHideHotWordsRv(MotionEvent event) {
        if (hotWordsRv.getScaleX() == 1) {
            hotWordsRv.getGlobalVisibleRect(rect);
            if (!rect.contains((int) event.getRawX(), (int) event.getRawY())) {
                hideHotWordsLay();
            }
        }
    }

    public void hideHotWordsLay() {
        hotWordsRv.setVisibility(GONE);
        hotWordsBgImv.setVisibility(GONE);
    }

    public void updateRoomInfo(FixRoomInfo fixRoomInfo) {
        presenter.setRoomInfo(fixRoomInfo);
        presenter.updateRoomTalkState();
    }

    public void hideFreeTalkView() {
        msgImv.setImageResource(R.drawable.ll_bottom_msg_icon_locked);
        audioImv.setImageResource(R.drawable.ll_audio_locked);
        micImv.setImageResource(R.drawable.ll_mic_locked);
        lockImv.setVisibility(presenter.isSelfInSit() ? VISIBLE : GONE);
        lockImv.setImageResource(R.drawable.ll_lock_off);
    }

    public void showFreeTalkView(boolean showLock) {
        msgImv.setImageResource(R.drawable.ll_bottom_msg_icon);
        lockImv.setImageResource(R.drawable.ll_lock_on);
        lockImv.setVisibility((showLock && presenter.isSelfInSit()) ? VISIBLE : GONE);
    }

    public void updateAudioBtn(boolean agoraOn, boolean isFreeTalkOn) {
        if (isFreeTalkOn) {
            audioImv.setImageResource(agoraOn ? R.drawable.ll_audio_on : R.drawable.ll_audio_off);
        } else {
            audioImv.setImageResource(R.drawable.ll_audio_locked);
        }
    }

    public void updateMicBtn(boolean agoraOn, boolean isFreeTalkOn) {
        if (isFreeTalkOn) {
            micImv.setImageResource(agoraOn ? R.drawable.ll_mic_on : R.drawable.ll_mic_off);
        } else {
            micImv.setImageResource(R.drawable.ll_mic_locked);
        }
    }

    public void showFreeTalkApplyDialog(String nickname) {
        applyFreeTalkLay.setVisibility(VISIBLE);
        applyName.setText(nickname);

        final int leftTime = 10;
        countDownDeclineTxt(leftTime);
    }

    private void countDownDeclineTxt(final int leftTime) {
        if (leftTime < 0) {
            hideApplyFreeTalkDialog();
            return;
        }
        declineFreeTalkBtn.setText("拒绝（" + leftTime + "s）");
        mainHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                countDownDeclineTxt(leftTime - 1);
            }
        }, 1000);
    }

    public void hideApplyFreeTalkDialog() {
        applyFreeTalkLay.setVisibility(GONE);
        mainHandler.removeCallbacksAndMessages(null);
    }

    public void showFreeTalkCloseDialog(final OnClickListener onClickListener) {
        dialog = new BaseFullScreenDialog(getContext(), R.style.dialog_style_custom);
        LLDialogView dialogView = new LLDialogView(getContext());
        dialogView.setTitle(StrResUtil.getRes(R.string.tip));
        dialogView.setContentTv("是否关闭所有玩家的自由发言功能？");
        dialogView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                onClickListener.onClick(v);
                dialog.dismiss();
            }
        }, new OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        dialog.setContentView(dialogView);
        dialog.setCanceledOnTouchOutside(true);
        dialog.initLandlordDialog();
        dialog.show();
    }

    public void hideKeyboard() {
        gameSendView.doHide();
    }

    public void updateHotWordsImv(boolean selfInSit) {
        hotWordsImv.setVisibility(selfInSit ? VISIBLE : GONE);
    }

    public void updateHostIconVisibility(boolean show) {
        hostImv.setVisibility(show ? VISIBLE : GONE);
    }

    public void updateHostStatus(boolean isSelfHost) {
        if (isSelfHost) {
            hostImv.setEnabled(false);
            hostImv.setImageResource(R.drawable.host_icon_disabled);
        } else {
            hostImv.setEnabled(true);
            hostImv.setImageResource(R.drawable.host_icon);
        }
    }

    public void hideAllDialog() {
        if (dialog != null) dialog.dismiss();
    }

    public class HotWordsRvAdapter extends RecyclerView.Adapter<HotWordsViewHolder> {
        private List<TopPhraseInfo> hotWordsList = new ArrayList<>();

        @NonNull
        @Override
        public HotWordsViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new HotWordsViewHolder(LayoutInflater.from(getContext()).inflate(R.layout.hot_words_item_lay, parent, false));
        }

        @Override
        public void onBindViewHolder(@NonNull final HotWordsViewHolder holder, final int position) {
            final String word = hotWordsList.get(position).text;
            holder.wordTv.setText(word);
            holder.layout.setOnTouchListener(new OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    int action = event.getAction();
                    if (action == MotionEvent.ACTION_DOWN) {
                        holder.wordTv.setTextColor(Color.parseColor("#598FFF"));
                    } else if (action == MotionEvent.ACTION_UP
                            || action == MotionEvent.ACTION_CANCEL
                            || action == MotionEvent.ACTION_OUTSIDE) {
                        holder.wordTv.setTextColor(Color.parseColor("#ffffff"));
                    }
                    return false;
                }
            });
            holder.layout.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    presenter.sendMsg(word, hotWordsList.get(position).voice_id);
                }
            });
        }

        @Override
        public int getItemCount() {
            return hotWordsList.size();
        }

        public void update(List<TopPhraseInfo> hotWordsList) {
            this.hotWordsList.clear();
            this.hotWordsList.addAll(hotWordsList);
            this.notifyDataSetChanged();
        }
    }

    public class HotWordsViewHolder extends RecyclerView.ViewHolder {
        public TextView wordTv;
        public LinearLayout layout;

        public HotWordsViewHolder(View itemView) {
            super(itemView);
            wordTv = (TextView) itemView.findViewById(R.id.word_tv);
            layout = itemView.findViewById(R.id.layout);
        }
    }

    public void setCallback(SocialGameMsgCallback callback) {
        if (presenter != null) {
            presenter.setCallback(callback);
        }
    }
}
