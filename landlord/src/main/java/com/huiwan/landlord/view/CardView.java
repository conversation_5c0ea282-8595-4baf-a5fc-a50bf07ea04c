package com.huiwan.landlord.view;

import android.content.Context;
import android.graphics.Point;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.huiwan.landlord.R;
import com.huiwan.landlord.i.ILandlordPresenter;
import com.huiwan.landlord.manage.CardResUtil;
import com.huiwan.base.util.ScreenUtil;

public class CardView extends RelativeLayout {
    public static final int CARD_TYPE_HAND = 1;
    public static final int CARD_TYPE_PUT = 2;
    public static final int CARD_TYPE_BOTTOM = 3;

    private ILandlordPresenter presenter;
    private boolean isUp = false;
    private byte value;
    private ImageView colorImv;
    private ImageView valueImv;
    private ImageView blackLayer;
    private int toPutXCord; // 出牌后的x坐标
    private Point handCardCord;
    private ImageView bottomCardImv;
    private boolean isAnimating;
    private float scale = 1.0f; //小屏上尺寸需要缩放

    public void setPresenter(ILandlordPresenter presenter) {
        this.presenter = presenter;
    }

    public CardView(Context context, ILandlordPresenter presenter) {
        super(context);
        init();
        this.presenter = presenter;
    }

    private void init() {
        scale = ScreenUtil.isHdpiDevice(getContext()) ? 0.85f : 1.0f;
        inflate(getContext(), R.layout.card_lay, this);
        initView();
    }


    public void downCard(boolean shouldRemove) {
        isUp = false;
        if (shouldRemove) presenter.removeCardFromPut(this);
    }

    public void upCard() {
        isUp = true;
        presenter.addCardForPut(this);
    }

    public CardView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public void setValue(byte value, int type, boolean isLandLordRole) {
        this.value = value;
        CardResUtil.CardRes cardRes = CardResUtil.getCardRes(value);

        colorImv.setImageResource(cardRes.corlorRes);
        if (!CardResUtil.isJokerCard(cardRes.corlorRes)) {
            valueImv.setVisibility(VISIBLE);
            LayoutParams layoutParams = (LayoutParams) valueImv.getLayoutParams();
            if (type == CARD_TYPE_HAND) {
                layoutParams.width = ScreenUtil.dip2px(22 * scale);
                layoutParams.height = ScreenUtil.dip2px(27 * scale);
                layoutParams.topMargin = ScreenUtil.dip2px(4 * scale);
                layoutParams.leftMargin = ScreenUtil.dip2px(4 * scale);
            } else if (type == CARD_TYPE_PUT) {
                layoutParams.width = ScreenUtil.dip2px(14 * scale);
                layoutParams.height = ScreenUtil.dip2px(18 * scale);
                layoutParams.topMargin = ScreenUtil.dip2px(2 * scale);
                layoutParams.leftMargin = ScreenUtil.dip2px(2.5f * scale);
            } else {
                layoutParams.width = ScreenUtil.dip2px(8.5f * scale);
                layoutParams.height = ScreenUtil.dip2px(10.5f * scale);
                layoutParams.topMargin = ScreenUtil.dip2px(1 * scale);
                layoutParams.leftMargin = ScreenUtil.dip2px(1 * scale);
            }
            valueImv.setLayoutParams(layoutParams);

            valueImv.setImageResource(cardRes.valueRes);
        } else {
            valueImv.setVisibility(GONE);
        }

        if (isLandLordRole) {
            bottomCardImv.setVisibility(VISIBLE);
            LayoutParams layoutParams = (LayoutParams) bottomCardImv.getLayoutParams();
            if (type == CARD_TYPE_HAND) {
                layoutParams.width = ScreenUtil.dip2px(33 * scale);
                layoutParams.height = ScreenUtil.dip2px(33 * scale);
                layoutParams.topMargin = ScreenUtil.dip2px(4 * scale);
                layoutParams.rightMargin = ScreenUtil.dip2px(3 * scale);
            } else if (type == CARD_TYPE_PUT) {
                layoutParams.width = ScreenUtil.dip2px(33 * 0.625f * scale);
                layoutParams.height = ScreenUtil.dip2px(33 * 0.625f * scale);
                layoutParams.topMargin = ScreenUtil.dip2px(4 * 0.625f * scale);
                layoutParams.rightMargin = ScreenUtil.dip2px(3 * 0.625f * scale);
            } else {
                bottomCardImv.setVisibility(GONE);
            }
            bottomCardImv.setLayoutParams(layoutParams);
        } else {
            bottomCardImv.setVisibility(GONE);
        }
    }

    public void setDisplayBack() {
        colorImv.setImageResource(R.drawable.small_card_back);
        bottomCardImv.setVisibility(GONE);
        valueImv.setVisibility(GONE);
        setRotationY(180);
    }

    public boolean isUp() {
        return isUp;
    }

    public byte getValue() {
        return value;
    }

    public void setSelect(boolean isSelect) {
        if (isSelect) {
            showBlackLayer();
        } else {
            hideBlackLayer();
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return false;
    }

    private void initView() {
        colorImv = (ImageView) findViewById(R.id.color_imv);
        valueImv = (ImageView) findViewById(R.id.value_imv);
        blackLayer = (ImageView) findViewById(R.id.black_layer);
        bottomCardImv = (ImageView) findViewById(R.id.bottom_card_imv);
    }

    public void showBlackLayer() {
        blackLayer.setVisibility(VISIBLE);
    }

    public void hideBlackLayer() {
        blackLayer.setVisibility(GONE);
    }

    public void setToPutXCord(int toPutXCord) {
        this.toPutXCord = toPutXCord;
    }

    public int getToPutXCord() {
        return toPutXCord;
    }

    public void setHandCardCord(Point handCardCord) {
        this.handCardCord = handCardCord;
    }

    public Point getHandCardCord() {
        return handCardCord;
    }

    public boolean isAnimating() {
        return isAnimating;
    }

    public void setIsAnimating(boolean isAnimating) {
        this.isAnimating = isAnimating;
    }
}
