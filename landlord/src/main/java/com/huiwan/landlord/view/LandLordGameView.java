package com.huiwan.landlord.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Service;
import android.content.Context;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Point;
import android.graphics.Shader;
import android.media.AudioManager;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.WindowManager;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.huiwan.anim.FrameAnimation;
import com.huiwan.base.util.PressUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StringUtil;
import com.huiwan.base.util.TouchEffectUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.landlord.LLTimerView;
import com.huiwan.landlord.R;
import com.huiwan.landlord.i.ILandlordGameView;
import com.huiwan.landlord.manage.CardAnimUtil;
import com.huiwan.landlord.manage.CardResUtil;
import com.huiwan.landlord.manage.LLGameEffectManager;
import com.huiwan.landlord.presenter.LandLordPresenter;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserInfoLoadCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.User;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;

import java.util.ArrayList;
import java.util.List;

public class LandLordGameView extends RelativeLayout implements View.OnClickListener, ISocialGameView, ILandlordGameView {
    private LandLordPresenter presenter;

    private float scale = 1.0f; //小屏上尺寸需要缩放
    private RelativeLayout firstLineCardLay;
    private RelativeLayout secondLineCardLay;
    private RelativeLayout myPutCardLay;
    private RelativeLayout prevPutCardLay;
    private RelativeLayout nextPutCardLay;
    private CardView bottomCard1;
    private CardView bottomCard2;
    private CardView bottomCard3;
    private ImageView cardInvalidImv;
    private ImageView cardCantMatchImv;
    private ImageView callLordImv;
    private ImageView noCallImv;
    private ImageView passImv;
    private ImageView putCardImv;
    private ImageView putCardImvSingle;
    private ImageView cantMatchImv;
    private ImageView prepareGameImv;
    private ImageView promptImv;
    private ImageView cancelHostImv;
    private ImageView prevUserHead;
    private TextView prevName;
    private TextView prevBeanNum;
    private ImageView nextUserHead;
    private TextView nextName;
    private TextView nextBeanNum;
    private ImageView myHeadImv;
    private TextView myName;
    private TextView myBeanNum;
    private ImageView prevActionImv;
    private ImageView nextActionImv;
    private ImageView myActionImv;
    private TextView startPoints;
    private TextView multipleNum;
    private ImageView prevHandCardImv;
    private TextView prevHandCardNum;
    private ImageView nextHandCardImv;
    private TextView nextHandCardNum;
    private LLGameOverView gameOverView;
    private Handler mHandler = new Handler();
    private float myPutCardLayY;
    private RelativeLayout myCardContainer;
    private LLTimerView singleBtnMyTimer;
    private LLTimerView threeBtnMyTimer;
    private LLTimerView prevTimer;
    private LLTimerView nextTimer;
    private LLTimerView myCenterTimer;
    private LLBottomView bottomControlView;
    private ImageView prevHostIcon;
    private ImageView nextHostIcon;
    private FrameLayout prevChatLay;
    private TextView prevChatTv;
    private FrameLayout nextChatLay;
    private TextView nextChatTv;
    private FrameLayout myChatLay;
    private TextView myChatTv;
    private ImageView ticketImv;
    private TextView ticketTv;
    private TextView mostWinTv;
    private TextView prevEmptySeatTv;
    private TextView nextEmptySeatTv;
    private ImageView menuIv;
    private TextView roomNameTv;
    private TextView roomLevelTv;
    private ImageView roomInfoImv;
    private ImageView recallView;
    private ImageView prevBeanIcon;
    private ImageView nextBeanIcon;
    private TextView myEmptySeatTv;
    private ImageView myBeanIcon;
    private float prevHeadX;
    private float prevHeadY;
    private float nextHeadX;
    private float nextHeadY;
    private float selfHeadX;
    private float selfHeadY;
    private float prevHatX;
    private float prevHatY;
    private float nextHatX;
    private float nextHatY;
    private float selfHatX;
    private float selfHatY;
    private float centerHatX;
    private float centerHatY;
    private List<ImageView> myBackCardViewList = new ArrayList<>();
    private PopupWindow popup;
    private BaseFullScreenDialog dialog;
    private BarrageAnimView barrageView;
    private ImageView selfPrepareActionImv;
    private ImageView selfLandLordMark;
    private ImageView prevLandLordMark;
    private ImageView nextLandLordMark;
    private ImageView landlordLightBottom;
    private ImageView landlordLightAnim;
    private ImageView centerLandlordMark;
    private ImageView smallCenterLandlordMark;
    private ImageView prevSpeakerIcon;
    private ImageView nextSpeakerIcon;
    private ImageView selfSpeakerIcon;
    private ImageView multipleAnimImv;
    private ImageView prevFinishPutImv;
    private ImageView nextFinishPutImv;
    private ImageView selfFinishPutImv;
    private ImageView doubleImv;
    private ImageView noDoubleImv;
    private ImageView prevWinAlarmImv;
    private ImageView nextWinAlarmImv;
    private FrameAnimation prevWinAlarmAnim;
    private int[] winAlarmAnimRes = {R.drawable.ll_win_alarm_00000, R.drawable.ll_win_alarm_00003, R.drawable.ll_win_alarm_00004, R.drawable.ll_win_alarm_00008, R.drawable.ll_win_alarm_00009,
            R.drawable.ll_win_alarm_00011, R.drawable.ll_win_alarm_00012, R.drawable.ll_win_alarm_00013, R.drawable.ll_win_alarm_00014, R.drawable.ll_win_alarm_00016, R.drawable.ll_win_alarm_00018, R.drawable.ll_win_alarm_00019,
            R.drawable.ll_win_alarm_00021, R.drawable.ll_win_alarm_00022, R.drawable.ll_win_alarm_00023, R.drawable.ll_win_alarm_00024, R.drawable.ll_win_alarm_00025, R.drawable.ll_win_alarm_00027, R.drawable.ll_win_alarm_00028, R.drawable.ll_win_alarm_00029,
            R.drawable.ll_win_alarm_00030, R.drawable.ll_win_alarm_00031, R.drawable.ll_win_alarm_00033, R.drawable.ll_win_alarm_00034, R.drawable.ll_win_alarm_00035, R.drawable.ll_win_alarm_00037, R.drawable.ll_win_alarm_00038, R.drawable.ll_win_alarm_00039,
            R.drawable.ll_win_alarm_00041, R.drawable.ll_win_alarm_00042, R.drawable.ll_win_alarm_00043, R.drawable.ll_win_alarm_00047};
    private FrameAnimation nextWinAlarmAnim;
    private boolean isHandCardAlreadyShow;
    private boolean isLandlordLogoAniming;
    private ImageView prevCardAnimView;
    private ImageView nextCardAnimView;
    private ImageView myCardAnimView;
    private ImageView gameOverAnimView;

    public LandLordGameView(Context context, FixRoomInfo fixRoomInfo) {
        super(context);
        presenter = new LandLordPresenter(this, LoginHelper.getLoginUid());
        presenter.setRoomInfo(fixRoomInfo);
        init();
    }

    public LandLordGameView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        scale = ScreenUtil.isHdpiDevice(getContext()) ? 0.85f : 1.0f;
        CardResUtil.setScale(scale);
        inflate(getContext(), R.layout.land_lord_game_view, this);
        initView();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mHandler.removeCallbacksAndMessages(null);
        presenter.destroy();
    }

    private void initView() {
        firstLineCardLay = (RelativeLayout) findViewById(R.id.first_line_card_container);
        secondLineCardLay = (RelativeLayout) findViewById(R.id.second_line_card_container);
        bottomCard1 = findViewById(R.id.bottom_card_1);
        bottomCard2 = findViewById(R.id.bottom_card_2);
        bottomCard3 = findViewById(R.id.bottom_card_3);
        cardInvalidImv = (ImageView) findViewById(R.id.card_ivalid_imv);
        cardCantMatchImv = (ImageView) findViewById(R.id.card_cant_match_imv);
        passImv = (ImageView) findViewById(R.id.pass_imv);
        putCardImv = (ImageView) findViewById(R.id.put_card_imv);
        putCardImvSingle = (ImageView) findViewById(R.id.put_card_imv_single);
        cantMatchImv = (ImageView) findViewById(R.id.cant_match_imv);
        prepareGameImv = (ImageView) findViewById(R.id.prepare_game_imv);
        cancelHostImv = (ImageView) findViewById(R.id.cancel_host_imv);
        prevUserHead = findViewById(R.id.prev_user_head);
        prevName = (TextView) findViewById(R.id.prev_name);
        prevBeanNum = (TextView) findViewById(R.id.prev_bean_num);
        nextUserHead = findViewById(R.id.next_user_head);
        nextName = (TextView) findViewById(R.id.next_name);
        nextBeanNum = (TextView) findViewById(R.id.next_bean_num);
        myHeadImv = findViewById(R.id.my_head_imv);
        myName = (TextView) findViewById(R.id.my_name);
        myBeanNum = (TextView) findViewById(R.id.my_bean_num);
        prevActionImv = (ImageView) findViewById(R.id.prev_action_imv);
        promptImv = findViewById(R.id.prompt_imv);
        callLordImv = findViewById(R.id.call_lord_tv);
        noCallImv = findViewById(R.id.no_call_tv);
        nextActionImv = (ImageView) findViewById(R.id.next_action_imv);
        myActionImv = (ImageView) findViewById(R.id.my_action_imv);
        startPoints = (TextView) findViewById(R.id.start_points);
        multipleNum = (TextView) findViewById(R.id.multiple_num);
        myPutCardLay = (RelativeLayout) findViewById(R.id.my_put_card_lay);
        prevPutCardLay = (RelativeLayout) findViewById(R.id.prev_put_card_lay);
        nextPutCardLay = (RelativeLayout) findViewById(R.id.next_put_card_lay);
        prevHandCardImv = (ImageView) findViewById(R.id.prev_hand_card_imv);
        prevHandCardNum = (TextView) findViewById(R.id.prev_hand_card_num);
        nextHandCardImv = (ImageView) findViewById(R.id.next_hand_card_imv);
        nextHandCardNum = (TextView) findViewById(R.id.next_hand_card_num);
        myCardContainer = (RelativeLayout) findViewById(R.id.my_card_container);
        gameOverView = findViewById(R.id.game_over_view);
        singleBtnMyTimer = (LLTimerView) findViewById(R.id.single_btn_my_timer);
        threeBtnMyTimer = (LLTimerView) findViewById(R.id.three_btn_my_timer);
        prevTimer = (LLTimerView) findViewById(R.id.prev_timer);
        nextTimer = (LLTimerView) findViewById(R.id.next_timer);
        myCenterTimer = (LLTimerView) findViewById(R.id.my_center_timer);
        bottomControlView = (LLBottomView) findViewById(R.id.bottom_control_view);
        prevHostIcon = (ImageView) findViewById(R.id.prev_host_icon);
        nextHostIcon = (ImageView) findViewById(R.id.next_host_icon);
        prevChatLay = (FrameLayout) findViewById(R.id.prev_chat_lay);
        prevChatTv = (TextView) findViewById(R.id.prev_chat_tv);
        nextChatLay = (FrameLayout) findViewById(R.id.next_chat_lay);
        nextChatTv = (TextView) findViewById(R.id.next_chat_tv);
        myChatLay = (FrameLayout) findViewById(R.id.my_chat_lay);
        myChatTv = (TextView) findViewById(R.id.my_chat_tv);
        ticketImv = (ImageView) findViewById(R.id.ticket_imv);
        ticketTv = (TextView) findViewById(R.id.ticket_tv);
        mostWinTv = (TextView) findViewById(R.id.most_win_tv);
        prevEmptySeatTv = (TextView) findViewById(R.id.prev_empty_seat_tv);
        nextEmptySeatTv = (TextView) findViewById(R.id.next_empty_seat_tv);
        menuIv = (ImageView) findViewById(R.id.menu_iv);
        roomNameTv = (TextView) findViewById(R.id.room_name_tv);
        roomLevelTv = (TextView) findViewById(R.id.room_level_tv);
        roomInfoImv = (ImageView) findViewById(R.id.room_info_imv);
        recallView = (ImageView) findViewById(R.id.room_call_up_iv);
        prevBeanIcon = (ImageView) findViewById(R.id.prev_bean_icon);
        nextBeanIcon = (ImageView) findViewById(R.id.next_bean_icon);
        myEmptySeatTv = (TextView) findViewById(R.id.my_empty_seat_tv);
        myBeanIcon = (ImageView) findViewById(R.id.my_bean_icon);
        SurfaceView animView = findViewById(R.id.card_effect_anim_view);
        barrageView = (BarrageAnimView) findViewById(R.id.barrage_view);
        selfPrepareActionImv = findViewById(R.id.self_preapre_action_imv);
        selfLandLordMark = (ImageView) findViewById(R.id.self_land_lord_mark);
        prevLandLordMark = (ImageView) findViewById(R.id.prev_land_lord_mark);
        nextLandLordMark = (ImageView) findViewById(R.id.next_land_lord_mark);
        landlordLightBottom = (ImageView) findViewById(R.id.landlord_light_bottom);
        landlordLightAnim = (ImageView) findViewById(R.id.landlord_light_anim);
        centerLandlordMark = (ImageView) findViewById(R.id.center_landlord_mark);
        smallCenterLandlordMark = (ImageView) findViewById(R.id.small_center_landlord_mark);
        prevSpeakerIcon = (ImageView) findViewById(R.id.prev_speaker_icon);
        nextSpeakerIcon = (ImageView) findViewById(R.id.next_speaker_icon);
        selfSpeakerIcon = (ImageView) findViewById(R.id.self_speaker_icon);
        multipleAnimImv = (ImageView) findViewById(R.id.multiple_anim_imv);
        prevFinishPutImv = (ImageView) findViewById(R.id.prev_finish_put_imv);
        nextFinishPutImv = (ImageView) findViewById(R.id.next_finish_put_imv);
        selfFinishPutImv = (ImageView) findViewById(R.id.self_finish_put_imv);
        doubleImv = (ImageView) findViewById(R.id.double_tv);
        noDoubleImv = (ImageView) findViewById(R.id.no_double_tv);
        prevWinAlarmImv = (ImageView) findViewById(R.id.prev_win_alarm_imv);
        nextWinAlarmImv = (ImageView) findViewById(R.id.next_win_alarm_imv);
        prevCardAnimView = (ImageView) findViewById(R.id.prev_card_anim_view);
        nextCardAnimView = (ImageView) findViewById(R.id.next_card_anim_view);
        myCardAnimView = (ImageView) findViewById(R.id.my_card_anim_view);
        gameOverAnimView = (ImageView) findViewById(R.id.game_over_anim);

        prevUserHead.setBorderColor(Color.parseColor("#ffffff"));
        nextUserHead.setBorderColor(Color.parseColor("#ffffff"));
        myHeadImv.setBorderColor(Color.parseColor("#ffffff"));

        singleBtnMyTimer.setPresenter(presenter);
        myCenterTimer.setPresenter(presenter);
        threeBtnMyTimer.setPresenter(presenter);
        bottomCard1.setPresenter(presenter);
        bottomCard2.setPresenter(presenter);
        bottomCard3.setPresenter(presenter);
        bottomCard1.setDisplayBack();
        bottomCard2.setDisplayBack();
        bottomCard3.setDisplayBack();

        TouchEffectUtil.addLandLordTouchEffect(prepareGameImv);
        TouchEffectUtil.addLandLordTouchEffect(callLordImv);
        TouchEffectUtil.addLandLordTouchEffect(noCallImv);
        TouchEffectUtil.addLandLordTouchEffect(passImv);
        TouchEffectUtil.addLandLordTouchEffect(putCardImv);
        TouchEffectUtil.addLandLordTouchEffect(promptImv);
        TouchEffectUtil.addLandLordTouchEffect(putCardImvSingle);
        TouchEffectUtil.addLandLordTouchEffect(cancelHostImv);
        TouchEffectUtil.addLandLordTouchEffect(cantMatchImv);
        TouchEffectUtil.addLandLordTouchEffect(doubleImv);
        TouchEffectUtil.addLandLordTouchEffect(noDoubleImv);

        prevWinAlarmAnim = new FrameAnimation(prevWinAlarmImv, winAlarmAnimRes, 50, false);
        nextWinAlarmAnim = new FrameAnimation(nextWinAlarmImv, winAlarmAnimRes, 50, false);
        prevWinAlarmAnim.pauseAnimation();
        nextWinAlarmAnim.pauseAnimation();
        prevWinAlarmAnim.setAnimationListener(new FrameAnimation.AnimationListener() {
            @Override
            public void onAnimationStart() {
            }

            @Override
            public void onAnimationEnd() {
                prevWinAlarmImv.setImageResource(R.drawable.ll_win_alarm_00000);
                prevWinAlarmAnim.pauseAnimation();
            }

            @Override
            public void onAnimationRepeat() {
            }
        });
        nextWinAlarmAnim.setAnimationListener(new FrameAnimation.AnimationListener() {
            @Override
            public void onAnimationStart() {
            }

            @Override
            public void onAnimationEnd() {
                nextWinAlarmImv.setImageResource(R.drawable.ll_win_alarm_00000);
                nextWinAlarmAnim.pauseAnimation();
            }

            @Override
            public void onAnimationRepeat() {
            }
        });


        Shader beanNumShader = new LinearGradient(0, 0, 0, ScreenUtil.dip2px(12),
                Color.parseColor("#ffe7a5"), Color.parseColor("#f8be6b"), Shader.TileMode.CLAMP);
        myBeanNum.getPaint().setShader(beanNumShader);
        prevBeanNum.getPaint().setShader(beanNumShader);
        nextBeanNum.getPaint().setShader(beanNumShader);
        int[] colors = {Color.parseColor("#F8F3DE"), Color.parseColor("#FFB849"), Color.parseColor("#FFFB6A")};
        float[] pos = {0, 0.1f, 1};
        Shader multipleShader = new LinearGradient(0, 0, 0, ScreenUtil.dip2px(14), colors, pos, Shader.TileMode.CLAMP);
        multipleNum.getPaint().setShader(multipleShader);

        prepareGameImv.setOnClickListener(this);
        passImv.setOnClickListener(this);
        putCardImv.setOnClickListener(this);
        putCardImvSingle.setOnClickListener(this);
        promptImv.setOnClickListener(this);
        cantMatchImv.setOnClickListener(this);
        cancelHostImv.setOnClickListener(this);
        myHeadImv.setOnClickListener(this);
        prevUserHead.setOnClickListener(this);
        myHeadImv.setOnClickListener(this);
        nextUserHead.setOnClickListener(this);
        callLordImv.setOnClickListener(this);
        noCallImv.setOnClickListener(this);
        roomInfoImv.setOnClickListener(this);
        recallView.setOnClickListener(this);
        menuIv.setOnClickListener(this);
        roomLevelTv.setOnClickListener(this);
        doubleImv.setOnClickListener(this);
        noDoubleImv.setOnClickListener(this);

        myPutCardLay.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                myPutCardLayY = myPutCardLay.getY();
                myPutCardLay.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });
        firstLineCardLay.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (firstLineCardLay.getY() > 0) {
                    CardResUtil.setFirstLineY(firstLineCardLay.getY());
                }
                if (isHandCardAlreadyShow && CardResUtil.getSecondLineY() != 0) {
                    updateMyHandCard(presenter.getMyHandCardBytes(), false, false);
                }
                firstLineCardLay.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });
        secondLineCardLay.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                if (secondLineCardLay.getY() > 0) {
                    CardResUtil.setSecondLineY(secondLineCardLay.getY());
                }
                if (isHandCardAlreadyShow && CardResUtil.getFirstLineY() != 0) {
                    updateMyHandCard(presenter.getMyHandCardBytes(), false, false);
                }
                secondLineCardLay.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });
        prevUserHead.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                prevHeadX = prevUserHead.getX();
                prevHeadY = prevUserHead.getY();
                prevHatX = prevHeadX - ScreenUtil.dip2px(8);
                prevHatY = prevHeadY - ScreenUtil.dip2px(26);
                prevUserHead.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });
        nextUserHead.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                nextHeadX = nextUserHead.getX();
                nextHeadY = nextUserHead.getY();
                nextHatX = nextHeadX - ScreenUtil.dip2px(8);
                nextHatY = nextHeadY - ScreenUtil.dip2px(26);
                nextUserHead.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });
        myHeadImv.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                selfHeadX = myHeadImv.getX();
                selfHeadY = myHeadImv.getY();
                selfHatX = selfHeadX - ScreenUtil.dip2px(8);
                selfHatY = selfHeadY - ScreenUtil.dip2px(26);
                myHeadImv.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });
        smallCenterLandlordMark.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                centerHatX = smallCenterLandlordMark.getX();
                centerHatY = smallCenterLandlordMark.getY();
                smallCenterLandlordMark.getViewTreeObserver().removeOnGlobalLayoutListener(this);
            }
        });
        LLGameEffectManager.setSurfaceView(animView);
    }

    private void updateUserInfo(final int uid, final View seatView, final TextView nameView, final TextView beanNum, final View beanIcon, final CustomCircleImageView head, final int bean_count) {
        UserService.get().getCacheUser(uid, new UserInfoLoadCallback() {
            @Override
            public void onUserInfoSuccess(User userInfo) {
                seatView.setVisibility(GONE);
                nameView.setVisibility(VISIBLE);
                beanNum.setVisibility(VISIBLE);
                beanIcon.setVisibility(VISIBLE);
                if (uid == LoginHelper.getLoginUid()) {
                    nameView.setText(userInfo.nickname);
                } else {
                    nameView.setText(userInfo.getRemarkName());
                }
                setDoudouNum(bean_count, beanNum);
                head.setBorderWidth(ScreenUtil.dip2px(2));
                HeadImageLoader.loadCircleHeadImage(userInfo.headimgurl, head);
            }

            @Override
            public void onUserInfoFailed(String description) {
            }
        });
    }

    private void setDoudouNum(long doudou, TextView beanNum) {
        beanNum.setText(StringUtil.formatDouDouLong(doudou));
    }

    @Override
    public void onClick(View v) {
        if (v == prepareGameImv) {
            if (!presenter.isSelfReady()) {
                presenter.ready();
            } else {
                presenter.unready();
            }
        } else if (v == callLordImv) {
            presenter.callLord();
        } else if (v == noCallImv) {
            presenter.noCallLord();
        } else if (v == doubleImv) {
            presenter.doubleGame(true);
        } else if (v == noDoubleImv) {
            presenter.doubleGame(false);
        } else if (v == putCardImv || v == putCardImvSingle) {
            presenter.putCard();
        } else if (v == passImv || v == cantMatchImv) {
            presenter.pass();
        } else if (v == cancelHostImv) {
            presenter.cancelHost();
        } else if (v == myHeadImv) {
            if (presenter.isSelfSeated()) {
                showUserDialog(presenter.getSelfUid());
            } else {
                presenter.sitLast();
            }
        } else if (v == prevUserHead) {
            if (presenter.isPrevSeated()) {
                showUserDialog(presenter.getPrevUid());
            } else {
                if (!presenter.isSelfSeated()) {
                    presenter.sitPrev();
                }
            }
        } else if (v == nextUserHead) {
            if (presenter.isNextSeated()) {
                showUserDialog(presenter.getNextUid());
            } else {
                if (!presenter.isSelfSeated()) {
                    presenter.sitNext();
                }
            }
        } else if (v == promptImv) {
            presenter.doPrompt();
        } else if (v == menuIv) {
            showMenuPop(menuIv);
        } else if (v == roomInfoImv) {
            JumpUtil.gotoFixRoomSettingActivity(getContext(), presenter.getRoomId());
        } else if (v == roomLevelTv) {
            WebActivity.go(getContext(), ConfigHelper.getInstance().getFixRoomLevelUrl() + presenter.getRoomId());
        } else if (v == recallView) {
            DialogUtil.showFixRoomConveneDialog(getContext(), presenter.getRoomId());
        }
    }

    private void showUserDialog(int uid) {
        dialog = SocialGameUserDialog.showFixRoomUserDialog(getContext(), uid, presenter.getFixRoomInfo(), SocialGameUserDialogData.SCENE_FIX_ROOM, callback);
    }

    private void showMenuPop(ImageView menuIv) {
        popup = new PopupWindow(this);
        View layout = LayoutInflater.from(getContext()).inflate(R.layout.land_lord_menu, null);
        View gameExplain = layout.findViewById(R.id.ll_menu_game_explain);
        View gameExit = layout.findViewById(R.id.ll_menu_game_exit);
        View gameVoice = layout.findViewById(R.id.ll_menu_game_voice);
        View standUp = layout.findViewById(R.id.ll_menu_standup);
        View buyDoudou = layout.findViewById(R.id.ll_menu_buy_doudou);

        PressUtil.addPressEffect(gameExit);
        PressUtil.addPressEffect(gameExplain);
        PressUtil.addPressEffect(gameVoice);
        PressUtil.addPressEffect(standUp);
        PressUtil.addPressEffect(buyDoudou);

        standUp.setVisibility((presenter.isSelfSeated() && !presenter.isWatcher() && !presenter.isGaming()) ? VISIBLE : GONE);
        gameExplain.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                WebActivity.go(getContext(), ConfigHelper.getInstance().getFixRoomRuleUrl());
                popup.dismiss();
            }
        });
        gameExit.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                popup.dismiss();
                FixRoomExitUtil.clickBackFromRoom(getContext(), presenter.getRoomId(), null);
            }
        });
        gameVoice.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                showRegulateDialog();
                popup.dismiss();
            }
        });
        standUp.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                presenter.standUp();
                popup.dismiss();
            }
        });
        buyDoudou.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                DialogUtil.showExchangeGood();
                popup.dismiss();
            }
        });

        popup.setContentView(layout);
        popup.setHeight(WindowManager.LayoutParams.WRAP_CONTENT);
        popup.setWidth(WindowManager.LayoutParams.WRAP_CONTENT);
        popup.setOutsideTouchable(true);
        popup.setFocusable(true);
        popup.setBackgroundDrawable(getResources().getDrawable(R.color.transparent));
        popup.showAsDropDown(menuIv, -ScreenUtil.dip2px(20), ScreenUtil.dip2px(-8));
    }

    private AudioManager audioManager;
    private int maxMusic = 0, maxCall = 0;

    public void showRegulateDialog() {
        if (audioManager == null) {
            audioManager = (AudioManager) WPApplication.getInstance().getSystemService(Service.AUDIO_SERVICE);
            maxMusic = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
            maxCall = audioManager.getStreamMaxVolume(AudioManager.STREAM_VOICE_CALL);
        }
        IceBallDialogUtil.showRegulateDialog(getContext(), new IceBallAdjustVoiceDialog.Callback() {
            @Override
            public void onTouchFirst(float percent) {
                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, (int) (percent * maxMusic), AudioManager.FLAG_REMOVE_SOUND_AND_VIBRATE);
            }

            @Override
            public void onTouchSecend(float percent) {
                audioManager.setStreamVolume(AudioManager.STREAM_VOICE_CALL, (int) (percent * maxCall), AudioManager.FLAG_REMOVE_SOUND_AND_VIBRATE);
            }
        });
    }

    public void updateMyHandCard(final byte[] myCards, final boolean isGameStart, final boolean isLandLordCardDisplay) {
        isHandCardAlreadyShow = true;

        clearMyHandCard();
        presenter.clearMyHandCardViews();

        int bottomCardNum = 0;
        for (int i = 0; i < myCards.length; i++) {
            byte card = myCards[i];
            final CardView cardView = new CardView(getContext(), presenter);
            cardView.setValue(card, CardView.CARD_TYPE_HAND, presenter.isLandLordRole(0));
            presenter.addMyCardView(cardView);

            Point coord = CardResUtil.getHandCardsPostion(myCards.length, i);

            LayoutParams layoutParams = new LayoutParams(ScreenUtil.dip2px(72 * scale), ScreenUtil.dip2px(96 * scale));
            layoutParams.leftMargin = coord.x;
            layoutParams.topMargin = coord.y;
            if (isLandLordCardDisplay) {
                if (presenter.isBottomCard(card)) {
                    bottomCardNum++;
                    layoutParams.topMargin -= ScreenUtil.dip2px(12 * scale);
                    if (bottomCardNum == 3) {
                        presenter.delayDownBottomCards();
                    }
                }
            }
            myCardContainer.addView(cardView, layoutParams);
            if (isGameStart) {
                cardView.setVisibility(GONE);
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        cardView.setVisibility(VISIBLE);
                    }
                }, 150 * i);
            }
        }
    }

    private void clearMyHandCard() {
        List<CardView> myHandCardViewList = presenter.getMyHandCardViews();
        for (CardView cardView : myHandCardViewList) {
            myCardContainer.removeView(cardView);
        }
    }

    public void showCallLordBtn(boolean isGrab, long timeLeft) {
        myActionImv.setVisibility(GONE);
        callLordImv.setVisibility(!presenter.isWatcher() ? VISIBLE : GONE);
        noCallImv.setVisibility(!presenter.isWatcher() ? VISIBLE : GONE);
        callLordImv.setImageResource(isGrab ? R.drawable.grab_lord_btn : R.drawable.call_lord_btn);
        noCallImv.setImageResource(isGrab ? R.drawable.no_grab_btn : R.drawable.no_call_btn);

        prevTimer.updateTimer(-1);
        nextTimer.updateTimer(-1);
        myCenterTimer.updateTimer(timeLeft);
    }

    public void onPrevUserPutCard(int prevUid, byte[] prevCards, byte[] nextCards, boolean isPrevPass, boolean isNextPass,
                                  boolean isSelfLordFirstHand, boolean isSelfHost, boolean isGameEnd, long timeLeft, boolean isCurrentActionUser) {
        myActionImv.setVisibility(View.GONE);
        callLordImv.setVisibility(View.GONE);
        noCallImv.setVisibility(View.GONE);
        doubleImv.setVisibility(GONE);
        noDoubleImv.setVisibility(GONE);
        myCenterTimer.updateTimer(-1);

        boolean canMatch;
        if (isSelfLordFirstHand) {
            canMatch = true;
        } else {
            if (isCurrentActionUser) {
                LLGameEffectManager.playCardAudioAndAnim(prevUid, prevCards, isPrevPass, prevCardAnimView);
            }
            showPrevPutCard(prevCards, isPrevPass);
            showNextPutCard(nextCards, isNextPass);

            if ((prevCards.length == 0 && nextCards.length == 0) || presenter.isWatcher()) {
                canMatch = true;
            } else {
                canMatch = presenter.canMatch();
            }
        }

        if (isGameEnd) {
            return;
        }
        if (isSelfHost) {
            hideSelfActionButton();
        } else {
            if (canMatch) {
                if (prevCards.length == 0 && nextCards.length == 0) {
                    promptImv.setVisibility(GONE);
                    passImv.setVisibility(GONE);
                    putCardImv.setVisibility(GONE);
                    cantMatchImv.setVisibility(GONE);
                    putCardImvSingle.setVisibility(!presenter.isWatcher() ? VISIBLE : GONE);
                    singleBtnMyTimer.updateTimer(timeLeft);
                } else {
                    promptImv.setVisibility(!presenter.isWatcher() ? VISIBLE : GONE);
                    passImv.setVisibility(!presenter.isWatcher() ? VISIBLE : GONE);
                    putCardImv.setVisibility(!presenter.isWatcher() ? VISIBLE : GONE);
                    cantMatchImv.setVisibility(GONE);
                    putCardImvSingle.setVisibility(GONE);
                    threeBtnMyTimer.updateTimer(timeLeft);
                }
            } else {
                promptImv.setVisibility(View.GONE);
                passImv.setVisibility(View.GONE);
                putCardImv.setVisibility(View.GONE);
                cantMatchImv.setVisibility(!presenter.isWatcher() ? VISIBLE : GONE);
                putCardImvSingle.setVisibility(GONE);

                cardCantMatchImv.setVisibility(!presenter.isWatcher() ? VISIBLE : GONE);
                presenter.showCardCannotSelectLayer();

                singleBtnMyTimer.updateCantMatchTimer(timeLeft);
            }
        }
    }

    private void hideSelfActionButton() {
        promptImv.setVisibility(GONE);
        passImv.setVisibility(GONE);
        putCardImv.setVisibility(GONE);
        cantMatchImv.setVisibility(GONE);
        putCardImvSingle.setVisibility(GONE);
    }

    public void showPrevPutCard(byte[] cards, boolean isPass) {
        prevTimer.updateTimer(-1);
        prevPutCardLay.removeAllViews();

        if (isPass) {
            prevActionImv.setVisibility(VISIBLE);
            prevActionImv.setImageResource(R.drawable.pass_txt);
        } else {
            prevActionImv.setVisibility(View.GONE);

            for (int i = 0; i < cards.length; i++) {
                CardView cardView = new CardView(getContext(), presenter);
                cardView.setValue(cards[i], CardView.CARD_TYPE_PUT, presenter.isLandLordRole(2));
                LayoutParams layoutParams = new LayoutParams(ScreenUtil.dip2px(45 * scale), ScreenUtil.dip2px(60 * scale));
                float cardLeftMargin = 14;
                if (cards.length >= 10) {
                    cardLeftMargin = 13.2f;
                }
                if (i <= 9) {
                    layoutParams.leftMargin = ScreenUtil.dip2px(cardLeftMargin * scale) * i;
                } else {
                    layoutParams.leftMargin = ScreenUtil.dip2px(cardLeftMargin * scale) * (i - 10);
                    layoutParams.topMargin = ScreenUtil.dip2px(30 * scale);
                }
                prevPutCardLay.addView(cardView, layoutParams);
            }
        }
    }

    public void onNextUserPutCard(int nextUid, byte[] nextCards, byte[] myCards,
                                  boolean isNextPass, boolean isSelfPass, boolean isGameEnd, long timeLeft, boolean isCurrentActionUser) {
        if (!isGameEnd) {
            prevPutCardLay.removeAllViews();
            prevActionImv.setVisibility(View.GONE);
            prevTimer.updateTimer(timeLeft);
        }

        if (isCurrentActionUser) {
            LLGameEffectManager.playCardAudioAndAnim(nextUid, nextCards, isNextPass, nextCardAnimView);
        }
        showNextPutCard(nextCards, isNextPass);
        showMyPutCard(myCards, isSelfPass, false);
        hideCantMatchLay();
    }

    private void hideCantMatchLay() {
        presenter.hideCardCannotSelectLayer();
        cardCantMatchImv.setVisibility(GONE);
    }

    private void showNextPutCard(byte[] cards, boolean isPass) {
        nextTimer.updateTimer(-1);
        nextPutCardLay.removeAllViews();

        if (isPass) {
            nextActionImv.setVisibility(VISIBLE);
            nextActionImv.setImageResource(R.drawable.pass_txt);
        } else {
            if (cards.length == 0) return;
            nextActionImv.setVisibility(View.GONE);
            for (int i = 0; i < cards.length; i++) {
                CardView cardView = new CardView(getContext(), presenter);
                cardView.setValue(cards[i], CardView.CARD_TYPE_PUT, presenter.isLandLordRole(1));
                LayoutParams layoutParams = new LayoutParams(ScreenUtil.dip2px(45 * scale), ScreenUtil.dip2px(60 * scale));
                float cardRightMargin = 14;
                if (cards.length >= 10) {
                    cardRightMargin = 13.2f;
                }
                if (i <= 9) {
                    if (cards.length < 10) {
                        layoutParams.rightMargin = ScreenUtil.dip2px(cardRightMargin * scale) * (cards.length - i - 1);
                    } else {
                        layoutParams.rightMargin = ScreenUtil.dip2px(cardRightMargin * scale) * (10 - i - 1);
                    }
                } else {
                    layoutParams.rightMargin = ScreenUtil.dip2px(cardRightMargin * scale) * (20 - i - 1);
                    layoutParams.topMargin = ScreenUtil.dip2px(30 * scale);
                }
                layoutParams.addRule(ALIGN_PARENT_RIGHT);
                nextPutCardLay.addView(cardView, layoutParams);
            }
        }
    }

    public void showMyPutCard(byte[] myCards, boolean isSelfPass, boolean isCurrentActionUser) {
        if (isCurrentActionUser) {
            LLGameEffectManager.playCardAudioAndAnim(presenter.getSelfUid(), myCards, isSelfPass, myCardAnimView);
        }

        singleBtnMyTimer.updateTimer(-1);
        threeBtnMyTimer.updateTimer(-1);
        myCenterTimer.updateTimer(-1);

        if (isSelfPass) {
            myActionImv.setVisibility(VISIBLE);
            myActionImv.setImageResource(R.drawable.pass_txt);
        } else {
            presenter.clearMyPutCardView();
            myActionImv.setVisibility(GONE);
            for (int i = 0; i < myCards.length; i++) {
                CardView cardView = new CardView(getContext(), presenter);
                cardView.setValue(myCards[i], CardView.CARD_TYPE_PUT, presenter.isLandLordRole(0));
                LayoutParams layoutParams = new LayoutParams(ScreenUtil.dip2px(45 * scale), ScreenUtil.dip2px(60 * scale));
                layoutParams.leftMargin = CardResUtil.getCardsPutPosition(myCards.length, i) + ScreenUtil.dip2px(13.5f * scale);
                layoutParams.topMargin = (int) (getPutLayY() + ScreenUtil.dip2px(18f * scale));
                myCardContainer.addView(cardView, layoutParams);

                presenter.addHasPutCardView(cardView);
            }
        }
    }

    public void showPutCardError() {
        mHandler.removeCallbacksAndMessages(null);
        cardInvalidImv.setVisibility(VISIBLE);
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                cardInvalidImv.setVisibility(GONE);
            }
        }, 2 * 1000);
    }

    public void onUserPutCard(int selfUid, byte[] cards, boolean isSelfPass, boolean isGameEnd, boolean isCurrentActionUser) {
        if (!isGameEnd) {
            nextPutCardLay.removeAllViews();
            nextActionImv.setVisibility(View.GONE);
        }

        if (isCurrentActionUser) {
            LLGameEffectManager.playCardAudioAndAnim(selfUid, cards, isSelfPass, myCardAnimView);
        }
        putCardImv.setVisibility(View.GONE);
        putCardImvSingle.setVisibility(GONE);
        passImv.setVisibility(View.GONE);
        promptImv.setVisibility(View.GONE);
        cantMatchImv.setVisibility(View.GONE);
        singleBtnMyTimer.updateTimer(-1);
        threeBtnMyTimer.updateTimer(-1);

        if (isSelfPass) {
            myActionImv.setVisibility(View.VISIBLE);
            myActionImv.setImageResource(R.drawable.pass_txt);
        }
        hideCantMatchLay();
    }

    public void showPrevCallLordAction(int uid, boolean isWantLandLord, boolean isGrab, boolean isCurrentCall) {
        showCallLordAction(prevActionImv, isWantLandLord, isGrab);
        prevTimer.updateTimer(-1);
        if (isCurrentCall) {
            LLGameEffectManager.playCallLordAudio(uid, isWantLandLord, isGrab);
        }
    }

    public void showNextCallLordAction(int uid, boolean isWantLandLord, boolean isGrab, boolean isCurrentCall) {
        showCallLordAction(nextActionImv, isWantLandLord, isGrab);
        nextTimer.updateTimer(-1);
        if (isCurrentCall) {
            LLGameEffectManager.playCallLordAudio(uid, isWantLandLord, isGrab);
        }
    }

    public void showMyCallLordAction(int uid, boolean isGrab, boolean isWant, boolean isCurrentCall) {
        callLordImv.setVisibility(View.GONE);
        noCallImv.setVisibility(View.GONE);
        showCallLordAction(myActionImv, isWant, isGrab);
        myCenterTimer.updateTimer(-1);
        if (isCurrentCall) {
            LLGameEffectManager.playCallLordAudio(uid, isWant, isGrab);
        }
    }

    public void hideAllAction() {
        myActionImv.setVisibility(GONE);
        prevActionImv.setVisibility(GONE);
        nextActionImv.setVisibility(GONE);
    }

    private void showCallLordAction(ImageView view, boolean isWantLandLord, boolean isGrab) {
        view.setVisibility(VISIBLE);

        if (isWantLandLord) {
            view.setImageResource(isGrab ? R.drawable.grab_lord_txt : R.drawable.call_lord_txt);
        } else {
            view.setImageResource(isGrab ? R.drawable.no_grab_txt : R.drawable.no_call_txt);
        }
    }

    public void clearAllCallLordAction() {
        callLordImv.setVisibility(View.GONE);
        noCallImv.setVisibility(View.GONE);
        doubleImv.setVisibility(GONE);
        noDoubleImv.setVisibility(GONE);
        myActionImv.setVisibility(GONE);
        myCenterTimer.setVisibility(GONE);
        prevTimer.setVisibility(GONE);
        nextTimer.setVisibility(GONE);
        nextPutCardLay.removeAllViews();
        prevPutCardLay.removeAllViews();
    }

    public void hideCallLordBtn() {
        callLordImv.setVisibility(GONE);
        noCallImv.setVisibility(GONE);
    }

    public void hideDoubleBtn() {
        doubleImv.setVisibility(GONE);
        noDoubleImv.setVisibility(GONE);
    }

    public void hideActionBtn() {
        hideCallLordBtn();
        putCardImvSingle.setVisibility(GONE);
        putCardImv.setVisibility(GONE);
        promptImv.setVisibility(GONE);
        cantMatchImv.setVisibility(GONE);
        passImv.setVisibility(GONE);
    }

    public void hidePrevAction(long timeLeft) {
        prevActionImv.setVisibility(GONE);

        prevTimer.updateTimer(timeLeft);
        nextTimer.updateTimer(-1);
        myCenterTimer.updateTimer(-1);
    }

    public void hideNextAction(long timeLeft) {
        nextActionImv.setVisibility(GONE);

        prevTimer.updateTimer(-1);
        nextTimer.updateTimer(timeLeft);
        myCenterTimer.updateTimer(-1);
    }

    public void updateMultipleNum(int multiple) {
        multipleNum.setText(multiple + "");
    }

    public void updateBottomCard(final byte[] bottomCards) {
        if (bottomCard1.getRotationY() == 180) {
            CardView[] bottomCardViews = {bottomCard1, bottomCard2, bottomCard3};
            for (int i = 0; i < 3; i++) {
                displayBottomCardAnim(bottomCards, bottomCardViews, i);
            }
        }
    }

    private void displayBottomCardAnim(final byte[] bottomCards, final CardView[] cardViews, final int i) {
        showBottomCardBack();
        ObjectAnimator rotateAnimator = ObjectAnimator.ofFloat(cardViews[i], "rotationY", 0);
        rotateAnimator.setDuration(300);
        rotateAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                Float rotateValue = (Float) animation.getAnimatedValue();
                if (rotateValue <= 90) {
                    cardViews[i].setValue(bottomCards[i], CardView.CARD_TYPE_BOTTOM, false);
                }
            }
        });
        rotateAnimator.start();
    }

    public void updateHandCardNum(int prevSize, int nextSize) {
        prevHandCardImv.setVisibility(prevSize > 0 ? VISIBLE : INVISIBLE);
        prevHandCardNum.setVisibility(prevSize > 0 ? VISIBLE : INVISIBLE);
        nextHandCardImv.setVisibility(nextSize > 0 ? VISIBLE : INVISIBLE);
        nextHandCardNum.setVisibility(nextSize > 0 ? VISIBLE : INVISIBLE);

        prevHandCardNum.setText(String.valueOf(prevSize));
        nextHandCardNum.setText(String.valueOf(nextSize));

        if (prevSize > 2) {
            prevWinAlarmImv.setVisibility(GONE);
        }
        if (nextSize > 2) {
            nextWinAlarmImv.setVisibility(GONE);
        }
    }

    public void onTouchUp(MotionEvent event) {
        presenter.onTouchUp(event);
    }

    public boolean onTouchDown(MotionEvent e) {
        if (bottomControlView != null) bottomControlView.checkHideHotWordsRv(e);
        presenter.onTouchDown(e);
        return false;
    }

    public void onScroll(MotionEvent event) {
        presenter.onScroll(event);
    }

    public void showGameEndPutCard(byte[] prevCards, byte[] nextCards, boolean isChuntian, boolean isFanChuntian) {
        if (prevCards.length > 0) {
            showPrevPutCard(prevCards, false);
        }
        if (nextCards.length > 0) {
            showNextPutCard(nextCards, false);
        }
        if (isChuntian) {
            LLGameEffectManager.playChuntian(gameOverAnimView);
        }
        if (isFanChuntian) {
            LLGameEffectManager.playFanChuntian(gameOverAnimView);
        }

        if (prevCards.length == 0) {
            prevWinAlarmImv.setVisibility(GONE);
            updateFinishPutCardImv(VISIBLE, GONE, GONE);
        } else if (nextCards.length == 0) {
            nextWinAlarmImv.setVisibility(GONE);
            updateFinishPutCardImv(GONE, VISIBLE, GONE);
        } else {
            hideCancelHost();
            updateFinishPutCardImv(GONE, GONE, VISIBLE);
        }
    }

    private void updateFinishPutCardImv(int prevVisible, int nextVisible, int selfVisible) {
        prevFinishPutImv.setVisibility(prevVisible);
        nextFinishPutImv.setVisibility(nextVisible);
        selfFinishPutImv.setVisibility(selfVisible);
    }

    public void hideFinishPutCardImv() {
        updateFinishPutCardImv(GONE, GONE, GONE);
    }

    public void showGameEndDialog() {
        gameOverView.setVisibility(VISIBLE);
        gameOverView.show(presenter.getGameOverData());
    }

    public void clearGameScene() {
        prevPutCardLay.removeAllViews();
        nextPutCardLay.removeAllViews();
        myCardContainer.removeAllViews();
        prevActionImv.setVisibility(GONE);
        nextActionImv.setVisibility(GONE);
        myActionImv.setVisibility(GONE);
        showPrepareBtn();
        bottomCard1.setVisibility(GONE);
        bottomCard2.setVisibility(GONE);
        bottomCard3.setVisibility(GONE);
        bottomCard1.setDisplayBack();
        bottomCard2.setDisplayBack();
        bottomCard3.setDisplayBack();
        prevHandCardImv.setVisibility(GONE);
        prevHandCardNum.setVisibility(GONE);
        nextHandCardImv.setVisibility(GONE);
        nextHandCardNum.setVisibility(GONE);
        prevHostIcon.setVisibility(GONE);
        nextHostIcon.setVisibility(GONE);
        hideCancelHost();
        callLordImv.setVisibility(GONE);
        noCallImv.setVisibility(GONE);
        doubleImv.setVisibility(GONE);
        noDoubleImv.setVisibility(GONE);
        presenter.clearMyPutCardView();
        presenter.clearMyHandCardViews();
        hideLandLordMark();
        hideFinishPutCardImv();
        prevWinAlarmImv.setVisibility(GONE);
        nextWinAlarmImv.setVisibility(GONE);
        nextTimer.updateTimer(-1);
        prevTimer.updateTimer(-1);
        threeBtnMyTimer.updateTimer(-1);
        singleBtnMyTimer.updateTimer(-1);
        myCenterTimer.updateTimer(-1);
        multipleNum.setText(String.valueOf(0));
    }

    private void showPrepareBtn() {
        prepareGameImv.setVisibility(!presenter.isWatcher() ? VISIBLE : GONE);
        prepareGameImv.setImageResource(R.drawable.ll_prepare_btn);
    }

    public void updateHostStatus(boolean isSelfHost, boolean isPrevHost, boolean isNextHost) {
        cancelHostImv.setVisibility((isSelfHost && !presenter.isWatcher()) ? VISIBLE : GONE);
        prevHostIcon.setVisibility(isPrevHost ? VISIBLE : GONE);
        nextHostIcon.setVisibility(isNextHost ? VISIBLE : GONE);
        bottomControlView.updateHostStatus(isSelfHost);
    }

    public void hideCancelHost() {
        cancelHostImv.setVisibility(GONE);
    }

    public float getPutLayY() {
        if (myPutCardLayY == 0) {
            myPutCardLayY = ScreenUtil.dip2px(314 * scale);
        }
        return myPutCardLayY;
    }

    public void addCardToPut(CardView cardViewNew, LayoutParams layoutParams) {
        myCardContainer.addView(cardViewNew, layoutParams);
    }

    public void removeHandCardView(CardView cardView) {
        myCardContainer.removeView(cardView);
    }

    public void hidePutCardBtn() {
        putCardImvSingle.setVisibility(GONE);
    }

    private Handler myChatHandler = new Handler();
    private Handler prevChatHandler = new Handler();
    private Handler nextChatHandler = new Handler();

    public void showMyChatMsg(String msg, boolean isHotWordMsg) {
        showChatMsg(msg, myChatLay, myChatTv, myChatHandler);
        if (isHotWordMsg) {
            LLGameEffectManager.playHotWordsSound(msg, presenter.getSelfUid());
        }
    }

    public void showPrevChatMsg(String msg, boolean isHotWordMsg) {
        showChatMsg(msg, prevChatLay, prevChatTv, prevChatHandler);
        if (isHotWordMsg) {
            LLGameEffectManager.playHotWordsSound(msg, presenter.getPrevUid());
        }
    }

    public void showNextChatMsg(String msg, boolean isHotWordMsg) {
        showChatMsg(msg, nextChatLay, nextChatTv, nextChatHandler);
        if (isHotWordMsg) {
            LLGameEffectManager.playHotWordsSound(msg, presenter.getNextUid());
        }
    }

    private void showChatMsg(String msg, final FrameLayout layout, TextView tv, Handler handler) {
        handler.removeCallbacksAndMessages(null);
        layout.setVisibility(VISIBLE);
        tv.setText(msg);
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                layout.setVisibility(GONE);
            }
        }, 3000);
    }

    public void checkShowMyWinAlarm(int selfUid, int size) {
        if (size <= 2) {
            LLGameEffectManager.playWinAlarmAudio(selfUid, size);
        }
    }

    public void checkShowPrevWinAlarm(int prevUid, int size) {
        if (size <= 2) {
            LLGameEffectManager.playWinAlarmAudio(prevUid, size);
            if (prevWinAlarmImv.getVisibility() == GONE) {
                prevWinAlarmImv.setVisibility(VISIBLE);
                prevWinAlarmAnim.restartAnimation();
            }
        }
    }

    public void checkShowNextWinAlarm(int nextUid, int size) {
        if (size <= 2) {
            LLGameEffectManager.playWinAlarmAudio(nextUid, size);
            if (nextWinAlarmImv.getVisibility() == GONE) {
                nextWinAlarmImv.setVisibility(VISIBLE);
                nextWinAlarmAnim.restartAnimation();
            }
        }
    }

    public void updateNextPlayerTimer(long timeLeft) {
        nextPutCardLay.removeAllViews();
        nextActionImv.setVisibility(GONE);
        nextTimer.updateTimer(timeLeft);
    }

    public void hideTicketLay() {
        ticketImv.setVisibility(GONE);
        ticketTv.setVisibility(GONE);
        mostWinTv.setVisibility(GONE);
    }

    @Override
    public void updateRoomInfo(FixRoomInfo fixRoomInfo) {
        presenter.updateRoomInfo(fixRoomInfo);
    }

    public void updatePrevUserInfo(int prevUid, boolean ready, int bean_count) {
        prevActionImv.setVisibility((ready && !presenter.isGaming()) ? VISIBLE : GONE);
        prevActionImv.setImageResource(R.drawable.prepared_txt);
        updateUserInfo(prevUid, prevEmptySeatTv, prevName, prevBeanNum, prevBeanIcon, prevUserHead, bean_count);
    }

    public void updateSelfUserInfo(int selfUid, boolean ready, int bean_count) {
        prepareGameImv.setVisibility((!presenter.isWatcher() && !presenter.isGaming()) ? VISIBLE : GONE);
        prepareGameImv.setImageResource(ready ? R.drawable.ll_cancel_prepare : R.drawable.ll_prepare_btn);
        selfPrepareActionImv.setVisibility((presenter.isWatcher() && ready && !presenter.isGaming()) ? VISIBLE : GONE);
        updateUserInfo(selfUid, myEmptySeatTv, myName, myBeanNum, myBeanIcon, myHeadImv, bean_count);
    }

    public void updateNextUserInfo(int nextUid, boolean ready, int bean_count) {
        nextActionImv.setVisibility((ready && !presenter.isGaming()) ? VISIBLE : GONE);
        nextActionImv.setImageResource(R.drawable.prepared_txt);
        updateUserInfo(nextUid, nextEmptySeatTv, nextName, nextBeanNum, nextBeanIcon, nextUserHead, bean_count);
    }

    public void hidePrepareBtn() {
        prepareGameImv.setVisibility(GONE);
        selfPrepareActionImv.setVisibility(GONE);
    }

    public void finishReady() {
        prepareGameImv.setImageResource(R.drawable.ll_cancel_prepare);
    }

    public void finishUnReady() {
        prepareGameImv.setImageResource(R.drawable.ll_prepare_btn);
    }

    public void showBottomCardBack() {
        bottomCard1.setVisibility(VISIBLE);
        bottomCard2.setVisibility(VISIBLE);
        bottomCard3.setVisibility(VISIBLE);
        bottomCard1.setDisplayBack();
        bottomCard2.setDisplayBack();
        bottomCard3.setDisplayBack();
    }

    public void updateBottomView(FixRoomInfo fixRoomInfo) {
        bottomControlView.updateRoomInfo(fixRoomInfo);
    }

    public void showPrevUserEmptySeat() {
        prevEmptySeatTv.setVisibility(VISIBLE);
        prevBeanNum.setVisibility(GONE);
        prevBeanIcon.setVisibility(GONE);
        prevUserHead.setImageResource(R.drawable.ll_empty_seat);
        prevUserHead.setBorderWidth(0);
        prevActionImv.setVisibility(GONE);
        prevName.setVisibility(GONE);
    }

    public void showNextUserEmptySeat() {
        nextEmptySeatTv.setVisibility(VISIBLE);
        nextBeanNum.setVisibility(GONE);
        nextBeanIcon.setVisibility(GONE);
        nextUserHead.setImageResource(R.drawable.ll_empty_seat);
        nextUserHead.setBorderWidth(0);
        nextName.setVisibility(GONE);
        nextActionImv.setVisibility(GONE);
    }

    public void showMyEmptySeat() {
        myName.setVisibility(GONE);
        myBeanNum.setVisibility(GONE);
        myBeanIcon.setVisibility(GONE);
        myEmptySeatTv.setVisibility(VISIBLE);
        prepareGameImv.setVisibility(GONE);
        selfPrepareActionImv.setVisibility(GONE);
        myHeadImv.setImageResource(R.drawable.ll_empty_seat);
        myHeadImv.setBorderWidth(0);
    }

    public void showSitToPrevAnim(int selfUid) {
        updatePrevUserInfo(selfUid, false, presenter.getPrevSeatInfo().getBean_count());

        ObjectAnimator prevAnimator = ObjectAnimator.ofFloat(prevUserHead, "translationY", selfHeadY - prevHeadY);
        prevAnimator.setDuration(300);
        ObjectAnimator nextXAnimator = ObjectAnimator.ofFloat(nextUserHead, "translationX", prevHeadX - nextHeadX);
        nextXAnimator.setDuration(300);
        ObjectAnimator nextYAnimator = ObjectAnimator.ofFloat(nextUserHead, "translationY", prevHeadY - nextHeadY);
        nextYAnimator.setDuration(300);
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(prevAnimator, nextXAnimator, nextYAnimator);
        animatorSet.setStartDelay(500);
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                resetHeadTranslation();
                nextEmptySeatTv.setVisibility(VISIBLE);
                nextUserHead.setImageResource(R.drawable.ll_empty_seat);

                updatePrevUserInfo(presenter.getPrevUid(), presenter.isPrevReady(), presenter.getPrevSeatInfo().getBean_count());
                updateSelfUserInfo(presenter.getSelfUid(), false, presenter.getSelfSeatInfo().getBean_count());
                showPrepareBtn();

                presenter.setAnimingChangeSeat(false);
            }

            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
                hideAllSeatState();
            }
        });
        animatorSet.start();
    }

    public void showSitToNextAnim(int selfUid) {
        updateNextUserInfo(selfUid, false, presenter.getNextSeatInfo().getBean_count());

        ObjectAnimator prevAnimator = ObjectAnimator.ofFloat(prevUserHead, "translationX", nextHeadX - prevHeadX);
        prevAnimator.setDuration(300);
        ObjectAnimator nextXAnimator = ObjectAnimator.ofFloat(nextUserHead, "translationX", selfHeadX - nextHeadX);
        nextXAnimator.setDuration(300);
        ObjectAnimator nextYAnimator = ObjectAnimator.ofFloat(nextUserHead, "translationY", selfHeadY - nextHeadY);
        nextYAnimator.setDuration(300);
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(prevAnimator, nextXAnimator, nextYAnimator);
        animatorSet.setStartDelay(500);
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                resetHeadTranslation();
                prevEmptySeatTv.setVisibility(VISIBLE);
                prevUserHead.setImageResource(R.drawable.ll_empty_seat);

                updateNextUserInfo(presenter.getNextUid(), presenter.isNextReady(), presenter.getNextSeatInfo().getBean_count());
                updateSelfUserInfo(presenter.getSelfUid(), false, presenter.getSelfSeatInfo().getBean_count());
                showPrepareBtn();

                presenter.setAnimingChangeSeat(false);
            }

            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
                hideAllSeatState();
            }
        });
        animatorSet.start();
    }

    private void hideAllSeatState() {
        prevName.setVisibility(GONE);
        prevBeanIcon.setVisibility(GONE);
        prevBeanNum.setVisibility(GONE);
        nextName.setVisibility(GONE);
        nextBeanIcon.setVisibility(GONE);
        nextBeanNum.setVisibility(GONE);
        nextActionImv.setVisibility(GONE);
        prevActionImv.setVisibility(GONE);
        myEmptySeatTv.setVisibility(GONE);
    }

    private void resetHeadTranslation() {
        prevUserHead.setTranslationX(0);
        prevUserHead.setTranslationY(0);
        nextUserHead.setTranslationX(0);
        nextUserHead.setTranslationY(0);
        myHeadImv.setTranslationX(0);
        myHeadImv.setTranslationY(0);
    }

    public void updateWatcherHandCardNum(int length) {
        for (ImageView backCard : myBackCardViewList) {
            myCardContainer.removeView(backCard);
        }
        for (CardView cardView : presenter.getMyHandCardViews()) {
            myCardContainer.removeView(cardView);
        }
        presenter.clearMyPutCardView();

        myBackCardViewList.clear();
        for (int i = 0; i < length; i++) {
            Point coord = CardResUtil.getHandCardsPostion(length, i);

            ImageView backCardView = new ImageView(getContext());
            backCardView.setImageResource(R.drawable.big_card_back);
            LayoutParams layoutParams = new LayoutParams(ScreenUtil.dip2px(72 * scale), ScreenUtil.dip2px(96 * scale));
            layoutParams.leftMargin = coord.x;
            layoutParams.topMargin = coord.y;

            myCardContainer.addView(backCardView, layoutParams);
            myBackCardViewList.add(backCardView);
        }
    }

    public void updateRoomInfoView(boolean gaming) {
        ticketTv.setVisibility(gaming ? GONE : VISIBLE);
        mostWinTv.setVisibility(gaming ? GONE : VISIBLE);
        ticketImv.setVisibility(gaming ? GONE : VISIBLE);

        ticketTv.setText(presenter.getTicketNum());
        mostWinTv.setText(StringUtil.formatDouDouLong(presenter.getMostWin()));
        startPoints.setText(presenter.getBaseScore() + "分");
        roomNameTv.setText(presenter.getRoomName());
        roomLevelTv.setText(presenter.getLevelName());
    }

    public void showBarrageView(String content, int type, int uid) {
        barrageView.startAnim(content, type, uid);
    }

    public void clearOthersPutCardView() {
        prevPutCardLay.removeAllViews();
        nextPutCardLay.removeAllViews();
    }

    public void hideGameOverDialog() {
        gameOverView.setVisibility(GONE);
    }

    public void showLandLordLogoAnim(final int landLordIndex) {
        isLandlordLogoAniming = true;
        float translationX = 0;
        float translationY = 0;
        if (landLordIndex == 0) {
            translationX = selfHatX - centerHatX;
            translationY = selfHatY - centerHatY;
        } else if (landLordIndex == 1) {
            translationX = nextHatX - centerHatX;
            translationY = nextHatY - centerHatY;
        } else {
            translationX = prevHatX - centerHatX;
            translationY = prevHatY - centerHatY;
        }

        CardAnimUtil.showLandLordLogoAnim(centerLandlordMark, landlordLightAnim, landlordLightBottom,
                translationX, translationY, new CardAnimUtil.AnimEndCallback() {
                    @Override
                    public void onAnimEnd() {
                        isLandlordLogoAniming = false;
                        updateLandLordLogo(landLordIndex);
                    }
                });
    }

    public void updateLandLordLogo(int landLordIndex) {
        if (isLandlordLogoAniming) return;

        if (landLordIndex == 0) {
            selfLandLordMark.setVisibility(VISIBLE);
            prevLandLordMark.setVisibility(GONE);
            nextLandLordMark.setVisibility(GONE);
        } else if (landLordIndex == 1) {
            selfLandLordMark.setVisibility(GONE);
            prevLandLordMark.setVisibility(GONE);
            nextLandLordMark.setVisibility(VISIBLE);
        } else if (landLordIndex == 2) {
            selfLandLordMark.setVisibility(GONE);
            prevLandLordMark.setVisibility(VISIBLE);
            nextLandLordMark.setVisibility(GONE);
        }
    }

    public void hideLandLordMark() {
        selfLandLordMark.setVisibility(GONE);
        prevLandLordMark.setVisibility(GONE);
        nextLandLordMark.setVisibility(GONE);
    }

    private Handler selfSpeakerHandler = new Handler();
    private Handler prevSpeakerHandler = new Handler();
    private Handler nextSpeakerHandler = new Handler();

    public void updateSpeakerView(List<SpeakerInfo> speakerInfos) {
        for (SpeakerInfo speakerInfo : speakerInfos) {
            if (speakerInfo.volume <= 0) continue;

            if (speakerInfo.uid == presenter.getSelfUid()) {
                showSpeakerIcon(selfSpeakerIcon, selfSpeakerHandler);
            } else if (speakerInfo.uid == presenter.getNextUid()) {
                showSpeakerIcon(nextSpeakerIcon, nextSpeakerHandler);
            } else if (speakerInfo.uid == presenter.getPrevUid()) {
                showSpeakerIcon(prevSpeakerIcon, prevSpeakerHandler);
            }
        }
    }

    private void showSpeakerIcon(final ImageView speakerIcon, Handler handler) {
        handler.removeCallbacksAndMessages(null);
        speakerIcon.setVisibility(VISIBLE);
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                speakerIcon.setVisibility(GONE);
            }
        }, 1500);
    }

    public void showMultipleAnim(long animDelayTime, final int multiple) {
        ObjectAnimator upAnimator = ObjectAnimator.ofFloat(multipleAnimImv, "translationY", -ScreenUtil.dip2px(8)).setDuration(330);
        upAnimator.setInterpolator(new AccelerateInterpolator());
        ObjectAnimator downAnimator = ObjectAnimator.ofFloat(multipleAnimImv, "translationY", 0).setDuration(170);
        downAnimator.setInterpolator(new DecelerateInterpolator());
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(multipleAnimImv, "alpha", 1.0f).setDuration(330);

        AnimatorSet translationAnimator = new AnimatorSet();
        translationAnimator.playSequentially(upAnimator, downAnimator);
        translationAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                multipleAnimImv.setAlpha(0.0f);
                updateMultipleNum(multiple);
            }
        });
        AnimatorSet totalAnimator = new AnimatorSet();
        totalAnimator.playTogether(translationAnimator, alphaAnimator);
        totalAnimator.setStartDelay(animDelayTime);
        totalAnimator.start();
    }

    public void updateDoubleStatus(boolean isSelfActioned, boolean isSelfDouble, boolean isNextActioned,
                                   boolean isNextDouble, boolean isPrevActioned, boolean isPrevDouble,
                                   long timeLeft, boolean isCurrentSelfAction, boolean isCurrentNextAction, boolean isCurrentPrevAction) {
        myActionImv.setVisibility(isSelfActioned ? VISIBLE : GONE);
        nextActionImv.setVisibility(isNextActioned ? VISIBLE : GONE);
        prevActionImv.setVisibility(isPrevActioned ? VISIBLE : GONE);
        myActionImv.setImageResource(isSelfDouble ? R.drawable.ll_double_txt : R.drawable.ll_no_double_txt);
        nextActionImv.setImageResource(isNextDouble ? R.drawable.ll_double_txt : R.drawable.ll_no_double_txt);
        prevActionImv.setImageResource(isPrevDouble ? R.drawable.ll_double_txt : R.drawable.ll_no_double_txt);
        doubleImv.setVisibility(presenter.isWatcher() || isSelfActioned ? GONE : VISIBLE);
        noDoubleImv.setVisibility(presenter.isWatcher() || isSelfActioned ? GONE : VISIBLE);
        myCenterTimer.updateTimer(isSelfActioned ? -1 : timeLeft);

        if (isCurrentSelfAction) {
            LLGameEffectManager.playDoubleSound(presenter.getSelfUid(), isSelfDouble);
        }
        if (isCurrentPrevAction) {
            LLGameEffectManager.playDoubleSound(presenter.getPrevUid(), isPrevDouble);
        }
        if (isCurrentNextAction) {
            LLGameEffectManager.playDoubleSound(presenter.getNextUid(), isNextDouble);
        }
    }

    public void updateSelfDoudouNum() {
        setDoudouNum(LoginHelper.getDouDou(), myBeanNum);
    }

    @Override
    public void onPause(boolean isFinish) {
        if (presenter != null) presenter.pauseBgm();
    }

    @Override
    public void onResume() {
        if (presenter != null) presenter.resumeBgm();
    }

    @Override
    public void hideDialog() {
        if (popup != null) popup.dismiss();
        if (dialog != null) dialog.dismiss();
        if (bottomControlView != null) bottomControlView.hideAllDialog();
    }

    @Override
    public void onPushRoomMsg(List<FixRoomMsg> chatMsgs) {
        presenter.onPushGameMsg(chatMsgs);
    }

    @Override
    public void onSelfSendMsg(FixRoomMsg msg) {
        presenter.onSendMsg(msg);
    }

    @Override
    public boolean consumeGift(GiftShowInfo showInfo) {
        return false;
    }

    private SocialGameMsgCallback callback;

    @Override
    public void setCallback(SocialGameMsgCallback callback) {
        if (bottomControlView != null) {
            bottomControlView.setCallback(callback);
        }
        this.callback = callback;
    }

    @Override
    public void listRefresh() {

    }

    @Override
    public void onSpeak(List<SpeakerInfo> speakerInfos) {
        updateSpeakerView(speakerInfos);
    }

    @Override
    public List<FixRoomMsg> getPublicScreenMsg() {
        return new ArrayList<>();
    }

    @Nullable
    @Override
    public Object getGameInfo() {
        return null;
    }
}
