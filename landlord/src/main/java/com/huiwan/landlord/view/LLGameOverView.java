package com.huiwan.landlord.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.view.animation.RotateAnimation;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.huiwan.landlord.R;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.user.UserService;
import com.huiwan.user.UserSimpleInfoCallback;
import com.huiwan.landlord.model.LLGameOverData;
import com.wepie.wespy.net.tcp.packet.LandLordPackets;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TouchEffectUtil;

import java.util.List;

public class LLGameOverView extends RelativeLayout {
    private ImageView light;
    private ImageView bg;
    private ImageView title;
    private TextView name1;
    private TextView points1;
    private TextView multiple1;
    private TextView bean1;
    private TextView name2;
    private TextView points2;
    private TextView multiple2;
    private TextView bean2;
    private TextView name3;
    private TextView points3;
    private TextView multiple3;
    private TextView bean3;
    private ImageView close;
    private ImageView oneMoreGame;
    private ImageView landLordIcon1;
    private ImageView landLordIcon2;
    private ImageView landLordIcon3;
    private RotateAnimation rotateAnimation;
    private ImageView closeCenter;
    private ImageView mask;

    public LLGameOverView(Context context) {
        super(context);
        init();
    }

    public LLGameOverView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.ll_game_over_view, this);
        initView();
    }

    private void initView() {
        light = (ImageView) findViewById(R.id.light);
        bg = (ImageView) findViewById(R.id.bg);
        title = (ImageView) findViewById(R.id.title);
        name1 = (TextView) findViewById(R.id.name_1);
        points1 = (TextView) findViewById(R.id.points_1);
        multiple1 = (TextView) findViewById(R.id.multiple_1);
        bean1 = (TextView) findViewById(R.id.bean_1);
        name2 = (TextView) findViewById(R.id.name_2);
        points2 = (TextView) findViewById(R.id.points_2);
        multiple2 = (TextView) findViewById(R.id.multiple_2);
        bean2 = (TextView) findViewById(R.id.bean_2);
        name3 = (TextView) findViewById(R.id.name_3);
        points3 = (TextView) findViewById(R.id.points_3);
        multiple3 = (TextView) findViewById(R.id.multiple_3);
        bean3 = (TextView) findViewById(R.id.bean_3);
        close = (ImageView) findViewById(R.id.close);
        mask = (ImageView) findViewById(R.id.mask);
        oneMoreGame = (ImageView) findViewById(R.id.one_more_game);
        landLordIcon1 = (ImageView) findViewById(R.id.land_lord_icon_1);
        landLordIcon2 = (ImageView) findViewById(R.id.land_lord_icon_2);
        landLordIcon3 = (ImageView) findViewById(R.id.land_lord_icon_3);
        closeCenter = findViewById(R.id.close_center);
        rotateAnimation = new RotateAnimation(0, 360, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        rotateAnimation.setDuration(8 * 1000);
        rotateAnimation.setInterpolator(new LinearInterpolator());
        rotateAnimation.setRepeatCount(Animation.INFINITE);

        LayoutParams layoutParams = (LayoutParams) bg.getLayoutParams();
        layoutParams.topMargin = (int) (ScreenUtil.getScreenHeight() * 0.33);
        bg.setLayoutParams(layoutParams);

        TouchEffectUtil.addLandLordTouchEffect(oneMoreGame);
        TouchEffectUtil.addLandLordTouchEffect(close);
        oneMoreGame.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                hide();
            }
        });
        close.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                hide();
            }
        });
        closeCenter.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                hide();
            }
        });
        mask.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
            }
        });
    }

    private void hide() {
        setVisibility(GONE);
        stopLightAnim();
    }

    public void show(LLGameOverData gameOverData) {
        setVisibility(VISIBLE);
        if (gameOverData.isSelfWin) {
            light.setImageResource(R.drawable.ll_win_light);
            title.setImageResource(R.drawable.ll_win_title);
            startLightAnim();

//            IceSoundUtil.getInstance().playSound(IceSoundUtil.TYPE_ICE_BALL_WIN);
        } else {
            light.setImageResource(R.drawable.ll_lose_light);
            title.setImageResource(R.drawable.ll_lose_title);

//            IceSoundUtil.getInstance().playSound(IceSoundUtil.TYPE_ICE_BALL_FAIL);
        }
        if (gameOverData.isWatcher) {
            closeCenter.setVisibility(VISIBLE);
            close.setVisibility(GONE);
            oneMoreGame.setVisibility(GONE);
        } else {
            closeCenter.setVisibility(GONE);
            close.setVisibility(VISIBLE);
            oneMoreGame.setVisibility(VISIBLE);
        }
        points1.setText(String.valueOf(gameOverData.points));
        points2.setText(String.valueOf(gameOverData.points));
        points3.setText(String.valueOf(gameOverData.points));

        List<LandLordPackets.LLGameCheckout> checkoutList = gameOverData.checkoutList;
        List<LandLordPackets.LLRoleType> roleTypeList = gameOverData.roleTypeList;
        if (roleTypeList.get(0).getNumber() == gameOverData.winRole) {
            bean1.setText("+" + checkoutList.get(0).getDoudou());
        } else {
            bean1.setText(String.valueOf(checkoutList.get(0).getDoudou()));
        }
        if (roleTypeList.get(1).getNumber() == gameOverData.winRole) {
            bean2.setText("+" + checkoutList.get(1).getDoudou());
        } else {
            bean2.setText(String.valueOf(checkoutList.get(1).getDoudou()));
        }
        if (roleTypeList.get(2).getNumber() == gameOverData.winRole) {
            bean3.setText("+" + checkoutList.get(2).getDoudou());
        } else {
            bean3.setText(String.valueOf(checkoutList.get(2).getDoudou()));
        }
        if (roleTypeList.get(0).getNumber() == LandLordPackets.LLRoleType.LANDLORD_VALUE) {
            landLordIcon1.setVisibility(VISIBLE);
            multiple1.setText("X" + String.valueOf(gameOverData.getMultiple(checkoutList.get(0).getUid())));
        } else {
            landLordIcon1.setVisibility(GONE);
            multiple1.setText("X" + String.valueOf(gameOverData.getMultiple(checkoutList.get(0).getUid())));
        }
        if (roleTypeList.get(1).getNumber() == LandLordPackets.LLRoleType.LANDLORD_VALUE) {
            landLordIcon2.setVisibility(VISIBLE);
            multiple2.setText("X" + String.valueOf(gameOverData.getMultiple(checkoutList.get(1).getUid())));
        } else {
            landLordIcon2.setVisibility(GONE);
            multiple2.setText("X" + String.valueOf(gameOverData.getMultiple(checkoutList.get(1).getUid())));
        }
        if (roleTypeList.get(2).getNumber() == LandLordPackets.LLRoleType.LANDLORD_VALUE) {
            landLordIcon3.setVisibility(VISIBLE);
            multiple3.setText("X" + String.valueOf(gameOverData.getMultiple(checkoutList.get(2).getUid())));
        } else {
            landLordIcon3.setVisibility(GONE);
            multiple3.setText("X" + String.valueOf(gameOverData.getMultiple(checkoutList.get(2).getUid())));
        }
        UserService.get().getCacheSimpleUser(checkoutList.get(0).getUid(), new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                name1.setText(simpleInfo.nickname);
            }

            @Override
            public void onUserInfoFailed(String description) {
            }
        });
        UserService.get().getCacheSimpleUser(checkoutList.get(1).getUid(), new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                name2.setText(simpleInfo.nickname);
            }

            @Override
            public void onUserInfoFailed(String description) {
            }
        });
        UserService.get().getCacheSimpleUser(checkoutList.get(2).getUid(), new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                name3.setText(simpleInfo.nickname);
            }

            @Override
            public void onUserInfoFailed(String description) {
            }
        });
    }

    private void startLightAnim() {
        stopLightAnim();
        light.setAnimation(rotateAnimation);
        rotateAnimation.start();
    }

    private void stopLightAnim() {
        rotateAnimation.cancel();
        light.clearAnimation();
    }
}
