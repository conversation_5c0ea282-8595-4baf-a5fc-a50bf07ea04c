package com.huiwan.landlord.model;

import com.google.protobuf.ByteString;
import com.huiwan.landlord.manage.CardTypeManager;
import com.wepie.wespy.net.tcp.packet.LandLordPackets;

import java.util.ArrayList;
import java.util.List;

public class LLGameState {
    public long timeLeft;
    public int currentGamer;
    public LandLordPackets.LLGameState gameState;
    public byte[] bottomCards;
    public List<LandLordPackets.LLGamerInfo> gamerInfos = new ArrayList<>();
    public int betLevel;
    public List<LandLordPackets.LLGameCheckout> checkout;
    public int winRole;
    public int controlGamer; // 上一轮牌最大，当前一定需要出牌的控手玩家
    public List<List<Card>> promptCards;
    public int stateChangeCause;
    public int actionUid;
    public boolean isMultiple;
    public boolean isSpring;


    public static LLGameState parseFromProto(LandLordPackets.LLGameInfo gameInfo, int stateChangeCause, int actionUid, boolean multiple) {
        LLGameState gameState = new LLGameState();
        gameState.timeLeft = gameInfo.getTimeLeft();
        gameState.currentGamer = gameInfo.getCurrentGamer();
        gameState.gameState = gameInfo.getGameState();
        gameState.gamerInfos.addAll(gameInfo.getGamersList());
        gameState.betLevel = gameInfo.getLevelValue();
        gameState.bottomCards = gameInfo.getBottomCards().toByteArray();
        gameState.checkout = gameInfo.getGameCheckoutList();
        gameState.winRole = gameInfo.getWinRoleValue();
        gameState.controlGamer = gameInfo.getControlGamer();
        gameState.stateChangeCause = stateChangeCause;
        gameState.actionUid = actionUid;
        gameState.isMultiple = multiple;
        gameState.isSpring = gameInfo.getIsSpring();
        gameState.promptCards = new ArrayList<>();

        List<ByteString> promptCardsString = gameInfo.getPromptCardsList();
        if (promptCardsString != null && promptCardsString.size() > 0) {
            for (ByteString byteString : promptCardsString) {
                byte [] cards = byteString.toByteArray();
                List<Card> cardList = CardTypeManager.parseFromBytes(cards);
                gameState.promptCards.add(cardList);
            }
        }

        return gameState;
    }
}
