package com.huiwan.landlord.model;

import com.wepie.wespy.net.tcp.packet.LandLordPackets;

import java.util.List;

public class LLGameOverData {
    public boolean isSelfWin;
    public List<LandLordPackets.LLGameCheckout> checkoutList;
    public List<LandLordPackets.LLRoleType> roleTypeList;
    public boolean isWatcher;
    public int winRole;
    public List<LandLordPackets.LLGamerInfo> gamerInfos;
    public int points;

    public int getMultiple(int uid) {
        if (gamerInfos == null) return 0;
        for (LandLordPackets.LLGamerInfo gamerInfo : gamerInfos) {
            if (gamerInfo.getUid() == uid) {
                return (int) gamerInfo.getMultiple();
            }
        }
        return 0;
    }
}
