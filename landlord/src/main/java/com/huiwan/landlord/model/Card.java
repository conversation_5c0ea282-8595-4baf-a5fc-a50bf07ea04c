package com.huiwan.landlord.model;

public class Card {
    // 一张牌的大类型
    public enum CardBigType {
        HEI_TAO, HONG_TAO, MEI_HUA, FANG_KUAI, XIAO_WANG, DA_WANG
    }

    public byte value;

    // 牌的大类型，方块，梅花,红桃,黑桃,小王,大王
    public CardBigType bigType;

    // 牌的等级，对牌进行排序时会用到
    public int grade;

    public Card(byte value) {
        this.value = value;
        this.grade = getGrade(value);
        this.bigType = getBigType(value);
    }

    /**
     * 根据牌的value获得一张牌的大类型：方块，梅花,红桃,黑桃,小王,大王
     *
     * @param value
     *            牌的value
     *
     * @return 牌的大类型：方块，梅花,红桃,黑桃,小王,大王
     */
    public static CardBigType getBigType(byte value) {
        CardBigType bigType = null;
        if (value >= 0x02 && value <= 0x0e) {
            bigType = CardBigType.FANG_KUAI;
        } else if (value >= 0x12 && value <= 0x1e) {
            bigType = CardBigType.MEI_HUA;
        } else if (value >= 0x22 && value <= 0x2e) {
            bigType = CardBigType.HONG_TAO;
        } else if (value >= 0x32 && value <= 0x3e) {
            bigType = CardBigType.HEI_TAO;
        } else if (value == 0x4f) {
            bigType = CardBigType.XIAO_WANG;
        } else if (value == 0x5f) {
            bigType = CardBigType.DA_WANG;
        }
        return bigType;
    }

    /**
     * 根据牌的value，获得一张牌的等级
     *
     * @param value
     *            牌的value
     * @return 与牌数字对应的等级
     */
    public static int getGrade(byte value) {

        if ((value >= 0x02 && value <= 0x0e) || (value >= 0x12 && value <= 0x1e) ||
                (value >= 0x22 && value <= 0x2e) || (value >= 0x32 && value <= 0x3e) ||
                (value == 0x4f) || (value == 0x5f)) {
            // do nothing
        } else {
//            throw new RuntimeException("牌不合法");
        }

        int grade = 0;

        // 2个王必须放在前边判断
        if (value == 0x4f) {
            grade = 14;
        } else if (value == 0x5f) {
            grade = 15;
        }

        else {
            String valueString = Integer.toHexString(value & 0xFF);
            if (valueString.length() == 1) {
                StringBuilder sb = new StringBuilder();
                valueString = sb.append("0").append(valueString).toString();
            }
            grade = (Integer.parseInt(valueString.substring(1, 2), 16) - 2) % 15;
            // 2在最前面
            if (grade == 0) grade = 13;
        }

        return grade;
    }
}
