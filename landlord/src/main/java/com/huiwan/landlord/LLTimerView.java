package com.huiwan.landlord;

import android.content.Context;
import android.os.Handler;
import android.util.AttributeSet;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.huiwan.base.util.SoundPoolUtil;
import com.huiwan.landlord.i.ILandlordPresenter;

public class LLTimerView extends RelativeLayout {
    private TextView leftTimeView;
    private final Handler handler = new Handler();
    private ILandlordPresenter presenter;

    public LLTimerView(Context context) {
        super(context);
        init();
    }

    public LLTimerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.ll_timer_view, this);
        initView();
    }

    private void initView() {
        leftTimeView = (TextView) findViewById(R.id.left_time);
    }

    public void updateTimer(long time) {
        setVisibility(VISIBLE);
        handler.removeCallbacksAndMessages(null);
        if (time == -1) {
            setVisibility(GONE);
            return;
        }
        int leftTime = (int) (time/1000);
        timerCountDown(leftTime, false);
    }

    private void timerCountDown(final int leftTime, final boolean autoPass) {
        if (leftTime < 0) {
            if (autoPass) {
                if (presenter != null) presenter.pass();
            }
            setVisibility(GONE);
            return;
        }
        if (leftTime == 5) {
            playAlarmSoundEffect();
        }
        leftTimeView.setText(String.valueOf(leftTime));
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                timerCountDown(leftTime - 1, autoPass);
            }
        }, 1000);
    }

    private void playAlarmSoundEffect() {
        SoundPoolUtil.getInstance().playRaw(R.raw.ll_speak);
    }

    public void updateCantMatchTimer(long timeLeft) {
        if (timeLeft > 10000) timeLeft = 10000;
        setVisibility(VISIBLE);
        handler.removeCallbacksAndMessages(null);
        if (timeLeft == -1) {
            setVisibility(GONE);
            return;
        }
        int leftTime = (int) (timeLeft/1000);
        timerCountDown(leftTime, true);
    }

    public void setPresenter(ILandlordPresenter presenter) {
        this.presenter = presenter;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        handler.removeCallbacksAndMessages(null);
    }
}
