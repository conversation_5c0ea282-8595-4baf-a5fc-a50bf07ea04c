package com.huiwan.landlord;

import android.content.Context;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.huiwan.base.util.TouchEffectUtil;

public class LLDialogView extends RelativeLayout {
    private TextView titleTv;
    private TextView contentTv;
    private ImageView noBtn;
    private ImageView yesBtn;

    public LLDialogView(Context context) {
        super(context);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.ll_close_freetalk_dialog, this);
        initView();
    }

    private void initView() {
        titleTv = (TextView) findViewById(R.id.title);
        contentTv = (TextView) findViewById(R.id.content_tv);
        noBtn = (ImageView) findViewById(R.id.no_btn);
        yesBtn = (ImageView) findViewById(R.id.yes_btn);

        TouchEffectUtil.addLandLordTouchEffect(yesBtn);
        TouchEffectUtil.addLandLordTouchEffect(noBtn);
    }

    public void setTitle(String title) {
        titleTv.setText(title);
    }

    public void setContentTv(String content) {
        contentTv.setText(content);
    }

    public void setOnClickListener(OnClickListener yesClickListener, OnClickListener noClickListener) {
        yesBtn.setOnClickListener(yesClickListener);
        noBtn.setOnClickListener(noClickListener);
    }
}
