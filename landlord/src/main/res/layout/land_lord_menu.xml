<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/ll_popup_bg"
        android:orientation="vertical"
        android:paddingBottom="8dp"
        android:paddingTop="10dp">

        <LinearLayout
            android:id="@+id/ll_menu_game_voice"
            android:layout_width="152dp"
            android:layout_height="45dp"
            android:gravity="center_horizontal"
            android:orientation="horizontal">
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="音量调节"
                android:textColor="#666666"
                android:textSize="15dp" />
        </LinearLayout>

        <ImageView
            android:layout_width="90dp"
            android:layout_height="1px"
            android:layout_gravity="center_horizontal"
            android:src="#33979797" />

        <LinearLayout
            android:id="@+id/ll_menu_standup"
            android:layout_width="152dp"
            android:layout_height="45dp"
            android:gravity="center_horizontal"
            android:orientation="horizontal">


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="站起围观"
                android:textColor="#666666"
                android:textSize="15dp" />
        </LinearLayout>

        <ImageView
            android:layout_width="90dp"
            android:layout_height="1px"
            android:layout_gravity="center_horizontal"
            android:src="#33979797" />

        <LinearLayout
            android:id="@+id/ll_menu_game_explain"
            android:layout_width="152dp"
            android:layout_height="45dp"
            android:gravity="center_horizontal"
            android:orientation="horizontal">
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="游戏规则"
                android:textColor="#666666"
                android:textSize="15dp" />
        </LinearLayout>

        <ImageView
            android:layout_width="90dp"
            android:layout_height="1px"
            android:layout_gravity="center_horizontal"
            android:src="#33979797" />

        <LinearLayout
            android:id="@+id/ll_menu_buy_doudou"
            android:layout_width="152dp"
            android:layout_height="45dp"
            android:gravity="center_horizontal"
            android:orientation="horizontal">
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="兑换豆豆"
                android:textColor="#666666"
                android:textSize="15dp" />
        </LinearLayout>

        <ImageView
            android:layout_width="90dp"
            android:layout_height="1px"
            android:layout_gravity="center_horizontal"
            android:src="#33979797" />
        
        <LinearLayout
            android:id="@+id/ll_menu_game_exit"
            android:layout_width="152dp"
            android:layout_height="45dp"
            android:gravity="center_horizontal"
            android:orientation="horizontal">
            
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="退出游戏"
                android:textColor="#666666"
                android:textSize="15dp" />
        </LinearLayout>


    </LinearLayout>


</LinearLayout>
