<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:app="http://schemas.android.com/apk/res-auto"
                xmlns:tools="http://schemas.android.com/tools"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/land_lord_bg"
                android:keepScreenOn="true"
    >


    <RelativeLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <ImageView
            android:id="@+id/menu_iv"
            android:layout_width="60dp"
            android:layout_height="match_parent"
            android:scaleType="centerInside"
            android:src="@drawable/home_menu"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <TextView
                android:id="@+id/room_name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxWidth="260dp"
                android:singleLine="true"
                android:textColor="@color/white_color"
                android:textSize="16dp"
                tools:text="我爱斗地主"/>

            <TextView
                android:id="@+id/room_level_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ddddddd"
                android:textColor="@color/white_color"
                android:textSize="10dp"/>
        </LinearLayout>

        <ImageView
            android:id="@+id/room_info_imv"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:paddingEnd="16dp"
            android:scaleType="centerInside"
            android:src="@drawable/action_bar_contact_white"/>

        <ImageView
            android:id="@+id/room_call_up_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/room_info_imv"
            android:paddingEnd="16dp"
            android:src="@drawable/action_bar_recall_ll_white"
            />
    </RelativeLayout>

    <ImageView
        android:id="@+id/ticket_imv"
        android:layout_width="125dp"
        android:layout_height="56dp"
        android:layout_alignParentRight="true"
        android:src="@drawable/ll_ticket"
        android:layout_alignTop="@id/my_head_imv"
        android:layout_marginRight="-3.5dp"
        />

    <TextView
        android:id="@+id/ticket_tv"
        android:layout_width="36dp"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@id/ticket_imv"
        android:layout_marginLeft="70dp"
        android:layout_alignTop="@id/ticket_imv"
        android:layout_marginTop="11.5dp"
        android:gravity="center_horizontal"
        android:textSize="10dp"
        android:textStyle="bold"
        tools:text="100"
        android:textColor="#FFFB6A"
        />

    <TextView
        android:id="@+id/most_win_tv"
        android:layout_width="36dp"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@id/ticket_imv"
        android:layout_below="@id/ticket_tv"
        android:layout_marginLeft="70dp"
        android:layout_marginTop="1.5dp"
        android:gravity="center_horizontal"
        android:textSize="10dp"
        android:textStyle="bold"
        tools:text="100"
        android:textColor="#FFFB6A"
        />

    <!--底牌-->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/action_bar"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        >

        <com.huiwan.landlord.view.CardView
            android:id="@+id/bottom_card_1"
            android:layout_width="27dp"
            android:layout_height="36dp"
            android:visibility="gone"
            />

        <com.huiwan.landlord.view.CardView
            android:id="@+id/bottom_card_2"
            android:layout_width="27dp"
            android:layout_height="36dp"
            android:layout_marginLeft="8dp"
            android:visibility="gone"
            />

        <com.huiwan.landlord.view.CardView
            android:id="@+id/bottom_card_3"
            android:layout_width="27dp"
            android:layout_height="36dp"
            android:layout_marginLeft="8dp"
            android:visibility="gone"
            />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/second_line_card_container"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/ll_second_line_card_height"
        android:layout_centerHorizontal="true"
        android:gravity="bottom"
        android:layout_alignBottom="@id/my_head_imv"
        android:layout_marginBottom="@dimen/ll_second_line_card_margin_bottom"
        >
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/first_line_card_container"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/ll_second_line_card_height"
        android:layout_centerHorizontal="true"
        android:layout_alignTop="@id/second_line_card_container"
        android:layout_marginTop="@dimen/ll_first_line_card_margin_bottom"
        android:gravity="bottom"
        >
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/my_card_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >

    </RelativeLayout>

    <Space
        android:id="@+id/space_center"
        android:layout_width="49dp"
        android:layout_height="49dp"
        android:layout_above="@id/second_line_card_container"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="62dp"
        />

    <ImageView
        android:id="@+id/card_ivalid_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/second_line_card_container"
        android:layout_marginBottom="54dp"
        android:layout_centerHorizontal="true"
        android:src="@drawable/card_invalid"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/card_cant_match_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_alignTop="@id/my_head_imv"
        android:layout_marginTop="10dp"
        android:src="@drawable/card_cant_match"
        android:visibility="gone"
        />

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/second_line_card_container"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="66dp"
        android:layout_marginTop="5dp"
        >

        <ImageView
            android:id="@+id/pass_imv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:src="@drawable/pass_btn"
            android:visibility="gone"
            />

        <com.huiwan.landlord.LLTimerView
            android:id="@+id/three_btn_my_timer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="3dp"
            android:layout_marginRight="3dp"
            android:layout_toRightOf="@id/pass_imv"
            android:visibility="gone"
            />

        <ImageView
            android:id="@+id/prompt_imv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_toRightOf="@id/three_btn_my_timer"
            android:src="@drawable/prompt_btn"
            android:visibility="gone"
            />

        <ImageView
            android:id="@+id/put_card_imv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_toRightOf="@id/prompt_imv"
            android:src="@drawable/put_card_btn"
            android:layout_marginLeft="2dp"
            android:visibility="gone"
            />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/second_line_card_container"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="66dp"
        android:orientation="horizontal"
        >

        <com.huiwan.landlord.LLTimerView
            android:id="@+id/single_btn_my_timer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            />

        <ImageView
            android:id="@+id/cant_match_imv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:layout_marginTop="5dp"
            android:gravity="center"
            android:src="@drawable/cant_match_btn"
            android:visibility="gone"
            />

        <ImageView
            android:id="@+id/put_card_imv_single"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:layout_marginTop="5dp"
            android:src="@drawable/put_card_btn"
            android:visibility="gone"
            />

    </LinearLayout>

    <ImageView
        android:id="@+id/call_lord_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/space_center"
        android:layout_toLeftOf="@id/space_center"
        android:src="@drawable/call_lord_btn"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/double_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/space_center"
        android:layout_toLeftOf="@id/space_center"
        android:src="@drawable/ll_double_btn"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/no_call_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/space_center"
        android:layout_toRightOf="@id/space_center"
        android:src="@drawable/no_call_btn"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/no_double_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/space_center"
        android:layout_toRightOf="@id/space_center"
        android:src="@drawable/ll_no_double_btn"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/self_preapre_action_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="106dp"
        android:src="@drawable/prepared_txt"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/cancel_host_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_alignTop="@id/my_head_imv"
        android:layout_marginTop="5dp"
        android:src="@drawable/cancel_host_btn"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/prepare_game_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/my_head_imv"
        android:layout_marginTop="5dp"
        android:layout_centerHorizontal="true"
        android:src="@drawable/ll_cancel_prepare"
        android:visibility="gone"
        />

    <ImageView
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_below="@id/action_bar"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="@dimen/ll_head_top_margin"
        android:src="@drawable/ll_empty_seat"
        />

    <ImageView
        android:id="@+id/prev_user_head"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_below="@id/action_bar"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="@dimen/ll_head_top_margin"
        android:src="@drawable/ll_empty_seat"
        />

    <ImageView
        android:id="@+id/prev_speaker_icon"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_alignBottom="@id/prev_user_head"
        android:layout_alignRight="@id/prev_user_head"
        android:src="@drawable/ll_user_speak_icon"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/prev_finish_put_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/prev_user_head"
        android:layout_marginLeft="7dp"
        android:layout_marginTop="2dp"
        android:layout_toRightOf="@id/prev_user_head"
        android:src="@drawable/ll_finish_card_left"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/prev_land_lord_mark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@id/prev_user_head"
        android:layout_alignTop="@id/prev_user_head"
        android:layout_marginTop="-16dp"
        android:layout_marginLeft="-3dp"
        android:src="@drawable/land_lord_mark_icon_small"
        android:visibility="gone"
        />

    <RelativeLayout
        android:id="@+id/prev_user_info_lay"
        android:layout_width="102dp"
        android:layout_height="36dp"
        android:layout_below="@id/prev_user_head"
        android:layout_marginTop="8dp"
        android:background="@drawable/land_lord_user_info_bg_left"
        >

        <TextView
            android:id="@+id/prev_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxWidth="48dp"
            android:singleLine="true"
            android:textColor="@color/white_color"
            android:textSize="12dp"
            android:textStyle="bold"
            android:visibility="gone"
            tools:text="玩家1"
            />

        <ImageView
            android:id="@+id/prev_bean_icon"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_below="@id/prev_name"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="1dp"
            android:src="@drawable/land_lord_bean_icon"
            android:visibility="gone"
            />

        <TextView
            android:id="@+id/prev_bean_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/prev_bean_icon"
            android:layout_marginLeft="2dp"
            android:layout_marginTop="-2dp"
            android:layout_toRightOf="@id/prev_bean_icon"
            android:textSize="12dp"
            android:visibility="gone"
            tools:text="3.229万"
            />

        <TextView
            android:id="@+id/prev_empty_seat_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="16dp"
            android:text="虚位以待"
            android:textColor="@color/white_color"
            android:textSize="12dp"
            android:textStyle="bold"
            />
    </RelativeLayout>

    <ImageView
        android:id="@+id/prev_host_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/prev_hand_card_imv"
        android:layout_marginLeft="6dp"
        android:layout_marginTop="8dp"
        android:layout_toRightOf="@id/prev_user_info_lay"
        android:src="@drawable/ll_host_icon"
        android:visibility="gone"
        />

    <ImageView
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_alignParentRight="true"
        android:layout_below="@id/action_bar"
        android:layout_marginRight="16dp"
        android:layout_marginTop="@dimen/ll_head_top_margin"
        android:src="@drawable/ll_empty_seat"
        />

    <ImageView
        android:id="@+id/next_user_head"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_alignParentRight="true"
        android:layout_below="@id/action_bar"
        android:layout_marginRight="16dp"
        android:layout_marginTop="@dimen/ll_head_top_margin"
        android:src="@drawable/ll_empty_seat"
        />

    <ImageView
        android:id="@+id/next_speaker_icon"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_alignBottom="@id/next_user_head"
        android:layout_alignRight="@id/next_user_head"
        android:src="@drawable/ll_user_speak_icon"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/next_finish_put_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/next_user_head"
        android:layout_marginRight="7dp"
        android:layout_marginTop="2dp"
        android:layout_toLeftOf="@id/next_user_head"
        android:src="@drawable/ll_finish_card_right"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/next_land_lord_mark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@id/next_user_head"
        android:layout_alignTop="@id/next_user_head"
        android:layout_marginTop="-16dp"
        android:layout_marginLeft="-3dp"
        android:src="@drawable/land_lord_mark_icon_small"
        android:visibility="gone"
        />

    <RelativeLayout
        android:id="@+id/next_user_info_lay"
        android:layout_width="102dp"
        android:layout_height="36dp"
        android:layout_alignParentRight="true"
        android:layout_below="@id/next_user_head"
        android:layout_marginTop="8dp"
        android:background="@drawable/land_lord_user_info_bg_right"
        >

        <TextView
            android:id="@+id/next_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxWidth="48dp"
            android:singleLine="true"
            android:textColor="@color/white_color"
            android:textSize="12dp"
            android:textStyle="bold"
            android:visibility="gone"
            tools:text="玩家2"
            />

        <ImageView
            android:id="@+id/next_bean_icon"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_below="@id/next_name"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="1dp"
            android:src="@drawable/land_lord_bean_icon"
            android:visibility="gone"
            />

        <TextView
            android:id="@+id/next_bean_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/next_bean_icon"
            android:layout_marginLeft="2dp"
            android:layout_marginTop="-2dp"
            android:layout_toRightOf="@id/next_bean_icon"
            android:textSize="12dp"
            android:visibility="gone"
            tools:text="3.229万"
            />

        <TextView
            android:id="@+id/next_empty_seat_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginRight="16dp"
            android:layout_alignParentRight="true"
            android:text="虚位以待"
            android:textColor="@color/white_color"
            android:textSize="12dp"
            android:textStyle="bold"
            />
    </RelativeLayout>

    <ImageView
        android:id="@+id/next_host_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/next_hand_card_imv"
        android:layout_marginRight="6dp"
        android:layout_marginTop="8dp"
        android:layout_toLeftOf="@id/next_user_info_lay"
        android:src="@drawable/ll_host_icon"
        android:visibility="gone"
        />

    <ImageView
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="94dp"
        android:layout_marginLeft="16dp"
        android:src="@drawable/ll_empty_seat"
        />

    <ImageView
        android:id="@+id/my_head_imv"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="94dp"
        android:layout_marginLeft="16dp"
        android:src="@drawable/ll_empty_seat"
        />

    <ImageView
        android:id="@+id/self_speaker_icon"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_alignBottom="@id/my_head_imv"
        android:layout_alignRight="@id/my_head_imv"
        android:src="@drawable/ll_user_speak_icon"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/self_finish_put_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/my_head_imv"
        android:layout_marginLeft="7dp"
        android:layout_marginTop="2dp"
        android:layout_toRightOf="@id/my_head_imv"
        android:src="@drawable/ll_finish_card_left"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/self_land_lord_mark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@id/my_head_imv"
        android:layout_alignTop="@id/my_head_imv"
        android:layout_marginTop="-16dp"
        android:layout_marginLeft="-3dp"
        android:src="@drawable/land_lord_mark_icon_small"
        android:visibility="gone"
        />

    <RelativeLayout
        android:id="@+id/my_user_info_lay"
        android:layout_width="102dp"
        android:layout_height="36dp"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="50dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/land_lord_user_info_bg_left"
        >

        <TextView
            android:id="@+id/my_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxWidth="48dp"
            android:singleLine="true"
            android:textColor="@color/white_color"
            android:textSize="12dp"
            android:textStyle="bold"
            android:visibility="gone"
            tools:text="玩家"
            />

        <ImageView
            android:id="@+id/my_bean_icon"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_below="@id/my_name"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="1dp"
            android:src="@drawable/land_lord_bean_icon"
            android:visibility="gone"
            />

        <TextView
            android:id="@+id/my_bean_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@id/my_bean_icon"
            android:layout_marginLeft="2dp"
            android:layout_marginTop="-2dp"
            android:layout_toRightOf="@id/my_bean_icon"
            android:textSize="12dp"
            android:visibility="gone"
            tools:text="3.229万"
            />

        <TextView
            android:id="@+id/my_empty_seat_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="16dp"
            android:text="虚位以待"
            android:textColor="@color/white_color"
            android:textSize="12dp"
            android:textStyle="bold"
            />
    </RelativeLayout>

    <ImageView
        android:id="@+id/prev_hand_card_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/action_bar"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/ll_others_hand_card_top_margin"
        android:layout_toRightOf="@id/prev_user_head"
        android:src="@drawable/others_card_back"
        android:visibility="gone"
        />

    <TextView
        android:id="@+id/prev_hand_card_num"
        android:layout_width="23dp"
        android:layout_height="wrap_content"
        android:layout_below="@id/action_bar"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="@dimen/ll_other_hand_card_txt_top_margin"
        android:layout_toRightOf="@id/prev_user_head"
        android:gravity="center_horizontal"
        android:textColor="@color/white_color"
        tools:text="13"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/prev_win_alarm_imv"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_marginLeft="1dp"
        android:layout_toRightOf="@id/prev_hand_card_imv"
        android:layout_alignTop="@id/prev_hand_card_imv"
        android:layout_marginTop="-6dp"
        android:src="@drawable/ll_win_alarm_00000"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/next_hand_card_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/action_bar"
        android:layout_marginRight="15dp"
        android:layout_marginTop="@dimen/ll_others_hand_card_top_margin"
        android:layout_toLeftOf="@id/next_user_head"
        android:src="@drawable/others_card_back"
        android:visibility="gone"
        />

    <TextView
        android:id="@+id/next_hand_card_num"
        android:layout_width="23dp"
        android:layout_height="wrap_content"
        android:layout_below="@id/action_bar"
        android:layout_marginRight="15dp"
        android:layout_marginTop="@dimen/ll_other_hand_card_txt_top_margin"
        android:layout_toLeftOf="@id/next_user_head"
        android:gravity="center_horizontal"
        android:textColor="@color/white_color"
        android:visibility="gone"
        tools:text="1"
        />

    <ImageView
        android:id="@+id/next_win_alarm_imv"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_marginRight="1dp"
        android:layout_alignTop="@id/next_hand_card_imv"
        android:layout_marginTop="-6dp"
        android:layout_toLeftOf="@id/next_hand_card_imv"
        android:src="@drawable/ll_win_alarm_00000"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/prev_action_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/prev_user_info_lay"
        android:layout_marginLeft="27dp"
        android:layout_marginTop="22dp"
        android:src="@drawable/prepared_txt"
        android:visibility="gone"
        />

    <com.huiwan.landlord.LLTimerView
        android:id="@+id/prev_timer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/prev_user_info_lay"
        android:layout_marginLeft="27dp"
        android:layout_marginTop="15dp"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/next_action_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_below="@id/next_user_info_lay"
        android:layout_marginRight="27dp"
        android:layout_marginTop="22dp"
        android:src="@drawable/prepared_txt"
        android:visibility="gone"
        />

    <com.huiwan.landlord.LLTimerView
        android:id="@+id/next_timer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_below="@id/next_user_info_lay"
        android:layout_marginRight="27dp"
        android:layout_marginTop="15dp"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/my_action_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignBottom="@id/space_center"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="5dp"
        />

    <com.huiwan.landlord.LLTimerView
        android:id="@+id/my_center_timer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/second_line_card_container"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="68dp"
        android:visibility="gone"
        />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_toRightOf="@id/my_user_info_lay"
        android:layout_marginBottom="60dp"
        android:orientation="horizontal"
        android:paddingStart="12dp"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="底分："
            android:textColor="#598FFF"
            android:textStyle="bold"
            />

        <TextView
            android:id="@+id/start_points"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="10分"
            android:textColor="@color/white_color"
            android:textStyle="bold"
            />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:text="倍数："
            android:textColor="#598FFF"
            android:textStyle="bold"
            />

        <TextView
            android:id="@+id/multiple_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0"
            android:textColor="#F8F3DE"
            android:textStyle="bold"
            />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/prev_put_card_lay"
        android:layout_width="@dimen/ll_others_put_card_lay_width"
        android:layout_height="@dimen/ll_others_put_card_lay_height"
        android:layout_below="@id/prev_user_info_lay"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="@dimen/others_put_card_top_margin"
        >

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/next_put_card_lay"
        android:layout_width="@dimen/ll_others_put_card_lay_width"
        android:layout_height="@dimen/ll_others_put_card_lay_height"
        android:layout_alignParentRight="true"
        android:layout_below="@id/next_user_info_lay"
        android:layout_marginRight="16dp"
        android:layout_marginTop="@dimen/others_put_card_top_margin"
        android:gravity="right"
        >
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/my_put_card_lay"
        android:layout_width="match_parent"
        android:layout_height="@dimen/ll_my_put_card_lay_height"
        android:layout_above="@id/second_line_card_container"
        android:layout_marginBottom="@dimen/ll_my_put_card_lay_bottom_margin"
        android:gravity="center_horizontal"
        >

    </RelativeLayout>

    <ImageView
        android:id="@+id/prev_card_anim_view"
        android:layout_width="match_parent"
        android:layout_height="147dp"
        android:scaleType="centerCrop"
        android:layout_alignTop="@id/prev_put_card_lay"
        android:layout_marginTop="-36dp"
        android:translationX="-96dp"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/next_card_anim_view"
        android:layout_width="match_parent"
        android:layout_height="147dp"
        android:scaleType="centerCrop"
        android:layout_alignTop="@id/next_put_card_lay"
        android:layout_marginTop="-36dp"
        android:translationX="96dp"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/my_card_anim_view"
        android:layout_width="match_parent"
        android:layout_height="147dp"
        android:scaleType="centerCrop"
        android:layout_alignTop="@id/my_put_card_lay"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="-28dp"
        android:visibility="gone"
        />

    <FrameLayout
        android:id="@+id/prev_chat_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40dp"
        android:background="@drawable/ll_left_bubble"
        android:paddingBottom="14dp"
        android:paddingStart="14dp"
        android:paddingEnd="14dp"
        android:paddingTop="7.5dp"
        android:layout_alignTop="@id/prev_user_head"
        android:layout_marginTop="-20dp"
        android:visibility="gone"
        >

        <TextView
            android:id="@+id/prev_chat_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="200dp"
            android:layout_gravity="center"
            android:textColor="#AD4C0F"
            android:textSize="12dp"
            android:textStyle="bold"
            tools:text="怎么又断线，网络怎么这么差呀1234567"
            />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/next_chat_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginRight="40dp"
        android:layout_alignTop="@id/next_user_head"
        android:layout_marginTop="-20dp"
        android:background="@drawable/ll_right_bubble"
        android:paddingBottom="14dp"
        android:paddingStart="14dp"
        android:paddingEnd="14dp"
        android:paddingTop="7.5dp"
        android:visibility="gone"
        >

        <TextView
            android:id="@+id/next_chat_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="200dp"
            android:layout_gravity="center"
            android:textColor="#AD4C0F"
            android:textSize="12dp"
            android:textStyle="bold"
            tools:text="快点吧，我等到"
            />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/my_chat_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/my_head_imv"
        android:layout_marginLeft="40dp"
        android:layout_marginTop="-20dp"
        android:background="@drawable/ll_left_bubble"
        android:paddingBottom="14dp"
        android:paddingStart="14dp"
        android:paddingEnd="14dp"
        android:paddingTop="7.5dp"
        android:visibility="gone"
        >

        <TextView
            android:id="@+id/my_chat_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="200dp"
            android:textColor="#AD4C0F"
            android:textSize="12dp"
            android:textStyle="bold"
            tools:text="怎么又断线啊，网络怎么这么差啊"
            />
    </FrameLayout>

    <ImageView
        android:id="@+id/landlord_light_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:alpha="0"
        android:src="@drawable/land_lord_light_circle"
        />

    <ImageView
        android:id="@+id/landlord_light_anim"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/center_landlord_mark"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="-55dp"
        android:alpha="0"
        android:src="@drawable/land_lord_light"
        />

    <ImageView
        android:id="@+id/center_landlord_mark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:alpha="0"
        android:src="@drawable/land_lord_mark_icon"
        />

    <ImageView
        android:id="@+id/small_center_landlord_mark"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_alignTop="@id/center_landlord_mark"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="30dp"
        android:src="@drawable/land_lord_mark_icon_small"
        android:visibility="invisible"
        />

    <com.huiwan.barrage.BarrageAnimView
        android:id="@+id/barrage_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        />

    <com.huiwan.landlord.view.LLBottomView
        android:id="@+id/bottom_control_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentBottom="true"
        />

    <SurfaceView
        android:id="@+id/card_effect_anim_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="50dp"
        tools:visibility="gone"
        />

    <ImageView
        android:id="@+id/game_over_anim"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="50dp"
        android:scaleType="centerInside"
        android:visibility="gone"
        />
    <ImageView
        android:id="@+id/multiple_anim_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:alpha="0"
        android:src="@drawable/multiple_anim"
        />

    <com.huiwan.landlord.view.LLGameOverView
        android:id="@+id/game_over_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        />
</RelativeLayout>