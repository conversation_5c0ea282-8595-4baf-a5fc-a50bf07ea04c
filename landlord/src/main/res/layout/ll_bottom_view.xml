<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    >

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#0051A1"
        android:layout_alignParentBottom="true"
        >

        <ImageView
            android:id="@+id/lock_imv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:src="@drawable/ll_lock_off"
            />

        <ImageView
            android:id="@+id/audio_imv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="16dp"
            android:layout_toRightOf="@id/lock_imv"
            android:src="@drawable/ll_audio_on"
            />

        <ImageView
            android:id="@+id/mic_imv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="16dp"
            android:src="@drawable/ll_mic_on"
            android:layout_toRightOf="@id/audio_imv"
            />

        <ImageView
            android:id="@+id/msg_imv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="16dp"
            android:layout_toRightOf="@id/mic_imv"
            android:src="@drawable/ll_bottom_msg_icon"
            />

        <ImageView
            android:id="@+id/hot_words_imv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="16dp"
            android:src="@drawable/ll_hot_words"
            />

        <ImageView
            android:id="@+id/host_imv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/hot_words_imv"
            android:layout_marginRight="16dp"
            android:src="@drawable/host_icon"
            />
    </RelativeLayout>


    <ImageView
        android:id="@+id/hot_words_bg_imv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/hot_words_bg"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="30dp"
        android:layout_alignParentRight="true"
        android:visibility="gone"
        />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/hot_words_rv"
        android:layout_width="270dp"
        android:layout_height="252dp"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="50dp"
        android:layout_alignParentRight="true"
        android:layout_marginRight="10dp"
        android:paddingBottom="8dp"
        android:visibility="gone"
        />

    <RelativeLayout
        android:id="@+id/apply_free_talk_lay"
        android:layout_width="178dp"
        android:layout_height="72dp"
        android:background="@drawable/free_talk_apply_bg"
        android:layout_alignParentRight="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="50dp"
        android:visibility="gone"
        >
        <TextView
            android:id="@+id/apply_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="sam1234"
            android:alpha="0.5"
            android:textColor="@color/white_color"
            android:textStyle="bold"
            android:textSize="12dp"
            android:layout_marginLeft="14dp"
            android:layout_marginTop="14dp"
            android:singleLine="true"
            android:maxWidth="51dp"
            android:ellipsize="end"
            />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="申请开启自由发言"
            android:textColor="@color/white_color"
            android:textStyle="bold"
            android:textSize="12dp"
            android:layout_alignTop="@id/apply_name"
            android:layout_alignParentRight="true"
            android:layout_marginRight="14dp"
            />

        <TextView
            android:id="@+id/decline_btn_tv"
            android:background="@drawable/shape_b40001_corner2"
            android:layout_width="66dp"
            android:layout_height="24dp"
            android:gravity="center"
            android:textColor="@color/white_color"
            android:textStyle="bold"
            android:textSize="10dp"
            android:text="拒绝"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="14dp"
            android:layout_marginLeft="14dp"
            />

        <TextView
            android:id="@+id/agree_btn_tv"
            android:background="@drawable/shape_00c532_corner2"
            android:layout_width="66dp"
            android:layout_height="24dp"
            android:gravity="center"
            android:textColor="@color/white_color"
            android:textStyle="bold"
            android:textSize="10dp"
            android:text="同意"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="14dp"
            android:layout_alignParentRight="true"
            android:layout_marginRight="14dp"
            />
    </RelativeLayout>

<!--    <com.huiwan.widget.WerewolfSendView-->
<!--        android:id="@+id/game_send_view"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:layout_alignParentBottom="true"-->
<!--        android:visibility="gone"-->
<!--        />-->
</RelativeLayout>