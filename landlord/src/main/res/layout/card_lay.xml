<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="@dimen/ll_card_lay_width"
                android:layout_height="@dimen/ll_card_lay_height"
                xmlns:tools="http://schemas.android.com/tools">
    <ImageView
        android:id="@+id/color_imv"
        android:layout_width="@dimen/ll_card_color_imv_width"
        android:layout_height="@dimen/ll_card_color_imv_height"
        tools:src="@drawable/club_card"
        />

    <ImageView
        android:id="@+id/value_imv"
        android:layout_width="@dimen/ll_card_value_imv_width"
        android:layout_height="@dimen/ll_card_value_imv_height"
        tools:src="@drawable/black_2"
        android:layout_marginLeft="@dimen/ll_card_value_imv_left_margin"
        android:layout_marginTop="@dimen/ll_card_value_imv_top_margin"
        />

    <ImageView
        android:id="@+id/black_layer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/card_black_layer"
        android:visibility="gone"
        />

    <ImageView
        android:id="@+id/bottom_card_imv"
        android:layout_width="@dimen/ll_card_bottom_logo_imv_width"
        android:layout_height="@dimen/ll_card_bottom_logo_imv_height"
        android:src="@drawable/bottom_card_logo"
        android:layout_alignParentRight="true"
        android:layout_marginTop="@dimen/ll_card_bottom_logo_imv_top_margin"
        android:layout_marginRight="@dimen/ll_card_bottom_logo_imv_right_margin"
        />
</RelativeLayout>