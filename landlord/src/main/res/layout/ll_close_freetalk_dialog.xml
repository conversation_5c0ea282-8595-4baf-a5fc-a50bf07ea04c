<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:tools="http://schemas.android.com/tools"
                android:layout_width="360dp"
                android:layout_height="246dp"
                android:background="@drawable/android_bg_1"
    >
    <!--<ImageView-->
        <!--android:layout_width="wr"-->
        <!--android:layout_height=""/>-->
    <ImageView
        android:layout_width="168dp"
        android:layout_height="32dp"
        android:layout_centerHorizontal="true"
        android:src="@drawable/ll_dialog_title_area"
        android:layout_marginTop="17dp"
        />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:textColor="#AE4410"
        android:textSize="16dp"
        android:textStyle="bold"
        tools:text="提示"
        android:layout_marginTop="19dp"
        />

    <ImageView
        android:id="@+id/content_area"
        android:layout_width="290dp"
        android:layout_height="163dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="48dp"
        android:background="@drawable/ll_dialog_content_bg"
        />

    <ImageView
        android:layout_width="288dp"
        android:layout_height="81dp"
        android:background="@drawable/ll_dialog_bg"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="48dp"
        />

    <View
        android:layout_width="290dp"
        android:layout_height="1px"
        android:layout_alignLeft="@id/content_area"
        android:layout_alignTop="@id/content_area"
        android:layout_marginTop="81.5dp"
        android:background="#DADADA"
        />

    <TextView
        android:id="@+id/content_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="260dp"
        android:layout_alignTop="@id/content_area"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="32dp"
        android:textColor="#232323"
        tools:text="你正在游戏中，退出将由笨笨的机器人代打，确定退出吗？"
        android:gravity="center"
        />

    <ImageView
        android:id="@+id/no_btn"
        android:layout_width="122dp"
        android:layout_height="wrap_content"
        android:src="@drawable/ll_no_btn"
        android:layout_alignLeft="@id/content_area"
        android:layout_marginLeft="16dp"
        android:layout_alignBottom="@id/content_area"
        android:layout_marginBottom="16dp"
        />

    <ImageView
        android:id="@+id/yes_btn"
        android:layout_width="122dp"
        android:layout_height="wrap_content"
        android:src="@drawable/ll_yes_btn"
        android:layout_alignRight="@id/content_area"
        android:layout_marginRight="16dp"
        android:layout_alignBottom="@id/content_area"
        android:layout_marginBottom="16dp"
        />
</RelativeLayout>