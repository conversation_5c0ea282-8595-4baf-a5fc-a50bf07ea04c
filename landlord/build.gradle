apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.ext.android.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.ext.android.minSdkVersion
        buildToolsVersion rootProject.ext.android.buildToolsVersion
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation project(":lib:libdownload")
    implementation project(":lib:libtcp")
    implementation project(":lib:anim")
    implementation project(":libdialog")
    implementation libs.androidx.appcompat
    implementation project(":service:UserService")
    implementation project(":service:ConfigService")
    implementation project(":service:VoiceService")
    implementation project(":sdk:baseutil")
}