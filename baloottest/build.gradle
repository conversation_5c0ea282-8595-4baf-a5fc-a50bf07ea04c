plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    compileSdkVersion 32

    defaultConfig {
        applicationId "com.example.baloottest"
        minSdkVersion 21
        targetSdkVersion 32
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        manifestPlaceholders = rootProject.ext.appConfig

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    packagingOptions {
        pickFirst "lib/arm64-v8a/libc++_shared.so"
        pickFirst "lib/armeabi-v7a/libc++_shared.so"
    }
}

dependencies {

    implementation rootProject.ext.dependencies.Androidx
    implementation rootProject.ext.dependencies.kotlinx_coroutines_android
    implementation rootProject.ext.dependencies.kotlinx_viewmodel
    implementation rootProject.ext.dependencies.kotlin_core
    implementation rootProject.ext.dependencies.lifecycle_ktx
    implementation 'androidx.appcompat:appcompat:1.4.2'
    implementation 'com.google.android.material:material:1.6.1'
    implementation rootProject.ext.dependencies.constraintlayout
    implementation project(path: ':module:baloot')
    implementation project(path: ':lib:baseutil')
    implementation project(path: ':lib:api')
    implementation project(path: ':lib:store')
    implementation project(path: ':lib:hwconstants')
    implementation project(path: ':lib:libhttp')
    implementation project(":service:VoiceService")
    implementation project(path: ':service:ConfigService')
    implementation project(path: ':service:UserService')
    implementation project(path: ':lib:libtcp')
    implementation project(path: ':lib:libproto')
    implementation project(path: ':lib:deviceid')
    implementation project(path: ':lib:liblog')
    implementation project(path: ':lib:libdialog')
    implementation project(path: ':lib:api-plugin:track')
    implementation project(path: ':lib:api-plugin:voice-api')
    implementation project(":module:webview")
    implementation project(":module:hwroom")
    implementation project(":module:baloot_asset_pack")
    implementation project(path: ':component:gift')
    implementation project(path: ':lib:libwidget')
    implementation project(path: ':lib:api-plugin:add-friend')
    implementation project(path: ':lib:api-plugin:hwroom-api')
    testImplementation libs.junit
    androidTestImplementation libs.androidx-junit
    androidTestImplementation libs.androidx-espresso-core
}