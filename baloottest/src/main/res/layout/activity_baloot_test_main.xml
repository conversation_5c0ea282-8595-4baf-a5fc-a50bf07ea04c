<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".TestEmptyActivity">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <EditText
            android:id="@+id/uid_et"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="uid"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/sid_et"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="sid"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/anchor_view"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="right"
            android:background="#000"
            tools:ignore="RtlHardcoded" />

        <Button
            android:id="@+id/main_page_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="main page" />

        <EditText
            android:id="@+id/game_page_rid_et"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="rid"
            android:inputType="number"
            android:text="1234" />

        <EditText
            android:id="@+id/game_page_watch_uid_et"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="watch uid"
            android:inputType="number"
            android:text="0" />

        <Button
            android:id="@+id/game_page_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="game page" />


        <Button
            android:id="@+id/test_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="test" />


        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/test_color_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:checked="false"
            android:text="颜色开关" />


        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/test_language_btn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:checked="false"
            android:text="阿语" />

        <TextView
            android:layout_width="360dp"
            android:layout_height="wrap_content"
            android:text="@string/sw_dimen"
            android:layout_marginStart="10dp"
            android:background="#f0f0f0"
            />

        <com.wepie.module.baloot.game.widget.BlStrokeNumView
            android:id="@+id/time_stroke_num_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <com.wepie.module.baloot.game.widget.poker.PokerView
            android:id="@+id/poker_view"
            android:layout_width="50dp"
            android:layout_height="70dp" />

    </androidx.appcompat.widget.LinearLayoutCompat>


</ScrollView>