#!/bin/sh
baseUrl="http://47.241.37.141:8082"


balootBaseUrl="$baseUrl/game/baloot"
curl "$balootBaseUrl/baloot_common.proto" > baloot_common.proto
curl "$balootBaseUrl/baloot_game.proto" > baloot_game.proto
curl "$balootBaseUrl/baloot_player.proto" > baloot_player.proto
curl "$balootBaseUrl/baloot_push.proto" > baloot_push.proto
curl "$balootBaseUrl/baloot_reqrsp.proto" > baloot_reqrsp.proto

echo '\noption java_package="com.wepie.wespy.net.tcp.packet";\noption java_outer_classname = "BalootPacketsCommon";' >> baloot_common.proto
echo '\noption java_package="com.wepie.wespy.net.tcp.packet";\noption java_outer_classname = "BalootPacketsGame";' >> baloot_game.proto
echo '\noption java_package="com.wepie.wespy.net.tcp.packet";\noption java_outer_classname = "BalootPacketsPlayer";' >> baloot_player.proto
echo '\noption java_package="com.wepie.wespy.net.tcp.packet";\noption java_outer_classname = "BalootPacketsPush";' >> baloot_push.proto
echo '\noption java_package="com.wepie.wespy.net.tcp.packet";\noption java_outer_classname = "BalootPacketsReqRsp";' >> baloot_reqrsp.proto