apply from: "../base_module.gradle"

dependencies {

    implementation project(path: ':lib:libproto')
    implementation project(path: ':lib:libtcp')
    implementation project(path: ':lib:hwconstants')
    implementation project(path: ':lib:libhttp')
    implementation project(path: ':lib:baseutil')
    implementation project(path: ':service:UserService')
    implementation project(path: ':service:ConfigService')
    implementation project(path: ':component:decorate')
    implementation project(path: ':component:activity')
    implementation project(path: ':module:littlegame')
    implementation project(path: ':lib:api-plugin:track')
    implementation project(path: ':lib:libwidget')
//    implementation project(path: ':lib:base-ui')
    implementation project(path: ':lib:api-plugin:share')
    implementation project(path: ':lib:os-libshare')
    implementation project(path: ':module:hwroom')
    implementation project(path: ':module:login-wejoy')
    implementation project(path: ':module:webview')
    implementation project(path: ':lib:libdialog')
    implementation project(path: ':lib:api-plugin:hwroom-api')
    implementation project(path: ':lib:floating')

    //这里引用的模块注意多进程处理，初始化等等
    implementation libs.eventBus
}