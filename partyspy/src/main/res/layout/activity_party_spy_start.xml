<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/party_spy_start_bg"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:has_action_bar="false"
        app:action_bar_color="@color/transparent"/>

    <RelativeLayout
        android:id="@+id/title_lay"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="54dp"
        app:layout_constraintTop_toBottomOf="@id/action_bar">

        <ImageView
            android:id="@+id/back_iv"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/party_back_icon"
            android:layout_centerVertical="true"
            android:layout_marginStart="18dp"
            android:scaleX="@integer/image_scale_x"/>

        <ImageView
            android:id="@+id/help_iv"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="18dp"
            android:src="@drawable/party_help_icon" />
    </RelativeLayout>

    <ImageView
        android:id="@+id/logo_iv"
        android:layout_width="169dp"
        android:layout_height="228dp"
        android:layout_marginTop="44dp"
        android:src="@drawable/party_start_dec"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_lay"/>

    <ImageView
        android:id="@+id/create_iv"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:layout_marginTop="55dp"
        android:background="@drawable/party_spy_create_room_item_selector"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/logo_iv"/>

    <ImageView
        android:id="@+id/enter_iv"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:layout_marginTop="19dp"
        android:src="@drawable/party_spy_join_room_item_selector"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/create_iv"/>

    <TextView
        android:id="@+id/share_tv"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:text="@string/party_game_invite_friend"
        android:textSize="15dp"
        android:textColor="#ffffff"
        android:gravity="center"
        android:layout_marginBottom="54dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>