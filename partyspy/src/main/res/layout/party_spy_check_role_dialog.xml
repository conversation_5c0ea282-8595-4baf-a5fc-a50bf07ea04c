<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="303dp"
    android:layout_height="258dp"
    android:background="@drawable/shape_ffffff_corner12"
    android:clipChildren="false"
    android:layoutDirection="rtl">

    <com.huiwan.decorate.DecorHeadImgView
        android:id="@+id/head_iv"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_centerHorizontal="true"
        tools:src="@drawable/party_head_image_icon"
        android:layout_marginTop="32dp"/>

    <ImageView
        android:id="@+id/pass_mask_iv"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginStart="8dp"
        android:background="@drawable/shape_60000000_corner_100"
        android:visibility="gone"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="32dp"/>

    <ImageView
        android:id="@+id/out_iv"
        android:layout_width="56dp"
        android:layout_height="54dp"
        android:src="@drawable/party_pass_tips_icon"
        android:layout_alignStart="@id/head_iv"
        android:layout_alignTop="@id/head_iv"/>

    <TextView
        android:id="@+id/num_tv"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_alignTop="@id/head_iv"
        android:layout_alignEnd="@id/head_iv"
        android:background="@drawable/shape_ecedef_oval"
        android:gravity="center"
        android:textColor="#1B1D38"
        android:textSize="11dp"
        tools:text="11" />

    <ImageView
        android:id="@+id/role_iv"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:layout_marginTop="95dp"
        android:layout_centerHorizontal="true"
        tools:src="@drawable/party_spy_role2" />

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        tools:text="游戏继续"
        android:textSize="16dp"
        android:textColor="#1B1D38"
        android:textStyle="bold"
        android:layout_centerHorizontal="true"
        android:layout_below="@id/role_iv"
        android:layout_marginTop="8dp"
        android:gravity="center"/>

    <TextView
        android:id="@+id/content_tv"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        tools:text="请继续下一轮发言"
        android:textSize="14dp"
        android:textColor="#999CB4"
        android:gravity="center"
        android:layout_marginTop="8dp"
        android:layout_centerHorizontal="true"
        android:layout_below="@id/title_tv" />

    <TextView
        android:id="@+id/enter_tv"
        android:layout_width="164dp"
        android:layout_height="40dp"
        android:layout_below="@id/content_tv"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="24dp"
        android:background="@drawable/shape_00ccf9_corner24"
        android:gravity="center"
        android:text="@string/party_spy_i_kown"
        android:textColor="#ffffff"
        android:textSize="16dp"
        android:textStyle="bold" />
</RelativeLayout>