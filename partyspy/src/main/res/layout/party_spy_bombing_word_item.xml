<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_1affffff_corner8"
    android:id="@+id/word_lay">

    <TextView
        android:id="@+id/word_tv"
        android:layout_width="164dp"
        android:layout_height="44dp"
        tools:text="大锅真帅"
        android:textSize="14dp"
        android:textColor="#FFFFFF"
        android:gravity="center"
        android:padding="10dp"
        android:layout_marginTop="2dp"
        android:layout_marginStart="2dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>