<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_transparent50"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:id="@+id/content_lay"
        android:layout_width="match_parent"
        android:layout_height="338dp"
        android:orientation="vertical"
        android:background="@drawable/party_spy_identity_tips_dialog_bg"
        android:gravity="center_horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" >

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/party_game_start"
            android:textSize="16dp"
            android:textColor="#ffffff"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="12dp"/>

        <TextView
            android:id="@+id/special_role_description_tx"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/party_spy_you_are_the_special_spy"
            android:textSize="14dp"
            android:textColor="#F0D5AE"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="8dp"
            android:gravity="center"/>

        <TextView
            android:id="@+id/role_word_tx"
            android:layout_width="280dp"
            android:layout_height="114dp"
            android:background="@drawable/party_spy_word_bg"
            tools:text="你的身份是：白板"
            android:textSize="16dp"
            android:textStyle="bold"
            android:gravity="center"
            android:textColor="#ffffff"/>

        <TextView
            android:id="@+id/enter_tv"
            android:layout_width="183dp"
            android:layout_height="40dp"
            android:text="@string/party_spy_i_kown"
            android:textSize="16dp"
            android:textColor="#1B1D38"
            android:textStyle="bold"
            android:gravity="center"
            android:background="@drawable/shape_f7f8fa_corners24_bg"
            android:layout_marginTop="32dp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>