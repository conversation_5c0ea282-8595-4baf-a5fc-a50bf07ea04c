<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="78dp"
    android:layout_height="wrap_content"
    android:clipChildren="false">

    <com.huiwan.decorate.DecorHeadImgView
        android:id="@+id/head_iv"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_marginStart="10dp"
        android:src="@drawable/party_head_image_icon"
        android:layout_marginTop="10dp"/>

    <TextView
        android:id="@+id/owner_tv"
        android:layout_width="32dp"
        android:layout_height="16dp"
        android:layout_centerHorizontal="true"
        android:text="@string/party_host"
        android:background="@drawable/shape_edaf33_corner8"
        android:gravity="center"
        android:layout_marginTop="50dp"
        android:textSize="10dp"
        android:textStyle="bold"
        android:textColor="#FFFFFF"
        android:visibility="gone"
        android:paddingTop="1.5dp"
        tools:visibility="visible"/>

    <ImageView
        android:id="@+id/pass_mask"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/shape_60000000_corner_100"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/out_iv"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_marginTop="10dp"
        android:layout_marginStart="10dp"
        android:layout_centerHorizontal="true"
        android:src="@drawable/party_pass_tips_icon"
        android:visibility="gone"
        tools:visibility="visible"/>

    <TextView
        android:id="@+id/num_tv"
        android:layout_width="16dp"
        android:layout_height="16dp"
        tools:text="1"
        android:textSize="11dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:textColor="#1B1D38"
        android:background="@drawable/shape_ffffff_oval"
        android:gravity="center"
        android:layout_alignParentEnd="true" />

    <TextView
        android:id="@+id/name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="大锅真帅"
        android:textSize="12dp"
        android:layout_centerHorizontal="true"
        android:textColor="#999CB4"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginTop="4dp"
        android:layout_below="@id/head_iv"/>

    <TextView
        android:id="@+id/show_role_iv"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:text="@string/party_spy_verify_identity"
        android:textSize="12dp"
        android:textColor="@color/color_accent"
        android:textStyle="bold"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:background="@drawable/shape_stroke_accent_corner16"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginTop="4dp"
        android:layout_below="@id/name_tv"/>

    <ImageView
        android:id="@+id/role_iv"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:layout_centerHorizontal="true"
        android:layout_below="@id/name_tv"
        android:layout_marginTop="4dp"
        android:adjustViewBounds="true"
        android:minWidth="40dp"
        android:src="@drawable/party_spy_role2"
        android:visibility="gone"
        tools:visibility="visible"/>
</RelativeLayout>