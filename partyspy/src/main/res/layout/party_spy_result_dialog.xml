<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/result_head_bg_iv"
            android:layout_width="244dp"
            android:layout_height="244dp"
            android:src="@drawable/party_spy_win_sunshine"
            app:layout_constraintTop_toTopOf="@id/head_lay"
            app:layout_constraintBottom_toBottomOf="@id/head_lay"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <ImageView
            android:id="@+id/title_iv"
            android:layout_width="222dp"
            android:layout_height="60dp"
            android:src="@drawable/party_spy_victory_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <FrameLayout
            android:id="@+id/head_lay"
            android:layout_width="wrap_content"
            android:layout_height="98dp"
            android:layout_marginTop="38dp"
            app:layout_constraintTop_toBottomOf="@+id/title_iv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.huiwan.decorate.DecorHeadImgView
                android:id="@+id/head_iv"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_gravity="center_horizontal"
                tools:src="@drawable/default_head_icon"/>

            <ImageView
                android:id="@+id/role_iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="66dp"
                android:layout_gravity="center_horizontal"
                tools:src="@drawable/party_spy_role2_big"/>
        </FrameLayout>

        <TextView
            android:id="@+id/citizen_word_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/party_citizen_word"
            android:textColor="#ffffff"
            android:textSize="16dp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginTop="20dp"
            app:layout_constraintTop_toBottomOf="@+id/head_lay"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/citizen_word_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="最长文案，与屏幕左右固定间距为16最长文案，与屏幕左右固定间距为16"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="#ffffff"
            android:textSize="20dp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginTop="12dp"
            app:layout_constraintTop_toBottomOf="@+id/citizen_word_tip"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <ImageView
            android:id="@+id/enter_tv"
            android:layout_width="200dp"
            android:layout_height="60dp"
            android:layout_marginTop="40dp"
            android:background="@drawable/party_spy_sure_item_selector"
            app:layout_constraintTop_toBottomOf="@+id/citizen_word_tv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>