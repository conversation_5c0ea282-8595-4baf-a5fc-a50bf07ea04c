<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0E162B"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:clipChildren="false">

   <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:has_action_bar="false"
        app:action_bar_color="@color/transparent"/>

    <RelativeLayout
        android:id="@+id/title_lay"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="54dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/back_iv"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/party_back_icon"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:scaleX="@integer/image_scale_x"/>

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="24dp"
            android:textSize="16dp"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            tools:text="创建房间"/>
    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/num_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="41dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_lay">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/party_spy_users_name"
            android:textSize="16dp"
            android:textColor="@color/white"
            android:layout_marginStart="20dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="@+id/num_tv"/>

        <TextView
            android:id="@+id/num_tv"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            tools:text="4"
            android:textSize="28dp"
            android:textColor="@color/white"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/people_tv"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/people_tv"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:text="@string/party_select_players_suffix"
            android:textSize="14dp"
            android:textColor="@color/white"
            android:gravity="bottom"
            android:layout_marginEnd="21dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <SeekBar
        android:id="@+id/num_seek_bar"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:minHeight="12dp"
        android:maxHeight="12dp"
        android:progressDrawable="@drawable/party_spy_seekbar_bg"
        android:thumb="@drawable/party_spy_thumb_bg"
        android:splitTrack="false"
        android:thumbOffset="0dp"
        android:layout_marginTop="16dp"
        android:max="8"
        android:progress="2"
        app:layout_constraintTop_toBottomOf="@id/num_lay"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/card_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:orientation="horizontal"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintTop_toBottomOf="@id/num_seek_bar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:id="@+id/normal_lay"
            android:layout_width="97dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/normal_iv"
                android:layout_width="97dp"
                android:layout_height="116dp"
                android:src="@drawable/party_spy_card1_icon" />

            <TextView
                android:id="@+id/normal_tv"
                android:layout_width="wrap_content"
                android:layout_height="31dp"
                tools:text="x3"
                android:textSize="22dp"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:gravity="center" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/spy_lay"
            android:layout_width="97dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_marginStart="16dp"
            app:layout_constraintStart_toEndOf="@+id/normal_lay"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/spy_iv"
                android:layout_width="97dp"
                android:layout_height="116dp"
                android:src="@drawable/party_spy_card2_icon" />

            <TextView
                android:id="@+id/spy_tv"
                android:layout_width="wrap_content"
                android:layout_height="31dp"
                tools:text="x3"
                android:textSize="22dp"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:gravity="center" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/select_title_tv"
        android:layout_width="wrap_content"
        android:layout_height="25dp"
        android:text="@string/party_spy_thesaurus_select"
        android:textColor="#FFFFFF"
        android:textSize="18dp"
        android:gravity="center"
        android:layout_marginTop="32dp"
        android:layout_marginStart="20dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/card_lay"/>

    <LinearLayout
        android:id="@+id/word_update_lay"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:orientation="horizontal"
        android:layout_marginEnd="20dp"
        android:gravity="center_vertical"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:background="@drawable/shape_33ffffff_corner16_stroke1"
        app:layout_constraintTop_toTopOf="@+id/select_title_tv"
        app:layout_constraintBottom_toBottomOf="@+id/select_title_tv"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/party_spy_word_update"/>
        <TextView
            android:id="@+id/change_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/party_spy_word_update"
            android:textColor="#FFFFFF"
            android:textSize="12dp"
            android:includeFontPadding="false"
            android:layout_marginStart="4dp" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/spy_rv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="17dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/select_title_tv"
        tools:layoutManager="GridLayoutManager"
        tools:spanCount="3"
        tools:itemCount="6"
        tools:listitem="@layout/party_spy_edit_item"/>

    <ImageView
        android:id="@+id/enter_iv"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:gravity="center"
        android:background="@drawable/party_spy_sure_item_selector"
        android:layout_marginBottom="54dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>