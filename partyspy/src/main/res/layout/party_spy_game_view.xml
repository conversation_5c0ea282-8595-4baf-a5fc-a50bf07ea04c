<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/party_spy_start_bg"
    android:clipChildren="false">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:has_action_bar="false"
        app:action_bar_color="@color/transparent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/title_lay"
        android:layout_width="match_parent"
        android:layout_height="88dp"
        android:layout_marginTop="30dp"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/back_iv"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/party_back_icon"
            android:layout_marginStart="16dp"
            android:layout_marginBottom="6dp"
            android:layout_alignParentBottom="true"
            android:scaleX="@integer/image_scale_x"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <LinearLayout
            android:id="@+id/room_id_lay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="7dp"
            android:layout_marginBottom="10dp"
            android:gravity="center_horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/party_room_id"
                android:gravity="center"
                android:textSize="12dp"
                android:textColor="@color/white"
                android:textStyle="bold"/>

            <TextView
                android:id="@+id/room_id_tv"
                android:layout_width="wrap_content"
                android:layout_height="34dp"
                tools:text="123456"
                android:textSize="20dp"
                android:textColor="@color/white"
                android:textStyle="bold"/>
        </LinearLayout>

        <!--<TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_e6ffffff_corner24"
            android:padding="16dp"
            android:textColor="#333333"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="游戏开始，请从3号开始发言"/>-->

        <ImageView
            android:id="@+id/edit_iv"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/party_spy_game_edit_icon"
            android:layout_alignParentBottom="true"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/card_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        app:layout_constraintTop_toBottomOf="@id/title_lay"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:id="@+id/game_start_lay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:visibility="visible">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/game_start_civilian_lay"
                android:layout_width="0dp"
                android:layout_height="54dp"
                android:background="@drawable/shape_1affffff_33000000_corner8"
                android:visibility="gone"
                android:layout_weight="1"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/game_start_civilian_num_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:text="3"
                    android:textSize="20dp"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:gravity="center_horizontal"
                    android:includeFontPadding="false"
                    app:layout_constraintVertical_chainStyle="packed"
                    app:layout_constraintBottom_toTopOf="@+id/game_start_civilian_name_tv"/>

                <TextView
                    android:id="@+id/game_start_civilian_name_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/party_room_result_vote_dialog_role_good"
                    android:textSize="12dp"
                    android:textColor="#999CB4"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/game_start_civilian_num_tv"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:includeFontPadding="false"
                    android:gravity="center_horizontal" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/game_start_spy_lay"
                android:layout_width="0dp"
                android:layout_height="54dp"
                android:layout_weight="1"
                android:background="@drawable/shape_1affffff_33000000_corner8"
                android:visibility="gone"
                android:layout_marginStart="8dp"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/game_start_spy_num_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:text="3"
                    android:textSize="20dp"
                    android:textColor="@color/white"
                    android:textStyle="bold"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toTopOf="@+id/game_start_spy_tv"
                    app:layout_constraintVertical_chainStyle="packed"
                    android:includeFontPadding="false"
                    android:gravity="center_horizontal" />

                <TextView
                    android:id="@+id/game_start_spy_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/party_room_result_vote_dialog_role_spy"
                    android:textSize="12dp"
                    android:textColor="#999CB4"
                    android:paddingStart="10dp"
                    android:paddingEnd="10dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/game_start_spy_num_tv"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:includeFontPadding="false"
                    android:gravity="center_horizontal" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/game_init_lay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:visibility="gone">
            <LinearLayout
                android:id="@+id/normal_lay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintEnd_toStartOf="@+id/spy_lay"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/normal_iv"
                    android:layout_width="97dp"
                    android:layout_height="116dp"
                    android:src="@drawable/party_spy_card1_icon"
                    tools:visibility="visible"/>

                <TextView
                    android:id="@+id/civilian_num_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="3人"
                    android:background="@drawable/shape_1affffff_corner12"
                    android:layout_gravity="center"
                    android:textSize="12dp"
                    android:textColor="@color/white"
                    android:layout_marginTop="7dp"
                    android:paddingStart="8dp"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp"
                    android:paddingEnd="8dp"
                    android:gravity="center"
                    tools:visibility="visible"/>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/spy_lay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:layout_marginStart="16dp"
                app:layout_constraintStart_toEndOf="@+id/normal_lay"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/spy_iv"
                    android:layout_width="97dp"
                    android:layout_height="116dp"
                    android:src="@drawable/party_spy_card2_icon"
                    tools:visibility="visible"/>

                <TextView
                    android:id="@+id/spy_num_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="3人"
                    android:background="@drawable/shape_1affffff_corner12"
                    android:layout_gravity="center"
                    android:textSize="12dp"
                    android:textColor="@color/white"
                    android:layout_marginTop="7dp"
                    android:paddingStart="8dp"
                    android:paddingTop="4dp"
                    android:paddingBottom="4dp"
                    android:paddingEnd="8dp"
                    android:gravity="center"
                    tools:visibility="visible"/>

            </LinearLayout>
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/seat_list_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/card_lay"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="22dp">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <LinearLayout
                    android:id="@+id/seat1_lay"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="5dp"
                    android:clipChildren="false">
                    <party.room.seat.PartySpySeatView
                        android:id="@+id/seat1"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:clipChildren="false"/>

                    <party.room.seat.PartySpySeatView
                        android:id="@+id/seat2"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:clipChildren="false"/>

                    <party.room.seat.PartySpySeatView
                        android:id="@+id/seat3"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:clipChildren="false"/>

                    <party.room.seat.PartySpySeatView
                        android:id="@+id/seat4"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:clipChildren="false"/>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/seat2_lay"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="8dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="5dp"
                    android:clipChildren="false">
                    <party.room.seat.PartySpySeatView
                        android:id="@+id/seat5"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:clipChildren="false"/>

                    <party.room.seat.PartySpySeatView
                        android:id="@+id/seat6"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:clipChildren="false"/>

                    <party.room.seat.PartySpySeatView
                        android:id="@+id/seat7"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:clipChildren="false"/>

                    <party.room.seat.PartySpySeatView
                        android:id="@+id/seat8"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:clipChildren="false"/>
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/seat3_lay"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="8dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="5dp"
                    android:clipChildren="false">
                    <party.room.seat.PartySpySeatView
                        android:id="@+id/seat9"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:clipChildren="false"/>

                    <party.room.seat.PartySpySeatView
                        android:id="@+id/seat10"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:clipChildren="false"/>

                    <party.room.seat.PartySpySeatView
                        android:id="@+id/seat11"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:clipChildren="false"/>

                    <party.room.seat.PartySpySeatView
                        android:id="@+id/seat12"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:clipChildren="false"/>
                </LinearLayout>
            </LinearLayout>
        </ScrollView>

    </FrameLayout>

    <TextView
        android:id="@+id/word_tv"
        android:layout_width="280dp"
        android:layout_height="114dp"
        tools:text="何炅"
        android:textSize="25dp"
        android:textColor="@color/white"
        android:textStyle="bold"
        android:gravity="center"
        app:layout_goneMarginBottom="54dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/party_spy_word_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/word_change_iv"
        android:visibility="gone"
        tools:visibility="visible"/>

    <TextView
        android:id="@+id/word_change_iv"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:text="@string/party_spy_change_word"
        android:textSize="14dp"
        android:textColor="#ffffff"
        android:gravity="center"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="7dp"
        android:paddingBottom="7dp"
        android:background="@drawable/shape_1affffff_corner16"
        android:layout_marginBottom="54dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        tools:visibility="visible"/>

    <LinearLayout
        android:id="@+id/start_lay"
        android:layout_width="wrap_content"
        android:layout_marginTop="43dp"
        android:layout_height="60dp"
        android:orientation="horizontal"
        android:layout_marginBottom="54dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="gone">

        <ImageView
            android:id="@+id/share_tv"
            android:layout_width="160dp"
            android:layout_height="60dp"
            android:background="@drawable/party_spy_invite_friends_item_selector"/>

        <ImageView
            android:id="@+id/start_tv"
            android:layout_width="160dp"
            android:layout_height="60dp"
            android:layout_marginStart="16dp"
            android:background="@drawable/party_spy_start_game_item_selector"
            />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/current_word_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp"
        app:layout_constraintBottom_toTopOf="@+id/start_lay"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14dp"
            android:textColor="#F0D5AE"
            android:textStyle="bold"
            android:text="@string/party_game_current_type_word_title"/>

        <TextView
            android:id="@+id/current_word"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="14dp"
            android:textColor="#F0D5AE"
            android:textStyle="bold"
            tools:text="西天取经"
            android:layout_marginStart="8dp"/>
    </LinearLayout>

    <TextView
        android:id="@+id/wait_tips_tv"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:text="@string/party_spy_waitting_for_host_start_game"
        android:textSize="20dp"
        android:textColor="#FFFFFF"
        android:gravity="center"
        android:layout_marginBottom="33dp"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/start_lay"
        android:visibility="gone"
        tools:visibility="gone"/>

</androidx.constraintlayout.widget.ConstraintLayout>