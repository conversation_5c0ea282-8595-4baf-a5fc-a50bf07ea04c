<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="438dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/party_spy_bombing_dialog_bg">

    <TextView
        android:id="@+id/bombing_word_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="24dp"
        android:textStyle="bold"
        android:textColor="#FFFFFF"
        android:text="@string/party_game_bombing_word"/>

    <TextView
        android:id="@+id/bombing_word_left_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/bombing_word_tv"
        app:layout_constraintBottom_toBottomOf="@+id/bombing_word_tv"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="16dp"
        android:textColor="#80FFFFFF"
        tools:text="15s"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/bombing_word_rv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="60dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone"
        tools:visibility="visible"
        tools:layoutManager="GridLayoutManager"
        tools:spanCount="2"
        tools:itemCount="8"
        tools:listitem="@layout/party_spy_bombing_word_item"/>

    <ImageView
        android:id="@+id/bombing_word_sure_iv"
        android:layout_width="200dp"
        android:layout_height="60dp"
        android:background="@drawable/party_spy_sure_btn_disable"
        android:layout_marginBottom="58dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:clickable="false"
        android:visibility="gone"
        tools:visibility="visible"/>

    <com.huiwan.decorate.DecorHeadImgView
        android:id="@+id/head_iv"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:src="@drawable/party_head_image_icon"
        android:layout_marginTop="148dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="visible"
        tools:visibility="visible"/>

    <TextView
        android:id="@+id/num_tv"
        android:layout_width="16dp"
        android:layout_height="16dp"
        tools:text="1"
        android:textSize="11dp"
        android:textColor="#1B1D38"
        android:background="@drawable/shape_ffffff_oval"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="@+id/head_iv"
        app:layout_constraintEnd_toEndOf="@+id/head_iv"
        android:visibility="gone"
        tools:visibility="visible"/>

    <TextView
        android:id="@+id/name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="大锅真帅"
        android:textSize="12dp"
        android:layout_centerHorizontal="true"
        android:textColor="#999CB4"
        android:textStyle="bold"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginTop="4dp"
        app:layout_constraintTop_toBottomOf="@+id/head_iv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"
        tools:visibility="visible"/>

    <TextView
        android:id="@+id/bomb_desc_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/party_game_bombing_tip"
        android:textColor="#F0D5AE"
        android:textSize="14dp"
        android:layout_marginTop="32dp"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@+id/name_tv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"
        tools:visibility="visible"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bomb_word_result_lay"
        android:layout_width="188dp"
        android:layout_height="44dp"
        android:layout_marginTop="32dp"
        app:layout_constraintTop_toBottomOf="@+id/name_tv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"
        tools:background="@drawable/party_spy_bomb_word_success_bg"
        tools:visibility="visible">

        <TextView
            android:id="@+id/bomb_result_word"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="32dp"
            android:textSize="14dp"
            android:textColor="#FFFFFF"
            tools:text="大锅真帅"/>

        <ImageView
            android:id="@+id/bomb_result_icon"
            android:layout_width="16dp"
            android:layout_height="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="10dp"
            tools:src="@drawable/party_spy_bomb_word_success_icon"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>