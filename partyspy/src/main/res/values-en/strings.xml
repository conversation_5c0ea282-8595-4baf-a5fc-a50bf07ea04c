<resources>
    <string name="party_spy_change_config">Change Configuration</string>
    <string name="party_spy_change_config_tips">The current match ends after the configuration is modified. Are you sure to modify it？</string>
    <string name="party_spy_cannot_intput_empty_room_num">The room number entered cannot be empty</string>
    <string name="party_spy_chenge_config_and_restart_tips">The number of people in the room has changed. Please modify the configuration and start again</string>
    <string name="party_spy_wait_for_host_restart_tips">The number of people in the room has changed, please wait for the Room Owner to start again</string>
    <string name="party_spy_you_have_been_remove_to_room">You\'ve been kicked out of the room</string>
    <string name="party_spy_return_to_first_pager">Back to Homepage</string>
    <string name="party_spy_users_name">Players</string>
    <string name="party_spy_thesaurus_select">Select a category</string>
    <string name="party_spy_word_update">Refresh</string>
    <string name="party_spy_change_word_tips">Are you sure to change the word? After it was changed, the game will restart</string>
    <string name="party_spy_waitting_for_host_start_game">Waiting for the game to start</string>
    <string name="party_spy_change_word">Change a word</string>
    <string name="party_spy_confirm_role_tips">Once verified, the player will be out. Are you sure to view the identity of No.%1$d?</string>
    <string name="party_spy_game_start_spearking_tips">Starts from No.%1$d</string>
    <string name="party_spy_check_result">View the result</string>
    <string name="party_spy_start_spearking_tips">Game on, starts from No.%1$d</string>
    <string name="party_spy_you_are_the_special_spy">You are the Spy</string>
    <string name="party_spy_you_role">Character: %1$s</string>
    <string name="party_spy_x_person">%1$d&#160;&#160;player</string>
    <string name="party_spy_change_sucessfully">Modified successfully</string>
    <string name="party_spy_change_config_tips_when_not_gaming">After changing the number of people in the room, the words will be reassigned. Are you sure to change it?</string>
    <string name="party_spy_i_kown">Got it</string>
    <string name="party_spy_verify_identity">Verify</string>

    <string name="party_create_room">Create Room</string>
    <string name="party_citizen_word">Villagers\' Word</string>
    <string name="party_spy_win">Spy Won</string>
    <string name="party_room_result_vote_dialog_role_good">Villager</string>
    <string name="party_room_result_vote_dialog_role_spy">Spy</string>
    <string name="party_game_common_game_over">Game Over</string>
    <string name="party_room_game_continue">No spy found</string>
    <string name="party_villagers_won">Villagers won</string>
    <string name="party_exit_room_tip">Exit from the room?</string>
    <string name="party_room_id">Room ID</string>
    <string name="party_select_players_suffix">people</string>
    <string name="party_game_start">Start</string>
    <string name="party_host">Owner</string>
    <string name="party_game_current_type_word_title">Category of words:</string>
    <string name="party_game_word_random_title">Random</string>
    <string name="party_game_bombing_word">Guess</string>
    <string name="party_game_bombing_word_success">Guess Succeeded</string>
    <string name="party_game_bombing_word_fail">Guess Failed</string>
    <string name="party_game_find_spy">Spotted Spy</string>
    <string name="party_game_bombing_tip">He is guessing the villagers\' word \n once he guessed correctly, the spies win</string>
    <string name="party_game_bombing_word_give_up">Give up</string>
    <string name="party_game_invite_friend">Invite Friends</string>
    <string name="party_game_sure">Confirm</string>
    <string name="party_game_cancel">Cancel</string>
    <string name="party_game_seat_num_less_tip">The seats need to be full before the game start</string>
</resources>