package net.tcp.sender;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.constants.GameType;
import com.huiwan.libtcp.PacketSendHelper;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.config.ClientVersionUtil;
import com.huiwan.libtcp.config.ProtoConfig;
import com.huiwan.libtcp.sender.TcpBaseSender;
import com.huiwan.user.LoginHelper;
import com.wepie.wespy.net.tcp.packet.CommonPackets;
import com.wepie.wespy.net.tcp.packet.HeadPackets;
import com.wepie.wespy.net.tcp.packet.PartyRoomPackets;

import java.util.List;

import party.room.mode.PartyRoomCreateInfo;
import party.spy.edit.PartySpyEditInfo;

public class PartyRoomPacketSender {
    public static void partySpyCreateReq(int rid, PartySpyEditInfo info, int wordType, SeqCallback callback) {
        if (info == null) {
            return;
        }
        PartyRoomCreateInfo createInfo = new PartyRoomCreateInfo(GameType.GAME_TYPE_PARTY_SPY, info.getAllCount(), info.getSpyCount(), info.getNormalCount(), info.getWhiteboardCount(), wordType);
        partyRoomCreateReq(rid, createInfo, callback);
    }

    private static void partyRoomCreateReq(int rid, PartyRoomCreateInfo info, SeqCallback callback) {
        if (info == null) {
            return;
        }
        PartyRoomPackets.PartyCreateRoomReq req =  PartyRoomPackets.PartyCreateRoomReq.newBuilder()
                .setGameType(info.gameType)
                .setSeatCount(info.seatCount)
                .setFaguanCount(info.faguanCount)
                .setWolfCitizenCount(info.wolfCitizenCount)
                .setWolfCount(info.wolfCount)
                .setProphetCount(info.prophetCount)
                .setWitchCount(info.witchCount)
                .setProphetCount(info.prophetCount)
                .setWitchCount(info.witchCount)
                .setHunterCount(info.hunterCount)
                .setGuardCount(info.guardCount)
                .setSpyCount(info.spyCount)
                .setSpyCivilianCount(info.spyCivilianCount)
                .setWordLib(info.wordLib)
                .build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.CREATE_ROOM_VALUE),
                getBodyBuilder().setCreate(req).build()
                , null, callback);
    }

    public static void partyRoomEnterReq(int rid, SeqCallback callback) {
        PartyRoomPackets.PartyEnterRoomReq req =  PartyRoomPackets.PartyEnterRoomReq.newBuilder()
                .setRid(rid)
                .build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.ENTER_ROOM_VALUE),
                getBodyBuilder().setEnter(req).build()
                , null, callback);
    }

    public static void partyRoomSyncReq(int rid, SeqCallback callback) {
        PartyRoomPackets.PartySyncRoomReq req =  PartyRoomPackets.PartySyncRoomReq.newBuilder()
                .build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.SYNC_ROOM_VALUE),
                getBodyBuilder().setSyncRoom(req).build()
                , null, callback);
    }

    public static void partyRoomExitReq(int rid, SeqCallback callback) {
        PartyRoomPackets.PartyExitRoomReq req =  PartyRoomPackets.PartyExitRoomReq.newBuilder()
                .setRid(rid)
                .build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.EXIT_ROOM_VALUE),
                getBodyBuilder().setExit(req).build()
                , null, callback);
    }

    public static void partyRoomStartReq(int rid, SeqCallback callback) {
        PartyRoomPackets.PartyStartGameReq req =  PartyRoomPackets.PartyStartGameReq.newBuilder()
                .build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.START_GAME_VALUE),
                getBodyBuilder().setStart(req).build()
                , null, callback);
    }

    public static void partyRoomAgainReq(int rid, SeqCallback callback) {
        PartyRoomPackets.PartyOneMoreGameReq req =  PartyRoomPackets.PartyOneMoreGameReq.newBuilder()
                .build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.ONE_MORE_GAME_VALUE),
                getBodyBuilder().setOneMore(req).build()
                , null, callback);
    }

    public static void partySpyEditReq(int rid, PartySpyEditInfo info, int wordType, SeqCallback callback) {
        if (info == null) {
            return;
        }
        PartyRoomCreateInfo createInfo = new PartyRoomCreateInfo(GameType.GAME_TYPE_PARTY_SPY, info.getAllCount(), info.getSpyCount(), info.getNormalCount(), info.getWhiteboardCount(), wordType);
        partyRoomEditReq(rid, createInfo, callback);
    }

    private static void partyRoomEditReq(int rid, PartyRoomCreateInfo info, SeqCallback callback) {
        if (info == null) {
            return;
        }
        PartyRoomPackets.PartyModRoomConfigReq req =  PartyRoomPackets.PartyModRoomConfigReq.newBuilder()
                .setGameType(info.gameType)
                .setFaguanCount(info.faguanCount)
                .setWolfCitizenCount(info.wolfCitizenCount)
                .setWolfCount(info.wolfCount)
                .setProphetCount(info.prophetCount)
                .setWitchCount(info.witchCount)
                .setProphetCount(info.prophetCount)
                .setWitchCount(info.witchCount)
                .setHunterCount(info.hunterCount)
                .setGuardCount(info.guardCount)
                .setSpyCount(info.spyCount)
                .setCivilianCount(info.spyCivilianCount)
                .setWhiteBoardCount(info.whiteBoardCount)
                .setWordLib(info.wordLib)
                .build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.MOD_ROOM_CONFIG_VALUE),
                getBodyBuilder().setModRoom(req).build()
                , null, callback);
    }

    public static void partyCheckResultReq(int rid, int seatNum, SeqCallback callback) {
        PartyRoomPackets.PartyCheckResultReq req =  PartyRoomPackets.PartyCheckResultReq.newBuilder()
                .setSeatNum(seatNum)
                .build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.CHECK_RESULT_VALUE),
                getBodyBuilder().setCheckResult(req).build()
                , null, callback);
    }

    public static void partyInviteReq(int rid, List<Integer> uidList, int gameType, SeqCallback callback) {
        PartyRoomPackets.PartyInviteReq req =  PartyRoomPackets.PartyInviteReq.newBuilder()
                .addAllInviteList(uidList)
                .setGameType(gameType)
                .build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.INVITE_FRIEND_VALUE),
                getBodyBuilder().setInvite(req).build()
                , null, callback);
    }

    public static void partySendMsgReq(int rid, String content, SeqCallback callback) {
        PartyRoomPackets.PartySendRoomMsgReq req =  PartyRoomPackets.PartySendRoomMsgReq.newBuilder()
                .setSender(LoginHelper.getLoginUid())
                .setSenderName(LoginHelper.getNickname())
                .setMsgType(CommonPackets.MsgType.TEXT)
                .setMsgSubtype(CommonPackets.MsgTextSubType.TEXT_NORMAL_VALUE)
                .setContent(content)
                .setExt("")
                .build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.SEND_ROOM_MSG_VALUE),
                getBodyBuilder().setSendMsg(req).build()
                , null, callback);
    }

    public static void partyGuessDrawReq(int rid, String content, SeqCallback callback) {
        PartyRoomPackets.PartyGuessDrawReq req =  PartyRoomPackets.PartyGuessDrawReq.newBuilder()
                .setContent(content)
                .build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.GUESS_DRAW_VALUE),
                getBodyBuilder().setGuessDraw(req).build()
                , null, callback);
    }

    public static void partyLikeDrawReq(int rid, int uid, SeqCallback callback) {
        PartyRoomPackets.PartyLikeDrawReq req =  PartyRoomPackets.PartyLikeDrawReq.newBuilder()
                .setUid(uid)
                .build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.LIKE_DRAW_VALUE),
                getBodyBuilder().setDrawLike(req).build()
                , null, callback);
    }

    public static void partyKickUserReq(int rid, int seatNum, SeqCallback callback) {
        PartyRoomPackets.PartyKickUserReq req =  PartyRoomPackets.PartyKickUserReq.newBuilder()
                .setSeatNum(seatNum)
                .build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.KICK_USER_VALUE),
                getBodyBuilder().setKick(req).build()
                , null, callback);
    }

    public static void partyChangeOwner(int rid, int uid, SeqCallback callback) {
        PartyRoomPackets.PartyChangeOwnerReq req =  PartyRoomPackets.PartyChangeOwnerReq.newBuilder()
                .setUid(uid)
                .build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.CHANGE_OWNER_VALUE),
                getBodyBuilder().setChangeOwner(req).build()
                , null, callback);
    }

    public static void partySpyBombWord(int rid, String bombWord, SeqCallback callback) {
        PartyRoomPackets.PartyBombWordReq req = PartyRoomPackets.PartyBombWordReq.newBuilder().setBombWord(bombWord).build();
        PacketSendHelper.sendVoiceRoomPacket(buildPartyRoomReqHead(rid, PartyRoomPackets.PartyRoomOpType.BOMB_WORD_VALUE),
                getBodyBuilder().setBombWord(req).build(), null, callback);
    }

    private static PartyRoomPackets.PartyRoomReqBody.Builder getBodyBuilder() {
        return PartyRoomPackets.PartyRoomReqBody.newBuilder();
    }

    private static HeadPackets.ReqHead buildPartyRoomReqHead(int rid, int type) {
        return HeadPackets.ReqHead.newBuilder()
                .setCommand(HeadPackets.CommandType.PARTYROOM_VALUE)
                .setType(type)
                .setUid(LoginHelper.getLoginUid())
                .setRid(rid)
                .setSeq(TcpBaseSender.getSeq())
                .setMaxGameType(GameType.MAX_GAME_TYPE)
                .setGameVersion(ClientVersionUtil.VERSION)
                .setLang(LibBaseUtil.getLang().value)
                .build();
    }
}
