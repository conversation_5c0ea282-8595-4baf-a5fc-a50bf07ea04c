package net.tcp.handler;

import com.google.protobuf.GeneratedMessageLite;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.HuiwanPacketHandlerInterface;
import com.huiwan.libtcp.huiwan.HuiwanSeqCallbackManager;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.wepie.wespy.net.tcp.packet.HeadPackets;
import com.wepie.wespy.net.tcp.packet.PartyRoomPackets;
import com.wepie.wespy.net.tcp.packet.PartyRoomPushPackets;
import com.wepie.wespy.net.tcp.packet.PushPackets;

import net.tcp.sender.PartyRoomPacketSender;

import org.greenrobot.eventbus.EventBus;

import party.event.PartyKickUserEvent;
import party.event.PartyRoomEnterEvent;
import party.room.PartyRoomManager;
import party.room.mode.PartyRoomInfo;

public class PartyRoomHandler implements HuiwanPacketHandlerInterface {
    public static void handlePushMsg(GeneratedMessageLite message, RspHeadInfo rspHeadInfo) {
        PartyRoomPushPackets.PartyRoomPush push = (PartyRoomPushPackets.PartyRoomPush)message;
        if (push == null) {
            return;
        }
        switch (push.getPushType()) {
            case PARTYROOM_SYS_SYNC_ROOM:
                PartyRoomPacketSender.partyRoomSyncReq(rspHeadInfo.rid, new SeqCallback() {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        PartyRoomPackets.PartySyncRoomRsp rsp = (PartyRoomPackets.PartySyncRoomRsp) head.message;
                        PartyRoomManager.get().getRoomInfoLiveData().setValue(PartyRoomInfo.parse(rsp.getRoomInfo()));
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                });
                break;
            case PARTYROOM_SYS_SEND_MSG:
                break;
            case PARTYROOM_SYS_SHOW_ROLE:
                PartyRoomPacketSender.partyRoomSyncReq(rspHeadInfo.rid, new SeqCallback() {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        PartyRoomPackets.PartySyncRoomRsp rsp = (PartyRoomPackets.PartySyncRoomRsp) head.message;
                        PartyRoomManager.get().getRoomInfoLiveData().setValue(PartyRoomInfo.parse(rsp.getRoomInfo()));
                        PartyRoomManager.get().getRoleSeatNumLiveData().setValue(push.getShowRole().getSeatNum());
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                });
                break;
            case PARTYROOM_SYS_ONE_MORE:
                PartyRoomManager.get().setNeedShowStart(true);
                PartyRoomPacketSender.partyRoomSyncReq(rspHeadInfo.rid, new SeqCallback() {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        PartyRoomPackets.PartySyncRoomRsp rsp = (PartyRoomPackets.PartySyncRoomRsp) head.message;
                        PartyRoomManager.get().getRoomInfoLiveData().setValue(PartyRoomInfo.parse(rsp.getRoomInfo()));
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                });
                break;
            case PARTYROOM_SYS_KICKED:
                EventBus.getDefault().post(new PartyKickUserEvent(rspHeadInfo.rid, false));
                break;
            case PARTYROOM_SYS_ENTER_ROOM:
                EventBus.getDefault().post(new PartyRoomEnterEvent(rspHeadInfo.rid));
                break;
            case PARTYROOM_SYS_KICK_IN_GAME:
                EventBus.getDefault().post(new PartyKickUserEvent(rspHeadInfo.rid, true));
                break;

        }
    }

    public static void handleMsg(GeneratedMessageLite message, RspHeadInfo rspHeadInfo) {
        rspHeadInfo.message = message;
        SeqCallback callback = HuiwanSeqCallbackManager.getInstance().getCallback(rspHeadInfo.seq, rspHeadInfo.command, rspHeadInfo.type, rspHeadInfo.code);
        if (callback != null) callback.onSuccess(rspHeadInfo);
    }

    @Override
    public boolean invokeHandleMethod(RspHeadInfo rspHeadInfo, GeneratedMessageLite message) {
        int command = rspHeadInfo.command;
        if (command == HeadPackets.CommandType.PUSH_VALUE) {
            if (rspHeadInfo.type == PushPackets.PushType.SEND_PARTYROOM_VALUE) {
                PartyRoomHandler.handlePushMsg(message, rspHeadInfo);
                return true;
            }
        } else if (command == HeadPackets.CommandType.PARTYROOM_VALUE) {
            PartyRoomHandler.handleMsg(message, rspHeadInfo);
            return true;
        }
        return false;
    }
}
