package party.room.seat;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.FrameLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.lib.api.ApiService;
import com.huiwan.user.UserService;
import com.huiwan.user.UserSimpleInfoCallback;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.lib.api.plugins.hwroom.HwRoomApi;
import party.room.PartyRoomManager;
import party.room.mode.PartyRoomInfo;
import party.room.mode.PartySeatInfo;

public abstract class PartyBaseSeatView extends FrameLayout {
    public static final int SCENE_PARTY_ROOM = 5;
    DecorHeadImgView headIv;
    TextView ownerTv;
    TextView nameTv;

    public PartyBaseSeatView(@NonNull Context context) {
        super(context);
        initView();

    }

    public PartyBaseSeatView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    protected abstract void initView();

    public void update(PartySeatInfo info, boolean isOwner) {
        ownerTv.setVisibility(isOwner ? VISIBLE : GONE);
        if (info.getUid() > 0) {
            UserService.get().getCacheSimpleUser(info.getUid(), new UserSimpleInfoCallback() {
                @Override
                public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                    nameTv.setText(simpleInfo.getRemarkName());
                }

                @Override
                public void onUserInfoFailed(String description) {

                }
            });
            headIv.setOnClickListener(v -> {
                PartyRoomInfo roomInfo = PartyRoomManager.get().getRoomInfoLiveData().getValue();
                if (roomInfo != null) {
                    int seatNum = 0;
                    for (int i = 0; i < roomInfo.getSeatInfoList().size(); i++) {
                        if (roomInfo.getSeatInfoList().get(i).getUid() == info.getUid()) seatNum = i + 1;
                    }
                    ApiService.of(HwRoomApi.class).showPartyUserDialog(getContext(), info.getUid(),
                            roomInfo.getRid(), roomInfo.getGameType(), seatNum, SCENE_PARTY_ROOM, "",
                            roomInfo.getOwner(), roomInfo.isStartedState());
                }
            });
        } else {
            nameTv.setText("");
        }
    }
}
