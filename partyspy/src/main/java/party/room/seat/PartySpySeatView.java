package party.room.seat;

import android.content.Context;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.partyspy.R;
import net.tcp.sender.PartyRoomPacketSender;

import party.room.PartyRoomManager;
import party.room.mode.PartySeatInfo;
import party.room.mode.PartySpyPlayerInfo;

public class PartySpySeatView extends PartyBaseSeatView {
    private TextView numTv;
    private TextView showRoleIv;
    private ImageView roleIv;
    private ImageView outIv;
    private ImageView passMaskIv;
    private final Paint paint = new Paint();

    public PartySpySeatView(@NonNull Context context) {
        super(context);
    }

    public PartySpySeatView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void initView() {
        inflate(getContext(), R.layout.party_spy_seat_view, this);
        headIv = findViewById(R.id.head_iv);
        numTv = findViewById(R.id.num_tv);
        ownerTv = findViewById(R.id.owner_tv);
        nameTv = findViewById(R.id.name_tv);
        showRoleIv = findViewById(R.id.show_role_iv);
        roleIv = findViewById(R.id.role_iv);
        outIv = findViewById(R.id.out_iv);
        passMaskIv = findViewById(R.id.pass_mask);
    }

    @Override
    public void update(PartySeatInfo info, boolean isOwner) {
        super.update(info, isOwner);
        numTv.setText(String.valueOf(info.getNum()));
        headIv.setVisibility(VISIBLE);
        if (info.getUid() > 0) {
            headIv.setClickable(true);
            headIv.showUserHead(info.getUid());
        } else {
            headIv.clear();
            headIv.setClickable(false);
            headIv.setImageResource(R.drawable.party_head_image_icon);
        }
    }

    public void refreshNoStart() {
        showRoleIv.setVisibility(GONE);
        roleIv.setVisibility(GONE);
        outIv.setVisibility(GONE);
        passMaskIv.setVisibility(GONE);
        showGray(false);
    }

    public void refreshStarted(PartySpyPlayerInfo playerInfo, boolean isSelfOwner, int index, int startIndex) {
        showGray(playerInfo.isOver());
        if (playerInfo.isOver()) {
            showRoleIv.setVisibility(GONE);
            roleIv.setVisibility(VISIBLE);
            if (playerInfo.isCitizen()) {
                roleIv.setImageResource(R.drawable.party_spy_role1);
            } else if (playerInfo.isSpy()) {
                roleIv.setImageResource(R.drawable.party_spy_role2);
            }
            outIv.setVisibility(VISIBLE);
            passMaskIv.setVisibility(VISIBLE);
        } else {
            showRoleIv.setVisibility(isSelfOwner ? VISIBLE : GONE);
            showRoleIv.setOnClickListener(v -> {
                DialogBuild.newBuilder(showRoleIv.getContext()).setSingleBtn(false)
                        .setSureTx(R.string.party_game_sure)
                        .setCancelTx(R.string.party_game_cancel)
                        .setContent(ResUtil.getResource().getString(R.string.party_spy_confirm_role_tips, index))
                        .setCanCancel(true).setDialogCallback(new DialogBuild.DialogCallback() {
                            @Override
                            public void onClickSure() {
                                PartyRoomPacketSender.partyCheckResultReq(PartyRoomManager.get().getRid(), index, new SeqCallback() {
                                    @Override
                                    public void onSuccess(RspHeadInfo head) {

                                    }

                                    @Override
                                    public void onFail(RspHeadInfo head) {
                                        ToastUtil.show(head.desc);
                                    }
                                });
                            }

                            @Override
                            public void onClickCancel() {

                            }
                        }).show();
            });
            roleIv.setVisibility(GONE);
            outIv.setVisibility(GONE);
            passMaskIv.setVisibility(GONE);
        }
    }

    private void showGray(boolean isOver) {
        if (isOver) {
            ColorMatrix cm = new ColorMatrix();
            cm.setSaturation(0);
            paint.setColorFilter(new ColorMatrixColorFilter(cm));
        } else {
            paint.setColorFilter(null);
        }
        headIv.setLayerType(View.LAYER_TYPE_HARDWARE, paint);
    }
}
