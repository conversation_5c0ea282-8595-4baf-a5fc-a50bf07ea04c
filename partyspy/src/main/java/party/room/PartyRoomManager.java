package party.room;

import androidx.lifecycle.MutableLiveData;
import party.room.mode.PartyRoomInfo;

public class PartyRoomManager {
    private static volatile PartyRoomManager instance;
    private final MutableLiveData<PartyRoomInfo> roomInfoLiveData = new MutableLiveData<>();
    private final MutableLiveData<Integer> roleSeatNumLiveData = new MutableLiveData<>();
    private boolean needShowStart = true;

    private PartyRoomManager() {
    }

    public static PartyRoomManager get() {
        if (instance == null) {
            synchronized (PartyRoomManager.class) {
                if (instance == null) {
                    instance = new PartyRoomManager();
                }
            }
        }
        return instance;
    }

    public int getRid() {
        PartyRoomInfo info = roomInfoLiveData.getValue();
        if (info == null) return 0;
        return info.getRid();
    }

    public MutableLiveData<PartyRoomInfo> getRoomInfoLiveData() {
        return roomInfoLiveData;
    }


    public MutableLiveData<Integer> getRoleSeatNumLiveData() {
        return roleSeatNumLiveData;
    }

    public void setNeedShowStart(boolean needShowStart) {
        this.needShowStart = needShowStart;
    }

    public boolean isNeedShowStart() {
        return needShowStart;
    }
}

