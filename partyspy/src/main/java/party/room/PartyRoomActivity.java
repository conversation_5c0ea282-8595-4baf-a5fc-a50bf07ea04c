package party.room;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.widget.FrameLayout;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.GameType;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LoginHelper;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.partyspy.R;

import net.tcp.sender.PartyRoomPacketSender;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import party.event.PartyKickUserEvent;
import party.room.mode.PartyRoomInfo;
import party.room.view.IPartyBaseView;
import party.room.view.PartySpyGameView;

public class PartyRoomActivity extends BaseActivity {
    private FrameLayout rootView;
    private IPartyBaseView baseView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(rootView = new FrameLayout(this));
        StatusBarUtil.initStatusBar(this);
        StatusBarUtil.setStatusFontWhiteColor(this);
        PartyRoomManager.get().getRoomInfoLiveData().observe(this, this::update);
        PartyRoomManager.get().getRoleSeatNumLiveData().observe(this, this::showRole);
        EventBus.getDefault().register(this);
    }

    private void showRole(int seatNum) {
        PartyRoomInfo info = PartyRoomManager.get().getRoomInfoLiveData().getValue();
        if (baseView != null && info != null && seatNum != 0) {
            baseView.showRole(info, seatNum);
        }
    }

    private void update(PartyRoomInfo info) {
        if (baseView == null || baseView.getGameType() != info.getGameType()) {
            addView(info.getGameType());
        }
        if (baseView != null) baseView.update(info);
    }


    private void addView(int gameType) {
        rootView.removeAllViews();
        TrackUtil.funcDurationStart(TrackUtil.getGameTypeSource(gameType));
        if (gameType == GameType.GAME_TYPE_PARTY_SPY) {
            baseView = new PartySpyGameView(this);
        }
        if (baseView != null) {
            baseView.setCallback(() -> {
                doExit();
            });
            rootView.addView(baseView);
        }
    }

    @Override
    public void onBackPressed() {
        doExit();
//        super.onBackPressed();
    }

    private void doExit() {
        DialogBuild.newBuilder(this).setSingleBtn(false).setContent(ResUtil.getString(R.string.party_exit_room_tip))
                .setCanCancel(true).setDialogCallback(new DialogBuild.DialogCallback() {
                    @Override
                    public void onClickSure() {
                        PartyRoomPacketSender.partyRoomExitReq(PartyRoomManager.get().getRid(), new SeqCallback() {
                            @Override
                            public void onSuccess(RspHeadInfo head) {

                            }

                            @Override
                            public void onFail(RspHeadInfo head) {

                            }
                        });
                        finish();
                    }

                    @Override
                    public void onClickCancel() {

                    }
                }).show();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPartyKickUser(PartyKickUserEvent event) {
        PartyRoomInfo roomInfo = PartyRoomManager.get().getRoomInfoLiveData().getValue();
        if (roomInfo != null && roomInfo.getRid() == event.rid) {
            if (event.isGaming) {
                String text = LoginHelper.getLoginUid() == roomInfo.getOwner() ? ResUtil.getString(R.string.party_spy_chenge_config_and_restart_tips)
                        : ResUtil.getString(R.string.party_spy_wait_for_host_restart_tips);
                if (baseView != null) {
                    DialogBuild.newBuilder(this).setSingleBtn(true).setColorStateList(ResUtil.getColorStateList(R.color.sel_accent_ex_accent_gray)).setContent(text).setSureTx(R.string.party_spy_i_kown).setDialogCallback(() -> { }).show();
                }

            } else {
                DialogBuild.newBuilder(this).setSingleBtn(true).setColorStateList(ResUtil.getColorStateList(R.color.sel_accent_ex_accent_gray)).setContent(R.string.party_spy_you_have_been_remove_to_room).setCanCancel(false).setSureTx(R.string.party_spy_return_to_first_pager).setDialogCallback(this::finish).show();
            }
        }
    }

    @Override
    protected void clearMemory() {
        super.clearMemory();
        PartyRoomManager.get().getRoleSeatNumLiveData().setValue(0);
        EventBus.getDefault().unregister(this);
    }

    @Override
    protected void onDestroy() {
        if (baseView != null) {
            int rid = PartyRoomManager.get().getRid();

            TrackUtil.funcDurationEnd(TrackUtil.getGameTypeSource(baseView.getGameType()), rid);
        }
        super.onDestroy();
    }

    public static void go(Context context) {
        Intent intent = new Intent(context, PartyRoomActivity.class);
        context.startActivity(intent);
    }

}
