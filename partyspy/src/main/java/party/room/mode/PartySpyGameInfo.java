package party.room.mode;

import com.wepie.wespy.net.tcp.packet.PartyRoomPackets;
import java.util.List;

public class PartySpyGameInfo {
    private List<PartySpyPlayerInfo> playerInfoList;
    private int startIdx;
    private int spyCount;
    private int civilianCount;
    private int whiteboardCount;
    private int wordLib;
    private int victoryRole;
    private List<String> bombWordItems;
    private long currentStateLeftTime;
    private PartyRoomPackets.BombResult bombResult;
    private String wordCateName;

    public List<PartySpyPlayerInfo> getPlayerInfoList() {
        return playerInfoList;
    }

    public void setPlayerInfoList(List<PartySpyPlayerInfo> playerInfoList) {
        this.playerInfoList = playerInfoList;
    }

    public int getStartIdx() {
        return startIdx;
    }

    public void setStartIdx(int startIdx) {
        this.startIdx = startIdx;
    }

    public int getSpyCount() {
        return spyCount;
    }

    public void setSpyCount(int spyCount) {
        this.spyCount = spyCount;
    }

    public int getCivilianCount() {
        return civilianCount;
    }

    public void setCivilianCount(int civilianCount) {
        this.civilianCount = civilianCount;
    }

    public int getWhiteboardCount() {
        return whiteboardCount;
    }

    public void setWhiteboardCount(int whiteboardCount) {
        this.whiteboardCount = whiteboardCount;
    }

    public int getWordLib() {
        return wordLib;
    }

    public void setWordLib(int wordLib) {
        this.wordLib = wordLib;
    }

    public int getVictoryRole() {
        return victoryRole;
    }

    public void setVictoryRole(int victoryRole) {
        this.victoryRole = victoryRole;
    }

    public void setBombWordItems(List<String> bombWordItems) {
        this.bombWordItems = bombWordItems;
    }

    public List<String> getBombWordItems() {
        return bombWordItems;
    }

    public long getCurrentStateLeftTime() {
        return currentStateLeftTime;
    }

    public void setCurrentStateLeftTime(long currentStateLeftTime) {
        this.currentStateLeftTime = currentStateLeftTime;
    }

    public PartyRoomPackets.BombResult getBombResult() {
        return bombResult;
    }

    public void setBombResult(PartyRoomPackets.BombResult bombResult) {
        this.bombResult = bombResult;
    }

    public String getWordCateName() {
        return wordCateName;
    }

    public void setWordCateName(String wordCateName) {
        this.wordCateName = wordCateName;
    }

    public PartySpyPlayerInfo getPlayer(int uid) {
        for (int i = 0; i < playerInfoList.size(); i++) {
            if (playerInfoList.get(i).getUid() == uid) return playerInfoList.get(i);
        }
        return new PartySpyPlayerInfo();
    }

    public String getSpyWord() {
        for (PartySpyPlayerInfo info: playerInfoList) {
            if (info.isSpy()) return info.getWord();
        }
        return "";
    }

    public String getCitizenWord() {
        for (PartySpyPlayerInfo info: playerInfoList) {
            if (info.isCitizen()) return info.getWord();
        }
        return "";
    }

    public boolean isSpyVictory() {
        return victoryRole == PartySpyPlayerInfo.SPY || victoryRole == PartySpyPlayerInfo.WHITE_BOARD;
    }

    public boolean isCitizenVictory() {
        return victoryRole == PartySpyPlayerInfo.SPY_CITIZEN;
    }

    public static PartySpyGameInfo parse(PartyRoomPackets.SpyGame spyGame) {
        PartySpyGameInfo gameInfo = new PartySpyGameInfo();
        gameInfo.setPlayerInfoList(PartySpyPlayerInfo.parseList(spyGame.getPlayersList()));
        gameInfo.setStartIdx(spyGame.getStartIdx());
        gameInfo.setSpyCount(spyGame.getSpyCount());
        gameInfo.setCivilianCount(spyGame.getCivilianCount());
        gameInfo.setWhiteboardCount(spyGame.getWhiteBoardCount());
        gameInfo.setWordLib(spyGame.getWordLib());
        gameInfo.setVictoryRole(spyGame.getVictoryRole());
        gameInfo.setBombWordItems(spyGame.getBombWordItemsList());
        gameInfo.setCurrentStateLeftTime(spyGame.getCurrentStateLeftTime());
        gameInfo.setBombResult(spyGame.getBombResult());
        gameInfo.setWordCateName(spyGame.getWordCateName());
        return gameInfo;
    }
}
