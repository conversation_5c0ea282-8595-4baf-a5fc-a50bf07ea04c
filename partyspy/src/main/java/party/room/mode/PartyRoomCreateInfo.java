package party.room.mode;

public class PartyRoomCreateInfo {
    public int gameType = 0;
    public int seatCount = 0;

    //狼人杀配置
    public int faguanCount = 0;
    public int wolfCitizenCount = 0;
    public int wolfCount = 0;
    public int prophetCount  = 0;
    public int witchCount = 0;
    public int hunterCount = 0;
    public int guardCount = 0;

    //谁是卧底配置
    public int spyCount = 0;
    public int spyCivilianCount = 0;
    public int whiteBoardCount = 0;
    public int wordLib = 0;

    public PartyRoomCreateInfo(int gameType, int seatCount, int spyCount, int spyCivilianCount, int whiteBoardCount, int wordLib) {
        this.gameType = gameType;
        this.seatCount = seatCount;
        this.spyCount = spyCount;
        this.spyCivilianCount = spyCivilianCount;
        this.whiteBoardCount = whiteBoardCount;
        this.wordLib = wordLib;
    }

    public PartyRoomCreateInfo(int gameType, int seatCount) {
        this.gameType = gameType;
        this.seatCount = seatCount;
    }

    public PartyRoomCreateInfo(int gameType, int seatCount, int faguanCount, int wolfCitizenCount, int wolfCount, int prophetCount, int witchCount, int hunterCount, int guardCount) {
        this.gameType = gameType;
        this.seatCount = seatCount;
        this.faguanCount = faguanCount;
        this.wolfCitizenCount = wolfCitizenCount;
        this.wolfCount = wolfCount;
        this.prophetCount = prophetCount;
        this.witchCount = witchCount;
        this.hunterCount = hunterCount;
        this.guardCount = guardCount;
    }
}
