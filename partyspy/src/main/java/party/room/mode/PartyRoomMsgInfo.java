package party.room.mode;

import com.wepie.wespy.net.tcp.packet.CommonPackets;
import com.wepie.wespy.net.tcp.packet.PartyRoomPackets;
import java.util.ArrayList;
import java.util.List;

public class PartyRoomMsgInfo {
    private int msgSeq;
    private int sender;
    private CommonPackets.MsgType type;
    private int subtype;
    private String content;
    private String ext;
    private long timestamp;

    public int getMsgSeq() {
        return msgSeq;
    }

    public void setMsgSeq(int msgSeq) {
        this.msgSeq = msgSeq;
    }

    public int getSender() {
        return sender;
    }

    public void setSender(int sender) {
        this.sender = sender;
    }

    public CommonPackets.MsgType getType() {
        return type;
    }

    public void setType(CommonPackets.MsgType type) {
        this.type = type;
    }

    public int getSubtype() {
        return subtype;
    }

    public void setSubtype(int subtype) {
        this.subtype = subtype;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public static List<PartyRoomMsgInfo> parseList(List<PartyRoomPackets.PartyRoomMsg> roomMsgsList) {
        List<PartyRoomMsgInfo> list = new ArrayList<>();
        for (PartyRoomPackets.PartyRoomMsg msg : roomMsgsList) {
            list.add(parse(msg));
        }
        return list;
    }

    private static PartyRoomMsgInfo parse(PartyRoomPackets.PartyRoomMsg msg) {
        PartyRoomMsgInfo info = new PartyRoomMsgInfo();
        info.setMsgSeq(msg.getMsgseq());
        info.setSender(msg.getSender());
        info.setType(msg.getType());
        info.setSubtype(msg.getSubtype());
        info.setContent(msg.getContent());
        info.setExt(msg.getExt());
        info.setTimestamp(msg.getTimestamp());
        return info;
    }
}
