package party.room.mode;

import com.wepie.wespy.net.tcp.packet.PartyRoomPackets;
import java.util.List;

public class PartyRoomMsgListInfo {
    private int msgSeq;
    private List<PartyRoomMsgInfo> roomMsgInfoList;

    public int getMsgSeq() {
        return msgSeq;
    }

    public void setMsgSeq(int msgSeq) {
        this.msgSeq = msgSeq;
    }

    public List<PartyRoomMsgInfo> getRoomMsgInfoList() {
        return roomMsgInfoList;
    }

    public void setRoomMsgInfoList(List<PartyRoomMsgInfo> roomMsgInfoList) {
        this.roomMsgInfoList = roomMsgInfoList;
    }

    public static PartyRoomMsgListInfo parse(PartyRoomPackets.PartyRoomMsgs msgList) {
        PartyRoomMsgListInfo info = new PartyRoomMsgListInfo();
        info.setMsgSeq(msgList.getMsgseq());
        info.setRoomMsgInfoList(PartyRoomMsgInfo.parseList(msgList.getRoomMsgsList()));
        return info;
    }
}
