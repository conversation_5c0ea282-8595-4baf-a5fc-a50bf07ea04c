package party.room.mode;

import com.wepie.wespy.net.tcp.packet.PartyRoomPackets;
import java.util.ArrayList;
import java.util.List;

public class PartySeatInfo {
    public static final int STATE_EMPTY = PartyRoomPackets.SeatState.STATE_EMPTY_VALUE;
    public static final int STATE_SEATED = PartyRoomPackets.SeatState.STATE_SEATED_VALUE;
    public static final int STATE_READY = PartyRoomPackets.SeatState.STATE_READY_VALUE;
    public static final int STATE_QUIT_GAME = PartyRoomPackets.SeatState.STATE_QUIT_GAME_VALUE;

    private int num;
    private int seatState;
    private int uid;

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public int getSeatState() {
        return seatState;
    }

    public void setSeatState(int seatState) {
        this.seatState = seatState;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public static List<PartySeatInfo> parseList(List<PartyRoomPackets.SeatInfo> seatInfoList) {
        List<PartySeatInfo> list = new ArrayList<>();
        for (PartyRoomPackets.SeatInfo seatInfo : seatInfoList) {
            list.add(parse(seatInfo));
        }
        return list;
    }

    public static PartySeatInfo parse(PartyRoomPackets.SeatInfo seatInfo) {
        PartySeatInfo info = new PartySeatInfo();
        info.setNum(seatInfo.getNum());
        info.setSeatState(seatInfo.getSeatState());
        info.setUid(seatInfo.getUid());
        return info;
    }
}
