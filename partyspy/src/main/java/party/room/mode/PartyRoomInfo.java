package party.room.mode;

import com.wepie.wespy.net.tcp.packet.PartyRoomPackets;
import java.util.List;

public class PartyRoomInfo {
    private static final int INIT = PartyRoomPackets.PartyRoomGameState.INIT_VALUE;
    private static final int GAME_STARTED = PartyRoomPackets.PartyRoomGameState.GAME_STARTED_VALUE;
    private static final int GAME_OVER = PartyRoomPackets.PartyRoomGameState.GAME_OVER_VALUE;
    private static final int GAME_SPY_BOMBING = PartyRoomPackets.PartyRoomGameState.GAME_SPY_BOMBING_VALUE;
    private static final int GAME_SPY_BOMB_RESULT_SHOW = PartyRoomPackets.PartyRoomGameState.GAME_SPY_BOMB_RESULT_SHOW_VALUE;

    private int version;
    private int rid;
    private int owner;
    private int gameType;
    private List<PartySeatInfo> seatInfoList;
    private int seatCount;
    private List<Integer> uidList;
    private int gameState;
    private PartySpyGameInfo spyGameInfo;
    private PartyRoomMsgListInfo roomMsgInfo;

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getOwner() {
        return owner;
    }

    public void setOwner(int owner) {
        this.owner = owner;
    }

    public int getGameType() {
        return gameType;
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    public List<PartySeatInfo> getSeatInfoList() {
        return seatInfoList;
    }

    public void setSeatInfoList(List<PartySeatInfo> seatInfoList) {
        this.seatInfoList = seatInfoList;
    }

    public int getSeatCount() {
        return seatCount;
    }

    public void setSeatCount(int seatCount) {
        this.seatCount = seatCount;
    }

    public List<Integer> getUidList() {
        return uidList;
    }

    public void setUidList(List<Integer> uidList) {
        this.uidList = uidList;
    }

    public int getGameState() {
        return gameState;
    }

    public void setGameState(int gameState) {
        this.gameState = gameState;
    }

    public PartySpyGameInfo getSpyGameInfo() {
        return spyGameInfo;
    }

    public void setSpyGameInfo(PartySpyGameInfo spyGameInfo) {
        this.spyGameInfo = spyGameInfo;
    }

    public PartyRoomMsgListInfo getRoomMsgInfo() {
        return roomMsgInfo;
    }

    public void setRoomMsgInfo(PartyRoomMsgListInfo roomMsgInfo) {
        this.roomMsgInfo = roomMsgInfo;
    }

    public boolean isInitState() {
        return gameState == INIT;
    }

    public boolean isStartedState() {
        return gameState == GAME_STARTED;
    }

    public boolean isOverState() {
        return gameState == GAME_OVER;
    }

    public boolean isSpyBombing() {
        return gameState == GAME_SPY_BOMBING;
    }

    public boolean isSpyBombResultShow() {
        return gameState == GAME_SPY_BOMB_RESULT_SHOW;
    }

    public static PartyRoomInfo parse(PartyRoomPackets.PartyRoomInfo roomInfo) {
        PartyRoomInfo info = new PartyRoomInfo();
        info.setVersion(roomInfo.getVersion());
        info.setRid(roomInfo.getRid());
        info.setOwner(roomInfo.getOwner());
        info.setGameType(roomInfo.getGameType());
        info.setSeatInfoList(PartySeatInfo.parseList(roomInfo.getSeatsList()));
        info.setSeatCount(roomInfo.getSeatCount());
        info.setUidList(roomInfo.getUidListList());
        info.setGameState(roomInfo.getGameState());
        info.setSpyGameInfo(PartySpyGameInfo.parse(roomInfo.getSpyGame()));
        info.setRoomMsgInfo(PartyRoomMsgListInfo.parse(roomInfo.getRoomMsgs()));
        return info;
    }
}
