package party.room.mode;

import com.wepie.wespy.net.tcp.packet.PartyRoomPackets;
import java.util.ArrayList;
import java.util.List;

public class PartySpyPlayerInfo {

    static final int SPY_CITIZEN = PartyRoomPackets.PartyRoomSpyRole.SPY_CITIZEN_VALUE;
    static final int SPY = PartyRoomPackets.PartyRoomSpyRole.SPY_VALUE;
    static final int WHITE_BOARD = PartyRoomPackets.PartyRoomSpyRole.WHITE_BOARD_VALUE;


    private int uid;
    private int role;
    private String word;
    private boolean isOver;

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public String getWord() {
        return word;
    }

    public void setWord(String word) {
        this.word = word;
    }

    public boolean isOver() {
        return isOver;
    }

    public void setOver(boolean over) {
        isOver = over;
    }

    public boolean isCitizen() {
        return role == SPY_CITIZEN;
    }

    public boolean isSpy() {
        return role == SPY;
    }

    public boolean isWhiteBoard() {
        return role == WHITE_BOARD;
    }

    public static List<PartySpyPlayerInfo> parseList(List<PartyRoomPackets.SpyPlayers> playersList) {
        List<PartySpyPlayerInfo> list = new ArrayList<>();
        for (PartyRoomPackets.SpyPlayers player : playersList) {
            list.add(parse(player));
        }
        return list;
    }

    private static PartySpyPlayerInfo parse(PartyRoomPackets.SpyPlayers player) {
        PartySpyPlayerInfo info = new PartySpyPlayerInfo();
        info.setUid(player.getUid());
        info.setRole(player.getRole());
        info.setWord(player.getWord());
        info.setOver(player.getIsOver());
        return info;
    }
}
