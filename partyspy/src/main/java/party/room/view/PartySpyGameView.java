package party.room.view;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.international.regoin.IDRegionUtil;
import com.huiwan.constants.GameType;
import com.huiwan.user.LoginHelper;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.partyspy.R;

import java.util.List;

import party.room.PartyRoomManager;
import party.room.mode.PartyRoomInfo;
import party.room.mode.PartySeatInfo;
import party.room.mode.PartySpyPlayerInfo;
import party.room.seat.PartySpySeatView;
import party.spy.edit.PartySpyEditActivity;
import party.spy.result.PartySpyResultDialog;
import party.spy.role.PartySprRoleNewDialog;
import party.spy.role.PartySpyBombingDialog;
import party.spy.role.PartySpyCheckRoleDialog;
import party.spy.tips.PartySpyTipsDialog;

public class PartySpyGameView extends IPartyBaseView {
    private ImageView backIv, editIv;
    private TextView roomIdTv;
    private ImageView normalIv, spyIv;
    private TextView civilianNumTv, spyNumTv;
    private TextView gameStartCivilianNumTv, gameStartSpyNumTv;
    private ConstraintLayout gameStartCivilianLay, gameStartSpyLay;
    private View gameInitLay, gameStartLay;
    private final PartySpySeatView[] seatViewList = new PartySpySeatView[12];
    private TextView wordTv;
    private TextView wordChangeIv;
    private ImageView startTv, shareTv;
    private TextView waitTipsTv;
    private View currentWordLay;
    private TextView currentWordTv;
    private View seatListLay;
    private View cardLay;
    private int lastState = -1;
    private Dialog resultDialog, tipsDialog, checkRoleDialog, roleDialog;
    private PartySpyBombingDialog partySpyBombingDialog;


    public PartySpyGameView(@NonNull Context context) {
        super(context);
        initView();
    }

    public PartySpyGameView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    private void initView() {
        inflate(getContext(), R.layout.party_spy_game_view, this);
        backIv = findViewById(R.id.back_iv);
        editIv = findViewById(R.id.edit_iv);
        roomIdTv = findViewById(R.id.room_id_tv);
        gameInitLay = findViewById(R.id.game_init_lay);
        gameStartLay = findViewById(R.id.game_start_lay);
        normalIv = findViewById(R.id.normal_iv);
        spyIv = findViewById(R.id.spy_iv);
        civilianNumTv = findViewById(R.id.civilian_num_tv);
        spyNumTv = findViewById(R.id.spy_num_tv);
        gameStartCivilianNumTv = findViewById(R.id.game_start_civilian_num_tv);
        gameStartSpyNumTv = findViewById(R.id.game_start_spy_num_tv);
        gameStartCivilianLay = findViewById(R.id.game_start_civilian_lay);
        gameStartSpyLay = findViewById(R.id.game_start_spy_lay);
        wordTv = findViewById(R.id.word_tv);
        wordChangeIv = findViewById(R.id.word_change_iv);
        startTv = findViewById(R.id.start_tv);
        shareTv = findViewById(R.id.share_tv);
        waitTipsTv = findViewById(R.id.wait_tips_tv);
        seatViewList[0] = findViewById(R.id.seat1);
        seatViewList[1] = findViewById(R.id.seat2);
        seatViewList[2] = findViewById(R.id.seat3);
        seatViewList[3] = findViewById(R.id.seat4);
        seatViewList[4] = findViewById(R.id.seat5);
        seatViewList[5] = findViewById(R.id.seat6);
        seatViewList[6] = findViewById(R.id.seat7);
        seatViewList[7] = findViewById(R.id.seat8);
        seatViewList[8] = findViewById(R.id.seat9);
        seatViewList[9] = findViewById(R.id.seat10);
        seatViewList[10] = findViewById(R.id.seat11);
        seatViewList[11] = findViewById(R.id.seat12);
        seatListLay = findViewById(R.id.seat_list_lay);
        cardLay = findViewById(R.id.card_lay);
        currentWordLay = findViewById(R.id.current_word_lay);
        currentWordTv = findViewById(R.id.current_word);
        initEvent();
    }

    private void initEvent() {
        backIv.setOnClickListener(v -> {
            if (callback != null) callback.exit();
        });
        editIv.setOnClickListener(v -> PartySpyEditActivity.goEditRoom(getContext()));
        startTv.setOnClickListener(v -> {
            PartyRoomInfo info = PartyRoomManager.get().getRoomInfoLiveData().getValue();
            if (info == null) {
                return;
            }

            List<PartySeatInfo> seatInfoList = info.getSeatInfoList();
            int seatPlayerNum = getSeatPlayerNum(seatInfoList);
            if (seatPlayerNum < info.getSeatCount()) {
                ToastUtil.show(R.string.party_game_seat_num_less_tip);
                return;
            }

            if (info.isOverState()) {
                doAgain();
            } else {
                doStart();
            }
        });
        shareTv.setOnClickListener(v -> doShare());
        wordChangeIv.setOnClickListener(v -> DialogBuild.newBuilder(wordChangeIv.getContext()).setSingleBtn(false)
                .setSureTx(R.string.party_game_sure).setCancelTx(R.string.party_game_cancel).setContent(R.string.party_spy_change_word_tips)
                .setDialogCallback(new DialogBuild.DialogCallback() {
                    @Override
                    public void onClickSure() {
                        doAgain();
                    }

                    @Override
                    public void onClickCancel() {

                    }
                }).show());
    }

    private int getSeatPlayerNum(List<PartySeatInfo> seatInfoList) {
        if (seatInfoList == null || seatInfoList.isEmpty()) {
            return 0;
        }

        int num = 0;
        for (PartySeatInfo seatInfo: seatInfoList) {
            if (seatInfo.getUid() > 0) {
                num++;
            }
        }
        return num;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void update(PartyRoomInfo info) {
        roomIdTv.setText(getRoomName(info.getRid()));
        civilianNumTv.setText(ResUtil.getStr(R.string.party_spy_x_person, info.getSpyGameInfo().getCivilianCount()));
        spyNumTv.setText(ResUtil.getStr(R.string.party_spy_x_person, info.getSpyGameInfo().getSpyCount()));
        gameStartCivilianNumTv.setText(info.getSpyGameInfo().getCivilianCount() + "");
        gameStartSpyNumTv.setText(info.getSpyGameInfo().getSpyCount() + "");
        updateState(info);
        updateSeat(info);
    }

    @Override
    public void showRole(PartyRoomInfo info, int seatNum) {
        if (info.isSpyBombing() || info.isSpyBombResultShow()) {
            return;
        }

        PartySpyPlayerInfo player = info.getSpyGameInfo().getPlayer(LoginHelper.getLoginUid());
        if (player != null) {
            hideDialog();
            checkRoleDialog = PartySpyCheckRoleDialog.showDialog(getContext(), info, seatNum, isOver -> {
                if (isOver) {
                    resultDialog = PartySpyResultDialog.showDialog(getContext(), info, new PartySpyResultDialog.Callback() {
                        @Override
                        public void onDismiss() {
                        }

                    });
                }
            });
        }
    }


    private void hideDialog() {
        if (resultDialog != null && resultDialog.isShowing()) resultDialog.dismiss();
        if (tipsDialog != null && tipsDialog.isShowing()) tipsDialog.dismiss();
        if (checkRoleDialog != null && checkRoleDialog.isShowing()) checkRoleDialog.dismiss();
        if (roleDialog != null && roleDialog.isShowing()) roleDialog.dismiss();
    }

    private void updateState(PartyRoomInfo info) {
        PartySpyPlayerInfo player = info.getSpyGameInfo().getPlayer(LoginHelper.getLoginUid());
        boolean isOwner = LoginHelper.getLoginUid() == info.getOwner();
        if (info.isStartedState() && (PartyRoomManager.get().isNeedShowStart() || info.getGameState() != lastState)) {
            hideDialog();
            roleDialog = PartySprRoleNewDialog.showDialog(getContext(), player, () -> tipsDialog = PartySpyTipsDialog.showDialog(getContext(), info.getSpyGameInfo().getStartIdx()));
            PartyRoomManager.get().setNeedShowStart(false);
        }
        lastState = info.getGameState();
        editIv.setVisibility(isOwner ? VISIBLE : GONE);
        currentWordTv.setText(info.getSpyGameInfo().getWordCateName());
        if (info.isInitState() || info.isOverState()) {
            changeViewWhenInitStateOrOverState(isOwner);
            updateCurrentWorldLay(false);
        } else if (info.isSpyBombing()) {
            // 进入卧底爆词界面
            hideDialog();
            showBombWordDialog(info);
        } else if (info.isSpyBombResultShow()) {
            if (partySpyBombingDialog == null) {
                showBombWordDialog(info);
            }
            partySpyBombingDialog.updateBombResult(info);
        } else {
            startTv.setVisibility(GONE);
            shareTv.setVisibility(GONE);
            waitTipsTv.setVisibility(GONE);
            wordChangeIv.setVisibility(isOwner ? VISIBLE : GONE);
            wordTv.setText(TextUtil.isEmpty(player.getWord()) ? "-" : player.getWord());
            wordTv.setVisibility(VISIBLE);
            currentWordLay.setVisibility(VISIBLE);
            updateCurrentWorldLay(true);
            showPictureDesc(false);
        }
    }

    private void updateCurrentWorldLay(boolean isGaming) {
        ConstraintLayout.LayoutParams lp = (ConstraintLayout.LayoutParams) currentWordLay.getLayoutParams();
        if (isGaming) {
            lp.bottomToTop = R.id.word_tv;
        } else {
            lp.bottomToTop = R.id.start_lay;
        }
    }

    private void changeViewWhenInitStateOrOverState(boolean isOwner) {
        wordTv.setVisibility(GONE);
        wordChangeIv.setVisibility(GONE);
        startTv.setVisibility(isOwner ? VISIBLE : GONE);
        shareTv.setVisibility(VISIBLE);
        waitTipsTv.setVisibility(isOwner ? GONE : VISIBLE);
        currentWordLay.setVisibility(isOwner ? VISIBLE : INVISIBLE);
        showPictureDesc(true);
    }

    private void showPictureDesc(boolean show) {
        if (show) {
            updatePictureViewState(VISIBLE, GONE);
        } else {
            updatePictureViewState(GONE, VISIBLE);

        }
    }

    private void updatePictureViewState(int gameInitViewState, int gameStartViewState) {
        gameInitLay.setVisibility(gameInitViewState);
        gameStartLay.setVisibility(gameStartViewState);
        normalIv.setVisibility(gameInitViewState);
        spyIv.setVisibility(gameInitViewState);
        civilianNumTv.setVisibility(gameInitViewState);
        spyNumTv.setVisibility(gameInitViewState);
        gameStartCivilianLay.setVisibility(gameStartViewState);
        gameStartSpyLay.setVisibility(gameStartViewState);
    }

    private void updateSeat(PartyRoomInfo info) {
        for (int i = 0; i < seatViewList.length; i++) {
            if (i < info.getSeatInfoList().size()) {
                boolean isOwner = info.getSeatInfoList().get(i).getUid() == info.getOwner();
                seatViewList[i].setVisibility(VISIBLE);
                seatViewList[i].update(info.getSeatInfoList().get(i), isOwner);
                if (info.isInitState() || info.isOverState()) {
                    seatViewList[i].refreshNoStart();
                } else if (info.isStartedState()) {
                    if (i < info.getSpyGameInfo().getPlayerInfoList().size()) {
                        seatViewList[i].refreshStarted(info.getSpyGameInfo().getPlayerInfoList().get(i),
                                LoginHelper.getLoginUid() == info.getOwner(), i + 1, info.getSpyGameInfo().getStartIdx());
                    }
                }
            } else {
                seatViewList[i].setVisibility(INVISIBLE);
            }
        }
        seatListLay.post(() -> {
            ViewGroup.LayoutParams layoutParams = seatListLay.getLayoutParams();
            layoutParams.height = currentWordLay.getTop() - cardLay.getBottom() - ScreenUtil.dip2px(38);
            seatListLay.setLayoutParams(layoutParams);
        });
    }

    private String getRoomName(int rid) {
        return IDRegionUtil.INSTANCE.getFinalIDStrByGameType(rid, getGameType());
    }

    @Override
    public int getGameType() {
        return GameType.GAME_TYPE_PARTY_SPY;
    }

    private void showBombWordDialog(PartyRoomInfo info) {
        BaseFullScreenDialog dialog = new BaseFullScreenDialog(getContext(), R.style.dialog_style_custom);
        partySpyBombingDialog = new PartySpyBombingDialog(getContext());
        partySpyBombingDialog.setCallBack(() -> dialog.dismiss());
        partySpyBombingDialog.updatePartyRoomInfo(info);
        dialog.setContentView(partySpyBombingDialog.getRootView());
        dialog.initBottomDialog();
        dialog.setCanceledOnTouchOutside(false);
        dialog.show();
    }
}
