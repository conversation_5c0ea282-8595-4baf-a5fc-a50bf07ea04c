package party.room.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;

import net.tcp.sender.PartyRoomPacketSender;
import party.room.PartyRoomManager;
import party.room.mode.PartyRoomInfo;
import party.room.share.PartyRoomShareDialog;

public abstract class IPartyBaseView extends FrameLayout {
    private static final String TAG = "IPartyBaseView";

    Callback callback;

    public IPartyBaseView(@NonNull Context context) {
        super(context);
    }

    public IPartyBaseView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public abstract int getGameType();

    public abstract void update(PartyRoomInfo info);

    public abstract void showRole(PartyRoomInfo info, int seatNum);

//    public abstract void syncDrawGame(DrawGuessGameInfo info);

    void doShare() {
        PartyRoomShareDialog.showShareWithFriendDialog(getContext(), getGameType(), PartyRoomManager.get().getRid());
    }

    void doStart() {
        PartyRoomPacketSender.partyRoomStartReq(PartyRoomManager.get().getRid(), new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                LibBaseUtil.logInfo(TAG, "partyRoomStartReq: head.desc = {}", head.desc);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    void doAgain() {
        PartyRoomPacketSender.partyRoomAgainReq(PartyRoomManager.get().getRid(), new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {

            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public interface Callback {
        void exit();
    }
}
