package party.spy.start;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import com.huiwan.base.BuildConfig;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.http.UrlConfig;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.DataCallback;
import com.huiwan.module.webview.WebActivity;
import com.wepie.lib.api.plugins.hwroom.HwRoomApi;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.wespy.model.entity.EnterRoomInfo;
import com.wepie.wespy.partyspy.R;
import java.util.HashMap;
import java.util.Map;

import party.spy.IntervalClickListener;
import party.spy.edit.PartySpyEditActivity;

public class PartySpyStartActivity extends BaseActivity {

    public static final String PARTY_ROOM_SCENE = "partyroom";
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_party_spy_start);
        StatusBarUtil.initStatusBar(this);
        StatusBarUtil.setStatusFontWhiteColor(this);
        findViewById(R.id.back_iv).setOnClickListener(v -> finish());
        findViewById(R.id.create_iv).setOnClickListener(v -> {
            trackClick(TrackSource.CREATE_ROOM);
            PartySpyEditActivity.goCreateRoom(this);
        });
        findViewById(R.id.enter_iv).setOnClickListener(v -> {
            trackClick(TrackSource.JOIN_ROOM);
            showEnterRoomDialog(this);
        });
        findViewById(R.id.share_tv).setOnClickListener(new IntervalClickListener() {
            @Override
            protected void onClickInternal(View v) {
                String url = BuildConfig.DEBUG ? UrlConfig.INVITE_NEW_DEBUG : UrlConfig.INVITE_NEW_RELEASE;
                WebActivity.go(PartySpyStartActivity.this, url);
            }
        });
        findViewById(R.id.help_iv).setOnClickListener(v -> WebActivity.go(this, ConfigHelper.getInstance().getConstV3Info().offlineSpyHelp));
    }

    private void showEnterRoomDialog(Context context) {
        ApiService.of(HwRoomApi.class).showSearchPartySpyDialog(context, new DataCallback<String>() {
            @Override
            public void onCall(String ridString) {
                if (TextUtils.isEmpty(ridString)) {
                    ToastUtil.show(R.string.party_spy_cannot_intput_empty_room_num);
                    return;
                }
                EnterRoomInfo info = EnterRoomInfo.buildSearchRoom(context, ridString).setSource(TrackSource.PARTY_SPY);
                ApiService.of(HwRoomApi.class).handleSearch(info, PARTY_ROOM_SCENE);
            }

            @Override
            public void onCancel() {

            }
        });
    }

    private void trackClick(String btnName) {
        Map<String, Object> map = new HashMap<>();
        map.put("source", TrackSource.PARTY_SPY);
        TrackUtil.appClick(TrackScreenName.SPY_PAGE, btnName, map);
    }

    public static void go(Context context) {
        Intent intent = new Intent(context, PartySpyStartActivity.class);
        context.startActivity(intent);
    }
}
