package party.spy.role;

import android.app.Dialog;
import android.content.Context;
import android.util.AttributeSet;
import android.widget.FrameLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.net.tcp.packet.PartyRoomPackets;
import com.wepie.wespy.partyspy.R;
import party.room.mode.PartySpyPlayerInfo;

public class PartySprRoleNewDialog extends FrameLayout {
    private TextView specialRoleDescriptionTx;
    private TextView rolewordTx;
    private Callback callback;
    private final Runnable runnable = new Runnable() {
        @Override
        public void run() {
            if (callback != null) callback.onDismiss();
        }
    };

    public PartySprRoleNewDialog(@NonNull Context context) {
        super(context);
        initView();
    }

    public PartySprRoleNewDialog(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    private void initView() {
        inflate(getContext(), R.layout.party_spy_role_new_dialog, this);
        rolewordTx = findViewById(R.id.role_word_tx);
        specialRoleDescriptionTx = findViewById(R.id.special_role_description_tx);
        findViewById(R.id.enter_tv).setOnClickListener(v -> {
            if (callback != null) callback.onDismiss();
        });
        findViewById(R.id.root_view).setOnClickListener(v -> {
            if (callback != null) callback.onDismiss();
        });
        findViewById(R.id.content_lay).setOnClickListener(v -> {});
        postDelayed(runnable, 5000);
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public interface Callback {
        void onDismiss();
    }

    public void update(PartySpyPlayerInfo player) {
        specialRoleDescriptionTx.setVisibility(player.isWhiteBoard() ? VISIBLE : GONE);
        rolewordTx.setText(ResUtil.getStr(R.string.party_spy_you_role, getRoleName(player.getRole())));
    }

    private String getRoleName(int role) {
        if (role == PartyRoomPackets.PartyRoomSpyRole.SPY_CITIZEN_VALUE) {
            return ResUtil.getStr(R.string.party_room_result_vote_dialog_role_good);
        } else if (role == PartyRoomPackets.PartyRoomSpyRole.SPY_VALUE) {
            return ResUtil.getStr(R.string.party_room_result_vote_dialog_role_spy);
        }
        return ResUtil.getStr(R.string.party_room_result_vote_dialog_role_good);
    }

    @Override
    protected void onDetachedFromWindow() {
        removeCallbacks(runnable);
        super.onDetachedFromWindow();
    }

    public static Dialog showDialog(Context context, PartySpyPlayerInfo player, Callback callback) {
        BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.party_spy_show_role_dialog_style);
        PartySprRoleNewDialog view = new PartySprRoleNewDialog(context);
        view.update(player);
        view.setCallback(() -> {
            if (callback != null) callback.onDismiss();
            dialog.dismiss();
        });
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(true);
        dialog.initBottomDialogSlow();
        dialog.show();
        return dialog;
    }
}
