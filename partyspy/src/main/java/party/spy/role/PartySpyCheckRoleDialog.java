package party.spy.role;

import android.app.Dialog;
import android.content.Context;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.huiwan.base.str.ResUtil;
import com.huiwan.decorate.DecorHeadImgView;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.partyspy.R;
import java.util.List;

import party.room.mode.PartyRoomInfo;
import party.room.mode.PartySpyPlayerInfo;

public class PartySpyCheckRoleDialog extends FrameLayout {

    private DecorHeadImgView headIv;
    private ImageView roleIv;
    private TextView numTv;
    private TextView titleTv;
    private TextView contentTv;
    private TextView enterTv;
    private ImageView maskIv;
    private Callback callback;
    private final Paint paint = new Paint();

    public PartySpyCheckRoleDialog(@NonNull Context context) {
        super(context);
        initView();
    }

    public PartySpyCheckRoleDialog(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    private void initView() {
        inflate(getContext(), R.layout.party_spy_check_role_dialog, this);
        headIv = findViewById(R.id.head_iv);
        roleIv = findViewById(R.id.role_iv);
        numTv = findViewById(R.id.num_tv);
        titleTv = findViewById(R.id.title_tv);
        contentTv = findViewById(R.id.content_tv);
        enterTv = findViewById(R.id.enter_tv);
        maskIv = findViewById(R.id.pass_mask_iv);
    }

    private void update(PartyRoomInfo info, int seatNum) {
        List<PartySpyPlayerInfo> list = info.getSpyGameInfo().getPlayerInfoList();
        if (seatNum - 1 < list.size()) {
            PartySpyPlayerInfo player = list.get(seatNum - 1);
            headIv.showUserHead(player.getUid());
            showGray();
            numTv.setText(String.valueOf(seatNum));
            if (player.isCitizen()) {
                roleIv.setImageResource(R.drawable.party_spy_role1);
            } else if (player.isSpy()) {
                roleIv.setImageResource(R.drawable.party_spy_role2);
            }
            if (info.isOverState()) {
                titleTv.setText(R.string.party_game_common_game_over);
                contentTv.setText(info.getSpyGameInfo().isSpyVictory() ? R.string.party_spy_win : R.string.party_villagers_won);
                enterTv.setText(R.string.party_spy_check_result);
            } else {
                titleTv.setText(R.string.party_room_game_continue);
                contentTv.setText(ResUtil.getResource().getString(R.string.party_spy_start_spearking_tips, info.getSpyGameInfo().getStartIdx()));
                enterTv.setText(R.string.party_spy_i_kown);
            }
            enterTv.setOnClickListener(v -> {
                if (callback != null) callback.onDismiss(info.isOverState());
            });
        }
    }

    private void showGray() {
        ColorMatrix cm = new ColorMatrix();
        cm.setSaturation(0);
        paint.setColorFilter(new ColorMatrixColorFilter(cm));
        headIv.setLayerType(View.LAYER_TYPE_HARDWARE, paint);
        maskIv.setVisibility(VISIBLE);
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public interface Callback {
        void onDismiss(boolean isOver);
    }

    public static Dialog showDialog(Context context, PartyRoomInfo info, int seatNum, Callback callback) {
        BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        PartySpyCheckRoleDialog view = new PartySpyCheckRoleDialog(context);
        view.update(info, seatNum);
        view.setCallback(isOver -> {
            if (callback != null) callback.onDismiss(isOver);
            dialog.dismiss();
        });
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(false);
        dialog.show();
        return dialog;
    }
}
