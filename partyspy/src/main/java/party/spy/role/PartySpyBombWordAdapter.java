package party.spy.role;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.huiwan.widget.rv.BaseRvAdapter;
import com.huiwan.widget.rv.RVHolder;
import com.wepie.wespy.partyspy.R;

public class PartySpyBombWordAdapter extends BaseRvAdapter<String, PartySpyBombWordAdapter.PartySpyBombWordHolder> {

    private int selectIndex = -1;
    private Callback callback;

    public void setSelectIndex(int selectIndex) {
        this.selectIndex = selectIndex;
        notifyDataSetChanged();
    }

    public int getSelectIndex() {
        return selectIndex;
    }

    public String getSelectWord() {
        if (selectIndex < 0 || selectIndex >= dataList.size()) {
            return "";
        }
        return dataList.get(selectIndex);
    }

    @NonNull
    @Override
    public PartySpyBombWordHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        PartySpyBombWordHolder holder = new PartySpyBombWordHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.party_spy_bombing_word_item, parent, false));
        holder.itemView.setOnClickListener(v -> {
            setSelectIndex(holder.getBindingAdapterPosition());
            if (callback != null) callback.onClick();
        });
        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull PartySpyBombWordHolder holder, int position) {
        holder.update(dataList.get(position), selectIndex == position);
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    static class PartySpyBombWordHolder extends RVHolder {
        private final TextView wordTv;
        private final ConstraintLayout wordLay;
        public PartySpyBombWordHolder(View view) {
            super(view);
            wordTv = view.findViewById(R.id.word_tv);
            wordLay = view.findViewById(R.id.word_lay);
        }

        public void update(String word, boolean isSelect) {
            wordTv.setText(word);
            wordLay.setBackgroundResource(isSelect ? R.drawable.shape_party_select_bg : R.drawable.shape_1affffff_corner8);
        }
    }

    public interface Callback {
        void onClick();
    }
}
