package party.spy.role;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

import android.content.Context;
import android.os.CountDownTimer;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.UserSimpleInfoCallback;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.lib.share.SpaceItemDecoration;
import com.wepie.wespy.net.tcp.packet.PartyRoomPackets;
import com.wepie.wespy.partyspy.R;

import net.tcp.sender.PartyRoomPacketSender;

import java.util.List;

import party.room.PartyRoomManager;
import party.room.mode.PartyRoomInfo;
import party.room.mode.PartySeatInfo;
import party.room.mode.PartySpyGameInfo;
import party.room.mode.PartySpyPlayerInfo;
import party.spy.result.PartySpyResultDialog;

public class PartySpyBombingDialog {

    private View rootView;
    private TextView dialogTitleTv;
    private TextView leftTimeTv;
    private RecyclerView bombWordRv;
    private ImageView bombSureIv;
    private DecorHeadImgView spyHeadIv;
    private TextView spyNumTv;
    private TextView spyNameTv;
    private TextView bombDescTv;
    private View bombWordResultLay;
    private TextView bombResultWordTv;
    private ImageView bombResultIconIv;
    private final PartySpyBombWordAdapter adapter = new PartySpyBombWordAdapter();
    private CountDownTimer timer;
    private CallBack callBack;

    public PartySpyBombingDialog(@NonNull Context context) {
        initView(context);
    }

    private void initView(Context context) {
        rootView = LayoutInflater.from(context).inflate(R.layout.party_spy_bombing_dialog, null);
        rootView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ScreenUtil.dip2px(438)));
        dialogTitleTv = rootView.findViewById(R.id.bombing_word_tv);
        leftTimeTv = rootView.findViewById(R.id.bombing_word_left_time);
        bombWordRv = rootView.findViewById(R.id.bombing_word_rv);
        bombSureIv = rootView.findViewById(R.id.bombing_word_sure_iv);
        spyHeadIv = rootView.findViewById(R.id.head_iv);
        spyNumTv = rootView.findViewById(R.id.num_tv);
        spyNameTv = rootView.findViewById(R.id.name_tv);
        bombDescTv = rootView.findViewById(R.id.bomb_desc_tv);
        bombWordResultLay = rootView.findViewById(R.id.bomb_word_result_lay);
        bombResultWordTv = rootView.findViewById(R.id.bomb_result_word);
        bombResultIconIv = rootView.findViewById(R.id.bomb_result_icon);

        bombSureIv.setEnabled(false);
        bombWordRv.setLayoutManager(new GridLayoutManager(context, 2));
        adapter.setCallback(() -> {
            if (adapter.getSelectIndex() >= 0) {
                bombSureIv.setImageResource(R.drawable.party_spy_sure_item_selector);
                bombSureIv.setClickable(true);
                bombSureIv.setEnabled(true);
            } else {
                bombSureIv.setImageResource(R.drawable.party_spy_sure_btn_disable);
                bombSureIv.setClickable(false);
                bombSureIv.setEnabled(false);
            }
        });
        int spaceItemDecoration = ScreenUtil.dip2px(8);
        bombWordRv.addItemDecoration(new SpaceItemDecoration(spaceItemDecoration, spaceItemDecoration, spaceItemDecoration, spaceItemDecoration));
        bombWordRv.setAdapter(adapter);

        bombSureIv.setOnClickListener(v -> {
            PartyRoomInfo info = PartyRoomManager.get().getRoomInfoLiveData().getValue();
            if (info == null) {
                return;
            }

            PartyRoomPacketSender.partySpyBombWord(info.getRid(), adapter.getSelectWord(), null);
            sync();
        });
    }

    private void sync() {
        PartyRoomInfo info = PartyRoomManager.get().getRoomInfoLiveData().getValue();
        if (info == null) {
            return;
        }
        PartyRoomPacketSender.partyRoomSyncReq(info.getRid(), new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                PartyRoomPackets.PartySyncRoomRsp rsp = (PartyRoomPackets.PartySyncRoomRsp) head.message;
                if (rsp != null) {
                    PartyRoomInfo roomInfo = PartyRoomInfo.parse(rsp.getRoomInfo());
                    PartyRoomManager.get().getRoomInfoLiveData().setValue(roomInfo);

                    if (roomInfo.isOverState()) {
                        if (!info.isSpyBombing()) {
                            callBack.onDismiss();
                        }
                        PartySpyResultDialog.showDialog(rootView.getContext(), roomInfo, null);
                    }
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {

            }
        });
    }

    public void updateBombResult(PartyRoomInfo info) {
        if (info == null) {
            return;
        }

        updateBombWordLayVisibility(false);

        PartySpyPlayerInfo playerInfo = info.getSpyGameInfo().getPlayer(LoginHelper.getLoginUid());
        if (playerInfo.isSpy()) {
            updateBombSpyInfo(playerInfo);
        }

        bombDescTv.setVisibility(GONE);

        long leftTime = info.getSpyGameInfo().getCurrentStateLeftTime();
        updateLeftTime(leftTime);

        bombWordResultLay.setVisibility(VISIBLE);
        PartyRoomPackets.BombResult bombResult = info.getSpyGameInfo().getBombResult();
        boolean bombSuccess = bombResult.getSuccess();
        String bombWord = bombResult.getBombWord();

        if (bombSuccess) {
            bombWordResultLay.setBackgroundResource(R.drawable.party_spy_bomb_word_success_bg);
            bombResultIconIv.setBackgroundResource(R.drawable.party_spy_bomb_word_success_icon);
            dialogTitleTv.setText(R.string.party_game_bombing_word_success);
        } else {
            bombWordResultLay.setBackgroundResource(R.drawable.party_spy_bomb_word_fail_bg);
            bombResultIconIv.setBackgroundResource(R.drawable.party_spy_bomb_word_fail_icon);
            dialogTitleTv.setText(R.string.party_game_bombing_word_fail);
        }

        if (TextUtil.isEmpty(bombWord)) {
            bombResultWordTv.setText(R.string.party_game_bombing_word_give_up);
        } else {
            bombResultWordTv.setText(bombWord);
        }
    }

    public View getRootView() {
        return rootView;
    }

    public void updatePartyRoomInfo(PartyRoomInfo info) {
        if (info == null || info.getSpyGameInfo() == null) {
            return;
        }

        PartySpyGameInfo gameInfo = info.getSpyGameInfo();
        long leftTime = gameInfo.getCurrentStateLeftTime();
        updateLeftTime(leftTime);

        PartySpyPlayerInfo player = gameInfo.getPlayer(LoginHelper.getLoginUid());
        if (player.isSpy()) {
            List<String> bombWordItems = gameInfo.getBombWordItems();
            if (bombWordItems != null && !bombWordItems.isEmpty()) {
                adapter.refresh(bombWordItems);
            }
            updateBombWordLayVisibility(true);
            dialogTitleTv.setText(R.string.party_game_bombing_word);
        } else {
            updateBombWordLayVisibility(false);
            List<PartySpyPlayerInfo> playerInfos = gameInfo.getPlayerInfoList();
            for (PartySpyPlayerInfo playerInfo : playerInfos) {
                if (playerInfo.isSpy()) {
                    updateBombSpyInfo(playerInfo);
                    break;
                }
            }
            dialogTitleTv.setText(R.string.party_game_find_spy);
        }
    }

    private void updateBombSpyInfo(PartySpyPlayerInfo playerInfo) {
        if (playerInfo.getUid() > 0) {
            spyHeadIv.showUserHead(playerInfo.getUid());
            UserService.get().getCacheSimpleUser(playerInfo.getUid(), new UserSimpleInfoCallback() {
                @Override
                public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                    spyNameTv.setText(simpleInfo.getRemarkName());
                }

                @Override
                public void onUserInfoFailed(String description) {

                }
            });

            PartyRoomInfo info = PartyRoomManager.get().getRoomInfoLiveData().getValue();
            if (info != null) {
                List<PartySeatInfo> seatInfoList = info.getSeatInfoList();
                if (seatInfoList != null && !seatInfoList.isEmpty()) {
                    for (PartySeatInfo seatInfo : seatInfoList) {
                        if (seatInfo.getUid() == playerInfo.getUid()) {
                            spyNumTv.setText(String.valueOf(seatInfo.getNum()));
                        }
                    }
                }
            }
        } else {
            spyHeadIv.setImageResource(R.drawable.party_head_image_icon);
        }
    }

    private void updateLeftTime(long leftTime) {
        if (timer != null) {
            timer.cancel();
        }
        leftTimeTv.setText(leftTime / 1000 + "s");
        timer = new CountDownTimer(leftTime, 1000) {

            @Override
            public void onTick(long millisUntilFinished) {
                leftTimeTv.setText(millisUntilFinished / 1000 + "s");
            }

            @Override
            public void onFinish() {
                timer = null;

                PartyRoomInfo info = PartyRoomManager.get().getRoomInfoLiveData().getValue();
                if (info == null) {
                    return;
                }

                sync();
            }
        };
        timer.start();
    }

    private void updateBombWordLayVisibility(boolean showSelectWord) {
        bombWordRv.setVisibility(showSelectWord ? View.VISIBLE : View.GONE);
        bombSureIv.setVisibility(showSelectWord ? View.VISIBLE : View.GONE);
        spyHeadIv.setVisibility(showSelectWord ? View.GONE : View.VISIBLE);
        spyNumTv.setVisibility(showSelectWord ? View.GONE : View.VISIBLE);
        spyNameTv.setVisibility(showSelectWord ? View.GONE : View.VISIBLE);
        bombDescTv.setVisibility(showSelectWord ? View.GONE : View.VISIBLE);
    }

    public void setCallBack(CallBack callBack) {
        this.callBack = callBack;
    }

    public interface CallBack {
        void onDismiss();
    }
}
