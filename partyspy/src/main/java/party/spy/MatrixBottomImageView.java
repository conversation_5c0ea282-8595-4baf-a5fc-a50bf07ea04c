package party.spy;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import androidx.annotation.Nullable;

/**
 * 底部对齐控件
 */
public class MatrixBottomImageView extends androidx.appcompat.widget.AppCompatImageView {
    /**
     * 控件宽高
     */
    private int width, height;
    /**
     * 控件矩阵
     */
    private Matrix matrix;

    public MatrixBottomImageView(Context context) {
        this(context, null);
    }

    public MatrixBottomImageView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MatrixBottomImageView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        if (width != w || height != h) {//宽高改变时需刷新值
            width = w;
            height = h;
        }
    }

    private void init() {
        matrix = new Matrix();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        Drawable drawable = getDrawable();
        if (drawable != null) {
            int intrinsicWidth = drawable.getIntrinsicWidth();//获取图片宽度
            int intrinsicHeight = drawable.getIntrinsicHeight();//获取图片高度
            matrix.reset();
            float sx = width * 1f / intrinsicWidth;//求出缩放比例
            matrix.postScale(sx, sx, 0, 0);//以左上角为原点，进行缩放
            intrinsicHeight = (int) (intrinsicHeight * sx);//求出缩放后的图片高度
            int dy = 0;
            if (intrinsicHeight > height) {//缩放后的图片高度比控件高 则图片整体需上移
                dy = -intrinsicHeight + height;//实际的偏移量（往上移需要为负数）
            } else {//缩放后的图片高度比控件小 则图片整体需下移(如果view是写死dp的记得不要超过屏幕高度，否则整体会偏下)
                dy = height - intrinsicHeight;
            }
            matrix.postTranslate(0, dy);//进行平移
            canvas.setMatrix(matrix);//设置画布矩阵
        }
        super.onDraw(canvas);
    }
}