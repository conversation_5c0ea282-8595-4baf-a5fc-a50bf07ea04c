package party.spy.tips;

import android.app.Dialog;
import android.content.Context;
import android.util.AttributeSet;
import android.widget.FrameLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.partyspy.R;

public class PartySpyTipsDialog extends FrameLayout {
    private TextView tipsTv;
    private Callback callback;
    private final Runnable runnable = new Runnable() {
        @Override
        public void run() {
            if (callback != null) callback.onDismiss();
        }
    };

    public PartySpyTipsDialog(@NonNull Context context) {
        super(context);
        initView();
    }

    public PartySpyTipsDialog(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    private void initView() {
        inflate(getContext(), R.layout.party_spy_tips_dialog, this);
        tipsTv = findViewById(R.id.tips_tv);
        postDelayed(runnable, 3000);
    }

    public void update(int index) {
        tipsTv.setText(ResUtil.getResource().getString(R.string.party_spy_game_start_spearking_tips, index));
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public interface Callback {
        void onDismiss();
    }

    @Override
    protected void onDetachedFromWindow() {
        removeCallbacks(runnable);
        super.onDetachedFromWindow();
    }

    public static Dialog showDialog(Context context, int index) {
        BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.party_spy_speaker_tips_dialog_style);
        PartySpyTipsDialog view = new PartySpyTipsDialog(context);
        view.update(index);
        view.setCallback(dialog::dismiss);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(true);
        dialog.initCenterDialog();
        dialog.show();
        return dialog;
    }
}
