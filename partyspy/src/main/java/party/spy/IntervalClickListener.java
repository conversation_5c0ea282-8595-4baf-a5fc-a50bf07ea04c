package party.spy;

import android.view.View;

/**
 * <AUTHOR>
 * Created on 2022/10/17
 */
public abstract class Interval<PERSON>lickListener implements View.OnClickListener {
    int SKIP_DURATION = 500; //milliseconds
    long mLastClickTime  = 0;
    String TAG = "IntervalClickListener";

    @Override
     public void onClick(View v) {
        if ((System.currentTimeMillis() - mLastClickTime) > SKIP_DURATION) {
            onClickInternal(v);
            mLastClickTime = System.currentTimeMillis();
        }
    }

    protected abstract void onClickInternal(View v);
}
