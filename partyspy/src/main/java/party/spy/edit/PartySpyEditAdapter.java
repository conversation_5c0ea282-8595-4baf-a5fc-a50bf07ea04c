package party.spy.edit;

import android.annotation.SuppressLint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.huiwan.widget.rv.BaseRvAdapter;
import com.huiwan.widget.rv.RVHolder;
import com.wepie.lib.api.plugins.hwroom.WordType;
import com.wepie.wespy.partyspy.R;

import java.util.Random;

public class PartySpyEditAdapter extends BaseRvAdapter<WordType, PartySpyEditAdapter.PartySpyEditHolder> {
    private int selectIndex = 0;

    @SuppressLint("NotifyDataSetChanged")
    public void setSelectIndex(int selectIndex) {
        this.selectIndex = selectIndex;
        notifyDataSetChanged();
    }

    public boolean isSelectLast() {
        return selectIndex == dataList.size() - 1;
    }

    public WordType getSelectWordType() {
        if (selectIndex < dataList.size()) {
            // 如果选择的是随机，则从前面的词库中随机选择一个
            if (isSelectLast()) {
                return dataList.get(new Random().nextInt(dataList.size()));
            } else {
                return dataList.get(selectIndex);
            }
        } else {
            WordType wordType = new WordType();
            wordType.setWordType(0);
            return wordType;
        }
    }

    @NonNull
    @Override
    public PartySpyEditHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new PartySpyEditHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.party_spy_edit_item, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull PartySpyEditHolder holder, int position) {
        holder.update(dataList.get(position), selectIndex == position, () -> setSelectIndex(position));
    }

    static class PartySpyEditHolder extends RVHolder {
        private final TextView wordTv;
        private final ConstraintLayout wordLay;
        public PartySpyEditHolder(View view) {
            super(view);
            wordTv = view.findViewById(R.id.word_tv);
            wordLay = view.findViewById(R.id.word_lay);
        }

        public void update(WordType wordType, boolean isSelect, Callback callback) {
            wordTv.setText(wordType.getDesc());
            wordLay.setBackgroundResource(isSelect ? R.drawable.shape_party_select_bg : R.drawable.shape_1affffff_corner8);
            itemView.setOnClickListener(v -> {
                if (callback != null) callback.onClick();
            });
        }
    }

    public interface Callback {
        void onClick();
    }
}
