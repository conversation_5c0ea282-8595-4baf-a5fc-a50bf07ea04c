package party.spy.edit;

public class PartySpyEditInfo {
    private int allCount;
    private int normalCount;
    private int spyCount;
    private int whiteboardCount;

    public PartySpyEditInfo() {
        updateAllNum(6);
    }

    public PartySpyEditInfo(int normalCount, int spyCount, int whiteboardCount, int allCount) {
        this.normalCount = normalCount;
        this.spyCount = spyCount;
        this.whiteboardCount = whiteboardCount;
        this.allCount = allCount;
    }

    public int getNormalCount() {
        return normalCount;
    }

    public int getSpyCount() {
        return spyCount;
    }

    public int getWhiteboardCount() {
        return whiteboardCount;
    }

    public int getAllCount() {
        return allCount;
    }

    public void updateAllNum(int num) {
        allCount = num;
        spyCount = 1;
        whiteboardCount = 0;
        normalCount = allCount - spyCount - whiteboardCount;
    }

}
