package party.spy.edit;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.lib.api.ApiService;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.three.http.callback.DataCallback;
import com.three.http.callback.Result;
import com.wepie.lib.api.plugins.hwroom.HwRoomApi;
import com.wepie.lib.api.plugins.hwroom.WordType;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.lib.share.SpaceItemDecoration;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.net.tcp.packet.PartyRoomPackets;
import com.wepie.wespy.partyspy.R;
import net.tcp.sender.PartyRoomPacketSender;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import party.room.PartyRoomActivity;
import party.room.PartyRoomManager;
import party.room.mode.PartyRoomInfo;

public class PartySpyEditActivity extends BaseActivity {
    public static final int TYPE_SPY = 1;
    public static final int SCENE_OFFLINE = 3;
    private ImageView backIv;
    private TextView numTv;
    private TextView titleTv;
    private SeekBar numSeekBar;
    private ImageView normalIv, spyIv;
    private TextView normalTv, spyTv;
    private ImageView enterIv;
    private View spyWordUpdateLay;
    private final List<WordType> wordList = new ArrayList<>();
    private final static String IS_CREATE = "is_create";
    private boolean isCreate;
    private PartySpyEditAdapter adapter;
    private PartySpyEditInfo info;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_party_spy_edit);
        StatusBarUtil.initStatusBar(this);
        StatusBarUtil.setStatusFontWhiteColor(this);
        backIv = findViewById(R.id.back_iv);
        titleTv = findViewById(R.id.title_tv);
        numTv = findViewById(R.id.num_tv);
        numSeekBar = findViewById(R.id.num_seek_bar);
        normalIv = findViewById(R.id.normal_iv);
        spyIv = findViewById(R.id.spy_iv);
        normalTv = findViewById(R.id.normal_tv);
        spyTv = findViewById(R.id.spy_tv);
        enterIv = findViewById(R.id.enter_iv);
        spyWordUpdateLay = findViewById(R.id.word_update_lay);
        RecyclerView spyRv = findViewById(R.id.spy_rv);
        spyRv.setLayoutManager(new GridLayoutManager(this, 3));
        spyRv.addItemDecoration(new SpaceItemDecoration(ScreenUtil.dip2px(8), ScreenUtil.dip2px(4), 0, ScreenUtil.dip2px(4)));
        spyRv.setAdapter(adapter = new PartySpyEditAdapter());
        isCreate = getIntent().getBooleanExtra(IS_CREATE, true);
        initData();
    }

    private void initData() {
        if (isCreate) {
            info = new PartySpyEditInfo();
            enterIv.setBackgroundResource(R.drawable.party_spy_sure_item_selector);
            titleTv.setText(R.string.party_create_room);
        } else {
            PartyRoomInfo roomInfo = PartyRoomManager.get().getRoomInfoLiveData().getValue();
            if (roomInfo != null) {
                info = new PartySpyEditInfo(roomInfo.getSpyGameInfo().getCivilianCount(), roomInfo.getSpyGameInfo().getSpyCount(), roomInfo.getSpyGameInfo().getWhiteboardCount(), roomInfo.getSeatCount());
                enterIv.setBackgroundResource(R.drawable.party_spy_sure_change_item_selector);
                titleTv.setText(R.string.party_spy_change_config);
                int index = 0;
                for (int i = 0; i < wordList.size(); i++) {
                    if (wordList.get(i).getWordType() == roomInfo.getSpyGameInfo().getWordLib()) index = i;
                }
                adapter.setSelectIndex(index);
            }
        }
        updateInfo();
        initEvent();
        getWordTypes();
    }

    private void getWordTypes() {
        ApiService.of(HwRoomApi.class).getWordTypes(TYPE_SPY, SCENE_OFFLINE, new DataCallback<List<WordType>>() {
            @Override
            public void onSuccess(Result<List<WordType>> result) {
                List<WordType> resultData = result.data;
                if (resultData == null) {
                    return;
                }

                wordList.clear();
                wordList.addAll(resultData);
                WordType wordType = new WordType();
                wordType.setTypeDesc(ResUtil.getString(R.string.party_game_word_random_title));
                wordList.add(wordType);
                adapter.refresh(wordList);
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    @SuppressLint("SetTextI18n")
    private void updateInfo() {
        if (info == null) {
            return;
        }
        numTv.setText(String.valueOf(info.getAllCount()));
        numSeekBar.setProgress(info.getAllCount() - 4);
        if (ScreenUtil.isRtl()) {
            normalTv.setText(info.getNormalCount() + " x");
            spyTv.setText(info.getSpyCount() + " x");
        } else {
            normalTv.setText("x " + info.getNormalCount());
            spyTv.setText("x " + info.getSpyCount());
        }
    }

    private void initEvent() {
        backIv.setOnClickListener(v -> finish());
        numSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (info != null && info.getAllCount() != progress + 4) {
                    info.updateAllNum(progress + 4);
                }
                updateInfo();
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });
        enterIv.setOnClickListener(v -> {
            if (isCreate) {
                WordType selectWordType = adapter.getSelectWordType();
                PartyRoomPacketSender.partySpyCreateReq(0, info, selectWordType.getWordType(), new SeqCallback() {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        if (!head.codeOk()) {
                            ToastUtil.show(head.desc);
                            return;
                        }

                        PartyRoomPackets.PartyCreateRoomRsp roomRsp = (PartyRoomPackets.PartyCreateRoomRsp) head.message;
                        if (roomRsp == null) {
                            return;
                        }
                        PartyRoomInfo info = PartyRoomInfo.parse(roomRsp.getRoomInfo());
                        PartyRoomManager.get().getRoomInfoLiveData().setValue(info);
                        PartyRoomActivity.go(PartySpyEditActivity.this);
                        finish();
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                });
                if (info != null) {
                    trackClick(info.getAllCount(), selectWordType.getWordType());
                }
            } else {
                PartyRoomInfo roomInfo = PartyRoomManager.get().getRoomInfoLiveData().getValue();
                if (roomInfo != null) {
                    if (roomInfo.isStartedState()) {
                        DialogBuild.newBuilder(this).setSingleBtn(false).setContent(ResUtil.getString(R.string.party_spy_change_config_tips))
                                .setCanCancel(true).setDialogCallback(new DialogBuild.DialogCallback() {
                                    @Override
                                    public void onClickSure() {
                                        edit(roomInfo.getRid());
                                    }

                                    @Override
                                    public void onClickCancel() {

                                    }
                                }).show();
                    } else {
                        DialogBuild.newBuilder(this).setSingleBtn(false).setContent(ResUtil.getString(R.string.party_spy_change_config_tips_when_not_gaming))
                                .setCanCancel(true).setDialogCallback(new DialogBuild.DialogCallback() {
                                    @Override
                                    public void onClickSure() {
                                        edit(roomInfo.getRid());
                                    }

                                    @Override
                                    public void onClickCancel() {

                                    }
                                }).show();
                    }
                }
            }
        });
        spyWordUpdateLay.setOnClickListener(v -> {
            getWordTypes();
        });
    }

    private void edit(int rid) {
        PartyRoomPacketSender.partySpyEditReq(rid, info, adapter.getSelectWordType().getWordType(), new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                if (!head.codeOk()) {
                    ToastUtil.show(head.desc);
                    return;
                }
                ToastUtil.show(ResUtil.getStr(R.string.party_spy_change_sucessfully));
                finish();
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private void trackClick(int userNum, int type) {
        Map<String, Object> map = new HashMap<>();
        map.put("source", TrackSource.PARTY_SPY);
        map.put("user_num", userNum);
        map.put("type", type);
        TrackUtil.appClick(TrackScreenName.SPY_CREATE_PAGE, TrackButtonName.FINISH, map);
    }


    public static void goCreateRoom(Context context) {
        Intent intent = new Intent(context, PartySpyEditActivity.class);
        intent.putExtra(IS_CREATE, true);
        context.startActivity(intent);
    }


    public static void goEditRoom(Context context) {
        Intent intent = new Intent(context, PartySpyEditActivity.class);
        intent.putExtra(IS_CREATE, false);
        context.startActivity(intent);
    }

}
