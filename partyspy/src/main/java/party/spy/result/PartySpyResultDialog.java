package party.spy.result;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.util.AttributeSet;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.view.animation.RotateAnimation;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.user.LoginHelper;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.partyspy.R;

import party.room.mode.PartyRoomInfo;
import party.room.mode.PartySpyGameInfo;
import party.room.mode.PartySpyPlayerInfo;

public class PartySpyResultDialog extends FrameLayout {

    private ImageView titleIv;
    private DecorHeadImgView headIv;
    private ImageView roleIv, headBgIv;
    private TextView citizenWordTv;
    private Callback callback;
    public PartySpyResultDialog(@NonNull Context context) {
        super(context);
        initView();
    }

    public PartySpyResultDialog(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    private void initView() {
        inflate(getContext(), R.layout.party_spy_result_dialog, this);
        titleIv = findViewById(R.id.title_iv);
        headIv = findViewById(R.id.head_iv);
        roleIv = findViewById(R.id.role_iv);
        citizenWordTv = findViewById(R.id.citizen_word_tv);
        headBgIv = findViewById(R.id.result_head_bg_iv);
        findViewById(R.id.enter_tv).setOnClickListener(v -> {
            if (callback != null) callback.onDismiss();
        });
    }

    private void startAnim() {
        RotateAnimation anim = new RotateAnimation(0f, 360f, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        anim.setInterpolator(new LinearInterpolator());
        anim.setDuration(4000);
        anim.setRepeatCount(Animation.INFINITE);
        headBgIv.startAnimation(anim);
    }

    private void clearAnim() {
        if (headBgIv != null) {
            headBgIv.clearAnimation();
        }
    }
    @SuppressLint("SetTextI18n")
    private void update(PartyRoomInfo info) {
        PartySpyGameInfo gameInfo = info.getSpyGameInfo();
        PartySpyPlayerInfo player = gameInfo.getPlayer(LoginHelper.getLoginUid());
        boolean isVictory;
        if (player.isCitizen()) {
            isVictory = gameInfo.isCitizenVictory();
        } else {
            isVictory = gameInfo.isSpyVictory();
        }
        titleIv.setImageResource(isVictory ? R.drawable.party_spy_victory_icon : R.drawable.party_spy_fail_icon);
        headBgIv.setImageResource(isVictory ? R.drawable.party_spy_win_sunshine : R.drawable.party_spy_lose_sunshine);
        headIv.showUserHead(player.getUid());
        headIv.setBorderColor(player.isCitizen() ? 0xff466AD0 : 0xffC33744);
        headIv.setBorderWidth(ScreenUtil.dip2px(3));
        if (player.isCitizen()) {
            roleIv.setImageResource(R.drawable.party_spy_role1_big);
        } else if (player.isSpy()) {
            roleIv.setImageResource(R.drawable.party_spy_role2_big);
        }
        citizenWordTv.setText(gameInfo.getCitizenWord());
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public interface Callback {
        void onDismiss();
    }

    public static Dialog showDialog(Context context, PartyRoomInfo info, Callback callback) {
        BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        PartySpyResultDialog view = new PartySpyResultDialog(context);
        view.update(info);
        view.setCallback(new Callback() {
            @Override
            public void onDismiss() {
                if (callback != null) callback.onDismiss();
                view.clearAnim();
                dialog.dismiss();
            }

        });
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(true);
        dialog.initFullScreenDialog();
        dialog.show();
        view.startAnim();
        return dialog;
    }
}
