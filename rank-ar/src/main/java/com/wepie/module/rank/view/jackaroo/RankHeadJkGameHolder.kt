package com.wepie.module.rank.view.jackaroo


import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.huiwan.base.ui.Utils.gone
import com.huiwan.base.ui.Utils.invisible
import com.huiwan.base.ui.Utils.visible
import com.huiwan.base.util.ScreenUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.decorate.ChessSkinShowView
import com.huiwan.decorate.DecorHeadImgView
import com.huiwan.decorate.JackarooLevelView
import com.huiwan.decorate.NameTextView
import com.huiwan.user.UserService
import com.huiwan.user.UserSimpleInfoCallback
import com.huiwan.user.entity.UserSimpleInfo
import com.wejoy.weplay.ex.view.updateVisibility
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.libimageloader.WpImageLoader
import com.wepie.module.rank.R
import com.wepie.module.rank.util.RankJumpUtil
import com.wepie.module.rank.view.base.RankHeadBaseHolder
import com.wepie.module.rank.viewmodel.JackarooRankItem
import java.lang.ref.WeakReference

/**
 *
 * 顶部三面旗帜
 *
 * [R.layout.rank_list_jk_game_head]
 */
class RankHeadJkGameHolder(rootView: View?) : RankHeadBaseHolder<JackarooRankItem>(rootView) {
    override fun createHolder(view: View): HeadItemHolder<JackarooRankItem> {
        return HolderItem(view)
    }

    fun updateLp() {
        viewArrayList.forEachIndexed { index, item ->
            val view = item.findViewById<View>(R.id.rank_chess_lay)
            val lp = view.layoutParams as ViewGroup.MarginLayoutParams
            val marginDp = if (index == 0) 21 else 16
            lp.topMargin = ScreenUtil.dip2px(view.context, marginDp.toFloat())
            view.layoutParams = lp
        }
    }

    /**
     * [R.layout.rank_list_jk_game_head_item]
     */
    internal class HolderItem(parent: View) : HeadItemHolder<JackarooRankItem>() {
        private val flagIv: ImageView = parent.findViewById(R.id.rank_flag_rank)
        private val valueTv: TextView = parent.findViewById(R.id.value_num_tv)
        private val chessIv: ChessSkinShowView = parent.findViewById(R.id.rank_chess_iv)
        private val chipIconView: View = parent.findViewById(R.id.chip_icon_view)
        private val collectionIconView: View = parent.findViewById(R.id.collection_icon_view)
        private val gradeIv: ImageView = parent.findViewById(R.id.grade_diamond_iv)
        private val gradeKingLay: View = parent.findViewById(R.id.grade_king_lay)
        private val gradeKingTv: TextView = parent.findViewById(R.id.grade_king_tv)
        private val headIv: DecorHeadImgView = parent.findViewById(R.id.rank_head_rank)
        private val nameTv: NameTextView = parent.findViewById(R.id.rank_name_tv)
        private val levelView: JackarooLevelView = parent.findViewById(R.id.jk_level_view)
        private val crownIcon: ImageView = parent.findViewById(R.id.rank_flag_crown_icon)
        private val crownBg: ImageView = parent.findViewById(R.id.rank_flag_crown_bg)
        private val chessBgGradient: ImageView = parent.findViewById(R.id.rank_chess_gradient)
        private val levelLay: View = parent.findViewById(R.id.level_lay)

        var uid: Int = 0

        override fun updateRankNum(num: Int) {
            when (num) {
                1 -> {
                    flagIv.setImageResource(R.drawable.rank_flag_top1)
                    crownIcon.setImageResource(R.drawable.rank_crown_rank1)
                    crownBg.setImageResource(R.drawable.rank_crown_rank1_bg)
                    chessBgGradient.setImageResource(R.drawable.rank_top1_gradient)
                }

                2 -> {
                    flagIv.setImageResource(R.drawable.rank_flag_top2)
                    crownIcon.setImageResource(R.drawable.rank_crown_rank2)
                    crownBg.setImageResource(R.drawable.rank_crown_rank2_bg)
                    chessBgGradient.setImageResource(R.drawable.rank_top2_gradient)
                }

                3 -> {
                    flagIv.setImageResource(R.drawable.rank_flag_top3)
                    crownIcon.setImageResource(R.drawable.rank_crown_rank3)
                    crownBg.setImageResource(R.drawable.rank_crown_rank3_bg)
                    chessBgGradient.setImageResource(R.drawable.rank_top3_gradient)
                }

                else -> {}

            }
        }

        override fun update(info: JackarooRankItem) {
            levelLay.visible()
            this.uid = info.uid
            valueTv.text = info.value
            headIv.setOnClickListener { v: View ->
                RankJumpUtil.jumpUser(v.context, uid, TrackScreenName.RANK_JK_RANK)
            }
            chipIconView.updateVisibility(!info.collection)
            collectionIconView.updateVisibility(info.collection)
            levelView.setLevel(info.activeLevel)
            bindGradeIC(info)
            bindGradeKing(info)
            UserService.get().getCacheSimpleUser(uid, UserCb(WeakReference(this)))
        }

        override fun showEmpty() {
            chessIv.clear()
            headIv.setImageResource(R.drawable.default_head_icon)
            valueTv.text = ""
            nameTv.text = ""
            levelLay.invisible()
        }


        private fun bindGradeIC(item: JackarooRankItem) {
            val qfGrade = ConfigHelper.getInstance().constV3Info.findQFGrade(item.qualifyGrade)
            if (qfGrade == null || qfGrade.icon.isNullOrEmpty()) {
                gradeIv.gone()
            } else {
                gradeIv.visible()
                WpImageLoader.load(qfGrade.icon, gradeIv)
            }
        }

        private fun bindGradeKing(item: JackarooRankItem) {
            if (item.qualifyStar > 0) {
                gradeKingLay.visible()
                gradeKingTv.text = item.qualifyStar.toString()
            } else {
                gradeKingLay.gone()
            }
        }


        class UserCb(val ref: WeakReference<HolderItem>) : UserSimpleInfoCallback {
            override fun onUserInfoSuccess(simpleInfo: UserSimpleInfo?) {
                val holder = ref.get()
                if (holder != null && simpleInfo != null && simpleInfo.uid == holder.uid) {
                    holder.nameTv.setUserName(simpleInfo)
                    val propItem =
                        ConfigHelper.getInstance().propConfig.getPropItem(simpleInfo.collectionInfo.inUseChessPropId)

                    holder.chessIv.bind(propItem, false)
                    holder.headIv.showUserHead(simpleInfo.uid)
                }
            }

            override fun onUserInfoFailed(description: String?) {
            }
        }
    }
}
