package com.wepie.module.rank.activity;

import android.content.Intent;
import android.graphics.Matrix;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.util.SparseArray;
import android.util.SparseIntArray;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.appbar.MaterialToolbar;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.WebApi;
import com.wepie.module.rank.R;
import com.wepie.module.rank.fragment.BaseRankFragment;
import com.wepie.module.rank.fragment.RankCoupleFragment;
import com.wepie.module.rank.fragment.RankFamilyFragment;
import com.wepie.module.rank.fragment.RankJkGameFragment;
import com.wepie.module.rank.fragment.RankPopularityFragment;
import com.wepie.module.rank.fragment.RankVipFragment;
import com.wepie.module.rank.fragment.RankVoiceRoomFragment;
import com.wepie.module.rank.label.RankLabelHolder;
import com.wepie.module.rank.label.SubLabelAdapter;
import com.wepie.module.rank.viewmodel.RankViewModel;

import java.util.ArrayList;
import java.util.List;

// Created by sqx on 2021/5/27.
public class NewRankActivity extends BaseActivity implements IRankFullBg {
    private static final String TAG = "NewRankActivity";
    public static final int RANK_TAB_INDEX_JK = 0;
    public static final int POPULARITY_TAB_INDEX = 1;
    public static final int VIP_TAB_INDEX = 2;
    public static final int LOVE_TAB_INDEX = 3;
    public static final int VOICE_ROOM_TAB_INDEX = 4;
    public static final int FAMILY_TAB_INDEX = 5;
    public static final int AVATAR_TAB_INDEX = 6;
    public static final int MAX_TAB_INDEX = AVATAR_TAB_INDEX;
    public static final String SELECT_TAB_INDEX = "rank_select_tab_index";
    public static final String SELECT_CHILD_TAB_INDEX = "rank_select_child_tab_index";
    public static final String SELECT_CHILD_SUB_TAB_INDEX = "rank_select_child_sub_tab_index";


    public static final SparseIntArray typeMap = new SparseIntArray();

    private RankViewModel model;

    private ImageView bgIv;
    private RankLabelHolder labelHolder;
    private ViewPager2 fragmentPager;
    private final List<String> titleNameList = new ArrayList<>();

    private final SparseArray<FragmentItem<?>> fragmentSparse = new SparseArray<>(6);

    static class FragmentItem<T extends BaseRankFragment> {
        final Class<T> c;

        public FragmentItem(Class<T> c) {
            this.c = c;
        }

    }

    static {
        typeMap.put(0, RANK_TAB_INDEX_JK);
        typeMap.put(1, POPULARITY_TAB_INDEX);
        typeMap.put(2, VIP_TAB_INDEX);
        typeMap.put(3, LOVE_TAB_INDEX);
        typeMap.put(4, VOICE_ROOM_TAB_INDEX);
        typeMap.put(5, FAMILY_TAB_INDEX);
    }

    /**
     * 全局选中哪个tab信息
     */
    public static int selectTab = RANK_TAB_INDEX_JK;
    public static int selectChildIndex = 0;

    public static int selectChildSubIndex = 0;


    private final View.OnClickListener rankRankHelpListener = v -> {
        //跳转榜单帮助页面
        ApiService.of(WebApi.class).gotoWebActivity(NewRankActivity.this, ConfigHelper.getInstance().getConstV3Info().rankHelpUrl);
    };

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        StatusBarUtil.initStatusBar(this);
        StatusBarUtil.setStatusFontWhiteColor(this);
        setContentView(R.layout.activity_new_rank);
        model = new ViewModelProvider(this).get(RankViewModel.class);
        initArgs(getIntent());
        initViews();
        initFragmentSparser();
        initLabel();
        int index = typeMap.indexOfValue(selectTab);
        int pos = typeMap.keyAt(index);
        setSelectTab(pos, selectTab);
        model.setIndex(selectTab, selectChildIndex,selectChildSubIndex);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuItem item = menu.add("");
        item.setShowAsAction(MenuItem.SHOW_AS_ACTION_IF_ROOM);
        item.setIcon(R.drawable.ic_help_21);
        item.setOnMenuItemClickListener(i -> {
            rankRankHelpListener.onClick(item.getActionView());
            return true;
        });
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        initArgs(intent);
        int index = typeMap.indexOfValue(selectTab);
        int pos = typeMap.keyAt(index);
        setSelectTab(pos, selectTab);
        model.setIndex(selectTab, selectChildIndex,selectChildSubIndex);
    }

    private void initArgs(Intent intent) {
        selectTab = intent.getIntExtra(SELECT_TAB_INDEX, RANK_TAB_INDEX_JK);
        if (selectTab > MAX_TAB_INDEX || selectTab < RANK_TAB_INDEX_JK) {
            selectTab = RANK_TAB_INDEX_JK;
        }
        selectChildIndex = intent.getIntExtra(SELECT_CHILD_TAB_INDEX, 0);
        selectChildSubIndex = intent.getIntExtra(SELECT_CHILD_SUB_TAB_INDEX, 0);
    }

    private void initViews() {
        bgIv = findViewById(R.id.bg_iv);
        labelHolder = new RankLabelHolder(findViewById(R.id.rank_label_view));
        MaterialToolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        toolbar.setNavigationOnClickListener(v -> onBackPressed());
        ((ViewGroup.MarginLayoutParams) toolbar.getLayoutParams()).topMargin = ScreenUtil.getStatusBarHeight();
        Matrix matrix = new Matrix();
        bgIv.setImageMatrix(matrix);
        bgIv.setScaleType(ImageView.ScaleType.MATRIX);
        fragmentPager = findViewById(R.id.content_pager);
//        fragmentPager.setOffscreenPageLimit(6);
        fragmentPager.setAdapter(new FragmentStateAdapter(this) {
            @NonNull
            @Override
            public Fragment createFragment(int position) {
                int type = typeMap.valueAt(position);
                FragmentItem<?> item = fragmentSparse.get(type);
                Fragment fragment = null;
                try {
                    fragment = item.c.newInstance();
                } catch (IllegalAccessException | InstantiationException e) {
                    e.printStackTrace();
                }
                if (fragment == null) {
                    if (LibBaseUtil.buildDebug()) {
                        throw new RuntimeException("Init Fragment failed");
                    }
                    fragment = new RankPopularityFragment();
                }
                Bundle args = new Bundle();
                args.putInt(BaseRankFragment.KEY_INDEX, type);
                fragment.setArguments(args);
                return fragment;
            }

            @Override
            public int getItemCount() {
                return typeMap.size();
            }
        });
        fragmentPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                labelHolder.selectItem(position);
                setSelectTab(position);
            }
        });
    }

    private void initFragmentSparser() {
        fragmentSparse.put(RANK_TAB_INDEX_JK, new FragmentItem<>(RankJkGameFragment.class));
        fragmentSparse.put(POPULARITY_TAB_INDEX, new FragmentItem<>(RankPopularityFragment.class));
        fragmentSparse.put(VIP_TAB_INDEX, new FragmentItem<>(RankVipFragment.class));
        fragmentSparse.put(LOVE_TAB_INDEX, new FragmentItem<>(RankCoupleFragment.class));
        fragmentSparse.put(VOICE_ROOM_TAB_INDEX, new FragmentItem<>(RankVoiceRoomFragment.class));
        fragmentSparse.put(FAMILY_TAB_INDEX, new FragmentItem<>(RankFamilyFragment.class));
    }

    private void initLabel() {
        titleNameList.add(ResUtil.getString(R.string.rank_jackaroo_text));
        titleNameList.add(ResUtil.getString(R.string.rank_popularity_text));
        titleNameList.add(ResUtil.getString(R.string.rank_vip_text));
        titleNameList.add(ResUtil.getString(R.string.rank_couple_text));
        titleNameList.add(ResUtil.getString(R.string.rank_voice_room_text));
        titleNameList.add(ResUtil.getString(R.string.rank_family_text));
        titleNameList.add(ResUtil.getString(R.string.rank_avatar_show));

        labelHolder.setClickCallback((data, index) -> setSelectTab(index));
        List<SubLabelAdapter.LabelInfo> labelInfoList = new ArrayList<>();
        for (int i = 0; i < typeMap.size(); i++) {
            int index = typeMap.valueAt(i);
            labelInfoList.add(new SubLabelAdapter.LabelInfo(titleNameList.get(index)));
        }
        int index = typeMap.indexOfValue(selectTab);
        labelHolder.refreshLabel(labelInfoList, typeMap.keyAt(index));
    }

    private void setSelectTab(int position) {
        setSelectTab(position, typeMap.get(position));
    }

    private void setSelectTab(int position, int type) {
        FragmentItem<?> item = fragmentSparse.get(type);
        fragmentPager.setCurrentItem(position, false);
        selectTab = type;
        labelHolder.selectItem(position);
    }

    @Override
    public void showRankBg(@DrawableRes int bgRes) {
        applyBg(bgRes);
    }

    private void applyBg(@DrawableRes int bgRes) {
        Drawable drawable = ContextCompat.getDrawable(this, bgRes);
        if (drawable != null) {
            bgIv.setImageDrawable(drawable);
            if (drawable.getIntrinsicWidth() > 0 && drawable.getIntrinsicHeight() > 0) {
                float scaleBase = ScreenUtil.getScreenWidth() / (float) drawable.getIntrinsicWidth();
                float scaleY = ScreenUtil.getScreenHeight() / (float) drawable.getIntrinsicHeight();
                if (scaleY < scaleBase) {
                    //解决底部会留一点点白边
                    scaleY = scaleBase + 0.1f;
                }
                Matrix matrix = bgIv.getImageMatrix();
                matrix.setScale(scaleBase, scaleY);
                bgIv.setImageMatrix(matrix);
            }
        }
    }
}
