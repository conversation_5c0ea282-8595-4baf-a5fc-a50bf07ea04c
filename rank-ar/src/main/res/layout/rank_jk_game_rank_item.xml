<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="68dp"
    android:clipChildren="false">

    <com.huiwan.decorate.DecorHeadImgView
        android:id="@+id/head_iv"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="50dp"
        app:border_end_color="#FFC28C59"
        app:border_star_color="#FFFAF0D4"
        app:gradiant_area_size="48dp"
        app:head_border_width="1dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:use_gradiant_border="true" />

    <com.huiwan.decorate.ChessSkinShowView
        android:id="@+id/chess_iv"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:layout_marginEnd="-12dp"
        android:background="@drawable/rank_chess_bg"
        app:layout_constraintBottom_toBottomOf="@+id/head_iv"
        app:layout_constraintEnd_toEndOf="@+id/head_iv" />

    <TextView
        android:id="@+id/rank_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/head_iv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:onlyNum="true"
        tools:text="4" />

    <com.huiwan.decorate.NameTextView
        android:id="@+id/name_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="2dp"
        android:ellipsize="end"
        android:maxWidth="160dp"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="16dp"
        app:layout_constraintBottom_toTopOf="@id/jk_level_view"
        app:layout_constraintStart_toEndOf="@id/chess_iv"
        app:ntv_scene="rank"
        tools:text="name name name name" />

    <com.huiwan.decorate.JackarooLevelView
        android:id="@+id/jk_level_view"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:layout_marginBottom="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@id/name_tv" />

    <LinearLayout
        android:id="@+id/grade_king_lay"
        android:layout_width="wrap_content"
        android:layout_height="12dp"
        android:layout_marginStart="11dp"
        android:background="@drawable/rank_grade_star_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="8dp"
        android:paddingEnd="4dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/jk_level_view"
        app:layout_constraintStart_toStartOf="@id/grade_diamond_iv"
        app:layout_constraintTop_toTopOf="@id/jk_level_view">

        <View
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_marginStart="2dp"
            android:background="@drawable/grade_star" />

        <TextView
            android:id="@+id/grade_king_tv"
            android:layout_width="wrap_content"
            android:layout_height="12dp"
            android:gravity="center"
            android:includeFontPadding="false"
            android:textColor="#FFED53"
            android:textSize="10dp"
            android:textStyle="bold" />
    </LinearLayout>

    <ImageView
        android:id="@+id/grade_diamond_iv"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="2dp"
        app:layout_constraintBottom_toBottomOf="@id/jk_level_view"
        app:layout_constraintStart_toEndOf="@id/jk_level_view"
        app:layout_constraintTop_toTopOf="@id/jk_level_view" />

    <LinearLayout
        android:id="@+id/value_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:gravity="center_vertical|end"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:id="@+id/collection_icon_view"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginEnd="2dp"
            android:background="@drawable/collection_chip"
            android:visibility="gone"
            tools:visibility="visible" />

        <View
            android:id="@+id/chip_icon_view"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginEnd="2dp"
            android:background="@drawable/game_chip"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/value_num_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textFontWeight="700"
            android:textSize="12dp"
            android:textStyle="bold"
            tools:text="99999" />

    </LinearLayout>


    <View
        android:id="@+id/bottom_line"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:background="@color/white_alpha15"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/head_iv" />


</androidx.constraintlayout.widget.ConstraintLayout>