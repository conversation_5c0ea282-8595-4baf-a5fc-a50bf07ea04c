apply from: "../base_module.gradle"

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    //这里引用的模块注意多进程处理，初始化等等
    implementation libs.eventBus
    implementation project(path: ':lib:baseutil')
    implementation project(path: ':lib:floating')
    implementation project(path: ':lib:libwidget')
    implementation project(path: ':lib:anim')
    implementation project(path: ':lib:liblog')
    implementation project(path: ":lib:api-plugin:track")
    implementation project(path: ':service:UserService')
    implementation project(path: ':service:VoiceService')
    implementation project(path: ':service:ConfigService')
    implementation project(path: ':lib:libtcp')
    implementation project(path: ':lib:libdialog')
    implementation project(path: ':lib:Emojilib')
    implementation project(path: ':lib:libproto')
    implementation project(path: ':lib:hwconstants')
    implementation project(path: ':lib:store')
    implementation project(path: ':component:decorate')
    implementation project(path: ':component:gift')
    implementation project(path: ':component:barrage')
    implementation project(path: ':component:activity')
    implementation project(path: ':lib:libhttp')
    implementation project(path: ':lib:libimageloader')
    implementation project(path: ':lib:api')
    implementation project(path: ':lib:libdownload')
    implementation project(path: ':lib:api-plugin:add-friend')
    implementation project(path: ':lib:ipc')
    implementation project(path: ':lib:libunity')
    implementation project(":module:basechat")
    implementation project(":module:hwroom")
    implementation project(path: ':lib:libpermission')
    implementation project(path: ':lib:api-plugin:voice-api')
    implementation project(path: ':lib:api-plugin:hwroom-api')
    implementation project(path: ":lib:os-libshare")
    implementation project(path: ":lib:os-resources")

    implementation libs.constraintlayout
    implementation libs.kotlinx.coroutines.android
    implementation libs.kotlinx.viewmodel
    implementation project(path: ':lib:api-plugin:invite-api')
    implementation project(path: ':lib:libtrack-shushu')
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
}