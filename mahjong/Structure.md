新类型游戏房间结构说明（目前只有台湾麻将）
===
该Moudle主要负责新桌游功能的实现（目前只有台湾麻将一种）
该房间主要以MVVM的模式进行的设计，主要有NewGameActivity、NewGameRoomViewModel以及个类型对应的INewGameView构成

各组件说明
---
1、NewGameActivity（游戏房间承载组件）：
该Activity为游戏房间的主页面，每次进入该类型的游戏界面都会创建一个新的该NewGameActivity实例，退出游戏销毁该实例。
在该Activity中，主要负责管理整个游戏页面的生命周期，以及根据对应房间的roomData创建或刷新INewGameView。

2、NewGameRoomViewModel（房间数据管理组建）：
该ViewModel主要用于临时存储房间信息roomData，以及根据接收的不同IGameRoomEvent对roomData的数据
进行处理，然后更新并通知NewGameActivity刷新UI。

3、INewGameView（具体类型的游戏UI组件）：
该View由NewGameActivity创建实例，当用户创建或匹配到房间后跳转进入NewGameActivity，在NewGameActivity中初始化
各个组建并和Activity进行绑定，然后根据传入的房间rid获取房间信息，根据房间新中的gameType创建对应的INewGameView实例，
并将该实例添加到NewGameActivity的对应容器中。

INewGameView内部结构整体也将会和NewGameActivity的结构类似，内部会存在一个或多个ViewModel，每个ViewModel
负责缓存和处理指定部分的数据信息，根据接收的不同的指定对不同数据进行处理并更新，然后通知绑定对应LiveData的
View进行UI刷新。

4、其他：
其实目前只是设计了一个大体上的结构，比较简单的只是针对UI和数据的模型，之后具体开发时还将需要在当前的基础进行
组件添加补充，比如：聊天相关的数据发送组件、语音相关的组件、礼物相关的组件等等。目前在那时还没有对这些部分进行设计，
待之后进行相关开发在进行完善补充。

目前涉及的相关类与实体
---
相关处理类：
    * NewGameApi 接口，用于事件总线的Flow获取，具体实现为主项目中的NewGameApiImpl
    * NewGameApiImpl 接口NewGameApi，用于注册监听生成数据流
    * FlowDispatcher 事件分发处理器，该类用于分发监听到的tcp数据包转换后的Event信息
    * NewGameHandler(暂未添加) 用于根据指令接收和处理TCP数据包，并将处理后的数据转换为Event，调用FlowDispatcher分发
    * NewGameSocketThread(暂未添加) 用于连接新的游戏房间的socket，并在该线程中对socket进行读写操作

实体类：
    台湾麻将：
        * NewGameRoomInfo：房间信息
        * MahjongGameInfo：游戏信息 内部主要包含游戏状态、游戏桌面信息、玩家信息等
        * MahjongTableInfo：游戏台面信息
        * MahjongPlayerInfo：玩家信息 包含该玩家的状态、手牌、明牌区、出牌区、花牌等信息
        * MahjongRoundResult：每圈结算信息
            * RoundResultPlayerInfo：每圈结算时各玩家的结算信息
        * MahjongGameResult：每局结算信息
            * GameResultPlayerInfo：每局结算时各玩家的结算信息
    