<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/send_message_layout"
    android:layout_width="180dp"
    android:layout_height="235dp"
    android:background="@drawable/shape_message_layout_bg"
    android:orientation="vertical">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/default_message_rv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@drawable/shape_input_tv_bg"
        android:layout_marginStart="10dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="11dp">
        <TextView
            android:id="@+id/input_tv"
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:gravity="center_vertical"
            android:text="@string/mahjong_input_text"
            android:paddingStart="8dp"
            android:textColor="#C09373"
            android:textSize="14sp"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/send_btn"
            app:layout_constraintHorizontal_chainStyle="spread"/>

        <TextView
            android:id="@+id/send_btn"
            android:layout_width="wrap_content"
            android:layout_height="28dp"
            android:minWidth="60dp"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            android:text="@string/send"
            android:gravity="center"
            android:background="@drawable/mj_message_send_bg"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/input_tv"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="2dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>