<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:id="@+id/table_info_layout"
        android:layout_width="wrap_content"
        android:minWidth="61dp"
        android:layout_height="40dp"
        android:background="@drawable/mahjong_table_lay_bg"
        android:gravity="center_vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/mj_room_info_lay"
        app:layout_constraintHorizontal_chainStyle="packed">
        <TextView
            android:id="@+id/di_num_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#ffffff"
            android:textSize="12sp"
            tools:text="100"
            android:layout_marginStart="30dp"/>

        <TextView
            android:id="@+id/tai_num_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#ffffff"
            android:textSize="12sp"
            tools:text="60"
            android:layout_below="@+id/di_num_tv"
            android:layout_marginStart="30dp"/>
    </RelativeLayout>

    <TextView
        android:id="@+id/mj_bank_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="14sp"
        tools:text="東一局"
        app:layout_constraintTop_toBottomOf="@+id/table_info_layout"
        app:layout_constraintStart_toStartOf="@+id/table_info_layout"
        app:layout_constraintEnd_toEndOf="@+id/table_info_layout"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mj_room_info_lay"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:minWidth="52dp"
        android:background="@drawable/mj_room_info_lay_bg"
        app:layout_constraintTop_toTopOf="@+id/table_info_layout"
        app:layout_constraintBottom_toBottomOf="@+id/table_info_layout"
        app:layout_constraintStart_toEndOf="@+id/table_info_layout"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingStart="7dp"
        android:paddingEnd="7dp"
        android:layout_marginStart="6dp">

        <ImageView
            android:id="@+id/mj_visitor_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/mj_visitor_icon_white"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/mj_visitor_num_tv"
            app:layout_constraintHorizontal_chainStyle="spread"
            android:layout_marginTop="8dp" />

        <TextView
            android:id="@+id/mj_visitor_num_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="10sp"
            android:textColor="#FFFFFF"
            tools:text="123"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@+id/mj_visitor_icon"
            app:layout_constraintEnd_toStartOf="@+id/mj_visitor_arrow_iv"
            android:layout_marginTop="6dp"/>

        <ImageView
            android:id="@+id/mj_visitor_arrow_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/mj_visitor_arrow_white"
            app:layout_constraintTop_toTopOf="@+id/mj_visitor_icon"
            app:layout_constraintBottom_toBottomOf="@+id/mj_visitor_icon"
            app:layout_constraintStart_toEndOf="@+id/mj_visitor_num_tv"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="@dimen/dp2"/>

        <TextView
            android:id="@+id/mj_room_id_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            tools:text="1234房"
            android:textColor="#FFFFFF"
            android:textSize="10sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginBottom="2dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/mj_room_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="14sp"
        tools:text="好友房"
        app:layout_constraintTop_toBottomOf="@+id/mj_room_info_lay"
        app:layout_constraintStart_toStartOf="@+id/mj_room_info_lay"
        app:layout_constraintEnd_toEndOf="@+id/mj_room_info_lay"/>
</androidx.constraintlayout.widget.ConstraintLayout>