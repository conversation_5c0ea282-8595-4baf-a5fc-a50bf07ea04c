<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <include layout="@layout/mj_settlement_player_view"
        android:id="@+id/top_player"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="52dp"/>

    <include layout="@layout/mj_settlement_player_view"
        android:id="@+id/left_player"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="83dp"/>

    <include layout="@layout/mj_settlement_player_view"
        android:id="@+id/bottom_player"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="64dp"/>

    <include layout="@layout/mj_settlement_player_view"
        android:id="@+id/right_player"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="83dp"/>

    <ImageView
        android:id="@+id/center_iv"
        android:layout_width="82dp"
        android:layout_height="82dp"
        android:src="@drawable/mj_settlement_arrows_p"
        app:layout_constraintTop_toBottomOf="@+id/top_player"
        app:layout_constraintBottom_toTopOf="@+id/bottom_player"
        app:layout_constraintStart_toEndOf="@+id/left_player"
        app:layout_constraintEnd_toStartOf="@+id/right_player"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/left_top_iv"
        android:layout_width="82dp"
        android:layout_height="82dp"
        android:src="@drawable/mj_settlement_arrows_l"
        app:layout_constraintTop_toTopOf="@+id/top_player"
        app:layout_constraintBottom_toTopOf="@+id/left_player"
        app:layout_constraintEnd_toStartOf="@+id/top_player"
        android:layout_marginEnd="40dp"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/right_top_iv"
        android:layout_width="82dp"
        android:layout_height="82dp"
        android:src="@drawable/mj_settlement_arrows_l"
        app:layout_constraintTop_toTopOf="@+id/top_player"
        app:layout_constraintBottom_toTopOf="@+id/right_player"
        app:layout_constraintStart_toEndOf="@+id/top_player"
        android:layout_marginStart="40dp"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/left_bottom_iv"
        android:layout_width="82dp"
        android:layout_height="82dp"
        android:src="@drawable/mj_settlement_arrows_l"
        app:layout_constraintTop_toBottomOf="@+id/left_player"
        app:layout_constraintBottom_toBottomOf="@+id/bottom_player"
        app:layout_constraintEnd_toStartOf="@+id/bottom_player"
        android:layout_marginTop="18dp"
        android:layout_marginEnd="40dp"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/right_bottom_iv"
        android:layout_width="82dp"
        android:layout_height="82dp"
        android:src="@drawable/mj_settlement_arrows_l"
        app:layout_constraintTop_toBottomOf="@+id/right_player"
        app:layout_constraintBottom_toBottomOf="@+id/bottom_player"
        app:layout_constraintStart_toEndOf="@+id/bottom_player"
        android:layout_marginTop="18dp"
        android:layout_marginStart="40dp"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/mj_feeTips_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:textColor="#9AA2BF"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/dp13"/>
</androidx.constraintlayout.widget.ConstraintLayout>