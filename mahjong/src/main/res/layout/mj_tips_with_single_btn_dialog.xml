<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mj_tips_bg"
        android:layout_width="276dp"
        android:layout_height="wrap_content"
        android:background="@drawable/mj_hint_dialog_bg"
        android:paddingTop="28dp"
        android:paddingBottom="20dp"
        android:paddingStart="18dp"
        android:paddingEnd="18dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/mj_tips_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="#2D2D2D"
            android:textSize="14sp"
            tools:text="@string/mj_invite_tips"
            android:lineSpacingExtra="@dimen/dp3"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/mj_tips_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="112dp"
            android:minHeight="36dp"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:gravity="center"
            android:text="@string/sure"
            android:background="@drawable/invite_btn_bg"
            android:paddingTop="7dp"
            android:paddingBottom="7dp"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            app:layout_constraintTop_toBottomOf="@+id/mj_tips_tv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="26dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <Space
        android:id="@+id/close_guide_line"
        android:layout_width="1dp"
        android:layout_height="1dp"
        app:layout_constraintBottom_toTopOf="@+id/mj_tips_bg"
        app:layout_constraintStart_toEndOf="@+id/mj_tips_bg"
        android:layout_marginBottom="5dp"
        android:layout_marginStart="8dp"/>

    <ImageView
        android:id="@+id/mj_dialog_close"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:src="@drawable/yellow_close"
        app:layout_constraintTop_toTopOf="@+id/close_guide_line"
        app:layout_constraintEnd_toEndOf="@+id/close_guide_line"/>
</androidx.constraintlayout.widget.ConstraintLayout>