<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root_lay"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/mahjong_table_bg"
    android:clipChildren="false">

    <com.wepie.module.mahjong.view.MJGameReadyView
        android:id="@+id/ready_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:clipChildren="false"
        android:visibility="gone"/>

    <com.wepie.module.mahjong.view.MJGamePlayingView
        android:id="@+id/playing_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:clipChildren="false"
        android:visibility="gone"/>

    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/mj_environment_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <com.wepie.module.mahjong.view.MJToolsView
        android:id="@+id/tools_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:clipChildren="false"/>

    <com.wepie.module.mahjong.view.MJRoundOverView
        android:id="@+id/settlement_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:clipChildren="false"
        android:visibility="gone"/>

    <com.wepie.module.mahjong.view.MJHuangView
        android:id="@+id/huang_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:clipChildren="false"
        android:visibility="gone"/>

    <com.wepie.module.mahjong.view.MJGameOverView
        android:id="@+id/over_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:clipChildren="false"
        android:visibility="gone"/>

    <com.wepie.module.mahjong.view.custom.MJClickAnimView
        android:id="@+id/click_anim_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <com.wepie.module.mahjong.view.custom.MJInviteListView
        android:id="@+id/mj_invite_list"
        android:layout_width="276dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="17dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>