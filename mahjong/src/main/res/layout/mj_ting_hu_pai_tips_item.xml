<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/ting_tai_num"
        android:layout_width="wrap_content"
        android:layout_height="17dp"
        android:textColor="#FFFFFF"
        android:textSize="12sp"
        tools:text="2台"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <com.wepie.module.mahjong.view.MJCardView
        android:id="@+id/ting_pai_view"
        android:layout_width="35dp"
        android:layout_height="50dp"
        app:layout_constraintTop_toBottomOf="@+id/ting_tai_num"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/ting_pai_num"
        android:layout_width="wrap_content"
        android:minWidth="30dp"
        android:layout_height="14dp"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        tools:text="1张"
        android:gravity="center"
        android:background="@drawable/mj_ting_tips_num_bg"
        app:layout_constraintTop_toBottomOf="@+id/ting_pai_view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>