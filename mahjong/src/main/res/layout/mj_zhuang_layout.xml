<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/zhuang_txt"
        android:layout_width="wrap_content"
        android:layout_height="16dp"
        android:gravity="center"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:textColor="#ffffff"
        android:textSize="10dp"
        android:text="@string/zhuang_hint"
        android:background="@drawable/shape_zhuang_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>