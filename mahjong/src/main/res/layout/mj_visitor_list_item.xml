<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="42dp"
    android:layout_marginTop="8dp"
    android:paddingStart="14dp"
    android:paddingEnd="14dp">

    <com.huiwan.widget.CustomCircleImageView
        android:id="@+id/player_head_view"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:background="@drawable/shape_head_circle_bg"
        android:padding="2dp"
        android:src="@drawable/default_head_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/add_friend"
        android:layout_width="60dp"
        android:layout_height="26dp"
        android:background="@drawable/invite_btn_bg"
        android:gravity="center"
        android:text="@string/mj_visitor_add_friend"
        android:textColor="#FFFFFF"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.wepie.module.mahjong.view.custom.MJTimerProgressMask
        android:id="@+id/invite_mask_view"
        android:layout_width="60dp"
        android:layout_height="26dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@+id/add_friend"
        app:layout_constraintBottom_toBottomOf="@+id/add_friend"
        app:layout_constraintStart_toStartOf="@+id/add_friend"
        app:layout_constraintEnd_toEndOf="@+id/add_friend"/>

    <TextView
        android:id="@+id/player_nick"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="30dp"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:lines="1"
        android:textColor="#2D2D2D"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/add_friend"
        app:layout_constraintStart_toEndOf="@+id/player_head_view"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="花覅keeef~" />
</androidx.constraintlayout.widget.ConstraintLayout>