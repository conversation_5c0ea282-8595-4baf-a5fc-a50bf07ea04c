<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root_settlement_head"
    android:layout_width="wrap_content"
    android:layout_height="42dp">

    <com.huiwan.widget.CustomCircleImageView
        android:id="@+id/player_head_view"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:background="@drawable/shape_head_circle_bg"
        android:padding="2dp"
        android:src="@drawable/default_head_icon" />

    <LinearLayout
        android:id="@+id/head_right_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_toRightOf="@+id/player_head_view"
        android:orientation="vertical">

        <com.huiwan.decorate.NameTextView
            android:id="@+id/player_name_view"
            android:textColor="#FFFFFFFF"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="陈一波"/>

        <TextView
            android:id="@+id/zhuang_icon"
            android:layout_width="20dp"
            android:layout_height="16dp"
            android:gravity="center"
            android:background="@drawable/shape_zhuang_bg"
            android:text="@string/zhuang_hint"
            android:textColor="#FFFFFF"
            android:includeFontPadding="false"
            android:textSize="10sp"
            android:layout_marginTop="2dp" />
    </LinearLayout>
</RelativeLayout>