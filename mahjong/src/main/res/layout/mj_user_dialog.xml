<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/user_dialog_root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false">

    <RelativeLayout
        android:layout_width="304dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        android:clipChildren="false"
        android:scaleY="0.8"
        android:scaleX="0.8">

        <RelativeLayout
            android:id="@+id/ice_ball_content_lay"
            android:layout_width="304dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:background="@drawable/mj_user_dialog_bg">

            <LinearLayout
                android:id="@+id/ice_ball_name_lay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="30dp"
                android:layout_marginTop="48dp"
                android:layout_marginRight="30dp"
                android:gravity="center"
                android:orientation="horizontal">

                <com.huiwan.decorate.NameTextView
                    android:id="@+id/ice_ball_nick"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:maxEms="6"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:text="大大大大大大大大大"
                    android:textColor="#000000"
                    android:textSize="17dp" />

                <ImageView
                    android:id="@+id/gender_iv"
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_marginStart="4dp"
                    tools:src="@drawable/mj_female" />

                <com.huiwan.decorate.vip.VipLabelView
                    android:id="@+id/vip_label"
                    android:layout_width="28dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="2dp"
                    tools:src="@drawable/vip_label_1" />

                <ImageView
                    android:id="@+id/charm_iv"
                    android:layout_width="34dp"
                    android:layout_height="22dp"
                    android:layout_marginStart="2dp"
                    tools:src="@drawable/user_charm_level_1" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/seat_location_lay"
                android:layout_width="wrap_content"
                android:layout_height="17dp"
                android:layout_below="@id/ice_ball_name_lay"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="10dp">
                <com.huiwan.widget.CustomCircleImageView
                    android:id="@+id/seat_location_head_iv"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginEnd="4dp"
                    app:civ_border_width="1dp"
                    app:civ_border_color="#ECEDEF"/>

                <TextView
                    android:id="@+id/seat_location_tx"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center"
                    android:text="湖北 武汉"
                    android:textColor="#999999"
                    android:textSize="14dp" />

                <ImageView
                    android:layout_width="1dp"
                    android:layout_height="12dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    android:background="#999999"
                    android:textSize="14dp" />

                <TextView
                    android:id="@+id/seat_distance_tx"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:gravity="center"
                    android:text="0km"
                    android:textColor="#999999"
                    android:textSize="14dp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ice_ball_score_lay"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_below="@id/seat_location_lay"
                android:layout_marginTop="12dp"
                android:gravity="center"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="92dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/ice_ball_play_times"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="0"
                        android:textColor="#1B1B1B"
                        android:textSize="24dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="@string/mj_total_count"
                        android:textColor="#999999"
                        android:textSize="12dp" />
                </LinearLayout>

                <ImageView
                    android:layout_width="1px"
                    android:layout_height="36dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    android:src="#C2C2C2" />

                <LinearLayout
                    android:layout_width="92dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/ice_ball_win_rate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="0%"
                        android:textColor="#1B1B1B"
                        android:textSize="24dp" />

                    <TextView
                        android:id="@+id/level_explain_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="@string/mj_win_rate"
                        android:textColor="#999999"
                        android:textSize="12dp" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ice_ball_add_friend_lay"
                android:layout_width="match_parent"
                android:layout_height="43dp"
                android:layout_below="@id/ice_ball_score_lay"
                android:layout_centerHorizontal="true"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="12dp"
                android:layout_marginRight="20dp"
                android:layout_marginBottom="8dp"
                android:visibility="visible">

                <TextView
                    android:id="@+id/ice_ball_send_gift_bt"
                    android:layout_width="120dp"
                    android:layout_height="42dp"
                    android:background="@drawable/mj_send_gift_icon"
                    android:gravity="center"
                    android:text="@string/ice_ball_send_gift"
                    android:textColor="#ffffff"
                    android:textSize="16dp" />

                <Space
                    android:id="@+id/ice_ball_send_gift_center_space"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/ice_ball_add_friend_bt"
                    android:layout_width="120dp"
                    android:layout_height="42dp"
                    android:background="@drawable/mj_add_friend_icon"
                    android:gravity="center"
                    android:text="@string/common_add_friends"
                    android:textColor="#ffffff"
                    android:textSize="16dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/seat_dialog_mute_lay"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_below="@+id/ice_ball_add_friend_lay"
                android:background="@drawable/mj_dialog_mute_bg">
                <ImageView
                    android:id="@+id/seat_dialog_mute_iv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/mj_dialog_mute_icon"/>
                <TextView
                    android:id="@+id/seat_dialog_mute_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/mj_mute_user"
                    android:textColor="#999999"
                    android:gravity="center"
                    android:textSize="14dp"
                    android:layout_marginStart="6dp"/>
            </LinearLayout>
        </RelativeLayout>

        <com.huiwan.decorate.DecorHeadImgView
            android:id="@+id/ice_ball_head_image"
            android:layout_width="81dp"
            android:layout_height="81dp"
            android:layout_centerHorizontal="true"
            android:clipChildren="false"
            android:src="@drawable/default_head_icon"
            app:civ_border_color="#ffffff"
            app:civ_border_width="1dp" />

        <TextView
            android:id="@+id/seat_dialog_report_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:layout_marginTop="40dp"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:paddingEnd="16dp"
            android:paddingStart="16dp"
            android:text="@string/ice_ball_report"
            android:textColor="#999999"
            android:textSize="12sp"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/seat_dialog_avatar_iv"
            android:layout_marginTop="-79dp"
            android:layout_width="108dp"
            android:layout_height="225dp" />
    </RelativeLayout>
</RelativeLayout>