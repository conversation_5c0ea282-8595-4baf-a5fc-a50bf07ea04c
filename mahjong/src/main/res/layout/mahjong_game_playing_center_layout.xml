<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    tools:background="@drawable/mahjong_table_bg"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/center_bg_iv"
        android:layout_width="140dp"
        android:layout_height="140dp"
        android:background="@drawable/mj_table_center_pic"
        android:clipChildren="false"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_bias="0.37"
        android:rotationX="10"
        android:scaleY="0.7">

        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/center_light_anim"
            android:layout_width="96dp"
            android:layout_height="56dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="70dp"
            android:visibility="gone"/>

        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/center_light_particle_anim"
            android:layout_width="100dp"
            android:layout_height="50dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="gone"/>

        <com.wepie.module.mahjong.view.custom.MJStrokeTextView
            android:id="@+id/top_feng_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="北"
            android:textSize="14sp"
            android:textColor="#FFFFFF"
            android:gravity="center"
            android:rotation="180"
            app:mj_stroke_color="#000000"
            app:mj_stroke_width="3dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@+id/top_point_tv"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="20dp"
            android:layout_marginStart="2dp"/>
        <TextView
            android:id="@+id/top_point_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="+0"
            android:textSize="12sp"
            android:textColor="#FFFFFF"
            android:gravity="center"
            android:rotation="180"
            app:layout_constraintTop_toTopOf="@+id/top_feng_tv"
            app:layout_constraintBottom_toBottomOf="@+id/top_feng_tv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/top_feng_tv"
            app:layout_constraintHorizontal_chainStyle="packed"/>

        <LinearLayout
            android:id="@+id/left_point_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="50dp"
            android:orientation="horizontal"
            android:rotation="90"
            android:gravity="center_horizontal"
            android:clipChildren="false"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.wepie.module.mahjong.view.custom.MJStrokeTextView
                android:id="@+id/left_feng_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="東"
                android:textSize="14sp"
                android:textColor="#FFFFFF"
                android:gravity="center"
                app:mj_stroke_color="#000000"
                app:mj_stroke_width="3dp"
                android:includeFontPadding="false"/>
            <TextView
                android:id="@+id/left_point_tv"
                android:layout_width="wrap_content"
                android:layout_height="14dp"
                tools:text="+0"
                android:textSize="12sp"
                android:textColor="#FFFFFF"
                android:layout_marginStart="2dp"/>
        </LinearLayout>

        <com.wepie.module.mahjong.view.custom.MJStrokeTextView
            android:id="@+id/bottom_feng_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="南"
            android:textSize="14sp"
            android:textColor="#FFFFFF"
            android:gravity="center"
            app:mj_stroke_color="#000000"
            app:mj_stroke_width="3dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/bottom_point_tv"
            app:layout_constraintHorizontal_chainStyle="packed"
            android:layout_marginBottom="20dp"/>
        <TextView
            android:id="@+id/bottom_point_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="+0"
            android:textSize="12sp"
            android:textColor="#FFFFFF"
            android:gravity="center"
            app:layout_constraintTop_toTopOf="@+id/bottom_feng_tv"
            app:layout_constraintBottom_toBottomOf="@+id/bottom_feng_tv"
            app:layout_constraintStart_toEndOf="@+id/bottom_feng_tv"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="2dp"/>

        <LinearLayout
            android:id="@+id/right_point_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="50dp"
            android:rotation="270"
            android:orientation="horizontal"
            android:gravity="center_horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <com.wepie.module.mahjong.view.custom.MJStrokeTextView
                android:id="@+id/right_feng_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="西"
                android:textSize="14sp"
                android:textColor="#FFFFFF"
                app:mj_stroke_color="#000000"
                app:mj_stroke_width="3dp"
                android:gravity="center"/>

            <TextView
                android:id="@+id/right_point_tv"
                android:layout_width="wrap_content"
                android:layout_height="14dp"
                tools:text="+0"
                android:textSize="12sp"
                android:textColor="#FFFFFF"
                android:gravity="center"
                android:layout_marginStart="2dp"/>
        </LinearLayout>

        <TextView
            android:id="@+id/total_pai_num_tv"
            android:layout_width="58dp"
            android:layout_height="58dp"
            tools:text="餘36張"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="#91D4FF"
            android:gravity="center"
            android:background="@drawable/mj_table_center_num_bg"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>