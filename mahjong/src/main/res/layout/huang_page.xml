<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_settlement"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#BB000000">

    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/huang_anim_view"
        android:layout_width="450dp"
        android:layout_height="300dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <View
        android:id="@+id/top_result_bg"
        android:layout_width="60dp"
        android:layout_height="20dp"
        android:background="@drawable/mj_huang_ting_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="78dp"
        android:visibility="invisible"/>
    <ImageView
        android:id="@+id/top_result_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/mj_ting"
        app:layout_constraintStart_toStartOf="@+id/top_result_bg"
        app:layout_constraintEnd_toEndOf="@+id/top_result_bg"
        app:layout_constraintBottom_toBottomOf="@+id/top_result_bg"
        app:layout_constraintTop_toTopOf="@+id/top_result_bg"
        android:visibility="invisible"/>

    <View
        android:id="@+id/left_result_bg"
        android:layout_width="60dp"
        android:layout_height="20dp"
        android:background="@drawable/mj_huang_ting_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="161dp"
        android:layout_marginTop="148dp"
        android:visibility="invisible"/>
    <ImageView
        android:id="@+id/left_result_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/mj_ting"
        app:layout_constraintTop_toTopOf="@+id/left_result_bg"
        app:layout_constraintBottom_toBottomOf="@+id/left_result_bg"
        app:layout_constraintStart_toStartOf="@+id/left_result_bg"
        app:layout_constraintEnd_toEndOf="@+id/left_result_bg"
        android:visibility="invisible"/>

    <View
        android:id="@+id/bottom_result_bg"
        android:layout_width="60dp"
        android:layout_height="20dp"
        android:background="@drawable/mj_huang_ting_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="115dp"
        android:visibility="invisible"/>
    <ImageView
        android:id="@+id/bottom_result_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/mj_ting"
        app:layout_constraintTop_toTopOf="@+id/bottom_result_bg"
        app:layout_constraintBottom_toBottomOf="@+id/bottom_result_bg"
        app:layout_constraintStart_toStartOf="@+id/bottom_result_bg"
        app:layout_constraintEnd_toEndOf="@+id/bottom_result_bg"
        android:visibility="invisible"/>

    <View
        android:id="@+id/right_result_bg"
        android:layout_width="60dp"
        android:layout_height="20dp"
        android:background="@drawable/mj_huang_ting_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="148dp"
        android:layout_marginEnd="161dp"
        android:visibility="invisible"/>
    <ImageView
        android:id="@+id/right_result_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/mj_ting"
        app:layout_constraintTop_toTopOf="@+id/right_result_bg"
        app:layout_constraintBottom_toBottomOf="@+id/right_result_bg"
        app:layout_constraintStart_toStartOf="@+id/right_result_bg"
        app:layout_constraintEnd_toEndOf="@+id/right_result_bg"
        android:visibility="invisible"/>

    <com.wepie.module.mahjong.view.card.MJFrameLayout
        android:id="@+id/top_hu_pai_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/top_result_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="3dp"/>

    <com.wepie.module.mahjong.view.card.MJFrameLayout
        android:id="@+id/left_hu_pai_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/left_result_bg"
        app:layout_constraintStart_toStartOf="@+id/left_result_bg"
        app:layout_constraintEnd_toEndOf="@+id/left_result_bg"
        android:layout_marginTop="3dp"/>

    <com.wepie.module.mahjong.view.card.MJFrameLayout
        android:id="@+id/bottom_hu_pai_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/bottom_result_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="3dp"/>

    <com.wepie.module.mahjong.view.card.MJFrameLayout
        android:id="@+id/right_hu_pai_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/right_result_bg"
        app:layout_constraintLeft_toLeftOf="@+id/right_result_bg"
        app:layout_constraintRight_toRightOf="@+id/right_result_bg"
        android:layout_marginTop="3dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>