<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root"
        android:layout_width="@dimen/dp250"
        android:layout_height="@dimen/dp280"
        android:background="@drawable/model_joy"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:id="@+id/require_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dp108">

            <TextView
                android:id="@+id/require_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="@string/require_txt"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/coin_icon"
                android:layout_width="@dimen/dp18"
                android:layout_height="@dimen/dp18"
                android:layout_marginLeft="@dimen/dp6"
                android:src="@drawable/mj_money_coin" />

            <TextView
                android:id="@+id/require_money"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/dp5"
                android:includeFontPadding="false"
                android:textColor="#FEE27D"
                android:textSize="14sp"
                tool:text="30" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/di_tai_layout"
            android:layout_width="@dimen/dp110"
            android:layout_height="@dimen/dp46"
            android:layout_centerHorizontal="true"
            android:background="@drawable/di_tai_bg"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@+id/require_layout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dp16">

            <TextView
                android:id="@+id/di"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp3"
                android:includeFontPadding="false"
                android:textColor="#FFFC81"
                android:textSize="14sp"
                tool:text="底：10" />

            <TextView
                android:id="@+id/tai"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp3"
                android:includeFontPadding="false"
                android:textColor="#FFFC81"
                android:textSize="14sp"
                tool:text="台：5" />

        </LinearLayout>

        <TextView
            android:id="@+id/gold_cards"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp10"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            android:text="@string/mj_play_model_gold_cards"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintTop_toBottomOf="@+id/di_tai_layout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/circle"
            android:layout_marginEnd="@dimen/dp8"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/circle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp10"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            tool:text="1/4圈"
            app:layout_constraintTop_toBottomOf="@+id/di_tai_layout"
            app:layout_constraintStart_toEndOf="@+id/gold_cards"
            app:layout_constraintEnd_toStartOf="@+id/change_cards"/>

        <TextView
            android:id="@+id/change_cards"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/dp10"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            android:text="@string/mj_play_model_change_cards"
            app:layout_constraintTop_toBottomOf="@+id/di_tai_layout"
            app:layout_constraintStart_toEndOf="@+id/circle"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="@dimen/dp8"
            android:visibility="gone"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>