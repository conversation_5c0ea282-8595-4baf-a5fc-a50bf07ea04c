<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="58dp"
    android:background="@drawable/mj_bg_black_press"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingTop="4dp">

    <ImageView
        android:id="@+id/face_item_view_pre_iv"
        android:layout_width="36dp"
        android:layout_height="36dp" />

    <com.wepie.module.mahjong.view.custom.MJMarqueeTextView
        android:id="@+id/face_item_view_name_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:singleLine="true"
        android:marqueeRepeatLimit="marquee_forever"
        android:ellipsize="marquee"
        android:layout_marginTop="2dp"
        android:gravity="center"
        android:textColor="#fff"
        android:textSize="10dp"
        tools:text="骰子" />

    <TextView
        android:id="@+id/face_item_view_price_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"/>
</LinearLayout>