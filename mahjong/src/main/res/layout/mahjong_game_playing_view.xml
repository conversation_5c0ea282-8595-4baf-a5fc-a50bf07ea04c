<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:background="@drawable/mahjong_table_bg"
    android:clipChildren="false">

    <!--  牌桌中心各用户点数显示  -->
    <include layout="@layout/mahjong_game_playing_center_layout"/>

    <!--  对家的牌面展示区  -->
    <include layout="@layout/mahjong_game_playing_top_layout"/>

    <!--  上家的牌面展示区  -->
    <include layout="@layout/mahjong_game_playing_left_layout"/>

    <!--  下家的牌面展示区  -->
    <include layout="@layout/mahjong_game_playing_right_layout"/>

    <!--  自己的牌面展示区  -->
    <include layout="@layout/mahjong_game_playing_self_layout"/>

    <!--  出牌区指示箭头  -->
    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/mj_pai_indicate_view"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:visibility="gone"/>

    <!--  动画布局容器  -->
    <FrameLayout
        android:id="@+id/mahjong_anim_layout"
        android:layout_width="85dp"
        android:layout_height="115dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:background="@drawable/mj_pai_light_bg"
        android:visibility="gone"/>

    <FrameLayout
        android:id="@+id/mj_anim_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:visibility="gone"/>
</androidx.constraintlayout.widget.ConstraintLayout>