<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mj_bankrupt_dialog_bg"
        android:layout_width="276dp"
        android:layout_height="wrap_content"
        android:minHeight="170dp"
        android:background="@drawable/mj_hint_dialog_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">
        <TextView
            android:id="@+id/mj_bankrupt_tips_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/mj_bankrupt_tips"
            android:textColor="#2D2D2D"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="18dp"
            android:layout_marginTop="28dp"
            android:layout_marginEnd="18dp"/>

        <TextView
            android:id="@+id/mj_bankrupt_back"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:background="@drawable/mj_bankrupt_dialog_back_bg"
            android:gravity="center"
            android:text="@string/mj_bankrupt_back"
            android:textColor="#DB7D28"
            android:textSize="16sp"
            android:paddingStart="@dimen/dp15"
            android:paddingEnd="@dimen/dp15"
            app:layout_constraintTop_toBottomOf="@+id/mj_bankrupt_tips_tv"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/mj_bankrupt_charge"
            android:layout_marginTop="25dp"
            android:layout_marginBottom="18dp"/>

        <TextView
            android:id="@+id/mj_bankrupt_charge"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:background="@drawable/invite_btn_bg"
            android:gravity="center"
            android:text="@string/mj_bankrupt_charge"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:paddingStart="@dimen/dp15"
            android:paddingEnd="@dimen/dp15"
            app:layout_constraintTop_toBottomOf="@+id/mj_bankrupt_tips_tv"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/mj_bankrupt_back"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="25dp"
            android:layout_marginBottom="18dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>