<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    tools:background="@drawable/mahjong_table_bg">

    <!--  准备阶段上方玩家信息  -->
    <com.wepie.module.mahjong.view.MJPlayerView
        android:id="@+id/top_player_in_ready"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:layout_marginEnd="202dp"
        android:clipChildren="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/top_message_arrow"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:src="@drawable/mj_chat_message_bubble_arrow"
        android:rotation="180"
        app:layout_constraintTop_toTopOf="@+id/top_player_in_ready"
        app:layout_constraintEnd_toStartOf="@+id/top_player_in_ready"
        android:layout_marginTop="8dp"
        android:visibility="gone"/>
    <FrameLayout
        android:id="@+id/top_message_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="32dp"
        android:background="@drawable/mj_chat_message_bubble"
        app:layout_constraintTop_toTopOf="@+id/top_message_arrow"
        app:layout_constraintEnd_toStartOf="@+id/top_message_arrow"
        android:visibility="gone">
        <TextView
            android:id="@+id/top_message_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="170dp"
            android:textColor="#9C592D"
            android:textSize="14sp"
            tools:text="快点啦，别磨磨蹭蹭的～"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:paddingStart="7dp"
            android:paddingEnd="6dp"
            android:visibility="gone"/>

        <com.wepie.module.mahjong.userface.MJUserFaceView
            android:id="@+id/top_face_view"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="6dp"
            android:layout_marginStart="6dp"
            android:layout_marginEnd="6dp"
            android:visibility="gone"/>
    </FrameLayout>

    <!--  准备阶段左方玩家信息  -->
    <com.wepie.module.mahjong.view.MJPlayerView
        android:id="@+id/left_player_in_ready"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp33"
        android:layout_marginTop="80dp"
        android:clipChildren="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/left_message_arrow"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:src="@drawable/mj_chat_message_bubble_arrow"
        app:layout_constraintTop_toTopOf="@+id/left_player_in_ready"
        app:layout_constraintStart_toEndOf="@+id/left_player_in_ready"
        android:layout_marginTop="8dp"
        android:visibility="gone"/>
    <FrameLayout
        android:id="@+id/left_message_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="32dp"
        android:background="@drawable/mj_chat_message_bubble"
        app:layout_constraintTop_toTopOf="@+id/left_message_arrow"
        app:layout_constraintStart_toEndOf="@+id/left_message_arrow"
        android:visibility="gone">
        <TextView
            android:id="@+id/left_message_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="170dp"
            android:textColor="#9C592D"
            android:textSize="14sp"
            tools:text="快点啦，别磨磨蹭蹭的～"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:paddingStart="7dp"
            android:paddingEnd="8dp"
            android:visibility="gone"/>

        <com.wepie.module.mahjong.userface.MJUserFaceView
            android:id="@+id/left_face_view"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="6dp"
            android:layout_marginStart="6dp"
            android:layout_marginEnd="6dp"
            android:visibility="gone"/>
    </FrameLayout>

    <!--  准备阶段下方玩家信息  -->
    <com.wepie.module.mahjong.view.MJPlayerView
        android:id="@+id/bottom_player_in_ready"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp33"
        android:layout_marginBottom="82dp"
        android:clipChildren="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:id="@+id/bottom_message_arrow"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:src="@drawable/mj_chat_message_bubble_arrow"
        app:layout_constraintTop_toTopOf="@+id/bottom_player_in_ready"
        app:layout_constraintStart_toEndOf="@+id/bottom_player_in_ready"
        android:layout_marginTop="8dp"
        android:visibility="gone"/>
    <FrameLayout
        android:id="@+id/bottom_message_lay"
        android:layout_width="wrap_content"
        android:maxWidth="170dp"
        android:layout_height="wrap_content"
        android:minHeight="32dp"
        android:background="@drawable/mj_chat_message_bubble"
        app:layout_constraintTop_toTopOf="@+id/bottom_message_arrow"
        app:layout_constraintStart_toEndOf="@+id/bottom_message_arrow"
        android:visibility="gone">
        <TextView
            android:id="@+id/bottom_message_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="200dp"
            android:textColor="#9C592D"
            android:textSize="14sp"
            tools:text="快点啦，别磨磨蹭蹭的～"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:paddingStart="7dp"
            android:paddingEnd="6dp"
            android:visibility="gone"/>

        <com.wepie.module.mahjong.userface.MJUserFaceView
            android:id="@+id/bottom_face_view"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="6dp"
            android:layout_marginStart="6dp"
            android:layout_marginEnd="6dp"
            android:visibility="gone"/>
    </FrameLayout>
    <!--  准备阶段右方玩家信息  -->
    <com.wepie.module.mahjong.view.MJPlayerView
        android:id="@+id/right_player_in_ready"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="80dp"
        android:layout_marginEnd="50dp"
        android:clipChildren="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/right_message_arrow"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:rotation="180"
        android:src="@drawable/mj_chat_message_bubble_arrow"
        app:layout_constraintTop_toTopOf="@+id/right_player_in_ready"
        app:layout_constraintEnd_toStartOf="@+id/right_player_in_ready"
        android:layout_marginTop="8dp"
        android:visibility="gone"/>
    <FrameLayout
        android:id="@+id/right_message_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="32dp"
        android:background="@drawable/mj_chat_message_bubble"
        app:layout_constraintTop_toTopOf="@+id/right_message_arrow"
        app:layout_constraintEnd_toStartOf="@+id/right_message_arrow"
        android:visibility="gone">
        <TextView
            android:id="@+id/right_message_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="170dp"
            android:textColor="#9C592D"
            android:textSize="14sp"
            tools:text="快点啦，别磨磨蹭蹭的～"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:paddingStart="7dp"
            android:paddingEnd="6dp"
            android:visibility="gone"/>

        <com.wepie.module.mahjong.userface.MJUserFaceView
            android:id="@+id/right_face_view"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="6dp"
            android:layout_marginStart="6dp"
            android:layout_marginEnd="6dp"
            android:visibility="gone"/>
    </FrameLayout>

    <!--  准备阶段换房按钮  -->
    <com.wepie.module.mahjong.view.custom.MJPressTextView
        android:id="@+id/change_room_btn"
        android:layout_width="wrap_content"
        android:layout_height="56dp"
        android:background="@drawable/mahjong_change_room"
        android:gravity="center"
        android:minWidth="103dp"
        android:text=""
        android:textColor="#ffffff"
        android:textSize="22sp"
        android:visibility="gone"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/invite_player_btn"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="36dp"/>
    <!--  准备阶段邀请按钮  -->
    <com.wepie.module.mahjong.view.custom.MJPressTextView
        android:id="@+id/invite_player_btn"
        android:layout_width="wrap_content"
        android:layout_height="56dp"
        android:gravity="center"
        android:minWidth="103dp"
        android:text=""
        android:textColor="#ffffff"
        android:textSize="22sp"
        app:layout_constraintStart_toEndOf="@+id/change_room_btn"
        app:layout_constraintEnd_toStartOf="@+id/ready_btn"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="36dp"/>
    <!--  准备阶段准备/已准备按钮  -->
    <TextView
        android:id="@+id/ready_btn"
        android:layout_width="wrap_content"
        android:layout_height="56dp"
        android:layout_marginBottom="36dp"
        android:gravity="center"
        android:minWidth="103dp"
        android:text=""
        android:textColor="#ffffff"
        android:textSize="22sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/invite_player_btn"
        app:layout_constraintEnd_toEndOf="parent"/>

    <!--  准备阶段上方玩家准备状态  -->
    <TextView
        android:id="@+id/top_player_ready_status_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/mahjong_already_icon"
        android:drawablePadding="3dp"
        android:text="@string/mahjong_already"
        android:textColor="#9CE167"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@+id/top_player_in_ready"
        app:layout_constraintTop_toBottomOf="@+id/top_player_in_ready" />
    <!--  准备阶段左方玩家准备状态  -->
    <TextView
        android:id="@+id/left_player_ready_status_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/mahjong_already_icon"
        android:drawablePadding="3dp"
        android:text="@string/mahjong_already"
        android:textColor="#9CE167"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@+id/left_player_in_ready"
        app:layout_constraintTop_toBottomOf="@+id/left_player_in_ready" />
    <!--  准备阶段下方玩家准备状态  -->
    <TextView
        android:id="@+id/bottom_player_ready_status_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/mahjong_already_icon"
        android:drawablePadding="3dp"
        android:text="@string/mahjong_already"
        android:textColor="#9CE167"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/bottom_player_in_ready"
        app:layout_constraintStart_toStartOf="@+id/bottom_player_in_ready" />
    <!--  准备阶段右方玩家准备状态  -->
    <TextView
        android:id="@+id/right_player_ready_status_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/mahjong_already_icon"
        android:drawablePadding="3dp"
        android:text="@string/mahjong_already"
        android:textColor="#9CE167"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@+id/right_player_in_ready"
        app:layout_constraintTop_toBottomOf="@+id/right_player_in_ready" />

    <!--  匹配房观众视角提示  -->
    <FrameLayout
        android:id="@+id/mj_match_room_visitor_tips_lay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/mj_match_room_visitor_tips_lay_bg"
        android:visibility="gone">

        <TextView
            android:id="@+id/mj_visitor_watching"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:text="@string/mj_match_visitor_watching_text"
            android:drawableStart="@drawable/mj_visitor_icon_white_middle"
            android:drawablePadding="@dimen/dp6"
            android:layout_gravity="center|bottom"
            android:layout_marginBottom="82dp"/>

        <TextView
            android:id="@+id/mj_visitor_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="#999CB4"
            android:text="@string/mj_match_visitor_watching_tips"
            android:layout_gravity="center|bottom"
            android:layout_marginBottom="57dp"/>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>