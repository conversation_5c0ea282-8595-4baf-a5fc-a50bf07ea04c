<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@drawable/mahjong_table_bg"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <!--  自己用户信息  -->
    <com.wepie.module.mahjong.view.MJPlayerView
        android:id="@+id/bottom_player_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="98dp"
        android:layout_marginStart="@dimen/dp33"
        android:visibility="gone"
        tools:visibility="visible"/>

    <include
        android:id="@+id/bottom_player_bank_info"
        layout="@layout/mj_zhuang_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/bottom_player_info"
        app:layout_constraintStart_toStartOf="@+id/bottom_player_info"
        app:layout_constraintTop_toBottomOf="@+id/bottom_player_info" />

    <Space
        android:id="@+id/bottom_guide_line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        app:layout_constraintBottom_toBottomOf="@+id/center_bg_iv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="18dp" />

    <!--  自己的出牌区  -->
    <com.wepie.module.mahjong.view.card.MJFrameLayout
        android:id="@+id/bottom_chu_pai_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bottom_guide_line"/>

    <!--  自己的花牌区  -->
    <com.wepie.module.mahjong.view.card.MJFrameLayout
        android:id="@+id/bottom_hua_pai_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@+id/bottom_shou_pai_layout"
        app:layout_constraintStart_toEndOf="@+id/bottom_player_info"
        android:layout_marginStart="28dp"/>

    <!--  自己手牌区  -->
    <FrameLayout
        android:id="@+id/bottom_shou_pai_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:clipChildren="false"/>

    <!--  自己明牌区  -->
    <com.wepie.module.mahjong.view.card.MJFrameLayout
        android:id="@+id/bottom_ming_pai_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/bottom_shou_pai_layout"
        app:layout_constraintBottom_toBottomOf="@+id/bottom_shou_pai_layout"
        app:layout_constraintStart_toStartOf="@+id/bottom_shou_pai_layout"/>

    <!--  风位牌显示区  -->
    <FrameLayout
        android:id="@+id/bottom_feng_layout"
        android:layout_width="85dp"
        android:layout_height="115dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="17dp"
        android:visibility="gone"/>

    <FrameLayout
        android:id="@+id/mo_pai_svga_anim"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:visibility="gone"/>
    <!--  以下为用户自己的操作区域  -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/option_vertical_line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_percent="0.68"
        android:orientation="vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <!--  吃、碰、杠、听、胡、过 等操作按钮显示区域  -->
    <LinearLayout
        android:id="@+id/bottom_option_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="bottom"
        app:layout_constraintBottom_toTopOf="@+id/bottom_shou_pai_layout"
        app:layout_constraintEnd_toStartOf="@+id/option_vertical_line"
        android:layout_marginBottom="8dp"
        android:clipChildren="false"
        android:visibility="gone">

        <FrameLayout
            android:id="@+id/option_chi_lay"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:clipChildren="false">
            <ImageView
                android:id="@+id/option_chi_iv"
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:src="@drawable/mj_option_chi"
                android:layout_gravity="center"/>
            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/option_chi_bg"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:layout_gravity="center"/>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/option_peng_lay"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:clipChildren="false"
            android:layout_marginStart="16dp">
            <ImageView
                android:id="@+id/option_peng_iv"
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:src="@drawable/mj_option_peng"
                android:layout_gravity="center"/>
            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/option_peng_bg"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:layout_gravity="center"/>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/option_gang_lay"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:clipChildren="false"
            android:layout_marginStart="16dp">
            <ImageView
                android:id="@+id/option_gang_iv"
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:src="@drawable/mj_option_gang"/>
            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/option_gang_bg"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:layout_gravity="center"/>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/option_ting_lay"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:clipChildren="false"
            android:layout_marginStart="16dp">
            <ImageView
                android:id="@+id/option_ting_iv"
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:src="@drawable/mj_option_ting"/>
            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/option_ting_bg"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:layout_gravity="center"/>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/option_hu_lay"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:clipChildren="false"
            android:layout_marginStart="16dp">
            <ImageView
                android:id="@+id/option_hu_iv"
                android:layout_width="52dp"
                android:layout_height="52dp"
                android:src="@drawable/mj_option_hu"/>
            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/option_hu_bg"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:layout_gravity="center"/>
        </FrameLayout>

        <ImageView
            android:id="@+id/option_pass_iv"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:src="@drawable/mj_option_pass"
            android:layout_marginStart="16dp"/>
    </LinearLayout>
    <!--  听牌取消按钮-->
    <ImageView
        android:id="@+id/option_cancle_iv"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:src="@drawable/mj_option_cancle"
        app:layout_constraintBottom_toTopOf="@+id/bottom_shou_pai_layout"
        app:layout_constraintEnd_toStartOf="@+id/option_vertical_line"
        android:layout_marginBottom="8dp"
        android:visibility="gone"/>

    <!--  吃牌可选序列展示区域  -->
    <FrameLayout
        android:id="@+id/bottom_seq_tips_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/mj_pai_tips_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/bottom_shou_pai_layout"
        android:layout_marginBottom="8dp"
        android:visibility="gone"/>

    <Space
        android:id="@+id/pai_seq_tips_line"
        android:layout_width="1dp"
        android:layout_height="1dp"
        app:layout_constraintTop_toTopOf="@+id/bottom_seq_tips_layout"
        app:layout_constraintEnd_toEndOf="@+id/bottom_seq_tips_layout"
        android:layout_marginTop="15dp"
        android:layout_marginEnd="15dp"/>

    <!--  吃牌可选序列收起按钮  -->
    <ImageView
        android:id="@+id/bottom_chi_back_iv"
        android:layout_width="21dp"
        android:layout_height="23dp"
        android:src="@drawable/mj_chi_back_pic"
        app:layout_constraintBottom_toTopOf="@+id/pai_seq_tips_line"
        app:layout_constraintStart_toEndOf="@+id/pai_seq_tips_line"
        android:visibility="gone"/>

    <!--  吃碰杠胡时提示动画  -->
    <FrameLayout
        android:id="@+id/bottom_anim_layout"
        android:layout_width="185dp"
        android:layout_height="80dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="101dp"
        android:layout_marginEnd="167dp"
        android:visibility="gone"/>

    <!--  听牌状态标示  -->
    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/bottom_ting_iv"
        android:layout_width="34dp"
        android:layout_height="64dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="60dp"
        android:visibility="gone"/>

    <!--  消息显示区  -->
    <ImageView
        android:id="@+id/bottom_message_arrow"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:src="@drawable/mj_chat_message_bubble_arrow"
        app:layout_constraintTop_toTopOf="@+id/bottom_player_info"
        app:layout_constraintStart_toEndOf="@+id/bottom_player_info"
        android:layout_marginTop="8dp"
        android:visibility="gone"/>
    <FrameLayout
        android:id="@+id/bottom_message_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="32dp"
        android:background="@drawable/mj_chat_message_bubble"
        app:layout_constraintTop_toTopOf="@+id/bottom_message_arrow"
        app:layout_constraintStart_toEndOf="@+id/bottom_message_arrow"
        android:visibility="gone">
        <TextView
            android:id="@+id/bottom_message_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="170dp"
            android:textColor="#9C592D"
            android:textSize="14sp"
            tools:text="快点啦，别磨磨蹭蹭的～"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:paddingStart="7dp"
            android:paddingEnd="8dp"
            android:visibility="gone"/>

        <com.wepie.module.mahjong.userface.MJUserFaceView
            android:id="@+id/bottom_face_view"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="6dp"
            android:layout_marginStart="6dp"
            android:layout_marginEnd="6dp"
            android:visibility="gone"/>
    </FrameLayout>

    <!--  换三张交换按钮布局  -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mj_change_option_lay"
        android:layout_width="482dp"
        android:layout_height="120dp"
        android:background="@drawable/mj_change_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="75dp"
        android:visibility="gone">

        <ImageView
            android:id="@+id/mj_change_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/mj_change_btn_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="29dp"/>

        <TextView
            android:id="@+id/mj_change_tips_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFFFFF"
            android:textSize="11sp"
            android:text="@string/mj_change_card_tips"
            app:layout_constraintTop_toBottomOf="@+id/mj_change_btn"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <!--  围观观众/陪玩再换三张时看到的布局  -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mj_change_visitor_lay"
        android:layout_width="match_parent"
        android:layout_height="206dp"
        android:background="@drawable/mj_change_visitor_lay_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone">

        <TextView
            android:id="@+id/mj_change_visitor_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:text="@string/mj_change_visitor_tips"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginBottom="40dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--  操作倒计时  -->
    <com.wepie.module.mahjong.view.MJGradientColorTextView
        android:id="@+id/option_time_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textSize="60sp"
        tools:text="9"
        android:textColor="#FFFFFF"
        android:includeFontPadding="false"
        android:layout_marginStart="16dp"
        app:layout_constraintStart_toEndOf="@+id/option_vertical_line"
        app:layout_constraintBottom_toTopOf="@+id/bottom_shou_pai_layout"
        android:layout_marginBottom="8dp"
        android:visibility="gone"/>

    <!--  操作倒计时（额外时间）  -->
    <com.wepie.module.mahjong.view.MJGradientColorTextView
        android:id="@+id/option_extra_time_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        tools:text="+20"
        android:textSize="32sp"
        android:textColor="#FFFFFF"
        android:includeFontPadding="false"
        app:layout_constraintStart_toEndOf="@+id/option_time_tv"
        app:layout_constraintBottom_toTopOf="@+id/bottom_shou_pai_layout"
        android:layout_marginBottom="8dp"
        android:visibility="gone"/>
</merge>