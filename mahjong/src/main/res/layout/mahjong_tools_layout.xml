<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:background="@drawable/mahjong_table_bg">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/mj_room_info_vp"
        android:layout_width="180dp"
        android:layout_height="wrap_content"
        tools:layout_height="60dp"
        android:clipChildren="false"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="12dp"/>

    <com.huiwan.widget.IndicatorView
        android:id="@+id/mj_room_info_indicator"
        android:layout_width="180dp"
        android:layout_height="@dimen/dp14"
        app:dot_color_empty="#88FFFFFF"
        app:dot_color_select="#FFFFFF"
        app:layout_constraintTop_toBottomOf="@+id/mj_room_info_vp"
        app:layout_constraintStart_toStartOf="@+id/mj_room_info_vp"
        app:layout_constraintEnd_toEndOf="@+id/mj_room_info_vp"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/setting_iv"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:src="@drawable/mahjong_settting_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="24dp"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/voice_info_layout"
        android:layout_width="61dp"
        android:layout_height="wrap_content"
        android:background="@drawable/mahjong_voice_lay_bg"
        android:orientation="horizontal"
        app:layout_constraintEnd_toStartOf="@+id/setting_iv"
        app:layout_constraintTop_toTopOf="@+id/setting_iv"
        android:layout_marginEnd="8dp">

        <View
            android:id="@+id/voice_info_line"
            android:layout_width="1dp"
            android:layout_height="16dp"
            android:background="#33FFFFFF"
            app:layout_constraintTop_toTopOf="@+id/sound_iv"
            app:layout_constraintBottom_toBottomOf="@+id/sound_iv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <ImageView
            android:id="@+id/sound_iv"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/mahjong_sound_enable"
            android:scaleType="centerInside"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/mic_iv"/>

        <ImageView
            android:id="@+id/mic_iv"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/mahjong_mic_enable"
            android:scaleType="centerInside"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@+id/sound_iv"
            app:layout_constraintEnd_toEndOf="parent"/>

        <com.wepie.module.mahjong.view.custom.MJSwitchView
            android:id="@+id/mj_channel_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/sound_iv"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:visibility="gone"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/mj_channel_name_for_mate_tv"
        android:layout_width="42dp"
        android:layout_height="14dp"
        android:text="@string/mj_play_public_channel"
        android:textSize="8sp"
        android:textColor="@color/white"
        android:gravity="center"
        android:background="@drawable/mj_voice_channel_public_bg_for_mate"
        app:layout_constraintTop_toBottomOf="@+id/voice_info_layout"
        app:layout_constraintStart_toStartOf="@+id/voice_info_layout"
        app:layout_constraintEnd_toEndOf="@+id/voice_info_layout"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/send_message_iv"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:src="@drawable/mahjong_send_msg_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="85dp"
        android:layout_marginEnd="24dp"/>

    <ImageView
        android:id="@+id/send_emoticon_iv"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:src="@drawable/mahjong_send_emoticon_icon"
        app:layout_constraintBottom_toTopOf="@+id/send_message_iv"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="6dp"
        android:layout_marginEnd="24dp"/>

    <ImageView
        android:id="@+id/tips_iv"
        android:layout_width="30dp"
        android:layout_height="39dp"
        android:src="@drawable/mahjong_tips_icon"
        app:layout_constraintBottom_toTopOf="@+id/send_emoticon_iv"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="6dp"
        android:layout_marginEnd="27dp"
        android:visibility="gone"/>

    <View
        android:id="@+id/bottom_mask_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"/>

    <com.wepie.module.mahjong.userface.choose.MJUserFaceChooseView
        android:id="@+id/face_choose_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:background="#000000"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ting_pai_seq_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="94dp"
        android:visibility="gone">
        <FrameLayout
            android:id="@+id/seq_pai_tips_layout"
            android:layout_width="290dp"
            android:layout_height="90dp"
            android:background="@drawable/mj_pai_tips_bg"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/guoshui_tips_tv"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:text="@string/mj_guoshui_status"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:gravity="center"
            android:background="@drawable/guoshui_tips_shadow"
            app:layout_constraintTop_toTopOf="@+id/seq_pai_tips_layout"
            app:layout_constraintBottom_toBottomOf="@+id/seq_pai_tips_layout"
            app:layout_constraintStart_toStartOf="@+id/seq_pai_tips_layout"
            app:layout_constraintEnd_toEndOf="@+id/seq_pai_tips_layout"
            android:visibility="gone"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/mj_self_trusteeship_lay"
        android:layout_width="match_parent"
        android:layout_height="206dp"
        android:background="@drawable/mj_self_trusteeship_lay_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone">

        <ImageView
            android:id="@+id/mj_cancel_iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/mj_trusteeship_cancel"
            android:layout_gravity="center_horizontal|bottom"
            android:layout_marginBottom="67dp"/>

        <TextView
            android:id="@+id/mj_self_trusteeship_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="11sp"
            android:textColor="#ECF3F9"
            android:text="@string/mj_trusteeship_tips"
            android:layout_gravity="center_horizontal|bottom"
            android:layout_marginBottom="34dp"/>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>