<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false">

    <RelativeLayout
        android:id="@+id/head_layout"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_marginTop="5dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.wepie.module.mahjong.view.custom.MJSpeakerAnimView
            android:id="@+id/speaker_anim_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"/>

        <com.huiwan.decorate.DecorHeadImgView
            android:id="@+id/player_head_view"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:layout_centerInParent="true"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:padding="1dp"/>

        <ImageView
            android:id="@+id/unmanaged"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:layout_centerInParent="true"
            android:visibility="gone"
            android:src="@drawable/unmanaged" />
    </RelativeLayout>

    <ImageView
        android:id="@+id/player_mute_icon"
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:src="@drawable/mj_dialog_mute_icon"
        app:layout_constraintTop_toTopOf="@+id/head_layout"
        app:layout_constraintEnd_toEndOf="@+id/head_layout"
        android:layout_marginTop="1dp"
        android:layout_marginEnd="1dp"
        android:visibility="gone"/>

    <com.wepie.module.mahjong.userface.MJUserFaceView
        android:id="@+id/player_face_view"
        android:layout_width="42dp"
        android:layout_height="42dp"
        app:layout_constraintTop_toTopOf="@+id/head_layout"
        app:layout_constraintBottom_toBottomOf="@+id/head_layout"
        app:layout_constraintStart_toStartOf="@+id/head_layout"
        app:layout_constraintEnd_toEndOf="@+id/head_layout"/>

    <com.huiwan.decorate.NameTextView
        android:id="@+id/player_name_view"
        android:layout_width="76dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:gravity="center_horizontal"
        android:textColor="#ffffff"
        android:textSize="11sp"
        app:layout_constraintEnd_toEndOf="@+id/head_layout"
        app:layout_constraintStart_toStartOf="@+id/head_layout"
        app:layout_constraintTop_toBottomOf="@+id/head_layout" />

    <com.huiwan.decorate.DecorHeadImgView
        android:id="@+id/player_mate_head_view"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:padding="1dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/head_layout"
        android:layout_marginEnd="29dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>