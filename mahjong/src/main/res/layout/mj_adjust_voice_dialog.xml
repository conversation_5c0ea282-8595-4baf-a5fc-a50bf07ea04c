<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="276dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingBottom="16dp"
    android:paddingTop="8dp"
    android:background="@drawable/mj_voice_adjust_dialog_bg">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:text="@string/ice_ball_adjust_voice_dialog_1"
        android:textStyle="bold"
        android:textColor="#555454"
        android:textSize="18sp" />

    <RelativeLayout
        android:id="@+id/bgm_voc_lay"
        android:layout_width="wrap_content"
        android:layout_height="25dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp15">

        <TextView
            android:id="@+id/bgm_voc_text"
            android:layout_width="90dp"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginEnd="10dp"
            android:text="@string/mj_voice_adjust_bgm"
            android:textColor="#555454"
            android:textSize="14sp" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@id/bgm_voc_text">

            <com.huiwan.widget.TakeInProgressBar
                android:id="@+id/bgm_voc_bar"
                android:layout_width="120dp"
                android:layout_height="8dp"
                android:layout_centerVertical="true" />

            <ImageView
                android:id="@+id/bgm_voc_slide"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/mj_voice_circle_indicator" />
        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/game_voc_lay"
        android:layout_width="wrap_content"
        android:layout_height="25dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp16">

        <TextView
            android:id="@+id/game_voc_text"
            android:layout_width="90dp"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/ice_ball_adjust_voice_dialog_2"
            android:textColor="#555454"
            android:textSize="14sp"
            android:layout_marginEnd="10dp" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@id/game_voc_text">

            <com.huiwan.widget.TakeInProgressBar
                android:id="@+id/game_voc_bar"
                android:layout_width="120dp"
                android:layout_height="8dp"
                android:layout_centerVertical="true" />

            <ImageView
                android:id="@+id/game_voc_slide"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/mj_voice_circle_indicator" />
        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/chat_voc_lay"
        android:layout_width="wrap_content"
        android:layout_height="25dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp16">

        <TextView
            android:id="@+id/chat_voc_text"
            android:layout_width="90dp"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:text="@string/ice_ball_adjust_voice_dialog_3"
            android:textColor="#555454"
            android:textSize="14sp"
            android:layout_marginEnd="10dp" />

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/chat_voc_text">

            <com.huiwan.widget.TakeInProgressBar
                android:id="@+id/chat_voc_bar"
                android:layout_width="120dp"
                android:layout_height="8dp"
                android:layout_centerVertical="true" />

            <ImageView
                android:id="@+id/chat_voc_slide"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/mj_voice_circle_indicator"/>
        </RelativeLayout>
    </RelativeLayout>

    <TextView
        android:id="@+id/dialog_enter"
        android:layout_width="210dp"
        android:layout_height="42dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="23dp"
        android:background="@drawable/mj_voice_indicator"
        android:gravity="center"
        android:text="@string/ice_ball_adjust_voice_dialog_4"
        android:textColor="#ffffff"
        android:textSize="16sp" />

</LinearLayout>