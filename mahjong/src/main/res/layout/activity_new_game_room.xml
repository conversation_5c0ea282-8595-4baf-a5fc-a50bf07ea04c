<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/game_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    </FrameLayout>

    <com.huiwan.component.gift.show.GiftContentView
        android:id="@+id/gift_content_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.huiwan.barrage.BarrageAnimView
        android:id="@+id/game_barrage_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</androidx.constraintlayout.widget.ConstraintLayout>