<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.huiwan.widget.CustomCircleImageView
        android:id="@+id/player_head_view"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:background="@drawable/shape_head_circle_bg"
        android:padding="2dp"
        android:src="@drawable/default_head_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <ImageView
        android:id="@+id/over_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/mj_qiang"
        app:layout_constraintTop_toTopOf="@+id/player_head_view"
        app:layout_constraintBottom_toBottomOf="@+id/player_head_view"
        app:layout_constraintStart_toStartOf="@+id/player_head_view"
        app:layout_constraintEnd_toEndOf="@+id/player_head_view"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        android:visibility="gone"/>

    <com.huiwan.decorate.NameTextView
        android:id="@+id/player_name_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="陈一波"
        android:textColor="#FFFFFFFF"
        android:textSize="11sp"
        app:layout_constraintTop_toBottomOf="@+id/player_head_view"
        app:layout_constraintStart_toStartOf="@+id/player_head_view"
        app:layout_constraintEnd_toEndOf="@+id/player_head_view"/>

    <TextView
        android:id="@+id/zhuang_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_zhuang_bg"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        tools:text="莊x3"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        app:layout_constraintTop_toBottomOf="@+id/player_name_view"
        app:layout_constraintStart_toStartOf="@+id/player_name_view"
        app:layout_constraintEnd_toEndOf="@+id/player_name_view"
        android:layout_marginTop="2dp"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/rank_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/mj_rank_icon"
        app:layout_constraintTop_toTopOf="@+id/player_head_view"
        app:layout_constraintBottom_toTopOf="@+id/money_icon"
        app:layout_constraintStart_toEndOf="@+id/player_head_view"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_marginStart="5dp"/>

    <ImageView
        android:id="@+id/money_icon"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:src="@drawable/mj_money_coin"
        app:layout_constraintTop_toBottomOf="@+id/rank_icon"
        app:layout_constraintBottom_toBottomOf="@+id/player_head_view"
        app:layout_constraintStart_toStartOf="@+id/rank_icon"
        android:layout_marginTop="4dp"/>

    <TextView
        android:id="@+id/money_txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        tools:text="1700"
        android:textColor="#FEE893"
        android:textSize="11sp"
        app:layout_constraintTop_toTopOf="@+id/money_icon"
        app:layout_constraintBottom_toBottomOf="@+id/money_icon"
        app:layout_constraintStart_toEndOf="@+id/money_icon"
        android:layout_marginStart="2dp"/>

    <TextView
        android:id="@+id/point_txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        tools:text="-300"
        android:textColor="#FFFFFF"
        android:textSize="11sp"
        app:layout_constraintTop_toBottomOf="@+id/money_txt"
        app:layout_constraintStart_toStartOf="@+id/money_txt"/>

</androidx.constraintlayout.widget.ConstraintLayout>