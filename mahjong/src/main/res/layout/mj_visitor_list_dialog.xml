<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <View
        android:id="@+id/mj_visitor_list_bg"
        android:layout_width="276dp"
        android:layout_height="250dp"
        android:background="@drawable/mj_hint_dialog_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <Space
        android:id="@+id/close_guide_line"
        android:layout_width="1dp"
        android:layout_height="1dp"
        app:layout_constraintBottom_toTopOf="@+id/mj_visitor_list_bg"
        app:layout_constraintStart_toEndOf="@+id/mj_visitor_list_bg"
        android:layout_marginBottom="5dp"
        android:layout_marginStart="8dp"/>

    <ImageView
        android:id="@+id/mj_visitor_dialog_close"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:src="@drawable/yellow_close"
        app:layout_constraintTop_toTopOf="@+id/close_guide_line"
        app:layout_constraintEnd_toEndOf="@+id/close_guide_line"/>

    <TextView
        android:id="@+id/mj_visitor_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#9C592D"
        android:textSize="16sp"
        android:textStyle="bold"
        android:text="@string/mj_visitor_list_title"
        app:layout_constraintTop_toTopOf="@+id/mj_visitor_list_bg"
        app:layout_constraintStart_toStartOf="@+id/mj_visitor_list_bg"
        app:layout_constraintEnd_toEndOf="@+id/mj_visitor_list_bg"
        android:layout_marginTop="12dp"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/mj_visitor_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        tools:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:listitem="@layout/mj_visitor_list_item"
        app:layout_constraintTop_toBottomOf="@+id/mj_visitor_dialog_title"
        app:layout_constraintBottom_toBottomOf="@+id/mj_visitor_list_bg"
        app:layout_constraintStart_toStartOf="@+id/mj_visitor_list_bg"
        app:layout_constraintEnd_toEndOf="@+id/mj_visitor_list_bg"
        android:layout_marginStart="@dimen/dp6"
        android:layout_marginEnd="@dimen/dp6"
        android:layout_marginBottom="@dimen/dp6"/>
</androidx.constraintlayout.widget.ConstraintLayout>