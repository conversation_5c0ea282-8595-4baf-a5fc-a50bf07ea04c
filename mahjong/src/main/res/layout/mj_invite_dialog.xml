<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="276dp"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/mj_hint_dialog_bg"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/invite_layout"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:layout_marginTop="12dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/invite_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="14dp"
            android:includeFontPadding="false"
            android:text="@string/invitej_friends"
            android:textColor="#9C592D"
            android:textSize="16sp" />

        <LinearLayout
            android:id="@+id/input_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="28dp"
            android:layout_marginRight="18dp"
            android:background="@drawable/edit_txt_bg"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:src="@drawable/mj_search" />

            <EditText
                android:id="@+id/input_edit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:background="@android:color/transparent"
                android:gravity="center_vertical"
                android:hint="@string/invitej_friends"
                android:textColor="#C09373"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/recent_user"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="5dp"
        android:includeFontPadding="false"
        android:text="@string/recent_friends"
        android:textColor="#2D2D2D"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/invite_layout" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recent_friends_list"
        android:layout_width="match_parent"
        android:layout_height="136dp"
        android:layout_marginLeft="14dp"
        android:layout_marginRight="14dp"
        app:layout_constraintHeight_max="158dp"
        app:layout_constraintTop_toBottomOf="@+id/recent_user" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="14dp"
        android:layout_marginTop="14dp"
        android:layout_marginRight="14dp"
        android:background="#C9B27C"
        app:layout_constraintTop_toBottomOf="@+id/recent_friends_list" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/share_platform_rv"
        android:layout_width="268dp"
        android:layout_height="40dp"
        tools:layoutManager="LinearLayoutManager"
        tools:listitem="@layout/share_item_view"
        tools:itemCount="2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="11dp"
        android:layout_marginBottom="14dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>