<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/root_lay"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <TextView
        android:id="@+id/charm_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="11dp"
        android:textColor="#FFFFFF"
        android:layout_marginLeft="11dp"
        android:layout_marginBottom="2dp"
        android:paddingStart="11dp"
        android:paddingEnd="4dp"
        app:layout_constraintStart_toStartOf="@id/charm_iv"
        app:layout_constraintBottom_toBottomOf="@id/charm_iv"
        tools:text="1000890"
        tools:background="@drawable/mj_charm_number_bg_16_17_18"
        />

    <ImageView
        android:id="@+id/charm_iv"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="parent"
        tools:src="@drawable/user_charm_level_16"/>

</androidx.constraintlayout.widget.ConstraintLayout>