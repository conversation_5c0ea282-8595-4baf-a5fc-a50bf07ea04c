<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:minHeight="68dp"
    android:background="@drawable/mj_invite_list_item_bg"
    tools:layout_width="276dp">

    <com.huiwan.decorate.DecorHeadImgView
        android:id="@+id/head_iv"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@color/white"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="16dp"/>

    <TextView
        android:id="@+id/name_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="#9C592D"
        android:textSize="14sp"
        tools:text="用戶名某某某"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/desc_tv"
        app:layout_constraintStart_toEndOf="@+id/head_iv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="90dp" />

    <TextView
        android:id="@+id/desc_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="#2D2D2D"
        android:textSize="12sp"
        android:text="想和你并肩作戰"
        app:layout_constraintTop_toBottomOf="@+id/name_tv"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/name_tv"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="85dp"
        android:layout_marginTop="2dp"/>

    <ImageView
        android:id="@+id/refuse_iv"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@drawable/mj_invite_refuse_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="58dp"/>

    <com.huiwan.widget.CircleTimerProgressBar
        android:id="@+id/accept_process_bar"
        android:layout_width="32dp"
        android:layout_height="32dp"
        app:ctpb_progress_color="#FF9921"
        app:ctpb_progress_width="1.5dp"
        app:ctpb_progress_start_angle="-90"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="15dp"/>

    <ImageView
        android:id="@+id/accept_iv"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/mj_invite_accept_icon"
        android:padding="3dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="15dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>