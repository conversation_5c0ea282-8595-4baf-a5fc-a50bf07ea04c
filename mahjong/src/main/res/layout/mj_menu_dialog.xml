<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp96"
    android:layout_height="wrap_content"
    android:background="@drawable/mj_menu_bg"
    android:orientation="vertical">

    <TextView
        android:id="@+id/stand_watch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:text="@string/stand_watch"
        android:textColor="#FFFFFF"
        android:textSize="14sp" />

    <View
        android:id="@+id/stand_line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="19dp"
        android:layout_marginRight="19dp"
        android:background="#33FFFFFF" />

    <TextView
        android:id="@+id/game_course"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:text="@string/game_course"
        android:textColor="#FFFFFF"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="19dp"
        android:layout_marginRight="19dp"
        android:background="#33FFFFFF" />

    <TextView
        android:id="@+id/voice_change"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:text="@string/voice_change"
        android:textColor="#FFFFFF"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="19dp"
        android:layout_marginRight="19dp"
        android:background="#33FFFFFF" />

    <TextView
        android:id="@+id/invite_watch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:text="@string/invite_watch"
        android:textColor="#FFFFFF"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="19dp"
        android:layout_marginRight="19dp"
        android:background="#33FFFFFF" />

    <TextView
        android:id="@+id/exit_game"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:text="@string/exit_game"
        android:textColor="#FFFFFF"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="19dp"
        android:layout_marginRight="19dp"
        android:background="#33FFFFFF" />
</LinearLayout>