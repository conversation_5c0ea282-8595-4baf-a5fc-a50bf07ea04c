<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/mahjong_table_bg"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <View
        android:id="@+id/shadow_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_transparent80"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/share_image_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp">
        <com.huiwan.widget.image.RoundedImageView
            android:id="@+id/share_screenshot_bg"
            android:layout_width="590dp"
            android:layout_height="265dp"
            app:riv_corner_radius="8dp"
            android:src="@drawable/mahjong_table_bg"
            android:scaleType="centerCrop"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <ImageView
            android:id="@+id/app_icon_iv"
            android:layout_width="45dp"
            android:layout_height="61dp"
            android:src="@drawable/mj_share_app_icon"
            app:layout_constraintTop_toTopOf="@+id/share_screenshot_bg"
            app:layout_constraintStart_toStartOf="@+id/share_screenshot_bg"
            android:layout_marginTop="17dp"
            android:layout_marginStart="22dp"/>

        <ImageView
            android:id="@+id/mj_share_qr_icon"
            android:layout_width="58dp"
            android:layout_height="58dp"
            android:src="@drawable/mj_share_qr_icon"
            app:layout_constraintStart_toStartOf="@+id/share_screenshot_bg"
            app:layout_constraintBottom_toBottomOf="@+id/share_screenshot_bg"
            android:layout_marginStart="15dp"
            android:layout_marginBottom="17dp"/>

        <TextView
            android:id="@+id/mj_share_slogan_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/mj_share_result_slogan"
            android:textSize="9sp"
            android:textColor="@color/white"
            app:layout_constraintStart_toStartOf="@+id/mj_share_qr_icon"
            app:layout_constraintEnd_toEndOf="@+id/mj_share_qr_icon"
            app:layout_constraintBottom_toTopOf="@+id/mj_share_qr_icon"
            android:layout_marginBottom="6dp"/>

        <View
            android:id="@+id/mj_share_screenshot_bg"
            android:layout_width="482dp"
            android:layout_height="225dp"
            android:background="#55FFFFFF"
            app:layout_constraintTop_toTopOf="@+id/share_screenshot_bg"
            app:layout_constraintEnd_toEndOf="@+id/share_screenshot_bg"
            android:layout_marginTop="18dp"
            android:layout_marginEnd="15dp"/>

        <ImageView
            android:id="@+id/mj_share_screenshot_iv"
            android:layout_width="478dp"
            android:layout_height="221dp"
            android:scaleType="centerCrop"
            android:background="@color/color_transparent50"
            app:layout_constraintTop_toTopOf="@+id/mj_share_screenshot_bg"
            app:layout_constraintBottom_toBottomOf="@+id/mj_share_screenshot_bg"
            app:layout_constraintStart_toStartOf="@+id/mj_share_screenshot_bg"
            app:layout_constraintEnd_toEndOf="@+id/mj_share_screenshot_bg"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/mj_share_platform_rv"
        android:layout_width="wrap_content"
        android:layout_height="70dp"
        tools:layoutManager="GridLayoutManager"
        tools:listitem="@layout/share_item_view"
        tools:spanCount="5"
        app:layout_constraintTop_toBottomOf="@+id/share_image_layout"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>