<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        android:id="@+id/di_tai_tv"
        android:layout_width="80dp"
        android:layout_height="28dp"
        android:background="@drawable/create_unselect_bg"
        android:gravity="center"
        android:includeFontPadding="false"
        android:textColor="#FFFFFF"
        android:textSize="12sp"
        tools:text="10/5"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>