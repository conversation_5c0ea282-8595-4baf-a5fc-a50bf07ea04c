<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#B3000000">

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/ice_ball_viewpager"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true" />


    <com.huiwan.widget.IceBallRuleIndicatorView
        android:id="@+id/ice_ball_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="30dp"
         />

</RelativeLayout>