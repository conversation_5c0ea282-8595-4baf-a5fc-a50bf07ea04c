<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="276dp"
    android:layout_height="170dp"
    android:background="@drawable/mj_hint_dialog_bg">

    <TextView
        android:id="@+id/hint_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="18dp"
        android:layout_marginTop="28dp"
        android:layout_marginRight="18dp"
        android:text="@string/mj_leave_hint"
        android:textColor="#2D2D2D"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/ok_btn"
        android:layout_width="112dp"
        android:layout_height="36dp"
        android:layout_below="@+id/hint_title"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="46dp"
        android:background="@drawable/invite_btn_bg"
        android:gravity="center"
        android:text="@string/come_back"
        android:textColor="#FFFFFF"
        android:textSize="16sp" />
</RelativeLayout>