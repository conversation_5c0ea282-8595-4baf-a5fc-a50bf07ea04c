<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/mj_match_bg">

    <RelativeLayout
        android:id="@+id/head_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/ios55">

        <ImageView
            android:id="@+id/back"
            android:layout_width="@dimen/ios62"
            android:layout_height="@dimen/ios42"
            android:paddingBottom="@dimen/ios22"
            android:src="@drawable/left_arrow" />

        <TextView
            android:id="@+id/room_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:text="@string/mj_room_name"
            android:textColor="#FFFFFF"
            android:textSize="16dp" />

        <ImageView
            android:id="@+id/help"
            android:layout_width="@dimen/ios18"
            android:layout_height="@dimen/ios18"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/ios20"
            android:src="@drawable/help" />

    </RelativeLayout>

    <com.huiwan.widget.CustomCircleImageView
        android:id="@+id/player_head_view"
        android:layout_width="@dimen/ios78"
        android:layout_height="@dimen/ios71"
        android:layout_below="@+id/head_layout"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/ios14"
        android:padding="@dimen/ios2"
        android:src="@drawable/default_user_head_icon" />

    <RelativeLayout
        android:id="@+id/group_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/player_head_view"
        android:layout_alignBottom="@+id/player_head_view"
        android:layout_alignParentStart="true"
        android:layout_marginStart="@dimen/ios20"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/group_icon"
                android:layout_width="@dimen/ios36"
                android:layout_height="@dimen/ios36"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/group_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:text="@string/group_chat_title"
                android:textColor="#C0D5E6"
                android:textSize="12dp" />
        </LinearLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rank_list"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/player_head_view"
        android:layout_alignBottom="@+id/player_head_view"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/ios20">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/rank_icon"
                android:layout_width="@dimen/ios36"
                android:layout_height="@dimen/ios36"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/rank_icon" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:text="@string/rank_list_name"
                android:textColor="#C0D5E6"
                android:textSize="12dp" />
        </LinearLayout>
    </RelativeLayout>

    <ImageView
        android:id="@+id/activity_icon"
        android:layout_width="@dimen/ios36"
        android:layout_height="@dimen/ios46"
        android:layout_below="@id/rank_list"
        android:layout_alignStart="@id/rank_list"
        android:visibility="gone" />

    <View
        android:id="@+id/center_line"
        android:layout_width="1dp"
        android:layout_height="1dp"
        android:layout_below="@+id/player_head_view"
        android:layout_centerHorizontal="true" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/week_win_hint_layout"
        android:layout_width="116dp"
        android:layout_height="wrap_content"
        android:layout_below="@id/player_head_view"
        android:layout_marginTop="@dimen/ios8"
        android:layout_toStartOf="@+id/center_line">

        <TextView
            android:id="@+id/week_win_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="@string/week_win_coin"
            android:textColor="#FFFFFF"
            android:textSize="12dp"
            app:layout_constraintEnd_toStartOf="@+id/mj_history_icon"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/mj_history_icon"
            android:layout_width="20dp"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/ios4"
            android:paddingEnd="@dimen/ios4"
            android:src="@drawable/mj_history_icon"
            app:layout_constraintBottom_toBottomOf="@+id/week_win_hint"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/week_win_hint"
            app:layout_constraintTop_toTopOf="@+id/week_win_hint" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <TextView
        android:id="@+id/mine_coin_hint"
        android:layout_width="116dp"
        android:layout_height="wrap_content"
        android:layout_below="@id/player_head_view"
        android:layout_marginTop="@dimen/ios8"
        android:layout_toEndOf="@+id/center_line"
        android:gravity="center_horizontal"
        android:text="@string/mine_coin"
        android:textColor="#FFFFFF"
        android:textSize="12dp" />

    <RelativeLayout
        android:id="@+id/week_win_coin_layout"
        android:layout_width="116dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/week_win_hint_layout"
        android:layout_marginTop="@dimen/ios5"
        android:layout_toStartOf="@+id/center_line">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/week_coin_icon"
                android:layout_width="@dimen/ios15"
                android:layout_height="@dimen/ios15"
                android:src="@drawable/mj_money_coin" />

            <TextView
                android:id="@+id/week_win_coin_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/ios4"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="#FEE276"
                android:textSize="16dp"
                tool:text="203213231200" />
        </LinearLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/mine_coin_layout"
        android:layout_width="116dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/week_win_hint_layout"
        android:layout_alignBottom="@+id/week_win_coin_layout"
        android:layout_toEndOf="@+id/center_line">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/mine_coin_icon"
                android:layout_width="@dimen/ios15"
                android:layout_height="@dimen/ios15"
                android:src="@drawable/mj_money_coin" />

            <TextView
                android:id="@+id/mine_win_coin_num"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/ios4"
                android:ellipsize="end"
                android:includeFontPadding="false"
                android:maxLines="1"
                android:textColor="#FEE276"
                android:textSize="16dp"
                tool:text="20321323213300" />
        </LinearLayout>
    </RelativeLayout>

    <com.wepie.module.mahjong.view.recycleview.GalleryRecycleView
        android:id="@+id/model_select"
        android:layout_width="match_parent"
        android:layout_height="@dimen/ios280"
        android:layout_below="@+id/mine_coin_layout"
        android:layout_marginTop="@dimen/ios59"
        android:clipToPadding="false"
        android:paddingLeft="@dimen/ios62"
        android:paddingRight="@dimen/ios62" />

    <com.wepie.module.mahjong.view.recycleview.AlphaRelativeLayout
        android:id="@+id/quick_start"
        android:layout_width="@dimen/ios223"
        android:layout_height="@dimen/ios50"
        android:layout_below="@+id/model_select"
        android:layout_centerHorizontal="true"
        android:background="@drawable/quick_start">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/quick_start"
            android:textColor="#A04D0D"
            android:textSize="18dp"
            android:textStyle="bold" />
    </com.wepie.module.mahjong.view.recycleview.AlphaRelativeLayout>

    <com.wepie.module.mahjong.view.recycleview.AlphaRelativeLayout
        android:id="@+id/search_room_layout"
        android:layout_width="@dimen/ios108"
        android:layout_height="@dimen/ios46"
        android:layout_below="@+id/quick_start"
        android:layout_marginTop="@dimen/ios13"
        android:layout_toStartOf="@id/center_line"
        android:background="@drawable/mj_search_bg">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/search_icon"
                android:layout_width="@dimen/ios14"
                android:layout_height="@dimen/ios14"
                android:src="@drawable/search" />

            <TextView
                android:id="@+id/search_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/ios4"
                android:includeFontPadding="false"
                android:text="@string/search_room"
                android:textColor="#B1C4E7"
                android:textSize="12dp" />
        </LinearLayout>

    </com.wepie.module.mahjong.view.recycleview.AlphaRelativeLayout>

    <com.wepie.module.mahjong.view.recycleview.AlphaRelativeLayout
        android:id="@+id/create_room_layout"
        android:layout_width="@dimen/ios108"
        android:layout_height="@dimen/ios46"
        android:layout_below="@+id/quick_start"
        android:layout_marginTop="@dimen/ios13"
        android:layout_toEndOf="@id/center_line"
        android:background="@drawable/create_bg">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true">

            <ImageView
                android:id="@+id/create_icon"
                android:layout_width="@dimen/ios14"
                android:layout_height="@dimen/ios14"
                android:src="@drawable/create" />

            <TextView
                android:id="@+id/create_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/ios4"
                android:includeFontPadding="false"
                android:text="@string/create_room"
                android:textColor="#B1C4E7"
                android:textSize="12dp" />
        </LinearLayout>
    </com.wepie.module.mahjong.view.recycleview.AlphaRelativeLayout>

    <TextView
        android:id="@+id/mj_tips"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="@dimen/ios20"
        android:includeFontPadding="false"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:text="1、本產品以成年人為訴求對象 \n2、本產品內容為棋牌益智及娛樂類型，不提供「現金交易賭博」，也沒有機會贏得現金或實體獎品"
        android:textColor="#7FFFFFFF"
        android:textSize="10dp"
        android:visibility="gone"
        tool:visibility="visible" />

    <FrameLayout
        android:id="@+id/guide_fragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tool:visibility="gone" />

</RelativeLayout>