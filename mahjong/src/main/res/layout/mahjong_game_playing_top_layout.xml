<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@drawable/mahjong_table_bg"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <Space
        android:id="@+id/top_guide_line"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        app:layout_constraintTop_toTopOf="@+id/center_bg_iv"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="26dp"/>

    <!--  对家出牌区  -->
    <com.wepie.module.mahjong.view.card.MJFrameLayout
        android:id="@+id/top_chu_pai_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/top_guide_line"/>

    <!--  对家花牌区  -->
    <com.wepie.module.mahjong.view.card.MJFrameLayout
        android:id="@+id/top_hua_pai_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/top_shou_pai_layout"
        app:layout_constraintEnd_toStartOf="@+id/top_player_info"/>

    <!--  对家明牌  -->
    <Space
        android:id="@+id/top_ming_pai_guide"
        android:layout_width="1dp"
        android:layout_height="1dp"
        app:layout_constraintTop_toTopOf="@+id/top_shou_pai_layout"
        app:layout_constraintStart_toEndOf="@+id/top_shou_pai_layout"/>
    <com.wepie.module.mahjong.view.card.MJFrameLayout
        android:id="@+id/top_ming_pai_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/top_shou_pai_layout"
        app:layout_constraintBottom_toBottomOf="@+id/top_shou_pai_layout"
        app:layout_constraintEnd_toEndOf="@+id/top_ming_pai_guide"/>

    <!--  对家手牌  -->
    <FrameLayout
        android:id="@+id/top_shou_pai_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="8dp"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <!--  对家用户信息  -->
    <com.wepie.module.mahjong.view.MJPlayerView
        android:id="@+id/top_player_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@+id/top_shou_pai_layout"
        android:layout_marginTop="10dp"/>

    <include
        android:id="@+id/top_player_bank_info"
        layout="@layout/mj_zhuang_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/top_player_info"
        app:layout_constraintStart_toStartOf="@+id/top_player_info"
        app:layout_constraintTop_toBottomOf="@+id/top_player_info" />

    <FrameLayout
        android:id="@+id/top_feng_layout"
        android:layout_width="85dp"
        android:layout_height="115dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:scaleY="0.5"
        android:scaleX="0.5"
        android:visibility="gone"/>

    <FrameLayout
        android:id="@+id/top_anim_layout"
        android:layout_width="185dp"
        android:layout_height="80dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/top_player_info"
        android:layout_marginTop="17dp"
        android:layout_marginEnd="3dp"
        android:visibility="gone"/>

    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/top_ting_iv"
        android:layout_width="34dp"
        android:layout_height="64dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="10dp"
        android:visibility="gone"/>

    <ImageView
        android:id="@+id/top_message_arrow"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:src="@drawable/mj_chat_message_bubble_arrow"
        android:rotation="180"
        app:layout_constraintTop_toTopOf="@+id/top_player_info"
        app:layout_constraintEnd_toStartOf="@+id/top_player_info"
        android:layout_marginTop="8dp"
        android:visibility="gone"/>
    <FrameLayout
        android:id="@+id/top_message_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="32dp"
        android:background="@drawable/mj_chat_message_bubble"
        app:layout_constraintTop_toTopOf="@+id/top_message_arrow"
        app:layout_constraintEnd_toStartOf="@+id/top_message_arrow"
        android:visibility="gone">
        <TextView
            android:id="@+id/top_message_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="170dp"
            android:textColor="#9C592D"
            android:textSize="14sp"
            tools:text="快点啦，别磨磨蹭蹭的～"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:paddingStart="7dp"
            android:paddingEnd="8dp"
            android:visibility="gone"/>

        <com.wepie.module.mahjong.userface.MJUserFaceView
            android:id="@+id/top_face_view"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="6dp"
            android:layout_marginBottom="6dp"
            android:layout_marginStart="6dp"
            android:layout_marginEnd="6dp"
            android:visibility="gone"/>
    </FrameLayout>
</merge>