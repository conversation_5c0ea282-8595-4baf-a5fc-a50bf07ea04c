<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/settlement_anim_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/mj_settlement_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <com.wepie.module.mahjong.view.MJPlayerView
        android:id="@+id/settlement_player_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:rotation="-5"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintVertical_bias="0.5"
        android:layout_marginStart="160dp"
        android:visibility="invisible"/>

    <com.wepie.module.mahjong.view.custom.MJFadeImageView
        android:id="@+id/win_coin_bg"
        android:layout_width="155dp"
        android:layout_height="51dp"
        app:layout_constraintTop_toTopOf="@+id/win_coin_layout"
        app:layout_constraintStart_toStartOf="@+id/win_coin_layout"
        android:layout_marginTop="3dp"
        android:layout_marginStart="33dp"
        android:visibility="invisible"/>

    <RelativeLayout
        android:id="@+id/win_coin_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:rotation="-5"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/settlement_player_view"
        app:layout_constraintVertical_bias="0.45"
        android:layout_marginStart="17dp"
        android:visibility="invisible">

        <ImageView
            android:id="@+id/win_coin_icon"
            android:layout_width="33dp"
            android:layout_height="33dp"
            android:src="@drawable/mj_money_coin"
            android:layout_marginTop="12dp"/>

        <TextView
            android:id="@+id/win_coin_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="2000"
            android:textSize="40sp"
            android:textColor="#ffffff"
            android:includeFontPadding="false"
            android:layout_toEndOf="@+id/win_coin_icon"
            android:layout_marginStart="8dp"/>
    </RelativeLayout>

    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/coin_explode"
        android:layout_width="500dp"
        android:layout_height="250dp"
        app:layout_constraintTop_toTopOf="@+id/win_coin_layout"
        app:layout_constraintBottom_toBottomOf="@+id/win_coin_layout"
        app:layout_constraintStart_toStartOf="@+id/win_coin_layout"
        app:layout_constraintEnd_toEndOf="@+id/win_coin_layout"
        android:visibility="gone"/>

    <Space
        android:id="@+id/tai_line"
        android:layout_width="1dp"
        android:layout_height="1dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_bias="0.2"
        app:layout_constraintHorizontal_bias="0.48"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/tai_info_rv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:rotation="-5"
        android:clipChildren="false"
        app:layout_constraintTop_toTopOf="@+id/tai_line"
        app:layout_constraintStart_toStartOf="@+id/tai_line"
        android:layout_marginStart="15dp"
        android:visibility="invisible"/>

    <Space
        android:id="@+id/tai_num_line"
        android:layout_width="1dp"
        android:layout_height="1dp"
        app:layout_constraintStart_toStartOf="@+id/tai_info_rv"
        app:layout_constraintBottom_toBottomOf="@+id/tai_info_rv"
        android:layout_marginBottom="20dp"/>

    <com.wepie.module.mahjong.view.custom.MJFadeImageView
        android:id="@+id/tai_num_bg"
        android:layout_width="155dp"
        android:layout_height="51dp"
        app:layout_constraintTop_toTopOf="@+id/tai_num_layout"
        app:layout_constraintStart_toStartOf="@+id/tai_num_layout"
        android:layout_marginStart="3dp"
        android:layout_marginTop="6dp"
        android:visibility="invisible"/>

    <LinearLayout
        android:id="@+id/tai_num_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:rotation="-5"
        app:layout_constraintTop_toBottomOf="@+id/tai_num_line"
        app:layout_constraintStart_toStartOf="@+id/tai_num_line"
        android:layout_marginStart="80dp"
        android:visibility="invisible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:text="@string/total_tai"
            android:includeFontPadding="false"/>

        <com.wepie.module.mahjong.view.MJGradientColorTextView
            android:id="@+id/total_tai_num_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="9台"
            android:textSize="40sp"
            android:textColor="#FFA42C"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp"/>
    </LinearLayout>

    <com.wepie.module.mahjong.view.card.MJFrameLayout
        android:id="@+id/pai_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:rotation="-6"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_bias="0.82"
        android:visibility="invisible"/>

    <com.wepie.module.mahjong.view.card.MJFrameLayout
        android:id="@+id/hua_pai_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:rotation="-6"
        app:layout_constraintBottom_toBottomOf="@+id/pai_layout"
        app:layout_constraintStart_toStartOf="@+id/pai_layout"
        android:layout_marginBottom="35dp"
        android:visibility="invisible"/>

    <ImageView
        android:id="@+id/mj_round_over_screen_shot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/mj_round_over_screen_shot_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="24dp"
        android:layout_marginStart="33dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>