<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/mj_gold_card_tips_tv"
        android:layout_width="wrap_content"
        android:maxWidth="142dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="@string/mj_play_model_gold_cards_tips"
        android:textSize="14sp"
        android:textColor="@color/white"
        android:lineSpacingExtra="@dimen/dp3"
        android:background="@drawable/mj_gold_card_tips_bg"
        android:paddingTop="@dimen/dp14"
        android:paddingBottom="@dimen/dp6"
        android:paddingStart="9dp"
        android:paddingEnd="9dp"/>
</FrameLayout>