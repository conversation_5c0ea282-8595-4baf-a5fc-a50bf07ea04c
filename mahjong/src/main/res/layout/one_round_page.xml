<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_lay"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#BB000000">

    <include layout="@layout/one_round_page_user"
        android:id="@+id/first_user_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="24dp"/>

    <include layout="@layout/one_round_page_user"
        android:id="@+id/second_user_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="24dp"/>

    <include layout="@layout/one_round_page_user"
        android:id="@+id/third_user_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="24dp"/>

    <include layout="@layout/one_round_page_user"
        android:id="@+id/fourth_user_lay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="24dp"/>

    <com.wepie.module.mahjong.view.custom.MJPressTextView
        android:id="@+id/play_again"
        android:layout_width="129dp"
        android:layout_height="55dp"
        android:background="@drawable/mj_play_again_btn_bg"
        android:text="(10s)"
        android:textColor="#ECF3F9"
        android:textSize="14sp"
        android:textStyle="bold"
        android:shadowColor="#A04D0D"
        android:shadowRadius="5"
        android:shadowDx="5"
        android:shadowDy="5"
        android:gravity="bottom|center_horizontal"
        android:paddingBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="12dp"
        android:layout_marginEnd="38dp"/>

    <com.wepie.module.mahjong.view.custom.MJPressTextView
        android:id="@+id/change_room"
        android:layout_width="129dp"
        android:layout_height="55dp"
        android:background="@drawable/mj_change_room"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/play_again"
        android:layout_marginBottom="12dp"
        android:layout_marginEnd="10dp"/>

    <com.wepie.module.mahjong.view.custom.MJPressImageView
        android:id="@+id/share_icon"
        android:layout_width="53dp"
        android:layout_height="55dp"
        android:src="@drawable/mj_share"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/change_room"
        android:layout_marginBottom="12dp"
        android:layout_marginEnd="10dp"/>

    <ImageView
        android:id="@+id/mj_result_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/mj_back_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="12dp"
        android:layout_marginStart="24dp"/>

    <com.wepie.module.mahjong.view.custom.MJPushUpAnimView
        android:id="@+id/like_tips_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="79dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>