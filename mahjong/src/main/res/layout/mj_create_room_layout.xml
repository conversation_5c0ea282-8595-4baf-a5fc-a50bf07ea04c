<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="#B2000000">

    <LinearLayout
        android:id="@+id/mj_create_card_lay"
        android:layout_width="295dp"
        android:layout_height="411dp"
        android:layout_centerInParent="true"
        android:layout_marginStart="5dp"
        android:layout_marginTop="9dp"
        android:background="@drawable/create_room_dialog_bg"
        android:orientation="vertical">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:textColor="#FFFFFF"
            android:textSize="20sp"
            android:textStyle="bold"
            android:text="@string/create_room_dialog" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:textColor="#80FFFFFF"
                    android:textSize="12sp"
                    android:text="@string/di_tai" />

                <!-- 台/底及圈数选项后期需要调整成可动态配置的 -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/di_tai_rv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    tools:spanCount="3"
                    tools:listitem="@layout/mj_create_di_tai_list_item"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="9dp"
                    android:layout_marginTop="8dp"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="16dp"
                    android:textColor="#80FFFFFF"
                    android:textSize="12sp"
                    android:text="@string/circle_num" />

                <include
                    android:id="@+id/circle_layout"
                    layout="@layout/di_tai_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="16dp"
                    android:textColor="#80FFFFFF"
                    android:textSize="12sp"
                    android:text="@string/play_second"/>

                <include
                    android:id="@+id/second_layout"
                    layout="@layout/di_tai_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="8dp"/>
            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/dp8"
            android:layout_marginStart="20dp">
            <CheckBox
                android:id="@+id/change_three_cards"
                android:layout_width="wrap_content"
                android:minWidth="50dp"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@color/white"
                android:text="@string/mj_play_model_change_cards"
                android:background="@null"
                android:button="@null"
                android:drawableStart="@drawable/mj_create_room_select_drawable"
                android:drawablePadding="@dimen/dp4"
                android:layout_marginEnd="40dp"
                android:paddingTop="@dimen/dp8"
                android:paddingBottom="@dimen/dp8"/>

            <CheckBox
                android:id="@+id/gold_cards"
                android:layout_width="wrap_content"
                android:minWidth="50dp"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@color/white"
                android:text="@string/mj_play_model_gold_cards"
                android:background="@null"
                android:button="@null"
                android:drawableStart="@drawable/mj_create_room_select_drawable"
                android:drawablePadding="@dimen/dp4"
                android:paddingTop="@dimen/dp8"
                android:paddingBottom="@dimen/dp8"/>
        </LinearLayout>

        <TextView
            android:id="@+id/create_room"
            android:layout_width="219dp"
            android:layout_height="50dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="17dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/create_room_btn_bg"
            android:gravity="center"
            android:textColor="#A04D0D"
            android:textSize="18sp"
            android:textStyle="bold"
            android:text="@string/create_room_dialog" />
    </LinearLayout>

    <Space
        android:id="@+id/close_guide_line"
        android:layout_width="1dp"
        android:layout_height="1dp"
        android:layout_above="@+id/mj_create_card_lay"
        android:layout_toEndOf="@+id/mj_create_card_lay"
        android:layout_marginStart="7dp"/>

    <ImageView
        android:id="@+id/close"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/close"
        android:layout_alignTop="@+id/close_guide_line"
        android:layout_alignEnd="@+id/close_guide_line"/>
</RelativeLayout>