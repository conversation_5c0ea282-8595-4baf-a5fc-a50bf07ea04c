<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="105dp"
    android:layout_height="28dp"
    android:layout_marginLeft="12dp"
    android:layout_marginTop="2dp"
    android:layout_marginRight="12dp">

    <TextView
        android:id="@+id/tai_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FFFFFF"
        android:textSize="14sp"
        tool:text="连三拉二"
        android:layout_centerVertical="true"/>

    <TextView
        android:id="@+id/tai_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:textColor="#F7EC77"
        android:textSize="20sp"
        tool:text="3台"
        android:layout_centerVertical="true"/>

</RelativeLayout>