<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent">

    <RelativeLayout
        android:layout_width="305dp"
        android:layout_height="249dp"
        android:layout_centerInParent="true">

        <LinearLayout
            android:layout_width="295dp"
            android:layout_height="240dp"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="9dp"
            android:background="@drawable/search_room_bg"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20dp"
                android:text="@string/search_room_dialog"
                android:textColor="#FFFFFF"
                android:textStyle="bold"
                android:textSize="20sp" />

            <EditText
                android:id="@+id/input_room_number"
                android:layout_width="247dp"
                android:layout_height="54dp"
                android:maxLines="1"
                android:layout_gravity="center"
                android:layout_marginTop="20dp"
                android:background="#80193778"
                android:hint="@string/search_room_dialog_hint"
                android:textColorHint="#80FFFFFF"
                android:textCursorDrawable="@null"
                android:inputType="number"
                android:paddingLeft="20dp"
                android:textColor="#FFFFFF"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/enter_room"
                android:layout_width="219dp"
                android:layout_height="50dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="40dp"
                android:background="@drawable/create_room_btn_bg"
                android:gravity="center"
                android:text="@string/enter_room"
                android:textStyle="bold"
                android:textColor="#A04D0D"
                android:textSize="18sp" />
        </LinearLayout>

        <ImageView
            android:id="@+id/close"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_alignParentRight="true"
            android:src="@drawable/close" />
    </RelativeLayout>
</RelativeLayout>