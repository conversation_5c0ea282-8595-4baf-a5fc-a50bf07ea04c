<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/mj_like_anim_item_bg">

    <TextView
        android:id="@+id/tips_content_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#333333"
        android:textSize="12sp"
        tools:text="陈政给廖总点了个赞，一起再来一局吧！"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="6dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>