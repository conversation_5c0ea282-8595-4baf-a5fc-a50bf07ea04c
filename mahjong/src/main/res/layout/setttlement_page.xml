<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_settlement"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:background="#B3000000">

    <include layout="@layout/settltment_page_1"
        android:id="@+id/first_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:visibility="gone"
        tools:visibility="visible"/>

    <include layout="@layout/settltment_page_2"
        android:id="@+id/second_page"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/watch_window_btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#D7DFFF"
        android:textSize="12sp"
        android:text="@string/mj_watch_txt"
        android:drawableTop="@drawable/mj_watch_window_icon"
        android:drawablePadding="2dp"
        android:background="@null"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="57dp"
        android:layout_marginBottom="13dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>