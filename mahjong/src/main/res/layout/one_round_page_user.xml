<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <View
        android:id="@+id/user_bg"
        android:layout_width="169dp"
        android:layout_height="253dp"
        android:background="@drawable/mj_game_over_first_card_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="23dp"/>

    <View
        android:id="@+id/user_bg_border"
        android:layout_width="169dp"
        android:layout_height="253dp"
        android:background="@drawable/mj_game_over_card_border"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="23dp"
        android:visibility="gone"/>

    <com.wepie.module.mahjong.view.MJGradientColorTextView
        android:id="@+id/user_rank"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="48sp"
        android:textColor="#FFFFFF"
        tools:text="@string/mj_rank_1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <com.huiwan.widget.CustomCircleImageView
        android:id="@+id/user_head_view"
        android:layout_width="52dp"
        android:layout_height="52dp"
        android:src="@drawable/default_head_icon"
        android:background="@drawable/shape_head_circle_bg"
        android:padding="2dp"
        app:layout_constraintTop_toTopOf="@+id/user_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="47dp"/>

    <TextView
        android:id="@+id/exit_status_tv"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:textSize="16sp"
        android:textColor="#FFFFFF"
        android:text="@string/mj_exit_room"
        android:gravity="center"
        android:background="@drawable/mj_game_over_head_shadow"
        app:layout_constraintTop_toTopOf="@+id/user_head_view"
        app:layout_constraintBottom_toBottomOf="@+id/user_head_view"
        app:layout_constraintStart_toStartOf="@+id/user_head_view"
        app:layout_constraintEnd_toEndOf="@+id/user_head_view"
        android:visibility="gone"/>

    <TextView
        android:id="@+id/again_one_tv"
        android:layout_width="65dp"
        android:layout_height="wrap_content"
        android:minHeight="31dp"
        android:textSize="13sp"
        android:textColor="#FFFFFF"
        android:text="@string/mj_again_one"
        android:gravity="center_horizontal"
        android:background="@drawable/mj_game_over_again_one_bg"
        android:paddingTop="6dp"
        app:layout_constraintBottom_toBottomOf="@+id/user_head_view"
        app:layout_constraintEnd_toEndOf="@+id/user_bg"
        android:layout_marginBottom="24dp"
        android:layout_marginEnd="5dp"
        android:visibility="gone"/>

    <com.huiwan.decorate.NameTextView
        android:id="@+id/user_name_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="陈一波"
        android:textColor="#FFFFFFFF"
        android:textSize="12sp"
        app:layout_constraintTop_toBottomOf="@+id/user_head_view"
        app:layout_constraintStart_toStartOf="@+id/user_head_view"
        app:layout_constraintEnd_toEndOf="@+id/user_head_view"
        android:layout_marginTop="12dp"/>

    <ImageView
        android:id="@+id/gender_image"
        android:layout_width="13dp"
        android:layout_height="13dp"
        android:src="@drawable/mj_female"
        app:layout_constraintTop_toBottomOf="@+id/user_name_view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/vip_image"
        android:layout_marginTop="10dp"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <ImageView
        android:id="@+id/vip_image"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:src="@drawable/vip_label_1"
        app:layout_constraintTop_toTopOf="@+id/gender_image"
        app:layout_constraintBottom_toBottomOf="@+id/gender_image"
        app:layout_constraintStart_toEndOf="@+id/gender_image"
        app:layout_constraintEnd_toStartOf="@+id/charm_number_view"
        android:layout_marginStart="5dp"/>

    <ImageView
        android:id="@+id/charm_number_view"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        app:layout_constraintTop_toTopOf="@+id/gender_image"
        app:layout_constraintBottom_toBottomOf="@+id/gender_image"
        app:layout_constraintStart_toEndOf="@+id/vip_image"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="4dp"
        android:visibility="gone"/>

    <View
        android:id="@+id/coin_bg"
        android:layout_width="128dp"
        android:layout_height="37dp"
        android:background="@drawable/mj_game_over_coin_bg"
        app:layout_constraintTop_toTopOf="@+id/coin_name"
        app:layout_constraintBottom_toBottomOf="@+id/coin_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <TextView
        android:id="@+id/coin_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/mj_coin_string"
        android:textSize="12sp"
        android:textColor="#FFFFFF"
        app:layout_constraintTop_toBottomOf="@+id/gender_image"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="21dp"
        android:layout_marginStart="39dp"/>

    <TextView
        android:id="@+id/coin_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="+60"
        android:textSize="12sp"
        android:textColor="#F0FF37"
        app:layout_constraintTop_toTopOf="@+id/coin_name"
        app:layout_constraintBottom_toBottomOf="@+id/coin_name"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="28dp"/>

    <ImageView
        android:id="@+id/like_icon"
        android:layout_width="46dp"
        android:layout_height="46dp"
        android:src="@drawable/mj_red_like_icon"
        android:scaleType="centerInside"
        app:layout_constraintTop_toBottomOf="@+id/coin_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/add_icon"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <Space
        android:id="@+id/like_num_guide"
        android:layout_width="1dp"
        android:layout_height="1dp"
        app:layout_constraintTop_toTopOf="@+id/like_icon"
        app:layout_constraintEnd_toEndOf="@+id/like_icon"
        android:layout_marginEnd="12dp"
        android:layout_marginTop="8dp"/>

    <TextView
        android:id="@+id/like_num_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="4"
        android:textSize="11sp"
        android:textColor="@color/white"
        app:layout_constraintTop_toTopOf="@+id/like_num_guide"
        app:layout_constraintStart_toEndOf="@+id/like_num_guide"/>

    <ImageView
        android:id="@+id/center_line"
        android:layout_width="1dp"
        android:layout_height="28dp"
        android:src="@drawable/mj_game_over_center_line"
        app:layout_constraintTop_toTopOf="@+id/like_icon"
        app:layout_constraintBottom_toBottomOf="@+id/like_icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <ImageView
        android:id="@+id/add_icon"
        android:layout_width="46dp"
        android:layout_height="46dp"
        android:src="@drawable/mj_red_add_icon"
        android:scaleType="centerInside"
        app:layout_constraintTop_toBottomOf="@+id/coin_bg"
        app:layout_constraintStart_toEndOf="@+id/like_icon"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginStart="20dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>