<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <View
        android:id="@+id/player_bg"
        android:layout_width="181dp"
        android:layout_height="72dp"
        android:background="@drawable/mj_settltment_player_bg"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="3dp"/>

    <com.wepie.module.mahjong.view.MJPlayerView
        android:id="@+id/player_info_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="16dp"/>

    <com.wepie.module.mahjong.view.MJGradientColorTextView
        android:id="@+id/player_rank_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="20sp"
        android:textColor="#FFFFFF"
        tools:text="第一"
        app:layout_constraintTop_toTopOf="@+id/player_bg"
        app:layout_constraintEnd_toEndOf="@+id/player_bg"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="9dp"/>

    <com.wepie.module.mahjong.view.custom.AutoIncrementTextView
        android:id="@+id/coin_num"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="2000"
        android:textSize="24sp"
        android:textColor="#FEE893"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        app:layout_constraintTop_toTopOf="@+id/player_rank_tv"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="26dp"
        android:layout_marginEnd="9dp"/>

    <ImageView
        android:id="@+id/coin_icon"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:src="@drawable/mj_money_coin"
        app:layout_constraintTop_toTopOf="@+id/coin_num"
        app:layout_constraintEnd_toStartOf="@+id/coin_num"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="2dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>