<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="42dp"
    android:layout_marginTop="8dp">

    <com.huiwan.widget.CircleFrameLayout
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_centerVertical="true"
        app:cf_radius="24dp">

        <com.wepie.wespy.module.chat.ui.adapter.ConGroupHeadView
            android:id="@+id/group_head_iv"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:background="@drawable/shape_dddee0_corner6"
            android:clipChildren="true"
            android:clipToPadding="true"
            android:visibility="visible" />

    </com.huiwan.widget.CircleFrameLayout>

    <com.huiwan.widget.CustomCircleImageView
        android:id="@+id/player_head_view"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:background="@drawable/shape_head_circle_bg"
        android:padding="2dp"
        android:src="@drawable/default_head_icon" />

    <TextView
        android:id="@+id/player_nick"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="12dp"
        android:layout_toEndOf="@+id/player_head_view"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:lines="1"
        android:maxWidth="102dp"
        android:textColor="#2D2D2D"
        android:textSize="14sp"
        tool:text="花覅keeef~" />

    <TextView
        android:id="@+id/con_tag"
        android:layout_width="wrap_content"
        android:layout_height="18dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        android:layout_toEndOf="@id/player_nick"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingStart="4dp"
        android:paddingTop="1dp"
        android:paddingEnd="4dp"
        android:paddingBottom="1dp"
        android:textSize="11dp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/invite_friend"
        android:layout_width="60dp"
        android:layout_height="26dp"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:background="@drawable/invite_btn_bg"
        android:gravity="center"
        android:text="@string/mj_invite"
        android:textColor="#FFFFFF"
        android:textSize="12sp" />
</RelativeLayout>