<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/item1"
        android:layout_width="80dp"
        android:layout_height="28dp"
        android:background="@drawable/create_unselect_bg"
        android:gravity="center"
        android:includeFontPadding="false"
        android:textColor="#FFFFFF"
        android:textSize="12sp"
        tool:text="10/5" />

    <TextView
        android:id="@+id/item2"
        android:layout_width="80dp"
        android:layout_height="28dp"
        android:layout_marginStart="9dp"
        android:background="@drawable/create_unselect_bg"
        android:gravity="center"
        android:includeFontPadding="false"
        android:textColor="#FFFFFF"
        android:textSize="12sp"
        tool:text="50/20" />

    <TextView
        android:id="@+id/item3"
        android:layout_width="80dp"
        android:layout_height="28dp"
        android:layout_marginStart="9dp"
        android:background="@drawable/create_unselect_bg"
        android:gravity="center"
        android:includeFontPadding="false"
        android:textColor="#FFFFFF"
        android:textSize="12sp"
        tool:text="200/100" />

</LinearLayout>