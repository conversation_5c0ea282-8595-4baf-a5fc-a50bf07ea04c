<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="new_game_style" parent="AppTheme">
        <item name="android:windowBackground">@android:color/black</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <!-- Defined a new style with three items of color. -->
    <style name="mj_match_theme" parent="AppTheme">
        <item name="android:windowTranslucentStatus">true</item>
    </style>

    <style name="mj_share_theme" parent="AppTheme">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Translucent</item>
    </style>
</resources>