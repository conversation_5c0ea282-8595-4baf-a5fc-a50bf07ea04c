package com.wepie.module.mahjong.util;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;

import androidx.annotation.NonNull;
import com.wepie.liblog.main.HLog;
import com.wepie.module.mahjong.event.IGameRoomEvent;
import com.wepie.module.mahjong.tcp.MJPacketHandler;
import com.wepie.module.mahjong.tcp.dispatch.FlowDispatcher;

// mj game do task in sub thread
import java.util.concurrent.atomic.AtomicBoolean;

public class MJThreadHandler extends HandlerThread {
    private static MJThreadHandler instance = new MJThreadHandler("SubHandlerHelper");
    private static String TAG = "SubHandlerHelper";

    private AtomicBoolean hasStart = new AtomicBoolean(false);
    private Handler worker;

    public static final int HANDLE_TCP_RESPONSE = 101;
    public static final int HANDLE_TCP_RESPONSE_MAIN = 102;

    public static MJThreadHandler getInstance() {
        return instance;
    }

    private MJThreadHandler(String name) {
        super(name);
    }

    private Handler mainHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            handle(msg);
        }
    };

    private void initIfNeed(){
        if (!isReady()) {
            init();
        }
    }

    // do task in sub thread
    public boolean handleTaskSub(Object task, long delayMillis) {
        boolean flag = false;
        if (null == task) {
            return flag;
        }
        initIfNeed();
        if (!isReady()) {
            return flag;
        }
        if (task instanceof Runnable) {
            worker.postDelayed((Runnable) task, delayMillis);
            flag = true;
        } else if (task instanceof Message) {
            worker.sendMessageDelayed((Message) task, delayMillis);
            flag = true;
        }
        HLog.d(TAG, HLog.USR, "handle! handled=" + flag);
        return flag;
    }

    public void remove(Runnable runnable) {
        if (null != worker) {
            worker.removeCallbacks(runnable);
        }
        mainHandler.removeCallbacks(runnable);
    }

    public void remove(int what) {
        if (null != worker) {
            worker.removeMessages(what);
        }
        mainHandler.removeMessages(what);
    }

    public void clear() {
        if (null != worker) {
            worker.removeCallbacksAndMessages(null);
        }
        mainHandler.removeMessages(HANDLE_TCP_RESPONSE_MAIN);
    }

    private boolean isReady() {
        return isAlive() && null != worker && hasStart.get();
    }

    private void init() {
        start();
        worker = new Handler(getLooper()) {
            @Override
            public void handleMessage(@NonNull Message msg) {
                handle(msg);
            }
        };
        hasStart.set(true);
    }

    private void handle(Message msg) {
        if (null == msg) {
            return;
        }
        switch (msg.what) {
            case HANDLE_TCP_RESPONSE:
                MJPacketHandler.INSTANCE.handleRspPacket((MJPacketHandler.MJResponse) msg.obj);
                break;
            case HANDLE_TCP_RESPONSE_MAIN:
                FlowDispatcher.postDataFlow((IGameRoomEvent) msg.obj);
                break;
            default:
                break;
        }
        HLog.d(TAG, HLog.USR, "handle! what=" + msg.what);
    }

    public  void handleTaskMain(Message msg){
        if (null == msg) {
            return;
        }
        mainHandler.sendMessage(msg);
    }
}
