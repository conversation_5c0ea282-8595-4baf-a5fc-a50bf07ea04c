package com.wepie.module.mahjong.view.custom;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.FrameLayout;

import androidx.annotation.Nullable;

import com.huiwan.anim.SVGAUtil;
import com.huiwan.base.util.ScreenUtil;
import com.opensource.svgaplayer.SVGAImageView;
import com.wepie.module.mahjong.manager.MJSoundHelper;

public class MJClickAnimView extends FrameLayout {
    private SVGAImageView animView1;
    private SVGAImageView animView2;

    private long lastClickTime = 0L;

    public MJClickAnimView(Context context) {
        this(context, null);
    }

    public MJClickAnimView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MJClickAnimView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public MJClickAnimView(Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);

        initView();
    }

    private void initView() {
        animView1 = new SVGAImageView(getContext());
        animView2 = new SVGAImageView(getContext());

        addView(animView1);
        addView(animView2);

        int width = ScreenUtil.dip2px(90);
        LayoutParams lp = new LayoutParams(width, width);
        animView1.setLayoutParams(lp);
        animView2.setLayoutParams(lp);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (MotionEvent.ACTION_DOWN == event.getAction() && System.currentTimeMillis() - lastClickTime > 300L){
            float x = event.getX();
            float y = event.getY();
            SVGAImageView animView = getAnimView();
            startTouchAnim(animView, x, y);
        }

        return false;
    }

    private SVGAImageView getAnimView(){
        if (animView1 != null && !animView1.isAnimating()){
            return animView1;
        } else if (animView2 != null && !animView2.isAnimating()){
            return animView2;
        } else {
            return null;
        }
    }

    private void startTouchAnim(SVGAImageView animView, float x, float y){
        if (animView == null){
            return;
        }

        int diff = ScreenUtil.dip2px(45);
        animView.setX(x - diff);
        animView.setY(y - diff);

        animView.stopAnimation(true);
        SVGAUtil.playSvga("svga/mahjong/mj_click_anim.svga", animView);

        MJSoundHelper.INSTANCE.playPressButtonAudio();
    }
}
