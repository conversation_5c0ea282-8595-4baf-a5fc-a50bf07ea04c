package com.wepie.module.mahjong.entity;

import java.util.ArrayList;
import java.util.List;

public class MJRoomTableEntity {
    public static final int TABLE_TYPE_BASE = 1;
    public static final int TABLE_TYPE_GOLD = 2;

    private int roomTableType;

    private int di;
    private int tai;
    private int visitorNum;
    private boolean selfIsVisitor;
    private int rid;
    private int roomType;
    private String bankCountStr;

    private List<Integer> goldCards = new ArrayList<>();
    private boolean isStartAnim = false;

    public MJRoomTableEntity(int roomTableType){
        this.roomTableType = roomTableType;
    }

    public int getRoomTableType() {
        return roomTableType;
    }

    public void setRoomTableType(int roomTableType) {
        this.roomTableType = roomTableType;
    }

    public int getDi() {
        return di;
    }

    public void setDi(int di) {
        this.di = di;
    }

    public int getTai() {
        return tai;
    }

    public void setTai(int tai) {
        this.tai = tai;
    }

    public int getVisitorNum() {
        return visitorNum;
    }

    public void setVisitorNum(int visitorNum) {
        this.visitorNum = visitorNum;
    }

    public boolean isSelfIsVisitor() {
        return selfIsVisitor;
    }

    public void setSelfIsVisitor(boolean selfIsVisitor) {
        this.selfIsVisitor = selfIsVisitor;
    }

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getRoomType() {
        return roomType;
    }

    public void setRoomType(int roomType) {
        this.roomType = roomType;
    }

    public String getBankCountStr() {
        return bankCountStr;
    }

    public void setBankCountStr(String bankCountStr) {
        this.bankCountStr = bankCountStr;
    }

    public List<Integer> getGoldCards() {
        return goldCards;
    }

    public void setGoldCards(List<Integer> goldCards) {
        this.goldCards = goldCards;
    }

    public boolean isStartAnim() {
        return isStartAnim;
    }

    public void setStartAnim(boolean startAnim) {
        isStartAnim = startAnim;
    }

    @Override
    public String toString() {
        return "MJRoomTableEntity{" +
                "roomTableType=" + roomTableType +
                ", di=" + di +
                ", tai=" + tai +
                ", visitorNum=" + visitorNum +
                ", selfIsVisitor=" + selfIsVisitor +
                ", rid=" + rid +
                ", roomType=" + roomType +
                ", bankCountStr='" + bankCountStr + '\'' +
                ", goldCards=" + goldCards +
                ", isStartAnim=" + isStartAnim +
                '}';
    }
}
