package com.wepie.module.mahjong.view.custom;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import java.util.ArrayList;

/**
 * Created by geeksammao on 24/10/2017.
 */

public class MJSpeakerAnimView extends View {
    private int cx;
    private int cy;
    private int radius;
    private boolean isAniming = false;

    private Paint paint;
    private ArrayList<RoundInfo> drawArray = new ArrayList<>();
    private ArrayList<RoundInfo> removeArray = new ArrayList<>();
    private ArrayList<RoundInfo> recycleArray = new ArrayList<>();
    private long lastAddRoundTime;
    private long animEndTime;
    private static float RATE = 1.6f;

    public MJSpeakerAnimView(Context context) {
        super(context);
        init();
    }

    public MJSpeakerAnimView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        paint = new Paint();
        paint.setAntiAlias(true);
        paint.setColor(Color.parseColor("#ffffff"));
    }

    public void init(int cx, int cy, int radius) {
        this.cx = cx;
        this.cy = cy;
        this.radius = radius;
    }

    //初始化在post内调用，才能拿到view位置
    public void initFromView(final View view) {
        post(() -> init((int) view.getX() + view.getWidth() / 2, (int) view.getY() + view.getHeight() / 2, view.getHeight() / 2));
    }

    public void startAnim() {
        RATE = 1.6f;
        if(isAniming) {
            animEndTime = System.currentTimeMillis() + 1600;
            return;
        }
        isAniming = true;
        animEndTime = System.currentTimeMillis() + 1600;
        drawArray.clear();
        postInvalidate();
    }

    public void startMatchAnim() {
        RATE = 2.6f;
        if(isAniming) {
            animEndTime = System.currentTimeMillis() + 1000 * 60 * 3;
            return;
        }
        isAniming = true;
        animEndTime = System.currentTimeMillis()  + 1000 * 60 * 3;
        drawArray.clear();
        postInvalidate();
    }

    public void stopAnim() {
        isAniming = false;
        if(callback != null) callback.onAnimStop(this);
    }

    public boolean isAniming() {
        return isAniming;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        if(drawArray.size() == 0) {
            drawArray.add(getRoundInfo());
            lastAddRoundTime = System.currentTimeMillis();
        }else if(System.currentTimeMillis() - lastAddRoundTime > 750){
            drawArray.add(getRoundInfo());
            lastAddRoundTime = System.currentTimeMillis();
        }

        removeArray.clear();
        int size = drawArray.size();
        for(int i=0;i<size;i++) {
            RoundInfo roundInfo = drawArray.get(i);
            float alpha = roundInfo.getAlpha();
            float rate = roundInfo.getRadiusRate();
            paint.setAlpha((int) (alpha * 0.3f * 255));
            canvas.drawCircle(cx, cy, radius * rate, paint);

            if(alpha <= 0) removeArray.add(roundInfo);
        }
        drawArray.removeAll(removeArray);
        recycleArray.addAll(removeArray);

        if(System.currentTimeMillis() >= animEndTime) {
            stopAnim();
        }else {
            postInvalidate();
        }
    }

    public RoundInfo getRoundInfo() {
        RoundInfo roundInfo;
        if(recycleArray.size() > 0) {
            roundInfo = recycleArray.remove(0);
        }else {
            roundInfo = new RoundInfo();
        }
        roundInfo.startTime = System.currentTimeMillis();
        return roundInfo;
    }

    public static class RoundInfo {
        public long startTime;

        public float getAlpha() {
            long dt = System.currentTimeMillis() - startTime;
            if(dt <= 600) return 1;
            if(dt <= 1600) return 1- (dt - 600) * (1.6f - 0.6f) / 1000;
            return 0;
        }

        public float getRadiusRate() {
            long dt = System.currentTimeMillis() - startTime;
            float rate = 1 + (RATE - 1) * dt / 1600;
            if(rate > RATE) rate = RATE;
            return rate;
        }
    }

    private StopCallback callback;
    public void registerStopCallback(StopCallback callback) {
        this.callback = callback;
    }
    public interface StopCallback {
        void onAnimStop(View selfView);
    }
}