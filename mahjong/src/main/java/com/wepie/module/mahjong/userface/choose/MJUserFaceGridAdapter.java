package com.wepie.module.mahjong.userface.choose;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.configservice.model.voiceroom.FaceInfo;
import com.wepie.module.mahjong.R;

import java.util.ArrayList;
import java.util.List;

public class MJUserFaceGridAdapter extends RecyclerView.Adapter<MJUserFaceItemHolder> {
    private List<FaceInfo> faceInfoList = new ArrayList<>();
    private OnMJUserFaceClickListener listener;
    private boolean enable = false;

    public void refresh(List<FaceInfo> faceInfoList) {
        this.faceInfoList.clear();
        this.faceInfoList.addAll(faceInfoList);
        this.notifyDataSetChanged();
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
        notifyDataSetChanged();
    }

    public void setListener(OnMJUserFaceClickListener listener) {
        this.listener = listener;
    }

    @Override
    public void onBindViewHolder(@NonNull MJUserFaceItemHolder holder, int position) {
        holder.onBind(faceInfoList.get(position), listener);
        holder.setEnable(enable);
    }

    @NonNull
    @Override
    public MJUserFaceItemHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new MJUserFaceItemHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.mj_face_item_view, parent, false));
    }

    @Override
    public int getItemCount() {
        return faceInfoList.size();
    }
}