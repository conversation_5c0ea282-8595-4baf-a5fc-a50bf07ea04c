package com.wepie.module.mahjong.entity;

import java.util.ArrayList;
import java.util.List;

/**
 * 本圈游戏结算信息
 */
public class MJRoundResult {
    public boolean hasWinner_;      //本圈结束是否有赢家
    public List<RoundResultPlayerInfo> players_ = new ArrayList<>(); //本圈每个玩家的结算信息
    public MJResultTaiShu taiShu_;
    public int huPai_;

    @Override
    public String toString() {
        return "MJRoundResult{" +
                "hasWinner_=" + hasWinner_ +
                ", players_=" + players_ +
                ", taiShu_=" + taiShu_ +
                ", huPai_=" + huPai_ +
                '}';
    }

    public static class MJResultTaiShu {
        public int total_;
        public List<MJTaiItem> taiItems_ = new ArrayList<>();

        @Override
        public String toString() {
            return "MJResultTaiShu{" +
                    "total_=" + total_ +
                    ", taiItems_=" + taiItems_ +
                    '}';
        }
    }

    public static class MJTaiItem {
        public String type_;
        public int number_;

        @Override
        public String toString() {
            return "MJTaiItem{" +
                    "type_='" + type_ + '\'' +
                    ", number_=" + number_ +
                    '}';
        }
    }
}
