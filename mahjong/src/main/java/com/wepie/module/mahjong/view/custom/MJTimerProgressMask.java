package com.wepie.module.mahjong.view.custom;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.os.CountDownTimer;
import android.util.AttributeSet;
import android.view.View;

import com.huiwan.base.util.ScreenUtil;

public class MJTimerProgressMask extends View {
    private static final int MAX = 1000;
    private static final String TAG = MJTimerProgressMask.class.getSimpleName();

    private int progressColor;
    private CountDownTimer timer;
    private long totalTime;

    /**
     * 最大进度
     */
    private int max;

    /**
     * 当前进度
     */
    private int progress = 0;

    /**
     * 是否是倒计时
     */
    private boolean isCountDown = false;
    public void setCountDown(boolean isCountDown){
        this.isCountDown = isCountDown;
        progress = max;
    }

    private Paint maskPaint;
    private RectF rect;
    private float cornerRadius = ScreenUtil.dip2px(10);

    public MJTimerProgressMask(Context context) {
        this(context, null);
    }

    public MJTimerProgressMask(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MJTimerProgressMask(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);

        max = MAX;
        rect = new RectF(0, 0, getWidth(), getHeight());
        maskPaint = new Paint();
        maskPaint.setStyle(Paint.Style.FILL);
        maskPaint.setColor(progressColor);
        maskPaint.setAntiAlias(true);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        rect.left = 0;
        rect.top = 0;
        rect.right = getWidth() * progress / max;
        rect.bottom = getHeight();

        canvas.drawColor(Color.TRANSPARENT);
        canvas.drawRoundRect(rect, cornerRadius, cornerRadius, maskPaint);
    }

    public synchronized int getMax() {
        return max;
    }

    /**
     * 设置进度的最大值
     * @param max max
     */
    public void setMax(int max) {
        if(max > 0){
            this.max = max;
        }
    }

    /**
     * 获取进度.需要同步
     * @return 当前进度
     */
    public int getProgress() {
        return progress;
    }

    /**
     * 设置进度
     * @param progress 进度
     */
    public void setProgress(int progress) {
        if(progress < 0){
            this.progress = 0;
            return;
        }
        if(progress > max){
            progress = max;
        }
        if(progress <= max){
            this.progress = progress;
            postInvalidate();
        }
    }

    public void setTotalTime(long totalTime){
        this.totalTime = totalTime;
    }

    private void onUpdate(long millisUntilFinished){
        if (totalTime != 0){
            if (isCountDown){
                setProgress((int)(millisUntilFinished * max / totalTime));
            } else {
                setProgress((int)((totalTime - millisUntilFinished) * max / totalTime));
            }
        }
    }

    public void startTimer(final OnTimerListener listener){
        long timeInMillis = totalTime * progress / max;
        if (!isCountDown){
            timeInMillis = totalTime - timeInMillis;
        }
        if (timer == null && timeInMillis > 0){
            timer = new CountDownTimer(timeInMillis, 20) {
                @Override
                public void onTick(long millisUntilFinished) {
                    onUpdate(millisUntilFinished);
                }

                @Override
                public void onFinish() {
                    if (isCountDown){
                        onUpdate(totalTime);
                    } else {
                        onUpdate(0);
                    }
                    if (listener != null){
                        listener.onFinish();
                    }
                    timer = null;
                }
            };
            timer.start();
        } else {
            if (listener != null){
                listener.onFinish();
            }
        }
    }

    public void resetTimerAndProgress() {
        if (timer != null) timer.cancel();
        timer = null;
        if (isCountDown){
            setProgress(max);
        } else {
            setProgress(0);
        }
    }

    public void setProgressColor(int progressColor) {
        this.progressColor = progressColor;
        maskPaint.setColor(progressColor);
    }

    public boolean isStart() {
        return timer != null;
    }


    public interface OnTimerListener{
        /**
         * 计时结束
         */
        void onFinish();
    }
}
