package com.wepie.module.mahjong.entity;

import java.util.ArrayList;
import java.util.List;

/**
 * 每个玩家每圈的结算信息
 */
public class RoundResultPlayerInfo {
    public int uid_;  //玩家uid
    public int rank_; //本圈结束玩家排名
    public int coin_; //本圈结束前玩家金币
    public int winCoin_; //本圈赢取金币
    public int resultType_; //本圈结束玩家结局类型，例如：放炮、自摸、胡牌 等等
    public int bankInRow_;  //连庄次数
    public List<Integer> huPaiList_ = new ArrayList<>(); //胡牌列表
    public RoundResultTaiShu taiShu_; //台数统计信息

    @Override
    public String toString() {
        return "RoundResultPlayerInfo{" +
                "uid_=" + uid_ +
                ", rank_=" + rank_ +
                ", coin_=" + coin_ +
                ", winCoin_=" + winCoin_ +
                ", resultType_=" + resultType_ +
                ", bankInRow_=" + bankInRow_ +
                ", huPaiList_=" + huPaiList_ +
                ", taiShu_=" + taiShu_ +
                '}';
    }

    /**
     * 台数统计信息
     */
    public static class RoundResultTaiShu{
        public int total_; //总台数
        public List<RoundResultTaiItem> taiItems_ = new ArrayList<>(); //符合的牌型信息

        @Override
        public String toString() {
            return "RoundResultTaiShu{" +
                    "total_=" + total_ +
                    ", taiItems_=" + taiItems_ +
                    '}';
        }
    }

    /**
     * 牌型信息
     */
    public static class RoundResultTaiItem{
        private String type_; //牌型名称
        private int number_;  //该牌型赢得台数

        @Override
        public String toString() {
            return "RoundResultTaiItem{" +
                    "type_='" + type_ + '\'' +
                    ", number_=" + number_ +
                    '}';
        }
    }
}
