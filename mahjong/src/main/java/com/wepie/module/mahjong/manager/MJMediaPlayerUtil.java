package com.wepie.module.mahjong.manager;

import android.media.AudioManager;
import android.media.MediaPlayer;
import android.text.TextUtils;

import androidx.annotation.RawRes;

import com.huiwan.base.LibBaseUtil;
import com.wepie.liblog.main.HLog;

public class MJMediaPlayerUtil {
    private static MediaPlayer mediaPlayer;

    public static void playLocalFile(String path, boolean isLoop) {
        HLog.d("yyyy", "----->MediaPlayerUtil playLocalFile path=" + path+" isLoop="+isLoop);
        if(TextUtils.isEmpty(path)) return;

        release();
        try {
            mediaPlayer = new MediaPlayer();
            mediaPlayer.setDataSource(path);
            mediaPlayer.setLooping(isLoop);
            mediaPlayer.setOnCompletionListener(mediaPlayer -> release());
            mediaPlayer.prepare();
            mediaPlayer.start();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static int playingRawId = -1;
    public static void playRawRes(@RawRes int rawId, boolean isLoop) {
        HLog.d("yyyy", "----->MediaPlayerUtil playLocalFile path=" + rawId+" isLoop="+isLoop);

        release();
        try {
            mediaPlayer = MediaPlayer.create(LibBaseUtil.getApplication(), rawId);
            mediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
            mediaPlayer.setLooping(isLoop);
            mediaPlayer.setVolume(0.8F, 0.8F);
            mediaPlayer.setOnCompletionListener(mediaPlayer -> release());
            mediaPlayer.start();
            playingRawId = rawId;

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void playRawRes(@RawRes int rawId, boolean isLoop, float volume) {
        HLog.d("yyyy", "----->MediaPlayerUtil playLocalFile path=" + rawId+" isLoop="+isLoop);

        release();
        try {
            mediaPlayer = MediaPlayer.create(LibBaseUtil.getApplication(), rawId);
            mediaPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
            mediaPlayer.setLooping(isLoop);
            mediaPlayer.setVolume(volume, volume);
            mediaPlayer.setOnCompletionListener(mediaPlayer -> release());
            mediaPlayer.start();
            playingRawId = rawId;

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void setVolume(float volume){
        if (mediaPlayer != null && mediaPlayer.isPlaying()){
            mediaPlayer.setVolume(volume, volume);
        }
    }

    public static boolean isPlaying(@RawRes int rawId){
        if (playingRawId == -1 || playingRawId != rawId){
            return false;
        }

        if(mediaPlayer != null){
            return mediaPlayer.isPlaying();
        }

        return false;
    }

    public static void release() {
        try {
            if(mediaPlayer != null){
                if (mediaPlayer.isPlaying()) {
                    mediaPlayer.stop();
                }
                mediaPlayer.release();
                mediaPlayer = null;
                playingRawId = -1;
            }
        }catch (Exception e) {
            e.printStackTrace();
            HLog.e("",HLog.CLR,"{}",e);
        }
    }
}
