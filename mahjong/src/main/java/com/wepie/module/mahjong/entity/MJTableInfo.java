package com.wepie.module.mahjong.entity;

import java.util.ArrayList;
import java.util.List;

public class MJTableInfo {
    public BetInfo bet_; //台、底信息
    public List<Integer> dices_ = new ArrayList<>(); //骰子点数
    public BankInfo bank_; //庄家信息
    public int circleFeng_;
    public int bankFeng_;
    public int leftPaiCount_;
    public long baseActionMs_;
    public long extraActionMs_;
    public int forceQuitCoin_;
    public String feeTips_;
    public List<Integer> goldTiles_;
    public String goldTileTip_;

    //台、底信息
    public static class BetInfo{
        public int di_;     //本局游戏底是多少金币
        public int tai_;    //本局游戏每台多少金币

        @Override
        public String toString() {
            return "BetInfo{" +
                    "di_=" + di_ +
                    ", tai_=" + tai_ +
                    '}';
        }
    }

    //庄家信息
    public static class BankInfo{
        public int uid_;            //庄家uid
        public int countInRow_;     //庄家连庄数

        @Override
        public String toString() {
            return "BankInfo{" +
                    "uid_=" + uid_ +
                    ", countInRow_=" + countInRow_ +
                    '}';
        }
    }

    public static class bytes {
        public List<Integer> bytes = new ArrayList<Integer>();
    }

    @Override
    public String toString() {
        return "MJTableInfo{" +
                "bet_=" + bet_ +
                ", dices_=" + dices_ +
                ", bank_=" + bank_ +
                ", circleFeng_=" + circleFeng_ +
                ", bankFeng_=" + bankFeng_ +
                ", leftPaiCount_=" + leftPaiCount_ +
                ", baseActionMs_=" + baseActionMs_ +
                ", extraActionMs_=" + extraActionMs_ +
                ", forceQuitCoin_=" + forceQuitCoin_ +
                ", feeTips_='" + feeTips_ + '\'' +
                ", goldTiles_=" + goldTiles_ +
                ", goldTileTip_='" + goldTileTip_ + '\'' +
                '}';
    }
}
