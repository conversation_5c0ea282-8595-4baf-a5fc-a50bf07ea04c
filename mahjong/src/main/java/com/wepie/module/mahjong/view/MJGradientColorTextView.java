package com.wepie.module.mahjong.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Shader;
import android.util.AttributeSet;

import androidx.annotation.Nullable;

public class MJGradientColorTextView extends CustomTextView {
    private int startColor = Color.parseColor("#F2EE19");
    private int endColor = Color.parseColor("#FF7532");

    public MJGradientColorTextView(Context context) {
        super(context);
    }

    public MJGradientColorTextView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public MJGradientColorTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void setColor(int startColor, int endColor){
        this.startColor = startColor;
        this.endColor = endColor;
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        if (changed) {
            getPaint().setShader(new LinearGradient(0, 0, 0, getHeight(),
                    startColor, endColor, Shader.TileMode.CLAMP));
        }
    }


}
