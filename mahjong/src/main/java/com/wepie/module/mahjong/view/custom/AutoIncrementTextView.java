package com.wepie.module.mahjong.view.custom;

import android.animation.ValueAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.animation.AccelerateInterpolator;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;

import com.huiwan.base.util.TextUtil;
import com.huiwan.base.util.ToastUtil;

import java.util.regex.Pattern;

public class AutoIncrementTextView extends CustomTextView {
    public final static long DEFAULT_ANIM_DURATION = 250;

    public AutoIncrementTextView(@NonNull Context context) {
        super(context);
    }

    public AutoIncrementTextView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public AutoIncrementTextView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void riseNum(int num){
        startAnimation(num, DEFAULT_ANIM_DURATION);
    }

    public void riseNum(int num, long duration){
        startAnimation(num, duration);
    }

    private void startAnimation(int num, long duration){
        ValueAnimator animator = null;

        String content = (String) getText();
        if (TextUtil.isEmpty(content)){
            content = String.valueOf(0);
        }

        if (!isNumerical(content)){
            ToastUtil.debugShow("content is number !");
            return;
        }

        int contentNum = Integer.parseInt(content);
        int finalNum = contentNum + num;

        animator = ValueAnimator.ofInt(contentNum, finalNum);
        animator.addUpdateListener(valueAnimator -> {
            int curValue = (int)valueAnimator.getAnimatedValue();
            setText(String.valueOf(curValue));
        });
        animator.setDuration(duration);
        animator.setInterpolator(new AccelerateInterpolator());
        animator.start();
    }

    //用于判断字符串是否是纯数字
    private boolean isNumerical(CharSequence str){
        Pattern pattern = Pattern.compile("-?[0-9]+\\.?[0-9]*");
        boolean isNum = pattern.matcher(str).matches();

        return isNum;
    }
}
