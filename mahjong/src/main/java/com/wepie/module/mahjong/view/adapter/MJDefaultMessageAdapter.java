package com.wepie.module.mahjong.view.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.wepie.module.mahjong.R;

import java.util.ArrayList;
import java.util.List;

public class MJDefaultMessageAdapter extends RecyclerView.Adapter<MJDefaultMessageAdapter.ViewHolder> {
    private List<String> messageList = new ArrayList<>();
    private Context context;

    private OnItemClickLitener mOnItemClickLitener;

    public MJDefaultMessageAdapter(Context context){
        this.context = context;
    }

    public void addItems(List<String> messages) {
        this.messageList.clear();
        this.messageList.addAll(messages);
        notifyDataSetChanged();
    }

    public String getMessage(int position){
        String message = "";

        if (messageList.size() > 0 && position < messageList.size()){
            message = messageList.get(position);
        }

        return message;
    }

    public void setOnItemClickLitener(OnItemClickLitener mOnItemClickLitener){
        this.mOnItemClickLitener = mOnItemClickLitener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(context).inflate(R.layout.mj_default_message_item_layout, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        String message = messageList.get(position);
        holder.messageTv.setText(message);

        holder.messageTv.setOnClickListener(v -> {
            if (mOnItemClickLitener != null){
                mOnItemClickLitener.onItemClick(v, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return messageList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder{
        public TextView messageTv;
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            messageTv = itemView.findViewById(R.id.message_tv);
        }
    }

    public interface OnItemClickLitener{
        void onItemClick(View view, int position);
    }
}
