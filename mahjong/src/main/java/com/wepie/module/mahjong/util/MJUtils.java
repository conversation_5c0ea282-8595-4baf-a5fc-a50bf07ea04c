package com.wepie.module.mahjong.util;

import android.Manifest;
import android.app.Activity;
import android.content.Context;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.constants.GameType;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.WebApi;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.store.PrefUtil;
import com.wepie.lib.api.plugins.hwroom.HwRoomApi;
import com.wepie.liblog.main.HLog;
import com.wepie.libpermission.PermissionCallback;
import com.wepie.libpermission.WPPermission;
import com.wepie.module.mahjong.R;
import com.wepie.module.mahjong.dialog.MJVolumeDialog;
import com.wepie.wespy.helper.dialog.PermissionDialog;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.model.entity.EnterRoomInfo;
import com.wepie.wespy.net.tcp.packet.HWMatchPackets;
import com.wepie.wespy.net.tcp.sender.HWMatchPacketSender;

import java.util.List;

public class MJUtils {
    public static final String TAG = "MJUtils";
    public static final String REGION = "ch";
    public static final String MJ_SOUND_SWITCH = "mj_sound_switch";
    public static final String MJ_MIC_SWITCH = "mj_mic_switch";

    public static void showVoiceAdjustDialog(Context context){
        MJVolumeDialog dialog = new MJVolumeDialog(context);
        dialog.show();
    }

    public static void jumpToHelp(Context context) {
        GameConfig config = ConfigHelper.getInstance().getGameConfig().getGameConfig(GameType.GAME_TYPE_MJ);
        String url = config.getHelpUrl();
        ApiService.of(WebApi.class).gotoWebActivity(context, url);
        HLog.d(TAG, HLog.USR, "helpClick! url=" + url);
    }

    public static void jumpMJHistory(Context context){
        GameConfig config = ConfigHelper.getInstance().getGameConfig().getGameConfig(GameType.GAME_TYPE_MJ);
        String url = config.getRecordUrl();
        ApiService.of(WebApi.class).gotoWebActivity(context, url);
        HLog.d(TAG, HLog.USR, "historyClick! url=" + url);
    }

    public static void match(int betLevel, Activity context) {
        WPPermission.with(context)
                .permission(Manifest.permission.RECORD_AUDIO)
                .requestDialogTip(ResUtil.getStr(R.string.game_common_request_mic_tips))
                .request(new PermissionCallback() {
                    @Override
                    public void hasPermission(List<String> granted, boolean isAll, boolean alreadyHas) {
                        if (isAll) {
                            ProgressDialogUtil p = new ProgressDialogUtil();
                            p.showLoading(context, "", false);
                            HWMatchPacketSender.reMatch(betLevel, 0, GameType.GAME_TYPE_MJ, new SeqCallback() {
                                @Override
                                public void onSuccess(RspHeadInfo head) {
                                    p.hideLoading();
                                    Activity activity = ContextUtil.getActivityFromContext(context);
                                    if (null == activity || activity.isFinishing()) {
                                        return;
                                    }
                                    int rid = ((HWMatchPackets.HWSyncMatchRsp) head.message).getRid();
                                    HLog.d(TAG, HLog.USR, "match! rid=" + rid);
                                    EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildSearchRoom(context, rid).setGameType(GameType.GAME_TYPE_MJ);
                                    ApiService.of(HwRoomApi.class).handleEnterRoom(enterRoomInfo);
                                }

                                @Override
                                public void onFail(RspHeadInfo head) {
                                    p.hideLoading();
                                    HLog.d(TAG, HLog.USR, "match error! code=" + head.code + ", msg=" + head.desc);
                                }
                            });
                        } else {
                            ToastUtil.show(R.string.mj_request_mic_tips);
                        }
                    }

                    @Override
                    public void noPermission(List<String> denied, boolean quick) {
                        if (quick) {
                            PermissionDialog.showJumpPermissionDialog(context, ResUtil.getStr(R.string.mj_request_mic_tips));
                        } else {
                            ToastUtil.show(R.string.mj_request_mic_tips);
                        }
                    }
                });
    }

    public static String addRegionPrefix(Object number) {
        return REGION + number;
    }

    public static boolean getMicSwitch() {
        boolean flag = false;
        flag = PrefUtil.getInstance().getBoolean(MJ_MIC_SWITCH, false, true);
        return flag;
    }

    public static void setMicSwitch(boolean flag) {
        PrefUtil.getInstance().setBoolean(MJ_MIC_SWITCH, flag, true);
    }

    public static boolean getSoundSwitch() {
        boolean flag = false;
        flag = PrefUtil.getInstance().getBoolean(MJ_SOUND_SWITCH, true, true);
        return flag;
    }

    public static void setSoundSwitch(boolean flag) {
        PrefUtil.getInstance().setBoolean(MJ_SOUND_SWITCH, flag, true);
    }
}
