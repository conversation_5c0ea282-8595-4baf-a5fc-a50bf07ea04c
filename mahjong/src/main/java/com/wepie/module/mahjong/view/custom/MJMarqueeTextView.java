package com.wepie.module.mahjong.view.custom;

import android.content.Context;
import android.util.AttributeSet;

import androidx.appcompat.widget.AppCompatTextView;

/**
 * date 2019-05-14
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class MJMarqueeTextView extends CustomTextView {

    public MJMarqueeTextView(Context context) {
        super(context);
    }

    public MJMarqueeTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public MJMarqueeTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean isFocused() {
        return true;
    }
}
