package com.wepie.module.mahjong.view;

import android.content.res.Configuration;
import androidx.annotation.Nullable;

import com.huiwan.user.entity.FriendInfo;
import com.wepie.wespy.model.entity.HWRoomNotice;
import com.wepie.wespy.model.entity.hwroom.HWRoomInfo;
import com.wepie.wespy.model.entity.hwroom.HWRoomMsg;

import java.util.List;

public interface INewGameView {
    void updateRoomInfo(HWRoomInfo roomInfo);
    void onResume();
    void onPause(boolean isFinish);
    void onConfigurationChanged(Configuration newConfig);
    void onClear();
    @Nullable
    Object getGameInfo();

    void onPushRoomMsg(HWRoomMsg msgInfo);
    void onSelfSendMsg();
    void handleRoomNotice(HWRoomNotice roomNotice);
}
