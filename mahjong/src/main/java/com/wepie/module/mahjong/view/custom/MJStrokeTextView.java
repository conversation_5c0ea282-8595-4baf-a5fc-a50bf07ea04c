package com.wepie.module.mahjong.view.custom;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.util.ScreenUtil;
import com.wepie.module.mahjong.R;

public class MJStrokeTextView extends CustomTextView {

    private TextView borderText ;///用于描边的TextView

    private int strokeColor;
    private float strokeWidth;

    public MJStrokeTextView(@NonNull Context context) {
        this(context, null);
    }

    public MJStrokeTextView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, android.R.attr.textViewStyle);
    }

    public MJStrokeTextView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        borderText = new CustomTextView(context,attrs);

        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.MJStrokeTextView);
        strokeColor = a.getColor(R.styleable.MJStrokeTextView_mj_stroke_color, Color.parseColor("#231C79"));
        strokeWidth = a.getDimension(R.styleable.MJStrokeTextView_mj_stroke_width, ScreenUtil.dip2px(1));

        init();
    }

    public void init(){
        TextPaint tp1 = borderText.getPaint();
        tp1.setStrokeWidth(strokeWidth);      //设置描边宽度
        tp1.setStyle(Paint.Style.STROKE);     //对文字只描边
        borderText.setTextColor(strokeColor);  //设置描边颜色
        borderText.setGravity(getGravity());
    }

    public void setStrokeColor(int strokeColor){
        this.strokeColor = strokeColor;
        if (borderText != null){
            borderText.setTextColor(strokeColor);
        }
        invalidate();
    }

    @Override
    public void setText(CharSequence text, BufferType type) {
        if (borderText != null){
            borderText.setText(text, type);
        }
        super.setText(text, type);
    }

    @Override
    public void setTypeface(@Nullable Typeface tf) {
        super.setTypeface(tf);

        if (borderText != null){
            borderText.setTypeface(tf);
        }
    }

    @Override
    public void setLayoutParams (ViewGroup.LayoutParams params){
        super.setLayoutParams(params);
        borderText.setLayoutParams(params);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        CharSequence tt = borderText.getText();

        //两个TextView上的文字必须一致
        if(tt== null || !tt.equals(this.getText())){
            borderText.setText(getText());
            this.postInvalidate();
        }
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        borderText.measure(widthMeasureSpec, heightMeasureSpec);
    }

    protected void onLayout (boolean changed, int left, int top, int right, int bottom){
        super.onLayout(changed, left, top, right, bottom);
        borderText.layout(left, top, right, bottom);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        borderText.draw(canvas);
        super.onDraw(canvas);
    }

}