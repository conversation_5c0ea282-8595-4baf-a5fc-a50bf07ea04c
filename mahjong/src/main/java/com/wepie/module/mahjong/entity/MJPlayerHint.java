package com.wepie.module.mahjong.entity;

import java.util.ArrayList;
import java.util.List;

public class MJPlayerHint {
    public int hintType_;
    public int pengPai_;
    public HintChiData chiData_;
    public HintGangData gangData_;
    public HintTingData tingData_;
    public long actionEndTimestamp_;

    @Override
    public String toString() {
        return "MJPlayerHint{" +
                "hintType_=" + hintType_ +
                ", pengPai_=" + pengPai_ +
                ", chiData_=" + chiData_ +
                ", gangData_=" + gangData_ +
                ", tingData_=" + tingData_ +
                ", actionEndTimestamp_=" + actionEndTimestamp_ +
                '}';
    }

    public static class HintChiData {
        public int otherPai_;
        public List<ChiSeq> combinations_ = new ArrayList<>();

        @Override
        public String toString() {
            return "HintChiData{" +
                    "otherPai_=" + otherPai_ +
                    ", combinations_=" + combinations_ +
                    '}';
        }

        public static class ChiSeq {
            public List<Integer> bytes = new ArrayList<>();

            @Override
            public String toString() {
                return "ChiSeq{" +
                        "bytes=" + bytes +
                        '}';
            }
        }
    }

    public static class HintTingData {
        public List<HintTingDataItem> items_ = new ArrayList<>();

        @Override
        public String toString() {
            return "HintTingData{" +
                    "items_=" + items_ +
                    '}';
        }

        public static class HintTingDataItem {
            public int chuPai_;
            public List<HintTingHuPai> huPaiItems_ = new ArrayList<>();

            @Override
            public String toString() {
                return "HintTingDataItem{" +
                        "chuPai_=" + chuPai_ +
                        ", huPaiList_=" + huPaiItems_ +
                        '}';
            }
        }

        public static class HintTingHuPai {
            public int huPai_;
            public int taiShu_;
            public int leftCount_;

            @Override
            public String toString() {
                return "HintTingHuPai{" +
                        "huPai_=" + huPai_ +
                        ", taiShu_=" + taiShu_ +
                        ", leftCount_=" + leftCount_ +
                        '}';
            }
        }
    }

    public static class HintGangData {
        public List<HintGangDataItem> items_ = new ArrayList<>();

        @Override
        public String toString() {
            return "HintGangData{" +
                    "items_=" + items_ +
                    '}';
        }

        public static class HintGangDataItem {
            public int gangType_;
            public int gangPai_;

            @Override
            public String toString() {
                return "HintGangDataItem{" +
                        "gangType_=" + gangType_ +
                        ", gangPai_=" + gangPai_ +
                        '}';
            }
        }
    }
}
