package com.wepie.module.mahjong.view;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.PaintFlagsDrawFilter;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.user.LoginHelper;
import com.wepie.liblog.main.HLog;
import com.wepie.module.mahjong.R;
import com.wepie.module.mahjong.constants.MJCardConstant;
import com.wepie.module.mahjong.entity.CardInfo;
import com.wepie.module.mahjong.entity.MJPlayerHint;
import com.wepie.module.mahjong.entity.MJPutCardInfo;
import com.wepie.module.mahjong.manager.MJCardBMHelper;
import com.wepie.module.mahjong.manager.MJShowCardHelper;
import com.wepie.module.mahjong.manager.MJSoundHelper;
import com.wepie.module.mahjong.viewmodel.MahjongGameViewModel;

import java.util.List;

public class MJCardView extends View {

    private Paint mPaint;
    private int type = MJShowCardHelper.TYPE_BOTTOM_SHOU;
    private CardInfo cardInfo;
    private boolean isShowPai = true;
    private boolean isVisitor = false;
    private MahjongGameViewModel viewModel;
    private boolean canOption;

    private boolean isCanChange = false;

    private MJPlayerHint.HintTingData.HintTingDataItem tingDataItem;
    private OnSelectListener selectListener;
    private OnTingPaiListener tingPaiListener;

    public MJCardView(Context context) {
        super(context);

        init();
    }

    public MJCardView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);

        init();
    }

    public MJCardView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        init();
    }

    public MJCardView(Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);

        init();
    }

    public void showPai(boolean showPai){
        isShowPai = showPai;
    }

    public void setIsVisitor(boolean isVisitor){
        this.isVisitor = isVisitor;
    }

    public void setCardType(int cardType, CardInfo cardInfo){
        this.type = cardType;
        this.cardInfo = cardInfo;
    }

    public void setCanOption(boolean canOption){
        this.canOption = canOption;
        if (canOption && getContext() instanceof BaseActivity){
            BaseActivity activity = (BaseActivity) getContext();
            viewModel.getSelectCard().observe(activity, selectCard -> {
                if (isCanChange){
                    return;
                }

                if (selectCard != null && cardInfo != null && cardInfo.getOrginIndex() == selectCard.getOrginIndex()){
                    setY(0);
                } else {
                    setY(ScreenUtil.dip2px(10));
                }
            });
        }
    }

    public void setCanChange(boolean isCanChange){
        this.isCanChange = isCanChange;
        if (isCanChange && getContext() instanceof BaseActivity){
            BaseActivity activity = (BaseActivity) getContext();
            viewModel.getChangeCards().observe(activity, cardInfos -> {
                if (viewModel.isInChangeCards(cardInfo)){
                    setY(0);
                } else {
                    setY(ScreenUtil.dip2px(10));
                }
            });
        }
    }

    public CardInfo getCardInfo(){
        return cardInfo;
    }

    public void setTingDataItem(MJPlayerHint.HintTingData.HintTingDataItem tingDataItem){
        this.tingDataItem = tingDataItem;
        invalidate();
    }

    public void setSelectListener(OnSelectListener selectListener){
        this.selectListener = selectListener;
    }

    public void setTingListener(OnTingPaiListener tingPaiListener){
        this.tingPaiListener = tingPaiListener;
    }

    public void clearTingData(){
        this.tingDataItem = null;
        this.selectListener = null;
        this.tingPaiListener = null;

        invalidate();
    }

    private void init(){
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(Color.WHITE);
        mPaint.setAntiAlias(true);

        if (getContext() instanceof BaseActivity) {
            BaseActivity activity = (BaseActivity) getContext();
            viewModel = new ViewModelProvider(activity.getViewModelStore(),
                    ViewModelProvider.AndroidViewModelFactory.getInstance(LibBaseUtil.getApplication()))
                    .get(MahjongGameViewModel.class);
            viewModel.getForbidCard().observe(activity, this::updateForbidStatus);
        }
    }

    private void clickAction(){
        if (isCanChange && viewModel != null){
            viewModel.addChangeCard(cardInfo);
            return;
        }

        if (!canOption) return;

        if (isSelect()){
            if (canPutPai()){
                putCard();
            } else {
                clearSelect();
            }
        } else {
            selectCard();
        }
    }

    private float downX;
    private float downY;
    private float oldX;
    private float oldY;
    boolean isClick = true;
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        super.onTouchEvent(event);
        if (!canOption){
            return false;
        }

        if (this.isEnabled()) {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    isClick = true;
                    downX = event.getRawX();
                    downY = event.getRawY();
                    oldX = getX();
                    oldY = getY();
                    break;
                case MotionEvent.ACTION_MOVE:
                    if (isCanChange){
                        return true;
                    }

                    final float xDistance = (event.getRawX() - downX);
                    final float yDistance = (event.getRawY() - downY);

                    if (xDistance != 0 || yDistance != 0) {
                        setX(oldX + xDistance);
                        setY(oldY + yDistance);
                    }

                    if (Math.abs(xDistance) > ScreenUtil.dip2px(5)
                            || Math.abs(yDistance) > ScreenUtil.dip2px(5)){
                        isClick = false;
                    }
                    break;
                case MotionEvent.ACTION_UP:
                    if (isClick){
                        clickAction();
                    } else {
                        final float distance = (event.getRawY() - downY);
                        if (Math.abs(distance) > 2F / 3 * getHeight()){
                            if (canPutPai()){
                                putCard();
                            } else {
                                setX(oldX);
                                setY(oldY);
                                selectCard();
                            }
                        } else {
                            setX(oldX);
                            setY(oldY);
                        }
                    }
                    break;
                case MotionEvent.ACTION_CANCEL:
                    setX(oldX);
                    setY(oldY);
                    break;
            }

            return true;
        }
        return false;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (cardInfo == null){
            super.onDraw(canvas);
            return;
        }

        canvas.setDrawFilter(new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG|Paint.FILTER_BITMAP_FLAG));
        switch (type){
            case MJShowCardHelper.TYPE_BOTTOM_SHOU:
                if (isShowPai){
                    if (isVisitor){
                        drawOtherShouPai(canvas, R.drawable.n_mj_pai_bg_v_b);
                    } else {
                        drawSelfShouPai(canvas);
                    }
                } else {
                    drawOtherShouPai(canvas, R.drawable.n_mj_pai_bg_h_b);
                }
                break;
            case MJShowCardHelper.TYPE_BOTTOM_MING:
            case MJShowCardHelper.TYPE_TOP_MING:
                drawSelfMingPai(canvas);
                break;
            case MJShowCardHelper.TYPE_TOP_SHOU:
                if (isShowPai){
                    drawOtherShouPai(canvas, R.drawable.n_mj_pai_bg_v_b);
                } else {
                    drawOtherShouPai(canvas, R.drawable.n_mj_pai_bg_h_b);
                }
                break;
            case MJShowCardHelper.TYPE_LEFT_SHOU:
                if (isShowPai){
                    drawOtherShouPai(canvas, R.drawable.n_mj_pai_bg_l_b);
                } else {
                    drawOtherShouPai(canvas, R.drawable.n_mj_pai_bg_l_h_b);
                }
                break;
            case MJShowCardHelper.TYPE_LEFT_MING:
                drawLeftMingPai(canvas);
                break;
            case MJShowCardHelper.TYPE_RIGHT_SHOU:
                if (isShowPai){
                    drawOtherShouPai(canvas, R.drawable.n_mj_pai_bg_r_b);
                } else {
                    drawOtherShouPai(canvas, R.drawable.n_mj_pai_bg_r_h_b);
                }
                break;
            case MJShowCardHelper.TYPE_RIGHT_MING:
                drawRightMingPai(canvas);
                break;
            case MJShowCardHelper.TYPE_LIGHT_PAI:
                drawLightPai(canvas);
                break;
            default:
                super.onDraw(canvas);
        }
    }

    private void drawSelfShouPai(Canvas canvas){
        int viewWidth = getWidth();
        int viewHeight = getHeight();

        Bitmap bgBm = MJCardBMHelper.INSTANCE.getVerticalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard());
        int bgWidth = bgBm.getWidth();
        int bgHeight = bgBm.getHeight();
        Rect src = new Rect(0, 0, bgWidth, bgHeight);
        RectF dst = new RectF(0, 0, viewWidth, viewHeight);
        canvas.drawBitmap(bgBm, src, dst, mPaint);

        if (tingDataItem != null && cardInfo != null && cardInfo.getValue() == tingDataItem.chuPai_){
            int num = 0;
            for (int i = 0; i < tingDataItem.huPaiItems_.size();i++){
                num += tingDataItem.huPaiItems_.get(i).leftCount_;
            }
            float paiWidth = 41 / 51F * viewWidth;
            float paiHeight = 52 / 73F * viewHeight;
            drawTingPaiNum(num, viewWidth, viewHeight, paiWidth, paiHeight, canvas);
        }
    }

    private void drawOtherShouPai(Canvas canvas, int paiRes){
        int viewWidth = getWidth();
        int viewHeight = getHeight();
        int left = 0;
        int top = 0;
        float scaleX = 1f;

        Bitmap bgBm = BitmapFactory.decodeResource(getContext().getResources(),
                paiRes);
        int bgWidth = bgBm.getWidth();
        int bgHeight = bgBm.getHeight();
        left = (viewWidth - bgWidth) / 2;
        top = (viewHeight - bgHeight) / 2;
        scaleX = 1f * viewWidth / bgWidth;
        initMatrix(canvas, scaleX, scaleX, 0, 0);
        canvas.drawBitmap(bgBm, left, top, mPaint);
        if(bgBm != null && !bgBm.isRecycled()) bgBm.recycle();
    }

    private void drawSelfMingPai(Canvas canvas){
        int viewWidth = getWidth();
        int viewHeight = getHeight();

        Bitmap bgBm = MJCardBMHelper.INSTANCE.getHorizontalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard());
        int bgWidth = bgBm.getWidth();
        int bgHeight = bgBm.getHeight();
        Rect src = new Rect(0, 0, bgWidth, bgHeight);
        RectF dst = new RectF(0, 0, viewWidth, viewHeight);
        canvas.drawBitmap(bgBm, src, dst, mPaint);
    }

    private void drawLeftMingPai(Canvas canvas) {
        int viewWidth = getWidth();
        int viewHeight = getHeight();

        Bitmap bgBm = MJCardBMHelper.INSTANCE.getLeftHorizontalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard());
        int bgWidth = bgBm.getWidth();
        int bgHeight = bgBm.getHeight();
        Rect src = new Rect(0, 0, bgWidth, bgHeight);
        RectF dst = new RectF(0, 0, viewWidth, viewHeight);
        canvas.drawBitmap(bgBm, src, dst, mPaint);
    }

    private void drawRightMingPai(Canvas canvas) {
        int viewWidth = getWidth();
        int viewHeight = getHeight();

        Bitmap bgBm = MJCardBMHelper.INSTANCE.getRightHorizontalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard());
        int bgWidth = bgBm.getWidth();
        int bgHeight = bgBm.getHeight();
        Rect src = new Rect(0, 0, bgWidth, bgHeight);
        RectF dst = new RectF(0, 0, viewWidth, viewHeight);
        canvas.drawBitmap(bgBm, src, dst, mPaint);
    }

    private void drawLightPai(Canvas canvas){
        int viewWidth = getWidth();
        int viewHeight = getHeight();

        Bitmap bgBm = MJCardBMHelper.INSTANCE.getVerticalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard());
        int bgWidth = bgBm.getWidth();
        int bgHeight = bgBm.getHeight();
        Rect src = new Rect(0, 0, bgWidth, bgHeight);
        RectF dst = new RectF(0, 0, viewWidth, viewHeight);
        canvas.drawBitmap(bgBm, src, dst, mPaint);
    }

    private void initMatrix(Canvas mCanvas, float scaleX, float scaleY,
                            float offsetX, float offsetY) {
        Matrix mMatrix = new Matrix();
        mMatrix.reset();
        mMatrix.preScale(scaleX, scaleY);

        mMatrix.preTranslate(-getWidth() / 2, -getHeight() / 2);
        mMatrix.postTranslate(getWidth() / 2 + offsetX, getHeight() / 2 + offsetY);
        mCanvas.concat(mMatrix);
    }

    private void selectCard(){
        if (viewModel == null || cardInfo == null){
            return;
        }

        CardInfo selectCard = viewModel.getSelectCard().getValue();
        if (selectCard == null || cardInfo != selectCard){
            viewModel.setSelectCardInfo(cardInfo);

            MJSoundHelper.INSTANCE.playSelectPaiAudio();

            if (selectListener != null && tingDataItem != null){
                selectListener.onSelectPai(tingDataItem.huPaiItems_);
            }
        }
    }

    private boolean isSelect(){
        if (viewModel == null || cardInfo == null){
            return false;
        }

        CardInfo selectCard = viewModel.getSelectCard().getValue();
        if (selectCard != null && cardInfo == selectCard){
            return true;
        }

        return false;
    }

    private void clearSelect(){
        if (viewModel == null){
            return;
        }

        viewModel.setSelectCardInfo(null);
    }

    private boolean canPutPai(){
        if (viewModel != null && viewModel.getSelfCanPut()){
            return true;
        }

        return false;
    }

    private boolean putCard(){
        if (tingDataItem != null){
            if (tingPaiListener != null){
                tingPaiListener.onTingPai(cardInfo);
                savePutCard(cardInfo);
            }
        } else {
            viewModel.chuPai(cardInfo.getValue());
            savePutCard(cardInfo);
        }
        setVisibility(GONE);

        return false;
    }

    private void savePutCard(CardInfo cardInfo){
        if (viewModel == null){
            return;
        }

        MJPutCardInfo putCardInfo = new MJPutCardInfo();
        putCardInfo.setPaiValue(cardInfo.getValue());
        putCardInfo.setPutUid(LoginHelper.getLoginUid());
        putCardInfo.setPointX(getX());
        putCardInfo.setPointY(getY());

        viewModel.setLastPutCard(putCardInfo);
    }

    private float getTextWidth(String text, Paint paint){
        if (TextUtils.isEmpty(text)){
            return 0;
        }

        return paint.measureText(text);
    }

    private float getBaseLineOffSetY(Paint paint){
        Paint.FontMetrics metrics = paint.getFontMetrics();
        float ascent = metrics.ascent;
        float descent = metrics.descent;

        return -ascent / 2 - descent / 2;
    }

    private void drawTingPaiNum(int num, float viewWidth, float viewHeight, float paiWidth, float paiHeight, Canvas canvas){
        float left = (int) ((viewWidth - paiWidth) / 2);
        float top = (int) ((viewHeight - paiHeight) / 2);
        int right = (int) (left + paiWidth);
        mPaint.setColor(Color.parseColor("#BB2306"));
        RectF oval = new RectF(right - ScreenUtil.dip2px(16), top - ScreenUtil.dip2px(16), right + ScreenUtil.dip2px(16), top + ScreenUtil.dip2px(16));
        canvas.drawArc(oval, 90, 90, true, mPaint);

        TextPaint textPaint = new TextPaint();
        textPaint.setTextSize(ScreenUtil.sp2px(getContext(), 12));
        textPaint.setAntiAlias(true);
        textPaint.setStrokeWidth(1);
        textPaint.setShader(new LinearGradient(0, 0, 0, getHeight(),
                Color.parseColor("#F6F6F3"), Color.parseColor("#F6DF70"), Shader.TileMode.CLAMP));
        String content = String.valueOf(num);
        float contentWidth = getTextWidth(content, textPaint);
        float baseLineOffSetY = getBaseLineOffSetY(textPaint);
        canvas.drawText(content, 0, content.length(), right - contentWidth -ScreenUtil.dip2px(2), top + ScreenUtil.dip2px(8) + baseLineOffSetY, textPaint);
    }

    private void updateForbidStatus(int forbidCard){
        if (forbidCard > 0 && cardInfo != null && cardInfo.getValue() == forbidCard){
            setAlpha(0.8F);
            setEnabled(false);
        } else {
            setAlpha(1F);
            setEnabled(true);
        }
    }

    public void checkForbidStatus(){
        if (viewModel != null && viewModel.getForbidCard().getValue() != null){
            updateForbidStatus(viewModel.getForbidCard().getValue());
        }
    }

    public boolean isForbidCard(){
        if (cardInfo == null || viewModel == null || viewModel.getForbidCard().getValue() == null){
            return false;
        }

        if (cardInfo.getValue() == viewModel.getForbidCard().getValue()){
            return true;
        } else {
            return false;
        }
    }

    public boolean isGoldCard(){
        if (cardInfo == null || viewModel == null || viewModel.getGameData().getValue() == null
                || viewModel.getGameData().getValue().table_ == null
                || viewModel.getGameData().getValue().table_.goldTiles_ == null
                || viewModel.getGameData().getValue().table_.goldTiles_.size() <= 0){
            return false;
        }

        if (viewModel.getGameData().getValue().table_.goldTiles_.contains(cardInfo.getValue())){
            return true;
        }

        return false;
    }

    public interface OnSelectListener{
        void onSelectPai(List<MJPlayerHint.HintTingData.HintTingHuPai> tingHuSeq);
    }

    public interface OnTingPaiListener{
        void onTingPai(CardInfo cardInfo);
    }
}
