package com.wepie.module.mahjong.view.recycleview;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.configservice.editionentity.MJMatchSetting;
import com.wepie.module.mahjong.R;

import java.util.ArrayList;
import java.util.List;

public class GalleryAdapter extends RecyclerView.Adapter<GalleryAdapter.ViewHolder> {

    private List<GameConfig.MatchInfo> data = new ArrayList<>();

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View v = LayoutInflater.from(parent.getContext()).inflate(R.layout.model_select_item, null);
        // 刚开始不可见的view显示最小比例
        v.setScaleX(GalleryRecycleView.MIN_SCALE_PERCENT);
        v.setScaleY(GalleryRecycleView.MIN_SCALE_PERCENT);
        return new ViewHolder(v);
    }

    public void setData(List<GameConfig.MatchInfo> l) {
        if (null != l && !l.isEmpty()) {
            data.addAll(l);
        }
    }

    public GameConfig.MatchInfo getMatchInfo(int index) {
        GameConfig.MatchInfo info = null;
        if (index >= 0 && index <= (data.size() - 1)) {
            info = data.get(index);
        }
        return info;
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        GameConfig.MatchInfo item = data.get(position);
        holder.bindData(item);
    }

    @Override
    public int getItemCount() {
        return data.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        private TextView requrireMoney;
        private TextView di;
        private TextView tai;
        private TextView circle;
        private TextView goldCard;
        private TextView changeCards;
        private ConstraintLayout bg;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            initView(itemView);
        }

        private void initView(View itemView) {
            requrireMoney = itemView.findViewById(R.id.require_money);
            di = itemView.findViewById(R.id.di);
            tai = itemView.findViewById(R.id.tai);
            circle = itemView.findViewById(R.id.circle);
            goldCard = itemView.findViewById(R.id.gold_cards);
            changeCards = itemView.findViewById(R.id.change_cards);
            bg = itemView.findViewById(R.id.root);
        }

        public void bindData(GameConfig.MatchInfo item) {
            requrireMoney.setText(String.valueOf(item.getCoin()));
            MJMatchSetting setting = item.getMjMatchSetting();
            di.setText(ResUtil.getResource().getString(R.string.di_txt, setting.getDi()));
            tai.setText(ResUtil.getResource().getString(R.string.tai_txt, setting.getTai()));
            if (setting.getEnable_gold_tile()){
                goldCard.setVisibility(View.VISIBLE);
            } else {
                goldCard.setVisibility(View.GONE);
            }

            if (setting.getEnable_exchange()){
                changeCards.setVisibility(View.VISIBLE);
            } else {
                changeCards.setVisibility(View.GONE);
            }

            circle.setText(setting.getQuan());

            int res;
            switch (item.getBetLevel()) {
                case MJMatchSetting.MODEL_JOY:
                    res = R.drawable.model_joy;
                    break;
                case MJMatchSetting.MODEL_LOW:
                    res = R.drawable.model_low;
                    break;
                case MJMatchSetting.MODEL_MIDDLE:
                    res = R.drawable.model_middle;
                    break;
                case MJMatchSetting.MODEL_HIGH:
                    res = R.drawable.model_high;
                    break;
                default:
                    res = 0;
                    break;
            }
            bg.setBackgroundResource(res);
        }
    }
}
