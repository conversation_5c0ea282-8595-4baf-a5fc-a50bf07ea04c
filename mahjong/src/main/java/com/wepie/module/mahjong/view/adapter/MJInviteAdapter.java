package com.wepie.module.mahjong.view.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.constants.GameType;
import com.huiwan.decorate.DecorateUtil;
import com.huiwan.lib.api.ApiService;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.UserService;
import com.huiwan.user.UserSimpleInfoCallback;
import com.huiwan.user.entity.FriendInfo;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.CustomCircleImageView;
import com.wepie.liblog.main.HLog;
import com.wepie.module.mahjong.R;
import com.wepie.module.mahjong.manager.MJStateManager;
import com.wepie.wespy.model.entity.family.FamilyMainInfo;
import com.wepie.wespy.model.entity.group.GroupInfo;
import com.wepie.wespy.module.InviteFriendApi;
import com.wepie.wespy.module.chat.ui.adapter.ConGroupHeadView;
import com.wepie.wespy.module.voiceroom.invite.InviteItemData;
import com.wepie.wespy.net.tcp.sender.HWRoomPacketSender;

import java.util.ArrayList;
import java.util.List;

public class MJInviteAdapter extends RecyclerView.Adapter<MJInviteAdapter.ViewHolder> {

    private static final String TAG = "RecentFriendsAdapter";
    private List<InviteItemData> datas = new ArrayList<>();
    private List<InviteItemData> originalDatas = new ArrayList<>();

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new MJInviteAdapter.ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.recent_friends_invite, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        InviteItemData info = datas.get(position);
        holder.updateData(info);
    }

    public void updateData(List<InviteItemData> friends) {
        datas.clear();
        datas.addAll(friends);
        originalDatas.clear();
        originalDatas.addAll(friends);
        initNickName();
        notifyDataSetChanged();
    }

    // 搜索名字需要
    private void initNickName() {
        for (int i = 0; i < originalDatas.size(); i++) {
            InviteItemData data = originalDatas.get(i);
            if (data.isFriend()) {
                initFriendName(data);
            } else {
                initGroupName(data);
            }
        }
    }

    private void initFriendName(InviteItemData data) {
        FriendInfo info = ApiService.of(InviteFriendApi.class).getFriendInfo(data.uid);
        UserService.get().getCacheSimpleUser(info.getUid(), new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                if (null != simpleInfo) {
                    data.name = simpleInfo.getRemarkName();
                    data.headUrl = simpleInfo.getHeadimgurl();
                }
            }

            @Override
            public void onUserInfoFailed(String description) {
                ToastUtil.show(description);
                HLog.d(TAG, HLog.USR, "onUserInfoFailed! msg=" + description);
            }
        });
    }

    private void initGroupName(InviteItemData data) {
        GroupInfo groupInfo = ApiService.of(InviteFriendApi.class).getGroupInfo(data.gid);
        data.name = groupInfo.name;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }

    public void handleSearch(String s) {
        datas.clear();
        if (TextUtil.isEmpty(s)) {
            datas.addAll(originalDatas);
        } else {
            for (InviteItemData f : originalDatas) {
                if (!TextUtil.isEmpty(f.name) && f.name.startsWith(s)) {
                    datas.add(f);
                }
            }
        }
        notifyDataSetChanged();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        public boolean hasInvited;
        private CustomCircleImageView headView;
        private TextView nickName;
        private TextView inviteButton;
        private InviteItemData data;
        private ConGroupHeadView groupHeadView;
        private TextView conTag;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            init(itemView);
        }

        private void init(View v) {
            headView = v.findViewById(R.id.player_head_view);
            nickName = v.findViewById(R.id.player_nick);
            inviteButton = v.findViewById(R.id.invite_friend);
            groupHeadView = v.findViewById(R.id.group_head_iv);
            inviteButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    handleInvite();
                }
            });
            conTag = v.findViewById(R.id.con_tag);
        }

        public void updateData(InviteItemData data) {
            if (null == data) {
                return;
            }
            this.data = data;
            conTag.setVisibility(View.GONE);
            if (data.isFriend()) {
                refreshFriend(data);
            } else {
                refreshGroup(data);
            }
        }

        private void refreshFriend(InviteItemData data) {
            groupHeadView.setVisibility(View.INVISIBLE);
            headView.setVisibility(View.VISIBLE);
            if (!TextUtil.isEmpty(data.name) && !TextUtil.isEmpty(data.headUrl)) {
                DecorateUtil.loadCircleHeadImage(data.headUrl, headView);
                nickName.setText(data.name);
            } else {
                FriendInfo info = ApiService.of(InviteFriendApi.class).getFriendInfo(data.uid);
                UserService.get().getCacheSimpleUser(info.getUid(), new UserSimpleInfoCallback() {
                    @Override
                    public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                        if (null != simpleInfo) {
                            DecorateUtil.loadCircleHeadImage(simpleInfo.getHeadimgurl(), headView);
                            nickName.setText(data.name);
                        }
                    }

                    @Override
                    public void onUserInfoFailed(String description) {
                        ToastUtil.show(description);
                        HLog.d(TAG, HLog.USR, "onUserInfoFailed! msg=" + description);
                    }
                });
            }
        }

        private void refreshGroup(InviteItemData data) {
            GroupInfo groupInfo = ApiService.of(InviteFriendApi.class).getGroupInfo(data.gid);
            if (groupInfo == null) return;
            if (groupInfo.isFamilyGroup()) {
                FamilyMainInfo familyMainInfo = ApiService.of(InviteFriendApi.class).getFamilyInfo();
                if (familyMainInfo == null || familyMainInfo.getFamily() == null) {
                    headView.setVisibility(View.INVISIBLE);
                    groupHeadView.setVisibility(View.VISIBLE);
                    groupHeadView.showHeadIconIfNotFamily(groupInfo);
                } else {
                    groupHeadView.setVisibility(View.INVISIBLE);
                    headView.setVisibility(View.VISIBLE);
                    DecorateUtil.loadCircleHeadImage(familyMainInfo.getFamily().getFamilyAvatarUrl(), headView);
                    ApiService.of(InviteFriendApi.class).setFamilyTagUi(conTag);
                }
            } else {
                headView.setVisibility(View.INVISIBLE);
                groupHeadView.setVisibility(View.VISIBLE);
                groupHeadView.showHeadIconIfNotFamily(groupInfo);
            }
            nickName.setText(groupInfo.name);
        }

        private void handleInvite() {
            if (null == data) {
                return;
            }
            int rid = MJStateManager.INSTANCE.getRoomID();
            if (data.isFriend()) {
                inviteFriend(data, rid, GameType.GAME_TYPE_MJ);
            } else {
                inviteGroup(data, rid, GameType.GAME_TYPE_MJ);
            }
        }

        private void inviteFriend(InviteItemData itemData, int rid, int gameType) {
            List<Integer> uidList = new ArrayList<>();
            uidList.add(itemData.uid);
            HWRoomPacketSender.inviteFriend(rid, uidList, new SeqCallback() {
                @Override
                public void onSuccess(RspHeadInfo head) {
                    setInvitedStatus();
                }

                @Override
                public void onFail(RspHeadInfo head) {
                    ToastUtil.show(head.desc);
                }
            });
        }

        private void setInvitedStatus() {
            inviteButton.setBackgroundResource(R.drawable.invited_btn_bg);
            inviteButton.setText(ResUtil.getStr(R.string.mj_invited));
        }

        private void inviteGroup(InviteItemData itemData, int rid, int gameType) {
            HWRoomPacketSender.inviteGroup(rid, itemData.gid, new SeqCallback() {
                @Override
                public void onSuccess(RspHeadInfo head) {
                    setInvitedStatus();
                }

                @Override
                public void onFail(RspHeadInfo head) {
                    ToastUtil.show(head.desc);
                }
            });
        }
    }
}
