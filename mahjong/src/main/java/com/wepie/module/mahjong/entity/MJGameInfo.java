package com.wepie.module.mahjong.entity;

import java.util.ArrayList;
import java.util.List;

public class MJGameInfo {
    public int rid_;  //游戏房间ID
    public long gameId_;  //游戏ID
    public int state_; //游戏状态
    public MJTableInfo table_; //台面信息
    public List<MJPlayerInfo> players_ = new ArrayList<>(); //玩家的游戏信息
    public MJRoundResult roundResult_;  //每圈结算信息
    public MJGameResult gameResult_;    //每局结算信息
    public int version_;  //游戏房间信息版本号，用于判断是否需要全量更新房间信息
    public List<Integer> watcherUidList_ = new ArrayList<>();
    public int round_;
    public int fengIndex_;
    public int roundIndex_;

    @Override
    public String toString() {
        return "MJGameInfo{" +
                "rid_=" + rid_ +
                ", gameId_=" + gameId_ +
                ", state_=" + state_ +
                ", table_=" + table_ +
                ", players_=" + players_ +
                ", roundResult_=" + roundResult_ +
                ", gameResult_=" + gameResult_ +
                ", version_=" + version_ +
                ", watcherUidList_=" + watcherUidList_ +
                ", round_=" + round_ +
                ", fengIndex_=" + fengIndex_ +
                ", roundIndex_=" + roundIndex_ +
                '}';
    }

    //根据uid获取对应玩家
    public MJPlayerInfo getPlayerByUid(int uid) {
        MJPlayerInfo player = null;
        if (players_ != null && players_.size() > 0){
            for (int i = 0; i < players_.size(); i++){
                if (uid == players_.get(i).uid_){
                    player = players_.get(i);
                }
            }
        }

        return player;
    }
}
