package com.wepie.module.mahjong.view.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.wepie.module.mahjong.R;
import com.wepie.module.mahjong.entity.MJRoundResult;

import java.util.ArrayList;
import java.util.List;

public class MJRoundResultAdapter extends RecyclerView.Adapter<MJRoundResultAdapter.ViewHolder> {
    private List<MJRoundResult.MJTaiItem> taiItems = new ArrayList<>();
    private Context context;
    private TextUpdateCallBack textUpdateCallBack;

    public MJRoundResultAdapter(Context context){
        this.context = context;
    }

    public void addItems(List<MJRoundResult.MJTaiItem> taiItems) {
        this.taiItems.clear();
        this.taiItems.addAll(taiItems);
        notifyDataSetChanged();
    }

    public void addItem(MJRoundResult.MJTaiItem taiItem){
        taiItems.add(taiItem);
        if (taiItems.size() - 1 >= 0){
            notifyItemInserted(taiItems.size() - 1);
        } else {
            notifyDataSetChanged();
        }
    }

    public void clearItems(){
        this.taiItems.clear();
        notifyDataSetChanged();
    }

    public void setTextUpdateCallBack(TextUpdateCallBack textUpdateCallBack){
        this.textUpdateCallBack = textUpdateCallBack;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(context).inflate(R.layout.over_detail_layout, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        MJRoundResult.MJTaiItem item = taiItems.get(position);
        holder.taiTypeName.setText(item.type_);

        if (textUpdateCallBack != null){
            textUpdateCallBack.setText(holder.taiNum, ResUtil.getStr(R.string.tai, item.number_));
        } else {
            holder.taiNum.setText(ResUtil.getStr(R.string.tai, item.number_));
        }
    }

    @Override
    public int getItemCount() {
        return taiItems.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder{
        public TextView taiTypeName;
        public TextView taiNum;
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            taiTypeName = itemView.findViewById(R.id.tai_name);
            taiNum = itemView.findViewById(R.id.tai_number);
        }
    }

    public interface TextUpdateCallBack{
        void setText(TextView tv, String content);
    }
}
