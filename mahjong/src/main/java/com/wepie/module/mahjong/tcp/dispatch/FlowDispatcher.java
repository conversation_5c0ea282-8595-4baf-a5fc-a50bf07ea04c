package com.wepie.module.mahjong.tcp.dispatch;

import com.wepie.liblog.main.HLog;
import com.wepie.module.mahjong.event.IGameRoomEvent;
import com.wepie.module.mahjong.tcp.dispatch.callback.IFlowCallBack;

import java.util.concurrent.ConcurrentHashMap;

public class FlowDispatcher {
    public static ConcurrentHashMap<String, IFlowCallBack> flowCallBackMap = new ConcurrentHashMap<>();

    public static void registerFlowCallBack(String key, IFlowCallBack callBack) {
        HLog.d("FlowDispatcher", HLog.USR, "register callback! key: " + key + ", map=" + flowCallBackMap);
        flowCallBackMap.put(key, callBack);
    }

    public static void unregisterFlowCallBack(String key, IFlowCallBack callBack) {
        HLog.d("FlowDispatcher", HLog.USR, "unregister callback! key: " + key + ", map=" + flowCallBackMap);
        if (flowCallBackMap.containsKey(key)){
            flowCallBackMap.remove(key);
        }
    }

    public static void postDataFlow(IGameRoomEvent event) {
        HLog.d("FlowDispatcher", HLog.USR, "callback size=" + flowCallBackMap.size() + ", event=" + event);
        if (flowCallBackMap.size() > 0){
            for (IFlowCallBack callBack: flowCallBackMap.values()) {
                callBack.onDispatcherData(event);
            }
        }
    }
}
