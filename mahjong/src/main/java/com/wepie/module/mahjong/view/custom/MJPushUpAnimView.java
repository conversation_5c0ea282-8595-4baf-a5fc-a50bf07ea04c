package com.wepie.module.mahjong.view.custom;

import android.animation.ValueAnimator;
import android.content.Context;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;

import com.huiwan.base.util.ScreenUtil;

/**
 * date 2019/3/20
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class MJPushUpAnimView extends ViewGroup {
    public static final int DEFAULT = 0;
    private int animStyle = DEFAULT;
    private Handler handler = new Handler();
    private int maxChildCount = 4;
    public MJPushUpAnimView(Context context) {
        super(context);
    }

    public MJPushUpAnimView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public void setMaxChildCount(int maxChildCount) {
        this.maxChildCount = maxChildCount;
    }

    public void setAnimStyle(int style) {
        animStyle = style;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int measureWidth = MeasureSpec.getSize(widthMeasureSpec);
        int measureHeight = MeasureSpec.getSize(heightMeasureSpec);
        int count = getChildCount();
        for (int i = 0; i < count; i++) {
            View child = getChildAt(i);
            measureChild(child, widthMeasureSpec, heightMeasureSpec);
        }
        setMeasuredDimension(measureWidth, measureHeight);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        int b = bottom - top - getPaddingBottom();
        int count = getChildCount();
        for (int i = 0 ;i < count; i++) {
            View child = getChildAt(i);
            int childHeight = (int)(child.getMeasuredHeight() * (child.getScaleY() > 1 ? 1 : child.getScaleY())) + ScreenUtil.dip2px(8);
            int childWidth = (int)(child.getMeasuredWidth() * (child.getScaleX() > 1 ? 1 : child.getScaleX()));
            int l = (getWidth() - childWidth - getPaddingLeft() - getPaddingRight()) / 2 + getPaddingLeft();
            child.layout(l, b - childHeight, l + childWidth, b);
            b = b - childHeight;
        }
    }

    public void addAnimView(View view) {
        addAnimView(view, 2000);
    }

    public void addAnimView(View view, int duration) {
        if (getChildCount() >= maxChildCount) {
            removeViewAt(getChildCount()-1);
        }
        startAnim(view, duration);
        addView(view, 0);
    }


    private void startAnim(final View child, int duration) {
        switch (animStyle) {
            case DEFAULT:
                defaultAnim(child, duration);
                break;
        }
    }

    private void defaultAnim(final View child, int duration) {
        child.setScaleX(0.1f);
        child.setScaleY(0.1f);
        ValueAnimator animator = ValueAnimator.ofInt(0, 300).setDuration(300);
        animator.addUpdateListener(animation -> {
            int time = (Integer) animation.getAnimatedValue();
            if (time < 200) {
                float p = time / 200f;
                child.setScaleX(p * 1.2f);
                child.setScaleY(p * 1.2f);
            } else {
                float p = (time - 200f)/100f;
                child.setScaleX(1.2f - 0.2f * p);
                child.setScaleY(1.2f - 0.2f * p);
            }
            requestLayout();
        });
        animator.start();

        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                hideAnim(child);
            }
        }, duration);
    }

    private void hideAnim(final View child) {
        child.animate().alpha(0).setDuration(200).start();
        handler.postDelayed(() -> {
            if (child.getParent() instanceof MJPushUpAnimView) {
                MJPushUpAnimView parent = (MJPushUpAnimView) child.getParent();
                parent.removeView(child);
            }
            requestLayout();
        }, 200);
    }
}
