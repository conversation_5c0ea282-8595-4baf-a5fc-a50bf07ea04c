package com.wepie.module.mahjong.entity;

import java.util.ArrayList;
import java.util.List;

public class MJPlayerInfo {
    public int uid_;
    public int fengWei_;
    public List<Integer> shouPai_ = new ArrayList<>();
    public int moPai_;
    public List<Integer> chuPai_ = new ArrayList<>();
    public List<MingPaiItem> mingPaiItems_ = new ArrayList<>();
    public List<Integer> huaPai_ = new ArrayList<>();
    public int status_;
    public MJPlayerHint hint_;
    public boolean tingPai_;
    public long extraActionMs_;
    public boolean guoshuiState_;
    public List<TingHuPai> tingHuPaiList_ = new ArrayList<>();

    public static class MingPaiItem {
        public int type_;
        public List<Integer> paiSeq_ = new ArrayList<>();
        public int otherPai_;

        @Override
        public String toString() {
            return "MingPaiItem{" +
                    "type_=" + type_ +
                    ", paiSeq_=" + paiSeq_ +
                    ", otherPai_=" + otherPai_ +
                    '}';
        }
    }

    public static class TingHuPai{
        public int pai_;
        public int count_;
        public int score_;

        @Override
        public String toString() {
            return "TingHuPai{" +
                    "pai_=" + pai_ +
                    ", count_=" + count_ +
                    ", score_=" + score_ +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "MJPlayerInfo{" +
                "uid_=" + uid_ +
                ", fengWei_=" + fengWei_ +
                ", shouPai_=" + shouPai_ +
                ", moPai_=" + moPai_ +
                ", chuPai_=" + chuPai_ +
                ", mingPaiItems_=" + mingPaiItems_ +
                ", huaPai_=" + huaPai_ +
                ", status_=" + status_ +
                ", hint_=" + hint_ +
                ", tingPai_=" + tingPai_ +
                ", extraActionMs_=" + extraActionMs_ +
                ", guoshuiState_=" + guoshuiState_ +
                ", tingHuPaiList_=" + tingHuPaiList_ +
                '}';
    }
}
