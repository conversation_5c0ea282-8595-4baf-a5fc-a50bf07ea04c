package com.wepie.module.mahjong.userface;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;

import com.huiwan.anim.SVGAUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.model.voiceroom.FaceInfo;
import com.opensource.svgaplayer.SVGAImageView;
import com.opensource.svgaplayer.SVGAParser;
import com.opensource.svgaplayer.SVGAVideoEntity;
import com.wepie.download.DownloadCallback;
import com.wepie.download.DownloadUtil;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoadListener;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.liblog.main.HLog;
import com.wepie.module.mahjong.R;

import pl.droidsonroids.gif.GifDrawable;

/**
 * date 2018/6/7
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class MJUserFaceView extends RelativeLayout {

    @NonNull
    private ImageView faceImg;
    private ImageView animView;
    private SVGAImageView svgaImageView;

    @Nullable
    private GifDrawable gifDrawable;
    private final Handler faceHandler = new Handler(Looper.getMainLooper());

    public MJUserFaceView(Context context) {
        this(context, null);
    }

    public MJUserFaceView(Context context, AttributeSet attrs) {
        super(context, attrs);
        faceImg = new ImageView(context);
        LayoutParams lp = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        faceImg.setScaleType(ImageView.ScaleType.FIT_CENTER);
        lp.addRule(CENTER_IN_PARENT);
        addView(faceImg, lp);

        animView = new AppCompatImageView(context);
        LayoutParams lottieLp = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        lottieLp.addRule(CENTER_IN_PARENT);
        addView(animView, lottieLp);

        // 5.9.21
        svgaImageView = new SVGAImageView(context);
        LayoutParams svgaLp = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        lottieLp.addRule(CENTER_IN_PARENT);
        addView(svgaImageView, svgaLp);

        faceImg.setVisibility(GONE);
        animView.setVisibility(GONE);
        svgaImageView.setVisibility(GONE);
    }

    public void updateRoomFaceInfo(FaceInfo faceInfo, int resultId) {
        faceHandler.removeCallbacksAndMessages(null);
        String localAnimPath = "";
        if (faceInfo.isGif()) {
            localAnimPath = faceInfo.getLocalGifPath();
        } else if (faceInfo.isJson()) {
            localAnimPath = faceInfo.getLocalJsonPath();
        } else if (faceInfo.isSvga()) {
            localAnimPath = faceInfo.getLocalSvgaPath();
        }
        if (TextUtils.isEmpty(localAnimPath)) {
            showStaticFace(faceInfo, resultId);
        } else {
            DownloadUtil.downloadFileWithRetry(faceInfo.getAnimUri(), localAnimPath, 1, true, new DownloadCallback() {
                @Override
                public void onSuccess(String url, String path) {
                    if (faceInfo.isSvga()) {
                        showSvgaAnim(path, faceInfo, resultId);
                    } else if (faceInfo.isJson()) {
                        showJsonAnim(path, faceInfo, resultId);
                    } else if (faceInfo.isGif()) {
                        showGifFace(faceInfo, resultId);
                    }
                }

                @Override
                public void onFail(String msg) {
                    ToastUtil.show(ResUtil.getStr(R.string.mj_loading_emote_resource_failed) + msg);
                }

                @Override
                public void onPercent(int percent) {

                }
            });
        }
    }

    public void clearFace() {
        stopGifAnim();
        stopJsonAnim();
        stopSvgaAnim();
        faceHandler.removeCallbacksAndMessages(null);
        faceImg.setImageDrawable(null);
        faceImg.setVisibility(GONE);
    }

    private void showGifFace(final FaceInfo faceInfo, final int resultId) {
        try {
            if (gifDrawable != null) {
                if (gifDrawable.isPlaying()) {
                    gifDrawable.stop();
                }
            }
            gifDrawable = new GifDrawable(faceInfo.getLocalGifPath());
            faceImg.setImageDrawable(gifDrawable);
            faceImg.setVisibility(VISIBLE);
            animView.setVisibility(GONE);
            gifDrawable.start();
            if (faceInfo.getAnimTime() > 0 && faceInfo.getAnimTime() < 1000 * 60) {
                faceHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (faceInfo.getResSize() > 0) {
                            showStaticFace(faceInfo, resultId);
                        } else {
                            faceImg.setVisibility(GONE);
                        }
                    }
                }, faceInfo.getAnimTime());
            }
        } catch (Exception e){
           HLog.d("","error show gif, {}", e);
        }
    }

    private void showJsonAnim(String path, final FaceInfo faceInfo, final int resultId) {
        animView.setVisibility(VISIBLE);
        ImageLoadInfo loadInfo = ImageLoadInfo.newInfo();
        WpImageLoader.load(path, animView, loadInfo, new WpImageLoadListener<String>() {
            @Override
            public boolean onComplete(String model, Drawable data) {
                faceHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (faceInfo.getResSize() > 0) {
                            animView.setImageDrawable(null);
                            animView.setVisibility(GONE);
                            showStaticFace(faceInfo, resultId);
                        } else {
                            clearFace();
                        }
                    }
                }, faceInfo.getAnimTime());
                return false;
            }

            @Override
            public boolean onFailed(String model, Exception e) {
                return false;
            }
        });
    }

    private void showSvgaAnim(String path, final FaceInfo faceInfo, final int resultId) {
        SVGAUtil.getFileSvgaAnim(path, new SVGAParser.ParseCompletion() {
            @Override
            public void onComplete(@NonNull SVGAVideoEntity svgaVideoEntity) {
                svgaImageView.setVideoItem(svgaVideoEntity);
                svgaImageView.startAnimation();
                svgaImageView.setVisibility(VISIBLE);
                if (faceInfo.getAnimTime() > 0 && faceInfo.getAnimTime() < 1000 * 60) {
                    faceHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (faceInfo.getResSize() > 0) {
                                svgaImageView.stopAnimation(true);
                                showStaticFace(faceInfo, resultId);
                            } else {
                                clearFace();
                            }
                        }
                    }, faceInfo.getAnimTime());
                }
            }

            @Override
            public void onError() {

            }
        });
    }

    private void stopGifAnim() {
        if (gifDrawable != null) {
            gifDrawable.stop();
            gifDrawable = null;
        }
    }

    private void stopJsonAnim() {
        animView.setImageDrawable(null);
        animView.setVisibility(GONE);
    }

    private void stopSvgaAnim() {
        svgaImageView.stopAnimation();
        svgaImageView.setVisibility(GONE);
    }

    private void showStaticFace(FaceInfo faceInfo, int resultId) {
        faceImg.setVisibility(VISIBLE);
        String localPath = faceInfo.getLocalResPath(resultId);
        if (TextUtils.isEmpty(localPath) || faceInfo.getStaticTime() < 10) {
            clearFace();
        } else {
            try {
                stopGifAnim();
                WpImageLoader.load(faceInfo.getResultUrl(resultId), faceImg);
                faceHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        clearFace();
                    }
                }, faceInfo.getStaticTime());
            } catch (Exception e){
               HLog.d("","showStaticFace: {}", e);
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        clearFace();
        super.onDetachedFromWindow();
    }
}
