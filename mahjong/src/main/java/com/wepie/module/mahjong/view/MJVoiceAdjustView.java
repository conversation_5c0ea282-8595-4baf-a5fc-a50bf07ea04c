package com.wepie.module.mahjong.view;

import android.app.Service;
import android.content.Context;
import android.graphics.Color;
import android.media.AudioManager;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.widget.TakeInProgressBar;
import com.wepie.module.mahjong.R;
import com.wepie.module.mahjong.manager.MJSoundHelper;

/**
 * Created by bigwen on 2017/11/15.
 */
public class MJVoiceAdjustView extends LinearLayout {
    private String TAG = MJVoiceAdjustView.class.getSimpleName();
    private final int maxBGMVolume = 100;
    private final int maxEffectVolume = 100;
    private final int maxChatVolume = 100;
    private Context mContext;
    private TakeInProgressBar bgmVocBar, gameVocBar,chatVocBar;
    private ImageView bgmSildeIv, gameSildeIv,chatSildeIv;
    private TextView dialodEnter;
    private int startX;
    private int chipWidth = ScreenUtil.dip2px(20);
    private int maxRight = ScreenUtil.dip2px(120) - chipWidth;
    private MJVoiceAdjustView.Callback callback;
    private AudioManager audioManager;
    private boolean isUserTouch;
    private int volumeUpFlag = -1; // 未设置为-1，true为1，false为0

    public MJVoiceAdjustView(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public MJVoiceAdjustView(Context context, boolean isVolumeUp) {
        super(context);
        mContext = context;
        this.volumeUpFlag = isVolumeUp ? 1 : 0;
        init();
    }

    public MJVoiceAdjustView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.mj_adjust_voice_dialog, this);
        bgmVocBar = findViewById(R.id.bgm_voc_bar);
        gameVocBar = findViewById(R.id.game_voc_bar);
        chatVocBar = findViewById(R.id.chat_voc_bar);
        bgmSildeIv = findViewById(R.id.bgm_voc_slide);
        gameSildeIv = findViewById(R.id.game_voc_slide);
        chatSildeIv = findViewById(R.id.chat_voc_slide);
        dialodEnter = findViewById(R.id.dialog_enter);
        bgmVocBar.setPercent(0);
        gameVocBar.setPercent(0);
        chatVocBar.setPercent(0);
        bgmVocBar.setWidth(ScreenUtil.dip2px(120));
        gameVocBar.setWidth(ScreenUtil.dip2px(120));
        chatVocBar.setWidth(ScreenUtil.dip2px(120));
        bgmVocBar.setProgressColor(Color.parseColor("#FDD824"));
        gameVocBar.setProgressColor(Color.parseColor("#FDD824"));
        chatVocBar.setProgressColor(Color.parseColor("#FDD824"));

        audioManager = (AudioManager) getContext().getSystemService(Service.AUDIO_SERVICE);
        updateVoice();
        initTouch(bgmVocBar, bgmSildeIv);
        initTouch(gameVocBar, gameSildeIv);
        initTouch(chatVocBar, chatSildeIv);

        dialodEnter.setOnClickListener(v -> {
            if (onCloseListener != null) onCloseListener.close();
        });
    }

    private void updateVoice() {
        // 获取背景音乐声音
        int currentBGMVolume = MJSoundHelper.INSTANCE.getBGMVolume();

        // 获取游戏音效声音
        int currentEffectVolume = MJSoundHelper.INSTANCE.getEffectVolume();

        //语音聊天音量
        int currentChatVolume = MJSoundHelper.INSTANCE.getChatVolume();

        setDefaultWidth(currentBGMVolume * 1f / maxBGMVolume,
                currentEffectVolume * 1f / maxEffectVolume,
                currentChatVolume * 1f / maxChatVolume);
    }

    public void setDefaultWidth(float percent1, float percent2, float percent3){
        if (percent1 >= 0) {
            bgmVocBar.setPercent(percent1);
            updateSlideView(bgmSildeIv, percent1);
        }
        if (percent2 >= 0) {
            gameVocBar.setPercent(percent2);
            updateSlideView(gameSildeIv, percent2);
        }
        if (percent3 >= 0) {
            chatVocBar.setPercent(percent3);
            updateSlideView(chatSildeIv, percent3);
        }
    }


    private void initTouch(final View bar,final View slide) {
        slide.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                isUserTouch = true;

                int x = (int) event.getRawX();
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        startX = x;
                        break;
                    case MotionEvent.ACTION_MOVE:
                        int dx = x - startX;
                        updateSlideView(dx, slide);
                        startX = x;
                        updateGameVoice(bar,slide.getLeft()*1f/maxRight);
                        break;
                    case MotionEvent.ACTION_UP:
                        updateVoice();
                        break;
                    default:
                        break;
                }
                return true;
            }
        });
    }

    private void updateSlideView(int dx, View slide) {
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
                chipWidth, chipWidth);
        int startMargin = slide.getLeft() + dx;
        if(startMargin < 0) startMargin = 0;
        if(startMargin > maxRight) startMargin = maxRight;
        params.setMarginStart(startMargin);
        slide.setLayoutParams(params);
    }

    private void updateSlideView(View view,float percent){
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
                chipWidth, chipWidth);
        int leftMargin = (int)(maxRight*percent);
        if(leftMargin < 0) leftMargin = 0;
        if(leftMargin > maxRight) leftMargin = maxRight;
        params.leftMargin = leftMargin;
        view.setLayoutParams(params);
    }

    private void updateGameVoice(View view,float percent){
        if (view == bgmVocBar) {
            bgmVocBar.setPercent(percent);
            if (callback != null){
                callback.onTouchBGM(percent);
            }
        } else if (view == gameVocBar) {
            gameVocBar.setPercent(percent);
            if (callback != null){
                callback.onTouchGameEffect(percent);
            }
        } else {
            chatVocBar.setPercent(percent);
            if (callback != null){
                callback.onTouchChat(percent);
            }
        }
    }

    public void setCallback(MJVoiceAdjustView.Callback callback){
        this.callback = callback;
    }

    private MJVoiceAdjustView.OnCloseListener onCloseListener;
    public void setOnCloseListener(MJVoiceAdjustView.OnCloseListener onCloseListener){
        this.onCloseListener = onCloseListener;
    }

    public interface OnCloseListener{
        void close();
    }

    public interface Callback{
        void onTouchBGM(float percent);
        void onTouchGameEffect(float percent);
        void onTouchChat(float percent);
    }
}