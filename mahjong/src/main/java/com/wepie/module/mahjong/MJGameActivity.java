package com.wepie.module.mahjong;

import android.content.pm.ActivityInfo;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import androidx.lifecycle.ViewModelProvider;

import com.huiwan.barrage.BarrageAnimView;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.fitter.FullScreenUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.component.gift.show.GiftContentView;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.constants.GameType;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.UserSimpleInfoCallback;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.liblog.main.HLog;
import com.wepie.module.mahjong.chat.MJChatInterface;
import com.wepie.module.mahjong.chat.MJChatPresenter;
import com.wepie.module.mahjong.constants.MahjongIntentConfig;
import com.wepie.module.mahjong.entity.MJGameInfo;
import com.wepie.module.mahjong.entity.MJPlayerInfo;
import com.wepie.module.mahjong.manager.MJDialogShowUtil;
import com.wepie.module.mahjong.tcp.MJGamePacketSender;
import com.wepie.module.mahjong.tcp.MJSocketHelper;
import com.wepie.module.mahjong.util.MJThreadHandler;
import com.wepie.module.mahjong.util.MJVoiceUtil;
import com.wepie.module.mahjong.view.INewGameView;
import com.wepie.module.mahjong.view.MJGameView;
import com.wepie.module.mahjong.viewmodel.MahjongGameViewModel;
import com.wepie.module.mahjong.viewmodel.NewGameRoomViewModel;
import com.wepie.track.ShenceGameTypeSourceUtil;
import com.wepie.wespy.model.chat.event.NewFriendEvent;
import com.wepie.wespy.model.entity.HWRoomNotice;
import com.wepie.wespy.model.entity.hwroom.HWRoomInfo;
import com.wepie.wespy.model.entity.hwroom.HWRoomMsg;
import com.wepie.wespy.model.event.hwroom.ExitRoomEvent;
import com.wepie.wespy.model.event.hwroom.RoomFailEvent;
import com.wepie.wespy.model.event.hwroom.RoomInvitePlayMateEvent;
import com.wepie.wespy.model.event.hwroom.RoomNoticeEvent;
import com.wepie.wespy.model.event.hwroom.RoomPushEvent;
import com.wepie.wespy.model.event.hwroom.RoomSwitchChannelEvent;
import com.wepie.wespy.model.event.hwroom.RoomSyncEvent;
import com.wepie.wespy.module.care.main.CareUtil;
import com.wepie.wespy.module.hwroom.HWRoomManager;
import com.wepie.wespy.net.tcp.packet.HWRoomPackets;
import com.wepie.wespy.net.tcp.packet.HWRoomPushPackets;
import com.wepie.wespy.net.tcp.packet.MJPlayerPackets;
import com.wepie.wespy.net.tcp.sender.HWRoomPacketSender;
import com.wepie.wespy.util.HWRoomUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class MJGameActivity extends BaseActivity implements MJChatInterface {
    private NewGameRoomViewModel roomViewModel;

    private boolean isPortrait;

    private INewGameView gameView;  //游戏具体布局View
    private FrameLayout gameContainer;  //游戏布局容器
    private GiftContentView giftContentView; //礼物动效播放View
    private BarrageAnimView barrageAnimView;

    private MJChatPresenter chatPresenter;

    private boolean isClear = false; //房间是否已清空
    private static String TAG = "NewGameActivity";
    private int roomID;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        if (!isPortrait) {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        }
        super.onCreate(savedInstanceState);
        FullScreenUtil.setLandlordFullScreen(getWindow());
        setContentView(R.layout.activity_new_game_room);

        initView();
        subscribeViewModel();
        initPresenter();

        handleRoomInfo();
        TrackUtil.funcDurationStart(getTrackName());
    }

    private String getTrackName() {
        return ShenceGameTypeSourceUtil.getGameTypeSource(GameType.GAME_TYPE_MJ);
    }

    @Override
    protected void onResume() {
        super.onResume();

        if (gameView != null){
            gameView.onResume();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();

        if (gameView != null){
            gameView.onPause(isFinishing());
        }

        if (isFinishing()) {
            doFinish();
            ContextUtil.setKeepScreenOn(this, false);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        doFinish();
    }

    //View初始化
    private void initView() {
        gameContainer = findViewById(R.id.game_container);
        giftContentView = findViewById(R.id.gift_content_view);
        barrageAnimView = findViewById(R.id.game_barrage_view);
        EventBus.getDefault().register(this);
    }

    //刷新房间UI
    private void refreshRoomView(HWRoomInfo roomInfo){
        if (null == roomInfo) {
            return;
        }
        if (gameView == null) {
            gameView = createGameView(roomInfo);
            gameContainer.removeAllViews();
            gameContainer.addView((View) gameView);
        }

        if (gameView != null) {
            gameView.updateRoomInfo(roomInfo);
        }
        roomID = roomInfo.getRid();
    }

    //根据房间信息创建对应该房间的游戏布局
    public INewGameView createGameView(HWRoomInfo roomInfo){
        INewGameView view = new MJGameView(this);
        return view;
    }

    //释放游戏布局
    private void releaseGameView() {
        if (gameView != null) gameView.onClear();
        gameContainer.removeAllViews();
        gameView = null;
    }

    //订阅ViewModel
    private void subscribeViewModel(){
        roomViewModel = new ViewModelProvider(getViewModelStore(),
                ViewModelProvider.AndroidViewModelFactory.getInstance(LibBaseUtil.getApplication()))
                .get(NewGameRoomViewModel.class);

        roomViewModel.getRoomData().observe(this, this::refreshRoomView);
    }

    private void initPresenter(){
        chatPresenter = new MJChatPresenter(this);
        chatPresenter.register();
    }

    private void releasePresenter(){
        if (chatPresenter != null){
            chatPresenter.unregister();
            chatPresenter = null;
        }
    }

    //处理跳转参数
    private void handleRoomInfo() {
        HWRoomInfo roomInfo = HWRoomManager.getInstance().getHwRoomInfo();
        if (null == roomInfo) {
            HLog.d(TAG, HLog.USR, "handleIntent error! roomInfo is null!");
            ToastUtil.show(ResUtil.getResource().getString(R.string.get_room_error));
            return;
        }
        refreshRoomView(roomInfo);
        if (roomInfo.isGaming()) {
            MJGamePacketSender.INSTANCE.syncGame(roomInfo.getRid());
        }
        isPortrait = getIntent().getBooleanExtra(MahjongIntentConfig.ROOM_IS_PORTRAIT, false);
    }

    //销毁房间
    private void doFinish() {
        if (isClear) return;
        MJSocketHelper.INSTANCE.disConnect();
        releaseGameView();
        isClear = true;
        MJThreadHandler.getInstance().clear();
        MJVoiceUtil.INSTANCE.leaveChannel();
        releasePresenter();
        EventBus.getDefault().unregister(this);
        TrackUtil.funcDurationEnd(getTrackName(), roomID);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleSync(RoomSyncEvent event) {
        roomViewModel.updateRoomInfo(event.hwRoomInfo);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleExit(ExitRoomEvent r) {
        HWRoomInfo info = HWRoomManager.getInstance().getHwRoomInfo();
        if (info.isGaming()) {
            exitGame(info);
        } else {
            exitRoom(info);
        }
        handleExit();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleHWRoomPush(RoomPushEvent event) {
        if (event.pushType == HWRoomPushPackets.HWRoomSysPushType.HWROOM_SYS_KICK_USER_VALUE) {
            HWRoomManager.getInstance().clearHWRoomInfo();
            finish();
        } else if (event.pushType == HWRoomPushPackets.HWRoomSysPushType.HWROOM_SYS_ROOM_DISBAND_VALUE) {
            HWRoomManager.getInstance().clearHWRoomInfo();
            finish();
        } else if (event.pushType == HWRoomPushPackets.HWRoomSysPushType.HWROOM_SYS_CANCEL_PLAY_MATE_VALUE) {
            MJDialogShowUtil.INSTANCE.showTipsDialog(this, ResUtil.getStr(R.string.mj_play_mate_force_quit_tips));
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleRoomFail(RoomFailEvent event) {
        String errorMsg = event.rspHeadInfo.desc;
        HLog.d(TAG, HLog.USR, "handleRoomFail, code=${event.rspHeadInfo.code}, msg=$errorMsg");
        ToastUtil.show(errorMsg);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleRoomNotice(RoomNoticeEvent event){
        HWRoomNotice roomNotice = event.roomNotice;
        gameView.handleRoomNotice(roomNotice);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleInvitePlayMate(RoomInvitePlayMateEvent event){
        if (gameView instanceof MJGameView){
            ((MJGameView)gameView).addInviteMsg(event);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleSwitchChannel(RoomSwitchChannelEvent event){
        if (gameView instanceof MJGameView){
            ((MJGameView)gameView).handleSwitchChannel(event);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNewFriend(NewFriendEvent event) {
        HWRoomInfo roomInfo = HWRoomManager.getInstance().getHwRoomInfo();
        if (roomInfo != null && roomInfo.isInSit(LoginHelper.getLoginUid()) && roomInfo.isInSit(event.record.mUid)) {
            if (gameView instanceof MJGameView) {
                ((MJGameView) gameView).showNewFriend(event.record);
            }
        }
    }

    private void exitGame(HWRoomInfo info) {
        MJGamePacketSender.INSTANCE.forceQuit(new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {

            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private void handleExit() {
        HWRoomManager.getInstance().clearHWRoomInfo();
        finish();
    }

    private void exitRoom(HWRoomInfo info) {
        HWRoomPacketSender.exitRoom(info.getRid(), new SeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {

            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public void showGiftAnim(GiftShowInfo giftShowInfo) {
        if (giftContentView == null || giftShowInfo == null) return;
        giftContentView.showGiftAnim(giftShowInfo);
    }

    @Override
    public void onBackPressed() {
        HWRoomInfo roomInfo = HWRoomManager.getInstance().getHwRoomInfo();
        int forceQuitCoin = 30;
        boolean isHasForceQuited = false;
        if (gameView != null && gameView.getGameInfo() != null
                && gameView.getGameInfo() instanceof MJGameInfo){
            MJGameInfo gameInfo = (MJGameInfo) gameView.getGameInfo();
            forceQuitCoin = gameInfo.table_.forceQuitCoin_;
            MJPlayerInfo playerInfo = gameInfo.getPlayerByUid(LoginHelper.getLoginUid());
            if (playerInfo != null && playerInfo.status_ == MJPlayerPackets.PlayerStatus.PlayerStatusForceQuit_VALUE){
                isHasForceQuited = true;
            }
        }

        HWRoomUtil.INSTANCE.exit(this, roomInfo, forceQuitCoin, isHasForceQuited);
    }

    @Override
    public void addMsg(HWRoomMsg hwRoomMsg) {
        if (hwRoomMsg.getMsgType() == HWRoomMsg.MSG_TYPE_GIFT) {
            GiftShowInfo showInfo = HWRoomUtil.INSTANCE.fromHWRoomMSg(hwRoomMsg);
            if (showInfo != null) {
                onSendGift(showInfo);
            }
        } else {
            if (HWRoomManager.getInstance().getHwRoomInfo().isInSit(hwRoomMsg.getSender())){
                gameView.onPushRoomMsg(hwRoomMsg);
            } else {
                UserService.get().getCacheSimpleUser(hwRoomMsg.getSender(), new UserSimpleInfoCallback() {
                    @Override
                    public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                        String content = simpleInfo.nickname + ": " + hwRoomMsg.getContent();
                        float speed = ScreenUtil.getScreenWidth() * 1.0f / 5500;
                        barrageAnimView.startAnim(content, BarrageAnimView.TYPE_DEFAULT, 0, speed, false, null);
                    }

                    @Override
                    public void onUserInfoFailed(String description) {
                        float speed = ScreenUtil.getScreenWidth() * 1.0f / 5500;
                        barrageAnimView.startAnim(hwRoomMsg.getContent(), BarrageAnimView.TYPE_DEFAULT, 0, speed, false, null);
                    }
                });
            }
        }
    }

    public void onSendGift(GiftShowInfo info) {
        giftContentView.showGiftAnim(info);
        UserService.get().getCacheSimpleUser(info.sender, new UserSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo sendUser) {
                UserService.get().getCacheSimpleUser(info.receiver, new UserSimpleInfoCallback() {
                    @Override
                    public void onUserInfoSuccess(UserSimpleInfo recUser) {
                        barrageAnimView.startAnim(CareUtil.getHWRoomGiftText(sendUser.getRemarkName(), recUser.getRemarkName(), info), BarrageAnimView.TYPE_GIFT);
                    }

                    @Override
                    public void onUserInfoFailed(String description) {
                        HLog.d(TAG, HLog.USR, "onUserInfoFailed! msg=" + description);
                    }
                });
            }

            @Override
            public void onUserInfoFailed(String description) {
                HLog.d(TAG, HLog.USR, "onUserInfoFailed! msg=" + description);
            }
        });
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);

        FullScreenUtil.setLandlordFullScreen(getWindow());
    }
}
