package com.wepie.module.mahjong.view.custom;


import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.util.ScreenUtil;
import com.wepie.liblog.main.HLog;


/**
 * <AUTHOR>
 */
public class MJGridSpaceItemDecoration extends RecyclerView.ItemDecoration {
    private int space;
    private int halfSpace;
    private int edgeSpace;

    private boolean useEdge = true;
    private boolean onlyVertical = false;

    public MJGridSpaceItemDecoration(int dp) {
        this.space = ScreenUtil.dip2px(dp);
        this.halfSpace = space / 2;
        updateEdgeSpace();
    }

    public void setUseEdge(boolean useEdge) {
        this.useEdge = useEdge;
        updateEdgeSpace();
    }

    public void setOnlyVertical(boolean onlyVertical){
        this.onlyVertical = onlyVertical;
    }

    public void setEdgeSpace(int dp) {
        this.edgeSpace = ScreenUtil.dip2px(dp);
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        RecyclerView.LayoutManager lm = parent.getLayoutManager();
        RecyclerView.Adapter<?> adapter = parent.getAdapter();
        if (lm != null && adapter != null && lm instanceof GridLayoutManager) {
            int spanCount = ((GridLayoutManager) lm).getSpanCount();
            int total = adapter.getItemCount();
            updateOffset(outRect, parent.getChildAdapterPosition(view), spanCount, total);
        }
    }

    private void updateOffset(Rect outRect, int index, int spanCount, int total) {
        if (!onlyVertical){
            outRect.left = firstCol(index, spanCount) ? edgeSpace : halfSpace;
            outRect.right = lastCol(index, spanCount) ? edgeSpace : halfSpace;
        }
        outRect.top = firstRow(index, spanCount) ? edgeSpace : halfSpace;
        outRect.bottom = lastRow(index, spanCount, total) ? edgeSpace : halfSpace;
        HLog.d("GridSpaceItemDecoration", "updateOffset index:" +index + ", rect: " + outRect);
    }

    private void updateEdgeSpace() {
        edgeSpace = useEdge ? space : 0;
    }

    private boolean firstCol(int index, int spanCount) {
        return index % spanCount == 0;
    }

    private boolean firstRow(int index, int spanCount) {
        return index < spanCount;
    }

    private boolean lastCol(int index, int spanCount) {
        return (index + 1) % spanCount == 0;
    }

    private boolean lastRow(int index, int spanCount, int total) {
        int lineCount = (total - 1)/ spanCount + 1;
        int line = index / spanCount + 1;
        return line == lineCount;
    }
}