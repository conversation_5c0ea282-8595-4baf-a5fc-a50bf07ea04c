package com.wepie.module.mahjong.view.recycleview;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.store.PrefUtil;
import com.wepie.liblog.main.HLog;
import com.wepie.module.mahjong.MJMatchActivity;

public class GalleryRecycleView extends RecyclerView {
    private static final String TAG = "GalleryRecycleView";
    public static final float MIN_SCALE_PERCENT = 0.85f;
    private int centerX = 0;
    private int centerOffset = Integer.MAX_VALUE;
    private int centerItemIndex = 0;
    private int maxScrollWidth = 0; // 最大顺时滑动速度

    public GalleryRecycleView(Context context) {
        super(context);
    }

    public GalleryRecycleView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public GalleryRecycleView(Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    public boolean fling(int velocityX, int velocityY) {
        initMaxScrollWidth();
        velocityX = getVelocityX(velocityX);
        HLog.d(TAG, "fling! velocityX=" + velocityX + ", velocityY=" + velocityY);
        return super.fling(velocityX, velocityY);
    }

    private int getVelocityX(int velocityX) {
        if (maxScrollWidth > 0) {
            if (velocityX > 0) {
                velocityX = Math.min(velocityX, maxScrollWidth);
            } else {
                velocityX = Math.max(velocityX, -maxScrollWidth);
            }
        }
        return velocityX;
    }

    private void initMaxScrollWidth() {
        LayoutManager manager = getLayoutManager();
        if (!(manager instanceof LinearLayoutManager) || maxScrollWidth > 0) {
            return;
        }
        LinearLayoutManager linearLayoutManager = (LinearLayoutManager) manager;
        int firstIndex = linearLayoutManager.findFirstVisibleItemPosition();
        View v = linearLayoutManager.findViewByPosition(firstIndex);
        assert v != null;
        maxScrollWidth = v.getWidth() * 4;
    }

    @Override
    public void onScrolled(int dx, int dy) {
        super.onScrolled(dx, dy);
        initCenterPoint();
        scaleItem();
    }

    private void initCenterPoint() {
        if (centerX > 0) {
            return;
        }
        int[] point = new int[2];
        getLocationOnScreen(point);
        int halfW = getWidth() / 2;
        centerX = point[0] + halfW;
    }

    private void scaleItem() {
        centerOffset = Integer.MAX_VALUE;
        LayoutManager manager = getLayoutManager();
        if (manager instanceof LinearLayoutManager) {
            LinearLayoutManager linearLayoutManager = (LinearLayoutManager) manager;
            int firstIndex = linearLayoutManager.findFirstVisibleItemPosition();
            int lastIndex = linearLayoutManager.findLastVisibleItemPosition();
            for (int i = firstIndex; i <= lastIndex; i++) {
                View v = linearLayoutManager.findViewByPosition(i);
                scaleItem(v, i);
            }
        }
    }

    public int getCenterItemIndex() {
        return centerItemIndex;
    }

    private void scaleItem(View v, int index) {
        if (null == v) {
            return;
        }
        int[] point = new int[2];
        v.getLocationOnScreen(point);
        int w = v.getWidth();
        int parentW = getWidth();
        int itemCenterX = point[0] + w / 2;
        int offset = Math.abs((itemCenterX - centerX));
        updateIndex(offset, index);
        float scale = 1 - offset / (float) parentW;
        if (scale <= MIN_SCALE_PERCENT) {
            scale = MIN_SCALE_PERCENT;
        }
        Log.d(TAG, "centerX=" + centerX + ", index" + index + ",x=" + itemCenterX + ", w=" + w + ", offset=" + offset + ", scale=" + scale);
        v.setScaleX(scale);
        v.setScaleY(scale);
    }

    private void updateIndex(int offset, int index) {
        if (offset < centerOffset) {
            centerOffset = offset;
            centerItemIndex = index;
            PrefUtil.getInstance().setInt(MJMatchActivity.LAST_SELECT_INDEX_KEY, index, true);
        }
    }
}