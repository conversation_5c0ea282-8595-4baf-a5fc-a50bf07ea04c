package com.wepie.module.mahjong.userface.choose;

import android.graphics.Color;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.configservice.model.voiceroom.FaceInfo;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.module.mahjong.R;

public class MJUserFaceItemHolder extends RecyclerView.ViewHolder {
    private ImageView preIv;
    private TextView faceNameTv;
    private FaceInfo faceInfo;
    private OnMJUserFaceClickListener listener;

    MJUserFaceItemHolder(View view) {
        super(view);
        preIv = view.findViewById(R.id.face_item_view_pre_iv);
        faceNameTv = view.findViewById(R.id.face_item_view_name_tv);

        View.OnClickListener listener = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                clickFaceItem();
            }
        };
        itemView.setOnClickListener(listener);
    }

    void onBind(FaceInfo faceInfo, OnMJUserFaceClickListener listener) {
        this.faceInfo = faceInfo;
        this.listener = listener;
        WpImageLoader.load(faceInfo.getPreviewUrl(), preIv);
        faceNameTv.setText(faceInfo.getName());
    }

    void setEnable(boolean enable) {
        itemView.setEnabled(enable);
        preIv.setColorFilter(enable ? Color.TRANSPARENT : 0x66000000);
        faceNameTv.setTextColor(enable ? Color.WHITE : 0x66ffffff);
    }

    private void clickFaceItem() {
        if (this.listener != null && faceInfo != null) {
            this.listener.onClickFace(faceInfo);
        }

    }
}