package com.wepie.module.mahjong.view;

import android.content.Context;
import android.graphics.Camera;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.util.AttributeSet;

import androidx.appcompat.widget.AppCompatTextView;

public class MJTextView extends CustomTextView {
    private Matrix mMatrix;
    private Camera mCamera;

    private float mRotateX;
    private float mRotateY;
    private float mRotateZ;

    public MJTextView(Context context) {
        super(context);

        init();
    }

    public MJTextView(Context context, AttributeSet attrs) {
        super(context, attrs);

        init();
    }

    public MJTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        init();
    }

    private void init(){
        mMatrix = new Matrix();
        mCamera = new Camera();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        setCameraRotate(canvas);
        super.onDraw(canvas);
    }

    private void setCameraRotate(Canvas mCanvas) {
        mMatrix.reset();
        mCamera.save();
        mCamera.rotateX(mRotateX);//绕x轴旋转
        mCamera.rotateY(mRotateY);//绕y轴旋转
        mCamera.rotateZ(mRotateZ);//绕y轴旋转
        mCamera.getMatrix(mMatrix);//计算对于当前变换的矩阵，并将其复制到传入的mMatrix中
        mCamera.restore();
        /**
         * Camera默认位于视图的左上角，故生成的矩阵默认也是以其左上角为旋转中心，
         * 所以在动作之前调用preTranslate将mMatrix向左移动getWidth()/2个长度，
         * 向上移动getHeight()/2个长度，
         * 使旋转中心位于矩阵的中心位置，动作之后再post回到原位
         */
        mMatrix.preTranslate(-getWidth() / 2, -getHeight() / 2);
        mMatrix.postTranslate(getWidth() / 2, getHeight() / 2);

        mCanvas.concat(mMatrix);//将mMatrix与canvas中当前的Matrix相关联
    }

    public void transformView(float rotateX, float rotateY, float rotateZ){
        mRotateX = rotateX;
        mRotateY = rotateY;
        mRotateZ = rotateZ;

        invalidate();
    }
}
