package com.wepie.module.mahjong.userface.choose;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.voiceroom.FaceInfo;
import com.huiwan.widget.IndicatorView;
import com.wepie.module.mahjong.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;

import java.util.ArrayList;
import java.util.List;

/**
 * date 2018/6/11
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class MJUserFaceChooseView extends RelativeLayout {
    private static final int FACE_NUM_PER_PAGE = 12;
    private static final int COL_NUM = 6;
    private Handler handler = new Handler(Looper.getMainLooper());
    private View view;
    private ViewPager pager;
    private IndicatorView indicatorView;
    private List<MJUserFaceGridAdapter> rvAdapters = new ArrayList<>();
    private long showType = FaceInfo.SHOW_TYPE_ALL;

    public MJUserFaceChooseView(Context context) {
        this(context, null);
    }

    public MJUserFaceChooseView(Context context, AttributeSet attrs) {
        super(context, attrs);
        view = LayoutInflater.from(context).inflate(R.layout.mj_face_choose_view, this);
        pager = view.findViewById(R.id.face_choose_pager);
        pager.setPageMargin(ScreenUtil.dip2px(40));
        indicatorView = view.findViewById(R.id.face_choose_indicator);
        indicatorView.setEmptyColor(0xFF8B8B8B);
        indicatorView.setChooseColor(0xFFE1E1E1);
        setClipChildren(false);
    }

    public void setShowType(long showType) {
        this.showType = showType;
        initPages();
    }

    private void initPages() {
        List<FaceInfo> faceInfoList = ConfigHelper.getInstance().getFaces(showType);
        if (faceInfoList.isEmpty()) {
            return;
        }
        int page = (faceInfoList.size() - 1) / FACE_NUM_PER_PAGE + 1;
        if (page < 2) {
            indicatorView.setVisibility(INVISIBLE);
        } else {
            indicatorView.setVisibility(VISIBLE);
            indicatorView.setTotal(page);
        }
        List<View> list = new ArrayList<>(page);
        for (int i = 0; i < page; i++) {
            RecyclerView rv = new RecyclerView(view.getContext());
            MJUserFaceGridAdapter rvAdapter = new MJUserFaceGridAdapter();
            int start = FACE_NUM_PER_PAGE * i;
            int end = start + FACE_NUM_PER_PAGE;
            if (end > faceInfoList.size()) end = faceInfoList.size();
            rvAdapter.refresh(faceInfoList.subList(start, end));
            rv.setLayoutManager(new GridLayoutManager(view.getContext(), COL_NUM));
            rv.setAdapter(rvAdapter);
            list.add(rv);
            rvAdapters.add(rvAdapter);

        }
        MJUserFacePageAdapter pagerAdapter = new MJUserFacePageAdapter();
        pagerAdapter.refresh(list);
        pager.setAdapter(pagerAdapter);
        pager.addOnPageChangeListener(new ViewPager.SimpleOnPageChangeListener() {
            @Override
            public void onPageSelected(int position) {
                indicatorView.refresh(position);
            }
        });
    }

    public void setOnClickFaceListener(OnMJUserFaceClickListener listener) {
        int size = rvAdapters.size();
        for (int i = 0; i < size; i++) {
            MJUserFaceGridAdapter adapter = rvAdapters.get(i);
            adapter.setListener(listener);
        }
    }

    public void setEnableFromDelayTime(long delayTime) {
        if (delayTime > 0) {
            int size = rvAdapters.size();
            for (int i = 0; i < size; i++) {
                MJUserFaceGridAdapter adapter = rvAdapters.get(i);
                adapter.setEnable(false);
            }
            handler.removeCallbacksAndMessages(null);
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    setEnableFromDelayTime(0);
                }
            }, delayTime);
        } else {
            int size = rvAdapters.size();
            for (int i = 0; i < size; i++) {
                MJUserFaceGridAdapter adapter = rvAdapters.get(i);
                adapter.setEnable(true);
            }
        }
    }

    public void onHide() {
        handler.removeCallbacksAndMessages(null);
    }

    public static void showDialog(Context context, long delayTime, final OnMJUserFaceClickListener listener) {
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setCanceledOnTouchOutside(true);
        dialog.initBottomDialog();
        MJUserFaceChooseView view = new MJUserFaceChooseView(context);
        dialog.setContentView(view);
        OnMJUserFaceClickListener localListener = new OnMJUserFaceClickListener() {
            @Override
            public void onClickFace(FaceInfo faceInfo) {
                dialog.dismiss();
                if (listener != null) {
                    listener.onClickFace(faceInfo);
                }
            }
        };
        view.setOnClickFaceListener(localListener);
        view.setEnableFromDelayTime(delayTime);
        dialog.show();
    }


}
