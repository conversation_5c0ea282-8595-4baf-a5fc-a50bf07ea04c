package com.wepie.module.mahjong.entity;

public class CardInfo {
    //后端规定的(1-9 万，11-19 筒，21-29 条，31-37 东南西北中发白，41-48 春夏秋冬梅兰菊竹)
    private int value;
    private int orginIndex;
    private int uiIndex;

    private boolean isMoPai;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getOrginIndex() {
        return orginIndex;
    }

    public void setOrginIndex(int orginIndex) {
        this.orginIndex = orginIndex;
    }

    public int getUiIndex() {
        return uiIndex;
    }

    public void setUiIndex(int uiIndex) {
        this.uiIndex = uiIndex;
    }

    public boolean isMoPai() {
        return isMoPai;
    }

    public void setMoPai(boolean moPai) {
        isMoPai = moPai;
    }

    @Override
    public String toString() {
        return "CardInfo{" +
                "value=" + value +
                ", orginIndex=" + orginIndex +
                ", uiIndex=" + uiIndex +
                ", isMoPai=" + isMoPai +
                '}';
    }
}
