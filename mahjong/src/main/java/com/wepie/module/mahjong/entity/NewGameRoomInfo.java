package com.wepie.module.mahjong.entity;

import java.util.List;

public class NewGameRoomInfo {
    private int rid;  //游戏房间ID
    private int owner = 0; //房主ID
    private int gameType;  //游戏类型
    private List<Integer> playerList; //房间内玩家列表
    private List<Integer> watcherList; //围观列表

    private int version = 0; //版本号

    public int getRid() {
        return rid;
    }

    public void setRid(int rid) {
        this.rid = rid;
    }

    public int getOwner() {
        return owner;
    }

    public void setOwner(int owner) {
        this.owner = owner;
    }

    public int getGameType() {
        return gameType;
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    public List<Integer> getPlayerList() {
        return playerList;
    }

    public void setPlayerList(List<Integer> playerList) {
        this.playerList = playerList;
    }

    public List<Integer> getWatcherList() {
        return watcherList;
    }

    public void setWatcherList(List<Integer> watcherList) {
        this.watcherList = watcherList;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public boolean isUpdateAllInfo(int version){
        boolean isUpdate = false;
        if (version - this.version > 1){
            isUpdate = true;
        }

        return isUpdate;
    }
}
