package com.wepie.module.mahjong.view.custom;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.animation.AccelerateInterpolator;

import androidx.annotation.DrawableRes;

public class MJFadeImageView extends androidx.appcompat.widget.AppCompatImageView {
    private Bitmap bitmap = null;
    private Paint paint = null;
    private Rect src = null;
    private Rect dst = null;
    private float process = 1f;

    public MJFadeImageView(Context context) {
        super(context);

        init();
    }

    public MJFadeImageView(Context context, AttributeSet attrs) {
        super(context, attrs);

        init();
    }

    public MJFadeImageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        init();
    }

    private void init(){
        paint = new Paint();
        src = new Rect();
        dst = new Rect();
    }

    public void setBitmap(Bitmap bm){
        this.bitmap = bm;
    }

    public void setBitmap(@DrawableRes int resId){
        bitmap = BitmapFactory.decodeResource(getResources(), resId);
    }

    public void setProcess(float process){
        this.process = process;
        invalidate();
    }

    public void startAnim(long duration){
        ValueAnimator animator = ValueAnimator.ofFloat(0f, 1f);
        animator.addUpdateListener(valueAnimator -> {
            float curValue = (float)valueAnimator.getAnimatedValue();
            setProcess(curValue);
        });
        animator.setDuration(duration);
        animator.setInterpolator(new AccelerateInterpolator());
        animator.start();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (bitmap == null){
            super.onDraw(canvas);
            return;
        }

        src.top = 0;
        src.left = 0;
        src.bottom = bitmap.getHeight();
        src.right = (int) (bitmap.getWidth() * process);
        dst.top = 0;
        dst.left = 0;
        dst.bottom = bitmap.getHeight();
        dst.right = (int) (bitmap.getWidth() * process);

        canvas.drawBitmap(bitmap, src, dst, paint);
    }
}
