package com.wepie.module.mahjong.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.decorate.CharmManager;
import com.wepie.module.mahjong.R;

/**
 *
 * <AUTHOR>
 * date 2021-03-23
 */
public class MJCharmNumberView extends ConstraintLayout {
    private ImageView charmIv;
    private TextView charmTv;
    private ConstraintLayout rootView;

    public MJCharmNumberView(@NonNull Context context) {
        super(context);
        initView();
    }
    public MJCharmNumberView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public MJCharmNumberView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    public MJCharmNumberView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.mj_charm_number_view, this);
        charmIv = findViewById(R.id.charm_iv);
        charmTv = findViewById(R.id.charm_tv);
        rootView = findViewById(R.id.root_lay);
    }

    public void updateCharm(int charmNumber, int charmLevel){
        charmTv.setText(String.valueOf(charmNumber));
        if (charmLevel > 0) {
            charmIv.setImageResource(CharmManager.getInstance().getCharmSrc(charmLevel));
        }

        LayoutParams lp = (LayoutParams) charmTv.getLayoutParams();
        if (charmLevel == 0){
            lp.leftMargin = ScreenUtil.dip2px(0);
            charmTv.setPadding(ScreenUtil.dip2px(4),0,ScreenUtil.dip2px(4), 0);
        } else {
            lp.setMarginStart(ScreenUtil.dip2px(11));
            charmTv.setPadding(ScreenUtil.dip2px(11),0,ScreenUtil.dip2px(4),0);
        }

        ConstraintSet constraintSet = new ConstraintSet();
        constraintSet.clone(rootView);
        switch (charmLevel){
            case 0:
                charmIv.setVisibility(GONE);
                charmTv.setBackgroundResource(R.drawable.mj_charm_number_bg_0);
                break;
            case 1: case 2: case 3:
                charmTv.setBackgroundResource(R.drawable.mj_charm_number_bg_1_2_3);
                break;
            case 4: case 5: case 6:
                charmTv.setBackgroundResource(R.drawable.mj_charm_number_bg_4_5_6);
                constraintSet.connect(charmTv.getId(), ConstraintSet.BOTTOM, charmIv.getId(), ConstraintSet.BOTTOM, ScreenUtil.dip2px(1));
                break;
            case 7: case 8: case 9:
                charmTv.setBackgroundResource(R.drawable.mj_charm_number_bg_7_8_9);
                break;
            case 10: case 11: case 12:
                charmTv.setBackgroundResource(R.drawable.mj_charm_number_bg_10_11_12);
                constraintSet.connect(charmTv.getId(), ConstraintSet.BOTTOM, charmIv.getId(), ConstraintSet.BOTTOM, ScreenUtil.dip2px(1));
                break;
            case 13: case 14: case 15:
                charmTv.setBackgroundResource(R.drawable.mj_charm_number_bg_13_14_15);
                constraintSet.connect(charmTv.getId(), ConstraintSet.BOTTOM, charmIv.getId(), ConstraintSet.BOTTOM, ScreenUtil.dip2px(1));
                break;
            case 16: case 17: case 18:
                charmTv.setBackgroundResource(R.drawable.mj_charm_number_bg_16_17_18);
                break;
        }
        constraintSet.applyTo(rootView);
    }

}
