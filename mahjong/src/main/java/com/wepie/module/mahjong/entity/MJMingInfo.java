package com.wepie.module.mahjong.entity;


import com.wepie.wespy.net.tcp.packet.MJPlayerPackets;

import java.util.List;

public class MJMingInfo {
    private int type;
    private List<CardInfo> paiSeq;
    private CardInfo otherPai;
    private boolean isSelfOption;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public List<CardInfo> getPaiSeq() {
        return paiSeq;
    }

    public void setPaiSeq(List<CardInfo> paiSeq) {
        this.paiSeq = paiSeq;
    }

    public CardInfo getOtherPai() {
        return otherPai;
    }

    public void setOtherPai(CardInfo otherPai) {
        this.otherPai = otherPai;
    }

    public boolean isSelfOption() {
        return isSelfOption;
    }

    public void setSelfOption(boolean selfOption) {
        isSelfOption = selfOption;
    }

    public boolean isGang(){
        if (type == MJPlayerPackets.MingPaiItemType.MingPaiMingGang_VALUE
                || type == MJPlayerPackets.MingPaiItemType.MingPaiAnGang_VALUE
                || type == MJPlayerPackets.MingPaiItemType.MingPaiJiaGang_VALUE){
            return true;
        }
        return false;
    }
}
