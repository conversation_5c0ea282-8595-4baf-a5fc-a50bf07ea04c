package com.wepie.module.mahjong.entity;

import java.util.ArrayList;
import java.util.List;

/**
 * 每局游戏结束的结算信息
 */
public class MJGameResult {
    public List<GameResultPlayerInfo> players_ = new ArrayList<>(); //每局结算时各个玩家的结算信息

    @Override
    public String toString() {
        return "MJGameResult{" +
                "players_=" + players_ +
                '}';
    }

    /**
     * 每局结算时各个玩家的结算信息
     */
    public static class GameResultPlayerInfo{
        public int uid_;  //玩家uid
        public int coin_; //本局游戏获得金币
        public int exp_;  //本局游戏获得经验
        public int rank_; //本局游戏排名
        public boolean isBankrupt_; //游戏结束时玩家是否破产

        @Override
        public String toString() {
            return "GameResultPlayerInfo{" +
                    "uid_=" + uid_ +
                    ", coin_=" + coin_ +
                    ", exp_=" + exp_ +
                    ", rank_=" + rank_ +
                    ", isBankrupt_=" + isBankrupt_ +
                    '}';
        }
    }
}
