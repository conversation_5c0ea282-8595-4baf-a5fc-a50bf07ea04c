package com.wepie.module.mahjong.entity;

import java.util.ArrayList;
import java.util.List;

public class MJPush {
    public static class MoPiaData {
        public int moPaiUid_;
        public int pai_;
        public boolean needBuHua_;
        public long endTimestamp_;
        public MJPlayerHint hint_;

        @Override
        public String toString() {
            return "MoPiaData{" +
                    "moPaiUid_=" + moPaiUid_ +
                    ", pai_=" + pai_ +
                    ", needBuHua_=" + needBuHua_ +
                    ", endTimestamp_=" + endTimestamp_ +
                    ", hint_=" + hint_ +
                    '}';
        }
    }

    public static class ChuPaiData {
        public int chuPaiUid_;
        public int pai_;
        public long endTimestamp_;
        public MJPlayerHint hint_;

        @Override
        public String toString() {
            return "ChuPaiData{" +
                    "chuPaiUid_=" + chuPaiUid_ +
                    ", pai_=" + pai_ +
                    ", endTimestamp_=" + endTimestamp_ +
                    ", hint_=" + hint_ +
                    '}';
        }
    }

    public static class BuHuaData {
        public int buHuaUid_;
        public int pai_;

        @Override
        public String toString() {
            return "BuHuaData{" +
                    "buHuaUid_=" + buHuaUid_ +
                    ", pai_=" + pai_ +
                    '}';
        }
    }

    public static class ChiData {
        public int chiUid_;
        public int chiPai_;
        public List<Integer> chiPaiSeq_ = new ArrayList<>();
        public MJPlayerHint hint_;
        public int chuPaiUid_;

        @Override
        public String toString() {
            return "ChiData{" +
                    "chiUid_=" + chiUid_ +
                    ", chiPai_=" + chiPai_ +
                    ", chiPaiSeq_=" + chiPaiSeq_ +
                    ", hint_=" + hint_ +
                    ", chuPaiUid_=" + chuPaiUid_ +
                    '}';
        }
    }

    public static class PengData {
        public int pengUid_;
        public int pengPai_;
        public MJPlayerHint hint_;
        public int chuPaiUid_;

        @Override
        public String toString() {
            return "PengData{" +
                    "pengUid_=" + pengUid_ +
                    ", pengPai_=" + pengPai_ +
                    ", hint_=" + hint_ +
                    ", chuPaiUid_=" + chuPaiUid_ +
                    '}';
        }
    }

    public static class GangData {
        public int gangUid_;
        public int gangPai_;
        public int gangType_;
        public MJPlayerHint hint_;
        public int chuPaiUid_;
        public List<MJPlayerHint.HintTingData.HintTingHuPai> huPaiList_ = new ArrayList<>();
        public int goldTile_;

        @Override
        public String toString() {
            return "GangData{" +
                    "gangUid_=" + gangUid_ +
                    ", gangPai_=" + gangPai_ +
                    ", gangType_=" + gangType_ +
                    ", hint_=" + hint_ +
                    ", chuPaiUid_=" + chuPaiUid_ +
                    ", huPaiList_=" + huPaiList_ +
                    ", goldTile_=" + goldTile_ +
                    '}';
        }
    }

    public static class TingData {
        public int tingUid_;
        public int chuPai_;
        public List<MJPlayerHint.HintTingData.HintTingHuPai> huPaiList_ = new ArrayList<>();
        public MJPlayerHint hint_;

        @Override
        public String toString() {
            return "TingData{" +
                    "tingUid_=" + tingUid_ +
                    ", chuPai_=" + chuPai_ +
                    ", huPaiList_=" + huPaiList_ +
                    ", hint_=" + hint_ +
                    '}';
        }
    }

    public static class HuData {
        public int huType_;
        public int huUid_;
        public int paoUid_;
        public int huCardValue_;
        public int taiNum_;

        @Override
        public String toString() {
            return "HuData{" +
                    "huType_=" + huType_ +
                    ", huUid_=" + huUid_ +
                    ", paoUid_=" + paoUid_ +
                    ", huCardValue_=" + huCardValue_ +
                    ", taiNum_=" + taiNum_ +
                    '}';
        }
    }

    public static class GuoShuiData {
        public int guoShuiUid_;
        public boolean guoShuiState_;

        @Override
        public String toString() {
            return "GuoShuiData{" +
                    "guoShuiUid_=" + guoShuiUid_ +
                    ", guoShuiState_=" + guoShuiState_ +
                    '}';
        }
    }

    public static class PushNoActionData {
        public int chuUid_;
        public int chuPai_;

        @Override
        public String toString() {
            return "PushNoActionData{" +
                    "chuUid_=" + chuUid_ +
                    ", chuPai_=" + chuPai_ +
                    '}';
        }
    }

    public static class PlayerStatusData {
        public int uid_;
        public int status_;

        @Override
        public String toString() {
            return "PlayerStatusData{" +
                    "uid_=" + uid_ +
                    ", status_=" + status_ +
                    '}';
        }
    }

    public static class HuItemChangeData{
        public int tingUid_;
        public List<MJPlayerHint.HintTingData.HintTingHuPai> huPaiList_ = new ArrayList<>();

        @Override
        public String toString() {
            return "HuItemChangeData{" +
                    "tingUid_=" + tingUid_ +
                    ", huPaiList_=" + huPaiList_ +
                    '}';
        }
    }

    public static class PushPlayerShouPaiData{
        public List<Integer> shouPai_ = new ArrayList<>();
        public int targetUid_;

        @Override
        public String toString() {
            return "PushPlayerShouPaiDate{" +
                    "shouPai_=" + shouPai_ +
                    ", targetUid_=" + targetUid_ +
                    '}';
        }
    }
}
