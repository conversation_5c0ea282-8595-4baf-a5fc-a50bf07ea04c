package com.wepie.module.mahjong.view.card;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PaintFlagsDrawFilter;
import android.graphics.Rect;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.component.activity.BaseActivity;
import com.wepie.module.mahjong.entity.CardInfo;
import com.wepie.module.mahjong.entity.MJMingInfo;
import com.wepie.module.mahjong.entity.MJShouPaiParams;
import com.wepie.module.mahjong.manager.MJCardBMHelper;
import com.wepie.module.mahjong.manager.MJShowCardHelper;
import com.wepie.module.mahjong.viewmodel.MahjongGameViewModel;
import com.wepie.wespy.net.tcp.packet.MJPlayerPackets;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class MJFrameLayout extends View {
    private Paint mPaint;

    private List<CardInfo> cardList = new ArrayList<>();
    private List<MJMingInfo> mingList = new ArrayList<>();

    private int cardType = MJShowCardHelper.TYPE_BOTTOM_SHOU;
    private int cardWidth;
    private int cardHeight;
    private int cardOffsetX;
    private int cardOffsetY;

    private float lastMingPaiX = 0f;

    private boolean isTransform = true;

    private MahjongGameViewModel viewModel;

    public MJFrameLayout(@NonNull Context context) {
        super(context);
        init();
    }

    public MJFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public MJFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public MJFrameLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    private void init() {
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setColor(Color.WHITE);
        mPaint.setAntiAlias(true);
    }

    public void setCardParams(int type, int width, int height,
                              int offsetX, int offSetY) {
        cardType = type;
        cardWidth = width;
        cardHeight = height;
        cardOffsetX = offsetX;
        cardOffsetY = offSetY;

        if (viewModel == null && getContext() instanceof BaseActivity) {
            BaseActivity activity = (BaseActivity) getContext();
            viewModel = new ViewModelProvider(activity.getViewModelStore(),
                    ViewModelProvider.AndroidViewModelFactory.getInstance(LibBaseUtil.getApplication()))
                    .get(MahjongGameViewModel.class);
        }

        requestLayout();
        invalidate();
    }

    public float getCardWidth() {
        return cardWidth;
    }

    public float getCardHeight() {
        return cardHeight;
    }

    public void putCards(List<CardInfo> cards) {
        cardList = cards;
        requestLayout();
        invalidate();
    }

    public void putMingCards(List<MJMingInfo> mingCards) {
        mingList = mingCards;
        requestLayout();
        invalidate();
    }

    public void addCards(CardInfo card) {
        cardList.add(card);
        requestLayout();
        invalidate();
    }

    public void addMingCards(MJMingInfo mingCard) {
        mingList.add(mingCard);
        requestLayout();
        invalidate();
    }

    public void clearCards() {
        cardList.clear();
        requestLayout();
        invalidate();
    }

    public void clearMingCards() {
        mingList.clear();
        requestLayout();
        invalidate();
    }

    public void setTransform(boolean isTransform) {
        this.isTransform = isTransform;
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

        // 获取宽的测量模式
        int wSpecMode = MeasureSpec.getMode(widthMeasureSpec);
        // 获取控件提供的 view 宽的最大值
        int wSpecSize = MeasureSpec.getSize(widthMeasureSpec);
        // 获取高的测量模式
        int hSpecMode = MeasureSpec.getMode(heightMeasureSpec);
        // 获取控件提供的 view 高的最大值
        int hSpecSize = MeasureSpec.getSize(heightMeasureSpec);

        int measureWidth = wSpecSize;
        int measureHeight = hSpecSize;

        if (wSpecMode == MeasureSpec.AT_MOST) {
            measureWidth = measureWidthByType();
        }
        if (hSpecMode == MeasureSpec.AT_MOST) {
            measureHeight = measureHeightByType();
        }

        setMeasuredDimension(measureWidth, measureHeight);
    }

    //根据类型测量牌组布局宽度
    public int measureWidthByType() {
        int width = 0;
        if (cardList.size() <= 0 && mingList.size() <= 0 && !isChuLayout()) {
            return width;
        }

        switch (cardType) {
            case MJShowCardHelper.TYPE_BOTTOM_HUA:
            case MJShowCardHelper.TYPE_TOP_HUA:
            case MJShowCardHelper.TYPE_TOP_RESULT_HU:
            case MJShowCardHelper.TYPE_BOTTOM_RESULT_HU:
            case MJShowCardHelper.TYPE_ROUND_RESULT_HUA_PAI:
                width = cardWidth + (cardList.size() - 1) * cardOffsetX;
                break;
            case MJShowCardHelper.TYPE_BOTTOM_MING:
            case MJShowCardHelper.TYPE_TOP_MING:
                width = mingList.size() * (cardWidth + 2 * cardOffsetX);
                break;
            case MJShowCardHelper.TYPE_BOTTOM_CHU:
            case MJShowCardHelper.TYPE_TOP_CHU:
            case MJShowCardHelper.TYPE_LEFT_CHU:
            case MJShowCardHelper.TYPE_RIGHT_CHU:
                width = 5 * cardOffsetX + cardWidth;
                break;
            case MJShowCardHelper.TYPE_LEFT_MING:
                MJShouPaiParams leftParams = MJShowCardHelper.INSTANCE.getLeftShouPaiParams();
                width = (int) leftParams.getTotalWidth();
                break;
            case MJShowCardHelper.TYPE_RIGHT_MING:
                MJShouPaiParams rightParams = MJShowCardHelper.INSTANCE.getRightShouPaiParams();
                width = (int) rightParams.getTotalWidth();
                break;
            case MJShowCardHelper.TYPE_LEFT_HUA:
                width = MJShowCardHelper.INSTANCE.getValue(cardWidth, 16, isTransform)
                        + MJShowCardHelper.INSTANCE.getValues(cardOffsetX, 16 - cardList.size() + 1, 16, isTransform);
                break;
            case MJShowCardHelper.TYPE_RIGHT_HUA:
                width = cardWidth + MJShowCardHelper.INSTANCE.getValues(cardOffsetX, 0 , cardList.size() - 1, isTransform);
                break;
            case MJShowCardHelper.TYPE_LEFT_RESULT_HU:
            case MJShowCardHelper.TYPE_RIGHT_RESULT_HU:
                width = cardWidth + 2 * cardOffsetX;
                break;
            case MJShowCardHelper.TYPE_ROUND_RESULT_PAI:
                for (int i = 0; i < mingList.size(); i++){
                    if (mingList.get(i).isGang()){
                        width += cardWidth + 3 * cardOffsetX;
                    } else {
                        width += cardWidth + 2 * cardOffsetX;
                    }
                }
                width += 2 * cardWidth + (cardList.size() - 2) * cardOffsetX;
                break;
            default:
                break;
        }
        return width;
    }

    //根据类型测量牌组布局高度
    public int measureHeightByType() {
        int height = 0;
        if (cardList.size() <= 0 && mingList.size() <= 0 && !isChuLayout()) {
            return height;
        }

        switch (cardType) {
            case MJShowCardHelper.TYPE_BOTTOM_HUA:
            case MJShowCardHelper.TYPE_TOP_HUA:
            case MJShowCardHelper.TYPE_ROUND_RESULT_HUA_PAI:
            case MJShowCardHelper.TYPE_ROUND_RESULT_PAI:
            case MJShowCardHelper.TYPE_TOP_RESULT_HU:
            case MJShowCardHelper.TYPE_BOTTOM_RESULT_HU:
                height = cardHeight;
                break;
            case MJShowCardHelper.TYPE_BOTTOM_CHU:
            case MJShowCardHelper.TYPE_TOP_CHU:
                height = 2 * cardOffsetY + cardHeight;
                break;
            case MJShowCardHelper.TYPE_BOTTOM_MING:
            case MJShowCardHelper.TYPE_TOP_MING:
                height = cardHeight + cardOffsetY;
                break;
            case MJShowCardHelper.TYPE_LEFT_HUA:
                height = MJShowCardHelper.INSTANCE.getValue(cardHeight, 16, isTransform)
                        + MJShowCardHelper.INSTANCE.getValues(cardOffsetY, 16 - cardList.size() + 1, 16, isTransform);
                break;
            case MJShowCardHelper.TYPE_RIGHT_HUA:
                height = cardHeight + MJShowCardHelper.INSTANCE.getValues(cardOffsetY, 0, cardList.size() - 1, isTransform);
                break;
            case MJShowCardHelper.TYPE_LEFT_CHU:
            case MJShowCardHelper.TYPE_RIGHT_CHU:
                height = 3 * cardOffsetY + cardHeight;
                break;
            case MJShowCardHelper.TYPE_LEFT_MING:
                MJShouPaiParams leftParams = MJShowCardHelper.INSTANCE.getLeftShouPaiParams();
                height = (int) leftParams.getTotalHeight();
                break;
            case MJShowCardHelper.TYPE_RIGHT_MING:
                MJShouPaiParams rightParams = MJShowCardHelper.INSTANCE.getRightShouPaiParams();
                height = (int) rightParams.getTotalHeight();
                break;
            case MJShowCardHelper.TYPE_LEFT_RESULT_HU:
            case MJShowCardHelper.TYPE_RIGHT_RESULT_HU:
                height = cardHeight + (cardList.size() / 3) * cardOffsetY;
                break;
            default:
                break;
        }
        return height;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        Bitmap bm = null;
        switch (cardType) {
            case MJShowCardHelper.TYPE_TOP_CHU:
            case MJShowCardHelper.TYPE_BOTTOM_CHU:
                bm = drawTopAndBottomChuPai();
                break;
            case MJShowCardHelper.TYPE_LEFT_CHU:
                bm = drawLeftChuPai();
                break;
            case MJShowCardHelper.TYPE_RIGHT_CHU:
                bm = drawRightChuPai();
                break;
            case MJShowCardHelper.TYPE_BOTTOM_HUA:
            case MJShowCardHelper.TYPE_TOP_HUA:
                bm = drawTopAndBottomHuaPai();
                break;
            case MJShowCardHelper.TYPE_LEFT_HUA:
                bm = drawLeftHuaPai();
                break;
            case MJShowCardHelper.TYPE_RIGHT_HUA:
                bm = drawRightHuaPai();
                break;
            case MJShowCardHelper.TYPE_TOP_MING:
            case MJShowCardHelper.TYPE_BOTTOM_MING:
                bm = drawTopAndBottomMingPai();
                break;
            case MJShowCardHelper.TYPE_LEFT_MING:
                bm = drawLeftMingPai();
                break;
            case MJShowCardHelper.TYPE_RIGHT_MING:
                bm = drawRightMingPai();
                break;
            case MJShowCardHelper.TYPE_ROUND_RESULT_PAI:
            case MJShowCardHelper.TYPE_ROUND_RESULT_HUA_PAI:
                bm = drawResultPai();
                break;
            case MJShowCardHelper.TYPE_TOP_RESULT_HU:
            case MJShowCardHelper.TYPE_BOTTOM_RESULT_HU:
            case MJShowCardHelper.TYPE_LEFT_RESULT_HU:
            case MJShowCardHelper.TYPE_RIGHT_RESULT_HU:
                bm = drawResultHuPai();
                break;
            default:
                break;
        }

        if (bm == null) {
            super.onDraw(canvas);
            return;
        }

        int viewWidth = getWidth();
        int viewHeight = getHeight();
        int bgWidth = bm.getWidth();
        int bgHeight = bm.getHeight();
        int left = (viewWidth - bgWidth) / 2;
        int top = (viewHeight - bgHeight) / 2;

        canvas.setDrawFilter(new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG|Paint.FILTER_BITMAP_FLAG));
        canvas.drawBitmap(bm, left, top, mPaint);
    }

    private Bitmap drawTopAndBottomChuPai(){
        if (cardList.size() <= 0) {
            return null;
        }

        int parentWidth = measureWidthByType();
        int parentHeight = measureHeightByType();
        Bitmap bitmap = Bitmap.createBitmap(parentWidth, parentHeight, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.setDrawFilter(new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG|Paint.FILTER_BITMAP_FLAG));
        float x = 0;
        float y = 0;
        sort(cardList);
        for (int i = 0; i < cardList.size(); i++) {
            CardInfo cardInfo = cardList.get(i);
            x = getCardXByType(cardInfo.getOrginIndex(), 0, false);
            y = getCardYByType(cardInfo.getOrginIndex(), 0, false);

            Bitmap bgBm = MJCardBMHelper.INSTANCE.getHorizontalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
            int bgWidth = bgBm.getWidth();
            int bgHeight = bgBm.getHeight();
            Rect src = new Rect(0, 0, bgWidth, bgHeight);
            RectF dst = new RectF(x, y, x + cardWidth, y + cardHeight);
            canvas.drawBitmap(bgBm, src, dst, mPaint);
        }

        return bitmap;
    }

    private Bitmap drawLeftChuPai(){
        if (cardList.size() <= 0) {
            return null;
        }

        int parentWidth = measureWidthByType();
        int parentHeight = measureHeightByType();
        Bitmap bitmap = Bitmap.createBitmap(parentWidth, parentHeight, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.setDrawFilter(new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG|Paint.FILTER_BITMAP_FLAG));
        float x = 0;
        float y = 0;
        sort(cardList);
        for (int i = 0; i < cardList.size(); i++) {
            CardInfo cardInfo = cardList.get(i);
            x = getCardXByType(cardInfo.getOrginIndex(), 0, false);
            y = getCardYByType(cardInfo.getOrginIndex(), 0, false);

            Bitmap bgBm = MJCardBMHelper.INSTANCE.getLeftVerticalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
            int bgWidth = bgBm.getWidth();
            int bgHeight = bgBm.getHeight();
            Rect src = new Rect(0, 0, bgWidth, bgHeight);
            RectF dst = new RectF(x, y, x + cardWidth, y + cardHeight);
            canvas.drawBitmap(bgBm, src, dst, mPaint);
        }

        return bitmap;
    }

    private Bitmap drawRightChuPai(){
        if (cardList.size() <= 0) {
            return null;
        }

        int parentWidth = measureWidthByType();
        int parentHeight = measureHeightByType();
        Bitmap bitmap = Bitmap.createBitmap(parentWidth, parentHeight, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.setDrawFilter(new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG|Paint.FILTER_BITMAP_FLAG));
        float x = 0;
        float y = 0;
        sort(cardList);
        for (int i = 0; i < cardList.size(); i++) {
            CardInfo cardInfo = cardList.get(i);
            x = getCardXByType(cardInfo.getOrginIndex(), 0, false);
            y = getCardYByType(cardInfo.getOrginIndex(), 0, false);

            Bitmap bgBm = MJCardBMHelper.INSTANCE.getRightVerticalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
            int bgWidth = bgBm.getWidth();
            int bgHeight = bgBm.getHeight();
            Rect src = new Rect(0, 0, bgWidth, bgHeight);
            RectF dst = new RectF(x, y, x + cardWidth, y + cardHeight);
            canvas.drawBitmap(bgBm, src, dst, mPaint);
        }

        return bitmap;
    }

    private Bitmap drawTopAndBottomHuaPai(){
        if (cardList.size() <= 0) {
            return null;
        }

        int parentWidth = measureWidthByType();
        int parentHeight = measureHeightByType();
        Bitmap bitmap = Bitmap.createBitmap(parentWidth, parentHeight, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.setDrawFilter(new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG|Paint.FILTER_BITMAP_FLAG));
        float x = 0;
        float y = 0;
        sort(cardList);
        for (int i = 0; i < cardList.size(); i++) {
            CardInfo cardInfo = cardList.get(i);
            int index = cardInfo.getOrginIndex();
            x = getCardXByType(index, 0, false);
            y = getCardYByType(index, 0, false);

            Bitmap bgBm = MJCardBMHelper.INSTANCE.getHorizontalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
            int bgWidth = bgBm.getWidth();
            int bgHeight = bgBm.getHeight();
            Rect src = new Rect(0, 0, bgWidth, bgHeight);
            RectF dst = new RectF(x, y, x + cardWidth, y + cardHeight);
            canvas.drawBitmap(bgBm, src, dst, mPaint);
        }

        return bitmap;
    }

    private Bitmap drawLeftHuaPai(){
        if (cardList.size() <= 0) {
            return null;
        }

        int parentWidth = measureWidthByType();
        int parentHeight = measureHeightByType();
        Bitmap bitmap = Bitmap.createBitmap(parentWidth, parentHeight, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.setDrawFilter(new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG|Paint.FILTER_BITMAP_FLAG));
        float x = 0;
        float y = 0;
        sort(cardList);
        for (int i = 0; i < cardList.size(); i++) {
            CardInfo cardInfo = cardList.get(i);
            int index = cardInfo.getOrginIndex();
            x = getCardXByType(index, 0, false);
            y = getCardYByType(index, 0, false);

            Bitmap bgBm = MJCardBMHelper.INSTANCE.getLeftHorizontalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
            int bgWidth = bgBm.getWidth();
            int bgHeight = bgBm.getHeight();
            int currentWidth = MJShowCardHelper.INSTANCE.getValue(cardWidth, 16 - index, isTransform);
            int currentHeight = MJShowCardHelper.INSTANCE.getValue(cardHeight, 16 - index, isTransform);
            Rect src = new Rect(0, 0, bgWidth, bgHeight);
            RectF dst = new RectF(x, y, x + currentWidth, y + currentHeight);
            canvas.drawBitmap(bgBm, src, dst, mPaint);
        }

        return bitmap;
    }

    private Bitmap drawRightHuaPai(){
        if (cardList.size() <= 0) {
            return null;
        }

        int parentWidth = measureWidthByType();
        int parentHeight = measureHeightByType();
        Bitmap bitmap = Bitmap.createBitmap(parentWidth, parentHeight, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.setDrawFilter(new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG|Paint.FILTER_BITMAP_FLAG));
        float x = 0;
        float y = 0;
        sort(cardList);
        for (int i = 0; i < cardList.size(); i++) {
            CardInfo cardInfo = cardList.get(i);
            int index = cardInfo.getOrginIndex();
            x = getCardXByType(index, 0, false);
            y = getCardYByType(index, 0, false);

            Bitmap bgBm = MJCardBMHelper.INSTANCE.getRightHorizontalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
            int bgWidth = bgBm.getWidth();
            int bgHeight = bgBm.getHeight();
            int currentWidth = MJShowCardHelper.INSTANCE.getValue(cardWidth, index, isTransform);
            int currentHeight = MJShowCardHelper.INSTANCE.getValue(cardHeight, index, isTransform);
            Rect src = new Rect(0, 0, bgWidth, bgHeight);
            RectF dst = new RectF(x, y, x + currentWidth, y + currentHeight);
            canvas.drawBitmap(bgBm, src, dst, mPaint);
        }

        return bitmap;
    }

    private Bitmap drawTopAndBottomMingPai(){
        if (mingList.size() <= 0) {
            return null;
        }

        int parentWidth = measureWidthByType();
        int parentHeight = measureHeightByType();
        Bitmap bitmap = Bitmap.createBitmap(parentWidth, parentHeight, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.setDrawFilter(new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG|Paint.FILTER_BITMAP_FLAG));
        float x = 0;
        float y = 0;
        for (int seqIndex = 0; seqIndex < mingList.size(); seqIndex++) {
            MJMingInfo mingInfo = mingList.get(seqIndex);
            List<CardInfo> cardSeq = mingInfo.getPaiSeq();
            for (int index = 0; index < cardSeq.size(); index++){
                CardInfo cardInfo = cardSeq.get(index);
                x = getCardXByType(index, seqIndex, mingInfo.isGang());
                y = getCardYByType(index, seqIndex, mingInfo.isGang());
                Bitmap bgBm;
                if (MJPlayerPackets.MingPaiItemType.MingPaiAnGang_VALUE == mingInfo.getType()){
                    if (index > 2){
                        bgBm = MJCardBMHelper.INSTANCE.getHorizontalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
                    } else {
                        bgBm = MJCardBMHelper.INSTANCE.getHorizontalFrontBitmap(getContext(), 0, false);
                    }
                } else {
                    bgBm = MJCardBMHelper.INSTANCE.getHorizontalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
                }

                int bgWidth = bgBm.getWidth();
                int bgHeight = bgBm.getHeight();
                Rect src = new Rect(0, 0, bgWidth, bgHeight);
                RectF dst = new RectF(x, y, x + cardWidth, y + cardHeight);
                canvas.drawBitmap(bgBm, src, dst, mPaint);
            }
        }

        return bitmap;
    }

    private Bitmap drawLeftMingPai(){
        if (mingList.size() <= 0) {
            return null;
        }

        int parentWidth = measureWidthByType();
        int parentHeight = measureHeightByType();
        Bitmap bitmap = Bitmap.createBitmap(parentWidth, parentHeight, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.setDrawFilter(new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG|Paint.FILTER_BITMAP_FLAG));
        float x = 0;
        float y = 0;
        for (int seqIndex = 0; seqIndex < mingList.size(); seqIndex++) {
            MJMingInfo mingInfo = mingList.get(seqIndex);
            List<CardInfo> cardSeq = mingInfo.getPaiSeq();
            for (int index = 0; index < cardSeq.size(); index++){
                int drawIndex = getMingCardIndex(seqIndex, index, mingInfo.isGang());
                CardInfo cardInfo = cardSeq.get(index);
                x = getCardXByType(index, seqIndex, mingInfo.isGang());
                y = getCardYByType(index, seqIndex, mingInfo.isGang());
                Bitmap bgBm;
                if (MJPlayerPackets.MingPaiItemType.MingPaiAnGang_VALUE == mingInfo.getType()){
                    if (index > 2){
                        bgBm = MJCardBMHelper.INSTANCE.getLeftHorizontalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
                    } else {
                        bgBm = MJCardBMHelper.INSTANCE.getLeftHorizontalFrontBitmap(getContext(), 0, false);
                    }
                } else {
                    bgBm = MJCardBMHelper.INSTANCE.getLeftHorizontalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
                }
                int bgWidth = bgBm.getWidth();
                int bgHeight = bgBm.getHeight();
                float drawWidth = MJShowCardHelper.INSTANCE.getValue(cardWidth, 16 - drawIndex, true);
                float drawHeight = MJShowCardHelper.INSTANCE.getValue(cardHeight, 16 - drawIndex, true);
                Rect src = new Rect(0, 0, bgWidth, bgHeight);
                RectF dst = new RectF(x, y, x + drawWidth, y + drawHeight);
                canvas.drawBitmap(bgBm, src, dst, mPaint);
            }
        }

        return bitmap;
    }

    private Bitmap drawRightMingPai(){
        if (mingList.size() <= 0) {
            return null;
        }

        int parentWidth = measureWidthByType();
        int parentHeight = measureHeightByType();
        Bitmap bitmap = Bitmap.createBitmap(parentWidth, parentHeight, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.setDrawFilter(new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG|Paint.FILTER_BITMAP_FLAG));
        float x = 0;
        float y = 0;
        for (int seqIndex = mingList.size() - 1; seqIndex >= 0; seqIndex--) {
            MJMingInfo mingInfo = mingList.get(seqIndex);
            List<CardInfo> cardSeq = mingInfo.getPaiSeq();
            for (int index = cardSeq.size() - 1; index >= 0; index--){
                int drawIndex = getMingCardIndex(seqIndex, index, mingInfo.isGang());
                CardInfo cardInfo = cardSeq.get(index);
                int uiIndex = index;
                if (mingInfo.isGang()){
                    uiIndex = index - 1;
                    if (uiIndex < 0){
                        uiIndex = 3;
                    }
                }
                x = getCardXByType(uiIndex, seqIndex, mingInfo.isGang());
                y = getCardYByType(uiIndex, seqIndex, mingInfo.isGang());
                Bitmap bgBm;
                if (MJPlayerPackets.MingPaiItemType.MingPaiAnGang_VALUE == mingInfo.getType()){
                    if (uiIndex > 2){
                        bgBm = MJCardBMHelper.INSTANCE.getRightHorizontalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
                    } else {
                        bgBm = MJCardBMHelper.INSTANCE.getRightHorizontalFrontBitmap(getContext(), 0, false);
                    }
                } else {
                    bgBm = MJCardBMHelper.INSTANCE.getRightHorizontalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
                }
                int bgWidth = bgBm.getWidth();
                int bgHeight = bgBm.getHeight();
                float drawWidth = MJShowCardHelper.INSTANCE.getValue(cardWidth, drawIndex, true);
                float drawHeight = MJShowCardHelper.INSTANCE.getValue(cardHeight, drawIndex, true);
                Rect src = new Rect(0, 0, bgWidth, bgHeight);
                RectF dst = new RectF(x, y, x + drawWidth, y + drawHeight);
                canvas.drawBitmap(bgBm, src, dst, mPaint);
            }
        }

        return bitmap;
    }

    private Bitmap drawResultPai(){
        if (cardList.size() <= 0) {
            return null;
        }

        int parentWidth = measureWidthByType();
        int parentHeight = measureHeightByType();
        Bitmap bitmap = Bitmap.createBitmap(parentWidth, parentHeight, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.setDrawFilter(new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG|Paint.FILTER_BITMAP_FLAG));
        float x = 0;
        float y = 0;
        sort(cardList);

        int mingPaiX = 0;
        int mingPaiY = 0;
        for (int seqIndex = 0; seqIndex < mingList.size(); seqIndex++) {
            MJMingInfo mingInfo = mingList.get(seqIndex);
            List<CardInfo> cardSeq = mingInfo.getPaiSeq();
            for (int index = 0; index < cardSeq.size(); index++){
                CardInfo cardInfo = cardSeq.get(index);
                Bitmap bgBm = MJCardBMHelper.INSTANCE.getVerticalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
                int bgWidth = bgBm.getWidth();
                int bgHeight = bgBm.getHeight();
                Rect src = new Rect(0, 0, bgWidth, bgHeight);
                RectF dst = new RectF(mingPaiX, mingPaiY, mingPaiX + cardWidth, mingPaiY + cardHeight);
                canvas.drawBitmap(bgBm, src, dst, mPaint);
                if (index == cardSeq.size() - 1){
                    mingPaiX += cardWidth;
                } else {
                    mingPaiX += cardOffsetX;
                }
            }
        }

        for (int i = 0; i < cardList.size(); i++) {
            CardInfo cardInfo = cardList.get(i);
            x = mingPaiX + getCardXByType(cardInfo.getOrginIndex(), 0, false);
            y = mingPaiY + getCardYByType(cardInfo.getOrginIndex(), 0, false);

            Bitmap bgBm = MJCardBMHelper.INSTANCE.getVerticalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
            int bgWidth = bgBm.getWidth();
            int bgHeight = bgBm.getHeight();
            Rect src = new Rect(0, 0, bgWidth, bgHeight);
            RectF dst = new RectF(x, y, x + cardWidth, y + cardHeight);
            canvas.drawBitmap(bgBm, src, dst, mPaint);
        }

        return bitmap;
    }

    private Bitmap drawResultHuPai(){
        if (cardList.size() <= 0) {
            return null;
        }

        int parentWidth = measureWidthByType();
        int parentHeight = measureHeightByType();
        Bitmap bitmap = Bitmap.createBitmap(parentWidth, parentHeight, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        canvas.setDrawFilter(new PaintFlagsDrawFilter(0, Paint.ANTI_ALIAS_FLAG|Paint.FILTER_BITMAP_FLAG));
        for (int i = 0; i < cardList.size(); i++) {
            CardInfo cardInfo = cardList.get(i);
            float x = getCardXByType(cardInfo.getOrginIndex(), 0, false);
            float y = getCardYByType(cardInfo.getOrginIndex(), 0, false);

            Bitmap bgBm = MJCardBMHelper.INSTANCE.getHorizontalFrontBitmap(getContext(), cardInfo.getValue(), isGoldCard(cardInfo.getValue()));
            int bgWidth = bgBm.getWidth();
            int bgHeight = bgBm.getHeight();
            Rect src = new Rect(0, 0, bgWidth, bgHeight);
            RectF dst = new RectF(x, y, x + cardWidth, y + cardHeight);
            canvas.drawBitmap(bgBm, src, dst, mPaint);
        }

        return bitmap;
    }

    private void sort(List<CardInfo> cards) {
        Collections.sort(cards, (o1, o2) -> o1.getUiIndex() - o2.getUiIndex());
    }

    public float getCardXByType(int index, int seqIndex, boolean isGang) {
        float x = 0;
        switch (cardType) {
            case MJShowCardHelper.TYPE_TOP_CHU:
                x = getTopChuCardX(index);
                break;
            case MJShowCardHelper.TYPE_BOTTOM_CHU:
                x = getBottomChuCardX(index);
                break;
            case MJShowCardHelper.TYPE_LEFT_CHU:
                x = getLeftChuCardX(index);
                break;
            case MJShowCardHelper.TYPE_RIGHT_CHU:
                x = getRightChuCardX(index);
                break;

            case MJShowCardHelper.TYPE_TOP_HUA:
                x = getTopHuaCardX(index);
                break;
            case MJShowCardHelper.TYPE_BOTTOM_HUA:
                x = getBottomHuaCardX(index);
                break;
            case MJShowCardHelper.TYPE_LEFT_HUA:
                x = getLeftHuaCardX(index);
                break;
            case MJShowCardHelper.TYPE_RIGHT_HUA:
                x = getRightHuaCardX(index);
                break;

            case MJShowCardHelper.TYPE_TOP_MING:
                x = getTopMingCardX(seqIndex, index, isGang);
                break;
            case MJShowCardHelper.TYPE_BOTTOM_MING:
                x = getBottomMingCardX(seqIndex, index, isGang);
                break;
            case MJShowCardHelper.TYPE_LEFT_MING:
                x = getLeftMingCardX(seqIndex, index, isGang);
                break;
            case MJShowCardHelper.TYPE_RIGHT_MING:
                x = getRightMingCardX(seqIndex, index, isGang);
                break;
            case MJShowCardHelper.TYPE_ROUND_RESULT_PAI:
                if (index == cardList.size() - 1){
                    x = (index - 1) * cardOffsetX + cardWidth;
                } else {
                    x = index * cardOffsetX;
                }
                break;
            case MJShowCardHelper.TYPE_ROUND_RESULT_HUA_PAI:
            case MJShowCardHelper.TYPE_TOP_RESULT_HU:
            case MJShowCardHelper.TYPE_BOTTOM_RESULT_HU:
                x = index * cardOffsetX;
                break;
            case MJShowCardHelper.TYPE_LEFT_RESULT_HU:
            case MJShowCardHelper.TYPE_RIGHT_RESULT_HU:
                x = (index % 3) * cardOffsetX;
                break;
            default:
                break;
        }

        return x;
    }

    public float getCardYByType(int index, int seqIndex, boolean isGang){
        float y = 0;
        switch (cardType) {
            case MJShowCardHelper.TYPE_TOP_CHU:
                y = getTopChuCardY(index);
                break;
            case MJShowCardHelper.TYPE_BOTTOM_CHU:
                y = getBottomChuCardY(index);
                break;
            case MJShowCardHelper.TYPE_LEFT_CHU:
                y = getLeftChuCardY(index);
                break;
            case MJShowCardHelper.TYPE_RIGHT_CHU:
                y = getRightChuCardY(index);
                break;

            case MJShowCardHelper.TYPE_TOP_HUA:
                y = getTopHuaCardY(index);
                break;
            case MJShowCardHelper.TYPE_BOTTOM_HUA:
                y = getBottomHuaCardY(index);
                break;
            case MJShowCardHelper.TYPE_LEFT_HUA:
                y = getLeftHuaCardY(index);
                break;
            case MJShowCardHelper.TYPE_RIGHT_HUA:
                y = getRightHuaCardY(index);
                break;

            case MJShowCardHelper.TYPE_TOP_MING:
                y = getTopMingCardY(seqIndex, index, isGang);
                break;
            case MJShowCardHelper.TYPE_BOTTOM_MING:
                y = getBottomMingCardY(seqIndex, index, isGang);
                break;
            case MJShowCardHelper.TYPE_LEFT_MING:
                y = getLeftMingCardY(seqIndex, index, isGang);
                break;
            case MJShowCardHelper.TYPE_RIGHT_MING:
                y = getRightMingCardY(seqIndex, index, isGang);
                break;

            case MJShowCardHelper.TYPE_ROUND_RESULT_PAI:
            case MJShowCardHelper.TYPE_ROUND_RESULT_HUA_PAI:
            case MJShowCardHelper.TYPE_TOP_RESULT_HU:
            case MJShowCardHelper.TYPE_BOTTOM_RESULT_HU:
                y = 0;
                break;
            case MJShowCardHelper.TYPE_LEFT_RESULT_HU:
            case MJShowCardHelper.TYPE_RIGHT_RESULT_HU:
                y = (index / 3) * cardOffsetY;
                break;
            default:
                break;
        }

        return y;
    }

    public int getTopChuCardX(int index) {
        int parentWidth = 5 * cardOffsetX + cardWidth;
        int x = parentWidth - cardWidth - index % 6 * cardOffsetX;
        return x;
    }
    public int getTopChuCardY(int index) {
        int parentHeight = 2 * cardOffsetY + cardHeight;
        int y = parentHeight - cardHeight - index / 6 * cardOffsetY;
        return y;
    }

    public int getTopHuaCardX(int index){
        int x = measureWidthByType() - cardWidth - index * cardOffsetX;
        return x;
    }
    public int getTopHuaCardY(int index){
        return 0;
    }

    public int getBottomChuCardX(int index) {
        int x = index % 6 * cardOffsetX;
        return x;
    }
    public int getBottomChuCardY(int index) {
        int y = index / 6 * cardOffsetY;
        return y;
    }

    public int getBottomHuaCardX(int index){
        int x = index * cardOffsetX;
        return x;
    }
    public int getBottomHuaCardY(int index){
        return 0;
    }

    public int getLeftChuCardX(int index) {
        int parentWidth = 5 * cardOffsetX + cardWidth;
        int x = parentWidth - cardWidth - (index / 4) * cardOffsetX - (index % 4) * ScreenUtil.dip2px(2f);
        return x;
    }
    public int getLeftChuCardY(int index) {
        int y = (index % 4) * cardOffsetY;
        return y;
    }

    public int getLeftHuaCardX(int index){
        int x = measureWidthByType()
                - MJShowCardHelper.INSTANCE.getValue(cardWidth, 16, isTransform)
                - MJShowCardHelper.INSTANCE.getValues(cardOffsetX, 16 - index, 16, isTransform);
        return x;
    }
    public int getLeftHuaCardY(int index){
        int y = MJShowCardHelper.INSTANCE.getValues(cardOffsetY, 16 - index, 16, isTransform);
        return y;
    }

    public int getRightChuCardX(int index) {
        int x = (index / 4) * cardOffsetX + (4 - index % 4) * ScreenUtil.dip2px(2);
        return x;
    }
    public int getRightChuCardY(int index) {
        int parentHeight = 3 * cardOffsetY + cardHeight;
        int y = parentHeight - cardHeight - (index % 4) * cardOffsetY;
        return y;
    }

    public int getRightHuaCardX(int index){
        int x = measureWidthByType()
                - MJShowCardHelper.INSTANCE.getValue(cardWidth, 0, isTransform)
                - MJShowCardHelper.INSTANCE.getValues(cardOffsetX, 0, index, isTransform);
        return x;
    }
    public int getRightHuaCardY(int index){
        int y = measureHeightByType()
                - MJShowCardHelper.INSTANCE.getValue(cardHeight, 0, isTransform)
                - MJShowCardHelper.INSTANCE.getValues(cardOffsetY, 0, index, isTransform);
        return y;
    }

    public float getTopMingCardX(int seqIndex, int indexInSeq, boolean isGang){
        if (isGang && indexInSeq > 2){
            indexInSeq = 1;
        }
        float seqWidth = 9F / 10 * cardWidth + 2 * cardOffsetX;
        float x = measureWidthByType() - cardWidth - seqIndex * seqWidth - indexInSeq * cardOffsetX;
        return x;
    }
    public int getTopMingCardY(int seqIndex, int indexInSeq, boolean isGang){
        int y = cardOffsetY;
        if (isGang && indexInSeq > 2){
            y = 0;
        }
        return y;
    }

    public int getBottomMingCardX(int seqIndex, int indexInSeq, boolean isGang){
        if (isGang && indexInSeq > 2){
            indexInSeq = 1;
        }
        int seqWidth = cardWidth + 2 * cardOffsetX;
        int x = seqIndex * seqWidth + indexInSeq * cardOffsetX;
        return x;
    }
    public int getBottomMingCardY(int seqIndex, int indexInSeq, boolean isGang){
        int y = cardOffsetY;
        if (isGang && indexInSeq > 2){
            y = 0;
        }
        return y;
    }

    public float getLeftMingCardX(int seqIndex, int indexInSeq, boolean isGang){
        float x = measureWidthByType();
        for (int i = 0; i < seqIndex; i++){
            if (i == 0){
                x -= (MJShowCardHelper.INSTANCE.getValue(cardWidth, 16 - getMingCardIndex(i, 0, false), true)
                        + MJShowCardHelper.INSTANCE.getValue(cardOffsetX, 16 - getMingCardIndex(i, 1, false), true)
                        + MJShowCardHelper.INSTANCE.getValue(cardOffsetX, 16 - getMingCardIndex(i, 2, false), true));
            } else {
                x -= (MJShowCardHelper.INSTANCE.getValue(cardOffsetX, 16 - getMingCardIndex(i, 0, false), true)
                        + MJShowCardHelper.INSTANCE.getValue(cardOffsetX, 16 - getMingCardIndex(i, 1, false), true)
                        + MJShowCardHelper.INSTANCE.getValue(cardOffsetX, 16 - getMingCardIndex(i, 2, false), true));
            }

        }

        if (seqIndex == 0){
            x -= MJShowCardHelper.INSTANCE.getValue(cardWidth, 16, true);
        } else {
            x -= MJShowCardHelper.INSTANCE.getValue(cardOffsetX, 16 - getMingCardIndex(seqIndex, 0, false), true);
        }

        if (indexInSeq == 0){
            return x;
        }

        if (indexInSeq == 1){
            x -= (MJShowCardHelper.INSTANCE.getValue(cardOffsetX, 16 - getMingCardIndex(seqIndex, 1, false), true));
        } else {
            x -= (MJShowCardHelper.INSTANCE.getValue(cardOffsetX, 16 - getMingCardIndex(seqIndex, 1, false), true)
                    + MJShowCardHelper.INSTANCE.getValue(cardOffsetX, 16 - getMingCardIndex(seqIndex, 2, false), true));
        }

        return x;
    }
    public float getLeftMingCardY(int seqIndex, int indexInSeq, boolean isGang){
        float y = 0F;
        for (int i = 0; i < seqIndex; i++){
            y += (MJShowCardHelper.INSTANCE.getValue(cardOffsetY, 16 - getMingCardIndex(i, 0, false), true)
                    + MJShowCardHelper.INSTANCE.getValue(cardOffsetY, 16 - getMingCardIndex(i, 1, false), true)
                    + 0.95F * MJShowCardHelper.INSTANCE.getValue(cardHeight, 16 - getMingCardIndex(i, 2, false), true));
        }

        if (indexInSeq == 0){
            return y;
        }

        if (indexInSeq > 2){
            y += 2F/ 3 * MJShowCardHelper.INSTANCE.getValue(cardOffsetY, 16 - getMingCardIndex(seqIndex, 0, false), true);
        } else if (indexInSeq == 1){
            y += MJShowCardHelper.INSTANCE.getValue(cardOffsetY, 16 - getMingCardIndex(seqIndex, 0, false), true);
        } else {
            y += (MJShowCardHelper.INSTANCE.getValue(cardOffsetY, 16 - getMingCardIndex(seqIndex, 0, false), true)
                    + MJShowCardHelper.INSTANCE.getValue(cardOffsetY, 16 - getMingCardIndex(seqIndex, 1, false), true));
        }

        return y;
    }

    public float getRightMingCardX(int seqIndex, int indexInSeq, boolean isGang){
        float x = measureWidthByType();
        for (int i = 0; i < seqIndex; i++){
            if (i == 0){
                x -= (MJShowCardHelper.INSTANCE.getValue(cardWidth, getMingCardIndex(i, 0, false), true)
                        + MJShowCardHelper.INSTANCE.getValue(cardOffsetX, getMingCardIndex(i, 1, false), true)
                        + MJShowCardHelper.INSTANCE.getValue(cardOffsetX, getMingCardIndex(i, 2, false), true));
            } else {
                x -= (MJShowCardHelper.INSTANCE.getValue(cardOffsetX, getMingCardIndex(i, 0, false), true)
                        + MJShowCardHelper.INSTANCE.getValue(cardOffsetX, getMingCardIndex(i, 1, false), true)
                        + MJShowCardHelper.INSTANCE.getValue(cardOffsetX, getMingCardIndex(i, 2, false), true));
            }
        }

        if (seqIndex == 0){
            x -= MJShowCardHelper.INSTANCE.getValue(cardWidth, 0, true);
        } else {
            x -= MJShowCardHelper.INSTANCE.getValue(cardOffsetX, getMingCardIndex(seqIndex, 0, false), true);
        }

        if (indexInSeq == 0 || indexInSeq > 2){
            return x;
        }

        if (indexInSeq == 1){
            x -= (MJShowCardHelper.INSTANCE.getValue(cardOffsetX, getMingCardIndex(seqIndex, 1, false), true));
        } else {
            x -= (MJShowCardHelper.INSTANCE.getValue(cardOffsetX, getMingCardIndex(seqIndex, 1, false), true)
                    + MJShowCardHelper.INSTANCE.getValue(cardOffsetX, getMingCardIndex(seqIndex, 2, false), true));
        }

        return x;
    }
    public float getRightMingCardY(int seqIndex, int indexInSeq, boolean isGang){
        float y = measureHeightByType();
        for (int i = 0; i < seqIndex; i++){
            y -= 0.98F * (MJShowCardHelper.INSTANCE.getValue(cardHeight, getMingCardIndex(i, 0, false), true)
                    + MJShowCardHelper.INSTANCE.getValue(cardOffsetY, getMingCardIndex(i, 1, false), true)
                    + MJShowCardHelper.INSTANCE.getValue(cardOffsetY, getMingCardIndex(i, 2, false), true));
        }

        y -= MJShowCardHelper.INSTANCE.getValue(cardHeight, getMingCardIndex(seqIndex, 0, false), true);

        if (indexInSeq == 0){
            return y;
        }

        if (indexInSeq > 2){
            y -= (MJShowCardHelper.INSTANCE.getValue(cardHeight, getMingCardIndex(seqIndex, 1, false), true));
        } else if (indexInSeq == 1){
            y -= (MJShowCardHelper.INSTANCE.getValue(cardOffsetY, getMingCardIndex(seqIndex, 1, false), true));
        } else {
            y -= (MJShowCardHelper.INSTANCE.getValue(cardOffsetY, getMingCardIndex(seqIndex, 1, false), true)
                    + MJShowCardHelper.INSTANCE.getValue(cardOffsetY, getMingCardIndex(seqIndex, 2, false), true));
        }

        return y;
    }

    public int getChildCount() {
        if (cardType == MJShowCardHelper.TYPE_TOP_MING
                || cardType == MJShowCardHelper.TYPE_LEFT_MING
                || cardType == MJShowCardHelper.TYPE_BOTTOM_MING
                || cardType == MJShowCardHelper.TYPE_RIGHT_MING){
            return mingList.size() * 3;
        } else {
            return cardList.size();
        }
    }

    public int getMingCardIndex(int seqIndex, int indexInSeq, boolean isGang){
        int index = seqIndex * 3 + indexInSeq;
        if (isGang && indexInSeq > 2){
            index = seqIndex * 3 + 1;
        }

        return index;
    }

    public void clear() {
        cardList.clear();
        mingList.clear();

        requestLayout();
        invalidate();
    }

    private boolean isChuLayout(){
        boolean isChu = false;
        if (cardType == MJShowCardHelper.TYPE_TOP_CHU
                || cardType == MJShowCardHelper.TYPE_LEFT_CHU
                || cardType == MJShowCardHelper.TYPE_BOTTOM_CHU
                || cardType == MJShowCardHelper.TYPE_RIGHT_CHU){
            isChu = true;
        }

        return isChu;
    }

    public boolean isGoldCard(int cardValue){
        if (viewModel == null || viewModel.getGameData().getValue() == null
                || viewModel.getGameData().getValue().table_ == null
                || viewModel.getGameData().getValue().table_.goldTiles_ == null
                || viewModel.getGameData().getValue().table_.goldTiles_.size() <= 0){
            return false;
        }

        if (viewModel.getGameData().getValue().table_.goldTiles_.contains(cardValue)){
            return true;
        }

        return false;
    }
}
