package com.huiwan.component.prop;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.huiwan.base.ktx.ViewExtKt;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.NinePatchUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.component.util.PropResLoaderUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.propextra.ChatBubbleItem;
import com.huiwan.configservice.model.PropItem;
import com.wepie.libimageloader.WpImageLoadListener;

import java.lang.ref.WeakReference;

/**
 * date 2019-07-24
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class ChatMsgBubbleItem extends FrameLayout {
    private static final int SENDER_WIDTH = ScreenUtil.dip2px(27);
    private static final int RECEIVER_WIDTH = ScreenUtil.dip2px(23);
    private static final int SVGA_HEIGHT = ScreenUtil.dip2px(23);
    private static final int MAX_FOLD_LEVEL = 3;
    private static final ChatBubbleItem emptyBubbleItem;

    static {
        emptyBubbleItem = JsonUtil.fromJson("{\"text_color\":\"#000000\",\"gift_div_color\":\"#000000\",\"gift_tip_color\":\"#999999\",\"gift_desc_color\":\"#ff004c\",\"deep_link_color\":\"#49CCB6\"}", ChatBubbleItem.class);
    }

    private final RelativeLayout bubbleBg;
    private final View bubbleFoldIv;


    private ChatBubbleItem bubbleItem = emptyBubbleItem;

    public ChatMsgBubbleItem(Context context) {
        this(context, null);
    }

    public ChatMsgBubbleItem(Context context, AttributeSet attrs) {
        super(context, attrs);
        LayoutInflater.from(context).inflate(R.layout.chat_msg_bubble_item, this);
        bubbleBg = findViewById(R.id.bubble_bg);
        bubbleFoldIv = findViewById(R.id.bubble_fold_iv);
        initBubble();
    }

    public void showBubble(int propId, boolean self) {
        showBubble(propId, self, 1);
    }

    public void showBubble(int propId, boolean self, int foldLevel) {
        foldLevel = Math.max(1, foldLevel);
        foldLevel = Math.min(MAX_FOLD_LEVEL, foldLevel);
        for (int i = 0; i < MAX_FOLD_LEVEL; i++) {
            View view = bubbleBg.getChildAt(MAX_FOLD_LEVEL - i - 1);
            if (foldLevel > i) {
                view.setVisibility(VISIBLE);
                showBubble(view, propId, self, i + 1);
            } else {
                view.setVisibility(GONE);
            }
            if (foldLevel == 1) {
                ((RelativeLayout.LayoutParams) view.getLayoutParams()).bottomMargin = 0;
            } else {
                ((RelativeLayout.LayoutParams) view.getLayoutParams()).bottomMargin = (MAX_FOLD_LEVEL - i - 1) * ScreenUtil.dip2px(6);
            }
        }
        bubbleFoldIv.setVisibility(foldLevel > 1 ? View.VISIBLE : View.GONE);
    }

    public void showBubble(ChatBubbleItem bubbleItem, boolean self) {
        View view = View.inflate(getContext(), R.layout.chat_msg_bubble_item_bg, null);
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        showBubble(view, bubbleItem, self, 1);
        bubbleBg.addView(view, params);
    }

    public int getExtraPadding(int foldLevel) {
        foldLevel = Math.max(1, foldLevel);
        foldLevel = Math.min(3, foldLevel);
        if (foldLevel > 1) {
            return (MAX_FOLD_LEVEL - 1) * ScreenUtil.dip2px(6);
        }
        return 0;
    }

    private void showBubble(View view, int propId, boolean self, int foldLevel) {
        if (propId < 1) {
            showDefaultBubble(view, self, foldLevel);
        } else {
            PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(propId);
            ChatBubbleItem bubbleItem;
            if (propItem != null && propItem.getType() == PropItem.TYPE_CHAT_BUBBLE && ((bubbleItem = propItem.getChatBubbleItem()) != null)) {
                showBubble(view, bubbleItem, self, foldLevel);
            } else {
                showDefaultBubble(view, self, foldLevel);
            }
        }
    }

    public ChatBubbleItem getBubbleItem() {
        return bubbleItem;
    }

    public int getTextColor() {
        return bubbleItem.getTextColor();
    }

    public int getGiftDivColor() {
        return bubbleItem.getGiftDivColor();
    }

    public int getGiftDescColor() {
        return bubbleItem.getGiftDescColor();
    }

    public int getGiftTipColor() {
        return bubbleItem.getGiftTipColor();
    }

    public int getDeepLinkColor() {
        return bubbleItem.getDeepLinkColor();
    }

    void showDefaultBubble(View view, boolean self, int foldLevel) {
        bubbleItem = emptyBubbleItem;
        view.findViewById(R.id.lt_svga).setVisibility(GONE);
        view.findViewById(R.id.rt_svga).setVisibility(GONE);
        view.findViewById(R.id.lb_svga).setVisibility(GONE);
        view.findViewById(R.id.rb_svga).setVisibility(GONE);
        ImageView bgIv = view.findViewById(R.id.bg_iv);
        bgIv.setBackgroundResource(self ? R.drawable.bubble_self_default : R.drawable.bubble_other_default);
        Drawable background = bgIv.getBackground();
        if (background != null) {
            background = background.mutate();
            if (foldLevel == MAX_FOLD_LEVEL) {
                background.setColorFilter(Color.parseColor("#F1F3F6"), PorterDuff.Mode.SRC_ATOP);
            } else if (foldLevel > 1) {
                background.setColorFilter(Color.parseColor("#ECEEF4"), PorterDuff.Mode.SRC_ATOP);
            }
        }
        view.setAlpha(1);
        view.setScaleX((float) Math.pow(0.9f, foldLevel - 1));
    }

    private void initBubble() {
        int level = 1;
        while (level <= MAX_FOLD_LEVEL) {
            View view = View.inflate(getContext(), R.layout.chat_msg_bubble_item_bg, null);
            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            view.setLayoutParams(params);
            view.setVisibility(GONE);
            bubbleBg.addView(view, 0);
            level++;
        }
    }

    private void showBubble(View view, ChatBubbleItem bubbleItem, boolean self, int foldLevel) {
        this.bubbleItem = bubbleItem;
        float scale = (float) Math.pow(0.9, foldLevel - 1);
        if (self) {
            view.setScaleX(scale);
        } else {
            view.setScaleX(-scale);
        }
        updateBg(ChatBubbleItem.getLocalPath(bubbleItem.bgImgUrl), bubbleItem.bgImgUrl, view.findViewById(R.id.bg_iv));
        ImageView ltSvga = view.findViewById(R.id.lt_svga);
        ImageView rtSvga = view.findViewById(R.id.rt_svga);
        ImageView lbSvga = view.findViewById(R.id.lb_svga);
        ImageView rbSvga = view.findViewById(R.id.rb_svga);
        updateSenderLp(rtSvga, scale);
        updateSenderLp(rbSvga, scale);
        updateReceiverLp(ltSvga, scale);
        updateReceiverLp(lbSvga, scale);
        checkUpdateSvga(ltSvga, bubbleItem.animLt);
        checkUpdateSvga(rtSvga, bubbleItem.animRt);
        checkUpdateSvga(lbSvga, bubbleItem.animLb);
        checkUpdateSvga(rbSvga, bubbleItem.animRb);
        view.setPadding(0, 0, 0, 0);
        float alpha;
        switch (foldLevel) {
            case 2:
                alpha = 0.4f;
                break;
            case 3:
                alpha = 0.1f;
                break;
            default:
                alpha = 1;
                break;
        }
        view.setAlpha(alpha);
    }

    private void updateBg(String localPath, String url, ImageView bgIv) {
        if (!TextUtils.isEmpty(url)) {
            PropResLoaderUtil.INSTANCE.loadResForDownload(url, localPath, null, ViewExtKt.getLife(bgIv), new BgListener(bgIv), null);
        } else {
            bgIv.setBackground(null);
        }
    }

    /**
     * 后台加载，转换 nine_patch drawable
     */
    static class BgListener implements WpImageLoadListener<Object> {
        final WeakReference<View> v;

        public BgListener(View v) {
            this.v = new WeakReference<>(v);
        }

        @Override
        public boolean onComplete(Object model, Drawable drawable) {
            View view = v.get();
            if (view != null && drawable instanceof BitmapDrawable) {
                Bitmap bitmap = ((BitmapDrawable) drawable).getBitmap();
                Drawable d = NinePatchUtil.createDefaultNinePatchDrawable(view.getResources(), bitmap);
                view.setBackground(d);
            }
            return true;
        }

        @Override
        public boolean onFailed(Object model, Exception e) {
            View view = v.get();
            if (view != null) {
                view.setBackground(null);
            }
            return true;
        }
    }

    private void updateSenderLp(ImageView svgaImageView, float scale) {
        svgaImageView.getLayoutParams().width = (int) (SENDER_WIDTH * scale);
        svgaImageView.getLayoutParams().height = (int) (SVGA_HEIGHT * scale);
    }

    private void updateReceiverLp(ImageView svgaImageView, float scale) {
        svgaImageView.getLayoutParams().width = (int) (RECEIVER_WIDTH * scale);
        svgaImageView.getLayoutParams().height = (int) (SVGA_HEIGHT * scale);
    }

    private void checkUpdateSvga(final ImageView svgaImageView, String url) {
        String localPath = ChatBubbleItem.getLocalPath(url);
        if (svgaImageView.getTag() == localPath) {
            return;
        }
        svgaImageView.setTag(localPath);
        if (TextUtils.isEmpty(localPath)) {
            svgaImageView.setVisibility(GONE);
        } else {
            PropResLoaderUtil.INSTANCE.loadResForDownload(url, localPath, svgaImageView, null);
            svgaImageView.setVisibility(VISIBLE);
        }
    }
}
