#!/bin/bash
WORKSPACE="$1"
branch="$2"
channel="$3"
job_id="$4"

# 飞书Webhook配置
FEISHU_WEBHOOK_URL="https://open.feishu.cn/open-apis/bot/v2/hook/YOUR_WEBHOOK_TOKEN"

# 检查必要配置
check_feishu_config() {
    if [ -z "$FEISHU_WEBHOOK_URL" ] || [ "$FEISHU_WEBHOOK_URL" = "https://open.feishu.cn/open-apis/bot/v2/hook/YOUR_WEBHOOK_TOKEN" ]; then
        echo "警告: 飞书Webhook URL未配置，将跳过消息发送"
        return 1
    fi
    
    # 检查curl是否可用
    if ! command -v curl &> /dev/null; then
        echo "错误: curl命令不可用，无法发送飞书消息"
        return 1
    fi
    
    return 0
}
# 获取.gradle目录路径
GRADLE_DIR="$WORKSPACE/wejoy-android/.gradle"
# 定义记录lint执行时间的文件
LINT_RECORD_FILE="$GRADLE_DIR/last_lint_execution_${branch}"
echo "$LINT_RECORD_FILE"
# 检查.gradle目录是否存在，不存在则创建
if [ ! -d "$GRADLE_DIR" ]; then
    mkdir -p "$GRADLE_DIR"
fi

# 获取当前日期（只取年月日）
CURRENT_DATE=$(date +"%Y-%m-%d")
codeDir=$WORKSPACE/wejoy-android

# 发送飞书消息函数
send_feishu_message() {
    local title="$1"
    local status="$2"
    local message="$3"
    local lint_url="$4"
    
    # 检查配置
    if ! check_feishu_config; then
        return 1
    fi
    
    # 根据状态设置颜色
    local color="green"
    if [ "$status" = "失败" ]; then
        color="red"
    elif [ "$status" = "警告" ]; then
        color="orange"
    fi
    
    # 构造飞书卡片消息
    local json_payload=$(cat <<EOF
{
    "msg_type": "interactive",
    "card": {
        "elements": [
            {
                "tag": "div",
                "text": {
                    "content": "**项目**: WePlay Android\n**分支**: $branch\n**渠道**: $channel\n**状态**: $status\n**时间**: $(date '+%Y-%m-%d %H:%M:%S')",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "$message",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "action",
                "actions": [
                    {
                        "tag": "button",
                        "text": {
                            "content": "查看Lint报告",
                            "tag": "plain_text"
                        },
                        "type": "primary",
                        "url": "$lint_url"
                    }
                ]
            }
        ],
        "header": {
            "title": {
                "content": "$title",
                "tag": "plain_text"
            },
            "template": "$color"
        }
    }
}
EOF
    )
    
    # 发送消息（带重试机制）
    local max_retries=3
    local retry_count=0
    
    while [ $retry_count -lt $max_retries ]; do
        echo "尝试发送飞书消息 (第 $((retry_count + 1)) 次)..."
        
        response=$(curl -s -w "%{http_code}" -X POST \
            --connect-timeout 10 \
            --max-time 30 \
            -H "Content-Type: application/json" \
            -d "$json_payload" \
            "$FEISHU_WEBHOOK_URL" 2>/dev/null)
        
        # 检查发送结果
        if [ $? -eq 0 ]; then
            http_code="${response: -3}"
            response_body="${response%???}"
            
            if [ "$http_code" -eq 200 ]; then
                echo "飞书消息发送成功"
                return 0
            else
                echo "飞书消息发送失败，HTTP状态码: $http_code"
                if [ -n "$response_body" ]; then
                    echo "响应内容: $response_body"
                fi
            fi
        else
            echo "网络请求失败"
        fi
        
        retry_count=$((retry_count + 1))
        if [ $retry_count -lt $max_retries ]; then
            echo "等待 2 秒后重试..."
            sleep 2
        fi
    done
    
    echo "飞书消息发送失败，已重试 $max_retries 次"
    return 1
}

## 检查记录文件是否存在
#if [ -f "$LINT_RECORD_FILE" ]; then
#    # 读取上次执行lint的日期
#    LAST_EXECUTION_DATE=$(cat "$LINT_RECORD_FILE")
#
#    # 比较日期，如果已经是今天，则不执行
#    if [ "$CURRENT_DATE" = "$LAST_EXECUTION_DATE" ]; then
#        echo "Lint检查今天已经执行过，跳过执行"
#        exit 0
#    fi
#fi
#
## 执行lint检查
#echo "执行lint检查"
#cd $codeDir
#./gradlew ":wepie:lint${channel}Debug"
## 记录执行时间
#echo "$CURRENT_DATE" > "$LINT_RECORD_FILE"
#echo "Lint检查执行完成"
LINT_REPORT_FILE="$codeDir/wepie/build/reports/lint-results-${channel}Debug.html"
if [ -f "$LINT_REPORT_FILE" ]; then
  echo "Lint report generated successfully!"
  NODE_NAME=$(basename "$WORKSPACE")
  url="https://jk.wepieoa.com/view/WePlay%E5%AE%A2%E6%88%B7%E7%AB%AF/job/${NODE_NAME}/${job_id}/artifact/wejoy-android/wepie/build/reports/lint-results-${channel}Debug.html"
  
  # 发送飞书通知
  echo "正在发送飞书通知..."
  send_feishu_message \
    "Android Lint检查完成" \
    "成功" \
    "✅ Lint检查已完成，报告已生成\n📝 **Job ID**: $job_id\n🔗 点击下方按钮查看详细报告" \
    "$url"
  
  if [ $? -eq 0 ]; then
    echo "飞书通知发送成功"
  else
    echo "飞书通知发送失败，但lint检查已完成"
  fi
else
  echo "Lint report not found!"
  # 发送失败通知
  send_feishu_message \
    "Android Lint检查失败" \
    "失败" \
    "❌ Lint检查失败，未找到报告文件\n📝 **Job ID**: $job_id\n📁 **预期文件**: $LINT_REPORT_FILE" \
    "https://jk.wepieoa.com/view/WePlay%E5%AE%A2%E6%88%B7%E7%AB%AF/job/$(basename "$WORKSPACE")/${job_id}/"
fi