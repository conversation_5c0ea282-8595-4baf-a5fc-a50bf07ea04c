// 执行 ./gradlew :wepie:CopyStrings
task CopyStrings {
    doLast {
        def dirList = []
        rootProject.allprojects.forEach { p ->
            def sourceSets = p.android.sourceSets
            if (sourceSets != null) {
                sourceSets.main.res.srcDirs.forEach { dir ->
                    def f = new File(dir.toString(), "values-zh")
                    if (f.exists()) {
                        dirList.add(f)
                    }
                }
            }
        }

        def stringSet = new HashSet<String>()
        def destXml = new Node(null, "resources")

        dirList.each { dir ->
            for (File f in dir.listFiles()) {
                def xmlContent = parseXml(f)
                xmlContent.each { node ->
                    if (stringSet.add(node.attribute("name"))) {
                        destXml.appendNode(node.name(), node.attributes(), node.value())
                    } else {
                        println "Node:${node.attribute("name")} ${node.text()}"
                    }
                }
            }
        }
        printXml(destXml, project.file("total_strings.xml"))
    }
}

task MergeStrings {
    doFirst {
        doMergeString("values")
        doMergeString("values-en")
        doMergeString("values-ar")
        doMergeString("values-tr")
        doMergeString("values-hi")
        doMergeString("values-zh")
    }
}

task GetAllResDir {
    doFirst {
        def allFiles = []
        rootProject.allprojects.forEach { p ->
            try {
                p.android.sourceSets.main.res.srcDirs.forEach { dir ->
                    def f = new File(dir.toString())
                    if (f.exists()) {
                        allFiles.add(f.getAbsolutePath())
                    }
                }
            } catch (NullPointerException e) {
                //ignore
            } catch (Exception e) {
                e.printStackTrace()
                println("error===" + p + " " + e)
            }
        }
        def f = rootProject.file("build/res_dir.tmp")
        f.createNewFile()
        f.withWriter { writer ->
            allFiles.each { path ->
                writer.write(path)
                writer.write("\n")
            }
        }
    }
}

def doMergeString(String destLang) {
    def translationFile = project(":lib:language").file("build/generated/res/resValues/debug/${destLang}/strings.xml")
    if (!translationFile.exists()) {
        throw IllegalStateException("can not find " + translationFile)
    }
    def translationMap = [:]
    parseXml(translationFile).each { node ->
        translationMap[node.attribute("name")] = node.text()
    }
    def checkMap = [:]
    checkMap.putAll(translationMap)

    rootProject.allprojects.forEach { p ->
        def sourceSets = p.android.sourceSets
        if (sourceSets != null) {
            sourceSets.main.res.srcDirs.forEach { dir ->
                def destValuesDir = new File(dir.toString(), destLang)
                for (File f : destValuesDir.listFiles()) {
                    def change = false
                    def stringLines = new ArrayList<String>()
                    f.readLines().forEach {
                        def s = it.trim()
                        if (s.startsWith("<string name=\"")) {
                            def key = s.substring(14, s.indexOf("\"", 14))
                            def replace = translationMap.get(key)
                            if (replace != null) {
                                checkMap.remove(key)
                                change = true
                                stringLines.add("    <string name=\"${key}\">${replace}</string>")
                            } else {
                                stringLines.add(it)
                            }
                        } else {
                            stringLines.add(it)
                        }
                    }
                    if (change) {
                        f.write(stringLines.join("\n"))
                    }
                }
            }
        }
    }

    checkMap.each { key, value ->
        println("not found " + key + " " + value)
    }
}

def parseXml(File file) {
    def xmlParser = new XmlParser()
    def xmlContent = xmlParser.parse(file)
    return xmlContent
}

def printXml(Node node, File outputFile) {
    outputFile.withWriter { writer ->
        writer.write(printNode(node))
    }
}

def printNode(Node node) {
    def writer = new StringWriter()
    def printer = new XmlNodePrinter(new PrintWriter(writer), "    ", "\"")
    printer.setPreserveWhitespace(true)
    printer.print(node)
    return writer.toString()
}