# built application files
apk/*.apk
*.ap_
*.apk

# files for the dex VM
*.dex

# Java class files
*.class

# generated files
gen/

# Local configuration file (sdk path, etc)
local.properties

# Eclipse project files
.classpath
.project
# .properties
project.properties
.settings
# Proguard folder generated by Eclipse
proguard/
product/

# Intellij project files
*.iml
*.ipr
*.iws
.idea/

.kotlin/

# release file
build.xml
proguard-project.txt
ant.properties
wepie/official

#gradle
build/
#gradle/
.gradle
gradlew*
captures/
gradle.properties

#pack
/pack_apk/json.txt
*.zip
!ice_ball.zip
!ice_ball_canvas.zip
!cocos_game_*.zip
!native_game_*.zip
pack_version.txt

.DS_Store
.qshell

project_info.txt
qiniu_url.txt
ajcore*.txt
/.bash_profile

.cxx
/module
/component
/service
/lib

./daemon
/compress_after_image/
/compress_before_image/
tools/log

cocos_build_in_asset/src/main/assets
/local-plugins/plugins/bin/
