package com.wejoy.jackaroo.qualifying

import android.os.Bundle
import android.view.LayoutInflater
import androidx.collection.ArrayMap
import com.huiwan.base.util.StatusBarUtil
import com.huiwan.component.activity.BaseActivity
import com.huiwan.constants.GameType
import com.wejoy.jackaroo.qualifying.view.JKQualifyingHomeView
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.wespy.R
import com.wepie.wespy.databinding.JackarooQualifyingHomeActivityBinding

class JackarooQualifyingActivity : BaseActivity() {
    private lateinit var homeView: JKQualifyingHomeView
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        StatusBarUtil.initStatusBar(this)
        StatusBarUtil.setStatusFontWhiteColor(this)
        window.setBackgroundDrawableResource(R.color.color_2a1b54)
        val binding = JackarooQualifyingHomeActivityBinding.inflate(LayoutInflater.from(this))
        setContentView(binding.root)
        homeView = JKQualifyingHomeView(binding)
        StatusBarUtil.fitNavigationBar(binding.root)
        reportViewScreen()
    }

    private fun reportViewScreen() {
        val map: MutableMap<String, Any> = ArrayMap()
        map["game_type"] = GameType.GAME_TYPE_JACKAROO
        TrackUtil.appViewScreen(TrackScreenName.QUALIFYING_HOME_PAGE, map)
    }
}