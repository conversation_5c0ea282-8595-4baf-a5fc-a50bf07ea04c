package com.wejoy.jackaroo.record

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import com.huiwan.base.collectOn
import com.huiwan.base.ktx.dp
import com.huiwan.base.util.ScreenUtil
import com.huiwan.configservice.constentity.ConstV3Info
import com.huiwan.configservice.editionentity.instance
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.WebApi
import com.huiwan.widget.decoration.SpaceItemDecoration
import com.wepie.module.rank.activity.NewRankActivity
import com.wepie.wespy.databinding.JackarooCollectionFragmentContentBinding
import com.wepie.wespy.module.common.jump.JumpUtil
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.launch

class CollectionFragment : Fragment() {
    private lateinit var binding: JackarooCollectionFragmentContentBinding
    private val viewModel: JackarooGameCareerViewModel by lazy { ViewModelProvider(requireActivity())[JackarooGameCareerViewModel::class.java] }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return JackarooCollectionFragmentContentBinding.inflate(inflater, container, false).let {
            binding = it
            it.root
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        initView()
        initData()
    }

    private fun onClickCollectionRank(context: Context) {
        JumpUtil.gotoRankActivity(context, NewRankActivity.RANK_TAB_INDEX_JK, 1, 1)
    }

    private fun onClickCollectionValueHelp(context: Context) {
        ApiService.of(WebApi::class.java).showWebDialog(
            context,
            ConstV3Info::class.instance().collectionHelpUrl,
            0,
            0f
        )
    }

    private fun initView() {
        val adapter = CollectionPageAdapter(object : CollectionGradeAction {
            override fun onClickGrade(context: Context, grade: Int) {
                viewModel.notifyEvent(JackarooGameCareerEvent.ShowGradeDetail(grade))
            }
        })
        val headAdapter = CollectionPageHeadAdapter(
            ::onClickCollectionValueHelp,
            ::onClickCollectionRank
        )
        binding.collectionRv.adapter = ConcatAdapter(headAdapter, adapter)
        binding.collectionRv.layoutManager =
            LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false)
        binding.collectionRv.addItemDecoration(SpaceItemDecoration(0, 0, 12.dp, 0))
        viewModel.collectionPageStateFlow.collectOnCreate {
            if (it is CollectionPageState.LoadSuccess) {
                headAdapter.update(it.headInfo)
                adapter.refresh(it.gradeOverview)
            }
        }
    }

    private fun <T> Flow<T>.collectOnCreate(collect: FlowCollector<T>) {
        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.CREATED) {
                <EMAIL>(collect)
            }
        }
    }

    private fun initData() {
        viewModel.flow.collectOn(lifecycleScope) {
            if (it is JackarooGameCareerEvent.ShowGradeDetail) {
                JackarooCollectionDialogFragment.show(requireActivity(), viewModel.uid, it.grade)
            }
        }
    }
}