package com.wejoy.jackaroo.record

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.view.isVisible
import androidx.core.view.setPadding
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.huiwan.base.ui.Utils.dp
import com.huiwan.base.ui.Utils.gone
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.widget.SimpleOutlineProvider
import com.huiwan.widget.image.DrawableUtil
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.databinding.CollectionGradePropItemBinding

internal class CollectionPropItemAdapter :
    RecyclerView.Adapter<CollectionPropItemVh>() {
    private val data: MutableList<CollectionPropItemData> = ArrayList()
    private var onClickItem: () -> Unit = {}
    fun refresh(data: List<CollectionPropItemData>, clickItem: () -> Unit) {
        this.onClickItem = clickItem
        this.data.clear()
        this.data.addAll(data)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CollectionPropItemVh {
        return CollectionPropItemVh(
            CollectionGradePropItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            ), onClickItem
        )
    }

    override fun getItemCount(): Int = data.size

    override fun onBindViewHolder(holder: CollectionPropItemVh, position: Int) {
        holder.update(data[position])
    }
}

internal class CollectionPropItemVh(
    private val binding: CollectionGradePropItemBinding,
    private val onClickItem: () -> Unit
) :
    ViewHolder(binding.root) {
    init {
        binding.root.setOnDoubleClick {
            onClickItem()
        }
        binding.lockIv.outlineProvider = SimpleOutlineProvider(8.dp.toFloat())
        binding.lockIv.clipToOutline = true
        binding.propIv.background = DrawableUtil.genColorRadius(Color.parseColor("#e6e7ec"), 8)
    }

    fun update(item: CollectionPropItemData) {
        binding.propTv.text = item.propName
        if (item.isEmpty) {
            binding.lockIv.gone()
            binding.propIv.setPadding(9.dp)
            binding.propIv.setImageResource(R.drawable.jackaroo_collection_prop_empty)
            return
        }
        binding.propIv.setPadding(5.dp)
        WpImageLoader.load(
            item.propModel,
            binding.propIv, ImageLoadInfo.newGameSkinInfo().setCornerPixel(4.dp)
        )
        binding.lockIv.isVisible = !item.hasObtained
    }
}