package com.wejoy.jackaroo.record

import com.huiwan.configservice.editionentity.PropItemConfig
import com.huiwan.configservice.editionentity.instance
import com.wepie.wespy.R

internal fun CollectionGradeItem.toCollectionPageItem(): CollectionPageItem {
    return CollectionPageItem(
        grade = grade,
        ownedNum = obtainedNum,
        totalNum = calGradeTotalNum(),
        collectionPropItems = convertData(
            obtainedIds = overViewObtainedIdList,
            notObtainedIds = getNotObtainedLargestNId(CollectionGradeItem.MAX_OVERVIEW_PROP_NUM - overViewObtainedIdList.size)
        )
    )
}

private fun convertData(
    obtainedIds: List<Int>,
    notObtainedIds: List<Int>
): List<CollectionPropItemData> {
    if (obtainedIds.size >= CollectionGradeItem.MAX_OVERVIEW_PROP_NUM) {
        return obtainedIds.subList(
            0,
            CollectionGradeItem.MAX_OVERVIEW_PROP_NUM
        ).map { mapPropId2Data(it, true) }
    }
    val list = ArrayList<CollectionPropItemData>()
    list.addAll(obtainedIds.map { mapPropId2Data(it, true) })
    list.addAll(notObtainedIds.map {
        mapPropId2Data(it, false)
    })
    return list.fillToFixedSize()
}

private fun List<CollectionPropItemData>.fillToFixedSize(targetSize: Int = CollectionGradeItem.MAX_OVERVIEW_PROP_NUM): List<CollectionPropItemData> {
    return if (size < targetSize) {
        ArrayList<CollectionPropItemData>().apply {
            addAll(this@fillToFixedSize)
            repeat(targetSize - size) {
                add(EmptyPropItemData)
            }
        }
    } else if (size > targetSize) {
        this.subList(0, targetSize)
    } else {
        this
    }
}

private val EmptyPropItemData =
    CollectionPropItemData(true, R.drawable.jackaroo_collection_prop_empty, "", false)

private fun mapPropId2Data(propId: Int, hasObtained: Boolean): CollectionPropItemData {
    val propConfig =
        PropItemConfig::class.instance().getPropItem(propId) ?: return EmptyPropItemData
    return CollectionPropItemData(
        false,
        propConfig.mediaUrl ?: "",
        propConfig.name,
        hasObtained
    )
}