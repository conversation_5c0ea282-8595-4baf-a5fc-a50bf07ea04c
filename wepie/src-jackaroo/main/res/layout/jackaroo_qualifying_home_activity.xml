<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:theme="@style/AppTheme.QualityPage"
    tools:background="@color/color_2a1b54">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/top_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/bg_iv"
            android:layout_width="match_parent"
            android:layout_height="252dp"
            android:scaleType="centerCrop"
            android:src="@drawable/jackaroo_qualifying_home_bg"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/badge_iv"
            android:layout_width="140dp"
            android:layout_height="140dp"
            android:layout_marginTop="52dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="110dp"
            android:background="@drawable/jackaroo_qualifying_grade_bg"
            android:minWidth="243dp"
            android:paddingStart="12dp"
            android:paddingTop="4dp"
            android:paddingEnd="12dp"
            android:paddingBottom="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/badge_iv">

            <com.huiwan.widget.MarqueeTextView
                android:id="@+id/grade_name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="marquee"
                android:marqueeRepeatLimit="marquee_forever"
                android:maxWidth="120dp"
                android:maxLines="1"
                android:shadowColor="@color/color_403f0f00"
                android:shadowDx="0"
                android:shadowDy="1"
                android:shadowRadius="0"
                android:singleLine="true"
                android:textColor="@color/color_fee55d"
                android:textSize="14dp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/season_time_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:maxWidth="200dp"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/white_alpha80"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/grade_name_tv" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/treasure_box_iv"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginTop="96dp"
            android:layout_marginEnd="8dp"
            android:src="@drawable/jackaroo_qualifying_treasure_box"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/badge_iv" />

        <com.huiwan.widget.HWEffectTextView
            android:id="@+id/treasure_box_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="2dp"
            android:gravity="center"
            android:lineSpacingExtra="-4dp"
            android:maxWidth="64dp"
            android:shadowColor="@color/color_403f0f00"
            android:shadowDx="0"
            android:shadowDy="1"
            android:shadowRadius="0"
            android:text="@string/voice_room_nation_flag_award"
            android:textColor="@color/color_fee55d"
            android:textFontWeight="700"
            android:textSize="12dp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/treasure_box_iv"
            app:layout_constraintEnd_toEndOf="@id/treasure_box_iv"
            app:layout_constraintStart_toStartOf="@id/treasure_box_iv"
            app:stroke_color="@color/color_6c2f2f"
            app:stroke_width="2" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.huiwan.widget.FadingFrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="-26dp"
        android:paddingTop="2dp"
        app:fading_size="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_group"
        app:position="top">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/grade_refresh_lay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="120dp"
            app:srlEnableFooterTranslationContent="false">

            <androidx.coordinatorlayout.widget.CoordinatorLayout
                android:id="@+id/coordinator_lay"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.google.android.material.appbar.AppBarLayout
                    android:id="@+id/appbar_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/transparent"
                    app:elevation="0dp">

                    <include
                        android:id="@+id/center_group_lay"
                        layout="@layout/jackaroo_qualifying_home_center_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minHeight="0dp"
                        app:layout_scrollFlags="scroll|exitUntilCollapsed" />

                </com.google.android.material.appbar.AppBarLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="?qualify_list_bg"
                    android:paddingStart="1dp"
                    android:paddingTop="1dp"
                    android:paddingEnd="1dp"
                    app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">


                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:background="@drawable/jackaroo_qualifying_rank_list_top_edge_bg"
                        app:layout_constraintDimensionRatio="h,343:96"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/rank_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="16dp"
                        android:gravity="center"
                        android:minWidth="40dp"
                        android:text="@string/room_contribute_rank"
                        android:textColor="@color/white"
                        android:textSize="12dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/name_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="114dp"
                        android:layout_marginTop="16dp"
                        android:gravity="center"
                        android:text="@string/game_room_info_name"
                        android:textColor="@color/white"
                        android:textSize="12dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/earning_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="16dp"
                        android:gravity="center"
                        android:text="@string/jackaroo_rank_earns"
                        android:textColor="@color/white"
                        android:textSize="12dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/qualifying_grade_rv"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="43dp"
                        android:clipToPadding="false"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.coordinatorlayout.widget.CoordinatorLayout>
        </com.scwang.smart.refresh.layout.SmartRefreshLayout>
    </com.huiwan.widget.FadingFrameLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mine_grade_lay"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:background="@drawable/shape_gradient_463390_2e1565_topcorner12"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <include
            android:id="@+id/mine_grade_info"
            layout="@layout/jackaroo_qualifying_rank_holder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent" />

        <com.huiwan.widget.MarqueeTextView
            android:id="@+id/mine_grade_tip_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/shape_white_alpha10_r4"
            android:ellipsize="marquee"
            android:gravity="center"
            android:marqueeRepeatLimit="marquee_forever"
            android:maxLines="1"
            android:padding="8dp"
            android:textColor="@color/main_activity_text_tertiary"
            android:textSize="10dp"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@id/mine_grade_info" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:action_bar_color="@color/transparent"
        app:layout_constraintTop_toTopOf="parent"
        app:white_style="true" />
</androidx.constraintlayout.widget.ConstraintLayout>