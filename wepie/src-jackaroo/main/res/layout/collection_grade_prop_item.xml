<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="60dp"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/prop_iv"
        android:layout_width="58dp"
        android:layout_height="58dp"
        android:padding="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/jackaroo_collection_prop_empty" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/lock_iv"
        android:layout_width="58dp"
        android:layout_height="58dp"
        android:background="@color/black_alpha30"
        android:scaleType="center"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/prop_iv"
        app:srcCompat="@drawable/jackaroo_skin_lock" />

    <com.huiwan.base.ui.WPUIText
        android:id="@+id/prop_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:ellipsize="end"
        android:maxWidth="60dp"
        android:maxLines="1"
        android:textColor="@color/color_text_secondary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/prop_iv"
        app:sizeAndFont="Body2|tajawal" />
</androidx.constraintlayout.widget.ConstraintLayout>