package com.wepie.wespy.base.startup

import android.content.Context
import com.google.firebase.FirebaseApp
import com.wepie.debug.IXCrashListener
import com.wepie.debug.XCrashInitializer
import com.wepie.skynet.apm.Apm
import com.wepie.startup.InitializerManager
import com.wepie.wespy.BuildConfig
import com.wepie.wespy.utils.FirebaseHelper
import com.wepie.wespy.utils.LogUpload
import com.wepie.wpdd.DeviceIdInitializer
import java.io.File

class FirebaseAppInitializer : AbsInitializer {

    constructor() : this(InitializerManager.TYPE_BACKGROUND, InitializerManager.FILTER_ONE)
    constructor(
        @InitializerManager.InitializerType type: Int,
        @InitializerManager.FilterFlag flag: Int
    ) : super(InitializerParam.create(InitializerManager.TYPE_BACKGROUND, flag))

    override fun create(context: Context, attach: Map<String, Any>) {
        //主进程Firebase在FirebaseInitProvider中进行初始化
        Apm.recordPeriod("FirebaseAppInitializer#create", true)
        init(context)
        Apm.recordPeriod("FirebaseAppInitializer#create", false)
    }

    companion object {
        private val listener = object : IXCrashListener {
            override fun onUploadFiles(list: List<File>) {
                LogUpload.upload()
            }
        }

        @JvmStatic
        fun init(context: Context) {
            if (BuildConfig.DEBUG) {
                XCrashInitializer.init(context, true, listener)
            }
            try {
                FirebaseApp.getInstance()
            } catch (e: IllegalStateException) {
                FirebaseApp.initializeApp(context)
            }
        }
    }
}

class FirebaseProviderInitializer : androidx.startup.Initializer<Any> {
    override fun create(context: Context): Any {
        FirebaseAppInitializer.init(context)
        return ""
    }

    override fun dependencies(): MutableList<Class<out androidx.startup.Initializer<*>>> {
        return arrayListOf()
    }
}

abstract class FirebaseInitializer(
    @InitializerManager.InitializerType type: Int = InitializerManager.TYPE_BACKGROUND,
    @InitializerManager.FilterFlag flag: Int = InitializerManager.FILTER_TWO,
) :
    AbsInitializer(
        InitializerParam.create(
            type, flag,
            DeviceIdInitializer::class.java,
            FirebaseAppInitializer::class.java
        )
    )

class FirebaseCrashlyticsInitializer : FirebaseInitializer {

    constructor() : super()
    constructor(type: Int, flag: Int) : super(type, flag)


    override fun create(context: Context, attach: Map<String, Any>) {
        val uid = InitializerUtils.getLoginUid(attach)
        val did = InitializerUtils.getDid(attach)
        val isMainProcess = InitializerUtils.isMainProcess(attach)
        FirebaseHelper.initCrashlytics(uid, did, isMainProcess)
    }
}

class FirebaseAnalyticsInitializer : FirebaseInitializer {
    constructor() : super()
    constructor(type: Int, flag: Int) : super(type, flag)

    override fun create(context: Context, attach: Map<String, Any>) {
        val uid = InitializerUtils.getLoginUid(attach)
        val did = InitializerUtils.getDid(attach)
        FirebaseHelper.initAnalytics(context, uid, did)
    }
}