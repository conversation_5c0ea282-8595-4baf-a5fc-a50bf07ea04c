package com.wepie.wespy.cocosnew.view

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.util.PressUtil
import com.huiwan.widget.inflate
import com.wepie.wespy.R
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil
import com.wepie.wespy.cocosnew.view.dialog.IceBallRuleDialog

/**
 * Created by bigwen on 2017/11/13.
 */
class IceBallRuleAdapter(
    val gameType: Int,
    private val showStartBtn: <PERSON>olean,
    private val callback: Callback
) : RecyclerView.Adapter<IceBallRuleAdapter.ViewHolder>(), View.OnClickListener {
    private val imageUrls: MutableList<IceBallRuleDialog.IRuleData> = ArrayList()

    fun refresh(images: List<IceBallRuleDialog.IRuleData>) {
        if (imageUrls.isNotEmpty()) {
            imageUrls.clear()
        }
        imageUrls.addAll(images)
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int {
        return imageUrls.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(parent.inflate(R.layout.ice_ball_rule_item_view, false))
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = holder.iv
        val data = imageUrls[position]
        data.fill(item)
        val startBtn = holder.startBtn
        if (showStartBtn && position == itemCount - 1) {
            startBtn.isVisible = true
            CocosGameConfigUtil.loadResAsync(
                startBtn, gameType, CocosGameConfigUtil.GUIDE_STARE
            )
            startBtn.setOnClickListener(this)
        } else {
            startBtn.isVisible = false
        }
        item.setOnClickListener(this)
        holder.itemView.setOnClickListener(this)
    }

    override fun onClick(v: View) {
        if (v.id == R.id.guide_item_start_iv) {
            callback.start()
        } else if (v.id == R.id.ice_ball_rule_iv) {
            //do nothing
        } else {
            callback.hide()
        }
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val iv = itemView.findViewById<ImageView>(R.id.ice_ball_rule_iv)
        val startBtn = itemView.findViewById<ImageView>(R.id.guide_item_start_iv)

        init {
            PressUtil.addPressEffect(startBtn)
        }
    }

    interface Callback {
        fun hide()
        fun start()
    }
}