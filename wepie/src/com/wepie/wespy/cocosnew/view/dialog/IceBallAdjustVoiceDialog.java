package com.wepie.wespy.cocosnew.view.dialog;

import android.app.Activity;
import android.app.Service;
import android.content.Context;
import android.media.AudioManager;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.widget.TakeInProgressBar;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;

/**
 * Created by bigwen on 2017/11/15.
 * 只在婚礼房中使用,后期可以去掉,使用{@link com.huiwan.media.VolumeAdjustDialog}
 */
@Deprecated
public class IceBallAdjustVoiceDialog extends LinearLayout {

    private final String TAG = IceBallAdjustVoiceDialog.class.getSimpleName();
    private final Context mContext;
    private TakeInProgressBar gameVocBar, chatVocBar;
    private ImageView gameSildeIv, chatSildeIv;
    private TextView dialodEnter;
    private TextView mediaNameTv;
    private final int chipWidth = ScreenUtil.dip2px(20);
    private final int maxRight = ScreenUtil.dip2px(120) - chipWidth;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private IceBallAdjustVoiceDialog.Callback callback;
    private int maxMusic, maxCall;
    private AudioManager audioManager;
    private boolean isUserTouch;
    private int volumeUpFlag = -1; // 未设置为-1，true为1，false为0
    private int startX;

    public IceBallAdjustVoiceDialog(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public IceBallAdjustVoiceDialog(Context context, boolean isVolumeUp) {
        super(context);
        mContext = context;
        this.volumeUpFlag = isVolumeUp ? 1 : 0;
        init();
    }

    public IceBallAdjustVoiceDialog(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.ice_ball_adjust_voice_dialog, this);
        gameVocBar = findViewById(R.id.game_voc_bar);
        chatVocBar = findViewById(R.id.chat_voc_bar);
        gameSildeIv = findViewById(R.id.game_voc_slide);
        chatSildeIv = findViewById(R.id.chat_voc_slide);
        dialodEnter = findViewById(R.id.dialog_enter);
        mediaNameTv = findViewById(R.id.game_voc_text);
        gameVocBar.setPercent(0);
        chatVocBar.setPercent(0);
        gameVocBar.setWidth(ScreenUtil.dip2px(120));
        chatVocBar.setWidth(ScreenUtil.dip2px(120));
        gameVocBar.setProgressColor(ResUtil.getColor(com.huiwan.media.R.color.color_accent));
        chatVocBar.setProgressColor(ResUtil.getColor(com.huiwan.media.R.color.color_accent));

        audioManager = (AudioManager) LibBaseUtil.getApplication().getSystemService(Service.AUDIO_SERVICE);
        // 媒体音量
        maxMusic = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        // 通话音量
        maxCall = audioManager
                .getStreamMaxVolume(AudioManager.STREAM_VOICE_CALL);
        initDefaultVoice();
        initTouch(gameVocBar, gameSildeIv);
        initTouch(chatVocBar, chatSildeIv);

        dialodEnter.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onCloseListener != null) onCloseListener.close();
            }
        });
    }

    private void initDefaultVoice() {
        final int maxMusic = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        int currentMusic = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        if (isSetVolumeUp()) {
            currentMusic += maxMusic * 0.1;
            if (currentMusic > maxMusic) currentMusic = maxMusic;
            audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, currentMusic, AudioManager.FLAG_REMOVE_SOUND_AND_VIBRATE);
        } else if (isSetVolumeDown()) {
            currentMusic -= maxMusic * 0.1;
            if (currentMusic < 0) currentMusic = 0;
            audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, currentMusic, AudioManager.FLAG_REMOVE_SOUND_AND_VIBRATE);
        }
        // 通话音量
        final int maxCall = audioManager
                .getStreamMaxVolume(AudioManager.STREAM_VOICE_CALL);
        int currentCall = audioManager
                .getStreamVolume(AudioManager.STREAM_VOICE_CALL);

        setDefalutWidth(currentMusic * 1f / maxMusic, currentCall * 1f / maxCall);
    }

    private boolean isSetVolumeDown() {
        return volumeUpFlag == 0;
    }

    private boolean isSetVolumeUp() {
        return volumeUpFlag == 1;
    }

    public void setDefalutWidth(float percetn1, float percent2) {
        if (percetn1 >= 0) {
            gameVocBar.setPercent(percetn1);
            updateSlideView(gameSildeIv, percetn1);
        }
        if (percent2 >= 0) {
            chatVocBar.setPercent(percent2);
            updateSlideView(chatSildeIv, percent2);
        }
    }


    private void initTouch(final View bar, final View slide) {
        slide.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                isUserTouch = true;

                int x = (int) event.getRawX();
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        startX = x;
                        break;
                    case MotionEvent.ACTION_MOVE:
                        int dx = x - startX;
                        updateSlideView(dx, slide);
                        startX = x;
                        updateGameVoice(bar, slide.getLeft() * 1f / maxRight);
                        break;
                    case MotionEvent.ACTION_UP:
                        touchUp();
                        break;
                    default:
                        break;
                }
                return true;
            }
        });
    }

    private void updateSlideView(int dx, View slide) {
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
                chipWidth, chipWidth);
        int leftMargin;
        if (ScreenUtil.isRtl()) {
            leftMargin = params.getMarginStart() - dx;
        } else {
            leftMargin = params.getMarginStart() + dx;
        }
        if (leftMargin < 0) leftMargin = 0;
        if (leftMargin > maxRight) leftMargin = maxRight;
        params.setMarginStart(leftMargin);
        slide.setLayoutParams(params);
    }

    public void updateVolumeWithKey(final boolean isUp) {
        post(new Runnable() {
            @Override
            public void run() {
                RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
                        chipWidth, chipWidth);
                int leftMargin = 0;
                if (isUp) {
                    leftMargin = gameSildeIv.getLeft() + (int) (maxRight * 0.1);
                } else {
                    leftMargin = gameSildeIv.getLeft() - (int) (maxRight * 0.1);
                }
                if (leftMargin < 0) leftMargin = 0;
                if (leftMargin > maxRight) leftMargin = maxRight;
                params.setMarginStart(leftMargin);
                gameSildeIv.setLayoutParams(params);

                float percent = gameSildeIv.getLeft() * 1f / maxRight;
                gameVocBar.setPercent(percent);
                if (callback != null) {
                    callback.onTouchFirst(percent);
                }
            }
        });
    }

    private void touchUp() {
        int currentMusic = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        int currentCall = audioManager
                .getStreamVolume(AudioManager.STREAM_VOICE_CALL);
        if (currentCall < 1) {
            currentCall = 1;
            audioManager.setStreamVolume(AudioManager.STREAM_VOICE_CALL, 1, AudioManager.FLAG_REMOVE_SOUND_AND_VIBRATE);
        }
        setDefalutWidth(currentMusic * 1f / maxMusic, currentCall * 1f / maxCall);
    }

    private void updateSlideView(View view, float percent) {
        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(
                chipWidth, chipWidth);
        int leftMargin = (int) (maxRight * percent);
        if (leftMargin < 0) leftMargin = 0;
        if (leftMargin > maxRight) leftMargin = maxRight;
        params.setMarginStart(leftMargin);
        view.setLayoutParams(params);
    }

    private void updateGameVoice(View view, float percent) {
        if (view == gameVocBar) {
            gameVocBar.setPercent(percent);
            if (callback != null) {
                callback.onTouchFirst(percent);
            }
        } else {
            chatVocBar.setPercent(percent);
            if (callback != null) {
                callback.onTouchSecend(percent);
            }
        }
    }

    public void setCallback(IceBallAdjustVoiceDialog.Callback callback) {
        this.callback = callback;
    }

    private IceBallAdjustVoiceDialog.OnCloseListener onCloseListener;

    public void setOnCloseListener(IceBallAdjustVoiceDialog.OnCloseListener onCloseListener) {
        this.onCloseListener = onCloseListener;
    }

    /**
     * 用户没有手动调节的情况下，2.5秒后自动隐藏
     */
    public void hideIfNoUserTouch(final BaseFullScreenDialog dialog) {
        mainHandler.removeCallbacksAndMessages(null);
        mainHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (!isUserTouch) {
                    if (mContext instanceof Activity) {
                        // 判断Activity没有destroy再dismiss dialog
                        boolean isActivityDestroyed;
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                            isActivityDestroyed = ((Activity) mContext).isDestroyed();
                        } else {
                            isActivityDestroyed = ((Activity) mContext).isFinishing();
                        }
                        if (!isActivityDestroyed && dialog != null) {
                            dialog.dismiss();
                        }
                    }
                }
            }
        }, 2500);
    }

    public void initWeddingRoomUI() {
        mediaNameTv.setText(ResUtil.getStr(R.string.cocos_music_effect));
        dialodEnter.setBackgroundResource(R.drawable.gradient_ff92a9_ff4168_corner100);
    }

    public interface OnCloseListener {
        void close();
    }

    public interface Callback {
        void onTouchFirst(float percent);

        void onTouchSecend(float percent);
    }
}