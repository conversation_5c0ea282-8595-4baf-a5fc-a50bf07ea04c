package com.wepie.wespy.cocosnew.view.dialog;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.huiwan.base.util.ContextUtil;
import com.huiwan.platform.ThreadUtil;
import com.wejoy.littlegame.LittleGameSimpleInfo;
import com.wejoy.weplay.ex.ILife;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

/**
 * Created by bigwen on 2017/11/14.
 */

public class IceBallDialogUtil {

    public static void showRankRuleDialog(Context context, String text) {
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_corner8);
        View view = LayoutInflater.from(context).inflate(R.layout.ice_ball_rank_help_dialog_view, null);
        TextView ruleTv = view.findViewById(R.id.rule_tv);
        ruleTv.setText(text);
        View okBtn = view.findViewById(R.id.ice_ball_rank_rule_ok_btn);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(true);
        dialog.init();
        okBtn.setOnClickListener(v -> dialog.dismiss());
        dialog.show();
    }

    public static void showGuideDialog(Context context, LittleGameSimpleInfo simpleInfo, final Runnable callback) {
        showGuideDialog(context, simpleInfo, callback != null, callback);
    }

    public static void showGuideDialog(Context context, LittleGameSimpleInfo simpleInfo, boolean showStartBtn, final Runnable callback) {
        ILife life = ContextUtil.getLife(context);
        Future<Object> future = ThreadUtil.runInOtherThread(() -> {
            Bitmap[] bitmaps = CocosGameConfigUtil.getGuideList(simpleInfo.getGameType());
            ArrayList<BitmapRule> list = new ArrayList<>(bitmaps.length);
            for (Bitmap bitmap : bitmaps) {
                list.add(new BitmapRule(bitmap));
            }
            ThreadUtil.runOnUiThread(() -> {
                showGuidDialog(context, simpleInfo, list, showStartBtn, callback);
            });
            return null;
        });
        if (life != null) {
            life.onDestroy(() -> {
                future.cancel(true);
                return null;
            });
        }
    }

    public static void showGuideUrlDialog(Context context, LittleGameSimpleInfo simpleInfo, List<String> urlList, final Runnable callback) {
        showGuideUrlDialog(context, simpleInfo, urlList, callback != null, callback);
    }

    public static void showGuideUrlDialog(Context context, LittleGameSimpleInfo simpleInfo, List<String> urlList, boolean showStartBtn, final Runnable callback) {
        ArrayList<UrlRule> list = new ArrayList<>(urlList.size());
        for (String url : urlList) {
            list.add(new UrlRule(url));
        }
        showGuidDialog(context, simpleInfo, list, showStartBtn, callback);
    }

    private static void showGuidDialog(Context context, LittleGameSimpleInfo simpleInfo, List<? extends IceBallRuleDialog.IRuleData> list, boolean showStartBtn, final Runnable callback) {
        IceBallRuleDialog gameDialog = new IceBallRuleDialog();
        gameDialog.setShowStartBtn(showStartBtn);
        gameDialog.setSimpleInfo(simpleInfo);
        gameDialog.setData(list);
        gameDialog.setCallback(() -> {
            gameDialog.dismiss();
            if (callback != null) {
                callback.run();
            }
            return null;
        });
        gameDialog.initMatchParent();
        gameDialog.show(context);
    }

    static class BitmapRule implements IceBallRuleDialog.IRuleData {

        private final Bitmap bitmap;

        public BitmapRule(Bitmap bitmap) {
            this.bitmap = bitmap;
        }

        @Override
        public void fill(ImageView view) {
            view.setImageBitmap(bitmap);
        }
    }

    static class UrlRule implements IceBallRuleDialog.IRuleData {

        private final String url;

        public UrlRule(String url) {
            this.url = url;
        }

        @Override
        public void fill(@NotNull ImageView view) {
            WpImageLoader.load(url, view);
        }
    }
}
