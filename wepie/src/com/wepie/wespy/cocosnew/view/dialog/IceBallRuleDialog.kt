package com.wepie.wespy.cocosnew.view.dialog

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.viewpager2.widget.ViewPager2
import com.huiwan.base.util.ScreenUtil
import com.huiwan.widget.banner.ViewPagerLineIndicator
import com.wejoy.littlegame.LittleGameSimpleInfo
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.wespy.R
import com.wepie.wespy.cocosnew.view.IceBallRuleAdapter
import com.wepie.wespy.helper.dialog.BaseDialogFragment

/**
 * Created by bigwen on 2017/11/13.
 */
class IceBallRuleDialog : BaseDialogFragment() {
    var simpleInfo: LittleGameSimpleInfo = LittleGameSimpleInfo()
    var showStartBtn = false
    var callback: () -> Unit = {}

    var maxVisitCount = 0

    private val adapter: IceBallRuleAdapter by lazy {
        IceBallRuleAdapter(simpleInfo.gameType, showStartBtn, object : IceBallRuleAdapter.Callback {
            override fun hide() {
                dismissAllowingStateLoss()
            }

            override fun start() {
                callback.invoke()
            }
        })
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.ice_ball_rule_view, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val viewPager = view.findViewById<ViewPager2>(R.id.ice_ball_viewpager)
        viewPager.offscreenPageLimit = 4
        viewPager.adapter = adapter

        val indicatorView = view.findViewById<ViewPagerLineIndicator>(R.id.ice_ball_indicator)
        indicatorView.attachViewPager(viewPager)

        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {

            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                if (position > maxVisitCount) {
                    maxVisitCount = position
                }
            }
        })
    }

    fun setData(list: List<IRuleData>) {
        adapter.refresh(list)
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.setDimAmount(0.7F)
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        val map = mutableMapOf<String, Any>()
        map["pic_num"] = if (adapter.itemCount > 0) maxVisitCount + 1 else 0
        map["is_completed"] = maxVisitCount == adapter.itemCount - 1
        map.putAll(simpleInfo.toMap())
        TrackUtil.appViewScreen(TrackScreenName.NEW_USER_TUTORIAL, map)
    }

    interface IRuleData {
        fun fill(view: ImageView)
    }
}