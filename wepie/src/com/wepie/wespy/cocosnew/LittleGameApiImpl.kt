package com.wepie.wespy.cocosnew

import android.app.Dialog
import android.app.Service
import android.content.Context
import android.view.animation.AnimationUtils
import android.widget.ImageView
import androidx.collection.ArrayMap
import androidx.lifecycle.MutableLiveData
import com.huiwan.base.ActivityTaskManager
import com.huiwan.base.LibBaseUtil
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ContextUtil
import com.huiwan.base.util.JsonUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.component.game.chip.ChipApi
import com.huiwan.component.gift.GiftAnimUtil
import com.huiwan.component.gift.IGiftApi
import com.huiwan.component.gift.send.GiftSendInfo
import com.huiwan.component.gift.send.GiftShowConfig
import com.huiwan.component.gift.send.liveuser.LiveUser
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.editionentity.GameConfig
import com.huiwan.configservice.editionentity.GameConfig.MatchGroupInfo
import com.huiwan.configservice.international.service.GlobalConfigHelper
import com.huiwan.configservice.model.gift.Gift
import com.huiwan.constants.BaseConstants
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.impl
import com.huiwan.lib.api.plugins.HwApi
import com.huiwan.lib.api.plugins.friend.AddFriendCallback
import com.huiwan.lib.api.plugins.friend.AddFriendInfo
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.callback.SeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.littlegame.aa.GiftShowConfigHelperLittleGame
import com.huiwan.littlegame.cocos.CocosLaunchInfo
import com.huiwan.littlegame.cocos.CocosWebActivity
import com.huiwan.littlegame.cocos.CocosWebActivity.Companion.getIntent
import com.huiwan.littlegame.cocos.CocosWebView
import com.huiwan.littlegame.cocos.CocosWebViewPreloadAssetHelper
import com.huiwan.littlegame.cocos.getUnpackDir
import com.huiwan.littlegame.net.CocosMainTcpPacketSender
import com.huiwan.littlegame.util.CommonUtil
import com.huiwan.littlegame.view.DialogShowUtil
import com.huiwan.littlegame.view.IceLoadingView
import com.huiwan.littlegame.view.WejoyNativeUserDialog
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.huiwan.voiceservice.VoiceManager
import com.three.http.callback.LifeDataCallback
import com.wejoy.gamematch.net.GameMatchSender
import com.wejoy.littlegame.CocosGameVersionUnit
import com.wejoy.littlegame.ILittleGameApi
import com.wejoy.littlegame.ILittleGameApi.LittleGameSendGiftConfig
import com.wejoy.littlegame.LittleGame
import com.wejoy.littlegame.LittleGameEnterRoomSimpleInfo
import com.wejoy.littlegame.LittleGameInfo
import com.wejoy.littlegame.LittleGameSimpleInfo
import com.wejoy.littlegame.LittleGameTrackUtil
import com.wejoy.weplay.ex.context.toAppCompatActivity
import com.wejoy.weplay.ex.coroutine.toLife
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.base.GameStatusCheckManager
import com.wepie.wespy.cocosnew.match.invite.CocosInvite
import com.wepie.wespy.cocosnew.match.main.ar285.loader.CocosGameSilentPreloader
import com.wepie.wespy.cocosnew.match.main.ar285.loader.LittleGameResUtil
import com.wepie.wespy.cocosnew.suspend.SuspendUtil
import com.wepie.wespy.cocosnew.update.CocosResDebugVersionHelper
import com.wepie.wespy.cocosnew.update.CocosUnpackUtil
import com.wepie.wespy.cocosnew.update.CocosVersionManager
import com.wepie.wespy.cocosnew.view.dialog.IceBallDialogUtil
import com.wepie.wespy.helper.NotificationHelper
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog
import com.wepie.wespy.helper.dialog.DialogBuild
import com.wepie.wespy.helper.prefrence.MatchResPrefUtil
import com.wepie.wespy.helper.shence.ShenceEvent
import com.wepie.wespy.helper.shence.util.GameStatisticsUtil
import com.wepie.wespy.helper.shence.util.ShenceGiftUtil
import com.wepie.wespy.model.entity.match.GameVersionUnit
import com.wepie.wespy.module.abtest.AbTestManager
import com.wepie.wespy.module.accessibilityservice.AccessibilityServiceUtils
import com.wepie.wespy.module.chat.invitegame.Cocos243Helper
import com.wepie.wespy.module.common.jump.JumpCommon
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher
import com.wepie.wespy.net.http.api.GameApi
import com.wepie.wespy.net.tcp.packet.GameMatchPackets.GetGameVerRsp
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender
import com.wespy.component.suspend.SuspendManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.InputStream

class LittleGameApiImpl : ILittleGameApi {

    init {
        val manager = CocosVersionManager.getInstance()
        CoroutineScope(Dispatchers.IO).launch {
            manager.preloadVersion(ConfigHelper.getInstance().gameConfig.gameTypes)
        }
    }

    private var cocosWebViewCacheEnabled = false
    private var preloadCocosAssetEnabled = false

    /**
     * cocos小游戏内展示规则弹窗
     */
    override fun showGameRuleDialog(
        context: Context, info: LittleGameSimpleInfo, startTask: Runnable?
    ) {
        val guideRuleUrls = getGuideRuleUrl(info)
        if (guideRuleUrls.isNullOrEmpty()) {
            IceBallDialogUtil.showGuideDialog(context, info, startTask)
        } else {
            IceBallDialogUtil.showGuideUrlDialog(
                context, info, guideRuleUrls, startTask
            )
        }
    }

    /**
     * unity小游戏内展示规则弹窗
     */
    override fun showGameRuleDialog(context: Context, info: LittleGameSimpleInfo) {
        val gameType = info.gameType
        if (gameType <= 0) {
            return
        }
        val config = ConfigHelper.getInstance().getGameConfig(gameType)
        if (info.gameMode > 0 && config.generateHome != null) {
            val urlList = config.generateHome.getGameModeGuide(info.gameMode)
            if (urlList.isEmpty()) {
                IceBallDialogUtil.showGuideDialog(context, info, false, null)
            } else {
                IceBallDialogUtil.showGuideUrlDialog(context, info, urlList, false, null)
            }
        } else {
            IceBallDialogUtil.showGuideDialog(context, info, null)
        }
    }

    private fun getGuideRuleUrl(info: LittleGameSimpleInfo): List<String>? {
        val gameType = info.gameType
        if (gameType <= 0) {
            return null
        }
        val config = ConfigHelper.getInstance().getGameConfig(gameType)
        if (config.generateHome != null) {
            val urlList = config.generateHome.getGameModeGuide(info.gameMode)
            if (urlList.isNotEmpty()) {
                return urlList
            }
        }

        val matchGroupInfoList = config.matchGroupList
        if (!matchGroupInfoList.isNullOrEmpty() && info.mode > 0) {
            var matchGroupInfoCur: MatchGroupInfo? = null
            for (matchGroupInfo in matchGroupInfoList) {
                if (matchGroupInfo.matchInfoList.isNullOrEmpty()) {
                    continue
                }
                for (matchInfo in matchGroupInfo.matchInfoList) {
                    if (matchInfo.betLevel == info.betLevel &&
                        (matchInfo.roomMode == info.mode || matchInfo.matchMode == info.mode)
                    ) {
                        matchGroupInfoCur = matchGroupInfo
                        break
                    }
                }
                if (matchGroupInfoCur != null) {
                    break
                }
            }
            if (matchGroupInfoCur != null && matchGroupInfoCur.guideUrlList.isNotEmpty()) {
                return matchGroupInfoCur.guideUrlList
            }
        }
        return null
    }

    override fun giftShowConfig(rid: Int, toUid: Int, giftId: Int): GiftShowConfig {
        val config = GiftShowConfig()
        config.scene = Gift.GIFT_SCENE_TMP_ROOM
        config.canCombo = false
        config.rid = rid
        config.receiveUid = toUid
        config.showUser = true
        config.forbidAppGift = true
        config.giftId = giftId
        return config
    }

    override fun giftShowConfig(
        rid: Int, uidList: List<Int>, isMultiSend: Boolean, giftId: Int
    ): GiftShowConfig {
        val config = GiftShowConfig()
        val gamerList = uidList.mapIndexed { i, uid -> LiveUser(i, uid, "${i + 1}") }
        config.userLiveData = MutableLiveData(gamerList)
        config.scene = Gift.GIFT_SCENE_TMP_ROOM or Gift.GIFT_SCENE_VOICE_ALL
        config.canCombo = false
        config.rid = rid
        config.showUser = true
        config.forbidAppGift = true
        config.supportMultiPeople = false
        config.giftId = giftId
        config.allMicStrRes = R.string.cocos_mul_gift_mic
        return config
    }

    override fun getGameVersion(gameType: Int): Int {
        return CocosVersionManager.getInstance().getGameVersion(gameType)
    }

    override fun gotoIceBallMatchActivity(context: Context, gameType: Int, fromMainPage: Boolean) {
        JumpUtil.gotoIceBallMatchActivity(context, gameType, fromMainPage)
    }

    override fun gotoIceBallCreateRoomActivity(
        context: Context,
        gameType: Int,
        jumpCreateInfo: ILittleGameApi.JumpCreateInfo
    ) {
        if (jumpCreateInfo.canSupportCocosMatch && isSupportCocosMatch(gameType)) {
            SuspendManager.closeSuspend()
            Cocos243Helper.followMatchInCocos(context, gameType, CocosLaunchInfo.ENTER_MODE_FOLLOW_TEAM)
        } else {
            JumpUtil.gotoIceBallCreateRoomActivity(
                context,
                gameType,
                jumpCreateInfo.fromGameMain,
                jumpCreateInfo.needShowInviteDialog,
                jumpCreateInfo.source
            )
        }
    }

    override fun gotoIceBallMatchPrepareActivity(
        context: Context,
        gameType: Int,
        fromGameMain: Boolean,
        source: String,
    ) {
        val canSupportCocosMatch = !GameConfig.isJackarooVip(gameType, LittleGame.gameInfo.gameMode)
        if (isSupportCocosMatch(gameType) && canSupportCocosMatch) {
            SuspendManager.closeSuspend()
            Cocos243Helper.followMatchInCocos(context, gameType, CocosLaunchInfo.ENTER_MODE_FOLLOW_TEAM)
        } else {
            JumpUtil.gotoIceBallMatchPrepareActivity(
                context,
                gameType,
                fromGameMain,
                source
            )
        }
    }

    override fun gotoCocosMainActivity(context: Context, gameType: Int) {
        JumpUtil.gotoCocosMainActivity(context, gameType)
    }

    override fun onSuspendReqCancelMatch(
        tid: Int,
        gameType: Int,
        duration: Long,
        needCancelMatch: Boolean
    ) {
        if (needCancelMatch) {
            GameMatchSender.cancelMatchReq(tid, null)
            ShenceEvent.cancelMatch(gameType, duration)
        }
        GameMatchSender.leaveTeamReq(LittleGame.teamInfo.tid, null)
        SuspendManager.closeSuspend()
        EventDispatcher.postChatGameInviteCancelEvent()
        EventDispatcher.postLittleGameSuspend(false)
    }

    override fun getLittleGameResPath(gameType: Int): String {
        return getUnpackDir(gameType, false)
    }

    override fun updateLittleGameLoadingAnim(context: Context, gameType: Int, show: Boolean) {
        IceLoadingView.updateLittleGameLoadingAnim(context, gameType, show)
    }

    override fun showTimeOutPage(context: Context, backRunnable: Runnable?) {
        IceLoadingView.showTimeOutPage(context, backRunnable)
    }

    override fun handleEnterError(
        context: Context, errorCode: Int, scene: Int, info: LittleGameSimpleInfo, next: () -> Unit
    ): Boolean {
        when (errorCode) {
            RspHeadInfo.ERROR_CODE_COIN_NOT_ENOUGH -> {
                val map = mutableMapOf<String, Any>()
                map["scene"] = LittleGameTrackUtil.getSceneString(scene and 0xFF)
                map.putAll(info.toMap())
                ApiService.of(HwApi::class.java).showCoinNotEnoughDialog(true, map)
            }

            RspHeadInfo.ERROR_CODE_CHIP_REACH_MAX_LIMIT -> {
                ApiService.of(ChipApi::class.java).handleChipLimit(context, info)
            }

            RspHeadInfo.ERROR_CODE_CHIP_NOT_ENOUGH -> {
                ApiService.of(ChipApi::class.java).handleChipNotEnough(
                    context, scene and 0xFF00, scene and 0x00FF, info, next
                )
            }

            RspHeadInfo.ERROR_CODE_JACKAROO_VIP_ONLY -> {
                JumpUtil.gotoVipMainActivity(context, info.joinFailedVipReferScreen)
            }

            else -> {
                return false
            }
        }
        return true
    }

    override fun showGameStateNotification(title: String, desc: String, service: Service) {
        NotificationHelper.showForegroundNotification(
            service, NotificationHelper.NOTIFICATION_ID_GAME, title, desc
        )
    }

    override fun isCocosUseWeb(): Boolean = true

    override fun showUserInfoDialogInWeb(
        context: Context,
        uid: Int,
        rid: Int,
        gameType: Int,
        subSource: String,
        giftShowConfigFetcher: (callback: (LittleGameSendGiftConfig) -> Unit) -> Unit
    ): Dialog {
        val dialog = BaseFullScreenDialog(context, R.style.dialog_style_custom)
        val nativeUserDialog = WejoyNativeUserDialog(context)
        nativeUserDialog.update(uid, gameType, rid)
        nativeUserDialog.setJumpUserClickListener {
            val map = mutableMapOf<String, Any>()
            LittleGame.gameInfo.addTrackInfo(map)
            map[BaseConstants.SUB_SOURCE] = subSource
            ApiService.of(HwApi::class.java)
                .gotoUserInfoDetailActivityFromGame(
                    it.context,
                    uid,
                    gameType,
                    JsonUtil.toJsonString(map)
                )
        }
        nativeUserDialog.setCallback(object : WejoyNativeUserDialog.Callback {
            override fun onSendGift(uid: Int) {
                dialog.dismiss()
                handleSendGift(context, rid, uid, gameType, subSource, giftShowConfigFetcher)
            }

            override fun onAddFriend(uid: Int, addPrice: Int) {
                val addFriendInfo = AddFriendInfo(uid, gameType)
                addFriendInfo.subSource = subSource
                addGameTrackInfo(addFriendInfo.trackExtData, gameType)
                addFriendInfo.rid = LittleGame.teamInfo.rid
                CommonUtil.addFriendApi(context, addFriendInfo, object : AddFriendCallback {
                    override fun onSuccess(msg: String) {
                        nativeUserDialog.refreshAddFriendSuccess()
                        DialogBuild.newBuilder(context).setSingleBtn(true).setTitle(R.string.tip)
                            .setContent(R.string.common_add_friend_success).setSureTx(R.string.sure)
                            .show()
                    }

                    override fun onFail(code: Int, msg: String) {
                        ToastUtil.show(msg)
                    }
                })
            }

            override fun onDismiss() {
                dialog.dismiss()
            }
        })
//        dialog.initFullScreenDialogWithoutStatusBar()
        dialog.setContentView(nativeUserDialog)
        dialog.setCanceledOnTouchOutside(true)
        dialog.setOnDismissListener { }
        dialog.show()
        return dialog
    }

    private fun addGameTrackInfo(map: MutableMap<String, Any>, gameType: Int) {
        if (LittleGame.gameInfo.gameType == gameType && LittleGame.gameInfo.cid > 0) {
            LittleGame.gameInfo.addTrackInfo(map)
        } else if (LittleGame.teamInfo.gameType == gameType && LittleGame.teamInfo.tid > 0) {
            LittleGame.teamInfo.addTrackInfo(map)
        } else {
            LittleGame.gameInfo.addTrackInfo(map)
        }
    }

    private fun handleSendGift(
        context: Context,
        rid: Int,
        uid: Int,
        gameType: Int,
        subSource: String,
        giftShowConfigFetcher: (callback: (LittleGameSendGiftConfig) -> Unit) -> Unit,
    ) {
        giftShowConfigFetcher.invoke { config ->
            val source = TrackUtil.getGameTypeSource(gameType)
            val showConfig = if (config.commonSend) {
                config.giftShowConfig
            } else {
                GiftShowConfigHelperLittleGame.littleGame(rid, uid)
            }
            val giftDialog: Dialog? = GiftAnimUtil.showGiftView(
                context, showConfig
            ) { info: GiftSendInfo? ->
                if (info == null) {
                    HLog.e("LittleGameApiImpl", NullPointerException("info is null"), "")
                    return@showGiftView
                }
                info.gameType = gameType
                info.source = source
                info.subSource = subSource
                val user = UserService.get().getCacheSimpleLocalUser(info.recUid)
                var nickname: String? = ""
                if (user != null) nickname = user.remarkName
                val trackExt: MutableMap<String, Any> = ArrayMap()
                addGameTrackInfo(trackExt, gameType)
                if (config.commonSend) {
                    info.notifyUidList.addAll(config.notifyUidList)
                    trackExt.putAll(config.trackInfo)
                    GameApi.sendGiftCommon(
                        info,
                        object : LifeDataCallback<Any>(context.toAppCompatActivity()) {
                            override fun onSuccess(result: com.three.http.callback.Result<Any>?) {
                                HLog.d("LittleGameImpl", HLog.USR, "send gift success")
                                ShenceGiftUtil.reportSendGift(info, info.source, "", trackExt)
                            }

                            override fun onFail(code: Int, msg: String?) {
                                ToastUtil.show(msg)
                            }
                        })
                } else {
                    CocosMainTcpPacketSender.sendGift(info, nickname, object : SeqCallback {
                        override fun onSuccess(head: RspHeadInfo) {
                            ShenceGiftUtil.reportSendGift(info, info.source, "", trackExt)
                        }

                        override fun onFail(head: RspHeadInfo) {
                            IGiftApi::class.impl().onSendGiftFailed(info, head.code, head.desc)
                        }
                    })
                }
            }
            val activity = ContextUtil.getFragmentActivityFromContext(context)
            activity?.lifecycle?.addObserver(
                DialogShowUtil.CocosGiftDialogLifecycleObserver(giftDialog)
            )
            giftDialog?.apply { ILittleGameApi.recordShowingDialog(context, giftDialog) }
        }
    }

    override fun preload(context: Context) {
        if (isCocosUseWeb()) {
            CocosWebView.preload(context)
        }
    }

    override fun notifyUpdateCocosGameStatus(inGame: Boolean) {
        val roomInfo = LittleGame.gameInfo.roomInfo
        val mode = LittleGame.gameInfo.mode
        val level = LittleGame.gameInfo.betLevel
        val selfPlayer = LittleGame.gameInfo.isPlayer(LoginHelper.getLoginUid())

        if (roomInfo == null) {
            GameStatisticsUtil.onCocosGameDestroy()
            GameStatusCheckManager.setIsInGame(false)
        } else {
            if (inGame) {
                GameStatisticsUtil.onCocosGameCreate(LittleGame.gameInfo.gameType, roomInfo.rid)
                GameStatusCheckManager.setIsInGame(true, true)
                if (selfPlayer) {
                    AccessibilityServiceUtils.sendGameCheckout(
                        LibBaseUtil.getApplication(),
                        LittleGame.gameInfo.gameType, mode, level
                    )
                }
            } else {
                GameStatisticsUtil.onCocosGameDestroy()
                GameStatusCheckManager.setIsInGame(false, true)
                SuspendUtil.cocosGameBack()
            }
        }
    }

    override fun reportCocosSendGift(info: GiftSendInfo, source: String) {
        com.wepie.wespy.cocosnew.util.CommonUtil.reportCocosSendGift(info, source)
    }

    override fun getAgoraStatus(): BooleanArray {
        return if (LittleGame.gameInfo.isVoiceGame) {
            booleanArrayOf(
                !VoiceRoomService.getInstance().isMuteRoom,
                VoiceManager.getInstance().isLocalMicOn
            )
        } else {
            booleanArrayOf(CommonUtil.isAgoraOn(), CommonUtil.isMicConfigOn())
        }
    }

    override fun unpackCocosInGameRes(
        srcInputStream: InputStream,
        targetDir: String
    ): Result<Unit> {
        return kotlin.runCatching {
            val unpackResult = CocosUnpackUtil.unpack(srcInputStream, false, targetDir)
            if (!unpackResult.isSuccess) {
                throw IllegalStateException("unpack game fail,$unpackResult")
            }
        }
    }

    override fun unpackCocosMatchRes(
        srcInputStream: InputStream,
        gameType: Int,
    ): Result<Unit> {
        return kotlin.runCatching {
            val unpackResult = CocosVersionManager.getInstance()
                .unpackMatch(srcInputStream, gameType)
            if (!unpackResult.isSuccess) {
                throw IllegalStateException("unpack match fail,$unpackResult")
            }
        }
    }

    override fun updateCocosGameVersionInfo(gameType: Int, isWebGame: Boolean, targetVersion: Int) {
        CocosVersionManager.getInstance().updateGameVersion(gameType, targetVersion, isWebGame)
    }

    override fun updateCocosMatchVersionInfo(
        gameType: Int,
        targetVersion: String
    ) {
        MatchResPrefUtil.getInstance()
            .setString(
                MatchResPrefUtil.MATCH_TYPE_PREFFIX + gameType,
                targetVersion
            )
    }

    override fun getCocosMatchResVersion(gameType: Int): String {
        return MatchResPrefUtil.getInstance()
            .getString(MatchResPrefUtil.MATCH_TYPE_PREFFIX + gameType, "")
    }

    override fun isCocosWebGame(): Boolean {
        return CocosVersionManager.getInstance().isOldCocoUseWeb
    }

    override fun isInCocosWeb(): Boolean {
        return ActivityTaskManager.getInstance().isStack(CocosWebActivity::class.java)
    }

    override fun getMatchResDir(gameType: Int): String {
        return CocosVersionManager.getInstance().getMatchResDir(gameType)
    }

    @Throws(IllegalStateException::class)
    override suspend fun fetchGameUnit(gameType: Int): CocosGameVersionUnit {
        return suspendCancellableCoroutine {
            val localSpecifyGameVersion = CocosResDebugVersionHelper.loadVersion(gameType)
            val callback = object : LifeSeqCallback(it.toLife()) {
                override fun onSuccess(head: RspHeadInfo) {
                    val gameVerRsp = head.message as GetGameVerRsp
                    val gameUnit = GameVersionUnit.getGameVersionUnit(gameType, gameVerRsp)
                    HLog.d("LittleGameApiImpl", HLog.USR, "fetchGameVer:$gameUnit")
                    if (gameUnit != null) {
                        val cocosVerUnit = CocosGameVersionUnit(
                            gameUnit.webUpdateUrl,
                            gameUnit.updateUrl,
                            gameUnit.webManifestUrl,
                            gameUnit.gameVersion
                        )
                        it.resumeWith(Result.success(cocosVerUnit))
                    } else {
                        it.resumeWith(Result.failure(IllegalStateException("can not find gameUnit match gameType:$gameType")))
                    }
                }

                override fun onFail(head: RspHeadInfo) {
                    it.resumeWith(Result.failure(IllegalStateException("send tcp failed,code:${head.code},msg:${head.desc}")))
                }
            }
            GameMatchSender.getGameVerReq(gameType, localSpecifyGameVersion, callback)
        }
    }

    override fun updateBuildInAppCocosDebugInfo(gameType: Int, zipUrl: String) {
        CocosVersionManager.getInstance().inGameResZipUrl[gameType] = zipUrl
    }

    override fun showTeamInviteDialog(
        context: Context,
        gameType: Int,
        tid: Int,
        subScene: String
    ): Dialog {
        return CocosInvite.invite(context, tid, gameType, subScene)
    }

    override fun checkLoadResWithDialog(
        context: Context,
        gameType: Int,
        trackScene: String,
        next: (isCocos: Boolean) -> Unit
    ) {
        LittleGameResUtil.checkLoadRes(context, gameType, trackScene, next)
    }

    override val isForceIncUpdate: Boolean
        get() = AbTestManager.getInstance().isForceUpdateByInc

    override val incUpdateItemRatioLimit: Int
        get() = AbTestManager.getInstance().incUpdateItemRatioLimit

    override val incUpdateSizeRatioLimit: Int
        get() = AbTestManager.getInstance().incUpdateSizeRatioLimit

    override fun relaunchCocos(context: Context, deeplink: String) {
        JumpCommon.gotoOtherPager(context, deeplink, "RelaunchCocos", null)
    }

    override fun leaveCurTeamIfIn() {
        val tid = LittleGame.teamInfo.tid
        if (tid > 0) {
            GameMatchSender.leaveTeamReq(tid, null)
        }
    }

    override fun setLoadingDrawable(view: ImageView) {
        val res = R.drawable.wejoy_refresh_loading_default_icon
        val anim = R.anim.anim_wejoy_pull_loading
        val animation = AnimationUtils.loadAnimation(view.context, anim)
        view.animation = animation
        view.setImageTintList(ResUtil.getColorStateList(R.color.color_accent))
        view.setImageResource(res)
    }

    private fun isSupportCocosMatch(gameType: Int): Boolean {
        return ConfigHelper.getInstance().getGameConfig(gameType).isSupportCocosMatch
    }

    override fun checkLoadResSilent(gameType: Int) {
        CocosGameSilentPreloader.preLoad(gameType)
    }

    /**
     * Fun：赛事详情页进入cocos游戏
     * @param context
     * @param info  cocos游戏相关信息
     * 注意：这里不走enterRoom进房逻辑，原因是服务器会主动将比赛用户加入房间，避免重复进房请求
     */
    override fun gotoLittleGame(context: Context, info: LittleGameInfo) {
        LittleGameResUtil.checkLoadRes(context, info.gameType, "") {
            val intent = getIntent(context, CocosLaunchInfo.ENTER_MODE_JUMP_GAME)
            intent.putExtra(CocosLaunchInfo.KEY_GAME_TYPE, info.gameType)
            intent.putExtra(CocosLaunchInfo.KEY_GAME_MODE, info.gameMode)
            val version = CocosVersionManager.getInstance().getGameVersion(info.gameType)
            intent.putExtra(CocosLaunchInfo.KEY_COCOS_RES_VERSION, version)
            LittleGame.addGameInfoToIntent(
                intent, info,
                AbTestManager.getInstance().connectorSupport.contains(info.gameType)
            )
            GlobalConfigHelper.addGlobalConfig(intent)
            context.startActivity(intent)
            val activity = ContextUtil.getActivityFromContext(context)
            activity?.overridePendingTransition(R.anim.fade_in, R.anim.fade_out)
        }
    }

    /**
     * Fun：其他页面进入cocos游戏
     * @param context
     * @param info  cocos游戏相关信息
     * 注意：这里要走enterRoom进房逻辑，不然没有加入房间，部分操作不生效
     */
    override fun gotoLittleGameAndEnterRoom(context: Context, info: LittleGameInfo, enterRoomInfo: LittleGameEnterRoomSimpleInfo) {
        VoiceRoomPacketSender.enterRoom(
            enterRoomInfo.rid, enterRoomInfo.pwd, enterRoomInfo.keyConfirm, enterRoomInfo.isLoveRoom, enterRoomInfo.followUid,
            enterRoomInfo.gameType, enterRoomInfo.index, enterRoomInfo.source, object : LifeSeqCallback(ContextUtil.getLife(context)) {
                override fun onSuccess(head: RspHeadInfo?) {
                    gotoLittleGame(context, info)
                }

                override fun onFail(head: RspHeadInfo?) {
                    gotoLittleGame(context, info)
                    HLog.d(
                        "LittleGameApiImpl",
                        HLog.USR,
                        "enter room failed,code:${head?.code},msg:${head?.desc}"
                    )
                }
            })
    }

    override fun setCocosWebViewCacheEnabled(value: Boolean) {
        cocosWebViewCacheEnabled = value
    }

    override fun getCocosWebViewCacheEnabled(): Boolean {
        return cocosWebViewCacheEnabled
    }

    override fun setPreloadCocosAssetEnabled(value: Boolean) {
        preloadCocosAssetEnabled = value
    }

    override fun getPreloadCocosAssetEnabled(): Boolean {
        return preloadCocosAssetEnabled
    }

    override fun preloadCocosAsset(context: Context, gameType: Int) {
        CocosWebViewPreloadAssetHelper.preloadAsset(context, gameType)
    }
}