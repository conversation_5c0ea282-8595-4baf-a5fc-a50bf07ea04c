package com.wepie.wespy.cocosnew

import android.content.Intent
import com.huiwan.base.util.StringUtil
import com.huiwan.lib.api.impl
import com.wejoy.littlegame.LittleGame
import com.wepie.lib.api.plugins.track.TrackApi
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.liblog.main.HLog
import com.wepie.wespy.model.entity.match.TeamInfo

private const val KEY_SOURCE = "__cocos_match_source"


fun Intent.addCocosPrepareTrackInfo(trackSource: String) {
    putExtra(KEY_SOURCE, trackSource)
}

fun cocosTrackViewPrepare(intent: Intent) {
    if (LittleGame.teamInfo.tid <= 0) {
        return
    }
    val source = intent.getStringExtra(KEY_SOURCE)
    val teamInfo = LittleGame.teamInfo
    val sourceUse = source ?: ""
    val map = mapOf(
        "game_type" to teamInfo.gameType,
        "team_id" to teamInfo.tid,
        "mode_type" to teamInfo.trackModeType(),
        "match_mode" to teamInfo.trackMatchMode(),
        "bet_level" to teamInfo.betLevel,
        "source" to sourceUse
    )
    TrackUtil.appViewScreen(TrackSource.PREPARE_PAGE, map)
}

fun cocosTrackPrepareChangeReady() {
    val teamInfo = LittleGame.teamInfo
    if (teamInfo.selfIsReady()) {
        cocosTrackPrepareAppClick(TrackButtonName.CANCEL_PREPARE)
    } else {
        cocosTrackPrepareAppClick(TrackButtonName.PREPARE)
    }
}

fun cocosTrackPrepareAppClick(btnName: String, status: Int? = null) {
    val team = LittleGame.teamInfo
    val map = mutableMapOf(
        "game_type" to team.gameType,
        "mode_type" to TeamInfo.trackModeType(
            team.gameType,
            team.gameMode
        ),
        "bet_level" to team.betLevel,
        "match_mode" to team.trackMatchMode()
    )
    if (status != null) {
        map["status"] = status
    }
    TrackUtil.appClick(TrackSource.PREPARE_PAGE, btnName, map.toMap())
}

/**
 * CocosLaunch 传值
 */
sealed class CocosLaunchTrack(
    val step: Int,
    /**
     * 上一步应该是什么，不匹配的话，就不打这个点
     */
    val lastStep: Int,
    val gameType: Int = 0,
    /**
     * 更新资源的大小
     */
    val packageSize: Int = 0,
    val eventDurationMs: Int = 0,
) {

    /**
     * 点击首页的匹配入口
     */
    class ClickGameEntry(gameType: Int) : CocosLaunchTrack(step = 10, lastStep = -1, gameType)

    /**
     * 匹配时对应的检查资源
     */
    class CheckUpdateRes(gameType: Int) : CocosLaunchTrack(step = 20, lastStep = 10, gameType)

    /**
     * 更新资源成功【未更新情况也算】
     */
    class CheckUpdateResSuccess(gameType: Int, eventDurationMs: Int, packageSize: Int) :
        CocosLaunchTrack(
            step = 30,
            lastStep = 20,
            gameType = gameType,
            eventDurationMs = eventDurationMs,
            packageSize = packageSize
        )

    /**
     * 启动 cocos
     */
    class LaunchCocos(gameType: Int) :
        CocosLaunchTrack(step = 40, lastStep = 30, gameType = gameType)

    private fun fillMap(map: MutableMap<String, Any>, name: String, arg: Any?) {
        if (arg != null) {
            map[name] = arg
        }
    }

    fun toTrackMap(): Map<String, Any> {
        val map = mutableMapOf<String, Any>()
        val pktSize = if (packageSize > 0) {
            StringUtil.format("%.2f", packageSize / (1024 * 1024f)).toFloat()
        } else {
            0.0f
        }
        map["progress"] = step
        fillMap(map, "game_type", gameType)
        fillMap(map, "event_duration", eventDurationMs)
        fillMap(map, "package_size", pktSize)
        fillMap(map, "launch_mode", 0) // 冷启动。后续看支持
        return map
    }
}

/**
 * 记录本次打开首页的时间，用于计算整体的 duration
 */
private var lastStep = -1
private const val COCOS_LAUNCH = "CocosLaunch"
/**
 * CocosLaunch 打点
 */
fun cocosTrackLaunch(track: CocosLaunchTrack) {
    if (track.lastStep > 0 && track.lastStep != lastStep) {
        HLog.d(COCOS_LAUNCH, HLog.USR, "track ignore cur={}, last={}", track.step, lastStep)
        lastStep = -1
        return
    }
    lastStep = track.step
    val map = track.toTrackMap()
    TrackApi::class.impl().trackEvent(COCOS_LAUNCH, map)
}

fun cocosTrackLaunchReset() {
    lastStep = -1
}