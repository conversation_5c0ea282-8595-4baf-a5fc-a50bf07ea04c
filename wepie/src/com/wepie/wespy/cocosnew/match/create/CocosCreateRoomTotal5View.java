package com.wepie.wespy.cocosnew.match.create;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.wepie.lib.api.plugins.voice.SpeakerInfo;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.OnGameUserEventListener;
import com.wepie.wespy.cocosnew.match.prepare.CocosPrepareUserView;
import com.wepie.wespy.model.entity.match.SeatInfo;
import com.wepie.wespy.model.entity.match.TeamInfo;

import java.util.List;


/**
 *
 * 3人
 */
public class CocosCreateRoomTotal5View extends AbsCocosCreateRoomView {
    private final static String TAG = "CocosCreateRoomTotal5View";
    private final CocosPrepareUserView[] users = new CocosPrepareUserView[5];

    public CocosCreateRoomTotal5View(Context context) {
        super(context);
        initView();
    }

    public CocosCreateRoomTotal5View(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public CocosCreateRoomTotal5View(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    @Override
    public void initView() {
        setClipChildren(false);
        LayoutInflater.from(getContext()).inflate(R.layout.ice_ball_create_room_total5_view, this);
        users[0] = findViewById(R.id.ice_ball_create_room_user0);
        users[1] = findViewById(R.id.ice_ball_create_room_user1);
        users[2] = findViewById(R.id.ice_ball_create_room_user2);
        users[3] = findViewById(R.id.ice_ball_create_room_user3);
        users[4] = findViewById(R.id.ice_ball_create_room_user4);
        for (int i = 0; i < users.length; i++) {
            users[i].initSeatId(i);
        }
    }

    @Override
    public void updateSeatInfo(TeamInfo teamInfo, OnGameUserEventListener listener) {
        for (SeatInfo seatInfo : teamInfo.getSeats()) {
            if (seatInfo.getSeatNum() <= users.length) {
                users[seatInfo.getSeatNum()].update(seatInfo, teamInfo.getOwner(), true, listener);
            } else {
                HLog.e(TAG, "seat info invalid: " + seatInfo);
            }
        }
    }

    @Override
    public void setEmptyImg(@NonNull Drawable drawable) {
        for (CocosPrepareUserView view : users) {
            if (view != null) {
                view.setEmptyImg(drawable);
            }
        }
    }

    @Override
    public void refreshAnim(@NonNull List<? extends SpeakerInfo> speakerInfos) {
        for (SpeakerInfo speakerInfo : speakerInfos) {
            for (CocosPrepareUserView view : users) {
                if (speakerInfo.uid == view.getUid()) {
                    view.startAnim();
                }
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        for (CocosPrepareUserView view : users) {
            view.stopAnim();
        }
    }
}
