package com.wepie.wespy.cocosnew.match.create;


import android.os.Handler;
import android.os.Looper;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.gift.GiftAnimUtil;
import com.huiwan.component.gift.send.GiftSendInfo;
import com.huiwan.component.gift.show.GiftSendScene;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.constants.GameType;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.littlegame.event.GiftCommonSendEvent;
import com.huiwan.littlegame.event.IceGameCommandEvent;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.entity.User;
import com.huiwan.voiceservice.VoiceManager;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.gamematch.net.GameMatchSender;
import com.wejoy.littlegame.LittleGame;
import com.wepie.lib.api.plugins.voice.SpeakCallback;
import com.wepie.lib.api.plugins.voice.SpeakerInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.LittleGameStateCheckHelper;
import com.wepie.wespy.cocosnew.match.TeamVoiceManager;
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil;
import com.wepie.wespy.helper.shence.ShenceGameTypeSource;
import com.wepie.wespy.helper.shence.util.ShenceGiftUtil;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.model.event.iceball.IceBeforeStartEvent;
import com.wepie.wespy.model.event.iceball.IceSwitchSeatEvent;
import com.wepie.wespy.model.event.match.FinishTeamViewEvent;
import com.wepie.wespy.model.event.match.SyncTeamRspEvent;
import com.wepie.wespy.model.event.match.TeamKickOutEvent;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.http.api.GameApi;
import com.wespy.component.suspend.SuspendManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;


/**
 * <AUTHOR> Yuu
 * email <EMAIL>
 * date 2017/11/9.
 * <p>
 * 冰球创建房间
 */
public class CocosCreateRoomPresenter {
    private static final int RID = -1;
    public static final int SWITCH_WAIT_TIME = 10;
    public static final long SWITCH_WAIT_TIME_IN_MILLIS = SWITCH_WAIT_TIME * 1000;

    private final ICocosCreateRoomView createRoomView;

    private Handler switchHandle;
    private int switchSeatId;
    private int switchUserId;
    private final long lastReqSwitchTime = 0;
    private int gameType;
    private final int tid;
    private final int voiceType;
    private final LittleGameStateCheckHelper gameStateCheckHelper = LittleGameStateCheckHelper.INSTANCE;
    private boolean isSyncEnter;

    public CocosCreateRoomPresenter(ICocosCreateRoomView createRoomView) {
        this.createRoomView = createRoomView;
        gameStateCheckHelper.setSuccessCallback(createRoomView, () -> {
            isSyncEnter = true;
            createRoomView.hideLoading();
        });
        gameStateCheckHelper.checkGameState();
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        tid = teamInfo.getTid();
        voiceType = teamInfo.getVoiceType();
    }

    public void init() {
        initView();
        switchHandle = new Handler(Looper.getMainLooper());
    }


    private int timeToUpdate = SWITCH_WAIT_TIME;
    private final Runnable switchTimeUpdateRunnable = new Runnable() {
        @Override
        public void run() {
            updateSwitchTime();
        }
    };

    private void updateSwitchTime() {
        if (timeToUpdate <= 0) {
            createRoomView.hideSwitchSeatDialog();
            resetSwitchInfo();
        } else {
            createRoomView.updateSwitchSeatDialog(switchUserId, --timeToUpdate);
            switchHandle.postDelayed(switchTimeUpdateRunnable, 1001L);
        }
    }

    private void resetSwitchInfo() {
        switchSeatId = -1;
        switchUserId = -1;
    }

    /**
     * 推送，收到邀请更换位置
     *
     * @param event 开始匹配事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleSwitchSeat(IceSwitchSeatEvent event) {
        timeToUpdate = SWITCH_WAIT_TIME;
        if (switchUserId != event.fromUser && event.fromUser != LoginHelper.getLoginUid()) {
            switchUserId = event.fromUser;
            switchSeatId = event.fromSeatId;
            createRoomView.showNewSwitchSeatDialog(switchUserId, timeToUpdate);
            switchHandle.postDelayed(switchTimeUpdateRunnable, 1001L);
        }
    }

    /**
     * 推送，开始游戏
     *
     * @param event 进入游戏界面事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleBeforeStartGame(final IceBeforeStartEvent event) {
        if (isSyncEnter) {
            return;
        }
        gameStateCheckHelper.stopCheck();
        createRoomView.hideLoading();
        createRoomView.jumpToGame(event.beforeStartInfo);
    }

    /**
     * @param event 同步事件 Response
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleSyncTeamResponse(SyncTeamRspEvent event) {
        if (event.teamInfo.tid == LittleGame.getTeamInfo().tid
                && !event.teamInfo.selfIsInTeam()) {//sync 发现自己不在队伍里
            createRoomView.leave();
            ToastUtil.show(ResUtil.getStr(R.string.cocos_match_prepare_fail_not_in_team));
            return;
        }
        LittleGame.updateTeamInfo(event.teamInfo);
        createRoomView.updateTeamView(event.teamInfo);
        createRoomView.updateInvite(!event.teamInfo.isTeamFull());
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleKickOut(TeamKickOutEvent event) {
        createRoomView.leave();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleFinishTeamView(FinishTeamViewEvent event) {
        createRoomView.leave();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleCommonGift(GiftCommonSendEvent event) {
        final GiftShowInfo showInfo = event.showInfo;
        if (showInfo.sendScene == GiftSendScene.SceneCocosTeam.value) {
            GiftAnimUtil.getGiftApi().getBarrageContent(showInfo, new Function1<String, Unit>() {
                @Override
                public Unit invoke(String s) {
                    showInfo.barrageAnimStr = s;
                    createRoomView.showGiftAnim(showInfo);
                    return Unit.INSTANCE;
                }
            });
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onIceGameCommand(IceGameCommandEvent event) {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        if (event.command == IceGameCommandEvent.COMMAND_GAME_RETURN && GameConfig.isJackarooVip(teamInfo.game_type, teamInfo.gameMode)) {
            User user = LoginHelper.getLoginUser();
            if (user == null || !user.isVip())
                createRoomView.leave();
        }
    }

    public String getTitleText() {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        if (gameType == GameType.GAME_TYPE_SHEEP) {
            return CocosGameConfigUtil.getCocosGameConfig(GameType.GAME_TYPE_SHEEP).getName();
        }

        GameConfig.MatchInfo matchInfo = ConfigHelper.getInstance().getGameConfig().getLittleGameMatchInfo(
                getGameType(), teamInfo.betLevel, teamInfo.mode, teamInfo.gameMode, teamInfo.currencyType);

        if (matchInfo != null) {
            return matchInfo.getTitle();
        } else {
            return teamInfo.getModeName();
        }
    }

    private void initView() {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        createRoomView.updateTeamView(teamInfo);
        createRoomView.updateInvite(!teamInfo.isTeamFull());
    }

    public void agreeSwitchSeat() {
        switchHandle.removeCallbacks(switchTimeUpdateRunnable);
        GameMatchSender.agreeSwitchReq(LittleGame.getTeamInfo().getTid(), switchSeatId, switchUserId, new LifeSeqCallback(createRoomView) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                resetSwitchInfo();
            }

            @Override
            public void onFail(RspHeadInfo head) {
                resetSwitchInfo();
                createRoomView.showTipMsg(head.desc);
            }
        });
    }

    public void disagreeSwitchSeat() {
        resetSwitchInfo();
        switchHandle.removeCallbacks(switchTimeUpdateRunnable);
    }

    public void leaveTeam() {
        createRoomView.leave();
        EventDispatcher.postChatGameInviteCancelEvent();
        GameMatchSender.leaveTeamReq(LittleGame.getTeamInfo().getTid(), new LifeSeqCallback(createRoomView) {
            @Override
            public void onSuccess(RspHeadInfo head) {
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }


    public void showInviteFriend() {
        createRoomView.showInviteFriendDialog(LittleGame.getTeamInfo().getTid(), getGameType());
    }


    public void startGame() {
        /* 该 loading 应该在收到 push 的时候隐藏 */
        createRoomView.showLoading();
        isSyncEnter = false;
        GameMatchSender.startGameReq(RID, LittleGame.getTeamInfo().getTid(), new LifeSeqCallback(createRoomView) {
            @Override
            public void onSuccess(RspHeadInfo head) {
            }

            @Override
            public void onFail(RspHeadInfo head) {
                createRoomView.hideLoading();
                createRoomView.showTipMsg(head.desc);
            }
        });
    }

    public void changeReadyState() {
        isSyncEnter = false;
        if (LittleGame.getTeamInfo().selfIsReady()) {
            unReady();
        } else {
            ready();
        }
    }

    private void ready() {
        createRoomView.showLoading();
        GameMatchSender.readyReq(LittleGame.getTeamInfo().getTid(), new LifeSeqCallback(createRoomView) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                createRoomView.hideLoading();
            }

            @Override
            public void onFail(RspHeadInfo head) {
                createRoomView.hideLoading();
                createRoomView.showTipMsg(head.desc);
            }
        });
    }

    private void unReady() {
        createRoomView.showLoading();
        GameMatchSender.unreadyReq(LittleGame.getTeamInfo().getTid(), new LifeSeqCallback(createRoomView) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                createRoomView.hideLoading();
            }

            @Override
            public void onFail(RspHeadInfo head) {
                createRoomView.hideLoading();
                createRoomView.showTipMsg(head.desc);
            }
        });
    }

    public void sendGift(final GiftSendInfo sendInfo) {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        if (teamInfo != null) {
            for (int i = 0; i < teamInfo.players.size(); i++) {
                sendInfo.notifyUidList.add(teamInfo.players.get(i).uid);
            }
            GameApi.sendGiftCommon(sendInfo, new LifeDataCallback<>(createRoomView) {
                @Override
                public void onSuccess(Result<Object> result) {
                    ShenceGiftUtil.reportSendGift(sendInfo, ShenceGameTypeSource.getGameTypeShortSource(gameType));
                }

                @Override
                public void onFail(int code, String msg) {
                    ToastUtil.show(msg);
                }
            });
        }
    }

    public void registerEventBus() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    public void onDestroy() {
        switchHandle.removeCallbacks(switchTimeUpdateRunnable);
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        if (!SuspendManager.INSTANCE.isSuspend()) {
            gameStateCheckHelper.stopCheck();
        }
    }

    public void onResume() {
        if (TeamVoiceManager.getInstance().onActivityResume(tid, voiceType)) {
            registerSpeakCallback();
        }
    }

    public void clickSpeaker() {
        if (TeamVoiceManager.getInstance().clickSpeaker(tid, voiceType)) {
            registerSpeakCallback();
        }
    }

    public void registerSpeakCallback() {
        VoiceManager.getInstance().observeSpeak(createRoomView, new SpeakCallback() {
            @Override
            public void onSpeak(List<SpeakerInfo> speakerInfos) {
                createRoomView.onSpeak(speakerInfos);
            }

            @Override
            public void uploadMemberId(int member) {

            }
        });
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    public int getGameType() {
        return gameType;
    }

    public TeamInfo getTeamInfo() {
        return LittleGame.getTeamInfo();
    }
}
