package com.wepie.wespy.cocosnew.match.create;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.wepie.lib.api.plugins.voice.SpeakerInfo;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.OnGameUserEventListener;
import com.wepie.wespy.cocosnew.match.prepare.CocosPrepareUserView;
import com.wepie.wespy.model.entity.match.SeatInfo;
import com.wepie.wespy.model.entity.match.TeamInfo;

import java.util.List;


/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/11/16.
 */
public class CocosCreateRoom1V1View extends AbsCocosCreateRoomView {
    private final static String TAG = "CocosCreateRoom1V1View";
    private final CocosPrepareUserView[] members = new CocosPrepareUserView[2];

    public CocosCreateRoom1V1View(Context context) {
        super(context);
        initView();
    }

    public CocosCreateRoom1V1View(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public CocosCreateRoom1V1View(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    @Override
    public void initView() {
        setClipChildren(false);
        LayoutInflater.from(getContext()).inflate(R.layout.ice_ball_create_room_1v1_view, this);
        members[0] = findViewById(R.id.ice_ball_create_room_user0);
        members[1] = findViewById(R.id.ice_ball_create_room_user1);
    }

    @Override
    public void updateSeatInfo(TeamInfo teamInfo, OnGameUserEventListener listener) {
        for (SeatInfo seatInfo : teamInfo.getSeats()) {
            if (seatInfo.getSeatNum() < members.length && seatInfo.getSeatNum() >= 0) {
                CocosPrepareUserView userView = members[seatInfo.getSeatNum()];
                userView.update(seatInfo, teamInfo.getOwner(), false, listener);
            } else {
                HLog.e(TAG, "seat info invalid: " + seatInfo);
            }
        }
    }

    @Override
    public void setEmptyImg(@NonNull Drawable drawable) {
        for (CocosPrepareUserView view : members) {
            if (view != null) {
                view.setEmptyImg(drawable);
            }
        }
    }

    @Override
    public void refreshAnim(@NonNull List<? extends SpeakerInfo> speakerInfos) {
        for (SpeakerInfo speakerInfo : speakerInfos) {
            for (CocosPrepareUserView view : members) {
                if (speakerInfo.uid == view.getUid()) {
                    view.startAnim();
                }
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        for (CocosPrepareUserView view : members) {
            view.stopAnim();
        }
    }
}
