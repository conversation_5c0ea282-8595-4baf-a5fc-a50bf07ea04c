package com.wepie.wespy.cocosnew.match.create;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.wepie.lib.api.plugins.voice.SpeakerInfo;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.OnGameUserEventListener;
import com.wepie.wespy.cocosnew.match.prepare.CocosPrepareUserView;
import com.wepie.wespy.model.entity.match.SeatInfo;
import com.wepie.wespy.model.entity.match.TeamInfo;

import java.util.List;


/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/11/16.
 */
public class CocosCreateRoom2V2V2View extends AbsCocosCreateRoomView {
    private final static String TAG = "CocosCreateRoom2V2V2View";
    private final CocosPrepareUserView[] users = new CocosPrepareUserView[6];
    private final View[] lines = new View[3];

    public CocosCreateRoom2V2V2View(Context context) {
        super(context);
        initView();
    }

    public CocosCreateRoom2V2V2View(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public CocosCreateRoom2V2V2View(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    @Override
    public void initView() {
        setClipChildren(false);
        LayoutInflater.from(getContext()).inflate(R.layout.ice_ball_create_room_2v2v2_view, this);
        users[0] = findViewById(R.id.ice_ball_create_room_user0);
        users[1] = findViewById(R.id.ice_ball_create_room_user1);
        users[2] = findViewById(R.id.ice_ball_create_room_user2);
        users[3] = findViewById(R.id.ice_ball_create_room_user3);
        users[4] = findViewById(R.id.ice_ball_create_room_user4);
        users[5] = findViewById(R.id.ice_ball_create_room_user5);
        for (int i = 0; i < users.length; i++) {
            // 座位号从 1 开始
            users[i].initSeatId(i);
        }
        lines[0] = findViewById(R.id.little_game_line0);
        lines[1] = findViewById(R.id.little_game_line1);
        lines[2] = findViewById(R.id.little_game_line2);
    }

    @Override
    public void updateSeatInfo(TeamInfo teamInfo, OnGameUserEventListener listener) {
        for (SeatInfo seatInfo : teamInfo.getSeats()) {
            if (seatInfo.getSeatNum() <= users.length) {
                users[seatInfo.getSeatNum()].update(seatInfo, teamInfo.getOwner(), true, listener);
            } else {
                HLog.e(TAG, "seat info invalid: " + seatInfo);
            }
        }
    }

    @Override
    public void hideLines() {
        for (View line: lines) {
            if (line.getVisibility() == View.VISIBLE) {
                line.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void setEmptyImg(@NonNull Drawable drawable) {
        for (CocosPrepareUserView view : users) {
            if (view != null) {
                view.setEmptyImg(drawable);
            }
        }
    }

    @Override
    public void refreshAnim(@NonNull List<? extends SpeakerInfo> speakerInfos) {
        for (SpeakerInfo speakerInfo : speakerInfos) {
            for (CocosPrepareUserView view : users) {
                if (speakerInfo.uid == view.getUid()) {
                    view.startAnim();
                }
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        for (CocosPrepareUserView view : users) {
            view.stopAnim();
        }
    }
}
