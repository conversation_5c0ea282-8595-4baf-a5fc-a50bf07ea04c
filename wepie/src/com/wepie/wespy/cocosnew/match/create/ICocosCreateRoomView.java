package com.wepie.wespy.cocosnew.match.create;

import androidx.lifecycle.LifecycleOwner;

import com.huiwan.component.gift.show.GiftShowInfo;
import com.wepie.lib.api.plugins.voice.SpeakerInfo;
import com.wepie.wespy.model.entity.iceball.IceGameBeforeStart;
import com.wepie.wespy.model.entity.match.TeamInfo;

import java.util.List;

/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/11/17.
 */

public interface ICocosCreateRoomView extends LifecycleOwner {


    /**
     * 收到更换位置请求
     * @param fromUser 发送请求者
     * @param second 对话框继续显示的时间
     */
    void showNewSwitchSeatDialog(int fromUser, int second);


    /**
     * 更新请求交换信息
     * @param fromUser 发送请求者
     * @param second 对话框继续显示的时间
     */
    void updateSwitchSeatDialog(int fromUser, int second);

    /**
     * 隐藏座位交换框
     */
    void hideSwitchSeatDialog();

    /**
     * 显示邀请双向好友对话框
     * @param tid 队伍 id
     * @param gameType 游戏类型
     */
    void showInviteFriendDialog(int tid, int gameType);

    /**
     * 显示提示信息
     * @param msg 提示信息
     */
    void showTipMsg(String msg);

    /**
     * 显示 loading 界面
     */
    void showLoading();

    /**
     * 隐藏 loading 界面
     */
    void hideLoading();

    /**
     * 跳转到游戏界面
     */
    void jumpToGame(IceGameBeforeStart iceGameBeforeStart);

    /**
     * 更新队伍信息
     */
    void updateTeamView(TeamInfo teamInfo);

    /**
     * 是否可以邀请好友
     * @param canInvite true or false
     */
    void updateInvite(boolean canInvite);

    /**
     * 退出当前界面
     */
    void leave();

    /**
     * 收到礼物时播放礼物动画
     * @param showInfo show info
     */
    void showGiftAnim(GiftShowInfo showInfo);

    void onSpeak(List<SpeakerInfo> speakerInfos);
}
