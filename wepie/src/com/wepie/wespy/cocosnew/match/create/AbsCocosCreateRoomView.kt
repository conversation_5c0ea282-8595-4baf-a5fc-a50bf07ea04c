package com.wepie.wespy.cocosnew.match.create

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.widget.LinearLayout
import com.wepie.lib.api.plugins.voice.SpeakerInfo
import com.wepie.wespy.cocosnew.match.OnGameUserEventListener
import com.wepie.wespy.model.entity.match.TeamInfo

abstract class AbsCocosCreateRoomView : LinearLayout {

    constructor(context: Context?) : super(context)
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    abstract fun initView()

    abstract fun updateSeatInfo(teamInfo: TeamInfo, listener: OnGameUserEventListener?)
    abstract fun setEmptyImg(drawable: Drawable)

    abstract fun refreshAnim(speakerInfos: List<SpeakerInfo>)

    open fun hideLines() {}
}