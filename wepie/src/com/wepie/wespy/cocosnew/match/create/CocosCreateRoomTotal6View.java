package com.wepie.wespy.cocosnew.match.create;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.wepie.lib.api.plugins.voice.SpeakerInfo;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.OnGameUserEventListener;
import com.wepie.wespy.cocosnew.match.prepare.CocosPrepareUserView;
import com.wepie.wespy.model.entity.match.SeatInfo;
import com.wepie.wespy.model.entity.match.TeamInfo;

import java.util.List;


/**
 * 6人局，支持3v3
 */
public class CocosCreateRoomTotal6View extends AbsCocosCreateRoomView {
    private final static String TAG = "CocosCreateRoomTotal6View";
    private final CocosPrepareUserView[] users = new CocosPrepareUserView[6];
    private boolean is3V3 = false;

    public CocosCreateRoomTotal6View(Context context, boolean is3V3) {
        super(context);
        this.is3V3 = is3V3;
        initView();
    }

    public CocosCreateRoomTotal6View(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public CocosCreateRoomTotal6View(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    @Override
    public void initView() {
        setClipChildren(false);
        LayoutInflater.from(getContext()).inflate(R.layout.ice_ball_create_room_total6_view, this);
        if (is3V3) {
            users[0] = findViewById(R.id.ice_ball_create_room_user0);
            users[1] = findViewById(R.id.ice_ball_create_room_user1);
            users[2] = findViewById(R.id.ice_ball_create_room_user2);
            users[3] = findViewById(R.id.ice_ball_create_room_user3);
            users[4] = findViewById(R.id.ice_ball_create_room_user4);
            users[5] = findViewById(R.id.ice_ball_create_room_user5);
        } else {
            users[0] = findViewById(R.id.ice_ball_create_room_user0);
            users[1] = findViewById(R.id.ice_ball_create_room_user3);
            users[2] = findViewById(R.id.ice_ball_create_room_user4);
            users[3] = findViewById(R.id.ice_ball_create_room_user5);
            users[4] = findViewById(R.id.ice_ball_create_room_user2);
            users[5] = findViewById(R.id.ice_ball_create_room_user1);
            findViewById(R.id.little_game_line0).setVisibility(View.INVISIBLE);
            findViewById(R.id.little_game_line1).setVisibility(View.INVISIBLE);
        }
        for (int i = 0; i < users.length; i++) {
            users[i].initSeatId(i);
        }
    }

    @Override
    public void updateSeatInfo(TeamInfo teamInfo, OnGameUserEventListener listener) {
        for (SeatInfo seatInfo : teamInfo.getSeats()) {
            if (seatInfo.getSeatNum() <= users.length) {
                users[seatInfo.getSeatNum()].update(seatInfo, teamInfo.getOwner(), true, listener);
            } else {
                HLog.e(TAG, "seat info invalid: " + seatInfo);
            }
        }
    }

    @Override
    public void setEmptyImg(@NonNull Drawable drawable) {
        for (CocosPrepareUserView view : users) {
            if (view != null) {
                view.setEmptyImg(drawable);
            }
        }
    }

    @Override
    public void refreshAnim(@NonNull List<? extends SpeakerInfo> speakerInfos) {
        for (SpeakerInfo speakerInfo : speakerInfos) {
            for (CocosPrepareUserView view : users) {
                if (speakerInfo.uid == view.getUid()) {
                    view.startAnim();
                }
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        for (CocosPrepareUserView view : users) {
            view.stopAnim();
        }
    }
}
