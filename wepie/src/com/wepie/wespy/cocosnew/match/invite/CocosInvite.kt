package com.wepie.wespy.cocosnew.match.invite

import android.content.Context
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.lib.api.ApiService
import com.huiwan.libtcp.callback.SeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.user.LoginHelper
import com.wejoy.gamematch.net.GameMatchSender
import com.wejoy.weplay.ex.dialog.LifeDialog
import com.wepie.lib.api.plugins.share.IShareApi
import com.wepie.lib.api.plugins.share.ShareCallback
import com.wepie.lib.api.plugins.share.ShareInfo
import com.wepie.lib.api.plugins.share.ShareResult
import com.wepie.lib.api.plugins.share.ShareTrack
import com.wepie.lib.api.plugins.share.ShareType
import com.wepie.lib.api.plugins.share.ShareUtil
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName.INVITE
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.api.plugins.track.config.os.TrackString
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil

object CocosInvite {
    fun invite(
        context: Context,
        tid: Int,
        gameType: Int,
        subScene: String
    ): LifeDialog {
        val json = "%7b%22tid%22+%3a+${tid}%2c%22game_type%22+%3a+${gameType}%7d"
        val gameConfig = CocosGameConfigUtil.getCocosGameConfig(gameType)
        val inviteUrl = "${ConfigHelper.getInstance().myShareUrl}share/team?code=" +
                "${ShareUtil.id2code(LoginHelper.getLoginUid())}&game_type=${gameType}&payload=${json}"
        val shareInfo = ShareInfo()
        shareInfo.setTitle(gameConfig.inviteTitle)
        shareInfo.content = gameConfig.inviteDesc
        shareInfo.bitmapPath = ConfigHelper.getInstance().gameConfig.getGameIcon(gameType)

        //添加分享的打点信息
        shareInfo.screenName = TrackScreenName.SHARE_PAGE
        shareInfo.scene = TrackString.SCENE_GAME
        shareInfo.gameType = gameType
        shareInfo.extTrackInfo["sub_scene"] = subScene
        shareInfo.setLink(shareInfo.getLinkIntercept(inviteUrl))

        shareInfo.addTripartiteShareType()
        shareInfo.addShareTypes(ShareType.friend, ShareType.android, ShareType.copyLink)
        return ApiService.of(IShareApi::class.java).showShareDialog(
            context,
            shareInfo,
            object : ShareCallback {
                override fun onShare(data: ShareResult): Boolean {
                    if (data.shareType == ShareType.friend) {
                        if (data.target is ShareResult.Target.Friend) {
                            inviteFriend(
                                (data.target as ShareResult.Target.Friend).uid,
                                tid,
                                gameType,
                                subScene
                            )
                        } else if (data.target is ShareResult.Target.Group) {
                            inviteGroup(
                                (data.target as ShareResult.Target.Group).gid,
                                tid,
                                gameType,
                                subScene
                            )
                        }
                    }
                    return true
                }
            }
        )
    }

    private fun inviteFriend(uid: Int, tid: Int, gameType: Int, subScene: String) {
        val list = ArrayList<Int>(1)
        list.add(uid)
        GameMatchSender.inviteFriendReq(0, tid, list, object : SeqCallback {
            override fun onSuccess(head: RspHeadInfo) {
                ApiService.of(IShareApi::class.java).shareClickTrack(
                    ShareInfo.ShareReportData.build(
                        TrackScreenName.SHARE_PAGE,
                        TrackString.SCENE_GAME, ShareTrack.TYPE_LINK, gameType, "", INVITE, uid
                    ).setExt(
                        mapOf(
                            "sub_scene" to subScene
                        )
                    )
                )
            }

            override fun onFail(head: RspHeadInfo) {
                ToastUtil.show(head.desc)
            }
        })
    }

    private fun inviteGroup(gid: Int, tid: Int, gameType: Int, subScene: String) {
        GameMatchSender.inviteGroupReq(0, tid, gid, object : SeqCallback {
            override fun onSuccess(head: RspHeadInfo) {
                ApiService.of(IShareApi::class.java).shareClickTrack(
                    ShareInfo.ShareReportData.build(
                        TrackScreenName.SHARE_PAGE,
                        TrackString.SCENE_GAME, ShareTrack.TYPE_LINK, gameType, "", INVITE, gid
                    ).setExt(
                        mapOf(
                            "sub_scene" to subScene
                        )
                    )
                )
            }

            override fun onFail(head: RspHeadInfo) {
                ToastUtil.show(head.desc)
            }
        })
    }
}