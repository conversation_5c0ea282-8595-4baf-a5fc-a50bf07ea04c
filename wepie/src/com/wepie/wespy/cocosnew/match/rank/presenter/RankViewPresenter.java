package com.wepie.wespy.cocosnew.match.rank.presenter;

import com.huiwan.base.util.ToastUtil;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.wespy.cocosnew.match.rank.callback.ICocosRankingView;
import com.wepie.wespy.model.entity.cocos.IceBallRankInfo;
import com.wepie.wespy.model.entity.cocos.OnePersonalRankInfo;
import com.wepie.wespy.model.entity.cocos.TwoPersonalRankInfo;
import com.wepie.wespy.net.http.api.TopListApi;

import java.util.List;

/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/11/11.
 */
public class RankViewPresenter {

    private final ICocosRankingView rankingView;
    private int gameType;

    public RankViewPresenter(ICocosRankingView rankingView) {
        this.rankingView = rankingView;
    }

    public void updateIceBallWeekRank() {
        TopListApi.getGameRankWeekly(getGameType(), new LifeDataCallback<>(rankingView) {
            @Override
            public void onSuccess(Result<List<IceBallRankInfo>> result) {
                rankingView.updateWeekRankSuccess(result.data);
            }

            @Override
            public void onFail(int i, String s) {
                ToastUtil.show(s);
            }
        });
    }

    public void updateIceBallTotalRank() {
        TopListApi.getGameRankTotal(getGameType(), new LifeDataCallback<>(rankingView) {
            @Override
            public void onSuccess(Result<List<IceBallRankInfo>> result) {
                rankingView.updateTotalRankSuccess(result.data);
            }

            @Override
            public void onFail(int i, String s) {
                ToastUtil.show(s);
            }
        });
    }

    public void updateCooperateRankTotal() {
        TopListApi.getCooperateGameRankTotal(getGameType(), new LifeDataCallback<>(rankingView) {
            @Override
            public void onSuccess(Result<List<TwoPersonalRankInfo>> result) {
                rankingView.updateCooperateTotalRankSuccess(result.data);
            }

            @Override
            public void onFail(int i, String s) {
                ToastUtil.show(s);
            }
        });
    }

    public void updateCooperateWeeklyRank() {
        TopListApi.getCooperateGameRankWeekly(getGameType(), new LifeDataCallback<>(rankingView) {
            @Override
            public void onSuccess(Result<List<TwoPersonalRankInfo>> result) {
                rankingView.updateCooperateWeekRankSuccess(result.data);
            }

            @Override
            public void onFail(int i, String s) {
                ToastUtil.show(s);
            }
        });
    }

    public void updateGameRankPersonal() {
        TopListApi.getGameRankPersonal(getGameType(), new LifeDataCallback<>(rankingView) {
            @Override
            public void onSuccess(Result<List<OnePersonalRankInfo>> result) {
                rankingView.updatePersonalRankSuccess(result.data);
            }

            @Override
            public void onFail(int i, String s) {
                ToastUtil.show(s);
            }
        });
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    public int getGameType() {
        return gameType;
    }

}
