package com.wepie.wespy.cocosnew.match.rank.adapter;

import android.graphics.Color;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.decorate.NameTextView;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.cocos.TwoPersonalRankInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;

// Created by bigwen on 2018/10/8.
public class CooperateTwoUserRankItem extends RecyclerView.ViewHolder {

    private View itemView;
    private TextView rankTv;
    private ImageView rankIconIv;
    private DecorHeadImgView userIv1, userIv2;
    private NameTextView userNameTv1, userNameTv2;
    private TextView levelTv, starNumTv, playTimeTv;
    private ImageView touchView1, touchView2;

    public CooperateTwoUserRankItem(View view) {
        super(view);
        this.itemView = view;
        rankTv = view.findViewById(R.id.l_rank_item_number_tv);
        rankIconIv = view.findViewById(R.id.rank_icon_iv);
        userIv1 = view.findViewById(R.id.ice_ball_rank_item_header);
        userIv2 = view.findViewById(R.id.ice_ball_rank_item_header2);
        userNameTv1 = view.findViewById(R.id.ice_ball_rank_item_user_name);
        userNameTv2 = view.findViewById(R.id.ice_ball_rank_item_user_name2);
        levelTv = view.findViewById(R.id.cocos_game_level_tv);
        starNumTv = view.findViewById(R.id.rank_star_num_tv);
        playTimeTv = view.findViewById(R.id.rank_play_time_tv);
        touchView1 = view.findViewById(R.id.user_touch_view1);
        touchView2 = view.findViewById(R.id.user_touch_view2);;
    }

    public void refresh(int position, final TwoPersonalRankInfo rankInfo) {
        String rankStr = "" + (position+1);

        if (position == 0) {
            rankTv.setVisibility(View.INVISIBLE);
            rankIconIv.setVisibility(View.VISIBLE);
            rankIconIv.setImageResource(R.drawable.cooperate_rank_no1);
        } else if (position == 1) {
            rankTv.setVisibility(View.INVISIBLE);
            rankIconIv.setVisibility(View.VISIBLE);
            rankIconIv.setImageResource(R.drawable.cooperate_rank_no2);
        } else if (position == 2) {
            rankTv.setVisibility(View.INVISIBLE);
            rankIconIv.setVisibility(View.VISIBLE);
            rankIconIv.setImageResource(R.drawable.cooperate_rank_no3);
        } else {
            rankTv.setVisibility(View.VISIBLE);
            rankIconIv.setVisibility(View.INVISIBLE);
        }
        rankTv.setText(rankStr);

        userIv1.setBorderColor(Color.parseColor("#68d3d3d3"));
        userIv1.setBorderWidth(2);
        userIv2.setBorderColor(Color.parseColor("#68d3d3d3"));
        userIv2.setBorderWidth(2);
        userIv1.showUserHeadWithDecorationCache(rankInfo.getUid());
        userIv2.showUserHeadWithDecorationCache(rankInfo.getPartnerUid());
        userNameTv1.setRemarkUser(rankInfo.getUid(), rankInfo.getNickname());
        userNameTv2.setRemarkUser(rankInfo.getPartnerUid(), rankInfo.getPartnerNickname());
        levelTv.setText(ResUtil.getStr(R.string.level_x_1, rankInfo.getLevel()));
        starNumTv.setText(rankInfo.getStar() + "");
        playTimeTv.setText(TimeUtil.formTotalTime(rankInfo.getTime()));

        View.OnClickListener user1Click = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JumpUtil.enterUserInfoDetailActivity(itemView.getContext(), rankInfo.getUid(), TrackSource.LITTLE_GAME_RANK);
            }
        };
        View.OnClickListener user2Click = new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JumpUtil.enterUserInfoDetailActivity(itemView.getContext(), rankInfo.getPartnerUid(), TrackSource.LITTLE_GAME_RANK);
            }
        };
        touchView1.setOnClickListener(user1Click);
        touchView1.setOnClickListener(user1Click);
        touchView2.setOnClickListener(user2Click);
        touchView2.setOnClickListener(user2Click);
    }

}
