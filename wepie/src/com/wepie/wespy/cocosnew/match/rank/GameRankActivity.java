package com.wepie.wespy.cocosnew.match.rank;

import android.os.Bundle;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ColorUtil;
import com.huiwan.base.util.InitializerManagerUtils;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.constants.IntentConfig;
import com.wepie.startup.InitializerManager;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.rank.indicate.GameRankTitleView;
import com.wepie.wespy.cocosnew.match.rank.indicate.IndicateInterface;
import com.wepie.wespy.cocosnew.match.rank.presenter.CocosRankPresenter;
import com.wepie.wespy.cocosnew.match.rank.view.CooperateRankView;
import com.wepie.wespy.cocosnew.match.rank.view.NormalRankView;
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil;
import com.wepie.wespy.cocosnew.view.dialog.IceBallDialogUtil;

import java.util.ArrayList;

// 麻将、小游戏公用的排行榜
public class GameRankActivity extends BaseActivity {
    private FrameLayout contentView;
    private CocosRankPresenter presenter;

    private ImageView helpIv;
    private ImageView backIv;
    private TextView titleTv;

    private RelativeLayout titleLay;
    protected GameRankTitleView rankTitleView;
    protected ImageView rankIconIv;
    protected ImageView rankBgIv;

    /**
     * 修改本变量的数量的时候， rank title 的数量也应该跟随变化
     * 同时应当通知 rankPager 的 adapter 执行 notifyDataSetChanged
     */
    private final ArrayList<View> rankViews = new ArrayList<>();
    /**
     * .size() 大小应该与 rankViews 大小保持一致
     */
    private final ArrayList<String> rankTitles = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        StatusBarUtil.initStatusBar(this, false);
        setContentView(R.layout.acticity_cocos_ice_ball_rank);
        init();
    }

    private void init() {
        int gameType = getIntent().getIntExtra(IntentConfig.GAME_TYPE, -1);
        presenter = new CocosRankPresenter(gameType);

        contentView = findViewById(R.id.rank_content_view);
        titleTv = findViewById(R.id.title);
        helpIv = findViewById(R.id.ice_ball_rank_help_iv);
        backIv = findViewById(R.id.ice_ball_back_img);
        rankTitleView = findViewById(R.id.game_rank_view);
        rankIconIv = findViewById(R.id.rank_icon_iv);
        rankBgIv = findViewById(R.id.rank_bg_iv);
        titleLay = findViewById(R.id.rank_icon_lay);

        initEvent();

        String[] titleText;
        if (presenter.getPlayMode() == GameConfig.PLAY_MODE_COOPERATE) {
            CooperateRankView cooperateRankView = new CooperateRankView(this);
            cooperateRankView.initDate(gameType, index -> rankTitleView.selectItem(index));
            contentView.addView(cooperateRankView);
            titleText = new String[]{ResUtil.getString(R.string.activity_cocos_rank_1), ResUtil.getString(R.string.activity_cocos_rank_2), ResUtil.getStr(R.string.cocos_rank_3)};
            rankIconIv.setVisibility(View.GONE);
            ((RelativeLayout.LayoutParams) titleLay.getLayoutParams()).topMargin = ScreenUtil.dip2px(0);
        } else {
            NormalRankView normalRankView = new NormalRankView(this);
            normalRankView.initDate(gameType, index -> rankTitleView.selectItem(index));
            contentView.addView(normalRankView);
            titleText = new String[]{ResUtil.getString(R.string.activity_cocos_rank_1), ResUtil.getString(R.string.activity_cocos_rank_2)};
            ((RelativeLayout.LayoutParams) titleLay.getLayoutParams()).topMargin = ScreenUtil.dip2px(14);
        }
        titleTv.setText(getString(R.string.activity_cocos_rank_3));
        loadRankBg(gameType);
        setRankView(gameType, titleText);
        setRanIcon(gameType);
        StatusBarUtil.fitNavigationBar(contentView);
    }

    void setRanIcon(int gameType) {
        CocosGameConfigUtil.loadResAsync(rankIconIv, gameType, CocosGameConfigUtil.RANK_ICON);
    }

    public void setRankView(int gameType, String[] titleText) {
        String background = CocosGameConfigUtil.getCocosGameConfig(gameType).getRankIndicateBg();
        String foreground = CocosGameConfigUtil.getCocosGameConfig(gameType).getRankIndicateFg();
        String line = CocosGameConfigUtil.getCocosGameConfig(gameType).getRankIndicateLine();
        String unSelectTextColor = CocosGameConfigUtil.getCocosGameConfig(gameType).getRankIndicateTitleNormal();
        rankTitleView.setData(ColorUtil.getColor(background), ColorUtil.getColor(foreground), ColorUtil.getColor(line), ColorUtil.getColor(unSelectTextColor), titleText);
    }

    public void loadRankBg(int gameType) {
        CocosGameConfigUtil.loadResBgAsync(rankBgIv, gameType, CocosGameConfigUtil.RANK_BACKGROUND);
    }

    private void initEvent() {
        helpIv.setOnClickListener(v -> IceBallDialogUtil.showRankRuleDialog(GameRankActivity.this, presenter.getRankRuleText()));
        backIv.setOnClickListener(v -> finish());
        rankTitleView.setCallback(index -> {
            if (contentView.getChildCount() > 0 && contentView.getChildAt(0) instanceof IndicateInterface) {
                IndicateInterface indicateInterface = (IndicateInterface) contentView.getChildAt(0);
                indicateInterface.setCurrentItem(index);
            }
        });
    }

    @Override
    protected void filterStartup() {
        super.filterStartup();
        InitializerManagerUtils.wait(InitializerManager.FILTER_FOUR);
    }

}
