package com.wepie.wespy.cocosnew.match.rank.adapter;

import android.graphics.Color;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.JsonObject;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StringUtil;
import com.huiwan.constants.GameType;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.decorate.NameTextView;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.HwApi;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.cocos.IceBallRankInfo;

// Created by bigwen on 2018/10/8.
public class NormalRankItem extends RecyclerView.ViewHolder {

    private static final int COLOR_RANK_1 = Color.rgb(234, 184, 8);
    private static final int COLOR_RANK_2 = Color.rgb(166, 192, 211);
    private static final int COLOR_RANK_3 = Color.rgb(199, 116, 36);
    private static final int COLOR_RANK_OTHER = Color.rgb(60, 60, 60);

    private TextView rankTv;
    private ImageView rankBgIv;
    private DecorHeadImgView headerIv;
    private NameTextView nicknameIv;
    private TextView winTimesTv;
    private TextView winItemDesc;
    private View itemView;

    public NormalRankItem(View view) {
        super(view);
        this.itemView = view;
        rankTv = view.findViewById(R.id.ice_ball_rank_item_rank);
        rankBgIv = view.findViewById(R.id.ice_ball_rank_bg);
        headerIv = view.findViewById(R.id.ice_ball_rank_item_header);
        nicknameIv = view.findViewById(R.id.ice_ball_rank_item_user_name);
        winItemDesc = view.findViewById(R.id.ice_ball_rank_item_win_desc);
        winTimesTv = view.findViewById(R.id.ice_ball_rank_item_win);
    }

    public void refresh(int gameType, int position, final IceBallRankInfo rankInfo) {
        String winStr = "" + rankInfo.getScore();
        String rankStr = "" + (position + 1);

        if (position == 0) {
            rankBgIv.setVisibility(View.VISIBLE);
            rankBgIv.setImageResource(R.drawable.ice_ball_rank_1);
            rankTv.setTextColor(COLOR_RANK_1);
        } else if (position == 1) {
            rankBgIv.setVisibility(View.VISIBLE);
            rankBgIv.setImageResource(R.drawable.ice_ball_rank_2);
            rankTv.setTextColor(COLOR_RANK_2);
        } else if (position == 2) {
            rankBgIv.setVisibility(View.VISIBLE);
            rankBgIv.setImageResource(R.drawable.ice_ball_rank_3);
            rankTv.setTextColor(COLOR_RANK_3);
        } else {
            rankBgIv.setVisibility(View.GONE);
            rankTv.setTextColor(COLOR_RANK_OTHER);
        }
        rankTv.setText(rankStr);
        nicknameIv.setRemarkUser(rankInfo.uid, rankInfo.nickname);
        if (GameType.GAME_TYPE_MJ == gameType) {
            winItemDesc.setText(R.string.common_golds);
            LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) winTimesTv.getLayoutParams();
            lp.width = ScreenUtil.dip2px(100F);
            winTimesTv.setLayoutParams(lp);
            if (StringUtil.isNumber(winStr)) {
                try {
                    long winNum = Long.parseLong(winStr);
                    winTimesTv.setText(StringUtil.formatGuardNumberForK(winNum));
                } catch (NumberFormatException e) {
                    winTimesTv.setText(winStr);
                }
            } else {
                winTimesTv.setText(winStr);
            }
        } else if (GameType.GAME_TYPE_H_AND_B == gameType) {
            winItemDesc.setText(R.string.common_points);
            setWinTimesAndViewLpWidth(winStr, 80F);
        } else {
            winItemDesc.setText(R.string.win_times);
            setWinTimesAndViewLpWidth(winStr, 80F);
        }
        headerIv.showUserHeadWithDecorationCache(rankInfo.getUid());
        itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                JsonObject ext = new JsonObject();
                ext.addProperty("sub_source", TrackSource.LITTLE_GAME_RANK);
                ApiService.of(HwApi.class).gotoUserInfoDetailActivity(
                        v.getContext(), rankInfo.getUid(), TrackUtil.getGameTypeSource(GameType.GAME_TYPE_JACKAROO),
                        ext.toString()
                );

            }
        });
    }

    private void setWinTimesAndViewLpWidth(String winStr, float dpValue) {
        LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) winTimesTv.getLayoutParams();
        lp.width = ScreenUtil.dip2px(dpValue);
        winTimesTv.setLayoutParams(lp);
        winTimesTv.setText(winStr);
    }

}
