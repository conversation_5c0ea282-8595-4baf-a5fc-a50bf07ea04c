package com.wepie.wespy.cocosnew.match.rank.indicate;

import android.content.Context;
import android.graphics.drawable.GradientDrawable;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.wepie.wespy.R;
import com.huiwan.base.util.ScreenUtil;

// Created by bigwen on 2018/10/8.

public class IndicateItem extends FrameLayout {

    private final Context mContext;

    private final GradientDrawable leftDrawable = new GradientDrawable();
    private final GradientDrawable rightDrawable = new GradientDrawable();
    private final GradientDrawable centerDrawable = new GradientDrawable();

    private TextView titleTv;
    private ImageView selectBgView;
    private ImageView selectLineView;

    private int textNormalColor, textSelectColor;

    private ImageView dividerIv;

    public IndicateItem(@NonNull Context context) {
        super(context);
        mContext = context;
        init();
    }

    public IndicateItem(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.indicate_item, this);
        titleTv = findViewById(R.id.rank_text_tv);
        selectBgView = findViewById(R.id.rank_select_bg_iv);
        selectLineView = findViewById(R.id.rank_select_iv);
        dividerIv = findViewById(R.id.divide_iv);

        int CORNER = ScreenUtil.dip2px(22);
        if (ScreenUtil.isRtl()) {
            setCornerRadii(rightDrawable, CORNER, 0, 0, CORNER);
            setCornerRadii(leftDrawable, 0, CORNER, CORNER, 0);
        } else {
            setCornerRadii(leftDrawable, CORNER, 0, 0, CORNER);
            setCornerRadii(rightDrawable, 0, CORNER, CORNER, 0);
        }

        setCornerRadii(centerDrawable, 0, 0, 0, 0);
    }

    public void setResColor(int index, int totalCount, @ColorInt int bgColor, @ColorInt int seleteColor, @ColorInt int lineColor, @ColorInt int unSelectTextColor, String titleText) {
        if (index == 0) {//left
            selectBgView.setImageDrawable(leftDrawable);
        } else if (index == totalCount - 1) {//right
            selectBgView.setImageDrawable(rightDrawable);
        } else {
            selectBgView.setImageDrawable(centerDrawable);
        }
        leftDrawable.setColor(seleteColor);
        rightDrawable.setColor(seleteColor);
        centerDrawable.setColor(seleteColor);
        selectLineView.setBackgroundColor(lineColor);

        textNormalColor = unSelectTextColor;
        textSelectColor = lineColor;
        titleTv.setTextColor(textNormalColor);
        titleTv.setText(titleText);
    }

    public void setTitleSize(float size) {
        titleTv.setTextSize(TypedValue.COMPLEX_UNIT_SP, size);
    }

    private static void setCornerRadii(GradientDrawable drawable, float r0,
                                       float r1, float r2, float r3) {
        drawable.setCornerRadii(new float[]{r0, r0, r1, r1, r2, r2, r3,
                r3});
    }

    public void refreshView(boolean isChoose) {
        if (isChoose) {
            selectBgView.setVisibility(VISIBLE);
            selectLineView.setVisibility(VISIBLE);
            titleTv.setTextColor(textSelectColor);
        } else {
            selectBgView.setVisibility(INVISIBLE);
            selectLineView.setVisibility(INVISIBLE);
            titleTv.setTextColor(textNormalColor);
        }
    }

    public void refreshDivider(boolean show) {
        dividerIv.setVisibility(show ? VISIBLE : GONE);
    }

}