package com.wepie.wespy.cocosnew.match.rank.presenter;

import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil;
import com.huiwan.configservice.ConfigHelper;

// Created by bigwen on 2018/10/8.
public class CocosRankPresenter {

    private int gameType;
    private int playMode;

    public CocosRankPresenter(int gameType) {
        this.gameType = gameType;
    }

    public int getGameType() {
        return gameType;
    }

    public String getGameName() {
        return CocosGameConfigUtil.getCocosGameConfig(getGameType()).getName();
    }

    public String getRankRuleText() {
        return CocosGameConfigUtil.getCocosGameConfig(getGameType()).getRankRule();
    }

    public int getPlayMode() {
        return ConfigHelper.getInstance().getGameConfig().getGameConfig(getGameType()).getPlayMode();
    }
}
