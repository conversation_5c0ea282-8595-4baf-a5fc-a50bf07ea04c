package com.wepie.wespy.cocosnew.match.rank.callback;

import com.wejoy.weplay.ex.ILifeOwner;
import com.wepie.wespy.model.entity.cocos.IceBallRankInfo;
import com.wepie.wespy.model.entity.cocos.OnePersonalRankInfo;
import com.wepie.wespy.model.entity.cocos.TwoPersonalRankInfo;

import java.util.List;

/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/11/11.
 */

public interface ICocosRankingView extends ILifeOwner {

    void updateWeekRankSuccess(List<IceBallRankInfo> rankInfoList);
    void updateTotalRankSuccess(List<IceBallRankInfo> rankInfoList);
    void updateCooperateWeekRankSuccess(List<TwoPersonalRankInfo> rankInfos);
    void updateCooperateTotalRankSuccess(List<TwoPersonalRankInfo> rankInfos);
    void updatePersonalRankSuccess(List<OnePersonalRankInfo> rankInfoList);
}
