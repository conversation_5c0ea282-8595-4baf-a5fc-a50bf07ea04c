package com.wepie.wespy.cocosnew.match.rank.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.cocos.CocosRankInfo;
import com.wepie.wespy.model.entity.cocos.IceBallRankInfo;
import com.wepie.wespy.model.entity.cocos.OnePersonalRankInfo;
import com.wepie.wespy.model.entity.cocos.TwoPersonalRankInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/11/10.
 */

public class RankItemAdapter extends RecyclerView.Adapter {

    private static final int TYPE_NORMAL = 0;
    private static final int TYPE_COOPERATE_TWO_USER = 1;
    private static final int TYPE_COOPERATE_ONE_USER = 2;
    private final List<CocosRankInfo> rankingUsers = new ArrayList<>();
    private final Context context;
    private int gameType;

    public RankItemAdapter(Context context, int gameType) {
        this.context = context;
        this.gameType = gameType;
    }

    @Override
    public void onBindViewHolder(final RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof NormalRankItem) {
            NormalRankItem rankItem = (NormalRankItem) holder;
            rankItem.refresh(gameType, position, (IceBallRankInfo) rankingUsers.get(position));
        } else if (holder instanceof CooperateOneUserRankItem) {
            CooperateOneUserRankItem rankItem = (CooperateOneUserRankItem) holder;
            rankItem.refresh(position, (OnePersonalRankInfo) rankingUsers.get(position));
        } else if (holder instanceof CooperateTwoUserRankItem) {
            CooperateTwoUserRankItem rankItem = (CooperateTwoUserRankItem) holder;
            rankItem.refresh(position, (TwoPersonalRankInfo) rankingUsers.get(position));
        }
    }

    public void refreshNormalUsers(List<IceBallRankInfo> rankingUsers) {
        this.rankingUsers.clear();
        this.rankingUsers.addAll(rankingUsers);
        notifyDataSetChanged();
    }

    public void refreshTwoUsers(List<TwoPersonalRankInfo> rankingUsers) {
        this.rankingUsers.clear();
        this.rankingUsers.addAll(rankingUsers);
        notifyDataSetChanged();
    }

    public void refreshOneUsers(List<OnePersonalRankInfo> rankingUsers) {
        this.rankingUsers.clear();
        this.rankingUsers.addAll(rankingUsers);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@Nullable ViewGroup parent, int viewType) {
        if (viewType == TYPE_NORMAL) {
            return new NormalRankItem(LayoutInflater.from(context).inflate(R.layout.cocos_ice_ball_rank_item, parent, false));
        } else if (viewType == TYPE_COOPERATE_ONE_USER) {
            return new CooperateOneUserRankItem(LayoutInflater.from(context).inflate(R.layout.cocos_cooperate_one_user_rank_item, parent, false));
        } else if (viewType == TYPE_COOPERATE_TWO_USER) {
            return new CooperateTwoUserRankItem(LayoutInflater.from(context).inflate(R.layout.cocos_cooperate_two_user_rank_item, parent, false));
        } else {
            return new NormalRankItem(LayoutInflater.from(context).inflate(R.layout.cocos_cooperate_two_user_rank_item, parent, false));
        }
    }

    @Override
    public int getItemViewType(int position) {
        if (rankingUsers.get(position) instanceof IceBallRankInfo) {
            return TYPE_NORMAL;
        } else if (rankingUsers.get(position) instanceof OnePersonalRankInfo) {
            return TYPE_COOPERATE_ONE_USER;
        } else if (rankingUsers.get(position) instanceof TwoPersonalRankInfo) {
            return TYPE_COOPERATE_TWO_USER;
        }
        return TYPE_NORMAL;
    }

    @Override
    public int getItemCount() {
        return rankingUsers.size();
    }

}
