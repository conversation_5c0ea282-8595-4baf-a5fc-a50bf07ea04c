package com.wepie.wespy.cocosnew.match.rank.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.huiwan.widget.SimplePagerAdapter;
import com.huiwan.base.str.ResUtil;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.rank.adapter.RankItemAdapter;
import com.wepie.wespy.cocosnew.match.rank.callback.CocosRankCallback;
import com.wepie.wespy.cocosnew.match.rank.callback.ICocosRankingView;
import com.wepie.wespy.cocosnew.match.rank.indicate.IndicateInterface;
import com.wepie.wespy.cocosnew.match.rank.presenter.RankViewPresenter;
import com.wepie.wespy.helper.view.SimpleDividerDecoration;
import com.wepie.wespy.model.entity.cocos.IceBallRankInfo;
import com.wepie.wespy.model.entity.cocos.OnePersonalRankInfo;
import com.wepie.wespy.model.entity.cocos.TwoPersonalRankInfo;

import java.util.ArrayList;
import java.util.List;

// Created by bigwen on 2018/10/8.

public class CooperateRankView extends FrameLayout implements ICocosRankingView, IndicateInterface {

    private final Context mContext;
    private final List<View> rankViews = new ArrayList<>();
    private ViewPager2 rankPager;
    private RankItemAdapter weekAdapter;
    private RankItemAdapter totalAdapter;
    private RankItemAdapter cooperateAdapter;
    private CocosRankCallback cocosRankCallback;
    private RankViewPresenter presenter;

    private View weekView;
    private View totalView;
    private View cooperateView;
    private RecyclerView weekRecyclerView;
    private ViewGroup weekRecordEmptyLay;
    private TextView weekRecordEmptyTx;
    private RecyclerView totalRecyclerView;
    private ViewGroup totalRecordEmptyLay;
    private TextView totalRecordEmptyTx;
    private RecyclerView cooperateRecyclerView;
    private ViewGroup cooperateRecordEmptyLay;
    private TextView cooperateRecordEmptyTx;

    public CooperateRankView(@NonNull Context context) {
        super(context);
        mContext = context;
        init();
    }

    public CooperateRankView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        presenter = new RankViewPresenter(this);
        LayoutInflater.from(mContext).inflate(R.layout.normal_rank_view, this);
        rankPager = findViewById(R.id.ice_ban_rank_view_pager);
    }

    public void initDate(int gameType, CocosRankCallback cocosRankCallback) {
        initPagers(gameType);
        initPagerAdapter(rankPager);

        presenter.setGameType(gameType);

        presenter.updateCooperateWeeklyRank();
        presenter.updateCooperateRankTotal();
        presenter.updateGameRankPersonal();
        this.cocosRankCallback = cocosRankCallback;
    }

    @Override
    public void setCurrentItem(int index) {
        if (index >= 0 && index < rankViews.size()) {
            rankPager.setCurrentItem(index);
        }
    }

    private void initPagers(int gameType) {
        weekAdapter = new RankItemAdapter(mContext, gameType);
        totalAdapter = new RankItemAdapter(mContext, gameType);
        cooperateAdapter = new RankItemAdapter(mContext, gameType);
        weekView = LayoutInflater.from(mContext).inflate(R.layout.cocos_ice_ball_rank_time, rankPager, false);
        totalView = LayoutInflater.from(mContext).inflate(R.layout.cocos_ice_ball_rank_time, rankPager, false);
        cooperateView = LayoutInflater.from(mContext).inflate(R.layout.cocos_ice_ball_rank_time, rankPager, false);

        weekRecyclerView = weekView.findViewById(R.id.ice_ball_rank_recycler_view);
        totalRecyclerView = totalView.findViewById(R.id.ice_ball_rank_recycler_view);
        cooperateRecyclerView = cooperateView.findViewById(R.id.ice_ball_rank_recycler_view);
        weekRecordEmptyLay = weekView.findViewById(R.id.record_empty_lay);
        weekRecordEmptyTx = weekView.findViewById(R.id.empty_record_tv);
        totalRecordEmptyLay = totalView.findViewById(R.id.record_empty_lay);
        totalRecordEmptyTx = totalView.findViewById(R.id.empty_record_tv);
        cooperateRecordEmptyLay = cooperateView.findViewById(R.id.record_empty_lay);
        cooperateRecordEmptyTx = cooperateView.findViewById(R.id.empty_record_tv);

        initRankViews(weekView, weekRecyclerView ,weekAdapter);
        initRankViews(totalView, totalRecyclerView ,totalAdapter);
        initRankViews(cooperateView, cooperateRecyclerView, cooperateAdapter);

        rankPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }

            @Override
            public void onPageSelected(int position) {
                if (cocosRankCallback != null) cocosRankCallback.onPagerSelect(position);
                if (position == 0) {
                    if (weekAdapter.getItemCount() == 0) {
                        presenter.updateCooperateWeeklyRank();
                    }
                } else if (position == 1) {
                    if (totalAdapter.getItemCount() == 0) {
                        presenter.updateCooperateRankTotal();
                    }
                } else if (position == 2) {
                    if (cooperateAdapter.getItemCount() == 0) {
                        presenter.updateGameRankPersonal();
                    }
                }
            }
        });
    }


    private void initRankViews(View view, RecyclerView recyclerView, RankItemAdapter adapter) {
        recyclerView.setLayoutManager(new LinearLayoutManager(mContext));
        recyclerView.addItemDecoration(new SimpleDividerDecoration(20, 20));
        recyclerView.setAdapter(adapter);
        rankViews.add(view);
        if (rankPager.getAdapter() != null) {
            rankPager.getAdapter().notifyDataSetChanged();
        }
    }

    private void initPagerAdapter(ViewPager2 viewPager) {
        viewPager.setAdapter(new SimplePagerAdapter<>(rankViews));
    }


    @Override
    public void updateTotalRankSuccess(List<IceBallRankInfo> rankInfoList) {

    }

    @Override
    public void updateWeekRankSuccess(List<IceBallRankInfo> rankInfoList) {

    }

    @Override
    public void updateCooperateWeekRankSuccess(List<TwoPersonalRankInfo> rankInfos) {
        if(rankInfos.size() > 0) {
            weekRecyclerView.setVisibility(VISIBLE);
            weekRecordEmptyLay.setVisibility(GONE);
            weekRecordEmptyTx.setVisibility(GONE);
            weekAdapter.refreshTwoUsers(rankInfos);
        } else {
            weekRecyclerView.setVisibility(GONE);
            weekRecordEmptyLay.setVisibility(VISIBLE);
            weekRecordEmptyTx.setVisibility(VISIBLE);
            weekRecordEmptyTx.setText(ResUtil.getStr(R.string.no_ranking_description));
        }

    }

    @Override
    public void updateCooperateTotalRankSuccess(List<TwoPersonalRankInfo> rankInfos) {
        if(rankInfos.size() > 0) {
            totalRecyclerView.setVisibility(VISIBLE);
            totalRecordEmptyLay.setVisibility(GONE);
            totalRecordEmptyTx.setVisibility(GONE);
            totalAdapter.refreshTwoUsers(rankInfos);
        } else {
            totalRecyclerView.setVisibility(GONE);
            totalRecordEmptyLay.setVisibility(VISIBLE);
            totalRecordEmptyTx.setVisibility(VISIBLE);
            totalRecordEmptyTx.setText(ResUtil.getStr(R.string.no_ranking_description));
        }
    }

    @Override
    public void updatePersonalRankSuccess(List<OnePersonalRankInfo> rankInfoList) {
        if(!rankInfoList.isEmpty()) {
            cooperateRecyclerView.setVisibility(VISIBLE);
            cooperateRecordEmptyLay.setVisibility(GONE);
            cooperateRecordEmptyTx.setVisibility(GONE);
            cooperateAdapter.refreshOneUsers(rankInfoList);
        } else {
            cooperateRecyclerView.setVisibility(GONE);
            cooperateRecordEmptyLay.setVisibility(VISIBLE);
            cooperateRecordEmptyTx.setVisibility(VISIBLE);
            cooperateRecordEmptyTx.setText(ResUtil.getStr(R.string.no_ranking_description));
        }
    }

    @Override
    public ILife getLife() {
        return ILifeUtil.toLife(this);
    }
}