package com.wepie.wespy.cocosnew.match.rank.indicate;

import android.content.Context;
import android.graphics.drawable.GradientDrawable;

import androidx.annotation.ColorInt;

import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.huiwan.base.LibBaseUtil;
import com.wepie.wespy.R;
import com.huiwan.base.util.ScreenUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2017/12/15.
 */
public class GameRankTitleView extends LinearLayout {

    private Context mContext;
    private ImageView rankBgIv;
    private IndicateCallback callback;
    private GradientDrawable bgDrawable = new GradientDrawable();
    private LinearLayout contentLay;
    private List<IndicateItem> itemList = new ArrayList<>();
    private int nowIndex = 0;

    public GameRankTitleView(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public GameRankTitleView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.game_rank_title_view, this);
        rankBgIv = (ImageView) findViewById(R.id.rank_bg_iv);
        contentLay = findViewById(R.id.content_lay);
    }

    public void setData(@ColorInt int bgColor, @ColorInt int selectColor, @ColorInt int lineColor, @ColorInt int unSelectTextColor, String[] titles) {
        if (titles == null) return;
        int CORNER = ScreenUtil.dip2px(22);
        bgDrawable.setCornerRadii(new float[]{CORNER, CORNER, CORNER, CORNER, CORNER, CORNER, CORNER, CORNER});
        bgDrawable.setColor(bgColor);
        rankBgIv.setImageDrawable(bgDrawable);
        for (int i = 0; i < titles.length; i++) {
            IndicateItem indicateItem = new IndicateItem(mContext);
            indicateItem.setResColor(i, titles.length, bgColor, selectColor, lineColor, unSelectTextColor, titles[i]);
            LayoutParams params = new LayoutParams(0, ViewGroup.LayoutParams.MATCH_PARENT);
            params.weight = 1;
            contentLay.addView(indicateItem, params);
            itemList.add(indicateItem);
            final int position = i;
            indicateItem.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    onTouchItem(position);
                }
            });
            if (i == titles.length - 1) {
                indicateItem.refreshDivider(false);
            } else {
                indicateItem.refreshDivider(true);
            }
        }

        selectItem(nowIndex);
    }

    public void selectItem(int index) {
        for (int i = 0; i < itemList.size(); i++) {
            IndicateItem indicateItem = itemList.get(i);
            if (i == index) {
                indicateItem.refreshView(true);
            } else {
                indicateItem.refreshView(false);
            }

            if (i == index || i == index - 1 || i == itemList.size() - 1) {
                indicateItem.refreshDivider(false);
            } else {
                indicateItem.refreshDivider(true);
            }
        }
    }

    private void onTouchItem(int index) {
        nowIndex = index;
        selectItem(index);
        if (callback != null) callback.onClick(index);
    }


    public void setCallback(IndicateCallback callback) {
        this.callback = callback;
    }

}