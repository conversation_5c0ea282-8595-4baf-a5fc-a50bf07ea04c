package com.wepie.wespy.cocosnew.match;

import com.huiwan.media.VolumeUtil;
import com.huiwan.voiceservice.VoiceManager;
import com.wejoy.littlegame.LittleGame;
import com.wepie.lib.api.plugins.voice.VoiceConfig;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.config.UrlConfig;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.module.media.VoiceConfigHelper;

/**
 * Created by bigwen on 2019-12-31.
 * <p>
 * 管理组队的语音频道
 */
public class TeamVoiceManager {
    private static final String TAG = "TeamVoiceManager";

    private static TeamVoiceManager manager;

    public static TeamVoiceManager getInstance() {
        if (manager == null) {
            synchronized (TeamVoiceManager.class) {
                if (manager == null) manager = new TeamVoiceManager();
            }
        }
        return manager;
    }

    public boolean checkOpenAgora() {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        return checkOpenAgora(teamInfo.getTid(), teamInfo.getVoiceType());
    }

    public boolean checkOpenAgora(int tid, int voiceType) {
        if (VoiceManager.getInstance().isAgoraOn()) {
            HLog.d(TAG, HLog.USR, "openVoice {}", tid);
            String channel = VoiceManager.ICE_TEAM + tid;
            if (UrlConfig.isDebug()) {
                channel = VoiceManager.VOICE_DEV + channel;
            }
            VoiceConfig config = VoiceConfig.newBuilder()
                    .setVoiceType(voiceType)
                    .setChannelName(channel)
                    .setOpenMic(VoiceManager.getInstance().isMicOn())
                    .setBroadcaster(true)
                    .setLittleGame(true)
                    .setConfigSelector(new VoiceConfigHelper.ConfigSelector())
                    .build();
            VoiceManager.getInstance().joinChannel(config);
            return true;
        } else {
            VoiceManager.getInstance().leaveChannel();
            return false;
        }
    }

    public boolean clickSpeaker(int tid, int voiceType) {
        return checkOpenAgora(tid, voiceType);
    }

    public boolean onActivityResume(int tid, int voiceType) {
        return checkOpenAgora(tid, voiceType);
    }
}
