package com.wepie.wespy.cocosnew.match.prepare;


import android.content.Context;

import com.huiwan.component.gift.show.GiftShowInfo;
import com.wejoy.weplay.ex.ILife;
import com.wepie.lib.api.plugins.voice.SpeakerInfo;
import com.wepie.wespy.model.entity.match.TeamInfo;

import java.util.List;

/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/11/11.
 *
 */
public interface ICocosMatchPrepareView {

    /**
     * 更新标题
     */
    void updateTitle(String titleText);

    /**
     * 显示邀请双向好友对话框
     * @param tid 队伍 ID
     * @param gameType 模式
     */
    void showInviteFriendDialog(int tid, int gameType);

    /**
     * 显示提示信息
     * @param msg 提示信息
     */
    void showErrMsg(String msg);

    /**
     * 显示 loading 界面
     */
    void showLoading();

    /**
     * 隐藏 loading 界面
     */
    void hideLoading();

    /**
     * 跳转到匹配界面
     */
    void jumpToMatch();

    /**
     * 更新队伍信息
     */
    void updateViews(TeamInfo teamInfo);

    /**
     * 退出组队准备界面
     */
    void leave();

    void showUpdateDialog(int gameTypeValue);

    Context getViewContext();

    /**
     * 收到礼物时播放礼物动画
     * @param showInfo show info
     */
    void showGiftAnim(GiftShowInfo showInfo);

    ILife getLife();

    void onSpeak(List<SpeakerInfo> speakerInfos);
}
