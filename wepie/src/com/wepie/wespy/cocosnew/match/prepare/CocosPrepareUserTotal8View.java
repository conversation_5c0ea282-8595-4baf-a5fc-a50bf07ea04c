package com.wepie.wespy.cocosnew.match.prepare;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.Nullable;

import com.wepie.wespy.R;

/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/11/16.
 */

public class CocosPrepareUserTotal8View extends CocosPrepareUserView {
    public CocosPrepareUserTotal8View(Context context) {
        super(context);
    }

    public CocosPrepareUserTotal8View(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public CocosPrepareUserTotal8View(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.ice_ball_prepare_user_total8_view;
    }
}
