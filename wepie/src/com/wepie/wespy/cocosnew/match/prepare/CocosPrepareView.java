package com.wepie.wespy.cocosnew.match.prepare;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.wepie.lib.api.plugins.voice.SpeakerInfo;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.OnGameUserEventListener;
import com.wepie.wespy.model.entity.match.SeatInfo;

import java.util.List;


/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/11/16.
 */
public class CocosPrepareView extends LinearLayout {
    private final CocosPrepareUserView[] members = new CocosPrepareUserView[2];
    private OnGameUserEventListener listener;

    public CocosPrepareView(Context context) {
        super(context);
        init();
    }

    public CocosPrepareView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public CocosPrepareView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        setClipChildren(false);
        LayoutInflater.from(getContext()).inflate(R.layout.ice_ball_prepare_view, this);
        members[0] = findViewById(R.id.ice_ball_match_pre_user0);
        members[1] = findViewById(R.id.ice_ball_match_pre_user1);
    }

    public void updateSeatInfo(List<SeatInfo> seatInfoList, int owner) {
        for (SeatInfo seatInfo : seatInfoList) {
            if (seatInfo.getSeatNum() < members.length && seatInfo.getSeatNum() >= 0) {
                /*seat num 从 1 开始*/
                members[seatInfo.getSeatNum()].update(seatInfo, owner, false, listener);
            } else {
                HLog.e(TAG, "seat info invalid: " + seatInfo);
            }
        }
    }

    public void updateUserEmpty(Drawable emptyDraw) {
        for (CocosPrepareUserView userView : members) {
            userView.setEmptyImg(emptyDraw);
        }
    }

    public void refreshAnim(List<SpeakerInfo> speakerInfos) {
        for (SpeakerInfo speakerInfo : speakerInfos) {
            for (CocosPrepareUserView view : members) {
                if (speakerInfo.uid == view.getUid()) {
                    view.startAnim();
                }
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        for (CocosPrepareUserView view : members) {
            view.stopAnim();
        }
    }

    public void setListener(OnGameUserEventListener listener) {
        this.listener = listener;
    }

    private final static String TAG = CocosPrepareView.class.getSimpleName();
}
