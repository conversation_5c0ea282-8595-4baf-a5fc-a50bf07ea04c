package com.wepie.wespy.cocosnew.match.prepare;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.CustomCircleImageView;
import com.huiwan.widget.SpeakerAnimView;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.OnGameUserEventListener;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.model.entity.match.SeatInfo;

/**
 * <AUTHOR> Yuu
 * email <EMAIL>
 * date 2017/11/16.
 */

public class CocosPrepareUserView extends LinearLayout {
    private CustomCircleImageView headerIv;
    private TextView nameTv;
    private View masterLay;
    private ImageView exchangeIv;
    private View readyIv;
    private ImageView emptyIv;
    private ImageView kickIv;
    private SpeakerAnimView animView;

    private OnGameUserEventListener listener;
    private int uid;
    private int seatId;

    public CocosPrepareUserView(Context context) {
        super(context);
        init();
    }

    public CocosPrepareUserView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public CocosPrepareUserView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        setClipChildren(false);
        LayoutInflater.from(getContext()).inflate(getLayoutId(), this);
        headerIv = findViewById(R.id.head_iv);
        nameTv = findViewById(R.id.name_tv);
        masterLay = findViewById(R.id.owner_lay);
        readyIv = findViewById(R.id.ready_iv);
        exchangeIv = findViewById(R.id.exchange_iv);
        emptyIv = findViewById(R.id.empty_iv);
        kickIv = findViewById(R.id.kick_iv);
        animView = findViewById(R.id.speaker_anim_view);
        exchangeIv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    listener.onReqChange(seatId);
                }
            }
        });
        kickIv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (listener != null) {
                    listener.onKick(uid);
                }
            }
        });
        post(new Runnable() {
            @Override
            public void run() {
                animView.init((int) headerIv.getX() + headerIv.getWidth() / 2, (int) headerIv.getY() + headerIv.getHeight() / 2, ScreenUtil.dip2px(60) / 2);
            }
        });
        animView.registerStopCallback(new SpeakerAnimView.StopCallback() {
            @Override
            public void onAnimStop(View selfView) {
                animView.setVisibility(GONE);
            }
        });
        setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    listener.onUserClick(uid);
                }
            }
        });
    }

    protected int getLayoutId() {
        return R.layout.ice_ball_prepare_user_view;
    }

    public void startAnim() {
        if (uid <= 0) {
            stopAnim();
            return;
        }
        animView.setVisibility(VISIBLE);
        animView.startAnim();
    }

    public void stopAnim() {
        animView.stopAnim();
    }

    public void initSeatId(int seatId) {
        this.seatId = seatId;
    }

    public void update(final SeatInfo seatInfo, final int ownerUid, final boolean canExchange, OnGameUserEventListener listener) {
        final int uid = seatInfo.getUid();
        if (canExchange && uid != LoginHelper.getLoginUid()) {
            exchangeIv.setVisibility(VISIBLE);
        } else {
            exchangeIv.setVisibility(GONE);
        }
        this.listener = listener;
        if (uid <= 0) {
            leave();
            return;
        }
        final boolean isMaster = uid == ownerUid;
        this.uid = uid;
        if (uid == LoginHelper.getLoginUid()) {//此座位是自己
            kickIv.setVisibility(GONE);
        } else {
            if (ownerUid == LoginHelper.getLoginUid()) {//自己是房主
                kickIv.setVisibility(VISIBLE);
            } else {
                kickIv.setVisibility(GONE);
            }
        }
        if (isMaster) {
            masterLay.setVisibility(VISIBLE);
            readyIv.setVisibility(GONE);
        } else {
            masterLay.setVisibility(GONE);
            if (seatInfo.isReady()) {
                readyIv.setVisibility(View.VISIBLE);
            } else {
                readyIv.setVisibility(GONE);
            }
        }
        UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo userInfo) {
                HeadImageLoader.loadCircleHeadImage(userInfo.headimgurl, headerIv);
                emptyIv.setVisibility(View.GONE);
                nameTv.setText(userInfo.getRemarkName());
            }

            @Override
            public void onUserInfoFailed(String description) {
                HLog.e(TAG, description);
            }
        });

        if (seatInfo.isInGame()) {
            headerIv.setColorFilter(Color.GRAY, PorterDuff.Mode.MULTIPLY);
        } else {
            headerIv.clearColorFilter();
        }
    }

    private void leave() {
        stopAnim();
        headerIv.setImageDrawable(null);
        nameTv.setText("");
        uid = 0;
        masterLay.setVisibility(GONE);
        readyIv.setVisibility(GONE);
        kickIv.setVisibility(GONE);
        emptyIv.setVisibility(VISIBLE);
    }

    public void setEmptyImg(Drawable drawable) {
        emptyIv.setImageDrawable(drawable);
    }

    public int getUid() {
        return uid;
    }

    private static final String TAG = CocosPrepareUserView.class.getSimpleName();
}
