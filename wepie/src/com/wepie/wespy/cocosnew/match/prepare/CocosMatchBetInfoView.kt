package com.wepie.wespy.cocosnew.match.prepare

import android.view.View
import androidx.core.view.isVisible
import com.huiwan.base.util.StringUtil
import com.huiwan.base.util.TextUtil
import com.huiwan.configservice.ConfigHelper
import com.wejoy.littlegame.LittleGame
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.databinding.BetInfoOnMatchLayoutBinding

class CocosMatchBetInfoView(val root: View) {
    private val binding: BetInfoOnMatchLayoutBinding by lazy { BetInfoOnMatchLayoutBinding.bind(root) }

    init {
        initView()
    }

    private fun initView() {
        binding.entryTv.text = StringUtil.formatInteger(getEntryCoin(), "0.0")
        val teamInfo = LittleGame.teamInfo
        if (TextUtil.isEmpty(teamInfo.entryBetTitle)) {
            binding.entryLine.isVisible = false
            return
        }
        binding.entryLine.isVisible = true
        binding.entryBetTitleTv.text = teamInfo.entryBetTitle
        if (!TextUtil.isEmpty(teamInfo.entryBetIcon)) {
            binding.entryBetIcon.isVisible = true
            WpImageLoader.load(teamInfo.entryBetIcon, binding.entryBetIcon)
        }
        binding.entryBetValueTv.text = StringUtil.formatInteger(teamInfo.entryBetValue, "0.0")
    }

    private fun getEntryCoin(): Long {
        val config = ConfigHelper.getInstance().getGameConfig(LittleGame.gameInfo.gameType)
        for (matchInfo in config.matchInfoList) {
            if (matchInfo.gameMode == LittleGame.teamInfo.gameMode && matchInfo.betLevel == LittleGame.teamInfo.betLevel) {
                return matchInfo.coin.toLong()
            }
        }
        return 0L
    }
}