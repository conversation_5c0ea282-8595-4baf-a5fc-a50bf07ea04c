package com.wepie.wespy.cocosnew.match.main.ar285

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.collection.ArrayMap
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import com.huiwan.base.util.ColorUtil
import com.huiwan.base.util.ContextUtil
import com.huiwan.base.util.collection.toMap
import com.huiwan.component.game.chip.ChipApi
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.HwApi
import com.huiwan.user.UserService
import com.wejoy.littlegame.EXCHANGE_TYPE_MAIN
import com.wejoy.littlegame.LittleGameTrackUtil
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R

class LittleGameCurrencyView : LinearLayout {

    private val viewHolderMap: MutableMap<String, ViewHolder> by lazy { ArrayMap() }

    var gameType: Int = 0

    constructor(context: Context?) : super(context)
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    )

    init {
        LayoutInflater.from(context).inflate(R.layout.little_game_coin_view, this)
        setBackgroundColor(Color.parseColor("#F7F8FA"))

        val api = ApiService.of(HwApi::class.java)
        val coinViewHolder = ViewHolder(findViewById(R.id.coin_view)) {
            val map = ArrayMap<String, Any>()
            map["game_type"] = gameType.toString()
            map["scene"] = LittleGameTrackUtil.getSceneString(EXCHANGE_TYPE_MAIN)
            api.showCoinNotEnoughDialog(true, map)
        }
        viewHolderMap[TYPE_COIN] = coinViewHolder
        viewHolderMap[TYPE_CHIP] = ViewHolder(findViewById(R.id.chip_view)) {
            api.showGameChipExchangeDialog(
                gameType, -1, emptyMap()
            )
        }

        val activity = ContextUtil.getFragmentActivityFromContext(context)
        if (activity is LifecycleOwner) {
            UserService.get().selfUser.observe(activity) {
                coinViewHolder.update(it.getCoin().toString())
            }
        }
    }

    fun update(
        currencyList: List<LittleGameViewState.UserCurrencyItem>,
        style: VsCenterLittleGameConfig.PersonCurrencyStyle
    ) {
        val map = currencyList.toMap { it.type }
        val textColor = ColorUtil.getColor(style.txtColor)

        viewHolderMap.entries.forEach { entry ->
            entry.value.update(map[entry.key], textColor)
        }
        setBackgroundColor(ColorUtil.getColor(style.bgColor))
    }

    fun update(type: String, numStr: String) {
        viewHolderMap[type]?.update(numStr)
    }

    inner class ViewHolder(val view: View, val onClickListener: OnClickListener) {

        private val currencyTypeIv = view.findViewById<ImageView>(R.id.currency_type_iv)
        private val currencyNumTv = view.findViewById<TextView>(R.id.currency_num_tv)
        private val currencyPlusIv = view.findViewById<ImageView>(R.id.currency_plus_iv)

        init {
            view.setOnClickListener(onClickListener)
        }

        fun update(data: LittleGameViewState.UserCurrencyItem?, color: Int) {
            if (data == null) {
                view.isVisible = false
                return
            }
            WpImageLoader.load(data.url, currencyTypeIv)
            WpImageLoader.load(data.plusUrl, currencyPlusIv)
            view.isVisible = true
            currencyNumTv.text = if (data.type == TYPE_CHIP) {
                ApiService.of(ChipApi::class.java).formatChip(data.value)
            } else {
                data.value.toString()
            }
            currencyNumTv.setTextColor(color)
        }

        fun update(numStr: String) {
            currencyNumTv.text = numStr
        }
    }

    companion object {
        const val TYPE_COIN = "coin"
        const val TYPE_CHIP = "chip"
    }
}