package com.wepie.wespy.cocosnew.match.main.ar285

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.util.ScreenUtil

class LittleGameEntryItemDecoration : RecyclerView.ItemDecoration() {
    private val topOffset = ScreenUtil.dip2px(12f)
    private val cat2Offset = ScreenUtil.dip2px(6f)

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val lm = parent.layoutManager ?: return
        val adapter = parent.adapter ?: return
        if (lm !is GridLayoutManager) {
            return
        }
        val lookup = lm.spanSizeLookup
        if (adapter !is LittleGameEntryItemAdapter) {
            return
        }
        val position = parent.getChildAdapterPosition(view)
        val spanCount = lm.spanCount
        val spanIndex = lookup.getSpanIndex(position, spanCount) //获取每排的位置
        val spanSize = lookup.getSpanSize(position)

        var allCount = 0
        for (i in 0..position) {
            allCount += lookup.getSpanSize(i)
        }
        //只要在第一行,就不添加margin 所有的span size加起来,只要不超过spanCount,就代表还在第一行
        if (spanCount >= allCount) {
            outRect.top = 0
        } else {
            outRect.top = topOffset
        }
        val isRtl = ScreenUtil.isRtl()
        if (spanSize != spanCount) {
            if (spanIndex == 0) {
                if (isRtl) {
                    outRect.left = cat2Offset
                } else {
                    outRect.right = cat2Offset
                }
            } else {
                if (isRtl) {
                    outRect.right = cat2Offset
                } else {
                    outRect.left = cat2Offset
                }
            }
        }
    }

}