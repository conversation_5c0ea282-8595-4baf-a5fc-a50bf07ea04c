package com.wepie.wespy.cocosnew.match.main.ar285.loader

import com.huiwan.littlegame.cocos.resCheck.ICocosResLoadingWatcher
import com.wepie.liblog.main.HLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

class LoadingStateWatcher(
    private val loading: MutableStateFlow<LittleGameResLoader.Status>
) : ICocosResLoadingWatcher {
    private val tag = "CocosResLoadStateWatcher"

    private val scope = CoroutineScope(Dispatchers.Main)

    private var matchResLoadState: ResLoadSate = ResLoadSate.Loading
    private var inGameResLoadSate: ResLoadSate = ResLoadSate.Loading

    private var inGameResTotalSize = 0L
        set(value) {
            if (field != value) {
                updateLoadState()
            }
            field = value
        }
    private var matchResTotalSize = 0L
        set(value) {
            if (field != value) {
                updateLoadState()
            }
            field = value
        }
    private var inGameResDownloadSize = 0L
        set(value) {
            if (field != value) {
                updateLoadState()
            }
            field = value
        }
    private var matchResDownloadSize = 0L
        set(value) {
            if (field != value) {
                updateLoadState()
            }
            field = value
        }
    private var baseRate: Float = 0f
    private var mockProgressJob: Job? = null

    private fun notifyBecomeTransitionState() {
        if (mockProgressJob?.isActive == true
            && inGameResLoadSate != ResLoadSate.Loading
            && matchResLoadState != ResLoadSate.Loading
        ) {
            return
        }
        startMockPreActionProgress(loading)
    }

    /**
     * 为从从asset加载资源 以及获取 需要下载的大小的过程 模拟一个进度
     */
    private fun startMockPreActionProgress(
        loading: MutableStateFlow<LittleGameResLoader.Status>
    ) {
        log("startMockPreActionProgress")
        loading.value = loading.value.copy(
            curStatus = LittleGameResLoader.CurStatus.LoadingWithoutSize
        )
        val loadingWithoutSizeMaxRate = 0.1f
        mockProgressJob = scope.launch {
            while (loading.value.curStatus == LittleGameResLoader.CurStatus.LoadingWithoutSize) {
                val curRate = loading.value.rate
                if (curRate >= loadingWithoutSizeMaxRate) {
                    break
                }
                loading.value = loading.value.copy(rate = curRate + 0.01f)
                delay(250)
            }
        }
    }

    private fun updateLoadState() {
        trySaveMockProgress(loading.value.curStatus, loading.value.rate)
        updateWhenBothTotalSizeValid(loading)
        updateProgressFullyIfBothSuccess()
    }

    private fun updateProgressFullyIfBothSuccess() {
        if (matchResLoadState == ResLoadSate.Success && inGameResLoadSate == ResLoadSate.Success) {
            loading.value = loading.value.copy(rate = 1f)
            log("state=${loading.value}")
        }
    }

    private fun trySaveMockProgress(
        curState: LittleGameResLoader.CurStatus,
        curRate: Float,
    ) {
        if (curState == LittleGameResLoader.CurStatus.LoadingWithoutSize) {
            baseRate = curRate
        }
    }

    private fun isBothTotalSizeValid(): Boolean {
        val inGameResTotalValid =
            inGameResLoadSate != ResLoadSate.Loading || inGameResTotalSize > 0
        val matchResTotalValid =
            matchResLoadState != ResLoadSate.Loading || matchResTotalSize > 0
        return inGameResTotalValid && matchResTotalValid && isTotalSizeMeaningful()
    }

    private fun isTotalSizeMeaningful(): Boolean {
        return (inGameResTotalSize + matchResTotalSize) > 0
    }

    private fun updateWhenBothTotalSizeValid(loading: MutableStateFlow<LittleGameResLoader.Status>) {
        if (!isBothTotalSizeValid()) {
            return
        }
        val total = inGameResTotalSize + matchResTotalSize
        val curSize = inGameResDownloadSize + matchResDownloadSize
        val newRate = baseRate + (1f - baseRate) * (curSize / total.toFloat())
        loading.value = loading.value.copy(
            rate = newRate,
            cur = curSize,
            gameTotal = inGameResTotalSize,
            matchTotal = matchResTotalSize,
            curStatus = LittleGameResLoader.CurStatus.Loading
        )
    }

    private fun notifyResLoadStateChange() {
        mockProgressJob?.cancel()
        updateLoadState()
    }

    override fun updateTotalInGameSize(size: Long) {
        inGameResTotalSize = size
    }

    override fun updateTotalMatchSize(size: Long) {
        matchResTotalSize = size
    }

    override fun updateDownloadMatchSize(size: Long) {
        matchResDownloadSize = size
    }

    override fun updateDownloadGameRes(size: Long) {
        inGameResDownloadSize = size
    }

    override fun notifyLoadMatchResSuccess() {
        log(" notifyLoadMatchResSuccess ")
        matchResLoadState = ResLoadSate.Success
        notifyResLoadStateChange()
    }

    override fun notifyLoadMatchResFail() {
        log(" notifyLoadMatchResFail ")
        matchResLoadState = ResLoadSate.Failure
        notifyResLoadStateChange()
    }

    override fun notifyLoadInGameResResSuccess() {
        log(" notifyLoadInGameResResSuccess ")
        inGameResLoadSate = ResLoadSate.Success
        notifyResLoadStateChange()
    }

    override fun notifyLoadInGameResFail() {
        log(" notifyLoadInGameResFail ")
        inGameResLoadSate = ResLoadSate.Failure
        notifyResLoadStateChange()
    }

    override fun initMatchResIsNeedLoad(isNeedLoad: Boolean) {
        log(" initMatchResIsNeedLoad :$isNeedLoad")
        matchResLoadState = if (isNeedLoad) ResLoadSate.Loading else ResLoadSate.NoNeedLoad
        notifyBecomeTransitionState()
    }

    override fun initGameResIsNeedLoad(isNeedLoad: Boolean) {
        log(" initGameResIsNeedLoad :$isNeedLoad")
        inGameResLoadSate = if (isNeedLoad) ResLoadSate.Loading else ResLoadSate.NoNeedLoad
        notifyBecomeTransitionState()
    }

    private fun clear() {
        scope.cancel()
    }

    override fun cancel() {
        log("cancel")
        clear()
    }

    private fun log(msg: String) {
        HLog.d(tag, HLog.USR, msg)
    }

    enum class ResLoadSate {
        NoNeedLoad, Loading, Success, Failure
    }
}
