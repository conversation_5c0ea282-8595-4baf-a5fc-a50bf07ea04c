package com.wepie.wespy.cocosnew.match.main.ar285

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.context.holder.ContextHolder
import com.huiwan.base.util.ColorUtil
import com.huiwan.base.util.ContextUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.StatusBarUtil
import com.huiwan.base.util.ViewUtil
import com.huiwan.component.game.chip.ChipApi
import com.huiwan.component.game.chip.GameLotteryViewModel
import com.huiwan.decorate.DecorHeadImgView
import com.huiwan.lib.api.ApiService
import com.huiwan.littlegame.cocos.CocosLaunchInfo
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.huiwan.user.entity.CocosUserWeeklyInfo
import com.huiwan.widget.SimpleOutlineProvider
import com.huiwan.widget.decoration.FadingRvDecoration
import com.huiwan.widget.inflate
import com.wejoy.littlegame.GAME_SCENE_LITTLE_GAME_MATCH
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.share.SpaceItemDecoration
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.cocosnew.match.main.CocosMainActivity
import com.wepie.wespy.cocosnew.match.main.ILittleGameMainView
import com.wepie.wespy.cocosnew.match.main.ar285.bean.LittleGameStartConfig
import com.wepie.wespy.cocosnew.match.matching.CocosMatchUtil
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil
import com.wepie.wespy.helper.dialog.progress.IProgressDialog
import com.wepie.wespy.module.common.jump.JumpUtil
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 阿语服 2.8.5
 * https://modao.cc/app/71afgGtHrlwe7tdYIh8uNl#screen=slawbxa2gm8z4xo
 * https://www.figma.com/file/v9QSrGMfg4sMUReGGpSiLJ/%E5%B0%8F%E6%B8%B8%E6%88%8F%E9%A6%96%E9%A1%B5%E8%A7%84%E8%8C%83?node-id=17%3A2823&t=f4fk933fJzZS6R6Z-0
 */
class LittleGameAr285View(context: Context, attrs: AttributeSet?) :
    ConstraintLayout(context, attrs),
    ILittleGameMainView, FlowCollector<LittleGameEvents> {

    private val activity: FragmentActivity

    private val eventHandler = LittleGameEventHandler()
    private val userInfoAdapter = LittleGameUserInfoItemAdapter()
    private val entryAdapter = LittleGameEntryItemAdapter(null, eventHandler)
    private val bottomStartAdapter = LittleGameFuncItemAdapter(eventHandler)
    private val bottomEndAdapter = LittleGameFuncItemAdapter(eventHandler)

    private val vm: LittleGameMainViewModel
    private val recommendVM: LittleGameRecommendModel
    private val actionBar: View
    private val header: DecorHeadImgView
    private val matchBgIv: ImageView
    private val bgIv: ImageView
    private val backIv: ImageView
    private val helpIv: ImageView
    private val titleTv: TextView
    private val userInfoDetailRv: RecyclerView
    private val userInfoLay: ViewGroup
    private val userCurrencyView: LittleGameCurrencyView
    private val gameEntryRv: RecyclerView
    private val gameEntryBottomMask =
        FadingRvDecoration(ScreenUtil.dip2px(54f), FadingRvDecoration.POS_BOTTOM)
    private val bottomLay: ViewGroup
    private val bottomStartRv: RecyclerView
    private val bottomEndRv: RecyclerView

    private val gameCoinVM: GameLotteryViewModel

    init {
        StatusBarUtil.setStatusFontWhiteColor(context)
        activity = ContextUtil.getFragmentActivityFromContext(context)!!
        val owner = activity as ViewModelStoreOwner
        vm = ViewModelProvider(owner)[LittleGameMainViewModel::class.java]
        gameCoinVM = ViewModelProvider(owner)[GameLotteryViewModel::class.java]
        recommendVM = ViewModelProvider(owner)[LittleGameRecommendModel::class.java]
        inflate(R.layout.little_game_main_view, attach = true)
        actionBar = findViewById(R.id.action_bar)
        matchBgIv = findViewById(R.id.match_bg_iv)
        bgIv = findViewById(R.id.main_bg_iv)
        header = findViewById(R.id.head_iv)
        backIv = findViewById(R.id.back_iv)
        helpIv = findViewById(R.id.help_iv)
        titleTv = findViewById(R.id.title_tv)
        userInfoLay = findViewById(R.id.user_info_lay)
        userCurrencyView = findViewById(R.id.user_currency_view)
        userInfoDetailRv = findViewById(R.id.user_info_rv)
        gameEntryRv = findViewById(R.id.rv)
        bottomLay = findViewById(R.id.bottom_ctrl_lay)
        bottomStartRv = findViewById(R.id.bottom_start_rv)
        bottomEndRv = findViewById(R.id.bottom_end_rv)
        ViewUtil.setTopMargins(actionBar, ScreenUtil.getStatusBarHeight())
        backIv.setOnClickListener { ContextUtil.finishActivity(this) }
        header.showUserHeadWithDecoration(LoginHelper.getLoginUid())
        initViews()
        collectData()
    }

    private fun initViews() {
        userInfoDetailRv.adapter = userInfoAdapter
        userInfoDetailRv.addItemDecoration(LittleGameUserInfoItemDecor())
        gameEntryRv.layoutManager = GridLayoutManager(context, 2)
        gameEntryRv.adapter = entryAdapter
        gameEntryRv.addItemDecoration(LittleGameEntryItemDecoration())
        gameEntryRv.addItemDecoration(gameEntryBottomMask)
        gameEntryRv.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                checkUpdateCenterBottomMask()
            }
        })
        bottomStartRv.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        bottomStartRv.adapter = bottomStartAdapter
        val dp4 = ScreenUtil.dip2px(4f)
        bottomStartRv.addItemDecoration(SpaceItemDecoration(0, dp4, 0, dp4))
        bottomEndRv.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, true)
        bottomEndRv.adapter = bottomEndAdapter
        bottomEndRv.addItemDecoration(SpaceItemDecoration(0, dp4, 0, dp4))
        bottomStartAdapter.recommendVM = recommendVM
        bottomEndAdapter.recommendVM = recommendVM
    }

    private fun checkUpdateCenterBottomMask() {
        gameEntryBottomMask.setEnable(gameEntryRv.canScrollVertically(1))
        gameEntryRv.postInvalidate()
    }


    override suspend fun emit(value: LittleGameEvents) {
        when (value) {
            LittleGameEvents.JumpPrepareMatch -> JumpUtil.gotoIceBallMatchPrepareActivity(
                context, vm.gameType, true,
                ""
            )

            LittleGameEvents.JumpMatch -> JumpUtil.gotoIceBallMatchActivity(
                context, vm.gameType, true
            )

            LittleGameEvents.JumpCreate -> JumpUtil.gotoIceBallCreateRoomActivity(
                context, vm.gameType, true, false, ""
            )

            is LittleGameEvents.Error -> {
                CocosMatchUtil.handleFailMatch(
                    context, GAME_SCENE_LITTLE_GAME_MATCH, vm.gameType, value.info, value.head,
                    SCENE_LITTLE_GAME_MAIN
                ) { retry ->
                    value.next?.invoke(retry)
                }
            }

            is LittleGameEvents.JumpBeforeStart -> {
                val startConfig = LittleGameStartConfig(
                    value.beforeStart.roomInfo, vm.gameType, value.beforeStart.beforeStartData,
                    trackScene = SCENE_LITTLE_GAME_MAIN
                )
                ContextHolder.of(context, CocosMainActivity::class.java).playedGame = true
                JumpUtil.jumpLittleGame(context, startConfig)
            }

            else -> {}
        }
    }


    private fun collectData() {
        activity.lifecycleScope.launch {
            vm.stateFlow.collectLatest { update(it) }
        }
        activity.lifecycleScope.launch {
            vm.eventFlow.collect(this@LittleGameAr285View)
        }
        activity.lifecycleScope.launch {
            val api = ApiService.of(ChipApi::class.java)
            api.gameChipFlow()?.collectLatest { chipNum ->
                userCurrencyView.update(LittleGameCurrencyView.TYPE_CHIP, api.formatChip(chipNum))
                recommendVM.updateRecommendMatchItem()
            }
        }
        UserService.get().selfUser.observe(activity) {
            recommendVM.updateRecommendMatchItem()
        }
        activity.lifecycleScope.launch {
            recommendVM.matchItemListLiveData.observe(activity) { dataList ->
                if (dataList.isNotEmpty()) {
                    entryAdapter.refresh(dataList)
                }
            }
        }
    }

    /**
     * 根据数据变化更新界面
     */
    private fun update(state: LittleGameViewState) {
        titleTv.text = state.title
        updateBg(state.bg)
        updateUserInfoItem(state.userInfo)
        updateGamesItem(state.centerInfo)
        updateFuncItem(state.start, state.end, state.topEnd)
    }

    private fun updateBg(bg: String) {
        if (bg.isNotEmpty()) {
            WpImageLoader.loadMatchParentWidthBg(bg, bgIv)
        }
    }

    private fun updateUserInfoItem(userInfo: LittleGameViewState.UserInfo) {
        if (userInfo.list.isNotEmpty()) {
            userInfoDetailRv.layoutManager = GridLayoutManager(context, userInfo.list.size)
        }
        val bgColor = ColorUtil.getColor(userInfo.style.bgColor)
        val keyColor = ColorUtil.getColor(userInfo.style.keyColor)
        val valueColor = ColorUtil.getColor(userInfo.style.valueColor)
        userInfoLay.setBackgroundColor(bgColor)
        userInfoLay.clipToOutline = true
        userInfoLay.outlineProvider = SimpleOutlineProvider(ScreenUtil.dip2px(12f).toFloat())
        userInfoAdapter.refresh(userInfo.list, keyColor, valueColor)

        userCurrencyView.update(userInfo.currencyList, userInfo.currencyStyle)
    }

    private fun updateGamesItem(center: LittleGameViewState.PageCenterInfo) {
        entryAdapter.refresh(center.list)
        val lm = gameEntryRv.layoutManager as GridLayoutManager
        lm.spanSizeLookup = object : SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return if (entryAdapter.dataList[position].catTwo) 1 else 2
            }
        }
        recommendVM.updateRecommendMatchItem(center.list)
    }

    private fun updateFuncItem(
        start: LittleGameViewState.OpInfo,
        end: LittleGameViewState.OpInfo,
        topEnd: LittleGameViewState.OpInfo
    ) {
        bottomStartAdapter.refresh(start.list)
        bottomEndAdapter.refresh(end.list)
        if (topEnd.list.isNotEmpty()) {
            topEnd.list[0].let { gameItemInfo ->
                helpIv.visibility = View.VISIBLE
                WpImageLoader.load(gameItemInfo.iconUrl, helpIv)
                helpIv.setOnClickListener {
                    eventHandler.onClick(it, gameItemInfo.jumpType, gameItemInfo.jumpDetails)
                    funcClickTrack(
                        TrackScreenName.GAME_HOME_PAGE, vm.gameType, gameItemInfo.reportComment,
                        gameItemInfo.jumpDetails.matchItem.gameMode
                    )
                }
            }
        }
    }

    override fun setGameType(gameType: Int) {
        eventHandler.gameType = gameType
        vm.gameType = gameType
        userCurrencyView.gameType = gameType
        recommendVM.gameType = gameType
    }

    override fun refreshTitle(gameName: String?) {
        titleTv.text = gameName
    }

    override fun showPersonRank(cocosUserWeeklyInfo: CocosUserWeeklyInfo?) = Unit

    override fun showLoading() {
        ContextHolder.of(this, IProgressDialog::class.java).showProgressDialogDelay()
    }

    override fun hideLoading() {
        ContextHolder.of(this, IProgressDialog::class.java).hideProgressDialog()
    }

    override fun getViewContext(): Context {
        return context
    }

    override fun refreshRes(gameType: Int) = Unit

    override fun refreshScore(mode: Int) = Unit

    override fun onResume() {
        recommendVM.updateRecommendMatchItem()
    }

    override fun onPause() {
        super.onPause()
        // 提前加载一下匹配页面的背景图，防止匹配页面闪一下
        CocosGameConfigUtil.loadResAsync(
            matchBgIv, vm.gameType,
            CocosGameConfigUtil.MATCH_BACKGROUND
        )
    }

    override fun onDestroy() = Unit

    override fun getView(): View {
        return this
    }
}