package com.wepie.wespy.cocosnew.match.main.ar285.loader

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.graphics.Color
import android.os.Bundle
import android.os.SystemClock
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.StringUtil
import com.huiwan.widget.RoundRectProgressView
import com.wepie.lib.api.plugins.track.config.os.TrackString.SCENE_DOWNLOAD_CLOSE_DIALOG
import com.wepie.lib.api.plugins.track.config.os.TrackString.SCENE_DOWNLOAD_FAIL
import com.wepie.lib.api.plugins.track.config.os.TrackString.SCENE_DOWNLOAD_FINISH
import com.wepie.lib.api.plugins.track.config.os.TrackString.SCENE_UNPACK_FAIL
import com.wepie.libimageloader.WpImageLoader
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.cocosnew.match.main.ar285.LOAD_TYPE_NOT_PRE_LOAD
import com.wepie.wespy.cocosnew.match.main.ar285.bean.LittleGameStartConfig
import com.wepie.wespy.cocosnew.match.main.ar285.resDownloadStatusTrack
import com.wepie.wespy.cocosnew.match.main.ar285.showResLoadingDialogViewTrack
import com.wepie.wespy.helper.dialog.BaseDialogFragment
import com.wepie.wespy.helper.imageLoader.HeadImageLoader
import kotlinx.coroutines.flow.collectLatest
import java.lang.Float.max

class LittleGameResLoadDialog : BaseDialogFragment() {
    /**
     * 弹窗黑色背景透明度
     */
    private val dimAmount: Float = 0.8f

    private lateinit var progressView: RoundRectProgressView
    private lateinit var nameTv: TextView
    private lateinit var loadingTv: TextView
    private lateinit var iconIv: ImageView
    private lateinit var closeIv: View

    private var loader: LittleGameResLoader? = null
    private var startConfig: LittleGameStartConfig? = null
    private var closed = false

    private var curSize = 0L
    private var totalSize = 0L

    /**
     * 当前改动为所有加载行为实际上都会在弹窗内触发
     * 这里目前把不需要加载资源的情况改为弹窗内容透明度为 0
     * 当有资源需要加载时，透明度改为 1 再展示.
     */
    private var viewShowing = false
    private var startTime: Long = 0

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.little_game_res_load_dialog_view, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        view.alpha = 0f
        startTime = SystemClock.elapsedRealtime()
        progressView = view.findViewById(R.id.progress_view)
        nameTv = view.findViewById(R.id.name_tv)
        loadingTv = view.findViewById(R.id.loading_tv)
        iconIv = view.findViewById(R.id.icon_iv)
        closeIv = view.findViewById(R.id.close_iv)
        val loader = this.loader
        if (loader == null) {
            dismissAllowingStateLoss()
            return
        }
        progressView.radiusPx = ScreenUtil.dip2px(16f).toFloat()
        progressView.backColor = Color.parseColor("#EEEFF1")
        progressView.foreColor = ResUtil.getColor(R.color.color_accent)
        closeIv.setOnClickListener {
            closed = true
            dismissAllowingStateLoss()
        }
        lifecycleScope.launchWhenCreated {
            loader.gameState.collect {
                if (it.iconUrl.isNotEmpty()) {
                    WpImageLoader.load(
                        it.iconUrl, iconIv,
                        HeadImageLoader.genHeadLoadInfo().setCornerPixel(ScreenUtil.dip2px(12f))
                    )
                }
                nameTv.text = it.name
            }
        }
        lifecycleScope.launchWhenCreated {
            loader.loadingState.collectLatest {
                progressView.progress = max(0.01f, it.rate)
                curSize = it.cur
                totalSize = it.gameTotal + it.matchTotal
                val s = if (it.gameTotal < 0 || it.matchTotal < 0) {
                    ""
                } else {
                    getSizeDesc(curSize) + "/" + getSizeDesc(totalSize)
                }
                loadingTv.text = ResUtil.getStr(R.string.loading_s, s)
                when (it.curStatus) {
                    LittleGameResLoader.CurStatus.LoadingWithoutSize -> {
                        loadingTv.text = ResUtil.getStr(R.string.loading)
                        if (!viewShowing) {
                            HLog.d(TAG, HLog.USR, "show loading dialog without size")
                            view.animate().alpha(1f).setDuration(100).start()
                            viewShowing = true
                            dialog?.window?.setDimAmount(dimAmount)
                        }
                    }

                    LittleGameResLoader.CurStatus.Loading -> {
                        if (!viewShowing) {
                            HLog.d(TAG, HLog.USR, "show loading dialog")
                            view.animate().alpha(1f).setDuration(100).start()
                            viewShowing = true
                            dialog?.window?.setDimAmount(dimAmount)
                        }
                    }

                    LittleGameResLoader.CurStatus.Success,
                    LittleGameResLoader.CurStatus.AlreadyMeetVersion,
                    LittleGameResLoader.CurStatus.Failed,
                    LittleGameResLoader.CurStatus.UnpackFailed -> {
                        dismissAllowingStateLoss()
                    }

                    else -> {}
                }
            }
        }
        lifecycleScope.launchWhenCreated {
            loader.load()
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        if (viewShowing) {
            showDialogViewTrack(totalSize)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        downloadStatusTrackOnFinal()
    }

    private fun downloadStatusTrackOnFinal() {
        val curLoadState = loader?.loadingState?.value ?: return
        if (curLoadState.curStatus == LittleGameResLoader.CurStatus.AlreadyMeetVersion) {
            return
        }
        val scene = when (curLoadState.curStatus) {
            LittleGameResLoader.CurStatus.Success -> SCENE_DOWNLOAD_FINISH
            LittleGameResLoader.CurStatus.Failed -> SCENE_DOWNLOAD_FAIL
            LittleGameResLoader.CurStatus.UnpackFailed -> SCENE_UNPACK_FAIL
            else -> SCENE_DOWNLOAD_CLOSE_DIALOG
        }
        if (scene == SCENE_DOWNLOAD_FINISH) {
            downloadStatusTrack(0, scene)
        } else {
            downloadStatusTrack(totalSize - curSize, scene)
        }

    }

    private fun getSizeDesc(size: Long): String {
        if (size < 1024) {
            return size.toString()
        }
        if (size < 1024 * 1024) {
            return StringUtil.format("%.1fK", size / 1024f)
        }
        return StringUtil.format("%.1fM", size / (1024 * 1024f))
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.setDimAmount(0f)
        dialog.setCanceledOnTouchOutside(false)
        dialog.setOnKeyListener { _, keyCode, _ -> keyCode == KeyEvent.KEYCODE_BACK }
        return dialog
    }

    fun showDialog(
        context: Context,
        loader: LittleGameResLoader,
        startConfig: LittleGameStartConfig
    ) {
        this.loader = loader
        this.startConfig = startConfig
        show(context)
    }

    private fun showDialogViewTrack(totalSize: Long) {
        HLog.d(TAG, HLog.USR, "showDialogViewTrack")
        startConfig?.let {
            HLog.d(TAG, HLog.USR, "gameType = {}, totalSize = {}", it.gameType, totalSize)
            showResLoadingDialogViewTrack(it.gameType, totalSize, it.trackScene)
        }
    }

    private fun downloadStatusTrack(leftSize: Long, scene: String) {
        HLog.d(TAG, HLog.USR, "downloadStatusTrack")
        startConfig?.let {
            HLog.d(TAG, HLog.USR, "gameType = {}, leftSize = {}", it.gameType, leftSize)
            var durInSec = 0L
            if (startTime > 0) {
                durInSec = (SystemClock.elapsedRealtime() - startTime) / 1000
            }
            resDownloadStatusTrack(
                it.gameType,
                scene,
                totalSize,
                leftSize,
                durInSec,
                LOAD_TYPE_NOT_PRE_LOAD
            )
        }
    }

    companion object {
        private const val TAG = "LittleGameResLoadDialog"
    }
}