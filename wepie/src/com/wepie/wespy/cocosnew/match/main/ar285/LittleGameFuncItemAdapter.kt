package com.wepie.wespy.cocosnew.match.main.ar285

import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig
import com.huiwan.widget.image.DrawableUtil
import com.huiwan.widget.inflate
import com.huiwan.widget.rv.BaseRvAdapter
import com.huiwan.widget.rv.RVHolder
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R

class LittleGameFuncItemAdapter(val eventHandler: LittleGameEventHandler) :
    BaseRvAdapter<LittleGameViewState.GameItemInfo, LittleGameFuncItemAdapter.Holder>() {

    var recommendVM: LittleGameRecommendModel? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
        val v = parent.inflate(R.layout.little_game_main_view_func_item)
        return Holder(v)
    }

    override fun onBindViewHolder(holder: Holder, position: Int) {
        var item = dataList[position]
        val d = DrawableUtil.genColorRadius(Color.LTGRAY, 16)
        WpImageLoader.load(item.iconUrl, holder.iconIv, ImageLoadInfo().error(d).placeholder(d))
        holder.nameTv.text = item.name
        holder.itemView.setOnClickListener {
            if (item.jumpType == VsCenterLittleGameConfig.JUMP_TYPE_QUICK_START) {
                getRecommendItem()?.let { itemInfo ->
                    item = itemInfo
                }
            }
            eventHandler.onClick(it, item.jumpType, item.jumpDetails)
            funcClickTrack(TrackScreenName.GAME_HOME_PAGE, eventHandler.gameType, item.reportComment, item.jumpDetails.matchItem.gameMode)
        }
    }

    private fun getRecommendItem(): LittleGameViewState.GameItemInfo? {
        val itemList = recommendVM?.matchItemListLiveData?.value ?: return null
        if (itemList.isEmpty()) {
            return null
        }
        for (item in itemList) {
            if (item.isRecommend) {
                return item
            }
        }
        return itemList[0]
    }

    class Holder(v: View) : RVHolder(v) {
        val iconIv: ImageView = v.findViewById(R.id.icon_iv)
        val nameTv: TextView = v.findViewById(R.id.name_tv)
    }
}