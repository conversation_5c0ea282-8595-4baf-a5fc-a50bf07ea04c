package com.wepie.wespy.cocosnew.match.main.ar285.loader

import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import androidx.work.Constraints
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequest
import androidx.work.WorkInfo
import androidx.work.WorkManager
import com.huiwan.base.LibBaseUtil
import com.huiwan.configservice.editionentity.GamesConfig
import com.huiwan.configservice.editionentity.liveData
import com.wepie.liblog.main.HLog
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import java.util.Queue
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.LinkedBlockingDeque


object CocosGameSilentPreloader {
    private val pendingPreLoadTasks: Queue<Int> = LinkedBlockingDeque()
    private val workInfoLiveDataMap: MutableMap<String, LiveData<List<WorkInfo?>>> =
        ConcurrentHashMap()
    private val workManager: WorkManager by lazy { WorkManager.getInstance(LibBaseUtil.getApplication()) }
    private val observer: Observer<GamesConfig> = object : Observer<GamesConfig> {
        override fun onChanged(value: GamesConfig) {
            while (pendingPreLoadTasks.isNotEmpty()) {
                pendingPreLoadTasks.poll()?.let {
                    log("real start preLoad:$it")
                    createAndStartPreLoadTask(it)
                }
            }
        }
    }

    private fun createAndStartPreLoadTask(gameType: Int) {
        val workRequest = OneTimeWorkRequest.Builder(PreLoadWork::class.java)
            .setInputData(Data(mapOf(PreLoadWork.WORK_PARAM_GAME_TYPE to gameType)))
            .addTag(PreLoadWork.WORK_TAG)
            .setConstraints(
                Constraints.Builder().setRequiredNetworkType(NetworkType.CONNECTED)
                    .build()
            )
            .build()
        log("try enqueue load work ,game type:$gameType")
        val uniqueWorkName = genWorkName(gameType)
        tryAddWorkObserveOnlyOnce(uniqueWorkName)
        workManager.enqueueUniqueWork(
            uniqueWorkName,
            ExistingWorkPolicy.KEEP,
            workRequest
        )
    }

    private fun tryAddWorkObserveOnlyOnce(uniqueWorkName: String) {
        val targetLiveData =
            workInfoLiveDataMap[uniqueWorkName]
                ?: workManager.getWorkInfosForUniqueWorkLiveData(uniqueWorkName)
        if (targetLiveData.hasObservers()) {
            log("hasObservers this observe")
            return
        }
        workInfoLiveDataMap[uniqueWorkName] = targetLiveData
        MainScope().launch {
            targetLiveData.observeForever(UniqueWorkStateObserver(targetLiveData) {
                workInfoLiveDataMap.remove(uniqueWorkName)
                removeObserveIfPendingTaskIsEmpty()
            })
        }
    }


    fun preLoad(vararg gameTypes: Int) {
        gameTypes.forEach { gameType ->
            if (pendingPreLoadTasks.contains(gameType)) {
                log("exist $gameType preload task ignore")
                return@forEach
            }
            pendingPreLoadTasks.offer(gameType)
        }
        tryAddGameConfigObserve()
    }

    private fun tryAddGameConfigObserve() {
        MainScope().launch {
            if (pendingPreLoadTasks.isNotEmpty()) {
                log("add observe")
                GamesConfig::class.liveData().observeForever(observer)
            }
        }
    }

    private fun removeObserveIfPendingTaskIsEmpty() {
        MainScope().launch {
            if (pendingPreLoadTasks.isEmpty()) {
                log("remove observe")
                GamesConfig::class.liveData().removeObserver(observer)
            }
        }
    }

    private fun genWorkName(gameType: Int): String = "preLoad_cocos_res_$gameType"

    fun cancel(gameType: Int) {
        pendingPreLoadTasks.remove(gameType)
        log("try cancel $gameType preload")
        workManager.cancelUniqueWork(genWorkName(gameType))
        removeObserveIfPendingTaskIsEmpty()
    }

    fun cancelAl() {
        workManager.cancelAllWorkByTag(PreLoadWork.WORK_TAG)
        pendingPreLoadTasks.clear()
        removeObserveIfPendingTaskIsEmpty()
    }

    private fun log(msg: String) {
        HLog.d("CocosGameSilentPreloader", HLog.USR, msg)
    }
}