package com.wepie.wespy.cocosnew.match.main.ar285

import com.huiwan.configservice.editionentity.ILittleGameMatchInfo
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.wepie.wespy.model.entity.iceball.IceGameBeforeStart


/**
 * 游戏操作 Event
 */
sealed class LittleGameEvents {

    /**
     * 跳转单人匹配
     */
    object JumpMatch:LittleGameEvents()

    /**
     * 跳转组队匹配
     */
    object JumpPrepareMatch:LittleGameEvents()

    /**
     * 跳转创建房间
     */
    object JumpCreate:LittleGameEvents()

    /**
     * 游戏匹配或创建成功后跳转到游戏界面
     */
    class JumpBeforeStart(val beforeStart: IceGameBeforeStart, val unity:Boolean, val unityScene:Int):LittleGameEvents()

    /**
     * 操作出错
     */
    class Error(
        val head: RspHeadInfo,
        val info: ILittleGameMatchInfo,
        val next: ((retry: <PERSON><PERSON><PERSON>) -> Unit)?
    ) : LittleGameEvents()
}