package com.wepie.wespy.cocosnew.match.main.ar285

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.util.ColorUtil
import com.huiwan.base.util.ScreenUtil
import kotlin.math.roundToInt

class LittleGameUserInfoItemDecor : RecyclerView.ItemDecoration() {
    private val mBounds = Rect()
    private val mPaint = Paint()

    init {
        mPaint.style = Paint.Style.FILL
        mPaint.color = ColorUtil.getColor("#33BCBCBC")
    }

    override fun onDrawOver(canvas: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        val lm = parent.layoutManager ?: return
        if (parent.childCount < 2) {
            return
        }
        val lineLen = ScreenUtil.dip2px(24f)
        canvas.save()
        val top: Int = (parent.height - lineLen) / 2
        val bottom: Int = top + lineLen

        val childCount = parent.childCount - 1
        for (i in 0 until childCount) {
            val child = parent.getChildAt(i)
            lm.getDecoratedBoundsWithMargins(child, mBounds)
            var start = 0
            var end = 0
            if (ScreenUtil.isRtl()) {
                end = mBounds.left + child.translationX.roundToInt()
                start = end + ScreenUtil.dip2px(0.5f)
            } else {
                end = mBounds.right + child.translationX.roundToInt()
                start = end - ScreenUtil.dip2px(0.5f)
            }
            canvas.drawRect(
                start.toFloat(),
                top.toFloat(),
                end.toFloat(),
                bottom.toFloat(),
                mPaint
            )
        }
        canvas.restore()
    }
}