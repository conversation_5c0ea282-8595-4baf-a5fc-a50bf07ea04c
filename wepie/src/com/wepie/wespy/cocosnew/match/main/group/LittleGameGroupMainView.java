package com.wepie.wespy.cocosnew.match.main.group;

import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.entity.CocosUserWeeklyInfo;
import com.huiwan.widget.CustomCircleImageView;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.main.CocosMainPresenter;
import com.wepie.wespy.cocosnew.match.main.ILittleGameMainView;
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;

/**
 * Created by bigwen on 2019-12-24.
 */
public class LittleGameGroupMainView extends FrameLayout implements ILittleGameMainView {

    private final Context mContext;
    private static final int RANK_MARK = 100;
    private final ProgressDialogUtil dialogUtil = new ProgressDialogUtil();
    private CocosMainPresenter presenter;
    private TextView titleTv;
    private ImageView mainBgIv;
    private ImageView mainFrontIv;
    private TextView scoreTitleTv;
    private ImageView rankIv;
    private ImageView backIv;
    private ImageView ruleIv;
    private TextView weekTv;
    private TextView rankTv;
    private ImageView rankStarIv;
    private TextView starNumTv;
    private RecyclerView recycleView;
    private CocosGroupMatchAdapter adapter;

    public LittleGameGroupMainView(Context context, int gameType) {
        super(context);
        mContext = context;
        init(gameType);
    }

    private void init(int gameType) {
        LayoutInflater.from(mContext).inflate(R.layout.cocos_list_main_view, this);
        presenter = new CocosMainPresenter(this, gameType);
        initView();
        presenter.registerEventBus();
        presenter.initData();
    }

    private void initView() {
        backIv = findViewById(R.id.ice_ball_main_back_img);
        ruleIv = findViewById(R.id.ice_ball_rule_iv);
        titleTv = findViewById(R.id.ice_ball_main_title);
        rankIv = findViewById(R.id.ice_ball_rank_iv);
        mainBgIv = findViewById(R.id.main_bg_iv);
        mainFrontIv = findViewById(R.id.main_front_iv);
        scoreTitleTv = findViewById(R.id.ice_ball_win_times_this_week_title_tv);
        weekTv = findViewById(R.id.ice_ball_win_times_this_week_tv);
        rankTv = findViewById(R.id.ice_ball_rank_tv);
        rankStarIv = findViewById(R.id.rank_star_iv);
        starNumTv = findViewById(R.id.rank_star_num_tv);
        CustomCircleImageView imageView = findViewById(R.id.ice_ball_header_img);
        recycleView = findViewById(R.id.match_recycle_view);
        recycleView.setNestedScrollingEnabled(false);
        recycleView.setLayoutManager(new LinearLayoutManager(mContext));
        adapter = new CocosGroupMatchAdapter(mContext);
        recycleView.setAdapter(adapter);
        adapter.setCocosModeCallback(new CocosModeCallback() {
            @Override
            public void onClickItem(GameConfig.MatchInfo matchInfo) {
                presenter.clickQuickStart(matchInfo);
            }

            @Override
            public void onClickCreate(GameConfig.MatchInfo matchInfo) {
                presenter.clickGroupCreateRoom(matchInfo);
            }
        });

        HeadImageLoader.loadCircleHeadImage(LoginHelper.getHeadUrl(), imageView);

        backIv.setOnClickListener(onClickListener);
        rankIv.setOnClickListener(onClickListener);
        ruleIv.setOnClickListener(onClickListener);
    }

    private final OnClickListener onClickListener = new OnClickListener() {
        @Override
        public void onClick(View v) {
            if (v == backIv) {
                Activity activity = ContextUtil.getActivityFromContext(mContext);
                if (activity != null) {
                    activity.finish();
                }
            } else if (v == rankIv) {
                presenter.clickRankView();
            } else if (v == ruleIv) {
                presenter.clickGuideDialog();
            }
        }
    };

    @Override
    public void refreshScore(int mode) {
        if (mode == GameConfig.PLAY_MODE_COOPERATE) {
            scoreTitleTv.setText(ResUtil.getStr(R.string.cocos_week_best_grade));
        } else {
            scoreTitleTv.setText(getContext().getString(R.string.ice_ball_main_win_week));
        }
    }

    @Override
    public void onResume() {
        presenter.updatePersonRankIfNecessary();
    }

    @Override
    public void onDestroy() {
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        presenter.registerEventBus();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        presenter.unRegisterEventBus();
    }

    @Override
    public void refreshTitle(String gameName) {
        titleTv.setText(gameName);
    }

    @Override
    public void showPersonRank(CocosUserWeeklyInfo weeklyInfo) {
        String strRank;
        if (weeklyInfo.getRank() > RANK_MARK) {
            if (weeklyInfo.getScore() == 0) {
                strRank = "--";
            } else {
                strRank = "100+";
            }
        } else if (weeklyInfo.getRank() <= 0) {
            strRank = "--";
        } else {
            strRank = "" + weeklyInfo.getRank();
        }

        if (presenter.getPlayMode() == GameConfig.PLAY_MODE_COOPERATE) {
            if (weeklyInfo.getStar() <= 0 || weeklyInfo.getLevel() <= 0) {
                rankStarIv.setVisibility(View.GONE);
                starNumTv.setVisibility(View.GONE);
                weekTv.setText("--");
                rankTv.setText(strRank);
            } else {
                rankStarIv.setVisibility(View.VISIBLE);
                starNumTv.setVisibility(View.VISIBLE);
                rankTv.setText(strRank);
                starNumTv.setText(weeklyInfo.getStar() + "");
                weekTv.setText(ResUtil.getStr(R.string.level_x_1, weeklyInfo.getLevel()));
            }
        } else {
            String strScore = "" + weeklyInfo.getScore();
            rankStarIv.setVisibility(View.GONE);
            starNumTv.setVisibility(View.GONE);
            weekTv.setText(strScore);
            rankTv.setText(strRank);
        }
    }



    @Override
    public Context getViewContext() {
        return mContext;
    }

    public void refreshRes(int gameType) {
        CocosGameConfigUtil.loadResBgAsync(mainFrontIv, gameType, CocosGameConfigUtil.START_FRONT);
        CocosGameConfigUtil.loadResBgAsync(mainBgIv, gameType, CocosGameConfigUtil.START_BACKGROUND);
        CocosGameConfigUtil.loadResAsync(rankIv, gameType, CocosGameConfigUtil.LEADER_BOARD);
        adapter.refresh(ConfigHelper.getInstance().getGameConfig().getMatchGroup(gameType));
    }

    @Override
    public View getView() {
        return this;
    }
}