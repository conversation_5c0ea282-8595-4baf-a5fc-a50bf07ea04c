package com.wepie.wespy.cocosnew.match.main.ar285.loader

import com.wepie.wespy.cocosnew.match.main.ar285.loader.LittleGameResLoader.CurStatus
import kotlinx.coroutines.flow.StateFlow

interface LittleGameResLoader {
    val loadingState: StateFlow<Status>
    val gameState: StateFlow<GameInfo>
    var observer: (() -> Unit)?
    suspend fun load()

    enum class CurStatus {
        /**
         * init 期间弹窗保持为不可见
         */
        Init,

        /**
         * 进入加载过程时弹窗更新为可见，并隐藏进度数字
         */
        LoadingWithoutSize,

        /**
         * 进入加载过程时弹窗更新为可见，并开始更新进度
         */
        Loading,

        /**
         * 加载失败
         */
        Failed,

        /**
         * 解压失败
         */
        UnpackFailed,

        /**
         * 加载成功
         */
        Success,

        /**
         * 资源版本符合要求，不必更新
         */
        AlreadyMeetVersion,
    }



    data class Status(
        val rate: Float,
        val cur: Long,
        val matchTotal: Long,
        val gameTotal: Long,
        val curStatus: CurStatus = CurStatus.Init
    )

    data class GameInfo(val iconUrl: String, val name: String)
}
fun CurStatus.isSuccess(): Boolean {
    return this == CurStatus.Success || this == CurStatus.AlreadyMeetVersion
}