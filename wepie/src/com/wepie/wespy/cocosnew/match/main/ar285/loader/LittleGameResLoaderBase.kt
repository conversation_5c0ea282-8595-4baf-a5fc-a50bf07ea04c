package com.wepie.wespy.cocosnew.match.main.ar285.loader

import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.editionentity.GameConfig
import com.wepie.liblog.main.HLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext

abstract class LittleGameResLoaderBase : LittleGameResLoader {
    internal val loading = MutableStateFlow(LittleGameResLoader.Status(0f, 0, -1, -1))
    internal val gameInfo = MutableStateFlow(LittleGameResLoader.GameInfo("", ""))
    override val loadingState: StateFlow<LittleGameResLoader.Status> = loading
    override val gameState: StateFlow<LittleGameResLoader.GameInfo> = gameInfo
    override var observer: (() -> Unit)? = null

    internal fun initGame(gameType: Int): GameConfig {
        val config = ConfigHelper.getInstance().getGameConfig(gameType)
        gameInfo.value = LittleGameResLoader.GameInfo(config.iconUrl, config.name)
        return config
    }

    internal suspend fun invokeSuccess(info: SuccessInfo) {
        debug("load success")
        withContext(Dispatchers.Main) {
            loading.value = loading.value.copy(
                curStatus = if (info.isNoNeedUpdate) LittleGameResLoader.CurStatus.AlreadyMeetVersion else LittleGameResLoader.CurStatus.Success
            )
            observer?.invoke()
        }
    }

    internal suspend fun invokeFailed() {
        debug("load failed")
        withContext(Dispatchers.Main) {
            loading.value = loading.value.copy(curStatus = LittleGameResLoader.CurStatus.Failed)
            observer?.invoke()
        }
    }

    internal suspend fun invokeUnpackFailed() {
        debug("unpack failed")
        withContext(Dispatchers.Main) {
            loading.value = loading.value.copy(curStatus = LittleGameResLoader.CurStatus.UnpackFailed)
            observer?.invoke()
        }
    }

    internal fun debug(msg:String) {
        HLog.d("LGLoader", HLog.USR, msg)
    }
}

class SuccessInfo(
    val isNoNeedUpdate :Boolean
)