package com.wepie.wespy.cocosnew.match.main.ar285

import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.huiwan.widget.inflate
import com.huiwan.widget.rv.BaseRvAdapter
import com.huiwan.widget.rv.RVHolder
import com.wepie.wespy.R

class LittleGameUserInfoItemAdapter :
    BaseRvAdapter<LittleGameViewState.UserInfoItem, LittleGameUserInfoItemAdapter.Holder>() {
    private var keyColor = Color.BLACK
    private var valueColor = Color.BLACK

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
        val v = parent.inflate(R.layout.little_game_main_view_user_detail_item)
        return Holder(v)
    }

    fun refresh(newDataList: List<LittleGameViewState.UserInfoItem>, keyColor:Int, valueColor:Int) {
        this.keyColor = keyColor
        this.valueColor = valueColor
        refresh(newDataList)
    }

    override fun onBindViewHolder(holder: Holder, position: Int) {
        val item = dataList[position]
        holder.labelTv.text = item.label
        holder.valueTv.text = item.value
        holder.labelTv.setTextColor(keyColor)
        holder.valueTv.setTextColor(valueColor)
    }

    class Holder(v: View) : RVHolder(v) {
        val labelTv: TextView = v.findViewById(R.id.label_tv)
        val valueTv: TextView = v.findViewById(R.id.value_tv)
    }
}