package com.wepie.wespy.cocosnew.match.main.normal;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.widget.OnItemClickListener;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;

import java.util.ArrayList;
import java.util.List;

public class CocosNormalMatchPagerAdapter extends RecyclerView.Adapter<CocosNormalMatchPagerAdapter.ViewHolder> {

    private final List<GameConfig.MatchInfo> list = new ArrayList<>();

    private final Context mContent;

    private OnItemClickListener<GameConfig.MatchInfo> mListener;

    public CocosNormalMatchPagerAdapter(Context context) {
        this.mContent = context;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(mContent).inflate(R.layout.cocos_ice_ball_type_item, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.bind(list.get(position), mListener);
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    public void refresh(List<GameConfig.MatchInfo> list, OnItemClickListener<GameConfig.MatchInfo> listener) {
        this.list.clear();
        this.list.addAll(list);
        this.mListener = listener;
        notifyDataSetChanged();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {

        ImageView iceBallIv;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            iceBallIv = itemView.findViewById(R.id.ice_ball_main_type_item_iv);
        }

        void bind(GameConfig.MatchInfo matchInfo, OnItemClickListener<GameConfig.MatchInfo> listener) {
            WpImageLoader.load(matchInfo.getImgUrl(), iceBallIv);
            iceBallIv.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onClickItem(matchInfo, getAbsoluteAdapterPosition());
                }
            });
        }
    }
}