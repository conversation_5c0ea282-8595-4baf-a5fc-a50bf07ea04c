package com.wepie.wespy.cocosnew.match.main.ar285.loader

import androidx.lifecycle.LiveData
import androidx.lifecycle.Observer
import androidx.work.WorkInfo
import com.wepie.liblog.main.HLog

internal class UniqueWorkStateObserver(
    private val workInfoLiveData: LiveData<List<WorkInfo?>>,
    private val onFinish: () -> Unit
) :
    Observer<List<WorkInfo?>> {
    override fun onChanged(workInfos: List<WorkInfo?>) {
        val workInfo = workInfos.first()
        if (workInfo == null) {
            log(" work not exist")
            workInfoLiveData.removeObserver(this)
            return
        }
        log(" work state: ${workInfo.state}")
        if (workInfo.state.isFinished) {
            workInfoLiveData.removeObserver(this)
            onFinish()
        }
    }


    private fun log(msg: String) {
        HLog.d("WorkStateObserver $this", HLog.USR, msg)
    }
}