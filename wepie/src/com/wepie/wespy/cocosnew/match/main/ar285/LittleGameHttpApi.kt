package com.wepie.wespy.cocosnew.match.main.ar285

import com.huiwan.base.util.JsonUtil
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig
import com.huiwan.lib.api.ApiService
import com.huiwan.libtcp.callback.ContinuationSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.three.http.core.HttpUtil
import com.three.http.core.postSuspend
import com.wejoy.gamematch.net.GameMatchSender
import com.wejoy.littlegame.ILittleGameApi
import com.wepie.wespy.cocosnew.match.main.ar285.bean.LittleGameAchievement
import com.wepie.wespy.cocosnew.update.CocosResDebugVersionHelper
import com.wepie.wespy.net.tcp.packet.GameMatchPackets.CreateTeamReq2
import com.wepie.wespy.net.tcp.packet.GameMatchPackets.TeamMatchType
import kotlinx.coroutines.suspendCancellableCoroutine

const val GAME_API_ACHIEVEMENTS_INFO = "/game_api_v1/achievement_list"
const val GAME_API_RECOMMEND_MATCH_ITEM = "/game_api_v1/recommend_match_item"

suspend fun fetchGameAchievementsInfo(gameType: Int, queryKeys: List<String>) =
    HttpUtil.newBuilder().uri(GAME_API_ACHIEVEMENTS_INFO)
        .addParam("query_keys", JsonUtil.toJsonString(queryKeys))
        .addParam("game_type", gameType.toString())
        .build().postSuspend<LittleGameAchievement>()

suspend fun fetchRecommendMatchItem(uid: Int, matchList: String, gameType: Int) =
    HttpUtil.newBuilder().uri(GAME_API_RECOMMEND_MATCH_ITEM)
        .addParam("uid", uid.toString())
        .addParam("match_list", matchList)
        .addParam("game_type", gameType.toString())
        .build().postSuspend<VsCenterLittleGameConfig.RecommendItem>()

suspend fun tcpCreateTeamReq(
    mode: Int, betLevel: Int, gameType: Int, gameMode: Int, currencyType: Int
) = suspendCancellableCoroutine<RspHeadInfo> {
    val builder = CreateTeamReq2.newBuilder()
        .setMode(mode)
        .setBetLevel(betLevel)
        .setGameType(gameType)
        .setGameMode(gameMode)
        .setGameVersion(ApiService.of(ILittleGameApi::class.java).getGameVersion(gameType))
        .setMatchType(TeamMatchType.NORMAL_MATCH_TYPE_VALUE)
        .setGameCurrencyTypeValue(currencyType)
    GameMatchSender.createTeamReq2(0, builder, ContinuationSeqCallback(it))
}

/*
mode 可能是 room mode
 */
suspend fun tcpQuickMatch(
    tid: Int, mode: Int, betLevel: Int, gameType: Int, gameMode: Int, currencyType: Int
) = suspendCancellableCoroutine<RspHeadInfo> {
    val version = ApiService.of(ILittleGameApi::class.java).getGameVersion(gameType)
    val localSpecifyGameVersion = CocosResDebugVersionHelper.loadVersion(gameType)
    GameMatchSender.quickMatchReq2(
        0,
        tid,
        mode,
        betLevel,
        gameType,
        gameMode,
        TeamMatchType.NORMAL_MATCH_TYPE_VALUE,
        version,
        currencyType,
        localSpecifyGameVersion,
        ContinuationSeqCallback(it)
    )
}