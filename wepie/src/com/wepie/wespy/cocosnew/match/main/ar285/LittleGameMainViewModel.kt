package com.wepie.wespy.cocosnew.match.main.ar285

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.editionentity.ILittleGameMatchInfo
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.FuncItem
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.LayoutItem
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.littlegame.util.LittleGameUtil
import com.three.http.core.KtResultSuccess
import com.wepie.wespy.R
import com.wepie.wespy.cocosnew.match.matching.CocosMatchUtil
import com.wepie.wespy.model.entity.match.TeamInfo
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class LittleGameMainViewModel(app: Application) : AndroidViewModel(app) {

    /**
     * 考虑到界面更新频率并不算高，整体复杂度不高
     * 这里不对界面进行分区更新等操作。
     */
    private val state = MutableStateFlow(LittleGameViewState())
    private val events = MutableSharedFlow<LittleGameEvents>()

    /**
     * 界面状态数据流
     */
    val stateFlow: StateFlow<LittleGameViewState> = state

    /**
     * 游戏操作事件流
     */
    val eventFlow: SharedFlow<LittleGameEvents> = events

    var gameType: Int = 0
        set(value) {
            field = value
            _gameTypeState.value = field
            updateStateByGameType()
        }

    private val _gameTypeState: MutableStateFlow<Int> = MutableStateFlow(gameType)
    val gameTypeState = _gameTypeState.asStateFlow()

    /**
     * 根据配置生成界面所需的界面数据
     */
    private fun updateStateByGameType() {
        val config = ConfigHelper.getInstance().getGameConfig(gameType)
        val name = config.name
        val generateHome = config.generateHome
        if (generateHome == null) {
            ToastUtil.show(R.string.server_data_err)
            return
        }

        val userInfoKeys = generateHome.person?.achievementList ?: emptyList()
        val userCurrencyKeys = generateHome.person?.currency?.currencyList ?: emptyList()
        val centerEntry = generateHome.entryLayout?.layoutList ?: emptyList()
        val bottomStart = generateHome.funcLayout?.bottomStart ?: emptyList()
        val bottomEnd = generateHome.funcLayout?.bottomEnd ?: emptyList()
        val topEnd = generateHome.funcLayout?.topEnd ?: emptyList()
        state.value = state.value.copy(
            title = name,
            bg = generateHome.metaData.bgUrl,
            userInfo = LittleGameViewState.UserInfo(
                userInfoKeys.map {
                    LittleGameViewState.UserInfoItem(it.title, "-")
                },
                generateHome.person.styleConfig,
                userCurrencyKeys.map {
                    LittleGameViewState.UserCurrencyItem(it.queryKey, 0, it.icon, it.plus)
                },
                generateHome.person.currency.style,
            ),
            centerInfo = LittleGameViewState.PageCenterInfo(genGameItem1(centerEntry)),
            start = LittleGameViewState.OpInfo(genGameItem2(bottomStart)),
            end = LittleGameViewState.OpInfo(genGameItem2(bottomEnd)),
            topEnd = LittleGameViewState.OpInfo(genGameItem2(topEnd))
        )
        updateAchievement()
    }

    fun switchGamePage(gameType: Int) {
        this.gameType = gameType
    }

    private fun genGameItem1(layoutItem: List<LayoutItem>): List<LittleGameViewState.GameItemInfo> {
        return layoutItem.map {
            LittleGameViewState.GameItemInfo(
                it.entryImg,
                it.jumpType,
                it.jumpDetails,
                !it.isFullLine,
                reportComment = it.reportComment
            )
        }
    }

    private fun genGameItem2(funcItem: List<FuncItem>): List<LittleGameViewState.GameItemInfo> {
        return funcItem.map {
            LittleGameViewState.GameItemInfo(
                it.iconUrl,
                it.jumpType,
                it.jumpDetails,
                false,
                it.title,
                reportComment = it.reportComment
            )
        }
    }

    /**
     * 更新成就区信息，个人信息部分.
     */
    private fun updateAchievement() {
        val config = ConfigHelper.getInstance().getGameConfig(gameType)
        val achievement = config.generateHome?.person?.achievementList ?: return
        val currency = config.generateHome?.person?.currency?.currencyList ?: return
        viewModelScope.launch {
            val queryKeys = ArrayList<String>()
            queryKeys.addAll(achievement.map { it.queryKey })
            queryKeys.addAll(currency.map { it.queryKey })
            val res = fetchGameAchievementsInfo(gameType, queryKeys)
            if (res !is KtResultSuccess) {
                ToastUtil.show(res.failedDesc)
                return@launch
            }
//            if (res.data.list.size != queryKeys.size) {
//                ToastUtil.show(R.string.server_data_err)
//                return@launch
//            }
            val userInfoList = state.value.userInfo.list.mapIndexed { index, info ->
                LittleGameViewState.UserInfoItem(info.label, res.data.list.getOrNull(index) ?: "")
            }
            val userCurrencyList = state.value.userInfo.currencyList.mapIndexed { index, currency ->
                val curr: Long = try {
                    res.data.list[index + userInfoList.size].toLong()
                } catch (e: Exception) {
                    0L
                }
                LittleGameViewState.UserCurrencyItem(
                    currency.type, curr, currency.url, currency.plusUrl
                )
            }
            state.value = state.value.copy(
                userInfo = LittleGameViewState.UserInfo(
                    userInfoList, config.generateHome.person.styleConfig,
                    userCurrencyList, config.generateHome.person.currency.style
                )
            )
        }
    }

    /**
     * 请求匹配
     */
    fun reqMatch(jumpMatchItem: VsCenterLittleGameConfig.JumpMatchItem) {
        viewModelScope.launch {
            val head = if (TeamInfo.isMorePlayerMatch(jumpMatchItem.matchMode)) {
                match2v2CreateTeam(jumpMatchItem, LittleGameEvents.JumpPrepareMatch, false)
            } else {
                match1v1(jumpMatchItem, false)
            }
            LittleGameUtil.trackMatchGameError(head.code, gameType, jumpMatchItem)
        }
    }

    /**
     * @param isRetry 标记是否自动重试
     */
    private suspend fun match1v1(
        jumpMatchItem: VsCenterLittleGameConfig.JumpMatchItem, isRetry: Boolean
    ): RspHeadInfo {
        val head = tcpQuickMatch(
            -1,
            jumpMatchItem.matchMode,
            jumpMatchItem.betLevel,
            gameType,
            jumpMatchItem.gameMode,
            jumpMatchItem.currencyType
        )
        if (head.codeOk()) {
            CocosMatchUtil.saveMatchInfo(head)
            events.emit(LittleGameEvents.JumpMatch)
        } else {
            if (isRetry) {
                events.emit(LittleGameEvents.Error(head, jumpMatchItem, null))
            } else {
                events.emit(
                    LittleGameEvents.Error(head, jumpMatchItem) { retry ->
                        viewModelScope.launch {
                            match1v1(jumpMatchItem, retry)
                        }
                    })
            }
        }
        return head
    }

    private suspend fun match2v2CreateTeam(
        matchInfo: ILittleGameMatchInfo, successEvents: LittleGameEvents, isRetry: Boolean
    ): RspHeadInfo {
        val mode = if (successEvents == LittleGameEvents.JumpCreate) {
            matchInfo.isCreate = true
            matchInfo.roomMode
        } else {
            matchInfo.matchMode
        }
        val head = tcpCreateTeamReq(
            mode, matchInfo.betLevel, gameType, matchInfo.gameMode, matchInfo.currencyType
        )
        if (head.codeOk()) {
            CocosMatchUtil.saveMatchInfo(head)
            events.emit(successEvents)
        } else {
            if (isRetry) {
                events.emit(LittleGameEvents.Error(head, matchInfo, null))
            } else {
                events.emit(LittleGameEvents.Error(head, matchInfo) {
                    viewModelScope.launch {
                        match2v2CreateTeam(matchInfo, successEvents, true)
                    }
                })
            }
        }
        return head
    }

    fun reqCreate(matchInfo: ILittleGameMatchInfo) {
        viewModelScope.launch {
            val head = match2v2CreateTeam(matchInfo, LittleGameEvents.JumpCreate, false)
            LittleGameUtil.trackMatchGameError(head.code, gameType, matchInfo)
        }
    }
}