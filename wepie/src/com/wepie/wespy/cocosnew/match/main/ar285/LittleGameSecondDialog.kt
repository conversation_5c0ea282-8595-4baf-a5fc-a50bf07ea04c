package com.wepie.wespy.cocosnew.match.main.ar285

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.huiwan.base.util.ContextUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ViewUtil
import com.huiwan.configservice.editionentity.GameConfig
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JumpPop
import com.huiwan.lib.api.impl
import com.wejoy.littlegame.ILittleGameApi
import com.wejoy.littlegame.LittleGameSimpleInfo
import com.wepie.wespy.R
import com.wepie.wespy.cocosnew.match.main.ar285.bean.LittleGameSecondConfigData
import com.wepie.wespy.cocosnew.match.main.ar285.bean.totoLittleGameSecondConfigData
import kotlinx.coroutines.launch

class LittleGameSecondDialog : BottomSheetDialogFragment() {
    private var data: LittleGameSecondConfigData? = null
    private var gameMode: Int = 0

    private lateinit var rv: RecyclerView
    private lateinit var sheetBar: View
    private lateinit var titleTv: TextView
    private lateinit var tipsIv: ImageView
    private val eventHandler = LittleGameEventHandler()
    private val entryAdapter = LittleGameEntryItemAdapter(this, eventHandler)
    private var recommendVM: LittleGameRecommendModel? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.little_game_main_sec_dialog_view, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViewModel()
        val data = this.data
        if (data == null) {
            dismissAllowingStateLoss()
            return
        }
        initViews(view)
        update(data)

        view.post {
            val maxHeight = ScreenUtil.getScreenHeight() * 2 / 3;
            if (view.measuredHeight > maxHeight) {
                ViewUtil.setViewHeight(view, maxHeight)
            } else {
                ViewUtil.setViewHeight(view, ViewGroup.LayoutParams.WRAP_CONTENT)
            }
        }
    }

    private fun initViewModel() {
        val owner = viewLifecycleOwner as ViewModelStoreOwner
        recommendVM = ViewModelProvider(owner)[LittleGameRecommendModel::class.java].apply {
            gameType = eventHandler.gameType
            viewModelScope.launch {
                matchItemListLiveData.observe(viewLifecycleOwner) {
                    if (it.isNotEmpty()) {
                        entryAdapter.refresh(it)
                    }
                }
            }
        }
    }

    private fun initViews(v: View) {
        rv = v.findViewById(R.id.rv)
        sheetBar = v.findViewById(R.id.sheet_bar)
        titleTv = v.findViewById(R.id.title_tv)
        tipsIv = v.findViewById(R.id.game_mode_rule_iv)
        tipsIv.setOnClickListener {
            ILittleGameApi::class.impl().showGameRuleDialog(
                requireContext(), LittleGameSimpleInfo(
                    gameType = eventHandler.gameType,
                    gameMode = gameMode,
                    currencyType = GameConfig.CURRENCY_CHIP
                )
            )
        }
    }

    /**
     * @since jk 5.0.5
     * @return 是否1行两列
     */
    private fun shouldUse2CatALine(): Boolean {
        return ScreenUtil.getScreenWidthDp() > 600
    }

    private fun update(data: LittleGameSecondConfigData) {
        titleTv.text = data.title
        val spanCount = if (shouldUse2CatALine()) 4 else 2
        rv.layoutManager = GridLayoutManager(context, spanCount)
        rv.adapter = entryAdapter
        val gameItemList = data.itemInfos
        entryAdapter.refresh(gameItemList)
        recommendVM?.updateRecommendMatchItem(gameItemList)
        val lm = rv.layoutManager as GridLayoutManager
        lm.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return if (entryAdapter.dataList[position].catTwo) 1 else 2
            }
        }
        rv.addItemDecoration(LittleGameEntryItemDecoration())
    }

    fun showDialog(context: Context, gameType: Int, data: LittleGameSecondConfigData) {
        showDialog(context, gameType, 0, data)
    }

    fun showDialog(context: Context, gameType: Int, gameMode: Int, pop: JumpPop) {
        showDialog(context, gameType, gameMode, pop.totoLittleGameSecondConfigData())
    }

    fun showDialog(
        context: Context,
        gameType: Int,
        gameMode: Int,
        data: LittleGameSecondConfigData
    ) {
        this.data = data
        this.gameMode = gameMode
        this.eventHandler.gameType = gameType
        setStyle(STYLE_NORMAL, R.style.TransparentBottomSheetStyle)
        val activity = ContextUtil.getFragmentActivityFromContext(context)
        if (activity != null) {
            try {
                show(activity.supportFragmentManager, "LittleGameSec")
            } catch (e: IllegalStateException) {
                //ignore
            }
        }
    }
}