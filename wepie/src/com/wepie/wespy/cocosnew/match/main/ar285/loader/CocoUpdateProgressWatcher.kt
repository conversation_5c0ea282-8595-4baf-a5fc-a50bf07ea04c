package com.wepie.wespy.cocosnew.match.main.ar285.loader

import com.huiwan.littlegame.cocos.resCheck.ICocosResLoadingWatcher

class CocoUpdateProgressWatcher : ICocosResLoadingWatcher {
    @Volatile
    var matchResCur: Long = 0L
        private set

    @Volatile
    var gameResCur: Long = 0L
        private set

    @Volatile
    var matchResTotal: Long = 0L
        private set

    @Volatile
    var gameResTotal: Long = 0L
        private set

    override fun initMatchResIsNeedLoad(isNeedLoad: Boolean) {
        if (!isNeedLoad) {
            matchResTotal = 0L
        }
    }

    override fun initGameResIsNeedLoad(isNeedLoad: Boolean) {
        if (!isNeedLoad) {
            gameResTotal = 0L
        }
    }

    override fun updateTotalInGameSize(size: Long) {
        gameResTotal = size
    }

    override fun updateTotalMatchSize(size: Long) {
        matchResTotal = size
    }

    override fun updateDownloadMatchSize(size: Long) {
        matchResCur = size
    }

    override fun updateDownloadGameRes(size: Long) {
        gameResCur = size
    }

    override fun notifyLoadMatchResSuccess() {
        matchResCur = matchResTotal
    }

    override fun notifyLoadMatchResFail() {

    }

    override fun notifyLoadInGameResResSuccess() {
        gameResCur = gameResTotal
    }

    override fun notifyLoadInGameResFail() {

    }

    override fun cancel() {

    }

}