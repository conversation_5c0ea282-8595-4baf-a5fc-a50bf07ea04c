package com.wepie.wespy.cocosnew.match.main.ar285

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ShapeDrawable
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.ImageView
import com.huiwan.base.util.ScreenUtil
import com.huiwan.component.game.chip.ChipApi
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JUMP_TYPE_MATCH
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JUMP_TYPE_QUICK_START
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JumpMatchItem
import com.huiwan.lib.api.ApiService
import com.huiwan.user.LoginHelper
import com.huiwan.widget.image.DrawableUtil
import com.huiwan.widget.inflate
import com.huiwan.widget.rv.BaseRvAdapter
import com.huiwan.widget.rv.RVHolder
import com.wejoy.littlegame.LittleGameSimpleInfo
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.helper.shence.ShenceEvent

class LittleGameEntryItemAdapter(
    val dialog: LittleGameSecondDialog?,
    val eventHandler: LittleGameEventHandler
) :
    BaseRvAdapter<LittleGameViewState.GameItemInfo, LittleGameEntryItemAdapter.Holder>() {

    private var lastClickTime = 0L

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
        val v = parent.inflate(R.layout.little_game_main_view_game_item)
        return Holder(v)
    }

    override fun onBindViewHolder(holder: Holder, position: Int) {
        val item = dataList[position]
        val d = DrawableUtil.genColorRadius(Color.LTGRAY, 16)
        if (d is ShapeDrawable) {
            d.intrinsicHeight = if (item.catTwo) {
                ScreenUtil.dip2px(50f)
            } else {
                ScreenUtil.dip2px(100f)
            }
            d.intrinsicWidth = if (item.catTwo) {
                ScreenUtil.dip2px(165f)
            } else {
                ScreenUtil.dip2px(343f)
            }
        }
        WpImageLoader.load(
            item.iconUrl, holder.iconIv,
            ImageLoadInfo().imageType(
                if (item.catTwo) {
                    ImageLoadInfo.IMAGE_TYPE_HOME_GAME_SHORT
                } else {
                    ImageLoadInfo.IMAGE_TYPE_HOME_GAME_LONG
                }
            ).error(d).placeholder(d)
        )

        if (item.isRecommend) {
            holder.iconIv.post {
                val lp = holder.board.layoutParams.apply {
                    width = holder.iconIv.width
                    height = holder.iconIv.height
                }
                holder.board.layoutParams = lp
                ConfigHelper.getInstance().getGameConfig(eventHandler.gameType).generateHome?.let {
                    holder.board.visibility = View.VISIBLE
                    WpImageLoader.load(
                        if (item.catTwo) {
                            it.recommend.highLightBoardShort
                        } else {
                            it.recommend.highLightBoard
                        }, holder.board
                    )
                }
            }
        } else {
            holder.board.visibility = View.GONE
        }

        holder.itemView.setOnClickListener(object : OnClickListener {
            override fun onClick(v: View) {
                val current = System.currentTimeMillis()
                if (current - lastClickTime < 500L) { //500ms内点击多次无效
                    return
                }
                lastClickTime = current

                var jumpItem = item
                if (item.jumpType == JUMP_TYPE_QUICK_START) {
                    var recommendItem: LittleGameViewState.GameItemInfo? = null
                    for (gameItemInfo in dataList) {
                        if (gameItemInfo.isRecommend) {
                            recommendItem = gameItemInfo
                        }
                    }
                    if (recommendItem == null) {
                        jumpItem = dataList[0]
                    } else {
                        jumpItem = recommendItem
                    }
                }
                if (!checkReachMaxLimit(holder.itemView.context, item.jumpType, jumpItem)) {
                    eventHandler.onClick(v, item.jumpType, jumpItem.jumpDetails)
                }
                val matchItem = jumpItem.jumpDetails.matchItem
                val map = mapOf(
                    "game_type" to eventHandler.gameType.toString(),
                    "mode_type" to "${eventHandler.gameType}${matchItem.gameMode}",
                    "bet_level" to jumpItem.jumpDetails.matchItem.betLevel,
                )
                ShenceEvent.appClick(TrackScreenName.HOME_PAGE, map)
            }
        })
    }

    private fun getLittleGameSimpleInfo(matchItem: JumpMatchItem): LittleGameSimpleInfo{
        return LittleGameSimpleInfo(
            eventHandler.gameType,
            matchItem.getMatchMode(), matchItem.getGameMode(), matchItem.getBetLevel(),
            matchItem.getCurrencyType(), matchItem.isCreate
        )
    }

    private fun checkReachMaxLimit(context: Context, jumpType: Int, jumpItem: LittleGameViewState.GameItemInfo): Boolean {
        if (jumpType == JUMP_TYPE_MATCH && !eventHandler.canJumpCocosGuide(jumpItem.jumpDetails)){
            val matchItem = jumpItem.jumpDetails.matchItem
            val matchInfos = ConfigHelper.getInstance().getGameConfig(eventHandler.gameType).matchInfoListForMatch
                .filter { it.currencyType == matchItem.currencyType && it.matchMode == matchItem.matchMode
                        && it.gameMode == matchItem.gameMode && it.roomMode == matchItem.roomMode
                        && it.betLevel == matchItem.betLevel}
            val chipCoin = LoginHelper.getLoginUser()?.chipCoin?:0
            HLog.d(TAG, HLog.USR, "checkReachMaxLimit mine chipCoin: $chipCoin matchItem: $matchItem matchInfos: $matchInfos")
            if (matchInfos.isNotEmpty() && chipCoin > matchInfos[0].maxCoinLimit) {
                HLog.d(TAG, HLog.USR, "checkReachMaxLimit yes")
                ApiService.of(ChipApi::class.java).handleChipLimit(context, getLittleGameSimpleInfo(jumpItem.jumpDetails.matchItem))
                return true
            }
        }
        dialog?.dismiss()
        HLog.d(TAG, HLog.USR, "checkReachMaxLimit no")
        return false
    }

    class Holder(v: View) : RVHolder(v) {
        val iconIv: ImageView = v.findViewById(R.id.image_iv)
        val board: ImageView = v.findViewById(R.id.image_board)
    }

    companion object{
        const val TAG = "LittleGameEntryItemAdapter"
    }
}