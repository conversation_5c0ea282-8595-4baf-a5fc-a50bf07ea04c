package com.wepie.wespy.cocosnew.match.main;

import android.app.Activity;
import android.content.Context;

import com.huiwan.base.context.holder.ContextHolder;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.lib.api.ApiService;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.module.webview.WebActivity;
import com.huiwan.store.PrefUtil;
import com.huiwan.user.entity.CocosUserWeeklyInfo;
import com.huiwan.user.http.UserApi;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.littlegame.ILittleGameApi;
import com.wejoy.littlegame.LittleGame;
import com.wejoy.littlegame.LittleGameKt;
import com.wejoy.littlegame.LittleGameSimpleInfo;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.MatchPacketPresenter;
import com.wepie.wespy.cocosnew.match.main.ar285.LittleGameEventHandler;
import com.wepie.wespy.cocosnew.match.main.ar285.LittleGameTrackUtilsKt;
import com.wepie.wespy.cocosnew.match.main.ar285.loader.LittleGameResUtil;
import com.wepie.wespy.cocosnew.match.matching.CocosMatchUtil;
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil;
import com.wepie.wespy.cocosnew.util.LittleGameGuideDialogUtils;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.model.event.iceball.IceBeforeStartEvent;
import com.wepie.wespy.module.common.jump.JumpCocosTeamUtil;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.voiceroom.util.view.ListViewDialog;
import com.wepie.wespy.net.tcp.packet.GameMatchPackets;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import kotlin.Unit;

public class CocosMainPresenter {

    private static final String TAG = CocosMainPresenter.class.getSimpleName();

    private final ILittleGameMainView mainView;
    private final int gameType;
    private int selectedIndex = 0;

    public CocosMainPresenter(ILittleGameMainView mainView, int gameType) {
        this.mainView = mainView;
        this.gameType = gameType;
    }

    public void updateSelectedIndex(int selectedIndex) {
        this.selectedIndex = selectedIndex;
    }

    /**
     * 0 和 1 分别是 单人的初级场和中级场
     * 2 和 3 分别是 双人的初级场和中级场
     */
    private void quickStart(final GameConfig.MatchInfo matchInfo) {
        if (showMatchGuide(mainView.getViewContext(), gameType, matchInfo, () -> quickStart(matchInfo))) {
            return;
        }

        List<GameConfig.MatchInfo> matchInfoList = ConfigHelper.getInstance().getGameMatchInfo(getGameType());
        if (matchInfoList.isEmpty()) {
            ToastUtil.show(ResUtil.getStr(R.string.cocos_little_game_config_error));
            return;
        }
        if (TeamInfo.isMorePlayerMatch(matchInfo.getMatchMode())) {
            quickStart2v2(matchInfo);
        } else {
            quickStart1v1(matchInfo);
        }
    }

    private void createRoom(final GameConfig.MatchInfo matchInfo, final boolean needShowInviteDialog) {
        if (ConfigHelper.getInstance().getVoiceRoomConfig().isHomeSelfBuildLittleGame(gameType)) {
            LittleGameEventHandler.createVoiceGameRoom(mainView.getViewContext(), matchInfo, gameType);
            return;
        }
        if (showMatchGuide(mainView.getViewContext(), gameType, matchInfo, () -> createRoom(matchInfo, needShowInviteDialog))) {
            return;
        }

        List<GameConfig.MatchInfo> matchInfoList = ConfigHelper.getInstance().getGameMatchInfo(getGameType());
        if (matchInfoList.isEmpty()) {
            ToastUtil.show(ResUtil.getStr(R.string.cocos_little_game_config_error));
            return;
        }
        mainView.showLoading();
        MatchPacketPresenter.createTeamReq2(false, matchInfo, getGameType(), new LifeSeqCallback(mainView.getView()) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                JumpCocosTeamUtil.existVoiceRoomIfIn();
                GameMatchPackets.CreateTeamRsp2 createTeamRsp = (GameMatchPackets.CreateTeamRsp2) head.message;
                LittleGame.updateTeamInfo(TeamInfo.parse(createTeamRsp));
                saveLastPlay();
                mainView.hideLoading();
                jumpToCreateRoom(needShowInviteDialog);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                mainView.hideLoading();
                LittleGameSimpleInfo info = new LittleGameSimpleInfo(gameType, matchInfo.getRoomMode(),
                        matchInfo.getGameMode(), matchInfo.getBetLevel(), matchInfo.getCurrencyType());
                boolean ret = ApiService.of(ILittleGameApi.class).handleEnterError(mainView.getViewContext(),
                        head.code, LittleGameKt.GAME_SCENE_LITTLE_GAME_MAIN | LittleGameKt.EXCHANGE_TYPE_ENTER,
                        info, () -> {
                            createRoom(matchInfo, needShowInviteDialog);
                            return Unit.INSTANCE;
                        });
                if (ret) {
                    return;
                }
                if (head.code == RspHeadInfo.ERROR_CODE_VERSION_TOO_LOW) {
                    LittleGameResUtil.INSTANCE.checkLoadRes(mainView.getViewContext(), gameType, true, LittleGameTrackUtilsKt.SCENE_LITTLE_GAME_MAIN, cocos -> {
                        createRoom(matchInfo, needShowInviteDialog);
                        return Unit.INSTANCE;
                    });
                } else {
                    mainView.showErrMsg(head.desc);
                }
            }
        });
    }

    private void quickStart1v1(final GameConfig.MatchInfo matchInfo) {
        final int level = matchInfo.getBetLevel();
        int mode = matchInfo.getMatchMode();
        mainView.showLoading();
        MatchPacketPresenter.quickMatchReq2(-1, -1, mode, level, getGameType(), matchInfo.getGameMode(), matchInfo.getCurrencyType(), new LifeSeqCallback(mainView.getView()) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                CocosMatchUtil.saveMatchInfo(head);
                saveLastPlay();
                mainView.hideLoading();
                jumpToMatch();
            }

            @Override
            public void onFail(RspHeadInfo head) {
                mainView.hideLoading();
                CocosMatchUtil.handleFailMatch(mainView.getViewContext(), LittleGameKt.GAME_SCENE_LITTLE_GAME_MATCH,
                        getGameType(), matchInfo, head, LittleGameTrackUtilsKt.SCENE_LITTLE_GAME_MAIN, (retry) -> quickStart1v1(matchInfo));
            }
        });
    }

    private void quickStart2v2(final GameConfig.MatchInfo matchInfo) {
        mainView.showLoading();
        MatchPacketPresenter.createTeamReq2(true, matchInfo, getGameType(), new LifeSeqCallback(mainView.getView()) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                JumpCocosTeamUtil.existVoiceRoomIfIn();
                GameMatchPackets.CreateTeamRsp2 createTeamRsp = (GameMatchPackets.CreateTeamRsp2) head.message;
                LittleGame.updateTeamInfo(TeamInfo.parse(createTeamRsp));
                saveLastPlay();
                mainView.hideLoading();
                jumpToPrepare();
            }

            @Override
            public void onFail(RspHeadInfo head) {
                mainView.hideLoading();
                LittleGameSimpleInfo info = new LittleGameSimpleInfo(gameType, matchInfo.getRoomMode(),
                        matchInfo.getGameMode(), matchInfo.getBetLevel(), matchInfo.getCurrencyType());
                boolean ret = ApiService.of(ILittleGameApi.class).handleEnterError(mainView.getViewContext(),
                        head.code, LittleGameKt.GAME_SCENE_LITTLE_GAME_MAIN | LittleGameKt.EXCHANGE_TYPE_ENTER, info, () -> {
                            quickStart2v2(matchInfo);
                            return Unit.INSTANCE;
                        });
                if (ret) {
                    return;
                }
                if (head.code == RspHeadInfo.ERROR_CODE_VERSION_TOO_LOW) {
                    LittleGameResUtil.INSTANCE.checkLoadRes(mainView.getViewContext(), gameType, true, LittleGameTrackUtilsKt.SCENE_LITTLE_GAME_MAIN, cocos -> {
                        quickStart2v2(matchInfo);
                        return Unit.INSTANCE;
                    });
                } else {
                    mainView.showErrMsg(head.desc);
                }
            }
        });
    }

    /**
     * 推送，确定匹配成功, 有可能出现在还未跳转到匹配界面就已经收到了服务器的 push
     *
     * @param match 匹配结果中的用户信息
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void pushMatchSuccess(IceBeforeStartEvent match) {
        ContextHolder.of(mainView.getViewContext(), CocosMainActivity.class).setPlayedGame(true);
    }

    public void updatePersonRankIfNecessary() {
        if (ContextHolder.of(mainView.getViewContext(), CocosMainActivity.class).getPlayedGame()) {
            ContextHolder.of(mainView.getViewContext(), CocosMainActivity.class).setPlayedGame(false);
            updatePersonRank();
            HLog.i(TAG, "update game rank");
        }
    }

    private void updatePersonRank() {
        UserApi.getUserGameWeekly(getGameType(), new LifeDataCallback<>(mainView.getView()) {
            @Override
            public void onSuccess(Result<CocosUserWeeklyInfo> result) {
                mainView.showPersonRank(result.data);
            }

            @Override
            public void onFail(int i, String s) {
                mainView.showErrMsg(s);
            }
        });
    }

    public int getLastPlay() {
        return PrefUtil.getInstance().getInt(getLastPlayKey(), 0);
    }

    private void saveLastPlay() {
        PrefUtil.getInstance().getPref().edit().putInt(getLastPlayKey(), selectedIndex).apply();
    }

    private String getLastPlayKey() {
        return PrefUtil.KEY_ICE_BALL_LAST_PLAY_TYPE + gameType;
    }

    private boolean showMatchGuide(Context context, int gameType, GameConfig.MatchInfo matchInfo, Runnable callback) {
        LittleGameSimpleInfo info = new LittleGameSimpleInfo(gameType, matchInfo.getMatchMode(), matchInfo.getGameMode(),
                matchInfo.getBetLevel(), matchInfo.getCurrencyType());
        return LittleGameGuideDialogUtils.showMatchGuide(context, info, callback);
    }

    public void registerEventBus() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    public void unRegisterEventBus() {
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    public int getGameType() {
        return gameType;
    }

    public int getPlayMode() {
        return CocosGameConfigUtil.getCocosGameConfig(getGameType()).getPlayMode();
    }

    private String getShareRewardUrl() {
        return CocosGameConfigUtil.getCocosGameConfig(getGameType()).getShareRewardUrl();
    }

    public void initData() {
        mainView.refreshTitle(CocosGameConfigUtil.getCocosGameConfig(getGameType()).getName());
        mainView.refreshRes(getGameType());
        mainView.refreshScore(getPlayMode());
        updatePersonRank();
    }

    private void jumpToMatch() {
        JumpUtil.gotoIceBallMatchActivity(mainView.getViewContext(), getGameType(), true);
    }

    private void jumpToPrepare() {
        JumpUtil.gotoIceBallMatchPrepareActivity(mainView.getViewContext(), getGameType(), true, "");
    }

    private void jumpToCreateRoom(final boolean needShowInviteDialog) {
        JumpUtil.gotoIceBallCreateRoomActivity(mainView.getViewContext(), getGameType(),
                true, needShowInviteDialog, "");
    }

    public void clickGuideDialog() {
        ApiService.of(ILittleGameApi.class).showGameRuleDialog(mainView.getViewContext(), new LittleGameSimpleInfo(gameType, 0, 0, 0, GameConfig.CURRENCY_COIN));
    }

    public void clickRankView() {
        JumpUtil.gotoGameRankActivity(mainView.getViewContext(), getGameType());
    }

    public void clickQuickStart(final GameConfig.MatchInfo matchInfo) {
        final Activity activity = ContextUtil.getActivityFromContext(mainView.getViewContext());
        if (activity == null) {
            return;
        }
        quickStart(matchInfo);
    }

    public void clickInviteFriend(final GameConfig.MatchInfo matchInfo) {
        createRoom(matchInfo, true);
    }

    public void clickCreateRoom(final GameConfig.MatchInfo matchInfo) {
        createRoom(matchInfo, false);
    }

    public void clickCustomWeb() {
        WebActivity.go(mainView.getViewContext(), getShareRewardUrl());
    }

    public void clickGroupCreateRoom(final GameConfig.MatchInfo matchInfo) {
        groupCreateRoom(matchInfo);
    }

    private void groupCreateRoom(final GameConfig.MatchInfo matchInfo) {
        GameConfig.MatchGroupInfo matchGroupInfoCur = null;
        List<GameConfig.MatchGroupInfo> matchGroupInfoList = ConfigHelper.getInstance().getGameConfig().getMatchGroup(getGameType());
        for (int groupIndex = 0; groupIndex < matchGroupInfoList.size(); groupIndex++) {
            GameConfig.MatchGroupInfo matchGroupInfo = matchGroupInfoList.get(groupIndex);
            for (GameConfig.MatchInfo info : matchGroupInfo.getMatchInfoList()) {
                if (info.getBetLevel() == matchInfo.getBetLevel() && info.getMatchMode() == matchInfo.getMatchMode()) {
                    matchGroupInfoCur = matchGroupInfo;
                    break;
                }
            }
        }
        if (matchGroupInfoCur == null || matchGroupInfoCur.getMatchInfoListForCreate().size() == 0) {
            ToastUtil.show(ResUtil.getStr(R.string.cocos_mode_error));
            return;
        }

        List<String> dataList = new ArrayList<>();
        final List<GameConfig.MatchInfo> matchInfoList = matchGroupInfoCur.getMatchInfoListForCreate();
        for (GameConfig.MatchInfo info : matchInfoList) {
            dataList.add(mainView.getViewContext().getString(R.string.formatter_s__s, info.getTitle(), info.getCoinDesc()));
        }

        if (matchInfoList.size() == 1) {
            createRoom(matchInfoList.get(0), false);
        } else {
            DialogUtil.showBottomDialog(mainView.getViewContext(), dataList, new ListViewDialog.Callback() {
                @Override
                public void onClickItem(int position) {
                    if (position < matchInfoList.size()) {
                        createRoom(matchInfoList.get(position), false);
                    }
                }
            });
        }
    }

}
