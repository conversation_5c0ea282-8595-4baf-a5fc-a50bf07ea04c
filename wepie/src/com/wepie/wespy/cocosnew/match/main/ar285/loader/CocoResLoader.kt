package com.wepie.wespy.cocosnew.match.main.ar285.loader

import androidx.annotation.MainThread
import com.huiwan.lib.api.ApiService
import com.huiwan.littlegame.cocos.resCheck.ICocosResLoadingWatcher
import com.huiwan.littlegame.cocos.resCheck.base.RES_VERSION_MEET_REQUIREMENTS
import com.huiwan.littlegame.cocos.resCheck.checker.InGameResCheck
import com.huiwan.littlegame.cocos.resCheck.checker.MatchResCheck
import com.huiwan.littlegame.cocos.resCheck.loaderFactory.CocosResLoadConfig
import com.huiwan.littlegame.cocos.resCheck.loaderFactory.LoaderFactory
import com.three.http.core.KtResult
import com.three.http.core.KtResultFailed
import com.three.http.core.KtResultSuccess
import com.wejoy.littlegame.ILittleGameApi
import com.wepie.liblog.main.HLog
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.job
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import kotlin.coroutines.coroutineContext


/**
 * 增量检查cocos资源
 */
class CocoResLoader(
    val gameType: Int,
    loadConfig: CocosResLoadConfig,
) {
    private val tag = "CocoResLoader"

    private val inGameResCheck: InGameResCheck by lazy {
        val isWebGame = ApiService.of(ILittleGameApi::class.java).isCocosWebGame()
        InGameResCheck(gameType, isWebGame)
    }
    private val matchResChecker: MatchResCheck by lazy {
        MatchResCheck(gameType)
    }
    private var needUpdateGameRes = false
    private var needUpdateMatch = false
    private val inGameResLoaderFactory: LoaderFactory<InGameResCheck> = loadConfig.inGameResFactory
    private val matchResLoaderFactory: LoaderFactory<MatchResCheck> =
        loadConfig.matchResLoadWrapperFactory
    private val loadingWatcher: ICocosResLoadingWatcher? = loadConfig.loadingWatcher
    private val updateMutex: Mutex = loadConfig.updateMutexProvider.getMutex(gameType)
    suspend fun checkRes(
        @MainThread
        onMatchResNeedUpdate: (() -> Unit)?
    ): KtResult<String> {
        log("start checkRes")
        return runWithCatch {
            if (checkIsLocalResOk()) {
                log("checkRes version Ok")
                return@runWithCatch KtResultSuccess(RES_VERSION_MEET_REQUIREMENTS)
            }
            bindWatcherCoroutineJob()
            notifyResNeedUpdate(onMatchResNeedUpdate)
            initLoadingWatcherState()
            runInIoIfWithMutex {
                log("in mutex before update")
                if (checkIsLocalResOk()) {
                    log("checkRes version Ok in mutex")
                    return@runInIoIfWithMutex KtResultSuccess(RES_VERSION_MEET_REQUIREMENTS)
                }
                startUpdate(matchResChecker, inGameResCheck)
            }
        }
    }

    private suspend fun runWithCatch(
        task: suspend () -> KtResult<String>
    ): KtResult<String> {
        return kotlin.runCatching {
            task()
        }.getOrElse { e ->
            log("checkRes exception :$e")
            if (e is CancellationException) {
                throw e
            }
            return KtResultFailed<String>(-1, e.message.toString()).also {
                log("update result $it")
            }
        }
    }

    private suspend fun runInIoIfWithMutex(task: suspend () -> KtResult<String>): KtResult<String> {
        return withContext(Dispatchers.IO) {
            updateMutex.withLock {
                task()
            }
        }
    }

    private suspend fun bindWatcherCoroutineJob() {
        loadingWatcher?.let { watcher ->
            coroutineContext.job.invokeOnCompletion {
                watcher.cancel()
            }
        }
    }

    private fun notifyResNeedUpdate(onMatchResNeedUpdate: (() -> Unit)?) {
        if (needUpdateMatch) {
            onMatchResNeedUpdate?.invoke()
        }
    }

    private fun initLoadingWatcherState() {
        loadingWatcher?.let {
            it.initMatchResIsNeedLoad(needUpdateMatch)
            it.initGameResIsNeedLoad(needUpdateGameRes)
        }
    }

    private suspend fun startUpdate(
        matchResChecker: MatchResCheck,
        inGameResCheck: InGameResCheck,
    ): KtResult<String> = coroutineScope {
        val matchResUpdater =
            matchResLoaderFactory.createLoaderWrapper(matchResChecker, loadingWatcher)
        val inGameResUpdater =
            inGameResLoaderFactory.createLoaderWrapper(inGameResCheck, loadingWatcher)
        val results = listOf(
            async {
                if (!needUpdateGameRes) {
                    return@async KtResultSuccess("")
                }
                inGameResUpdater.startUpdate()
            },
            async {
                if (!needUpdateMatch) {
                    return@async KtResultSuccess("")
                }
                matchResUpdater.startUpdate()
            }
        ).awaitAll()
        return@coroutineScope handleLoadResult(results[0], results[1]).also {
            log("update result $it")
        }
    }

    /**
     * @throws IllegalStateException
     */
    private suspend fun checkIsLocalResOk() = coroutineScope {
        val results = listOf(
            async(Dispatchers.IO) {
                matchResChecker.checkVersion()
            }, async(Dispatchers.IO) {
                inGameResCheck.checkVersion()
            }).awaitAll()
        needUpdateMatch = !results[0]
        needUpdateGameRes = !results[1]
        return@coroutineScope !needUpdateMatch && !needUpdateGameRes
    }

    private fun handleLoadResult(
        inGameResLoadResult: KtResult<String>,
        matchResLoadResult: KtResult<String>
    ): KtResult<String> {
        log("finish checkRes,gamResult:$inGameResLoadResult,matchResult:$matchResLoadResult")
        val isInGameResLoadSuccess = inGameResLoadResult is KtResultSuccess
        val isMatchResLoadSuccess = matchResLoadResult is KtResultSuccess
        updateLoadState(isInGameResLoadSuccess, isMatchResLoadSuccess)
        return if (isMatchResLoadSuccess && isInGameResLoadSuccess) {
            KtResultSuccess("")
        } else if (!isMatchResLoadSuccess) {
            matchResLoadResult
        } else {
            inGameResLoadResult
        }
    }

    private fun updateLoadState(isInGameResLoadSuccess: Boolean, isMatchResLoadSuccess: Boolean) {
        if (isInGameResLoadSuccess) {
            loadingWatcher?.notifyLoadInGameResResSuccess()
        } else {
            loadingWatcher?.notifyLoadInGameResFail()
        }
        if (isMatchResLoadSuccess) {
            loadingWatcher?.notifyLoadMatchResSuccess()
        } else {
            loadingWatcher?.notifyLoadMatchResFail()
        }
    }

    private fun log(msg: String) {
        HLog.d(tag, HLog.USR, "Thread:${Thread.currentThread().id}," + msg)
    }

}