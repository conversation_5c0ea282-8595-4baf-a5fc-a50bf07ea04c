package com.wepie.wespy.cocosnew.match.main.group;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.wepie.wespy.R;
import com.huiwan.configservice.editionentity.GameConfig;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2019-12-24.
 */
public class CocosGroupMatchItemImageAdapter extends RecyclerView.Adapter<CocosGroupMatchItemImageAdapter.ItemViewHolder> {

    private Context mContext;
    private LayoutInflater mLayoutInflater;
    private List<GameConfig.MatchInfo> dataList = new ArrayList<>();
    private CocosModeCallback cocosModeCallback;

    public CocosGroupMatchItemImageAdapter(Context mContext) {
        this.mContext = mContext;
        mLayoutInflater = LayoutInflater.from(mContext);
    }

    public void refresh(List<GameConfig.MatchInfo> dataList) {
        this.dataList.clear();
        this.dataList.addAll(dataList);
        notifyDataSetChanged();
    }

    public List<GameConfig.MatchInfo> getDataList() {
        return dataList;
    }

    @NonNull
    @Override
    public ItemViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new ItemViewHolder(mLayoutInflater.inflate(R.layout.cocos_match_group_item_image, viewGroup, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ItemViewHolder matchViewHolder, int position) {
        matchViewHolder.refresh(dataList.get(position), new CocosModeCallback() {
            @Override
            public void onClickItem(GameConfig.MatchInfo matchInfo) {
                if (cocosModeCallback != null) cocosModeCallback.onClickItem(matchInfo);
            }

            @Override
            public void onClickCreate(GameConfig.MatchInfo matchInfo) {
                if (cocosModeCallback != null) cocosModeCallback.onClickCreate(matchInfo);
            }
        });
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public void setCocosModeCallback(CocosModeCallback cocosModeCallback) {
        this.cocosModeCallback = cocosModeCallback;
    }

    static class ItemViewHolder extends RecyclerView.ViewHolder {

        private View rootView;
        private ImageView matchIv;

        ItemViewHolder(@NonNull View itemView) {
            super(itemView);
            rootView = itemView;
            matchIv = itemView.findViewById(R.id.image_view);
        }

        public void refresh(final GameConfig.MatchInfo matchGroupInfo, final CocosModeCallback cocosModeCallback) {
            ImageLoaderUtil.loadNormalImage(matchGroupInfo.getImgUrl(), matchIv);
            rootView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (cocosModeCallback != null) cocosModeCallback.onClickItem(matchGroupInfo);
                }
            });
        }
    }
}
