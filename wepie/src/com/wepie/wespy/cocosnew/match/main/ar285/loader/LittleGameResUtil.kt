package com.wepie.wespy.cocosnew.match.main.ar285.loader

import android.content.Context
import android.os.SystemClock
import com.huiwan.configservice.ConfigHelper
import com.wepie.liblog.main.HLog
import com.wepie.wespy.cocosnew.CocosLaunchTrack
import com.wepie.wespy.cocosnew.cocosTrackLaunch
import com.wepie.wespy.cocosnew.cocosTrackLaunchReset
import com.wepie.wespy.cocosnew.match.main.ar285.bean.LittleGameStartConfig
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo

object LittleGameResUtil {
    const val TAG = "LittleGameResUtil"


    private fun selectLoader(startConfig: LittleGameStartConfig): LittleGameResLoader? {
        val config = ConfigHelper.getInstance().getGameConfig(startConfig.gameType)
        val home = config.generateHome
        if (home != null) {
            return if (home.metaData?.isUnity == true) {
                throw IllegalStateException("not support unity")
            } else {
                HLog.d(TAG, HLog.USR, "home not null load cocos")
                LittleGameResLoaderCocosByIncCheck(startConfig)
            }
        } else {
            HLog.d(TAG, HLog.USR, "home null load cocos")
            return LittleGameResLoaderCocosByIncCheck(startConfig)
        }
    }


    fun checkLoadRes(
        context: Context,
        gameType: Int,
        trackScene: String,
        next: ((cocos: Boolean) -> Unit)?
    ) {
        val startConfig =
            LittleGameStartConfig(VoiceRoomInfo(), gameType, "", trackScene = trackScene)
        checkLoadRes(context, startConfig, next)
    }

    fun checkLoadRes(
        context: Context,
        gameType: Int,
        isMatchFailedVersionLow: Boolean,
        trackScene: String,
        next: ((cocos: Boolean) -> Unit)?
    ) {
        val startConfig = LittleGameStartConfig(
            VoiceRoomInfo(),
            gameType,
            "",
            isMatchFailedVersionLow = isMatchFailedVersionLow,
            trackScene = trackScene
        )
        checkLoadRes(context, startConfig, next)
    }

    /**
     * start config 中 unity scene 可能发生变化
     */
    fun checkLoadRes(
        context: Context,
        startConfig: LittleGameStartConfig,
        next: ((cocos: Boolean) -> Unit)?
    ) {
        val start = SystemClock.elapsedRealtime()
        HLog.d(TAG, HLog.USR, "checkLoadRes ${startConfig.gameType}")
        val loader = selectLoader(startConfig) ?: return

        LittleGameResLoadDialog().showDialog(context, loader, startConfig)
        loader.observer = {
            val status = loader.loadingState.value
            val curStatus = status.curStatus
            val success = loader.loadingState.value.curStatus.isSuccess()
            if (success) {
                val spentInMs = SystemClock.elapsedRealtime() - start
                val size = status.gameTotal + status.matchTotal
                cocosTrackLaunch(CocosLaunchTrack.CheckUpdateResSuccess(startConfig.gameType, spentInMs.toInt(), size.toInt()))
                next?.invoke(loader is LittleGameResLoaderCocosByIncCheck)
            }
            if (curStatus == LittleGameResLoader.CurStatus.Failed || curStatus == LittleGameResLoader.CurStatus.UnpackFailed) {
                cocosTrackLaunchReset()
            }
        }
    }
}