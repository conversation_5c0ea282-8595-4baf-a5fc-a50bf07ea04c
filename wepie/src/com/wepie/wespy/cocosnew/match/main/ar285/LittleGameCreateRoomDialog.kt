package com.wepie.wespy.cocosnew.match.main.ar285

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.huiwan.base.util.ContextUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.editionentity.GameConfig
import com.huiwan.configservice.editionentity.GameConfig.MatchInfo
import com.huiwan.widget.inflate
import com.huiwan.widget.rv.BaseRvAdapter
import com.huiwan.widget.rv.RVHolder
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.lib.share.SpaceItemDecoration
import com.wepie.wespy.R

class LittleGameCreateRoomDialog : BottomSheetDialogFragment() {
    private var gameType: Int = 0
    private var hasCocosGuide = false

    private lateinit var rv: RecyclerView
    private lateinit var sheetBar: View
    private val eventHandler = LittleGameEventHandler()
    private val entryAdapter = CreateItemAdapter(this, eventHandler)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.little_game_main_create_dialog_view, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val config = ConfigHelper.getInstance().getGameConfig(gameType)
        if (config == null || config.gameType <= 0) {
            dismissAllowingStateLoss()
            return
        }
        eventHandler.gameType = gameType
        initViews(view)
        update(config)
    }

    private fun initViews(v: View) {
        rv = v.findViewById(R.id.rv)
        sheetBar = v.findViewById(R.id.sheet_bar)
    }

    private fun update(config: GameConfig) {
        rv.layoutManager = LinearLayoutManager(context)
        rv.adapter = entryAdapter
        entryAdapter.refresh(config.matchInfoListForCreate.filter { it.coin == 0 })
        entryAdapter.hasCocosGuide = hasCocosGuide
        rv.addItemDecoration(SpaceItemDecoration(ScreenUtil.dip2px(12f), 0, 0, 0))
    }

    fun showDialog(context: Context, gameType: Int, hasCocosGuide: Boolean) {
        this.gameType = gameType
        this.hasCocosGuide = hasCocosGuide
        setStyle(STYLE_NORMAL, R.style.TransparentBottomSheetStyle)
        val activity = ContextUtil.getFragmentActivityFromContext(context)
        if (activity != null) {
            show(activity.supportFragmentManager, "LittleGameCreate")
        }
    }

    class Holder(v: View) : RVHolder(v) {
        val titleTv: TextView = v.findViewById(R.id.title_tv)
        val needTv: TextView = v.findViewById(R.id.coin_need_tv)
    }

    class CreateItemAdapter(
        val dialog: LittleGameCreateRoomDialog,
        val eventHandler: LittleGameEventHandler
    ) :
        BaseRvAdapter<MatchInfo, Holder>() {

        var hasCocosGuide = false

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            val v = parent.inflate(R.layout.little_game_main_create_dialog_item_view)
            return Holder(v)
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            val data = dataList[position]
            holder.titleTv.text = data.title
            holder.needTv.text = data.coinDesc.toString()
            holder.itemView.setOnClickListener {
                dialog.dismiss()
                eventHandler.onReqCreate(it, data, hasCocosGuide)
                createRoomClickTrack(eventHandler.gameType, data, TrackSource.CREATE_FRIEND_ROOM)
            }
        }
    }
}