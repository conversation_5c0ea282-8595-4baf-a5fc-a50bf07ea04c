package com.wepie.wespy.cocosnew.match.main.ar285

import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JumpDetails
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.LayoutItem
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.PersonStyle

/**
 * 小游戏主页界面信息数据
 */
data class LittleGameViewState(
    /**
     * 用户信息区
     */
    val userInfo: UserInfo = UserInfo(
        emptyList(), PersonStyle(), emptyList(),
        VsCenterLittleGameConfig.PersonCurrencyStyle()
    ),
    /**
     * 主页背景 url
     */
    val bg: String = "",
    /**
     * 中间区域的游戏操作项
     */
    val centerInfo: PageCenterInfo = PageCenterInfo(emptyList()),

    /**
     * 底部 开始【英左阿右】的 icon
     */
    val start: OpInfo = OpInfo(emptyList()),

    /**
     *  底部 结束【英右阿左】的 icon
     */
    val end: OpInfo = OpInfo(emptyList()),

    val topEnd: OpInfo = OpInfo(emptyList()),

    /**
     * 标题
     */
    val title: String = ""
) {

    data class UserInfoItem(val label: String, val value: String)

    data class UserCurrencyItem(
        val type: String,
        val value: Long,
        val url: String,
        val plusUrl: String
    )

    data class UserInfo(
        val list: List<UserInfoItem>,
        val style: PersonStyle,
        val currencyList: List<UserCurrencyItem>,
        val currencyStyle: VsCenterLittleGameConfig.PersonCurrencyStyle
    )

    data class GameItemInfo(
        val iconUrl: String,
        val jumpType: Int,
        val jumpDetails: JumpDetails,
        /**
         * 两个一行
         */
        val catTwo: Boolean = false,
        val name: String = "",
        val reportComment: String = "",
        var isRecommend: Boolean = false
    )

    data class PageCenterInfo(val list: List<GameItemInfo>)
    data class OpInfo(val list: List<GameItemInfo>)
}