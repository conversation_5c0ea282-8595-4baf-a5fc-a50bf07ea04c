package com.wepie.wespy.cocosnew.match.main.ar285.loader

import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.littlegame.cocos.resCheck.base.RES_VERSION_MEET_REQUIREMENTS
import com.huiwan.littlegame.cocos.resCheck.loaderFactory.CocosResLoadConfig
import com.three.http.core.KtResult
import com.three.http.core.KtResultFailed
import com.three.http.core.KtResultSuccess
import com.three.http.core.KtResultUnpackFailed
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.cocosnew.match.main.ar285.bean.LittleGameStartConfig
import kotlinx.coroutines.delay

class LittleGameResLoaderCocosByIncCheck(val startConfig: LittleGameStartConfig) :
    LittleGameResLoaderBase() {
    private val tag = "LittleGameResLoaderCocosByIncCheck"
    override suspend fun load() {
        val gameType: Int = startConfig.gameType
        debug("start load, gameType=$gameType")
        initGame(gameType)
        loading.value = loading.value.copy(curStatus = LittleGameResLoader.CurStatus.Init)
        CocosGameSilentPreloader.cancel(startConfig.gameType)
        handleKtResult(
            CocoResLoader(
                gameType,
                CocosResLoadConfig(LoadingStateWatcher(loading))
            ).checkRes(null)
        )
    }

    private suspend fun handleKtResult(res: KtResult<String>) {
        when (res) {
            is KtResultSuccess -> {
                delay(20L)
                invokeSuccess((SuccessInfo(res.data == RES_VERSION_MEET_REQUIREMENTS)))
            }

            is KtResultFailed -> {
                HLog.e(tag, HLog.USR, "handleCheckResResult :$res")
                val desc = ResUtil.getStr(R.string.cocos_update_down_fail)
                ToastUtil.show(desc)
                invokeFailed()
            }

            is KtResultUnpackFailed -> {
                invokeUnpackFailed()
            }

            else -> {}
        }
    }
}