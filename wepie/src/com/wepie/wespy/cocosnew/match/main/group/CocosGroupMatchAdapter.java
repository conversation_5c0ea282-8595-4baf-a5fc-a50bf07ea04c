package com.wepie.wespy.cocosnew.match.main.group;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.R;
import com.huiwan.configservice.editionentity.GameConfig;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.huiwan.base.util.ColorUtil;
import com.huiwan.base.util.ToastUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2019-12-24.
 */
public class CocosGroupMatchAdapter extends RecyclerView.Adapter<CocosGroupMatchAdapter.MatchViewHolder> {

    private Context mContext;
    private LayoutInflater mLayoutInflater;
    private List<GameConfig.MatchGroupInfo> dataList = new ArrayList<>();
    private CocosModeCallback cocosModeCallback;

    public CocosGroupMatchAdapter(Context mContext) {
        this.mContext = mContext;
        mLayoutInflater = LayoutInflater.from(mContext);
    }

    public void refresh(List<GameConfig.MatchGroupInfo> dataList) {
        this.dataList.clear();
        this.dataList.addAll(dataList);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public MatchViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new MatchViewHolder(mLayoutInflater.inflate(R.layout.cocos_match_group_item, viewGroup, false));
    }

    @Override
    public void onBindViewHolder(@NonNull MatchViewHolder matchViewHolder, int position) {
        matchViewHolder.refresh(dataList.get(position), new CocosModeCallback() {
            @Override
            public void onClickItem(GameConfig.MatchInfo matchInfo) {
                if (cocosModeCallback != null) cocosModeCallback.onClickItem(matchInfo);
            }

            @Override
            public void onClickCreate(GameConfig.MatchInfo matchInfo) {
                if (cocosModeCallback != null) cocosModeCallback.onClickCreate(matchInfo);
            }
        });
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public void setCocosModeCallback(CocosModeCallback cocosModeCallback) {
        this.cocosModeCallback = cocosModeCallback;
    }

    static class MatchViewHolder extends RecyclerView.ViewHolder {

        private View rootView;
        private RecyclerView recyclerView;
        private TextView titleTv, subTitleTv;
        private ImageView createRoomIv;
        private CocosGroupMatchItemImageAdapter adapter;
        private int curPosition;

        MatchViewHolder(@NonNull View itemView) {
            super(itemView);
            rootView = itemView;
            recyclerView = itemView.findViewById(R.id.recycle_view);
            titleTv = itemView.findViewById(R.id.title_tv);
            subTitleTv = itemView.findViewById(R.id.sub_title_tv);
            createRoomIv = itemView.findViewById(R.id.create_room_iv);
            final LinearLayoutManager linearLayoutManager = new LinearLayoutManager(itemView.getContext(), LinearLayoutManager.HORIZONTAL, false);
            recyclerView.setLayoutManager(linearLayoutManager);
            final PagerSnapHelper snapHelper = new PagerSnapHelper();
            snapHelper.attachToRecyclerView(recyclerView);
            adapter = new CocosGroupMatchItemImageAdapter(itemView.getContext());
            recyclerView.setAdapter(adapter);
            recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
                @Override
                public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                    super.onScrollStateChanged(recyclerView, newState);

                }

                @Override
                public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                    super.onScrolled(recyclerView, dx, dy);
                    curPosition = linearLayoutManager.findFirstCompletelyVisibleItemPosition();
                }
            });
        }

        public void refresh(GameConfig.MatchGroupInfo matchGroupInfo, final CocosModeCallback callback) {
            adapter.refresh(matchGroupInfo.getMatchInfoListForMatch());
            adapter.setCocosModeCallback(callback);
            titleTv.setText(matchGroupInfo.getTitle());
            titleTv.setTextColor(ColorUtil.getColor(matchGroupInfo.getTitleColor()));
            subTitleTv.setText(matchGroupInfo.getSubTitle());
            subTitleTv.setTextColor(ColorUtil.getColor(matchGroupInfo.getSubTitleColor()));
            ImageLoaderUtil.loadNormalImage(matchGroupInfo.getButtonUrl(), createRoomIv);

            createRoomIv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (curPosition >= 0 && curPosition < adapter.getItemCount()) {
                        GameConfig.MatchInfo matchInfo = adapter.getDataList().get(curPosition);
                        if (callback != null) callback.onClickCreate(matchInfo);
                    } else {
                        ToastUtil.debugShow(ResUtil.getStr(R.string.cocos_match_data_error_tips));
                    }
                }
            });
        }
    }
}
