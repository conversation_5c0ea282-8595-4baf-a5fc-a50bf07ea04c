package com.wepie.wespy.cocosnew.match.main

import android.os.Build
import android.os.Bundle
import androidx.lifecycle.Observer
import com.huiwan.base.util.InitializerManagerUtils
import com.huiwan.component.activity.BaseActivity
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.editionentity.GamesConfig
import com.huiwan.constants.GameType
import com.huiwan.constants.IntentConfig
import com.wepie.libimageloader.WpImageLoader
import com.wepie.liblog.main.HLog
import com.wepie.startup.InitializerManager
import com.wepie.wespy.R
import com.wepie.wespy.cocosnew.match.main.ar285.LittleGameAr285View
import com.wepie.wespy.cocosnew.match.main.ar285.littleGameMainShowTrack
import com.wepie.wespy.cocosnew.match.main.group.LittleGameGroupMainView
import com.wepie.wespy.cocosnew.match.main.normal.LittleGameNormalMainView
import com.wepie.wespy.cocosnew.match.matching.CocosMatchActivity

/**
 * 小游戏从 阿语服 2.8.5 版本开始支持 unity 游戏
 * https://modao.cc/app/71afgGtHrlwe7tdYIh8uNl#screen=slawbxa2gm8z4xo
 */
class CocosMainActivity : BaseActivity() {
    private var littleMainView: ILittleGameMainView? = null

    /**
     * 记录是否玩过游戏，玩过游戏时，设置为 true
     * 回到本页面时，刷新排行榜
     */
    var playedGame: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.R) {
            setTheme(R.style.AppTheme_Cocos_NoAnim)
        }
        super.onCreate(savedInstanceState)
        WpImageLoader.load(CocosMatchActivity.OUTER_CIRCLE_URL, null)

        val gameType = intent.getIntExtra(IntentConfig.GAME_TYPE, -1)
        val liveData = ConfigHelper.getInstance().gameConfigLiveData
        liveData.observe(this, object : Observer<GamesConfig?> {
            override fun onChanged(gamesConfig: GamesConfig?) {
                val homePageView = getHomePageView(gameType)
                setContentView(homePageView.view)
                littleGameMainShowTrack(gameType)
                littleMainView = homePageView
                HLog.d(TAG, HLog.USR, "onCreate, gameType=$gameType")
                liveData.removeObserver(this)
            }
        })
    }

    private fun getHomePageView(gameType: Int): ILittleGameMainView {
        val contentView: ILittleGameMainView
        val config = ConfigHelper.getInstance().gameConfig.getGameConfig(gameType)
        if (config.generateHome != null) {
            contentView = LittleGameAr285View(this, null)
            contentView.setGameType(gameType)
        } else {
            val isMatchGroupStyle =
                ConfigHelper.getInstance().gameConfig.isMatchGroupStyle(gameType)
            contentView = if (isMatchGroupStyle) {
                LittleGameGroupMainView(this, gameType);
            } else {
                LittleGameNormalMainView(this, gameType);
            }
        }
        return contentView
    }

    override fun onResume() {
        super.onResume()
        littleMainView?.onResume()
    }

    override fun onPause() {
        super.onPause()
        littleMainView?.onPause()
    }

    override fun onDestroy() {
        super.onDestroy()
        littleMainView?.onDestroy()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.clear()
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        savedInstanceState.clear()
        super.onRestoreInstanceState(savedInstanceState)
    }

    override fun filterStartup() {
        InitializerManagerUtils.wait(InitializerManager.FILTER_FOUR);
    }

    companion object {
        private const val TAG = "CocosMainActivity"
    }
}