package com.wepie.wespy.cocosnew.match.main.normal;

import android.app.Activity;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.viewpager2.widget.MarginPageTransformer;
import androidx.viewpager2.widget.ViewPager2;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.constants.GameType;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.entity.CocosUserWeeklyInfo;
import com.huiwan.widget.CustomCircleImageView;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.main.CocosMainPresenter;
import com.wepie.wespy.cocosnew.match.main.ILittleGameMainView;
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.module.home.main.view.PercentRelativeLayout;

import java.util.List;

/**
 * Created by bigwen on 2019-12-24.
 */
public class LittleGameNormalMainView extends FrameLayout implements ILittleGameMainView {

    private final Context mContext;
    private static final int RANK_MARK = 100;
    private CocosMainPresenter presenter;
    private ViewPager2 typePager;
    private TextView titleTv;
    private ImageView mainBgIv;
    private ImageView mainFrontIv;
    private ImageView inviteFriendBtn;
    private TextView scoreTitleTv;
    private ImageView customsIv;
    private ImageView rankIv;
    private ImageView quickStartIv;
    private ImageView backIv;
    private ImageView ruleIv;
    private ImageView createRoomIv;
    private PercentRelativeLayout typeContainer;
    private CocosNormalMatchPagerAdapter adapter;
    private final int mGameType;

    public LittleGameNormalMainView(Context context, int gameType) {
        super(context);
        mContext = context;
        mGameType = gameType;
        init(gameType);
    }

    private void init(int gameType) {
        LayoutInflater.from(mContext).inflate(R.layout.cocos_normal_main_view, this);
        presenter = new CocosMainPresenter(this, gameType);
        initView();
        initEvent();
        presenter.registerEventBus();
        presenter.initData();
    }

    private final View.OnClickListener onClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            if (v == backIv) {
                Activity activity = ContextUtil.getActivityFromContext(mContext);
                if (activity != null) {
                    activity.finish();
                }
            } else if (v == rankIv) {
                presenter.clickRankView();
            } else if (v == quickStartIv) {
                int currentPosition = typePager.getCurrentItem();
                if (currentPosition < adapter.getItemCount()) {
                    presenter.clickQuickStart(getMatchInfoByPosition(currentPosition));
                } else {
                    ToastUtil.show(ResUtil.getStr(R.string.cocos_match_data_error_tips));
                }
            } else if (v == inviteFriendBtn) {
                int currentPosition = typePager.getCurrentItem();
                if (currentPosition < adapter.getItemCount()) {
                    presenter.clickInviteFriend(getMatchInfoByPosition(currentPosition));
                } else {
                    ToastUtil.show(ResUtil.getStr(R.string.cocos_match_data_error_tips));
                }
            } else if (v == createRoomIv) {
                int currentPosition = typePager.getCurrentItem();
                if (currentPosition < adapter.getItemCount()) {
                    presenter.clickCreateRoom(getMatchInfoByPosition(currentPosition));
                } else {
                    ToastUtil.show(ResUtil.getStr(R.string.cocos_match_data_error_tips));
                }

            } else if (v == ruleIv) {
                presenter.clickGuideDialog();
            }
        }
    };

    private void initView() {
        createRoomIv = findViewById(R.id.ice_ball_create_room);
        backIv = findViewById(R.id.ice_ball_main_back_img);
        ruleIv = findViewById(R.id.ice_ball_rule_iv);
        titleTv = findViewById(R.id.ice_ball_main_title);
        rankIv = findViewById(R.id.ice_ball_rank_iv);
        mainBgIv = findViewById(R.id.main_bg_iv);
        mainFrontIv = findViewById(R.id.main_front_iv);
        inviteFriendBtn = findViewById(R.id.invite_social_friend_btn);
        scoreTitleTv = findViewById(R.id.ice_ball_win_times_this_week_title_tv);
        customsIv = findViewById(R.id.cocos_customs_iv);
        quickStartIv = findViewById(R.id.ice_ball_quick_start);
        CustomCircleImageView imageView = findViewById(R.id.ice_ball_header_img);
        typeContainer = findViewById(R.id.ice_ball_room_type_container);
        typePager = findViewById(R.id.ice_ball_room_type_view_pager);
        adapter = new CocosNormalMatchPagerAdapter(getContext());
        typePager.setAdapter(adapter);
        MarginPageTransformer pageTransformer = new MarginPageTransformer(ScreenUtil.dip2px(40.0F));
        typePager.setPageTransformer(pageTransformer);

        HeadImageLoader.loadCircleHeadImage(LoginHelper.getHeadUrl(), imageView);
        typeContainer.setHorizontalFadingEdgeEnabled(false);
        typeContainer.setVerticalFadingEdgeEnabled(false);
        typePager.setHorizontalFadingEdgeEnabled(false);
        typePager.setVerticalFadingEdgeEnabled(false);

        backIv.setOnClickListener(onClickListener);
        rankIv.setOnClickListener(onClickListener);
        quickStartIv.setOnClickListener(onClickListener);
        inviteFriendBtn.setOnClickListener(onClickListener);
        createRoomIv.setOnClickListener(onClickListener);
        ruleIv.setOnClickListener(onClickListener);
    }

    private GameConfig.MatchInfo getMatchInfoByPosition(int i) {
        List<GameConfig.MatchInfo> matchInfoList = ConfigHelper.getInstance().getGameConfig().getMatchInfo(presenter.getGameType());
        GameConfig.MatchInfo matchInfo;
        if (i < matchInfoList.size()) {
            matchInfo = matchInfoList.get(i);
        } else {
            matchInfo = new GameConfig.MatchInfo();
        }
        return matchInfo;
    }

    @Override
    public void refreshScore(int mode) {
        if (mode == GameConfig.PLAY_MODE_COOPERATE) {
            scoreTitleTv.setText(ResUtil.getStr(R.string.cocos_week_best_grade));
            customsIv.setVisibility(View.VISIBLE);
            customsIv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    presenter.clickCustomWeb();
                }
            });
        } else {
            scoreTitleTv.setText(getContext().getString(R.string.ice_ball_main_win_week));
            customsIv.setVisibility(View.GONE);
        }
    }

    @Override
    public void onResume() {
        presenter.updatePersonRankIfNecessary();
    }

    @Override
    public void onDestroy() {
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        presenter.registerEventBus();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        presenter.unRegisterEventBus();
    }

    @Override
    public void refreshTitle(String gameName) {
        titleTv.setText(gameName);
    }

    @Override
    public void showPersonRank(CocosUserWeeklyInfo weeklyInfo) {
        TextView weekTv = findViewById(R.id.ice_ball_win_times_this_week_tv);
        TextView rankTv = findViewById(R.id.ice_ball_rank_tv);
        LinearLayout starLay = findViewById(R.id.this_week_star_lay);
        TextView starNumTv = findViewById(R.id.rank_star_num_tv);
        TextView totalScore = findViewById(R.id.ice_ball_rank_title_tv);

        String strRank;
        if (weeklyInfo.getRank() > RANK_MARK) {
            if (weeklyInfo.getScore() == 0) {
                strRank = "--";
            } else {
                strRank = "100+";
            }
        } else if (weeklyInfo.getRank() <= 0) {
            strRank = "--";
        } else {
            strRank = String.valueOf(weeklyInfo.getRank());
        }

        if (mGameType == GameType.GAME_TYPE_H_AND_B) {
            totalScore.setText(R.string.common_total_points);
            String strScore;
            if (weeklyInfo.getScore() >= 0) {
                strScore = String.valueOf(weeklyInfo.getScore());
            } else {
                strScore = "--";
            }
            String weekWin = String.valueOf(weeklyInfo.getBbWinCount());
            starLay.setVisibility(View.GONE);
            weekTv.setText(weekWin);
            rankTv.setText(strScore);
        } else if (presenter.getPlayMode() == GameConfig.PLAY_MODE_COOPERATE) {
            if (weeklyInfo.getStar() <= 0 || weeklyInfo.getLevel() <= 0) {
                starLay.setVisibility(View.GONE);
                weekTv.setText("--");
                rankTv.setText(strRank);
            } else {
                starLay.setVisibility(View.VISIBLE);
                rankTv.setText(strRank);
                starNumTv.setText(String.valueOf(weeklyInfo.getStar()));
                weekTv.setText(ResUtil.getStr(R.string.level_x_1, weeklyInfo.getLevel()));
            }
        } else {
            String strScore = String.valueOf(weeklyInfo.getScore());
            starLay.setVisibility(View.GONE);
            weekTv.setText(strScore);
            rankTv.setText(strRank);
        }
    }

    @Override
    public Context getViewContext() {
        return mContext;
    }

    private void initEvent() {
        typeContainer.setOnTouchListener((v, event) -> {
            v.performClick();
            return typePager.dispatchTouchEvent(event);
        });

        typePager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }

            @Override
            public void onPageSelected(int position) {
                presenter.updateSelectedIndex(position);
            }
        });
    }

    public void refreshRes(int gameType) {
        CocosGameConfigUtil.loadResBgAsync(mainFrontIv, gameType, CocosGameConfigUtil.START_FRONT);
        CocosGameConfigUtil.loadResBgAsync(mainBgIv, gameType, CocosGameConfigUtil.START_BACKGROUND);
        CocosGameConfigUtil.loadResAsync(createRoomIv, gameType, CocosGameConfigUtil.CREATE_ROOM, true);
        CocosGameConfigUtil.loadResAsync(quickStartIv, gameType, CocosGameConfigUtil.START, true);
        if (CocosGameConfigUtil.isResFileExist(gameType, CocosGameConfigUtil.INVITE_SOCIAL_FRIEND_BTN)) {
            CocosGameConfigUtil.loadResAsync(inviteFriendBtn, gameType, CocosGameConfigUtil.INVITE_SOCIAL_FRIEND_BTN);
        }
        CocosGameConfigUtil.loadResAsync(rankIv, gameType, CocosGameConfigUtil.LEADER_BOARD);

        final List<GameConfig.MatchInfo> matchInfoList = ConfigHelper.getInstance().getGameConfig().getMatchInfo(gameType);
        if (!matchInfoList.isEmpty()) {
            adapter.refresh(matchInfoList, (matchInfo, index) -> presenter.clickQuickStart(matchInfo));
            typePager.setOffscreenPageLimit(matchInfoList.size());
            int index = presenter.getLastPlay();
            if (index < adapter.getItemCount()) {
                typePager.setCurrentItem(index);
            }
        }
    }

    @Override
    public View getView() {
        return this;
    }
}