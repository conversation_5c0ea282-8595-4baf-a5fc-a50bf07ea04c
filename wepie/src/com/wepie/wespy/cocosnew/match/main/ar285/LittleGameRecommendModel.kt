package com.wepie.wespy.cocosnew.match.main.ar285

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.huiwan.base.util.JsonUtil
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig.JUMP_TYPE_MATCH
import com.huiwan.user.LoginHelper
import com.three.http.core.KtResultSuccess
import com.wepie.liblog.main.HLog
import kotlinx.coroutines.launch
import org.json.JSONArray
import org.json.JSONObject

class LittleGameRecommendModel(app: Application) : AndroidViewModel(app) {

    val matchItemListLiveData = MutableLiveData(emptyList<LittleGameViewState.GameItemInfo>())

    var gameType: Int = 0

    fun updateRecommendMatchItem() {
        updateRecommendMatchItem(matchItemListLiveData.value ?: emptyList())
    }

    fun updateRecommendMatchItem(gameItemList: List<LittleGameViewState.GameItemInfo>) {
        if (gameType == 0 || gameItemList.isEmpty()) {
            return
        }

        var matchList = ""
        try {
            val jsonArray = JSONArray()
            for (gameItemInfo in gameItemList) {
                if (gameItemInfo.jumpType == JUMP_TYPE_MATCH) {
                    val jsonItem = JsonUtil.toJsonString(gameItemInfo.jumpDetails.matchItem)
                    jsonArray.put(JSONObject(jsonItem))
                }
            }
            matchList = jsonArray.toString()
        } catch (e: Exception) {
            HLog.d(TAG, "matchList parse error: {}", e.message)
            return
        }

        if (matchList.isEmpty()) {
            return
        }
        viewModelScope.launch {
            val res = fetchRecommendMatchItem(LoginHelper.getLoginUid(), matchList, gameType)
            if (res !is KtResultSuccess) {
                HLog.d(TAG, "fetch recommend match item fail: {}", res.failedDesc)
                return@launch
            }
            val recommendMatchItem = res.data.recommendMatchItem
            HLog.d(TAG, "fetch recommend match item success: {}", recommendMatchItem.descStr)
            for (gameItemInfo in gameItemList) {
                if (gameItemInfo.jumpType != JUMP_TYPE_MATCH) {
                    gameItemInfo.isRecommend = false
                    continue
                }
                val matchItem = gameItemInfo.jumpDetails.matchItem
                gameItemInfo.isRecommend = matchItem.betLevel == recommendMatchItem.betLevel
                        && matchItem.matchMode == recommendMatchItem.matchMode
                        && matchItem.roomMode == recommendMatchItem.roomMode
                        && matchItem.gameMode == recommendMatchItem.gameMode
                        && matchItem.currencyType == recommendMatchItem.currencyType
            }
            matchItemListLiveData.postValue(gameItemList)
        }
    }

    companion object {
        const val TAG = "LittleGameRecommendModel"
    }
}