package com.wepie.wespy.cocosnew.match.main;

import android.content.Context;
import android.view.View;

import com.huiwan.base.context.holder.ContextHolder;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.user.entity.CocosUserWeeklyInfo;
import com.wepie.wespy.helper.dialog.progress.IProgressDialog;

/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/11/16.
 *
 * 冰球主界面显示接口
 */

public interface ILittleGameMainView {

    default void setGameType(int gameType) {

    }

    void refreshTitle(String gameName);

    void showPersonRank(CocosUserWeeklyInfo cocosUserWeeklyInfo);

    Context getViewContext();

    void refreshRes(int gameType);

    void refreshScore(int mode);

    void onResume();

    void onDestroy();

    View getView();

    default void onPause() {

    }

    default void showLoading() {
        ContextHolder.of(getViewContext(), IProgressDialog.class).showProgressDialog("", true);
    }

    default void hideLoading() {
        ContextHolder.of(getViewContext(), IProgressDialog.class).hideProgressDialog();
    }

    default void showErrMsg(String msg) {
        ToastUtil.show(msg);
    }

}
