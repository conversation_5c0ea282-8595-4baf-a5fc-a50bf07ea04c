package com.wepie.wespy.cocosnew.match.main.ar285

import com.huiwan.base.util.StringUtil
import com.huiwan.configservice.editionentity.GameConfig.MatchInfo
import com.huiwan.lib.api.ApiService
import com.wepie.lib.api.plugins.track.TrackApi
import com.wepie.lib.api.plugins.track.TrackUtil
import com.wepie.liblog.main.HLog
import org.json.JSONObject

private const val TAG = "LittleGameTrackUtils"

const val SCREEN_NAME_RES_LOADING_DIALOG = "小游戏资源加载弹窗"
const val SCREEN_NAME_CREATE_TEAM_DIALOG = "小游戏创房选场弹窗"

const val SCENE_APP_MAIN = "APP首页"
const val SCENE_GAME_CENTER = "对战小游戏"
const val SCENE_FOLLOW = "跟房"
const val SCENE_CHAT = "聊天页"
const val SCENE_LITTLE_GAME_MAIN = "小游戏首页"
const val SCENE_USER_INFO_DETAIL = "个人主页"
const val SCENE_GAME_RECOVER = "恢复游戏"
const val SCENE_H5 = "H5"
const val GUIDE_STEP = "启动引导"

//非预加载
const val LOAD_TYPE_NOT_PRE_LOAD = 1

//预加载
const val LOAD_TYPE_PRE_LOAD = 2

fun showResLoadingDialogViewTrack(gameType: Int, totalSize: Long, trackScene: String) {
    JSONObject().apply {
        try {
            put(TrackUtil.trackValue().screenName, SCREEN_NAME_RES_LOADING_DIALOG)
            put("scene", trackScene)
            put("game_type", gameType)
            put("package_size", StringUtil.format("%.2f", totalSize / (1024 * 1024f)).toFloat())
            ApiService.of(TrackApi::class.java)
                .trackEvent(TrackUtil.trackValue().appViewScreen, this)
            HLog.d(TAG, HLog.USR, "showResLoadingDialogViewTrack success")
        } catch (e: Exception) {
            HLog.e(TAG, HLog.USR, "showResLoadingDialogViewTrack error: {}", e.message)
        }
    }
}

fun resDownloadStatusTrack(
    gameType: Int,
    scene: String,
    totalSize: Long,
    remainSize: Long,
    durInSec: Long,
    loadType: Int
) {
    val map = HashMap<String, Any>()
    map["game_type"] = gameType
    map["scene"] = scene
    map["package_size"] = StringUtil.format("%.2f", totalSize / (1024 * 1024f)).toFloat()
    map["package_remainsize"] = StringUtil.format("%.2f", remainSize / (1024 * 1024f)).toFloat()
    map["event_duration"] = durInSec
    map["load_type"] = loadType
    ApiService.of(TrackApi::class.java).trackEvent("UserDownloadStatus", map)
}

fun littleGameMainShowTrack(gameType: Int) {
    JSONObject().apply {
        try {
            put("game_type", gameType)
            put("screen_name", "游戏首页")
            ApiService.of(TrackApi::class.java)
                .trackEvent(TrackUtil.trackValue().appViewScreen, this)
            HLog.d(TAG, HLog.USR, "littleGameMainShowTrack success")
        } catch (e: Exception) {
            HLog.e(TAG, HLog.USR, "littleGameMainShowTrack error: {}", e.message)
        }
    }
}

fun funcClickTrack(screenName: String, gameType: Int, btnName: String, modeType: Int) {
    JSONObject().apply {
        try {
            put("game_type", gameType)
            put("screen_name", screenName)
            put("btn_name", btnName)
            put("mode_type", modeType)
            ApiService.of(TrackApi::class.java)
                .trackEvent(TrackUtil.trackValue().appClick, this)
            HLog.d(TAG, HLog.USR, "funcClickTrack success")
        } catch (e: Exception) {
            HLog.e(TAG, HLog.USR, "funcClickTrack error: {}", e.message)
        }
    }
}

fun createRoomClickTrack(gameType: Int, matchInfo: MatchInfo, scene: String) {
    JSONObject().apply {
        try {
            put("game_type", gameType)
            put("screen_name", SCREEN_NAME_CREATE_TEAM_DIALOG)
            // btn_name定义: betLevel_roomMode_gameMode_currencyType
            val btnName =
                "${matchInfo.betLevel}_${matchInfo.roomMode}_${matchInfo.gameMode}_${matchInfo.currencyType}"
            put("btn_name", btnName)
            put("mode_type", "$gameType${matchInfo.gameMode}")
            put("scene", scene)
            put("bet_level", matchInfo.betLevel)
            ApiService.of(TrackApi::class.java)
                .trackEvent(TrackUtil.trackValue().appClick, this)
            HLog.d(TAG, HLog.USR, "createRoomClickTrack success")
        } catch (e: Exception) {
            HLog.e(TAG, HLog.USR, "createRoomClickTrack error: {}", e.message)
        }
    }
}