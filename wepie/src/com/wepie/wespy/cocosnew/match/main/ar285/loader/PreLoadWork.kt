package com.wepie.wespy.cocosnew.match.main.ar285.loader

import android.content.Context
import android.os.SystemClock
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.huiwan.littlegame.cocos.CocosResMigration
import com.huiwan.littlegame.cocos.resCheck.base.RES_VERSION_MEET_REQUIREMENTS
import com.huiwan.littlegame.cocos.resCheck.loaderFactory.CocosResLoadConfig
import com.three.http.core.KtResult
import com.three.http.core.KtResultSuccess
import com.three.http.core.KtResultUnpackFailed
import com.wepie.lib.api.plugins.track.config.os.TrackString
import com.wepie.liblog.main.HLog
import com.wepie.wespy.cocosnew.match.main.ar285.LOAD_TYPE_PRE_LOAD
import com.wepie.wespy.cocosnew.match.main.ar285.resDownloadStatusTrack
import kotlinx.coroutines.CancellationException
import java.util.concurrent.atomic.AtomicLong

class PreLoadWork(context: Context, param: WorkerParameters) : CoroutineWorker(context, param) {
    private val gameType = param.inputData.getInt(WORK_PARAM_GAME_TYPE, 0)
    private val tag: String = "PreLoadWork_$gameType"
    private var startTime: AtomicLong = AtomicLong(0)

    override suspend fun doWork(): Result {
        log("try do work")
        if (gameType == 0) {
            log("game type is illegal")
            return Result.failure()
        }
        return runWithCatch {
            load()
        }
    }

    private suspend fun runWithCatch(action: suspend () -> Unit): Result {
        var hasException = false
        try {
            action()
        } catch (e: CancellationException) {
            hasException = true
            log("canceled")
        } catch (e: Exception) {
            hasException = true
            log("occur err:$e")
        }

        return if (hasException) Result.failure() else {
            log("load success")
            Result.success()
        }
    }


    private suspend fun load() {
        log("start preload in coroutine $gameType")
        CocosResMigration.migrateTo545()
        val watcher = CocoUpdateProgressWatcher()
        startTime.set(SystemClock.elapsedRealtime())
        val loadResult = CocoResLoader(
            gameType,
            CocosResLoadConfig(watcher)
        ).checkRes(null)
        trackUserDownloadStatusIfTotalSizeValid(loadResult, watcher)
        log("preload $gameType finished!,result :$loadResult")
    }

    private fun trackUserDownloadStatusIfTotalSizeValid(
        loadResult: KtResult<String>,
        watcher: CocoUpdateProgressWatcher
    ) {
        val totalSize = watcher.gameResTotal + watcher.matchResTotal
        trackUserDownloadStatus(
            loadResult,
            watcher.matchResCur + watcher.gameResCur,
            totalSize
        )
    }

    private fun trackUserDownloadStatus(
        loadResult: KtResult<String>,
        resCur: Long,
        resTotal: Long,
    ) {
        val endTime = SystemClock.elapsedRealtime() - startTime.get()
        loadResult.toScene()?.let {
            resDownloadStatusTrack(
                gameType, it, resTotal, resTotal - resCur, endTime / 1000,
                LOAD_TYPE_PRE_LOAD
            )
        }
    }

    private fun KtResult<String>.toScene(): String? {
        return when (this) {
            is KtResultSuccess -> {
                if (this.data == RES_VERSION_MEET_REQUIREMENTS) {
                    null
                } else {
                    TrackString.SCENE_DOWNLOAD_FINISH
                }
            }

            is KtResultUnpackFailed -> {
                TrackString.SCENE_UNPACK_FAIL
            }

            else -> {
                TrackString.SCENE_DOWNLOAD_FAIL
            }
        }
    }

    private fun log(msg: String) {
        HLog.d(tag, HLog.USR, msg)
    }

    companion object {
        const val WORK_PARAM_GAME_TYPE = "game_type"
        const val WORK_TAG = "preLoad_cocos_res_work"
    }

}