package com.wepie.wespy.cocosnew.match.main.ar285.bean

import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo

data class LittleGameStartConfig(
    val voiceRoomInfo:VoiceRoomInfo,
    val gameType:Int,
    /**
     * 小游戏开始时，游戏内需要使用的信息
     */
    val beforeGameStart:String,
    /**
     * 是否观战者
     */
    val isWatcher:Boolean = false,
    /**
     * 被观战的人的 uid
     */
    val watcherTargetUid:Int = 0,
    /**
     * 是否是恢复游戏场景。
     */
    val isRestore:Boolean = false,
    var isMatchFailedVersionLow: Boolean = false,
    /**
     * 用于埋点的场景信息
     */
    var trackScene: String = "",
    /**
     * 是否跳过匹配流程，用于引导类 cocos
     */
    var jumpMatch: Boolean = false
) {
    /**
     * 从外部场景带入的 game_mode.
     * 值大于 0 且在 LittleGame 中的 game 无效时使用
     */
    var gameMode = -1

    /**
     * 从外部场景带入的 赛事 id.
     */
    var competitionId = -1

    /**
     * 赛事是否禁用语音功能
     */
    var competitionForbidVoice = false
}