package com.wepie.wespy.cocosnew.match;

import static com.wepie.wespy.cocosnew.match.create.CocosCreateRoomPresenter.SWITCH_WAIT_TIME_IN_MILLIS;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.editionentity.ILittleGameMatchInfo;
import com.huiwan.lib.api.ApiService;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.wejoy.gamematch.net.GameMatchSender;
import com.wejoy.littlegame.ILittleGameApi;
import com.wejoy.littlegame.LittleGame;
import com.wejoy.weplay.ex.GlobalLife;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.update.CocosResDebugVersionHelper;
import com.wepie.wespy.model.entity.match.SeatInfo;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.net.tcp.packet.GameMatchPackets;

/**
 * Created by bigwen on 2018/1/24.
 */

public class MatchPacketPresenter {

    private static final int RID = -1;
    private static long lastReqSwitchTime;

    /**
     * 请求更换位置
     *
     * @param seatNum 被请求的位置
     */
    public static void reqSwitchSeat(int seatNum) {
        final long now = System.currentTimeMillis();
        boolean seatEmpty = true;
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        for (SeatInfo seatInfo : teamInfo.getSeats()) {
            if (seatInfo.getSeatNum() == seatNum && !seatInfo.isEmpty()) {
                if (now - lastReqSwitchTime < SWITCH_WAIT_TIME_IN_MILLIS) {
                    ToastUtil.show(ResUtil.getStr(R.string.cocos_match_change_seat_fail_1));
                    return;
                }
                seatEmpty = false;
                break;
            }
        }
        final boolean isSeated = !seatEmpty;
        /*
         * 服务器上座位号从 1 开始
         */
        GameMatchSender.switchSeatReq(teamInfo.getTid(), SeatInfo.seatNumLtoH(seatNum), new LifeSeqCallback(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                if (isSeated) {
                    lastReqSwitchTime = now;
                    ToastUtil.show(ResUtil.getStr(R.string.cocos_match_change_seat_tips_1));
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public static void onKickUser(int uid) {
        GameMatchSender.kickReq(RID, LittleGame.getTeamInfo().getTid(), uid, new LifeSeqCallback(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(RspHeadInfo head) {

            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
                GameMatchSender.syncTeamReq(null);//踢人error sync一下
            }
        });

    }

    public static void quickMatchReq2(int rid, int tid, int mode, int level, int gameType, int gameMode, int currencyType, SeqCallback callback) {
        int localSpecifyGameVersion = CocosResDebugVersionHelper.INSTANCE.loadVersion(gameType);
        GameMatchSender.quickMatchReq2(rid, tid, mode, level, gameType, gameMode, 0, getGameVersion(gameType), currencyType, localSpecifyGameVersion, callback);
    }

    public static void createTeamReq2(int mode, int level, int gameMode, int gameType, int currencyType, SeqCallback callback) {
        int loadSpecifyVersion = CocosResDebugVersionHelper.INSTANCE.loadVersion(gameType);
        GameMatchPackets.CreateTeamReq2.Builder builder = GameMatchPackets.CreateTeamReq2
                .newBuilder()
                .setMode(mode)
                .setBetLevel(level)
                .setGameType(gameType)
                .setGameMode(gameMode)
                .setGameCurrencyTypeValue(currencyType)
                .setMatchType(GameMatchPackets.TeamMatchType.NORMAL_MATCH_TYPE_VALUE)
                .setGameCurrencyTypeValue(currencyType)
                .setGameVersion(ApiService.of(ILittleGameApi.class).getGameVersion(gameType));

        if (loadSpecifyVersion > 0) {
            builder.setForceGameType(gameType).setForceGameVersion(loadSpecifyVersion);
        }
        GameMatchSender.createTeamReq2(0, builder, callback);
    }

    /**
     * @param match 多人组队时快速匹配创建队伍，还是直接创建队伍。
     */
    public static void createTeamReq2(boolean match, ILittleGameMatchInfo matchInfo, int gameType, SeqCallback callback) {
        createTeamReq2(match, matchInfo, gameType, false, callback);
    }
    public static void createTeamReq2(boolean match, ILittleGameMatchInfo matchInfo, int gameType, boolean privateTeam, SeqCallback callback) {
        int mode = match ? matchInfo.getMatchMode() : matchInfo.getRoomMode();
        GameMatchPackets.CreateTeamReq2.Builder builder = GameMatchPackets.CreateTeamReq2
                .newBuilder()
                .setMode(mode)
                .setBetLevel(matchInfo.getBetLevel())
                .setGameType(gameType)
                .setGameMode(matchInfo.getGameMode())
                .setGameCurrencyTypeValue(matchInfo.getCurrencyType())
                .setMatchType(GameMatchPackets.TeamMatchType.NORMAL_MATCH_TYPE_VALUE)
                .setGameCurrencyTypeValue(matchInfo.getCurrencyType())
                .setPrivate(privateTeam)
                .setGameVersion(ApiService.of(ILittleGameApi.class).getGameVersion(gameType));
        GameMatchSender.createTeamReq2(0, builder, callback);
    }

    public static void createTeamReqMatch2(int mode, int level, int gameType, int gameMode, int currencyType, SeqCallback callback) {
        GameMatchPackets.CreateTeamReq2.Builder builder = GameMatchPackets.CreateTeamReq2
                .newBuilder()
                .setMode(mode)
                .setBetLevel(level)
                .setGameType(gameType)
                .setGameMode(gameMode)
                .setMatchType(GameMatchPackets.TeamMatchType.INVITE_MATCH_TYPE_VALUE)
                .setGameCurrencyTypeValue(currencyType)
                .setGameVersion(ApiService.of(ILittleGameApi.class).getGameVersion(gameType));
        GameMatchSender.createTeamReq2(0, builder, callback);
    }

    public static void createTeamReq2InGroup(int gid, int mode, int level, int gameMode, int gameType, int currencyType, SeqCallback callback) {
        GameMatchPackets.CreateTeamReq2.Builder builder = GameMatchPackets.CreateTeamReq2
                .newBuilder()
                .setMode(mode)
                .setBetLevel(level)
                .setGameType(gameType)
                .setGameMode(gameMode)
                .setMatchType(GameMatchPackets.TeamMatchType.NORMAL_MATCH_TYPE_VALUE)
                .setGameCurrencyTypeValue(currencyType)
                .setGameVersion(ApiService.of(ILittleGameApi.class).getGameVersion(gameType));
        GameMatchSender.createTeamReq2(gid, builder, callback);
    }

    private static int getGameVersion(int gameType) {
        return ApiService.of(ILittleGameApi.class).getGameVersion(gameType);
    }
}
