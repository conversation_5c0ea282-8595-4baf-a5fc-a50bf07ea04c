package com.wepie.wespy.cocosnew.match.matching

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.widget.LinearLayout

abstract class AbsCocosMatchView : LinearLayout {

    constructor(context: Context?) : super(context)
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    abstract fun initView()

    open fun showSelf() {}

    open fun showOpponent(uid: Int) {}

    open fun setEmptyImg(drawable: Drawable) {}

    open fun update() {}

    open fun updateSeatInfo(uidList: List<Int>) {}

    open fun updateSeatInfo(uidList: List<Int>, showVerticalLine: Boolean) {}
}