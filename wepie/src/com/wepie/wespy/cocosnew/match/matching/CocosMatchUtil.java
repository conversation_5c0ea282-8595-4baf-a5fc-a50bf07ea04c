package com.wepie.wespy.cocosnew.match.matching;

import android.app.Activity;
import android.content.Context;

import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.editionentity.ILittleGameMatchInfo;
import com.huiwan.lib.api.ApiService;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.littlegame.cocos.CocosAudioUtilKt;
import com.wejoy.littlegame.GameScene;
import com.wejoy.littlegame.ILittleGameApi;
import com.wejoy.littlegame.LittleGame;
import com.wejoy.littlegame.LittleGameKt;
import com.wejoy.littlegame.LittleGameSimpleInfo;
import com.wepie.liblog.main.HLog;
import com.wepie.libpermission.PermissionCallback;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.main.ar285.loader.LittleGameResUtil;
import com.wepie.wespy.helper.dialog.PermissionDialog;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.module.common.jump.JumpCocosTeamUtil;
import com.wepie.wespy.net.tcp.packet.GameMatchPackets;

import java.util.List;

import kotlin.Unit;

public class CocosMatchUtil {

    // save match info after match success
    public static void saveMatchInfo(RspHeadInfo head) {
        JumpCocosTeamUtil.existVoiceRoomIfIn();
        if (head.message instanceof GameMatchPackets.GMQuickMatchRsp2) {
            GameMatchPackets.GMQuickMatchRsp2 matchRsp = (GameMatchPackets.GMQuickMatchRsp2) head.message;
            LittleGame.updateTeamInfo(TeamInfo.parse(matchRsp.getTeam(), matchRsp.getJkEntryBetInfo()));
            LittleGame.setTeamMatchExpectedTime(matchRsp.getExpectTime());
        } else if (head.message instanceof GameMatchPackets.CreateTeamRsp2) {
            GameMatchPackets.CreateTeamRsp2 matchRsp = (GameMatchPackets.CreateTeamRsp2) head.message;
            LittleGame.updateTeamInfo(TeamInfo.parse(matchRsp));
        }
    }

    public static void requirePermission(Activity activity, PermissionCallback callback) {
        CocosAudioUtilKt.requestGameAudioPermission(activity, new PermissionCallback() {
            @Override
            public void hasPermission(List<String> granted, boolean isAll, boolean alreadyHas) {
                callback.hasPermission(granted, isAll, alreadyHas);
            }

            @Override
            public void noPermission(List<String> denied, boolean quick) {
                showRefusedAudioTip(activity, quick);
                callback.noPermission(denied, quick);
            }
        });
    }

    /**
     * 拒绝授权时的提示。自动拒绝时弹窗，手动拒绝时 toast.
     *
     * @param quick 是否系统自动拒绝
     */
    public static void showRefusedAudioTip(Context context, boolean quick) {
        if (quick) {
            String msg = context.getString(R.string.cocos_not_permission_tips_2);
            PermissionDialog.showJumpPermissionDialog(context, msg);
        } else {
            ToastUtil.show(R.string.cocos_not_permission_tips_1);
        }
    }

    public static void handleFailMatch(Context context, @GameScene int scene, int gameType, ILittleGameMatchInfo info, RspHeadInfo head, String trackScene, UpdateSuccess callBack) {
        LittleGameSimpleInfo simpleInfo = new LittleGameSimpleInfo(gameType,
                info.getMatchMode(), info.getGameMode(), info.getBetLevel(),
                info.getCurrencyType(), info.getIsCreate());
        handleFailMatch(context, scene, simpleInfo, head, trackScene, callBack);
    }

    // handle error when match fail
    public static void handleFailMatch(Context context, @GameScene int scene, LittleGameSimpleInfo info, RspHeadInfo head, String trackScene, UpdateSuccess callBack) {
        HLog.e("MatchFailed", HLog.USR, "GameType=" + info.getGameType() + " " + head.code + " " + head.desc);

        boolean ret = ApiService.of(ILittleGameApi.class).handleEnterError(context, head.code,
                scene | LittleGameKt.EXCHANGE_TYPE_ENTER, info,
                () -> {
                    if (callBack != null) {
                        callBack.onUpdateSuccess(false);
                    }
                    return Unit.INSTANCE;
                });
        if (ret) {
            return;
        }

        if (head.code == RspHeadInfo.ERROR_CODE_VERSION_TOO_LOW) {
            LittleGameResUtil.INSTANCE.checkLoadRes(context, info.getGameType(), true, trackScene, cocos -> {
                if (callBack != null) {
                    callBack.onUpdateSuccess(true);
                }
                return Unit.INSTANCE;
            });
        } else {
            ToastUtil.show(head.desc);
        }
    }


    public interface UpdateSuccess {
        void onUpdateSuccess(Boolean retry);
    }
}
