package com.wepie.wespy.cocosnew.match.matching;


import androidx.lifecycle.LifecycleOwner;

import com.wepie.wespy.model.entity.iceball.IceGameBeforeStart;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/11/11.
 */

public interface ICocosMatchView extends LifecycleOwner {

    /**
     * 根据匹配界面设置标题
     */
    void showTitle(String res);

    /**
     * 显示 1 V 1 匹配界面
     * @param uid 用户 id
     */
    void show1V1(int uid);

    /**
     *
     * 显示 2 V 2 匹配界面
     */
    void show2V2(ArrayList<Integer> uidLis);

    /**
     * 1 v 1 更新对手 UI
     * @param uid 对手 ID
     */
    void update1V1Opponent(int uid);

    /**
     * 匹配成功，跳转到游戏界面
     */
    void jumpToGame(IceGameBeforeStart beforeStartInfo);

    /**
     * 显示错误信息
     * @param msg 要显示的信息
     */
    void showErrInfo(String msg);

    /**
     * 匹配成功更新动画
     */
    void updateMatchSuccess();

    /**
     * 更新已匹配时间
     * @param second 已经匹配的时间，单位 秒
     */
    void updateTimeSpent(int second);

    /**
     * 更新预计匹配时间
     * @param second 预计匹配时间
     */
    void setTimeExcepted(int second);

    /**
     * 取消匹配
     */
    void cancelMatch();

    void showTotal3(ArrayList<Integer> uidLis);
    void showTotal5(ArrayList<Integer> uidLis);
    void showTotal6(ArrayList<Integer> uidLis);
    void showTotal8(ArrayList<Integer> uidLis);
    void showOnlyOne();
    void leave();
    void show2v2v2(ArrayList<Integer> uidList);
    void delayFinish();
}
