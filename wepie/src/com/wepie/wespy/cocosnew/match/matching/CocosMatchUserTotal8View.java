package com.wepie.wespy.cocosnew.match.matching;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.Nullable;

import com.wepie.wespy.R;

/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/11/16.
 */

public class CocosMatchUserTotal8View extends CocosMatchUserView {
    public CocosMatchUserTotal8View(Context context) {
        super(context);
    }

    public CocosMatchUserTotal8View(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public CocosMatchUserTotal8View(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.ice_ball_match_user_total8_view;
    }
}
