package com.wepie.wespy.cocosnew.match.matching;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.DrawableRecycleUtil;
import com.huiwan.base.util.InitializerManagerUtils;
import com.huiwan.base.util.PressUtil;
import com.huiwan.base.util.SingleClickListener;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.constants.IntentConfig;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.IAppNotify;
import com.huiwan.lib.api.plugins.ICompetitionApi;
import com.wejoy.littlegame.ILittleGameApi;
import com.wejoy.littlegame.LittleGame;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.libimageloader.WpImageLoadListener;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.liblog.main.HLog;
import com.wepie.startup.InitializerManager;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.match.prepare.CocosMatchBetInfoView;
import com.wepie.wespy.cocosnew.match.prepare.CocosMatchPrepareActivity;
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.model.entity.iceball.IceGameBeforeStart;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * <AUTHOR> Yuu
 * email <EMAIL>
 * date 2017/11/9.
 * <p>
 * 冰球 匹配
 */
public class CocosMatchActivity extends BaseActivity implements ICocosMatchView, IAppNotify.INotifyPage {
    private static final String TAG = "CocosMatchActivity";
    public static final String OUTER_CIRCLE_URL = "https://res.weplayapp.com/conf/EF4360A5-5215-46CC-B8DE-4CA273216BED.webp";

    private final CocosMatchPresenter presenter = new CocosMatchPresenter(this);

    private ViewGroup centerMatchLay;
    private AbsCocosMatchView cocosMatchView;
    private ImageView outerCircleIv;
    private TextView titleTv;
    private TextView timeSpentTv;
    private TextView timeExceptedTv;
    private TextView matchTitleTv;
    private ImageView matchBgIV;
    private ImageView matchFrontIv;
    private TextView suspendTv;
    private ImageView animIv;
    private boolean fromGameMain = false;
    private ObjectAnimator animator;
    private final Handler handler = new Handler(Looper.getMainLooper());

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        int gameType = getIntent().getIntExtra(IntentConfig.GAME_TYPE, -1);
        beforeOnCreate(gameType);
        super.onCreate(savedInstanceState);
        StatusBarUtil.initStatusBar(this, false);
        setContentView(R.layout.acticity_cocos_ice_ball_match);
        fromGameMain = getIntent().getBooleanExtra(IntentConfig.FROM_GAME_MAIN, false);
        if (fromGameMain) {
            ShenceEvent.startMatch(gameType);
        }
        presenter.setGameType(gameType);
        initView();
        initEvent();
        presenter.init();
        ApiService.of(ILittleGameApi.class).preload(this);
        ApiService.of(ICompetitionApi.class).registerEnterCompetitionCallback(this);
    }

    private void beforeOnCreate(int gameType) {
        GameConfig gameConfig = ConfigHelper.getInstance().getGameConfig(gameType);
        if (gameConfig == null) {
            return;
        }

        if (gameConfig.unityGame) {
            // 解决Unity小游戏在匹配成功后，匹配页面finish的时候会闪一下的问题
            setTheme(R.style.AppTheme_Cocos_NoAnim);
        }
    }

    private void initView() {
        titleTv = findViewById(R.id.cocos_title_tv);
        suspendTv = findViewById(R.id.cocos_suspend_tv);

        timeSpentTv = findViewById(R.id.ice_ball_time_spent_tv);
        timeExceptedTv = findViewById(R.id.ice_ball_time_excepted_tv);
        matchTitleTv = findViewById(R.id.ice_ball_match_title_tv);
        matchBgIV = findViewById(R.id.match_bg_iv);
        matchFrontIv = findViewById(R.id.match_front_iv);
        animIv = findViewById(R.id.ice_ball_vs_anim_iv);
        outerCircleIv = findViewById(R.id.ice_ball_outer_circle_iv);
        DrawableRecycleUtil.setRecycleDrawable(outerCircleIv, () -> WpImageLoader.load(OUTER_CIRCLE_URL, outerCircleIv));
        new CocosMatchBetInfoView(findViewById(R.id.bet_info_lay));

        animator = ObjectAnimator.ofFloat(animIv, "rotation", 0, 360);
        animator.setInterpolator(new LinearInterpolator());
        animator.setDuration(1400);
        animator.setRepeatCount(ValueAnimator.INFINITE);
        animator.start();

        CocosGameConfigUtil.loadResBgAsyncWithRecycle(matchBgIV, presenter.getGameType(), CocosGameConfigUtil.MATCH_BACKGROUND);
        CocosGameConfigUtil.loadResBgAsyncWithRecycle(matchFrontIv, presenter.getGameType(), CocosGameConfigUtil.MATCH_FRONT);

        initCenterCocosMatchView();
    }

    private void initCenterCocosMatchView() {
        centerMatchLay = findViewById(R.id.ice_ball_vs_circle);
        if (!presenter.isShowAnim()) {
            animIv.setVisibility(View.GONE);
            animIv.clearAnimation();
            animator.cancel();
            centerMatchLay.setBackgroundResource(R.color.transparent);
        }

        TeamInfo teamInfo = LittleGame.getTeamInfo();
        if (teamInfo.isMateOneGamerTwo()) {
            cocosMatchView = new CocosMatch1V1View(this);
        } else if (teamInfo.isMateOneGamerFour() || teamInfo.isMateTwoGamerFour()) {
            cocosMatchView = new CocosMatch2V2View(this);
        } else if (teamInfo.isMateOneGameThree()) {
            cocosMatchView = new CocosMatchTotal3View(this);
        } else if (teamInfo.isMateOneGameFive()) {
            cocosMatchView = new CocosMatchTotal5View(this);
        } else if (teamInfo.isMateTwoGamerSix()) {
            cocosMatchView = new CocosMatch2V2V2View(this);
        } else if (teamInfo.isMateOneGameSix()) {
            cocosMatchView = new CocosMatchTotal6View(this, false);
        } else if (teamInfo.isMateOneGameEight()) {
            cocosMatchView = new CocosMatchTotal8View(this, false);
        } else {
            cocosMatchView = new CocosMatchOnlyOneView(this);
        }

        centerMatchLay.addView(cocosMatchView, new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        CocosGameConfigUtil.load(cocosMatchView, presenter.getGameType(), CocosGameConfigUtil.MATCH_EMPTY, new WpImageLoadListener<>() {
            @Override
            public boolean onComplete(String model, Drawable drawable) {
                cocosMatchView.setEmptyImg(drawable);
                return false;
            }

            @Override
            public boolean onFailed(String model, Exception e) {
                return false;
            }
        });
        trackViewScreen(teamInfo);
    }

    private void initEvent() {
        findViewById(R.id.cocos_room_back_img).setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                presenter.cancelMatch();
            }
        });
        suspendTv.setOnClickListener(new SingleClickListener() {
            @Override
            public void onClickInternal(@NonNull View v) {
                LittleGame.touchMatchSuspend(presenter.getStartTime());
                EventDispatcher.postLittleGameSuspend(true);
                if (fromGameMain) {
                    JumpUtil.gotoMainActivity(CocosMatchActivity.this);
                } else {
                    EventDispatcher.postFinishMatchPrepareActivity();
                    finish();
                }
            }
        });
        PressUtil.addPressEffect(suspendTv);
    }

    @Override
    public void onBackPressed() {
        presenter.cancelMatch();
    }

    @Override
    public void showTitle(String res) {
        titleTv.setText(res);
    }

    @Override
    public void showErrInfo(String msg) {
        ToastUtil.show(msg);
    }

    @Override
    public void jumpToGame(IceGameBeforeStart iceGameBeforeStart) {
        JumpUtil.gotoLittleGameActivity(this, false, 0, iceGameBeforeStart.roomInfo, iceGameBeforeStart.beforeStartData, false);
        delayFinish();
    }

    @Override
    public void show1V1(int uid) {
        if (cocosMatchView != null) {
            cocosMatchView.showSelf();
        }
    }

    @Override
    public void show2V2(ArrayList<Integer> uidList) {
        if (cocosMatchView != null) {
            cocosMatchView.updateSeatInfo(uidList, presenter.isShowVertical());
        }
    }

    @Override
    public void show2v2v2(ArrayList<Integer> uidList) {
        if (cocosMatchView != null) {
            cocosMatchView.updateSeatInfo(uidList, presenter.isShowVertical());
        }
    }

    @Override
    public void delayFinish() {
        HLog.d(TAG, HLog.USR, "delayFinish");
        handler.postDelayed(() -> {
            HLog.d(TAG, HLog.USR, "delayFinishInvoked");
            finish();
        }, 2000L);
    }

    @Override
    public void update1V1Opponent(int uid) {
        if (cocosMatchView != null) {
            cocosMatchView.showOpponent(uid);
        }
    }

    @Override
    public void updateMatchSuccess() {
        suspendTv.setVisibility(View.GONE);
        matchTitleTv.setText(R.string.match_success);
        animator.cancel();
        findViewById(R.id.ice_ball_vs_anim_iv).setVisibility(View.INVISIBLE);
        centerMatchLay.setBackgroundResource(R.color.transparent);
    }

    @Override
    public void cancelMatch() {
        int curMode = LittleGame.getTeamInfo().getMode();
        if (TeamInfo.isMorePlayerMatch(curMode)) {
            if (!ActivityTaskManager.getInstance().isStack(CocosMatchPrepareActivity.class)) {
                JumpUtil.gotoIceBallMatchPrepareActivity(this, LittleGame.getTeamInfo().getGameType(), fromGameMain, "");
            }
        }
        finish();
    }

    @Override
    public void updateTimeSpent(int second) {
        String formatStr = getString(R.string.time_minute_second);
        timeSpentTv.setText(String.format(LibBaseUtil.getTimeFormatLocal(), formatStr, second / 60, second % 60));
    }

    @Override
    public void setTimeExcepted(int second) {
        if (second > 30 * 60) second = 30 * 60;
        if (second <= 0) second = 1;
        String formatStr = getString(R.string.ice_ball_match_time_excepted);
        timeExceptedTv.setText(String.format(LibBaseUtil.getTimeFormatLocal(), formatStr, second / 60, second % 60));
    }

    @Override
    protected void clearMemory() {
        super.clearMemory();
        animator.cancel();
    }

    @Override
    protected void onResume() {
        super.onResume();
        presenter.resume();
    }

    @Override
    protected void onStop() {
        super.onStop();
        presenter.stop();
    }

    @Override
    protected void onDestroy() {
        presenter.destroy();
        super.onDestroy();
        HLog.d(TAG, HLog.USR, "onDestroy");
        handler.removeCallbacksAndMessages(null);
    }

    @Override
    public void showTotal3(ArrayList<Integer> uidLis) {
        if (cocosMatchView != null) {
            cocosMatchView.updateSeatInfo(uidLis);
        }
    }

    @Override
    public void showTotal5(ArrayList<Integer> uidLis) {
        if (cocosMatchView != null) {
            cocosMatchView.updateSeatInfo(uidLis);
        }
    }

    @Override
    public void showTotal6(ArrayList<Integer> uidLis) {
        if (cocosMatchView != null) {
            cocosMatchView.updateSeatInfo(uidLis, presenter.isShowVertical());
        }
    }

    @Override
    public void showTotal8(ArrayList<Integer> uidLis) {
        if (cocosMatchView != null) {
            cocosMatchView.updateSeatInfo(uidLis, presenter.isShowVertical());
        }
    }

    @Override
    public void showOnlyOne() {
        if (cocosMatchView != null) {
            cocosMatchView.update();
        }
        outerCircleIv.setVisibility(View.GONE);
    }

    @Override
    public void leave() {
        finish();
    }


    @Override
    public int supportFloatView() {
        return 0;
    }

    @Override
    protected void filterStartup() {
        super.filterStartup();
        InitializerManagerUtils.wait(InitializerManager.FILTER_FOUR);
    }

    @Override
    public void canAppNotify(@NonNull IAppNotify.IAppNotifyInfo info, @NonNull Function1<? super Boolean, Unit> callback) {
        callback.invoke(info.getScene() == IAppNotify.SCENE_COCOS_SKIN);
    }

    private void trackViewScreen(TeamInfo teamInfo) {
        Map<String, Object> map = new HashMap<>();
        map.put("game_type", teamInfo.game_type);
        map.put("mode_type", String.valueOf(teamInfo.game_type) + teamInfo.gameMode);
        map.put("match_mode", teamInfo.trackMatchMode());
        map.put("bet_level", teamInfo.betLevel);
        ShenceEvent.appViewScreen(TrackScreenName.COCOS_MATCH_PAGE, map);
    }
}
