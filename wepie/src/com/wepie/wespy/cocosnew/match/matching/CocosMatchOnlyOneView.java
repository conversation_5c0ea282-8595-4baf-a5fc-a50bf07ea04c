package com.wepie.wespy.cocosnew.match.matching;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.huiwan.user.LoginHelper;
import com.huiwan.user.entity.User;
import com.huiwan.widget.CustomCircleImageView;
import com.huiwan.widget.SpeakerAnimView;
import com.huiwan.widget.drawables.SpeakerAnimDrawable;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;


/**
 * 多余等于5人匹配使用
 */
public class CocosMatchOnlyOneView extends AbsCocosMatchView {

    private CustomCircleImageView headerIv;
    private ImageView genderIv;
    private TextView nameTv;
    private SpeakerAnimView speakerAnimView;

    public CocosMatchOnlyOneView(Context context) {
        super(context);
        initView();
    }

    public CocosMatchOnlyOneView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public CocosMatchOnlyOneView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    @Override
    public void initView() {
        setClipChildren(false);
        LayoutInflater.from(getContext()).inflate(R.layout.ice_ball_match_onlyone_view, this);
        headerIv = findViewById(R.id.ice_ball_match_member_header_iv);
        genderIv = findViewById(R.id.ice_ball_match_member_gender_iv);
        nameTv = findViewById(R.id.ice_ball_match_member_name_tv);
        speakerAnimView = findViewById(R.id.ice_ball_speak_anim);
        speakerAnimView.setImageDrawable(new SpeakerAnimDrawable(2.6F, 1000 * 60 * 3));

        post(() -> speakerAnimView.init((int) headerIv.getX() + headerIv.getWidth() / 2,
                (int) headerIv.getY() + headerIv.getHeight() / 2,
                headerIv.getWidth() / 2));
        genderIv.setVisibility(GONE);
    }

    @Override
    public void update() {
        HeadImageLoader.loadCircleHeadImage(LoginHelper.getHeadUrl(), headerIv);
        nameTv.setText(LoginHelper.getNickname());
        if (LoginHelper.getGender() == User.GENDER_MALE) {
            genderIv.setImageResource(R.drawable.icon_male);
        } else if (LoginHelper.getGender() == User.GENDER_FEMALE) {
            genderIv.setImageResource(R.drawable.icon_female);
        }
        speakerAnimView.startAnim();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        speakerAnimView.stopAnim();
    }
}
