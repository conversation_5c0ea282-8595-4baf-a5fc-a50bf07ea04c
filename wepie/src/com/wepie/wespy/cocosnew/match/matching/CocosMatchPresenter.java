package com.wepie.wespy.cocosnew.match.matching;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.constants.GameType;
import com.huiwan.lib.api.ApiService;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.littlegame.event.IceGameCommandEvent;
import com.huiwan.user.LoginHelper;
import com.wejoy.gamematch.net.GameMatchSender;
import com.wejoy.littlegame.LittleGame;
import com.wepie.lib.api.plugins.track.TrackApi;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.cocosnew.match.LittleGameStateCheckHelper;
import com.wepie.wespy.cocosnew.util.CocosGameConfigUtil;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.model.entity.iceball.IceGameBeforeStart;
import com.wepie.wespy.model.entity.match.SeatInfo;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.iceball.IceBeforeStartEvent;
import com.wepie.wespy.model.event.iceball.IceCancelMatchEvent;
import com.wepie.wespy.model.event.match.FinishTeamViewEvent;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wespy.component.suspend.SuspendInfo;
import com.wespy.component.suspend.SuspendManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR> Yuu
 * email <EMAIL>
 * date 2017/11/9.
 * <p>
 * 冰球匹配逻辑
 */
public class CocosMatchPresenter {

    private static final String TAG = "CocosMatchPresenter";
    private static final long WAITING_TIME_WHEN_MATCH_SUCCEED = 1000L;
    private final ICocosMatchView matchView;

    private Handler handler;
    private long startTime;
    private final Runnable timerRunnable = this::updateTime;
    private int gameType;
    // shouldJumpLittleGame, match, isResume这三个变量是为了保存状态，解决匹配过程中退出到后台过一会再进入游戏异常的问题
    private boolean shouldJumpLittleGame = false;
    private IceBeforeStartEvent match;
    private boolean isResume = false;
    private boolean isSyncTeamEnter = false;
    private final LittleGameStateCheckHelper gameStateCheckHelper = LittleGameStateCheckHelper.INSTANCE;

    public CocosMatchPresenter(ICocosMatchView matchView) {
        this.matchView = matchView;
    }

    public void init() {
        initTitle();
        initView();
        matchView.setTimeExcepted(LittleGame.getTeamMatchExpectedTime());
        handler = new Handler(Looper.getMainLooper());

        SuspendInfo suspendInfo = SuspendManager.INSTANCE.getSuspendInfo();
        if (suspendInfo != null && suspendInfo.getSuspendType() == SuspendInfo.TYPE_MATCHING) {
            startTime = suspendInfo.getStartTimeInMill();
        } else {
            startTime = System.currentTimeMillis();
        }
        SuspendManager.INSTANCE.closeSuspend();
        updateTime();
        registerEventBus();
        initGameStateCheckHelper();
    }

    private void initTitle() {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        String text = "";
        if (gameType == GameType.GAME_TYPE_SHEEP) {
            text = CocosGameConfigUtil.getCocosGameConfig(GameType.GAME_TYPE_SHEEP).getName();
        } else {
            GameConfig.MatchInfo matchInfo = ConfigHelper.getInstance().getGameConfig().getLittleGameMatchInfo(
                    getGameType(), teamInfo.betLevel, teamInfo.mode, teamInfo.gameMode, teamInfo.currencyType);
            if (matchInfo != null) {
                text = matchInfo.getTitle();
            } else {
                text = teamInfo.getModeName();
            }
        }
        matchView.showTitle(text);
    }

    private void initView() {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        if (teamInfo.isMateOneGamerTwo()) {
            matchView.show1V1(teamInfo.getOwner());
        } else if (teamInfo.isMateOneGamerFour() || teamInfo.isMateTwoGamerFour()) {
            ArrayList<Integer> uidList = new ArrayList<>(4);
            for (SeatInfo seatInfo : teamInfo.getSeats()) {
                if (seatInfo.getUid() > 0) {
                    uidList.add(seatInfo.getUid());
                }
            }
            matchView.show2V2(uidList);
        } else if (teamInfo.isMateOneGameThree()) {
            ArrayList<Integer> uidList = new ArrayList<>(4);
            for (SeatInfo seatInfo : teamInfo.getSeats()) {
                if (seatInfo.getUid() > 0) {
                    uidList.add(seatInfo.getUid());
                }
            }
            matchView.showTotal3(uidList);
        } else if (teamInfo.isMateOneGameFive()) {
            ArrayList<Integer> uidList = new ArrayList<>(5);
            for (SeatInfo seatInfo : teamInfo.getSeats()) {
                if (seatInfo.getUid() > 0) {
                    uidList.add(seatInfo.getUid());
                }
            }
            matchView.showTotal5(uidList);
        } else if (teamInfo.isMateOneGameSix()) {
            ArrayList<Integer> uidList = new ArrayList<>(6);
            for (SeatInfo seatInfo : teamInfo.getSeats()) {
                if (seatInfo.getUid() > 0) {
                    uidList.add(seatInfo.getUid());
                }
            }
            matchView.showTotal6(uidList);
        } else if (teamInfo.isMateOneGameEight()) {
            ArrayList<Integer> uidList = new ArrayList<>(8);
            for (SeatInfo seatInfo : teamInfo.getSeats()) {
                if (seatInfo.getUid() > 0) {
                    uidList.add(seatInfo.getUid());
                }
            }
            matchView.showTotal8(uidList);
        } else if (teamInfo.isMateTwoGamerSix()) {
            ArrayList<Integer> uidList = new ArrayList<>(6);
            for (SeatInfo seatInfo : teamInfo.getSeats()) {
                if (seatInfo.getUid() > 0) {
                    uidList.add(seatInfo.getUid());
                }
            }
            matchView.show2v2v2(uidList);
        } else {
            matchView.showOnlyOne();
        }
    }

    private void initGameStateCheckHelper() {
        gameStateCheckHelper.setSuccessCallback(matchView, () -> {
            isSyncTeamEnter = true;
            shouldJumpLittleGame = false;
            matchView.updateMatchSuccess();
            matchView.delayFinish();
            EventDispatcher.postChatGameInviteCancelEvent();
            com.huiwan.littlegame.EventDispatcher.postIceCommand(IceGameCommandEvent.COMMAND_CANCEL_MATCH_INVITE);
        });
        gameStateCheckHelper.checkGameState();
    }

    /**
     * 推送，确定匹配成功
     *
     * @param match 匹配结果中的用户信息
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void pushMatchSuccess(IceBeforeStartEvent match) {
        if (isSyncTeamEnter) {
            return;
        }
        gameStateCheckHelper.stopCheck();
        HLog.d(TAG, HLog.USR, "pushMatchSuccess rid: {}, tid: {}", match.beforeStartInfo.roomInfo.rid, LittleGame.getTeamInfo().getTid());
        this.match = match;
        updateMatchSuccess(match.beforeStartInfo);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleFinishTeamView(FinishTeamViewEvent event) {
        matchView.leave();
    }

    private void updateMatchSuccess(final IceGameBeforeStart beforeStartInfo) {
        logMatchSuccess(gameType, LoginHelper.getLoginUid(), "match");
        updateSeatFromGamers(beforeStartInfo);
        matchView.updateMatchSuccess();
        shouldJumpLittleGame = true;
        if (handler != null) {
            handler.postDelayed(() -> {
                if (isResume) {
                    matchView.jumpToGame(beforeStartInfo);
                    shouldJumpLittleGame = false;
                }
            }, WAITING_TIME_WHEN_MATCH_SUCCEED);
            handler.removeCallbacks(timerRunnable);
        }
    }

    private void logMatchSuccess(int gameType, int uid, String enterType) {
        JSONObject properties = new JSONObject();
        try {
            properties.put("source", TrackUtil.getGameTypeSource(gameType));
            properties.put("uid", uid);
            properties.put("enter_type", enterType);
            ApiService.of(TrackApi.class).trackEvent("MobLoadingCocos", properties);
        } catch (Exception ignored) {

        }
    }

    /**
     * 推送，取消匹配
     *
     * @param event 取消匹配事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void pushCancelMatch(IceCancelMatchEvent event) {
        if (!TextUtils.isEmpty(event.msg)) {
            matchView.showErrInfo(event.msg);
        }
        if (handler != null) {
            handler.removeCallbacks(timerRunnable);
        }
        matchView.cancelMatch();
        shouldJumpLittleGame = false;
    }


    public void cancelMatch() {
        ShenceEvent.cancelMatch(getGameType(), System.currentTimeMillis() - startTime);
        GameMatchSender.cancelMatchReq(LittleGame.getTeamInfo().getTid(), new LifeSeqCallback(matchView) {
            @Override
            public void onSuccess(RspHeadInfo head) {
            }

            @Override
            public void onFail(RspHeadInfo head) {
                matchView.showErrInfo(head.desc);
            }
        });
        if (handler != null) {
            handler.removeCallbacks(timerRunnable);
        }
        matchView.cancelMatch();
    }

    private void updateSeatFromGamers(IceGameBeforeStart iceGameBeforeStart) {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        if (teamInfo.isMateOneGamerTwo()) {
            updateSeat1v1(iceGameBeforeStart);
        } else if (teamInfo.isMateOneGamerFour() || teamInfo.isMateTwoGamerFour()) {
            updateSeat2V2(iceGameBeforeStart);
        } else if (teamInfo.isMateOneGameThree()) {
            updateTotal3Or5(iceGameBeforeStart, true);
        } else if (teamInfo.isMateOneGameFive()) {
            updateTotal3Or5(iceGameBeforeStart, false);
        } else if (teamInfo.isMateTwoGamerSix()) {
            updateSeat2V2V2(iceGameBeforeStart);
        } else {
            matchView.showOnlyOne();
        }
    }

    private void updateSeat2V2V2(IceGameBeforeStart iceGameBeforeStart) {
        ArrayList<Integer> teamList = new ArrayList<>();
        ArrayList<VoiceRoomInfo.SeatInfo> seatInfos = iceGameBeforeStart.roomInfo.seatInfos;
        for (int i = 0; i < seatInfos.size(); i++) {
            VoiceRoomInfo.SeatInfo seatInfo = seatInfos.get(i);
            if (seatInfo.uid > 0) {
                teamList.add(seatInfo.uid);
            }
        }
        while (teamList.size() < 6) {
            teamList.add(0);
        }
        int selfUid = LoginHelper.getLoginUid();
        List<Integer> team1 = new ArrayList<>();
        List<Integer> team2 = new ArrayList<>();
        List<Integer> team3 = new ArrayList<>();
        for (int i = 0; i < teamList.size(); i++) {
            int uid = teamList.get(i);
            if (i <= 1) {
                team1.add(uid);
            } else if (i <= 3) {
                team2.add(uid);
            } else if (i <= 5) {
                team3.add(uid);
            }
        }
        teamList.clear();
        if (team1.contains(selfUid)) {
            addTeamUid(selfUid, teamList, team1);
            teamList.addAll(team2);
            teamList.addAll(team3);
        } else if (team2.contains(selfUid)) {
            addTeamUid(selfUid, teamList, team2);
            teamList.addAll(team1);
            teamList.addAll(team3);
        } else if (team3.contains(selfUid)) {
            addTeamUid(selfUid, teamList, team3);
            teamList.addAll(team1);
            teamList.addAll(team2);
        }
        matchView.show2v2v2(teamList);
    }

    private void updateSeat1v1(IceGameBeforeStart iceGameBeforeStart) {
        int opponentUid = 0;
        ArrayList<VoiceRoomInfo.SeatInfo> seatInfos = iceGameBeforeStart.roomInfo.seatInfos;
        for (VoiceRoomInfo.SeatInfo seatInfo : seatInfos) {
            if (seatInfo.uid != LoginHelper.getLoginUid()
                    && seatInfo.uid > 0) {
                opponentUid = seatInfo.uid;
            }
        }
        matchView.update1V1Opponent(opponentUid);
    }

    private void updateSeat2V2(IceGameBeforeStart iceGameBeforeStart) {
        ArrayList<Integer> teamList = new ArrayList<>();
        ArrayList<VoiceRoomInfo.SeatInfo> seatInfos = iceGameBeforeStart.roomInfo.seatInfos;
        for (int i = 0; i < seatInfos.size(); i++) {
            VoiceRoomInfo.SeatInfo seatInfo = seatInfos.get(i);
            if (seatInfo.uid > 0) {
                teamList.add(seatInfo.uid);
            }
        }
        while (teamList.size() < 4) {
            teamList.add(0);
        }
        int selfUid = LoginHelper.getLoginUid();
        List<Integer> team1 = new ArrayList<>();
        List<Integer> team2 = new ArrayList<>();
        for (int i = 0; i < teamList.size(); i++) {
            int uid = teamList.get(i);
            if (i <= 1) {
                team1.add(uid);
            } else {
                team2.add(uid);
            }
        }
        teamList.clear();
        if (team1.contains(selfUid)) {
            addTeamUid(selfUid, teamList, team1);
            teamList.addAll(team2);
        } else {
            addTeamUid(selfUid, teamList, team2);
            teamList.addAll(team1);
        }
        matchView.show2V2(teamList);
    }

    private void updateTotal3Or5(IceGameBeforeStart iceGameBeforeStart, boolean isTotal3) {
        ArrayList<Integer> teamList = new ArrayList<>();
        ArrayList<VoiceRoomInfo.SeatInfo> seatInfos = iceGameBeforeStart.roomInfo.seatInfos;
        for (int i = 0; i < seatInfos.size(); i++) {
            VoiceRoomInfo.SeatInfo seatInfo = seatInfos.get(i);
            if (seatInfo.uid > 0) {
                teamList.add(seatInfo.uid);
            }
        }
        teamList.remove((Integer) LoginHelper.getLoginUid());
        teamList.add(0, LoginHelper.getLoginUid());
        if (isTotal3) {
            matchView.showTotal3(teamList);
        } else {
            matchView.showTotal5(teamList);
        }
    }

    private void addTeamUid(int selfUid, List<Integer> totalList, List<Integer> team) {
        if (team.size() >= 2 && team.get(0) != selfUid) {
            totalList.add(team.get(1));
            totalList.add(team.get(0));
        } else {
            totalList.addAll(team);
        }
    }

    private void registerEventBus() {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    public void resume() {
        isResume = true;
        if (match != null && shouldJumpLittleGame) {
            matchView.jumpToGame(match.beforeStartInfo);
            shouldJumpLittleGame = false;
        }
    }

    public void stop() {
        isResume = false;
    }

    public void destroy() {
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
            handler = null;
        }
        shouldJumpLittleGame = false;
        if (!SuspendManager.INSTANCE.isSuspend()) {
            gameStateCheckHelper.stopCheck();
        }
    }

    private void updateTime() {
        matchView.updateTimeSpent((int) (System.currentTimeMillis() - startTime) / 1000);
        if (handler != null) {
            handler.postDelayed(timerRunnable, 1000L);
        }
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    public int getGameType() {
        return gameType;
    }

    public boolean isShowVertical() {
        return LittleGame.getTeamInfo().isMateOneGamerFour();
    }

    public boolean isShowAnim() {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        return teamInfo.isMateOneGamerTwo() || teamInfo.isMateOneGameThree()
                || teamInfo.isMateOneGamerFour() || teamInfo.isMateTwoGamerFour()
                || teamInfo.isMateOneGameFive() || teamInfo.isMateTwoGamerSix()
                || teamInfo.isMateOneGameSix() || teamInfo.isMateOneGameEight();
    }

    public long getStartTime() {
        return startTime;
    }
}
