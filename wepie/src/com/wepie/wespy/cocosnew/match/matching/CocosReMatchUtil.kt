package com.wepie.wespy.cocosnew.match.matching

import androidx.lifecycle.lifecycleScope
import com.huiwan.base.ActivityTaskManager
import com.huiwan.base.util.GlobalEventFlow
import com.huiwan.littlegame.event.WebCocosGameExitEvent
import com.huiwan.platform.ThreadUtil
import com.wejoy.littlegame.LittleGame
import com.wejoy.weplay.ex.context.toLifecycleOwner
import com.wepie.liblog.main.HLog
import com.wepie.wespy.model.entity.match.TeamInfo
import com.wepie.wespy.model.event.iceball.IceStartMatchEvent
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.net.tcp.packet.GameMatchPackets
import com.wepie.wespy.net.tcp.sender.GameMatchSenderKt
import com.wepie.wespy.net.tcp.sender.SyncResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * <AUTHOR> zhangjing
 * @date : 星期一 6/17/24
 * @description : Cocos游戏再来一局逻辑优化：
 * https://wepie.modao.cc/proto/R3XWfLkVsc4bum7BuM2nAz/sharing?view_mode=read_only
 */
object CocosReMatchUtil {

    private const val TAG = "CocosReMatchUtil"
    private const val SCENE_ONE_MORE_ROUND = 1

    /**
     * 从日志看，正常的 Post->消费 只需要花费1ms，这里设置100ms兜底
     */
    private const val CHECK_DELAY_TIME = 100L

    /**
     * 从日志看，正常的 拉取队伍信息，跳转匹配页，大约需要200ms，考虑到网络波动，设置延时为1000ms
     */
    private const val CLOSE_COCOS_ACTIVITY_DELAY = 1000L

    private val consumedEvents: MutableList<String> = mutableListOf()

    /**
     * 兼容Cocos再来一局逻辑和小游戏段位逻辑，如果是因为Cocos再来一局逻辑而回到小游戏首页，不请求最新段位信息。
     */
    private var resumeForCocosRematch: Boolean = false

    private fun onReMatchPushEvent(
        scope: CoroutineScope,
        exceptTime: Int,
        onTeamInfo: (GameMatchPackets.TeamInfo) -> Unit
    ) {
        scope.launch {
            when (val syncResult = GameMatchSenderKt.syncTeamReq()) {
                is SyncResult.SyncTeamReqSuccess -> {
                    val teamInfo = syncResult.teamInfo
                    if (teamInfo.matchScene != SCENE_ONE_MORE_ROUND) {
                        HLog.d(
                            TAG, HLog.USR,
                            "teamInfo is not one more round scene, scene: {}",
                            teamInfo.matchScene
                        )
                        return@launch
                    }
                    HLog.d(
                        TAG, HLog.USR,
                        "rematch sync {}, {}", teamInfo.tid, teamInfo.matchScene
                    )
                    withContext(Dispatchers.Main) {
                        LittleGame.updateTeamInfo(
                            TeamInfo.parse(teamInfo, syncResult.jkEntryBetInfo)
                        )
                        LittleGame.teamMatchExpectedTime = exceptTime
                        onTeamInfo(teamInfo)
                        resumeForCocosRematch = false
                    }
                }

                is SyncResult.Failure -> {
                    HLog.d(TAG, HLog.USR, "handleOneMoreRound onFail: {}", syncResult.head?.message)
                }
            }
        }
    }

    /**
     * 兜底代码，如果再来一局事件没有被消费(如Cocos游戏游玩过程中杀进程再恢复游戏，直接跳转游戏页面，不初始化游戏主页)
     * 则调用兜底逻辑。
     */
    fun onRematchPushEventFallback(event: IceStartMatchEvent) {
        HLog.d(TAG, HLog.USR, "onRematchPushEventFallback {}, {}", event.id(), event.exceptTime)
        ThreadUtil.runOnUiThreadDelay(CHECK_DELAY_TIME) {
            if (!consumedEvents.contains(event.id())) {
                consumedEvents.add(event.id())
                val activity =
                    ActivityTaskManager.getInstance().topActivity ?: return@runOnUiThreadDelay
                val scope = activity.toLifecycleOwner()?.lifecycleScope ?: return@runOnUiThreadDelay
                onReMatchPushEvent(scope, event.exceptTime) { teamInfo ->
                    HLog.d(TAG, HLog.USR, "onRematchPushEventFallback -> onReMatchPushEvent {}", teamInfo.tid)
                    if (TeamInfo.isMorePlayerMatch(teamInfo.gameMode)) {
                        JumpUtil.gotoIceBallMatchPrepareActivity(
                            activity, teamInfo.gameType, false, ""
                        )
                    } else {
                        JumpUtil.gotoIceBallMatchActivity(
                            activity, teamInfo.gameType, false
                        )
                    }
                }
            } else {
                consumedEvents.remove(event.id())
            }
        }
    }

    fun tryCloseCocos() {
        onCocosGameReturnForRematch()
        checkCloseWebCocos()
    }

    private fun onCocosGameReturnForRematch() {
        resumeForCocosRematch = true
    }

    private fun checkCloseWebCocos() {
        ThreadUtil.runOnUiThreadDelay(CLOSE_COCOS_ACTIVITY_DELAY) {
            GlobalEventFlow.postEvent(WebCocosGameExitEvent())
        }
    }

}