package com.wepie.wespy.cocosnew.match.matching;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.user.LoginHelper;
import com.huiwan.user.entity.User;
import com.wepie.wespy.R;


/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/11/16.
 * 匹配时 1 v 1 view
 */

public class CocosMatch1V1View extends AbsCocosMatchView {
    private CocosMatchUserView self;
    private CocosMatchUserView opponent;

    public CocosMatch1V1View(Context context) {
        super(context);
        initView();
    }

    public CocosMatch1V1View(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public CocosMatch1V1View(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    @Override
    public void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.ice_ball_match_1v1_view, this);
        self = findViewById(R.id.ice_ball_match_member0);
        opponent = findViewById(R.id.ice_ball_match_member1);
    }

    @Override
    public void showSelf(){
        /* 理论上判断是多余的， 但这里的淡黄色提示看着很不舒服 */
        User user = LoginHelper.getLoginUser();
        if (user != null){
            self.update(user.getNickname(), user.headimgurl, user.gender);
        }
    }

    @Override
    public void setEmptyImg(@NonNull Drawable drawable) {
        if (self != null) {
            self.setEmptyImg(drawable);
        }
        if (opponent != null) {
            opponent.setEmptyImg(drawable);
        }
    }

    @Override
    public void showOpponent(int uid){
        opponent.update(uid);
    }

}
