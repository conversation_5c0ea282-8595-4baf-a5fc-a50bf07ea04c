package com.wepie.wespy.cocosnew.match.matching;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;

import com.wepie.wespy.R;

import java.util.List;


/**
 * 6人局，后续可以支持3v3
 */
public class CocosMatchTotal6View extends AbsCocosMatchView {

    private final CocosMatchUserView[] members = new CocosMatchUserView[6];
    private final View[] lines = new View[2];
    private boolean is3V3;

    public CocosMatchTotal6View(Context context, boolean is3V3) {
        super(context);
        this.is3V3 = is3V3;
        initView();
    }

    @Override
    public void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.ice_ball_match_total6_view, this);
        if (is3V3) {
            members[0] = findViewById(R.id.ice_ball_match_member0);
            members[1] = findViewById(R.id.ice_ball_match_member1);
            members[2] = findViewById(R.id.ice_ball_match_member2);
            members[3] = findViewById(R.id.ice_ball_match_member3);
            members[4] = findViewById(R.id.ice_ball_match_member4);
            members[5] = findViewById(R.id.ice_ball_match_member5);
        } else {
            members[0] = findViewById(R.id.ice_ball_match_member0);
            members[1] = findViewById(R.id.ice_ball_match_member3);
            members[2] = findViewById(R.id.ice_ball_match_member4);
            members[3] = findViewById(R.id.ice_ball_match_member5);
            members[4] = findViewById(R.id.ice_ball_match_member2);
            members[5] = findViewById(R.id.ice_ball_match_member1);
        }
        lines[0] = findViewById(R.id.h_line0);
        lines[1] = findViewById(R.id.h_line1);
    }

    @Override
    public void updateSeatInfo(@NonNull List<Integer> uidList, boolean showVerticalLine) {
        for (int i = 0; i < members.length; i++) {
            if (i < uidList.size()) {
                int uid = uidList.get(i);
                members[i].update(uid);
            }
        }
        if (showVerticalLine) {
            for (View line : lines) {
                line.setVisibility(View.VISIBLE);
            }
        } else {
            for (View line : lines) {
                line.setVisibility(View.GONE);
            }
        }
    }


    @Override
    public void setEmptyImg(@NonNull Drawable drawable) {
        for (CocosMatchUserView view : members) {
            if (view != null) {
                view.setEmptyImg(drawable);
            }
        }
    }
}
