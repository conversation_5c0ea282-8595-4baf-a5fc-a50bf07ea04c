package com.wepie.wespy.cocosnew.match.matching;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.User;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.CustomCircleImageView;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;

/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/11/16.
 */

public class CocosMatchUserView extends LinearLayout {
    private CustomCircleImageView headerIv;
    private ImageView genderIv;
    private ImageView emptyIv;
    private TextView nameTv;

    public CocosMatchUserView(Context context) {
        super(context);
        init();
    }

    public CocosMatchUserView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public CocosMatchUserView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        LayoutInflater.from(getContext()).inflate(getLayoutId(), this);
        headerIv = findViewById(R.id.ice_ball_match_member_header_iv);
        genderIv = findViewById(R.id.ice_ball_match_member_gender_iv);
        emptyIv = findViewById(R.id.empty_iv);
        nameTv = findViewById(R.id.ice_ball_match_member_name_tv);
    }

    protected int getLayoutId() {
        return R.layout.ice_ball_match_user_view;
    }

    public void update(int uid) {
        UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo userInfo) {
                if (userInfo == null) {
                    return;
                }
                update(userInfo.getRemarkName(), userInfo.headimgurl, userInfo.gender);
            }

            @Override
            public void onUserInfoFailed(String description) {
                HLog.e(TAG, description);
            }
        });
    }

    public void update(String name, String headUrl, int gender) {
        HeadImageLoader.loadCircleHeadImage(headUrl, headerIv);
        nameTv.setText(name);
        emptyIv.setVisibility(View.GONE);
        if (gender == User.GENDER_MALE) {
            genderIv.setImageResource(R.drawable.icon_male);
        } else if (gender == User.GENDER_FEMALE) {
            genderIv.setImageResource(R.drawable.icon_female);
        }
    }

    public void setEmptyImg(Drawable drawable) {
        emptyIv.setBackground(drawable);
    }

    private final static String TAG = CocosMatchUserView.class.getSimpleName();
}
