package com.wepie.wespy.cocosnew.match.matching;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.wepie.wespy.R;

import java.util.List;


/**
 * 5人匹配使用
 */

public class CocosMatchTotal5View extends AbsCocosMatchView {

    private final CocosMatchUserView[] members = new CocosMatchUserView[5];

    public CocosMatchTotal5View(Context context) {
        super(context);
        initView();
    }

    public CocosMatchTotal5View(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public CocosMatchTotal5View(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
    }

    @Override
    public void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.ice_ball_match_total5_view, this);
        members[0] = findViewById(R.id.ice_ball_match_member0);
        members[1] = findViewById(R.id.ice_ball_match_member1);
        members[2] = findViewById(R.id.ice_ball_match_member2);
        members[3] = findViewById(R.id.ice_ball_match_member3);
        members[4] = findViewById(R.id.ice_ball_match_member4);
    }

    @Override
    public void updateSeatInfo(@NonNull List<Integer> uidList) {
        for (int i = 0; i < members.length; i++) {
            if (i < uidList.size()) {
                int uid = uidList.get(i);
                members[i].update(uid);
            }
        }
    }

    @Override
    public void setEmptyImg(@NonNull Drawable drawable) {
        for (CocosMatchUserView view : members) {
            if (view != null) {
                view.setEmptyImg(drawable);
            }
        }
    }
}
