package com.wepie.wespy.cocosnew.match

import android.os.Handler
import android.os.Looper
import androidx.collection.ArrayMap
import androidx.lifecycle.LifecycleOwner
import com.huiwan.base.ActivityTaskManager
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.wejoy.gamematch.net.GameMatchSender
import com.wejoy.littlegame.LittleGame
import com.wejoy.weplay.ex.GlobalLife
import com.wejoy.weplay.ex.ILife
import com.wejoy.weplay.ex.lifecycle.toLife
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.wespy.model.entity.EnterRoomInfo
import com.wepie.wespy.model.entity.match.TeamInfo
import com.wepie.wespy.module.common.jump.JumpRoomUtil
import com.wepie.wespy.net.tcp.packet.GameMatchPackets
import com.wepie.wespy.net.tcp.packet.GameMatchPackets.SyncTeamRsp

object LittleGameStateCheckHelper {

    /** 快速开始  */
    const val GAME_MODE_MATCH: Int = 1

    const val UNITY_SOURCE_FACTOR: Int = 10
    var MATCH_GAME: Int = 1
    var CREATE_ROOM: Int = 2
    var SEARCH_ROOM: Int = 3
    var FOLLOW_ROOM: Int = 4
    var INVITE_GAME: Int = 5
    var RESTART_GAME: Int = 6

    private val handler = Handler(Looper.getMainLooper())
    private val checkGameStateRunnable = Runnable { syncTeam() }
    private var successCallback: Runnable = Runnable { }
    private var stopCheck = false

    fun checkGameState() {
        stopCheck = false
        handler.removeCallbacks(checkGameStateRunnable)
        handler.postDelayed(checkGameStateRunnable, 3000L)
    }

    private fun syncTeam() {
        GameMatchSender.syncTeamReq(object : LifeSeqCallback(GlobalLife) {
            override fun onSuccess(head: RspHeadInfo) {
                if (stopCheck) {
                    return
                }

                val syncTeamRsp = (head.message as? SyncTeamRsp) ?: return
                val teamInfo = TeamInfo.parse(syncTeamRsp.team, syncTeamRsp.jkEntryBetInfo)
                LittleGame.updateTeamInfo(teamInfo)
                if (teamInfo.getState() == GameMatchPackets.TeamState.InGame_VALUE) {
                    stopCheck()
                    enterGameRoom(teamInfo, syncTeamRsp.matchSource, syncTeamRsp.gameBasicInfo)
                    successCallback.run()
                } else {
                    checkGameState()
                }
            }

            override fun onFail(head: RspHeadInfo) {
                if (stopCheck) {
                    return
                }
                checkGameState()
            }
        })
    }

    fun setSuccessCallback(owner: LifecycleOwner, runnable: Runnable) {
        setSuccessCallback(owner.toLife(), runnable)
    }

    fun setSuccessCallback(life: ILife, runnable: Runnable) {
        successCallback = runnable
        life.onDestroy {
            successCallback = Runnable { }
        }
    }

    private fun enterGameRoom(
        info: TeamInfo,
        matchSource: Int,
        gameBasicInfo: GameMatchPackets.GameBasicInfo
    ) {
        val activity = ActivityTaskManager.getInstance().topActivity ?: return
        val enterRoomInfo: EnterRoomInfo = if (matchSource == 1) {
            val map: MutableMap<String, Any> = ArrayMap()
            map["sub_game_type"] = gameBasicInfo.littleGameType
            map["match_mode"] = TeamInfo.trackMatchMode(gameBasicInfo.gameMode, gameBasicInfo.mode)
            map["mode_type"] =
                TeamInfo.trackModeType(gameBasicInfo.littleGameType, gameBasicInfo.gameMode)
            map["bet_level"] = gameBasicInfo.betLevel
            EnterRoomInfo.buildEnterRoom(activity, info.rid, info.gameType)
                .setGameMode(GAME_MODE_MATCH).setSource(TrackSource.LITTLE_GAME)
        } else {
            EnterRoomInfo.buildEnterRoom(activity, info.rid, info.gameType)
                .setGameMode(GAME_MODE_MATCH)
                .setGameSource(info.gameType * UNITY_SOURCE_FACTOR + RESTART_GAME)
        }
        JumpRoomUtil.getInstance().enterRoom(enterRoomInfo)
    }

    fun stopCheck() {
        stopCheck = true
        handler.removeCallbacks(checkGameStateRunnable)
    }

}