package com.wepie.wespy.cocosnew.update;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.FileUtil;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * Created by bigwen on 2020-02-12.
 */
public class CocosUnpackUtil {
    private static final String TAG = "CocosUnpackUtil";

    private static final int BUFFER_LEN = 1024;

    public static MethodResult unpack(InputStream is, boolean isMatchRes, String path) {
        ZipInputStream zipIs = null;
        HLog.d(TAG, HLog.USR, "unpack isMatchRes={}, path={}", isMatchRes, path);
        try {
            zipIs = new ZipInputStream(is);
            ZipEntry ze;
            while ((ze = zipIs.getNextEntry()) != null) {
                //这里的path是/data/user/0/com.wejoy.weplay/ 本身就是虚拟的,需要转换一下
                //getCanonicalPath会变成 /data/data/com.wejoy.weplay/
                //因此在Android下需要都转换成真实路径做比较
                String destDir = new File(path).getCanonicalPath();
                File destFile = new File(path, ze.getName());
                String canonicalPath = destFile.getCanonicalPath();
                if (!canonicalPath.startsWith(destDir)) {
                    //https://support.google.com/faqs/answer/9294009
                    //zip解压路径攻击,每次解压新文件的时候,需要判断将要解压的路径是否在目标路径下
                    FLog.e(new Throwable("Zip Path Traversal Vulnerability Finish unzipping"));
                    return new MethodResult(MethodResult.METHOD_ERROR_UNPACK_FAIL, ResUtil.getStr(R.string.cocos_unpack_error));
                }
                if (ze.isDirectory()) {
                    if (isMatchRes) {
                        FileUtil.makeDirs(new File(path));
                    } else {
                        FileUtil.makeDirs(new File(path + ze.getName()));
                    }
                } else {
                    if (isMatchRes) {
                        cpyEntry(zipIs, path + FileUtil.getFileNameFromPath(ze.getName()));
                    } else {
                        cpyEntry(zipIs, path + ze.getName());
                    }
                    zipIs.closeEntry();
                }
            }
            HLog.d(TAG, HLog.USR, "unpack success!");
            return new MethodResult(MethodResult.METHOD_SUCCESS);
        } catch (Exception e) {
            HLog.d(TAG, HLog.USR, "unpack error! msg=" + e);
            FLog.e(new RuntimeException("Unpack Little Game Error", e));
            return new MethodResult(MethodResult.METHOD_ERROR_UNPACK_FAIL, ResUtil.getStr(R.string.cocos_unpack_error));
        } finally {
            if (zipIs != null) {
                try {
                    zipIs.close();
                } catch (IOException e) {
                    HLog.d(TAG, HLog.USR, "unpack finally error! msg=" + e);
                }
            }
        }
    }

    public static void cpyEntry(InputStream is, String destPath) throws Exception {
        FileOutputStream fos = null;
        try {
            FileUtil.createFile(destPath);
            fos = new FileOutputStream(destPath);
            byte[] buffer = new byte[BUFFER_LEN];
            int readByte;
            while ((readByte = is.read(buffer)) != -1) {
                fos.write(buffer, 0, readByte);
            }

        } catch (Exception e) {
            HLog.d(TAG, HLog.USR, "cpyEntry error! msg=" + e);
            FileUtil.safeDeleteFile(destPath);
            throw new Exception("Unpack Little Game Copy Failed, ", e);
        } finally {
            try {
                if (fos != null) {
                    fos.close();
                }
            } catch (Exception e) {
                HLog.d(TAG, HLog.USR, "cpyEntry finally error! msg=" + e);
            }
        }
    }
}
