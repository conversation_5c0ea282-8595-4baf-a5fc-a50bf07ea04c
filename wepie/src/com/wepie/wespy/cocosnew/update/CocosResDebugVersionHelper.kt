package com.wepie.wespy.cocosnew.update

import com.google.gson.reflect.TypeToken
import com.huiwan.base.util.JsonUtil
import com.huiwan.store.PrefUtil
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow


object CocosResDebugVersionHelper {
    private val key = PrefUtil.LOCAL_SPECIFY_COCOS_GAME_VERSION
    private val memMap: MutableMap<Int, Int> by lazy {
        java.util.HashMap(loadAllVersionsInner())
    }

    private fun loadAllVersionsInner(): Map<Int, Int> {
        val mapStr = PrefUtil.getInstance().getString(key, "{}")
        val mapType = object : TypeToken<Map<Int, Int>>() {}.type
        try {
            return JsonUtil.getGson().fromJson(mapStr, mapType) as Map<Int, Int>
        } catch (e: Exception) {
            return emptyMap()
        }
    }

    fun clearAllVersions() {
        PrefUtil.getInstance().setString(key, "")
        memMap.clear()
    }

    fun setGameResVersion(gameType: Int, version: Int): Flow<Boolean> {
        memMap[gameType] = version
        return saveAllVersions(memMap)
    }

    fun loadVersion(gameType: Int): Int {
        return memMap[gameType] ?: 0
    }

    fun loadAllVersions(): Flow<Map<Int, Int>> {
        val map = loadAllVersionsInner()
        return flow {
            emit(map)
        }
    }

    fun saveAllVersions(map: Map<Int, Int>): Flow<Boolean> {
        try {
            PrefUtil.getInstance().setString(key, JsonUtil.toJson(map))
            memMap.putAll(map)
        } catch (e: Exception) {
            return flow {
                emit(false)
            }
        }
        return flow {
            emit(true)
        }
    }

}