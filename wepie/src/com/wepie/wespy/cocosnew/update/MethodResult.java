package com.wepie.wespy.cocosnew.update;

/**
 * Created by <PERSON><PERSON> on 2020-02-12.
 */
public class MethodResult {

    public static final int METHOD_SUCCESS = 0;
    public static final int METHOD_ERROR_ON_WORK = -1;
    public static final int METHOD_ERROR_DOWNLOAD_FAIL = -2;
    public static final int METHOD_ERROR_UNPACK_FAIL = -3;
    public static final int METHON_ERROR_CREATE_FAIL = -4;

    private int code = METHOD_SUCCESS;
    private String msg = "";

    public MethodResult(int code) {
        this.code = code;
    }

    public MethodResult(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public boolean isSuccess() {
        return code == METHOD_SUCCESS;
    }

    @Override
    public String toString() {
        return "MethodResult{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                '}';
    }
}
