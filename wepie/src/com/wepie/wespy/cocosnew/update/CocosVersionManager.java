package com.wepie.wespy.cocosnew.update;

import android.text.TextUtils;
import android.util.Pair;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.configservice.international.service.GlobalConfigManager;
import com.huiwan.constants.GameType;
import com.huiwan.lib.api.ApiService;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.littlegame.cocos.CocosConstantsKt;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.store.file.FileCacheName;
import com.huiwan.store.file.FileConfig;
import com.huiwan.store.file.FileManager;
import com.wejoy.gamematch.net.GameMatchSender;
import com.wejoy.littlegame.ILittleGameApi;
import com.wejoy.weplay.ex.GlobalLife;
import com.wejoy.weplay.ex.ILifeOwnerKt;
import com.wejoy.weplay.ex.cancellable.Cancellable;
import com.wepie.download.DownloadCallback;
import com.wepie.download.DownloadUtil;
import com.wepie.download.Downloader;
import com.wepie.download.LifeDownloadCallback;
import com.wepie.download.SimpleDownloadCallback;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.cocosnew.callback.LifeUpdateCallback;
import com.wepie.wespy.cocosnew.callback.UpdateCallback;
import com.wepie.wespy.helper.prefrence.MatchResPrefUtil;
import com.wepie.wespy.model.entity.match.GameVersionUnit;
import com.wepie.wespy.net.tcp.packet.GameMatchPackets;

import org.jetbrains.annotations.TestOnly;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLongArray;

/**
 * <AUTHOR> date 2017/11/27
 * <p>
 * 重构思路：状态扭转，callback的执行尽量放在一个函数内进行，一般放在开启子线程的函数，同步调用的函数设置返回值
 */

public class CocosVersionManager {

    private static final String TAG = "CocosVersionManager";
    private static final String VERSION_KEY = "region_version";
    private static final String VERSION_FILE_NAME = "version.properties";
    private static final String RENAME_RES_SUFFIX = "_rename.zip";

    private final HashMap<Integer, Pair<String, Integer>> gameVersionMap = new HashMap<>();//内存维护本地版本号，避免重复从磁盘读取
    private final HashMap<Integer, Pair<String, Integer>> gameVersionWebMap = new HashMap<>();//内存维护本地版本号，避免重复从磁盘读取
    @TestOnly
    public final Map<Integer, String> inGameResZipUrl = new HashMap<>();
    private CocosVersionManager() {
    }

    public boolean isOldCocoUseWeb() {
        ILittleGameApi littleGameApi = ApiService.of(ILittleGameApi.class);
        if (littleGameApi != null) {
            return littleGameApi.isCocosUseWeb();
        }
        return false;
    }

    public void preloadVersion(List<Integer> gameTypeList) {
        String currentRegion = GlobalConfigManager.getInstance().getRegion();
        boolean isUseWeb = isOldCocoUseWeb();
        Map<Integer, Pair<String, Integer>> map = new ArrayMap<>();
        for (Integer gameType : gameTypeList) {
            map.put(gameType, getLocalVersionWithRegion(currentRegion, gameType, isUseWeb));
        }
        ThreadUtil.runOnUiThread(() -> {
            Map<Integer, Pair<String, Integer>> realMap = isUseWeb ? gameVersionWebMap : gameVersionMap;
            for (Map.Entry<Integer, Pair<String, Integer>> entry : map.entrySet()) {
                int gameType = entry.getKey();
                if (realMap.get(gameType) == null) {
                    realMap.put(gameType, entry.getValue());
                }
            }
        });
    }

    public void updateMatch(int type, LifeUpdateCallback updateCallback) {
        HLog.d(TAG, "updateMatch {}", type);
        downloadAndUpdateMatch(type, updateCallback);
    }

    public void updateGame(final int gameType, @NonNull final LifeUpdateCallback callback) {
        updateGame(gameType, isOldCocoUseWeb(), callback);
    }

    public void updateGame(final int gameType, boolean isWebGame, @NonNull final LifeUpdateCallback callback) {
        HLog.d(TAG, HLog.USR, "updateGame {}, isWebGame={}", gameType, isWebGame);
        int localSpecifyGameVersion = CocosResDebugVersionHelper.INSTANCE.loadVersion(gameType);
        GameMatchSender.getGameVerReq(gameType, localSpecifyGameVersion, new LifeSeqCallback(callback.getLife()) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                GameMatchPackets.GetGameVerRsp getGameVerRsp = (GameMatchPackets.GetGameVerRsp) head.message;
                HLog.d(TAG, HLog.USR, "updateGame: {}, isWebGame={}", getGameVerRsp, isWebGame);
                handlerUpdateRes(gameType, isWebGame, GameVersionUnit.getGameVersionUnit(gameType, getGameVerRsp), callback);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                invokeDownloadFail(callback);
                ToastUtil.show(head.desc);
            }
        });
    }

    public Cancellable updateGameAndMatch(final int gameType, @NonNull final LifeUpdateCallback updateCallback) {
        return updateGameAndMatch(gameType, isOldCocoUseWeb(), updateCallback);
    }

    public Cancellable updateGameAndMatch(final int gameType, boolean isWebGame, @NonNull final LifeUpdateCallback updateCallback) {
        HLog.d(TAG, HLog.USR, "updateGameAndMatch! gameType={}, isWebGame={}", gameType, isWebGame);
        int localSpecifyGameVersion = CocosResDebugVersionHelper.INSTANCE.loadVersion(gameType);
        return GameMatchSender.getGameVerReq(gameType, localSpecifyGameVersion, new LifeSeqCallback(ILifeOwnerKt.getLife(updateCallback)) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                GameMatchPackets.GetGameVerRsp getGameVerRsp = (GameMatchPackets.GetGameVerRsp) head.message;
                GameVersionUnit unit = GameVersionUnit.getGameVersionUnit(gameType, getGameVerRsp);
                HLog.d(TAG, HLog.USR, "updateGameAndMatch, onSuccess! unit={}", unit);
                if (unit == null) {
                    ToastUtil.show(ResUtil.getStr(R.string.cocos_version_get_error_1));
                    return;
                }
                downloadAndUpdateBoth(unit, isWebGame, updateCallback);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                HLog.d(TAG, HLog.USR, "updateGameAndMatch, onFail!");
                invokeDownloadFail(updateCallback);
                ToastUtil.show(head.desc);
            }
        });
    }

    private void handlerUpdateRes(int gameType, boolean isWebGame, GameVersionUnit unit, @NonNull final LifeUpdateCallback callback) {
        HLog.d(TAG, HLog.USR, "handlerUpdateRes start, gameType={}, isWebGame={}", gameType, isWebGame);
        int serverVersion = unit.getGameVersion();
        String updateUrl = unit.getUpdateUrl(isWebGame);

        if (serverVersion != getGameVersion(gameType, isWebGame)) {
            downloadAndUpdateGame(gameType, updateUrl, serverVersion, isWebGame, callback);
        } else {
            invokeFinishTask(callback);
        }
    }

    private void downloadAndUpdateGame(final int gameType, final String updateUrl, final int version, boolean isWebGame, @NonNull final LifeUpdateCallback callback) {
        HLog.d(TAG, HLog.USR, "downloadAndUpdateGame start, gameType={}, isWebGame={}", gameType, isWebGame);
        String gameResPath = getDownloadGameName(gameType, isWebGame);
        String renameGameResPath = getRenameFilePath(gameResPath);
        FileUtil.renameTo(gameResPath, renameGameResPath);
        DownloadUtil.downloadFile(updateUrl, gameResPath, true, new LifeDownloadCallback(callback.getLife()) {
            @Override
            public void onSuccess(String url, String path) {
                HLog.d(TAG, HLog.USR, "downloadAndUpdateGame onSuccess");
                ThreadUtil.runInOtherThread(() -> unpackGame(gameType, version, isWebGame, callback));
            }

            @Override
            public void onFail(String msg) {
                HLog.d(TAG, HLog.USR, "downloadAndUpdateGame onFail, msg:{}", msg);
                invokeDownloadFail(callback);
            }

            @Override
            public void onPercent(int percent, long cur, long total) {
                callback.onDownloadUpdate(percent);
                callback.onDownloadUpdate(percent, (int) cur, (int) total);
            }
        });
        ThreadUtil.runInOtherThread(() -> {
            FileUtil.safeDeleteFile(renameGameResPath);
        });
    }

    private String getRenameFilePath(String resPath) {
        if (!TextUtils.isEmpty(resPath) && resPath.contains(".zip")) {
            return resPath.replace(".zip", RENAME_RES_SUFFIX);
        }
        return resPath;
    }

    private void downloadAndUpdateMatch(final int gameType, final LifeUpdateCallback callback) {
        final String matchUrl = getMatchResDownloadUrl(gameType);
        HLog.d(TAG, HLog.USR, "downloadAndUpdateMatch,gameType={},matchUrl={}", gameType, matchUrl);
        if (TextUtils.isEmpty(matchUrl)) {
            ConfigHelper.getInstance().updateGameConfig(new ConfigHelper.UpdateCallback() {
                @Override
                public void onSuccess() {
                    doDownloadAndUnzipMatch(gameType, callback, matchUrl);
                }

                @Override
                public void onFailed(String msg) {
                    invokeDownloadFail(callback);
                }
            });
        } else {
            doDownloadAndUnzipMatch(gameType, callback, matchUrl);
        }
    }

    public void checkUpdateDownloadInviteGame(int gameType, boolean isWebGame, @NonNull UpdateCallback resCallback, @NonNull UpdateCallback gameCallback) {
        checkUpdateDownloadInviteInternal(gameType, isWebGame, getMatchResDownloadUrl(gameType), resCallback, gameCallback);
    }

    private void checkUpdateDownloadInviteInternal(int gameType, boolean isWebGame, String matchUrl, @NonNull UpdateCallback resCallback, @NonNull UpdateCallback gameCallback) {
        if (!isLocalMatchAResAvailable(gameType)) {
            // 消息更新时这里不强制更新匹配界面资源
            Downloader.newBuilder()
                    .setUrl(matchUrl)
                    .setShouldDownloadImmediate(true)
                    .setRetryTimes(3)
                    .setCacheFilePath(getDownloadMatchResName(gameType))
                    .download(new SimpleDownloadCallback() {
                        @Override
                        public void onSuccess(String url, String path) {
                            ThreadUtil.runInOtherThread(() -> unpackMatchFile(gameType, resCallback, true));
                        }

                        @Override
                        public void onFail(String msg) {
                            super.onFail(msg);
                            if (resCallback != null) {
                                resCallback.onDownloadFailed();
                            }
                        }
                    });
        } else {
            invokeFinishTask(resCallback);
        }
        int localSpecifyGameVersion = CocosResDebugVersionHelper.INSTANCE.loadVersion(gameType);
        GameMatchSender.getGameVerReq(gameType, localSpecifyGameVersion, new LifeSeqCallback(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                GameMatchPackets.GetGameVerRsp getGameVerRsp = (GameMatchPackets.GetGameVerRsp) head.message;
                GameVersionUnit unit = GameVersionUnit.getGameVersionUnit(gameType, getGameVerRsp);
                int serverVersion = unit.getGameVersion();
                String updateUrl = unit.getUpdateUrl(isWebGame);
                if (serverVersion > getGameVersion(gameType, isWebGame)) {
                    Downloader.newBuilder()
                            .setUrl(updateUrl)
                            .setShouldDownloadImmediate(true)
                            .setRetryTimes(3)
                            .setCacheFilePath(getDownloadGameName(gameType, isWebGame))
                            .download(new SimpleDownloadCallback() {
                                @Override
                                public void onSuccess(String url, String path) {
                                    ThreadUtil.runInOtherThread(() -> unpackGame(gameType, serverVersion, isWebGame, gameCallback));
                                }

                                @Override
                                public void onFail(String msg) {
                                    super.onFail(msg);
                                    if (resCallback != null) {
                                        resCallback.onDownloadFailed();
                                    }
                                }
                            });
                } else {
                    invokeFinishTask(gameCallback);
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private void doDownloadAndUnzipMatch(final int gameType, @NonNull final LifeUpdateCallback callback, final String matchUrl) {
        HLog.d(TAG, HLog.USR, "doDownloadAndUnzipMatch start, gameType={}", gameType);
        String matchResPath = getDownloadMatchResName(gameType);
        String renameMatchResPath = getRenameFilePath(matchResPath);
        FileUtil.renameTo(matchResPath, renameMatchResPath);
        DownloadUtil.downloadFile(matchUrl, matchResPath, true, new LifeDownloadCallback(callback.getLife()) {
            @Override
            public void onSuccess(String url, String path) {
                HLog.d(TAG, HLog.USR, "doDownloadAndUnzipMatch onSuccess");
                ThreadUtil.runInOtherThread(() -> {
                    deleteOldMatchRes(gameType);
                    unpackMatchFile(gameType, callback, true);
                });
            }

            @Override
            public void onFail(String msg) {
                HLog.d(TAG, HLog.USR, "doDownloadAndUnzipMatch onFail, msg:{}", msg);
                invokeDownloadFail(callback);
            }

            @Override
            public void onPercent(int percent, long cur, long total) {
                callback.onDownloadUpdate(percent);
                callback.onDownloadUpdate(percent, (int) cur, (int) total);
            }
        });
        ThreadUtil.runInOtherThread(() -> {
            FileUtil.safeDeleteFile(renameMatchResPath);
        });
    }

    private boolean unpackMatchFile(int gameType, @NonNull final UpdateCallback callback, boolean finishIfSuccess) {
        deleteOldMatchRes(gameType);
        String matchFileName = getDownloadMatchResName(gameType);
        try (InputStream is = new FileInputStream(matchFileName)) {
            MethodResult unpackResult = unpackMatch(is, gameType);
            if (unpackResult.isSuccess()) {
                updateMatchResVersion(gameType, ConfigHelper.getInstance().getGameConfig(gameType).getMatchResUrlAn());
                if (finishIfSuccess) {
                    invokeFinishTask(callback);
                }
                FileUtil.safeDeleteFile(matchFileName);
            } else {
                invokeUnpackFail(callback);
            }
            HLog.d(TAG, HLog.USR, "unpackMatchFile,unpackResult={}", unpackResult);
            return unpackResult.isSuccess();
        } catch (IOException e) {
            HLog.d(TAG, HLog.USR, "unpackMatchFile error! e={}", e);
            invokeUnpackFail(callback);
            return false;
        }
    }

    private void downloadAndUpdateBoth(GameVersionUnit unit, boolean isWebGame, @NonNull final UpdateCallback callback) {
        HLog.d(TAG, "downloadAndUpdateBoth: {}", unit.gameType);
        final String matchUrl = getMatchResDownloadUrl(unit.gameType);
        HLog.d(TAG, HLog.USR, "downloadAndUpdateBoth! matchUrl=" + matchUrl);
        if (TextUtils.isEmpty(matchUrl)) {
            ConfigHelper.getInstance().updateGameConfig(new ConfigHelper.UpdateCallback() {
                @Override
                public void onSuccess() {
                    doDownloadAndUnzipBoth(unit, matchUrl, isWebGame, callback);
                }

                @Override
                public void onFailed(String msg) {
                    invokeDownloadFail(callback);
                }
            });
        } else {
            doDownloadAndUnzipBoth(unit, matchUrl, isWebGame, callback);
        }
    }

    private void doDownloadAndUnzipBoth(GameVersionUnit unit, final String matchUrl, boolean isWebGame, @NonNull final UpdateCallback callback) {
        int gameType = unit.gameType;
        int version = unit.gameVersion;
        String matchResPath = getDownloadMatchResName(gameType);
        String gameResPath = getDownloadGameName(gameType, isWebGame);
        String renameMatchResPath = getRenameFilePath(matchResPath);
        String renameGameResPath = getRenameFilePath(gameResPath);
        HLog.d(TAG, HLog.USR, "doDownloadAndUnzipBoth,gameResourceType={}, gameName={}, updateUrl={} ", matchResPath, gameResPath, unit.updateUrl);
        ThreadUtil.runInOtherThread(() -> {
            FileUtil.renameTo(matchResPath, renameMatchResPath);
            FileUtil.renameTo(gameResPath, renameGameResPath);

            AtomicLongArray longArray = new AtomicLongArray(new long[]{-1, -1, -1, -1});

            DownloadCallback realCallback = new DownloadCallback() {
                final AtomicInteger successCount = new AtomicInteger(0);
                volatile boolean isFinish = false;
                volatile int totalSize = 0;

                @Override
                public void onSuccess(String url, String path) {
                    int count = successCount.incrementAndGet();
                    if (count != 2 || isFinish) {
                        return;
                    }
                    ThreadUtil.runInOtherThread(() -> {
                        boolean unpackMatchSuccess = unpackMatchFile(gameType, callback, false);
                        if (unpackMatchSuccess) {
                            unpackGame(gameType, version, isWebGame, callback);
                        }
                    });
                }

                @Override
                public void onFail(String msg) {
                    if (isFinish) {
                        return;
                    }
                    isFinish = true;
                    if (callback != null) {
                        callback.onDownloadFailed();
                    }
                }

                @Override
                public void onPercent(int percent, long cur, long total) {
                    if (isFinish) {
                        return;
                    }
                    HLog.d(TAG, HLog.USR, "download both " + cur + " " + total);
                    if (totalSize == 0) {
                        long matchSize = longArray.get(0);
                        long resSize = longArray.get(2);
                        if (matchSize < 0 || resSize < 0) {
                            return;
                        }
                        totalSize = (int) (matchSize + resSize);
                    }

                    long matchCurrentSize = longArray.get(1);
                    long resCurrentSize = longArray.get(3);
                    if (callback != null) {
                        int current = (int) (matchCurrentSize + resCurrentSize);
                        float per = current * 1.0F / totalSize;
                        HLog.d(TAG, HLog.USR, "download both merge " + current + " " + totalSize);
                        callback.onDownloadUpdate((int) (per * 100));
                        callback.onDownloadUpdate((int) (per * 100), current, totalSize);
                    }
                }
            };

            downloadMatchRes(matchUrl, matchResPath, longArray, realCallback);
            downloadGameRes(unit, gameResPath, longArray, isWebGame, realCallback);
            FileUtil.safeDeleteFile(renameMatchResPath);
            FileUtil.safeDeleteFile(renameGameResPath);
        });
    }

    private void downloadMatchRes(String url, String path, AtomicLongArray longArray, DownloadCallback callback) {
        DownloadUtil.downloadFile(url, path, true, new DownloadCallback() {
            @Override
            public void onSuccess(String url, String path) {
                HLog.d(TAG, HLog.USR, "downloadMatchRes onSuccess");
                callback.onSuccess(url, path);
            }

            @Override
            public void onFail(String msg) {
                HLog.d(TAG, HLog.USR, "downloadMatchRes onFail, msg:{}", msg);
                longArray.set(0, 0);
                longArray.set(1, 0);
                callback.onFail(msg);
            }

            @Override
            public void onPercent(int percent, long cur, long total) {
                longArray.set(0, total);
                longArray.set(1, cur);
                callback.onPercent(percent, cur, total);
            }
        });
    }

    private void downloadGameRes(GameVersionUnit unit, String gameName, AtomicLongArray longArray, boolean isWebGame, DownloadCallback callback) {
        String downloadUrl;
        if (isWebGame) {
            downloadUrl = unit.webUpdateUrl;
        } else {
            downloadUrl = unit.updateUrl;
        }
        DownloadUtil.downloadFile(downloadUrl, gameName, true, new DownloadCallback() {
            @Override
            public void onSuccess(String url, String path) {
                HLog.d(TAG, HLog.USR, "downloadGameRes onSuccess");
                callback.onSuccess(url, path);
            }

            @Override
            public void onFail(String msg) {
                HLog.d(TAG, HLog.USR, "downloadGameRes onFail, msg:{}", msg);
                longArray.set(2, 0);
                longArray.set(3, 0);
                callback.onFail(msg);
            }

            @Override
            public void onPercent(int percent, long cur, long total) {
                longArray.set(2, total);
                longArray.set(3, cur);
                callback.onPercent(percent, cur, total);
            }
        });
    }

    public MethodResult unpackMatch(InputStream is, int gameType) {
        MethodResult result = CocosUnpackUtil.unpack(is, true, getMatchResDir(gameType));
        if (result.isSuccess()) {
            GameConfig gameConfig = ConfigHelper.getInstance().getGameConfig(gameType);
            //没有generateHome说明是老版,校验文件
            if (isCheckMatchImg(gameConfig)) {
                List<GameConfig.MatchInfo> matchInfoList = gameConfig.getMatchInfoList();
                for (int i = 0; i < matchInfoList.size(); i++) {
                    GameConfig.MatchInfo matchInfo = matchInfoList.get(i);
                    String path = getMatchResDir(gameType) + "mode_" + (i + 1) + ".png";
                    String imageUrl = matchInfo.getImgUrl();
                    boolean flag = !downloadFileSync(imageUrl, path);
                    HLog.d(TAG, HLog.USR, "unpackMatch,path={},imageUrl={},flag={}", path, imageUrl, flag);
                    if (flag) {
                        return new MethodResult(MethodResult.METHOD_ERROR_DOWNLOAD_FAIL, ResUtil.getStr(R.string.cocos_version_get_error_2));
                    }
                }
            }

        }
        return result;
    }

    private void updateMatchResVersion(int gameType, String targetVersion) {
        MatchResPrefUtil.getInstance().setString(MatchResPrefUtil.MATCH_TYPE_PREFFIX + gameType, targetVersion);
    }

    private boolean downloadFileSync(String url, String path) {
        HLog.d(TAG, HLog.USR, "to download mode url: {}", url);
        CountDownLatch latch = new CountDownLatch(1);
        AtomicBoolean res = new AtomicBoolean(false);
        Downloader.newBuilder()
                .setUrl(url)
                .setCacheFilePath(path)
                .setRetryTimes(3)
                .setShouldDownloadImmediate(true)
                .download(new SimpleDownloadCallback() {
                    @Override
                    public void onSuccess(String url, String path) {
                        super.onSuccess(url, path);
                        res.set(true);
                        latch.countDown();
                        HLog.d(TAG, HLog.USR, "download mode url success: {}", url);
                    }

                    @Override
                    public void onFail(String msg) {
                        super.onFail(msg);
                        res.set(false);
                        latch.countDown();
                        HLog.d(TAG, HLog.USR, "download mode url failed: {} {}", url, msg);
                    }
                });
        try {
            latch.await();
        } catch (InterruptedException ignored) {
        }
        return res.get();
    }

    private boolean isCheckMatchImg(GameConfig gameConfig) {
        int gameType = gameConfig.getGameType();
        if (gameType == GameType.GAME_TYPE_SHEEP) {
            return false;
        } else if (gameConfig.generateHome != null) {
            return false;
        } else {
            return true;
        }
    }

    private void unpackGame(int gameType, int version, boolean isWebGame, @NonNull UpdateCallback callback) {
        deleteOldUnpackDir(gameType, isWebGame);
        String gameZipFileName = getDownloadGameName(gameType, isWebGame);
        try (InputStream is = new FileInputStream(gameZipFileName)) {
            MethodResult unpackGameResult = unpackGame(is, gameType, version, isWebGame);
            if (unpackGameResult.isSuccess()) {
                invokeFinishTask(callback);
                // 解压成功之后，删除原压缩包
                FileUtil.safeDeleteFile(gameZipFileName);
            } else {
                invokeUnpackFail(callback);
            }
        } catch (Exception e) {
            HLog.d(TAG, HLog.USR, "unpackGame error! msg={}", e);
            invokeUnpackFail(callback);
        }
    }

    public MethodResult unpackGame(InputStream is, int gameType, int version, boolean isWebGame) {
        HLog.d(TAG, "unpackGame, gameType={}, version={}, isWebGame={}", gameType, version, isWebGame);
        MethodResult result = CocosUnpackUtil.unpack(is, false, CocosConstantsKt.getUnpackDir(gameType, isWebGame));
        if (result.isSuccess()) {
            updateGameVersion(gameType, version, isWebGame);
        }
        return result;
    }

    public void updateGameVersion(int gameType, int version, boolean isWebGame) {
        String region = GlobalConfigManager.getInstance().getRegion();
        if (isWebGame) {
            gameVersionWebMap.put(gameType, new Pair<>(region, version));
        } else {
            gameVersionMap.put(gameType, new Pair<>(region, version));
        }
        storeProperties(gameType, region, version, isWebGame);
    }


    public void deleteAllRes() {
        gameVersionMap.clear();
        gameVersionWebMap.clear();
        MatchResPrefUtil.getInstance().clear();
        File gameFile = FileManager.getFile(FileConfig.getGameFolder());
        FileManager.deleteFileOrDir(gameFile.getAbsolutePath());
    }

    /**暂时只清理了内存中Map里缓存的版本信息，本地文件缓存在该方法暂未处理，
     后续如果不是从“设置-清理缓存”中调用该方法时需要将本地 getPropertyFileName(gameType, isWeb) 文件同时清除*/
    public void deleteGameResVersion(int gameType) {
        gameVersionMap.remove(gameType);
        gameVersionWebMap.remove(gameType);
    }

    private Pair<String, Integer> getGameVersionWithRegion(int gameType, boolean isWebGame) {
        Pair<String, Integer> localVersionWithRegion;
        String currentRegion = GlobalConfigManager.getInstance().getRegion();
        if (isWebGame) {
            localVersionWithRegion = gameVersionWebMap.get(gameType);
        } else {
            localVersionWithRegion = gameVersionMap.get(gameType);
        }
        if (localVersionWithRegion != null && TextUtils.equals(localVersionWithRegion.first, currentRegion)) {
            return localVersionWithRegion;
        }
        localVersionWithRegion = getLocalVersionWithRegion(currentRegion, gameType, isWebGame);
        if (isWebGame) {
            gameVersionWebMap.remove(gameType);
            gameVersionWebMap.put(gameType, localVersionWithRegion);
        } else {
            gameVersionMap.remove(gameType);
            gameVersionMap.put(gameType, localVersionWithRegion);
        }
        return localVersionWithRegion;
    }

    public int getGameVersion(int gameType) {
        return getGameVersion(gameType, isOldCocoUseWeb());
    }

    public int getGameVersion(int gameType, boolean isWebGame) {
        return getGameVersionWithRegion(gameType, isWebGame).second;
    }

    private String getDownloadGameName(int gameType, boolean isWebGame) {
        String zipName;
        if (isWebGame) {
            zipName = FileCacheName.COCOS_WEB_ZIP_RES + gameType + ".zip";
        } else {
            zipName = FileCacheName.COCOS_ZIP_RES + gameType + ".zip";
        }
        try {
            return FileConfig.getCocosGameZipPath(String.valueOf(gameType), zipName);
        } catch (Exception e) {
            e.printStackTrace();
            return FileConfig.getCocosErrorFolder(gameType, zipName);
        }
    }

    public String getDownloadMatchResName(int gameType) {
        String zipName = FileCacheName.COCOS_ZIP_MATCH_RES + gameType + ".zip";
        try {
            return FileConfig.getCocosGameZipPath(String.valueOf(gameType), zipName);
        } catch (Exception e) {
            e.printStackTrace();
            return FileConfig.getCocosErrorFolder(gameType, zipName);
        }
    }

    private String getMatchResDownloadUrl(int gameType) {
        return ConfigHelper.getInstance().getGameMatchResUrlAn(gameType);
    }

    public String getMatchResDir(int gameType) {
        return FileConfig.getCocosGameUnZipPath(String.valueOf(gameType), FileCacheName.COCOS_RES);
    }

    private String getPropertyFileName(int gameType, boolean isWebGame) {
        return CocosConstantsKt.getUnpackDir(gameType, isWebGame) + VERSION_FILE_NAME;
    }

    private void storeProperties(int gameType, String region, int version, boolean isWebGame) {
        Properties properties = new Properties();
        properties.put(VERSION_KEY, region + "_" + version);
        String fileName = getPropertyFileName(gameType, isWebGame);
        if (!FileUtil.fileExists(fileName)) {
            FileUtil.safeCreateFile(fileName);
        }
        try (OutputStream os = new FileOutputStream(fileName)) {
            properties.store(os, new Date().toString());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private Pair<String, Integer> getLocalVersionWithRegion(String currentRegion, int gameType, boolean isWebGame) {
        File file = new File(getPropertyFileName(gameType, isWebGame));
        String region = "";
        int ver = 0;
        if (file.exists()) {
            try {
                String regionWithVersion = getLocalProperty(VERSION_KEY, gameType, isWebGame);
                String[] s = regionWithVersion.split("_");
                if (TextUtils.equals(currentRegion, s[0])) {
                    region = s[0];
                    ver = Integer.parseInt(s[1]);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return new Pair<>(region, ver);
    }

    private String getLocalProperty(String key, int gameType, boolean isWebGame) {
        try (InputStream is = new FileInputStream(getPropertyFileName(gameType, isWebGame))) {
            Properties properties = new Properties();
            properties.load(is);
            return properties.getProperty(key, "");
        } catch (IOException e) {
            HLog.e(TAG, "getVersionFromLocal: {}", e);
            return "";
        }
    }

    private void deleteOldUnpackDir(int gameType, boolean isWebGame) {
        try {
            FileUtil.deleteDirectory(new File(CocosConstantsKt.getUnpackDir(gameType, isWebGame)));
        } catch (IOException e) {
            HLog.d("", "error delete web dir: {}", e);
        }
    }

    private void deleteOldMatchRes(int gameType) {
        try {
            FileUtil.deleteDirectory(new File(getMatchResDir(gameType)));
        } catch (IOException e) {
            HLog.d("", "error delete web dir: {}", e);
        }
    }

    public boolean isLocalMatchAResAvailable(int type) {
        return MatchResPrefUtil.getInstance().containsKey(MatchResPrefUtil.MATCH_TYPE_PREFFIX + type);
    }

    public boolean matchResNeedUpdate(int type, String serverUrl) {
        String localUrl = MatchResPrefUtil.getInstance().getString(MatchResPrefUtil.MATCH_TYPE_PREFFIX + type, "");
        return !localUrl.equals(serverUrl);
    }

    private void invokeDownloadFail(final UpdateCallback callback) {
        ThreadUtil.runOnUiThread(() -> {
            if (callback != null) callback.onDownloadFailed();
        });
    }

    private void invokeUnpackFail(final UpdateCallback callback) {
        ThreadUtil.runOnUiThread(() -> {
            if (callback != null) callback.onUnpackFailed();
        });
    }

    private void invokeFinishTask(final UpdateCallback callback) {
        ThreadUtil.runOnUiThread(() -> {
            if (callback != null) callback.onSuccess();
        });
    }

    public static CocosVersionManager getInstance() {
        return CocosVersionManager.Holder.INSTANCE;
    }

    public static void clear() {
        Holder.INSTANCE = new CocosVersionManager();
    }

    private static class Holder {
        static CocosVersionManager INSTANCE = new CocosVersionManager();
    }
}
