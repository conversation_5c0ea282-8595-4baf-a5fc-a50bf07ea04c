package com.wepie.wespy.cocosnew.callback;

import androidx.annotation.Nullable;

import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeOwner;

/**
 * date 2020/8/10
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class LifeUpdateCallback implements UpdateCallback, ILifeOwner {
    private final ILife mLife;

    public LifeUpdateCallback() {
        this(null);
    }

    public LifeUpdateCallback(ILife life) {
        this.mLife = life;
    }

    @Override
    public void onDownloadUpdate(int percent) {

    }

    @Override
    public void onDownloadFailed() {

    }

    @Override
    public void onSuccess() {

    }

    @Override
    public void onUnpackFailed() {

    }

    @Nullable
    @Override
    public ILife getLife() {
        return mLife;
    }
}
