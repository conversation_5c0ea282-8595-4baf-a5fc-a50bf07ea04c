package com.wepie.wespy.cocosnew.suspend;

import com.wejoy.littlegame.LittleGame;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.model.event.match.JoinTeamVoiceEvent;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wespy.component.suspend.SuspendManager;

/**
 * Created by bigwen on 2018/1/24.
 */

public class SuspendUtil {

    //小游戏返回处理
    public static void cocosGameBack() {
        TeamInfo teamInfo = LittleGame.getTeamInfo();
        if (teamInfo.tid <= 0) return;
        //如果在挂起状态，通知恢复语音
        if (SuspendManager.INSTANCE.isSuspend()) {
            EventDispatcher.postJoinTeamVoice(teamInfo, true, JoinTeamVoiceEvent.SRC_COME_BACK);
        }
    }
}
