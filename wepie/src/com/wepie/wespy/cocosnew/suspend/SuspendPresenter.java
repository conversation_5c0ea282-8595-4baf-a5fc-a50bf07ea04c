package com.wepie.wespy.cocosnew.suspend;

import com.huiwan.voiceservice.VoiceManager;
import com.wejoy.gamematch.net.GameMatchSender;
import com.wejoy.littlegame.LittleGame;
import com.wepie.wespy.cocosnew.match.LittleGameStateCheckHelper;
import com.wepie.wespy.cocosnew.match.TeamVoiceManager;
import com.wepie.wespy.model.entity.iceball.IceGameBeforeStart;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.model.event.iceball.IceBeforeStartEvent;
import com.wepie.wespy.model.event.iceball.IceCancelMatchEvent;
import com.wepie.wespy.model.event.iceball.IceStartMatchEvent;
import com.wepie.wespy.model.event.iceball.IceSyncEvent;
import com.wepie.wespy.model.event.match.CancelMatchEvent;
import com.wepie.wespy.model.event.match.JoinTeamVoiceEvent;
import com.wepie.wespy.model.event.match.LeaveTeamEvent;
import com.wepie.wespy.model.event.match.SyncTeamRspEvent;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.main.MainActivity;
import com.wepie.wespy.module.media.sound.SoundUtil;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.dataservice.event.LittleGameSuspendEvent;
import com.wespy.component.suspend.SuspendManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Created by bigwen on 2018/1/23.
 */

public class SuspendPresenter {

    private final MainActivity activity;
    private final LittleGameStateCheckHelper gameStateCheckHelper = LittleGameStateCheckHelper.INSTANCE;

    public SuspendPresenter(MainActivity activity) {
        this.activity = activity;
        EventBus.getDefault().register(this);
    }

    public void clear() {
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        gameStateCheckHelper.stopCheck();
    }

    /**
     * @param event 同步事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleSync(IceSyncEvent event) {
        GameMatchSender.syncTeamReq(null);
        EventDispatcher.postUpdateSuspendFloat();
    }

    /**
     * @param event 同步事件 Response
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleSyncTeamResponse(SyncTeamRspEvent event) {
        if (event.teamInfo.tid == LittleGame.getTeamInfo().tid
                && !event.teamInfo.selfIsInTeam()) {//sync 发现自己不在队伍里
            EventDispatcher.postLeaveTeam(false);
            return;
        }
        LittleGame.updateTeamInfo(event.teamInfo);
        EventDispatcher.postUpdateSuspendFloat();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleEnterSuspend(LittleGameSuspendEvent event) {
        if (event.isEnter()) {
            gameStateCheckHelper.setSuccessCallback(activity, () -> {
                if (!SuspendManager.INSTANCE.isSuspend()) {
                    return;
                }
                SoundUtil.getInstance().playSound(SoundUtil.TYPE_MATCH_SUCCESS);
                SuspendManager.INSTANCE.closeSuspend();
                cancelMatchAndClose();
                EventDispatcher.postUpdateSuspendFloat();
            });
            gameStateCheckHelper.checkGameState();
        } else {
            gameStateCheckHelper.stopCheck();
        }
    }

    /**
     * 推送，匹配成功，开始游戏
     *
     * @param event 进入游戏界面事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void handleBeforeStartGame(final IceBeforeStartEvent event) {
        if (!SuspendManager.INSTANCE.isSuspend()) {
            return;
        }
        SoundUtil.getInstance().playSound(SoundUtil.TYPE_MATCH_SUCCESS);
        SuspendManager.INSTANCE.closeSuspend();
        cancelMatchAndClose();
        IceGameBeforeStart iceGameBeforeStart = event.beforeStartInfo;
        JumpUtil.gotoLittleGameActivity(activity, false, 0, iceGameBeforeStart.roomInfo, iceGameBeforeStart.beforeStartData, false);
        EventDispatcher.postUpdateSuspendFloat();
    }

    /**
     * 推送，开始匹配
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void pushStartMatch(IceStartMatchEvent startMatchEvent) {
        if (!SuspendManager.INSTANCE.isSuspend()) {
            return;
        }
        LittleGame.setTeamMatchExpectedTime(startMatchEvent.exceptTime);
        LittleGame.touchMatchSuspend(System.currentTimeMillis());
        EventDispatcher.postUpdateSuspendFloat();
    }

    /**
     * 推送，取消匹配
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void pushCancelMatch(IceCancelMatchEvent event) {
        SuspendManager.INSTANCE.closeSuspend();
        cancelMatchAndClose();
        EventDispatcher.postUpdateSuspendFloat();
    }

    /**
     * 自己取消匹配
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onCancelMatch(CancelMatchEvent cancelMatchEvent) {
        SuspendManager.INSTANCE.closeSuspend();
        cancelMatchAndClose();
        EventDispatcher.postUpdateSuspendFloat();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onLeaveTeam(LeaveTeamEvent leaveTeamEvent) {
        SuspendManager.INSTANCE.closeSuspend();
        VoiceManager.getInstance().leaveChannel();
        EventDispatcher.postUpdateSuspendFloat();
        gameStateCheckHelper.stopCheck();
        LittleGame.clear();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onJoinTeamVoice(JoinTeamVoiceEvent joinTeamVoiceEvent) {
        TeamInfo teamInfo = joinTeamVoiceEvent.teamInfo;
        if (!teamInfo.hasVoice()) return;
        if (teamInfo.getTid() <= 0) return;
        TeamVoiceManager.getInstance().checkOpenAgora();
        EventDispatcher.postUpdateSuspendFloat();
    }

    //部分模式取消匹配后 队伍需要销毁
    private void cancelMatchAndClose() {
        if (!LittleGame.getTeamInfo().isMorePlayerMatch()
                && !LittleGame.getTeamInfo().isCreateRoom()) {
            EventDispatcher.postLeaveTeam(true);
        }
        gameStateCheckHelper.stopCheck();
    }
}
