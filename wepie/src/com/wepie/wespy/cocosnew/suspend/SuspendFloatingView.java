package com.wepie.wespy.cocosnew.suspend;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.view.animation.RotateAnimation;
import android.widget.ImageView;
import android.widget.TextView;

import com.huiwan.base.interfaces.IAction;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.base.util.TouchEffectUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.ICompetitionApi;
import com.huiwan.sdk.floating.FloatingBaseView;
import com.wejoy.littlegame.LittleGame;
import com.welib.alinetlog.AliNetLogUtil;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.helper.wfloating.FloatingCloseEvent;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wespy.component.suspend.SuspendInfo;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import kotlin.jvm.functions.Function1;

/**
 * Created by three on 2017/9/15.
 */
public class SuspendFloatingView extends FloatingBaseView {
    private final Context mContext;
    private ImageView headImage;
    private TextView nameTx;
    private ImageView exitImage;
    private RotateAnimation rotateAnimation;

    private final Function1<Context, Boolean> competitionCallback = new Function1<>() {
        @Override
        public Boolean invoke(Context context) {
            exitDirect();
            return true;
        }
    };

    public SuspendFloatingView(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.suspend_floating_view, this);
        headImage = findViewById(R.id.room_floating_head_image);
        nameTx = findViewById(R.id.room_floating_name_tx);
        exitImage = findViewById(R.id.room_floating_exit_bt);

        rotateAnimation = new RotateAnimation(0, 360, Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f);
        rotateAnimation.setDuration(8 * 1000);
        rotateAnimation.setInterpolator(new LinearInterpolator());
        rotateAnimation.setRepeatCount(Animation.INFINITE);
    }

    public void update(final SuspendInfo suspendInfo) {
        setVisibility(View.VISIBLE);

        int gameType = suspendInfo.getGameType();
        String name;
        if (suspendInfo.getSuspendType() == SuspendInfo.TYPE_MATCHING) {
            name = ResUtil.getStr(R.string.suspend_floating_view_matching);
            startAnim();
        } else {
            stopAnim();
            TeamInfo teamInfo = LittleGame.getTeamInfo();
            int totalNum = teamInfo.getSeats().size();
            int sitNum = teamInfo.getSitNum();
            name = ResUtil.getResource().getString(R.string.suspend_floating_view_group_d_d, sitNum, totalNum);
        }
        nameTx.setText(name);

        String url = ConfigHelper.getInstance().getGameConfig().getGameIcon(gameType);
        if (TextUtil.isEmpty(url)) {
            HLog.aliLog(AliNetLogUtil.PORT.HttpConfig, AliNetLogUtil.TYPE.err, "err suspendInfo:" + suspendInfo);
        } else {
            HeadImageLoader.loadHeadImage(url, 44, headImage);
        }

        setOnClickListener(view -> {
            SuspendInfo suspendInfo1 = com.wespy.component.suspend.SuspendManager.INSTANCE.getSuspendInfo();
            if (suspendInfo1 != null) {
                IAction<Context> action = suspendInfo1.getAction();
                if (action != null) {
                    action.onAction(view.getContext());
                }
            }
        });

        TouchEffectUtil.addTouchEffect(exitImage);
        exitImage.setOnClickListener(view -> showExitSureDialog());
    }

    private void showExitSureDialog() {
        SuspendInfo suspendInfo = com.wespy.component.suspend.SuspendManager.INSTANCE.getSuspendInfo();
        if (suspendInfo != null) {
            IAction<Context> action = suspendInfo.getOnExit();
            if (action != null) {
                action.onAction(mContext);
            }
        }
    }

    private void exitDirect() {
        SuspendInfo suspendInfo = com.wespy.component.suspend.SuspendManager.INSTANCE.getSuspendInfo();
        if (suspendInfo == null) {
            return;
        }
        IAction<Object> action = suspendInfo.getExitDirect();
        if (action != null) {
            action.onAction("");
        }

    }

    private void startAnim() {
        rotateAnimation.cancel();
        headImage.clearAnimation();
        headImage.setAnimation(rotateAnimation);
        rotateAnimation.start();
    }

    private void stopAnim() {
        headImage.clearAnimation();
        rotateAnimation.cancel();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        EventBus.getDefault().register(this);
        ApiService.of(ICompetitionApi.class).registerEnterCompetitionCallback(competitionCallback);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        stopAnim();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        ApiService.of(ICompetitionApi.class).unregisterEnterCompetitionCallback(competitionCallback);
    }

    @Override
    protected void onWindowVisibilityChanged(int visibility) {
        super.onWindowVisibilityChanged(visibility);
        if (visibility == View.VISIBLE) {
            ApiService.of(ICompetitionApi.class).registerEnterCompetitionCallback(competitionCallback);
        } else {
            ApiService.of(ICompetitionApi.class).unregisterEnterCompetitionCallback(competitionCallback);
        }
    }

    @Override
    public int getViewHeight() {
        return ScreenUtil.dip2px(62);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void closeSuspend(FloatingCloseEvent event) {
        if (event.getAndSet(true)) {
            return;
        }
        SuspendInfo suspendInfo = com.wespy.component.suspend.SuspendManager.INSTANCE.getSuspendInfo();
        if (suspendInfo != null) {
            IAction<Object> action = suspendInfo.getExitDirect();
            if (action != null) {
                action.onAction("");
            }
        }
    }
}
