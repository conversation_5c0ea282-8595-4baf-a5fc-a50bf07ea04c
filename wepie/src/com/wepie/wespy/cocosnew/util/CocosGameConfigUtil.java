package com.wepie.wespy.cocosnew.util;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import com.huiwan.base.util.ColorUtil;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.PressUtil;
import com.huiwan.base.util.ViewUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig;
import com.huiwan.store.file.FileCacheName;
import com.huiwan.store.file.FileConfig;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoadListener;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.libimageloader.WpViewBgLoadListener;
import com.wepie.liblog.main.HLog;

import java.io.File;

/**
 * Created by bigwen on 2017/12/12.
 */

public class CocosGameConfigUtil {

    private static final String TAG = "CocosGameConfigUtil";

    public static final String CREATE_ROOM = "create_room";
    public static final String GAME_INVITE = "game_invite";
    public static final String GAME_INVITE_DISABLE = "game_invite_disable";
    public static final String GAME_READY = "game_ready";
    public static final String GAME_START = "game_start";
    public static final String GAME_UNREADY = "game_unready";
    public static final String GAME_SIT_DOWN = "game_sit_down";

    public static final String GAME_EXIT = "game_exit";

    public static final String GAME_RULE = "game_rule";
    public static final String GUIDE = "guide";
    public static final String GUIDE_STARE = "guide_start";
    public static final String LEADER_BOARD = "leaderboard";
    public static final String MATCH_BACKGROUND = "match_background";
    public static final String MATCH_EMPTY = "match_empty";
    public static final String MATCH_FRONT = "match_front";
    public static final String RANK_BACKGROUND = "rank_background";
    public static final String RANK_FRONT = "rank_front";
    public static final String RANK_ICON = "rank_icon";
    public static final String ROOM_BACKGROUND = "room_background";
    public static final String ROOM_VIP_BACKGROUND = "vip_room_background";
    public static final String ROOM_EMPTY = "room_empty";
    public static final String ROOM_FRONT = "room_front";
    public static final String START = "start";
    public static final String START_BACKGROUND = "start_background";
    public static final String START_FRONT = "start_front";
    public static final String INVITE_SOCIAL_FRIEND_BTN = "game_share";
    public static final String MODE = "mode";

    public static GameConfig getCocosGameConfig(int gameType) {
        return ConfigHelper.getInstance().getGameConfig(gameType);
    }

    public static void loadResBgAsync(View view, final int gameType, final String resName) {
        String path = getResPath(gameType, resName);
        WpImageLoader.load(path, null, getResImageLoadInfo(gameType, resName).owner(view).setNoneCache(false), new WpViewBgLoadListener(view));
    }

    public static void loadResBgAsyncWithRecycle(View view, final int gameType, final String resName) {
        String path = getResPath(gameType, resName);
        if (!FileUtil.fileExists(path)) {
            return;
        }
        final WpViewBgLoadListener listener = new WpViewBgLoadListener(view);
        final ImageLoadInfo loadInfo = getResImageLoadInfo(gameType, resName).owner(view).setNoneCache(false);
        WpImageLoader.load(path, null, loadInfo, listener);
        ViewUtil.setVisibilityChangedListener(view, (changedView, visibility) -> {
            if (visibility == View.VISIBLE) {
                WpImageLoader.load(path, null, loadInfo, listener);
            } else {
                view.setBackgroundColor(ColorUtil.getColor("#2C5A46"));
            }
        });
    }

    public static void loadResAsync(final ImageView imageView, final int gameType, final String resName) {
        loadResAsync(imageView, gameType, resName, false);
    }

    public static void loadResAsync(final ImageView imageView, final int gameType, final String resName, Drawable defaultDrawable) {
        String path = getResPath(gameType, resName);
        if (!FileUtil.fileExists(path)) {
            imageView.setImageDrawable(defaultDrawable);
            return;
        }
        WpImageLoader.load(path, imageView, getResImageLoadInfo(gameType, resName).placeholder(defaultDrawable).error(defaultDrawable));
    }

    public static void loadResAsync(final ImageView imageView, final int gameType, final String resName, boolean isPress) {
        if (isPress) {
            PressUtil.addPressEffect(imageView);
        }
        String path = getResPath(gameType, resName);
        WpImageLoader.load(path, imageView, getResImageLoadInfo(gameType, resName));
    }

    public static void load(View view, int gameType, String resName, WpImageLoadListener<String> listener) {
        String path = getResPath(gameType, resName);
        WpImageLoader.load(path, null, getResImageLoadInfo(gameType, resName).owner(view), listener);
    }

    public static Bitmap[] getGuideList(int gameType) {
        int num = getCocosGameConfig(gameType).getGuideImageCount();
        if (num <= 0) {
            return new Bitmap[]{};
        }
        Bitmap[] bitmaps = new Bitmap[num];
        for (int i = 0; i < bitmaps.length; i++) {
            bitmaps[i] = getAssetsRes(gameType, GUIDE + "_" + (i + 1));
        }
        return bitmaps;
    }

    public static Bitmap getAssetsRes(int gameType, String resName) {
        String fileName = getResPath(gameType, resName);
        Bitmap bitmap = BitmapFactory.decodeFile(fileName);
        HLog.d(TAG, HLog.USR, "getAssetsRes, gameType=" + gameType + ", resName=" + resName + ",fileName=" + fileName + ", is null=" + (null == bitmap));
        return bitmap;
    }

    public static boolean isResFileExist(int gameType, String resName) {
        return FileUtil.fileExists(getResPath(gameType, resName));
    }

    private static String tryGetUrlResPath(int gameType, String resName) {
        VsCenterLittleGameConfig config = ConfigHelper.getInstance().getGameConfig(gameType).generateHome;
        String urlPath = "";
        if (config != null) {
            VsCenterLittleGameConfig.BtnUrls btnUrls = config.metaData.btnUrls;
            switch (resName) {
                case GAME_READY:
                    urlPath = btnUrls.ready;
                    break;
                case GAME_UNREADY:
                    urlPath = btnUrls.unready;
                    break;
                case GAME_INVITE:
                    urlPath = btnUrls.invite;
                    break;
                case GAME_INVITE_DISABLE:
                    urlPath = btnUrls.inviteDisable;
                    break;
                case GAME_START:
                    urlPath = btnUrls.start;
                    break;
                case GUIDE_STARE:
                    urlPath = btnUrls.guideStart;
                    break;
                default:
            }
        }
        return urlPath;
    }

    private static String getResPath(int gameType, String resName) {
        String path = tryGetUrlResPath(gameType, resName);
        if (TextUtils.isEmpty(path)) {
            return getLocalPicPath(gameType, resName);
        } else {
            return path;
        }
    }

    private static String getLocalPicPath(int gameType, String resName) {
        String filePrefix = FileConfig.getCocosGameUnZipPath(String.valueOf(gameType), FileCacheName.COCOS_RES) + resName;
        String webpPath = getWebpPath(filePrefix);
        if (webpPath != null) {
            return webpPath;
        }
        return filePrefix + ".png";
    }

    private static String getWebpPath(String prefix) {
        File picFile = new File(prefix + ".webp");
        if (picFile.exists()) {
            return picFile.getAbsolutePath();
        }
        return null;
    }

    public static ImageLoadInfo getResImageLoadInfo(int gameType, String resName) {
        return ImageLoadInfo.newInfo().setNoneCache(true);
    }
}