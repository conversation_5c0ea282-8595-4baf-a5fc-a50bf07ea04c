package com.wepie.wespy.cocosnew.util;

import android.content.Context;

import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.configservice.editionentity.VsCenterLittleGameConfig;
import com.huiwan.store.PrefUtil;
import com.wejoy.littlegame.LittleGameSimpleInfo;
import com.wepie.wespy.cocosnew.view.dialog.IceBallDialogUtil;

import java.util.List;

// 小游戏新手引导
public class LittleGameGuideDialogUtils {

    // 小游戏新手引导逻辑收拢
    public static boolean showMatchGuide(Context context, LittleGameSimpleInfo simpleInfo, Runnable callback) {
        List<GameConfig.MatchGroupInfo> matchGroupInfoList = ConfigHelper.getInstance().getGameConfig().getMatchGroup(simpleInfo.getGameType());
        if (!matchGroupInfoList.isEmpty()) {
            return showCatDialog(context, simpleInfo, matchGroupInfoList, callback);
        } else {
            return showCommonGuideDialog(context, simpleInfo, callback);
        }
    }

    public static boolean showCatDialog(Context context, LittleGameSimpleInfo simpleInfo, List<GameConfig.MatchGroupInfo> matchGroupInfoList, Runnable callback) {
        int gameType = simpleInfo.getGameType();
        int betLevel = simpleInfo.getBetLevel();
        int mode = simpleInfo.getMode();
        for (int groupIndex = 0; groupIndex < matchGroupInfoList.size(); groupIndex++) {
            GameConfig.MatchGroupInfo matchGroupInfo = matchGroupInfoList.get(groupIndex);
            for (GameConfig.MatchInfo info : matchGroupInfo.getMatchInfoList()) {
                if (info.getBetLevel() == betLevel && (info.getRoomMode() == mode || info.getMatchMode() == mode)) {
                    boolean isFirstEnter = PrefUtil.getInstance().getBoolean(getFirstTimeEnterKey(groupIndex, gameType), true);
                    if (isFirstEnter) {
                        PrefUtil.getInstance().setBoolean(getFirstTimeEnterKey(groupIndex, gameType), false);
                        IceBallDialogUtil.showGuideUrlDialog(context, simpleInfo, matchGroupInfo.getGuideUrlList(), true, () -> {
                            CommonUtil.trackQuickStart(gameType, mode);
                            if (callback != null) {
                                callback.run();
                            }
                        });
                        CommonUtil.trackAutoGuide(gameType, betLevel);
                        return true;
                    }
                    break;
                }
            }
        }
        return false;
    }

    public static boolean showCommonGuideDialog(Context context, LittleGameSimpleInfo simpleInfo, Runnable callback) {
        int gameType = simpleInfo.getGameType();
        boolean isFirstEnter = PrefUtil.getInstance().getBoolean(getFirstTimeEnterKey(-1, gameType), true);
        if (isFirstEnter) {
            PrefUtil.getInstance().setBoolean(getFirstTimeEnterKey(-1, gameType), false);
            int mode = simpleInfo.getMode();
            IceBallDialogUtil.showGuideDialog(context, simpleInfo, () -> {
                CommonUtil.trackQuickStart(gameType, mode);
                if (callback != null) {
                    callback.run();
                }
            });
            CommonUtil.trackAutoGuide(gameType, mode);
            return true;
        } else {
            return false;
        }
    }

    public static boolean showGuideDialogByGameMode(Context context, LittleGameSimpleInfo simpleInfo, Runnable callback) {
        int gameType = simpleInfo.getGameType();
        GameConfig config = ConfigHelper.getInstance().getGameConfig(gameType);
        VsCenterLittleGameConfig home = config.generateHome;
        if (home == null || home.metaData == null) {
            return false;
        }
        int gameMode = simpleInfo.getGameMode();
        boolean isFirstEnter = PrefUtil.getInstance().getBoolean(getFirstTimeEnterKeyByMode(gameType, gameMode), true);
        if (isFirstEnter) {
            PrefUtil.getInstance().setBoolean(getFirstTimeEnterKeyByMode(gameType, gameMode), false);
            List<String> urlList = null;
            for (VsCenterLittleGameConfig.HelpUrl helpUrl : home.metaData.helpUrlList) {
                if (helpUrl.gameMode == gameMode && !helpUrl.hide) {
                    urlList = helpUrl.urls;
                }
            }
            if (urlList == null || urlList.isEmpty()) {
                return false;
            }

            IceBallDialogUtil.showGuideUrlDialog(context, simpleInfo, urlList, () -> {
                CommonUtil.trackQuickStart(gameType, gameMode);
                if (callback != null) {
                    callback.run();
                }
            });
            CommonUtil.trackAutoGuide(gameType, gameMode);
            return true;
        } else {
            return false;
        }
    }

    public static String getFirstTimeEnterKey(int groupId, int gameType) {
        if (groupId >= 0) {
            return PrefUtil.KEY_ICE_BALL_FIRST_TIME + gameType + "_" + groupId;
        } else {
            return PrefUtil.KEY_ICE_BALL_FIRST_TIME + gameType;//兼容老的key
        }
    }

    public static String getFirstTimeEnterKeyByMode(int gameType, int gameMode) {
        return PrefUtil.KEY_LITTLE_GAME_MODE_FIRST_TIME + gameType + "_" + gameMode;
    }
}
