package com.wepie.wespy.cocosnew.util;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.component.gift.send.MulGiftInfo;
import com.huiwan.constants.IpcConstants;
import com.huiwan.littlegame.EventDispatcher;
import com.huiwan.littlegame.model.CocosGiftInfo;
import com.huiwan.littlegame.model.CocosRoomMsg;
import com.wejoy.littlegame.LittleGame;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceGiftInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;

public class LittleGameMsgUtil {

    public static void dispatchNormalToSubProcess(VoiceRoomMsg msg) {
        dispatchMsg(msg, IpcConstants.Notify.COCOS_LITTLE_GAME_MSG_NORMAL);
    }

    public static void dispatchGiftToSubProcess(VoiceRoomMsg msg) {
        dispatchMsg(msg, IpcConstants.Notify.COCOS_LITTLE_GAME_MSG_GIFT);
    }

    private static void dispatchMsg(VoiceRoomMsg msg, String type) {
        if (LittleGame.getUseConnector() && !LibBaseUtil.getBaseConfig().mainProcess) {
            CocosRoomMsg cocosMsg = new CocosRoomMsg();
            transformMsg(msg, cocosMsg);
            EventDispatcher.postCocosRoomMsg(cocosMsg);
            return;
        }
        int rid = VoiceRoomService.getInstance().getRid();
        if (rid > 0 && msg.getRid() == rid) {
            return;
        }
        CocosRoomMsg cocosMsg = new CocosRoomMsg();
        transformMsg(msg, cocosMsg);
        EventDispatcher.postCocosRoomMsg(cocosMsg);
    }

    private static void transformMsg(VoiceRoomMsg from, CocosRoomMsg to) {
        to.setRid(from.getRid());
        to.setMsgContent(from.getMsgContent());
        if (from.getSubType() == VoiceRoomMsg.SUBTYPE_MUL_GIFT) {
            to.setSubType(CocosRoomMsg.SUBTYPE_SEND_GIFT);
            to.setGiftInfo(transformGiftInfo(from.getMulGiftInfo()));
        } else {
            to.setSubType(from.getSubType());
            to.setGiftInfo(transformGiftInfo(from.getGiftInfo()));
        }

        to.setSendUid(from.getSend_uid());
        to.setMediaType(from.getMediaType());
        to.setSourceType(from.getSourceType());
    }

    private static CocosGiftInfo transformGiftInfo(VoiceGiftInfo roomGiftInfo) {
        if (roomGiftInfo == null) return null;

        CocosGiftInfo giftInfo = new CocosGiftInfo();
        giftInfo.setGiftId(roomGiftInfo.gift_id);
        giftInfo.setGiftNum(roomGiftInfo.gift_num);
        if (roomGiftInfo.extraReturnCoin > 0) {
            String builder = roomGiftInfo.give_away_desc + ResUtil.getStr(R.string.char_comma) +
                    ResUtil.getStr(R.string.gift_return_black_trigger_title) + ResUtil.getStr(R.string.char_comma) +
                    ResUtil.getStr(R.string.gift_return_black_extra_coin_title, roomGiftInfo.extraReturnCoin);
            giftInfo.setGiveAwayDesc(builder);
        } else {
            giftInfo.setGiveAwayDesc(roomGiftInfo.give_away_desc);
        }
        giftInfo.setReceiver(roomGiftInfo.receiver);
        giftInfo.setSender(roomGiftInfo.sender);
        giftInfo.setUidList(roomGiftInfo.uidList);

        return giftInfo;
    }

    private static CocosGiftInfo transformGiftInfo(MulGiftInfo roomGiftInfo) {
        if (roomGiftInfo == null) return null;

        CocosGiftInfo giftInfo = new CocosGiftInfo();
        giftInfo.setGiftId(roomGiftInfo.giftId);
        giftInfo.setGiftNum(roomGiftInfo.giftNum);
        if (roomGiftInfo.totalExtraReturnCoin > 0) {
            String builder = roomGiftInfo.totalDesc + ResUtil.getStr(R.string.char_comma) +
                    ResUtil.getStr(R.string.gift_return_black_trigger_title) + ResUtil.getStr(R.string.char_comma) +
                    ResUtil.getStr(R.string.gift_return_black_extra_coin_title, roomGiftInfo.totalExtraReturnCoin);
            giftInfo.setGiveAwayDesc(builder);
        } else {
            giftInfo.setGiveAwayDesc(roomGiftInfo.totalDesc);
        }
        giftInfo.setReceiver(Integer.MAX_VALUE);
        giftInfo.setSender(roomGiftInfo.sender);
        giftInfo.setUidList(roomGiftInfo.uidList);

        return giftInfo;
    }
}
