package com.wepie.wespy

import android.app.Activity
import com.huiwan.base.ActivityTaskManager
import com.huiwan.littlegame.util.CocosSoundUtil
import com.huiwan.voiceservice.VoiceManager
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.music.VoiceMusicManager
import com.wepie.wespy.module.voiceroom.util.VoiceMuteManager

object VoiceClear {
    private val listener = object : ActivityTaskManager.ActivityTaskListener() {
        override fun onActivityDestroyed(activity: Activity) {
            if (ActivityTaskManager.getInstance().hasActivity()) {
                return
            }
            VoiceRoomService.getInstance().setMuteRoom(0)
            VoiceMusicManager.get().clearAndStop()
            VoiceManager.getInstance().leaveAndRelease()
            CocosSoundUtil.releaseAll()
            VoiceMuteManager.clear()
        }
    }

    @JvmStatic
    fun init() {
        ActivityTaskManager.getInstance().registerActivityTaskListener(listener)
    }
}