package com.wepie.wespy.helper.dialog.coin;

import android.content.Context;
import android.content.Intent;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ReplacementSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewStub;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.user.UserService;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.model.event.RefreshSelfSuccessEvent;
import com.wepie.wespy.module.pay.commonapi.GoodsListRvHelper;
import com.wepie.wespy.module.pay.commonapi.WpPayResult;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.http.api.OrderApi;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * date 2020/6/30
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class PublicCoinPayBottomDialogView extends FrameLayout {
    private View closeBtn;
    private TextView coinTv;
    private TextView luckyNumTv;
    private ImageView firstChargeIv;
    protected CallDismissListener callDismissListener;
    private GoodsListRvHelper helper;

    public PublicCoinPayBottomDialogView(@NonNull Context context) {
        super(context);
        LayoutInflater.from(context).inflate(getLayoutId(), this);
        initView(context);
        initEvent();
        updateData();
    }

    protected int getLayoutId() {
        return R.layout.dialog_coin_not_enough_bottom;
    }

    protected int getSpanCount() {
        return 3;
    }

    protected String firstChargeUrl() {
        return ConfigHelper.getInstance().getConstV3Info().firstChargeBannerTmpRoom;
    }

    private void initView(Context context) {
        closeBtn = findViewById(R.id.close_iv);
        coinTv = findViewById(R.id.coin_tv);
        luckyNumTv = findViewById(R.id.lucky_num_tv);
        firstChargeIv = findViewById(R.id.first_charge_bg_iv);
        helper = new GoodsListRvHelper(findViewById(R.id.goods_list_rv), getSpanCount());
        HashMap<String, Object> trackParam = new HashMap<>();
        trackParam.put("screen_name", TrackScreenName.PAY_DIALOG);
        helper.addTrackData(trackParam);
    }

    private void initEvent() {
        closeBtn.setOnClickListener((v) -> closeDialog());
    }

    private void initParams(int scene) {
        if (scene == 1) {
            luckyNumTv.setVisibility(View.VISIBLE);
            updateLuckyNumberInfo();
        } else {
            luckyNumTv.setVisibility(View.GONE);
            updateFirstCharge();
        }
        helper.setUpdateCallback(new GoodsListRvHelper.UpdateCallback() {
            @Override
            public void onNeedUpdate() {
            }

            @Override
            public void onSuccess(WpPayResult payResult) {
                if (scene == 1) {
                    updateLuckyNumberInfo();
                }
            }

            @Override
            public void onFail() {
            }
        });
    }

    private void updateData() {
        helper.refresh();
        coinTv.setText(String.valueOf(UserService.get().getLoginUser().getCoin()));
    }

    void addTrackExt(Map<String, Object> track) {
        helper.addTrackData(track);
    }

    private void updateFirstCharge() {
        int firstCode = PrefUserUtil.getInstance().getInt(PrefUserUtil.IS_FIRST_CHARGE, -1);
        if (firstCode == 0) {
            firstChargeIv.setVisibility(View.GONE);
            return;
        }
        OrderApi.isUserFirstCharge(new LifeDataCallback<>(this) {
            @Override
            public void onSuccess(Result<Integer> result) {
                int code = result.data;
                String url = firstChargeUrl();
                if (code == 1) {
                    if (TextUtils.isEmpty(url)) {
                        firstChargeIv.setVisibility(View.GONE);
                    } else {
                        ImageLoaderUtil.loadNormalImage(url, firstChargeIv);
                        firstChargeIv.setVisibility(View.VISIBLE);
                    }
                } else {
                    firstChargeIv.setVisibility(View.GONE);
                    PrefUserUtil.getInstance().setInt(PrefUserUtil.IS_FIRST_CHARGE, 0);
                }
            }

            @Override
            public void onFail(int code, String e) {
                ToastUtil.show(e);
            }
        });
    }

    private void updateLuckyNumberInfo() {
        OrderApi.getLuckyNumberInfo(new LifeDataCallback<>(this) {
            @Override
            public void onSuccess(Result<LuckyNumberInfo> result) {
                LuckyNumberInfo info = result.data;
                if (info == null) {
                    return;
                }
                luckyNumTv.setText(String.valueOf(info.getChargeIntegration()));
                ViewStub stub = findViewById(R.id.lucky_number_banner_stub);
                if (stub != null) {
                    stub.inflate();
                }
                TextView tipTv = findViewById(R.id.lucky_num_charge_tip_tv);
                if (tipTv != null) {
                    SpannableStringBuilder ssb = new SpannableStringBuilder();
                    ssb.append(info.getChargeBannerText());
                    int index = ssb.length();
                    ssb.append(ResUtil.getString(R.string.take));
                    Drawable drawable = ResUtil.getDrawable(R.drawable.wejoy_lucky_num_icon);
                    drawable = drawable.mutate();
                    drawable.setBounds(0, 0, ScreenUtil.dip2px(20), ScreenUtil.dip2px(20));
                    Drawable finalDrawable = drawable;
                    ssb.setSpan(new ReplacementSpan() {

                        @Override
                        public int getSize(@NonNull Paint paint, CharSequence text, int start, int end, @Nullable Paint.FontMetricsInt fm) {
                            return ScreenUtil.dip2px(20);
                        }

                        @Override
                        public void draw(@NonNull Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, @NonNull Paint paint) {
                            Drawable b = finalDrawable;
                            Paint.FontMetricsInt fm = paint.getFontMetricsInt();
                            int transY = (y + fm.descent + y + fm.ascent) / 2 - b.getBounds().bottom / 2;//计算y方向的位移
                            canvas.save();
                            canvas.translate(x, transY);//绘制图片位移一段距离
                            b.draw(canvas);
                            canvas.restore();
                        }
                    }, index, ssb.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                    tipTv.setText(ssb);
                }
            }

            @Override
            public void onFail(int code, String msg) {
                luckyNumTv.setText("0");
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRefreshSelfSuccess(RefreshSelfSuccessEvent event) {
        updateData();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        EventDispatcher.registerEventObserver(this);
    }

    @Override
    protected void onDetachedFromWindow() {
        EventDispatcher.unregisterEventObserver(this);
        helper.clear();
        super.onDetachedFromWindow();
    }

    private void closeDialog() {
        if (callDismissListener != null) {
            callDismissListener.onCallDismiss();
        }
    }

    public static void showBottomDialog(Context context) {
        showBottomDialog(context, 0, Collections.emptyMap(), null);
    }

    public static void showBottomDialog(Context context, int scene, Runnable finishTask) {
        showBottomDialog(context, scene, Collections.emptyMap(), finishTask);
    }

    public static void showBottomDialog(Context context, @NonNull Map<String, Object> trackData) {
        showBottomDialog(context, 0, trackData, null);
    }

    public static void showBottomDialog(Context context, int scene, @NonNull Map<String, Object> trackData, Runnable finishTask) {
        if (LibBaseUtil.getBaseConfig().mainProcess) {
            BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
            PublicCoinPayBottomDialogView dialogView = new PublicCoinPayBottomDialogView(context);
            dialogView.initParams(scene);
            if (!trackData.isEmpty()) {
                dialogView.addTrackExt(trackData);
            }
            dialog.setContentView(dialogView);
            dialogView.callDismissListener = dialog::dismiss;
            dialog.setOnDismissListener(dialog1 -> {
                if (finishTask != null) {
                    finishTask.run();
                }
            });
            dialog.initBottomDialog();
            dialog.show();
        } else {
            context.startActivity(new Intent(context, PublicCoinPayBottomDialogActivity.class));
        }
        if (!trackData.isEmpty()) {
            // 向上发散的场景太多了，一个一个加不过来，直接在这加一个sub_screen_name
            trackData.put("sub_screen_name", TrackScreenName.DIAMOND);
            TrackUtil.appViewScreen(TrackScreenName.PAY_DIALOG, trackData);
        }
    }

    interface CallDismissListener {
        void onCallDismiss();
    }
}
