package com.wepie.wespy.module.family.main;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.widget.FrameLayout;

import androidx.annotation.Nullable;

import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.IntentConfig;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.family.FamilyMainInfo;
import com.wepie.wespy.module.family.main.mine.MyFamilyView;
import com.wepie.wespy.module.family.main.recommend.FamilyRecommendView;

/**
 * Created by bigwen on 2019-10-14.
 */
public class FamilyMainActivity extends BaseActivity {

    private FrameLayout contentLay;
    private FamilyMainPresenter presenter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setBackgroundDrawableResource(R.color.white);
        getWindow().setNavigationBarColor(Color.TRANSPARENT);
        setContentView(R.layout.activity_family_main);
        contentLay = findViewById(R.id.content_lay);
        presenter = new FamilyMainPresenter(this);
        presenter.register();
        presenter.init(getDefaultIndex());
    }

    public void showNotJoin() {
        if (contentLay.getChildCount() != 0 && contentLay.getChildAt(0) instanceof FamilyRecommendView) {
            return;
        }
        contentLay.removeAllViews();
        FamilyRecommendView notJoinView = new FamilyRecommendView(this);
        notJoinView.updateFamilyBoonTipsVisibleState(isFromDiscoverTabScene());
        contentLay.addView(notJoinView);
    }

    void showMyFamily(FamilyMainInfo mainInfo, int defaultIndex) {
        if (contentLay.getChildCount() != 0 && contentLay.getChildAt(0) instanceof MyFamilyView) {
            //do nothing
        } else {
            contentLay.removeAllViews();
            contentLay.addView(new MyFamilyView(this));
        }
        MyFamilyView myFamilyView = (MyFamilyView) contentLay.getChildAt(0);
        myFamilyView.update(mainInfo);
        if (defaultIndex >= 0) {
            myFamilyView.refreshTab(defaultIndex);
        }
    }

    @Override
    protected void clearMemory() {
        super.clearMemory();
        presenter.unregister();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (contentLay.getChildCount() != 0 && contentLay.getChildAt(0) instanceof MyFamilyView) {
            int defaultIndex = intent.getIntExtra(IntentConfig.TAB_INDEX, 0);
            MyFamilyView myFamilyView = (MyFamilyView) contentLay.getChildAt(0);
            if (defaultIndex >= 0) {
                myFamilyView.refreshTab(defaultIndex);
            }
        }
        if (presenter != null) presenter.refresh(-1);
    }

    void finishActivity() {
        finish();
    }

    public int getDefaultIndex() {
        return getIntent().getIntExtra(IntentConfig.TAB_INDEX, -1);
    }

    private boolean isFromDiscoverTabScene() {
        return getIntent().getBooleanExtra(IntentConfig.IS_FROM_DISCOVER_SCENE, false);
    }
}
