package com.wepie.wespy.module.main.activity.events

import android.graphics.Rect
import android.os.Bundle
import android.view.View
import androidx.core.view.WindowInsetsCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.util.DrawableRecycleUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.StatusBarUtil
import com.huiwan.base.util.WindowInsetsUtil
import com.huiwan.component.activity.BaseActivity
import com.huiwan.widget.actionbar.BaseWpActionBar
import com.huiwan.widget.decoration.SpaceItemDecoration
import com.wepie.wespy.R
import com.wepie.wespy.module.activity.ActivityDialogUtil

/**
 ** author: <EMAIL>
 ** created on: 2023/6/5 14:41
 ** description:活动页Activity
 */
class EventsActivity : BaseActivity() {

    private val contentRv by lazy { findViewById<RecyclerView>(R.id.rv_content) }
    private val topBar by lazy { findViewById<BaseWpActionBar>(R.id.top_bar) }
    private val eventsAdapter = EventsAdapter()
    private val moduleList = EventsJumpUtil.formEventsModule(ActivityDialogUtil.getCouponInfoList())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setBackgroundDrawableResource(R.color.main_activity_bg)
        setContentView(R.layout.activity_events)
        StatusBarUtil.initStatusBar(this, false)
        initView()
    }

    private fun initView() {
        DrawableRecycleUtil.setRecycleDrawable(
            findViewById(R.id.bg_iv), R.drawable.activity_default_top_bg
        )
        findViewById<View>(R.id.back_iv).setOnClickListener {
            finish()
        }
        WindowInsetsUtil.fitRecyclerView(contentRv)

        contentRv.apply {
            adapter = eventsAdapter
            layoutManager = GridLayoutManager(
                context, EVENTS_COLUMN_NUM, GridLayoutManager.VERTICAL, false
            ).apply {
                spanSizeLookup = object : SpanSizeLookup() {
                    override fun getSpanSize(position: Int): Int {
                        return if (moduleList.getOrNull(position)?.eventsInfo == null) {
                            EVENTS_COLUMN_NUM
                        } else {
                            1
                        }
                    }
                }
            }
            addItemDecoration(
                SpaceItemDecoration(
                    ScreenUtil.dip2px(ITEM_TOP_MARGIN_DP),
                    ScreenUtil.dip2px(ITEM_END_MARGIN_DP),
                    ScreenUtil.dip2px(ITEM_BOT_MARGIN_DP),
                    ScreenUtil.dip2px(ITEM_START_MARGIN_DP),
                )
            )
        }
        eventsAdapter.submitList(moduleList)
    }

    companion object {
        const val EVENTS_COLUMN_NUM = 2
        const val ITEM_TOP_MARGIN_DP = 0f
        const val ITEM_BOT_MARGIN_DP = 0f
        const val ITEM_START_MARGIN_DP = 4f
        const val ITEM_END_MARGIN_DP = 4f
    }

}