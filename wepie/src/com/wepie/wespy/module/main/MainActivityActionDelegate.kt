package com.wepie.wespy.module.main

import androidx.lifecycle.lifecycleScope
import com.wejoy.jackaroo.level.JackarooLevelUpDialogFragment
import com.wejoy.jackaroo.qualifying.dialog.JKQFGradeUpDialog
import com.wepie.wespy.module.main.manage.MainDialogManage
import kotlinx.coroutines.launch

class MainActivityActionDelegate(
    private val activity: MainActivity,
    private val vm: MainViewModel
) {
    init {
        activity.lifecycleScope.launch {
            vm.action.collect {
                handleAction(it)
            }
        }
    }

    private fun handleAction(action: MainActivityAction) {
        when (action) {
            is MainActivityAction.LevelUp -> showLevelUp(action)
            is MainActivityAction.ShowVoiceRoomRecommend -> {
                activity.showVoiceRoomRecommendDialog(action)
            }
            is MainActivityAction.QualifyingGradeUp -> {
                showQualifyingGradeUp(action)
            }
        }
    }

    private fun showLevelUp(action: MainActivityAction.LevelUp) {
        JackarooLevelUpDialogFragment.show(activity, action.levelInfo) {
            MainDialogManage.getInstance().callback.showNext()
        }
    }

    private fun showQualifyingGradeUp(action: MainActivityAction.QualifyingGradeUp) {
        JKQFGradeUpDialog.show(activity, action.checkoutInfo) {
            MainDialogManage.getInstance().callback.showNext()
        }
    }
}