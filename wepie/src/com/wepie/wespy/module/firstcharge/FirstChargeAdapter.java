package com.wepie.wespy.module.firstcharge;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.common.util.CollectionUtils;
import com.huiwan.base.util.TimeUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.ChargePackageInfo;
import com.wepie.wespy.module.firstcharge.configurable.FirstChargeConfigurableItemView;
import com.wepie.wespy.module.firstcharge.newuser.CloseCallback;
import com.wepie.wespy.module.firstcharge.newuser.FirstChargeBaseItemView;

import java.util.ArrayList;
import java.util.List;

public class FirstChargeAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private final List<ChargePackageInfo> packageInfoList = new ArrayList<>();
    private String gameTestGroup = "";
    private final Context context;
    private FirstChargeCallback callback;
    private static final int FESTIVAL_TYPE = 0;
    private static final int BARRAGE_SMALL_TYPE = 1;
    private static final int BARRAGE_TYPE = 2;
    private static final int NO_BARRAGE_SMALL_TYPE = 3;
    private static final int NO_BARRAGE_TYPE = 4;
    private static final int CARE_TYPE = 5;

    public FirstChargeAdapter(Context context) {
        this.context = context;
    }

    public void setCallback(FirstChargeCallback callback) {
        this.callback = callback;
    }

    @SuppressLint("NotifyDataSetChanged")
    public void refresh(List<ChargePackageInfo> list, String gameTestGroup) {
        this.packageInfoList.clear();
        long current = TimeUtil.getServerTime();
        for (ChargePackageInfo info : list) {
            if (info.deadlineInSec * 1000L - current < 1000L) {
                continue;
            }
            this.packageInfoList.add(info);
        }
        this.gameTestGroup = gameTestGroup;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == CARE_TYPE) {
            return new FirstChargeNewUserViewHolder(new FirstChargeBaseItemView(context, R.layout.first_charge_care_item_view, false));
        } else if (viewType == BARRAGE_TYPE) {
            return new FirstChargeNewUserViewHolder(new FirstChargeBaseItemView(context, R.layout.first_charge_barrage_item_view, true));
        } else if (viewType == BARRAGE_SMALL_TYPE) {
            return new FirstChargeNewUserViewHolder(new FirstChargeBaseItemView(context, R.layout.first_charge_barrage_small_item_view, true));
        } else if (viewType == NO_BARRAGE_TYPE) {
            return new FirstChargeNewUserViewHolder(new FirstChargeBaseItemView(context, R.layout.first_charge_not_barrage_item_view, false));
        } else if (viewType == NO_BARRAGE_SMALL_TYPE) {
            return new FirstChargeNewUserViewHolder(new FirstChargeBaseItemView(context, R.layout.first_charge_not_barrage_small_item_view, false));
        } else {
            return new FirstChargeFestivalViewHolder(new FirstChargeConfigurableItemView(context));
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder viewHolder, int position) {
        ChargePackageInfo info = packageInfoList.get(position);
        if (viewHolder instanceof FirstChargeFestivalViewHolder) {
            FirstChargeFestivalViewHolder holder = (FirstChargeFestivalViewHolder) viewHolder;
            updateFestivalHolder(info, holder);
        } else {
            FirstChargeNewUserViewHolder holder = (FirstChargeNewUserViewHolder) viewHolder;
            updateNewUserHolder(info, holder);
        }
    }

    @Override
    public int getItemCount() {
        return packageInfoList.size();
    }

    @Override
    public int getItemViewType(int position) {
        ChargePackageInfo packageInfo = packageInfoList.get(position);
        if (packageInfo.packageType > FirstChargeConfig.CARE_TWO) {
            return FESTIVAL_TYPE;
        } else if (packageInfo.packageType == FirstChargeConfig.CARE_ONE || packageInfo.packageType == FirstChargeConfig.CARE_TWO) {
            return CARE_TYPE;
        } else {
            if (gameTestGroup.equals("B") || gameTestGroup.equals("D") || gameTestGroup.equals("E")) {
                if (!CollectionUtils.isEmpty(packageInfo.configData.extraItem)) {
                    return BARRAGE_TYPE;
                } else {
                    return BARRAGE_SMALL_TYPE;
                }
            } else {
                if (!CollectionUtils.isEmpty(packageInfo.configData.extraItem)) {
                    return NO_BARRAGE_TYPE;
                } else {
                    return NO_BARRAGE_SMALL_TYPE;
                }
            }
        }
    }

    @Nullable
    public ChargePackageInfo getCurData(int index) {
        if (index < 0) {
            return null;
        }
        if (packageInfoList.size() <= index) {
            return null;
        }
        return packageInfoList.get(index);
    }

    public void updateFestivalHolder(ChargePackageInfo info, FirstChargeFestivalViewHolder holder) {
        holder.update(info, new CloseCallback() {
            @Override
            public void close() {
                if (packageInfoList.size() > 1) {
                    packageInfoList.remove(info);
                    notifyDataSetChanged();
                } else {
                    if (callback != null) callback.onClose();
                }
            }

            @Override
            public void closeAll() {
                if (callback != null) callback.onClose();
            }
        });
    }

    public void updateNewUserHolder(ChargePackageInfo packageInfo, FirstChargeNewUserViewHolder holder) {
        CloseCallback closeCallback = new CloseCallback() {
            @Override
            public void close() {
                if (packageInfoList.size() > 1) {
                    packageInfoList.remove(packageInfo);
                    notifyDataSetChanged();
                } else {
                    if (callback != null) callback.onClose();
                }
            }

            @Override
            public void closeAll() {
                if (callback != null) callback.onClose();
            }
        };
        holder.update(packageInfo, closeCallback);
    }

    static class FirstChargeNewUserViewHolder extends RecyclerView.ViewHolder {
        public FirstChargeNewUserViewHolder(@NonNull View itemView) {
            super(itemView);
            ViewGroup.LayoutParams lp = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            itemView.setLayoutParams(lp);
        }

        public void update(ChargePackageInfo info, CloseCallback closeCallback) {
            FirstChargeBaseItemView view = (FirstChargeBaseItemView) itemView;
            view.refresh(info);
            view.setCallback(closeCallback);
        }
    }

    static class FirstChargeFestivalViewHolder extends RecyclerView.ViewHolder {

        public FirstChargeFestivalViewHolder(@NonNull View itemView) {
            super(itemView);
            ViewGroup.LayoutParams lp = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            itemView.setLayoutParams(lp);
        }

        public void update(ChargePackageInfo info, CloseCallback closeCallback) {
            FirstChargeConfigurableItemView view = (FirstChargeConfigurableItemView) itemView;
            view.refresh(info);
            view.setCallback(closeCallback);
        }
    }
}
