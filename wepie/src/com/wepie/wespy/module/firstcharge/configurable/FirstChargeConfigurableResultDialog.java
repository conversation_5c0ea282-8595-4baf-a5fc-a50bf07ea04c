package com.wepie.wespy.module.firstcharge.configurable;

import android.annotation.SuppressLint;
import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.model.entity.ChargeConfigInfo;
import com.wepie.wespy.model.entity.ChargePackageInfo;
import com.wepie.wespy.module.firstcharge.FirstChargeCallback;
import com.huiwan.base.util.ScreenUtil;

public class FirstChargeConfigurableResultDialog extends FrameLayout {

    private Context mContext;
    private FirstChargeCallback callback;
    private RelativeLayout allLay;
    private RelativeLayout dialogLay;
    private ImageView enterTv, bgIv;

    public FirstChargeConfigurableResultDialog(@NonNull Context context) {
        super(context);
        mContext = context;
        initView();
    }

    public FirstChargeConfigurableResultDialog(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initView();
        initEvent();
    }

    private void initView() {
        LayoutInflater.from(mContext).inflate(R.layout.first_charge_configurable_result_view, this);
        allLay = findViewById(R.id.result_all_lay);
        dialogLay = findViewById(R.id.result_dialog_lay);
        enterTv = findViewById(R.id.enter_btn);
        bgIv = findViewById(R.id.bg_iv);
    }


    private void initEvent() {
        allLay.setOnClickListener(v -> {
            if (callback != null) callback.onClose();
        });
        dialogLay.setOnClickListener(v -> {
        });
    }

    @SuppressLint("ClickableViewAccessibility")
    public void refresh(ChargePackageInfo info) {
        ChargeConfigInfo configInfo = info.configData;
        ImageLoaderUtil.loadNormalImage(configInfo.endInfo.imgUrl, bgIv);
        ImageLoaderUtil.loadNormalImage(configInfo.endInfo.buttonUrl, enterTv);
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) enterTv.getLayoutParams();
        params.bottomMargin = ScreenUtil.ios2px(configInfo.endInfo.marginBottom);
        enterTv.setLayoutParams(params);
        enterTv.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    ImageLoaderUtil.loadNormalImage(configInfo.endInfo.buttonClickUrl, enterTv);
                    break;
                case MotionEvent.ACTION_UP:
                    if (callback != null) callback.onClose();
                    ImageLoaderUtil.loadNormalImage(configInfo.endInfo.buttonUrl, enterTv);
                    break;
                case MotionEvent.ACTION_CANCEL:
                    ImageLoaderUtil.loadNormalImage(configInfo.endInfo.buttonUrl, enterTv);
                    break;
            }
            return true;
        });
    }

    public void setCallback(FirstChargeCallback callback) {
        this.callback = callback;
    }

    public static void showResultDialog(Context context, ChargePackageInfo info) {
        final FirstChargeConfigurableResultDialog view = new FirstChargeConfigurableResultDialog(context);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_background_80);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(false);
        dialog.initFullWidthWithOrientation();
        view.refresh(info);
        view.setCallback(new FirstChargeCallback() {
            @Override
            public void onClose() {
                dialog.dismiss();
            }
        });
        dialog.show();
    }
}
