package com.wepie.wespy.module.firstcharge.configurable;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.os.CountDownTimer;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.collection.ArrayMap;

import com.google.android.gms.common.util.CollectionUtils;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.ViewUtil;
import com.huiwan.configservice.model.WespyGoods;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.AuthApi;
import com.huiwan.lib.api.plugins.IapApi;
import com.huiwan.module.authcheck.IDAuthCheckManager;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.cancellable.HandlerExKt;
import com.wepie.lib.api.plugins.track.TrackApi;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.model.entity.ChargeConfigInfo;
import com.wepie.wespy.model.entity.ChargePackageInfo;
import com.wepie.wespy.module.firstcharge.FirstChargeConfig;
import com.wepie.wespy.module.firstcharge.newuser.CloseCallback;
import com.wepie.wespy.module.firstcharge.util.CountdownUtil;
import com.wepie.wespy.module.firstcharge.util.FirstChargeUtil;
import com.wepie.wespy.module.pay.commonapi.InnerPayApi;
import com.wepie.wespy.module.pay.commonapi.WpPayResult;
import com.wepie.wespy.net.http.api.OrderApi;

import java.util.ArrayList;
import java.util.List;

public class FirstChargeConfigurableItemView extends FrameLayout {

    private final Context mContext;
    private ImageView bgIv, submitBtn, closeIv;
    private TextView hourTv, minuteTv, secondTv;
    private RelativeLayout timeLay;
    private CloseCallback callback;
    private CountDownTimer timer;
    private ChargePackageInfo info;
    private ArrayList<WespyGoods> goodList = new ArrayList<>();
    private long deadline = 0;

    public FirstChargeConfigurableItemView(@NonNull Context context) {
        super(context);
        mContext = context;
        initView();
    }

    public FirstChargeConfigurableItemView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initView();
    }

    private void initView() {
        LayoutInflater.from(mContext).inflate(R.layout.first_charge_configurable_item_view, this);
        bgIv = findViewById(R.id.bg_iv);
        submitBtn = findViewById(R.id.submit_btn);
        closeIv = findViewById(R.id.close_iv);
        timeLay = findViewById(R.id.time_lay);
        hourTv = findViewById(R.id.hour_tv);
        minuteTv = findViewById(R.id.minute_tv);
        secondTv = findViewById(R.id.second_tv);

        closeIv.setOnClickListener(v -> {
            if (callback != null) callback.closeAll();
        });
    }

    public void setCallback(CloseCallback callback) {
        this.callback = callback;
    }

    @SuppressLint("ClickableViewAccessibility")
    public void refresh(ChargePackageInfo info) {
        this.info = info;
        ChargeConfigInfo configInfo = info.configData;
        this.deadline = info.deadlineInSec * 1000;
        showTime();
        ImageLoaderUtil.loadNormalImage(configInfo.startInfo.imgUrl, bgIv);
        ImageLoaderUtil.loadNormalImage(configInfo.startInfo.buttonUrl, submitBtn);
        ViewUtil.setBottomMargins(submitBtn, ScreenUtil.ios2px(configInfo.startInfo.marginBottom));
        ViewUtil.setTopMargins(timeLay, ScreenUtil.ios2px(configInfo.startInfo.marginTop));
        ViewUtil.setTopMargins(closeIv, ScreenUtil.ios2px(460 - configInfo.startInfo.closeMarginBottom));
        submitBtn.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    ImageLoaderUtil.loadNormalImage(configInfo.startInfo.buttonClickUrl, submitBtn);
                    break;
                case MotionEvent.ACTION_UP:
                    if (CollectionUtils.isEmpty(goodList)) {
                        OrderApi.getGoodsList(OrderApi.GoodsType.GIFT_PACKET, ApiService.of(IapApi.class).getCurrency(), new LifeDataCallback<List<WespyGoods>>(this) {
                            @Override
                            public void onSuccess(Result<List<WespyGoods>> result) {
                                if (goodList == null) {
                                    goodList = new ArrayList<>();
                                }
                                goodList.clear();
                                goodList.addAll(result.data);
                                chargeMoney(goodList);
                            }

                            @Override
                            public void onFail(int code, String msg) {
                                ToastUtil.show(msg);
                            }
                        });
                    } else {
                        chargeMoney(goodList);
                    }
                    ImageLoaderUtil.loadNormalImage(configInfo.startInfo.buttonUrl, submitBtn);
                    break;
                case MotionEvent.ACTION_CANCEL:
                    ImageLoaderUtil.loadNormalImage(configInfo.startInfo.buttonUrl, submitBtn);
                    break;
            }
            return true;
        });
    }

    private void showTime() {
        long deadlineOffset = deadline - TimeUtil.getServerTime();
        if (deadlineOffset < 1000L) {
            if (callback != null) {
                HandlerExKt.postAutoCancel(this, 30, () -> {
                    if (callback != null) {
                        callback.close();
                    }
                });
            }
            return;
        }
        hourTv.setText(CountdownUtil.getHour(deadlineOffset));
        minuteTv.setText(CountdownUtil.getMinute(deadlineOffset));
        secondTv.setText(CountdownUtil.getSecond(deadlineOffset));
        if (timer != null) {
            timer.cancel();
        }
        timer = new CountDownTimer(deadlineOffset, 1000L) {
            @Override
            public void onTick(long millisUntilFinished) {
                hourTv.setText(CountdownUtil.getHour(millisUntilFinished));
                minuteTv.setText(CountdownUtil.getMinute(millisUntilFinished));
                secondTv.setText(CountdownUtil.getSecond(millisUntilFinished));
            }

            @Override
            public void onFinish() {
                if (callback != null) callback.close();
            }
        };
        timer.start();
    }

    private void chargeMoney(ArrayList<WespyGoods> goodList) {
        if (info == null) return;
        WespyGoods goods = null;
        for (int i = 0; i < goodList.size(); i++) {
            if (info.goodsId == goodList.get(i).goods_id) {
                goods = goodList.get(i);
            }
        }

        if (goods != null) {
            ArrayMap<String, Object> map = new ArrayMap<>(1);
            map.put("goods_id", goods.goods_id);
            ApiService.of(TrackApi.class).appClick(TrackScreenName.GIFT_PACK_DIALOG, TrackSource.CHARGE_BOX_NAME, map);

            if (!CollectionUtils.isEmpty(info.configData.extraItem)) {
                goods.setFirstCharge(1);
            }
            final WespyGoods finalGoods = goods;
            final ChargePackageInfo finalInfo = info;
            final Runnable showPayRunnable = () -> {
                Activity activity = ContextUtil.getActivityFromContext(mContext);
                if (activity != null) {
                    InnerPayApi.showPay(finalGoods, activity, new InnerPayApi.PayResultCallback() {
                        @Override
                        public void onSuccess(WpPayResult payResult) {
                            FirstChargeConfigurableResultDialog.showResultDialog(mContext, finalInfo);
                            FirstChargeUtil.showFirstCharge(false, false, FirstChargeConfig.DEFAULT);
                        }

                        @Override
                        public void onFail() {
                        }
                    });
                    clickShenceDot();
                }
            };
            if (info.needReal) {
                IDAuthCheckManager.doTaskOrShowNeedCertificate(mContext, AuthApi.SCENE_CHARGE, showPayRunnable::run);
            } else {
                showPayRunnable.run();
            }
        } else {
            ToastUtil.show(R.string.golds_data_error_please_again);
            goodList.clear();
        }
    }

    private void clickShenceDot() {
        if (info.packageType == FirstChargeConfig.QIXI_ONE) {
            ShenceEvent.clickBuyFirstCharge(TrackScreenName.FIRST_CHARGE_QIXI_ONE, TrackScreenName.FIRST_CHARGE_QIXI_ONE, info);
        } else if (info.packageType == FirstChargeConfig.QIXI_TWO) {
            ShenceEvent.clickBuyFirstCharge(TrackScreenName.FIRST_CHARGE_QIXI_TWO, TrackScreenName.FIRST_CHARGE_QIXI_TWO, info);
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        showTime();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (timer != null) {
            timer.cancel();
        }
    }
}
