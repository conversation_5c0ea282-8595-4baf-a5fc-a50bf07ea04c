package com.wepie.wespy.module.firstcharge.newuser.propitem;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;

import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.propextra.HeadDecorExtra;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.widget.CustomCircleImageView;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.BuildConfig;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;

/**
 * date 2020-6-1
 *
 * <AUTHOR>
 */
public class FirstChargePropItemImageView extends ViewGroup {
    private CustomCircleImageView modelIv;
    private ImageView mediaIv;
    private final Paint paint = new Paint();
    private final RectF viewRect = new RectF();
    private PropItem showingItem = null;
    private boolean rp = false;
    private final boolean showMask = false;
    private final int maskColor = 0x66000000;

    public FirstChargePropItemImageView(@NonNull Context context) {
        this(context, null);
    }

    public FirstChargePropItemImageView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        paint.setAntiAlias(true);
        paint.setColor(Color.TRANSPARENT);
        initViews(context);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        int count = getChildCount();
        for (int i = 0; i < count; i++) {
            View view = getChildAt(i);
            if (view.getVisibility() != View.GONE) {
                int h = view.getMeasuredHeight();
                int w = view.getMeasuredWidth();
                int childLeft = (r - l - w) / 2;
                int childTop = (b - t - h) / 2;
                view.layout(childLeft, childTop, childLeft + w, childTop + h);
            }
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int widthMode = MeasureSpec.getMode(widthMeasureSpec);
        int heightMode = MeasureSpec.getMode(heightMeasureSpec);
        int widthSize = MeasureSpec.getSize(widthMeasureSpec);
        int heightSize = MeasureSpec.getSize(heightMeasureSpec);

        if (widthMode != MeasureSpec.EXACTLY && heightMode != MeasureSpec.EXACTLY) {
            setMeasuredDimension(0, 0);
            if (BuildConfig.DEBUG) {
                ToastUtil.show("PropItemImageView need exactly measure mode");
            }
            return;
        }

        int measureSize;
        if (widthMode == MeasureSpec.EXACTLY) {
            measureSize = widthSize;
        } else {
            measureSize = heightSize;
        }
        updateModelLp(measureSize);
        showItemWithGivenSize(measureSize);
        int count = getChildCount();
        for (int i = 0; i < count; i++) {
            View view = getChildAt(i);
            if (view.getVisibility() != View.GONE) {
                measureChild(view, MeasureSpec.makeMeasureSpec(measureSize, MeasureSpec.EXACTLY), MeasureSpec.makeMeasureSpec(measureSize, MeasureSpec.EXACTLY));
            }
        }
        setMeasuredDimension(measureSize, measureSize);
    }

    public void showPropItem(int propId) {
        showPropItem(ConfigHelper.getInstance().getPropConfig().getPropItem(propId));
    }

    public void showRedPacket() {
        rp = true;
        invalidate();
    }

    public void showPropItem(PropItem item) {
        if (item == null) {
            clear();
        } else {
            if (item.isRedPacket()) {
                showRedPacket();
            } else {
                showPropItemInternal(item);
            }
        }
    }

    private void clear() {
        modelIv.setImageDrawable(null);
        mediaIv.setImageDrawable(null);
        showingItem = null;
    }

    private void showPropItemInternal(PropItem item) {
        rp = false;
        showingItem = item;
        if (getMeasuredHeight() * getMeasuredWidth() != 0) {
            showItemWithGivenSize(getMeasuredHeight());
        }
    }

    private void showItemWithGivenSize(final int givenSize) {
        if (rp) {
            showRp();
            return;
        }
        PropItem item = showingItem;
        if (item == null) {
            return;
        }
        boolean needCrop = updateMediaLp(item.getType(), givenSize);
        if (item.getType() == PropItem.TYPE_HEAD_DECORATION || item.getType() == PropItem.TYPE_FAMILY_AVATAR_DECOR) {
            HeadDecorExtra extra = item.getHeadDecorExtra();
            if (extra != null) {
                WpImageLoader.load(extra.getModelUrl(), modelIv, HeadImageLoader.genHeadLoadInfo());
                modelIv.setVisibility(VISIBLE);
            } else {
                modelIv.setVisibility(GONE);
            }
        } else {
            modelIv.setVisibility(GONE);
        }
        if (needCrop) {
            int radius = givenSize * 12 / 60;
            ImageLoaderUtil.loadNormalPxCornerImage(item.getMediaUrl(), radius, mediaIv);
        } else {
            ImageLoaderUtil.loadNormalImage(item.getMediaUrl(), mediaIv);
        }
    }

    private void showRp() {
        modelIv.setVisibility(GONE);
        LayoutParams lp = mediaIv.getLayoutParams();
        int lpSize = getMeasuredWidth() * 60 / 80;
        lp.width = lp.height = lpSize;
        mediaIv.requestLayout();
        mediaIv.setImageResource(R.drawable.red_packet_60);
    }

    private boolean updateMediaLp(int type, int measuredSize) {
        LayoutParams lp = mediaIv.getLayoutParams();
        boolean res = false;
        int lpSize;
        switch (type) {
            case PropItem.TYPE_RING:
                lpSize = measuredSize * 68 / 80;
                break;
            case PropItem.TYPE_PROP:
                lpSize = measuredSize * 60 / 80;
                break;
            case PropItem.TYPE_RESOURCE:
                lpSize = measuredSize;
                break;
            case PropItem.TYPE_HEAD_DECORATION:
                lpSize = measuredSize * 83 / 93;
                break;
            case PropItem.TYPE_FAMILY_AVATAR_DECOR:
                lpSize = measuredSize * 83 / 80;
                break;
            case PropItem.TYPE_GIFT_CARD:
                lpSize = measuredSize * 84 / 80;
                break;
            case PropItem.TYPE_FAMILY_BOX:
            case PropItem.TYPE_CHAT_BUBBLE:
            case PropItem.TYPE_HOME_ANIM:
            case PropItem.TYPE_DRAW_BOARD:
            case PropItem.TYPE_VOICE_BUBBLE:
            case PropItem.TYPE_VOICE_ENTER_ANIM:
            case PropItem.TYPE_DISCOVER_DECOR:
            case PropItem.TYPE_FAMILY_CHAT_BUBBLE:
            case PropItem.TYPE_USER_TAG:
            case PropItem.TYPE_VOICE_THEME:
            case PropItem.TYPE_VOICE_MIC:
            default:
                lpSize = LayoutParams.MATCH_PARENT;
                res = true;
        }
        lp.width = lp.height = lpSize;
        return res;
    }

    private void updateModelLp(int size) {
        LayoutParams modelLp = modelIv.getLayoutParams();
        modelLp.width = size * 60 / 80;
        modelLp.height = size * 60 / 80;
    }

    @Override
    protected void dispatchDraw(Canvas canvas) {
        float radius = getMeasuredHeight() * 12f / 60;
        viewRect.set(0, 0, getMeasuredWidth(), getMeasuredHeight());
        paint.setColor(0xffffffff);
        canvas.drawRoundRect(viewRect, radius, radius, paint);
        super.dispatchDraw(canvas);
        if (showMask) {
            paint.setColor(maskColor);
            canvas.drawRoundRect(viewRect, radius, radius, paint);
        }
    }

    private void initViews(Context context) {
        mediaIv = new AppCompatImageView(context);
        modelIv = new CustomCircleImageView(context);

        addView(modelIv, new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));
        addView(mediaIv, new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));
    }
}
