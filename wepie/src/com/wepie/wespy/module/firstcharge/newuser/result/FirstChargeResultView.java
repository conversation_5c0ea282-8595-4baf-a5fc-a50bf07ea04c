package com.wepie.wespy.module.firstcharge.newuser.result;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.ChargePackageInfo;
import com.wepie.wespy.model.entity.ChargePropInfo;
import com.wepie.wespy.module.firstcharge.FirstChargeCallback;
import com.wepie.wespy.module.firstcharge.FirstChargeConfig;
import com.wepie.wespy.module.firstcharge.newuser.FirstChargeRecycleViewAdapter;

import java.util.ArrayList;

public class FirstChargeResultView extends RelativeLayout {

    private Context mContext;
    private RelativeLayout allLay;
    private LinearLayout dialogLay;
    private RecyclerView resultRv;
    private TextView submitTv;
    private FirstChargeRecycleViewAdapter adapter;
    private FirstChargeCallback callback;

    public FirstChargeResultView(Context context) {
        super(context);
        mContext = context;
        initView();
    }

    public FirstChargeResultView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initView();
    }

    private void initView() {
        LayoutInflater.from(mContext).inflate(R.layout.first_charge_result_view, this);
        allLay = findViewById(R.id.result_all_lay);
        dialogLay = findViewById(R.id.result_dialog_lay);
        resultRv = findViewById(R.id.result_rv);
        submitTv = findViewById(R.id.submit_tv);
        adapter = new FirstChargeRecycleViewAdapter(mContext);
        GridLayoutManager manager = new GridLayoutManager(mContext, 3);
        resultRv.setLayoutManager(manager);
        resultRv.setAdapter(adapter);
        initEvent();
    }

    private void initEvent() {
        submitTv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (callback != null) callback.onClose();
            }
        });
        allLay.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (callback != null) callback.onClose();
            }
        });
        dialogLay.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
            }
        });
    }

    public void setCallback(FirstChargeCallback callback) {
        this.callback = callback;
    }

    public void refresh(ChargePackageInfo info) {
        ArrayList<ChargePropInfo> propInfoList = new ArrayList<>();
        if (info.configData.item != null && info.configData.item.size() > 0) {
            propInfoList.addAll(info.configData.item);
        }
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams)submitTv.getLayoutParams();
        if (info.configData.extraItem != null && info.configData.extraItem.size() > 0) {
            propInfoList.addAll(info.configData.extraItem);
            layoutParams.setMargins((int)getResources().getDimension(R.dimen.ios40),(int)getResources().getDimension(R.dimen.ios16),
                    (int)getResources().getDimension(R.dimen.ios40),(int)getResources().getDimension(R.dimen.ios52));
        } else {
            layoutParams.setMargins((int)getResources().getDimension(R.dimen.ios40),(int)getResources().getDimension(R.dimen.ios26),
                    (int)getResources().getDimension(R.dimen.ios40),(int)getResources().getDimension(R.dimen.ios52));
        }
        adapter.setList(propInfoList);
        if (info.packageType > FirstChargeConfig.TEN_WIN) {
            dialogLay.setBackgroundResource(R.drawable.cp_charge_result);
            submitTv.setBackgroundResource(R.drawable.sel_9458ff_corner24_stroke2);
        } else {
            dialogLay.setBackgroundResource(R.drawable.first_charge_result);
            submitTv.setBackgroundResource(R.drawable.sel_29c472_corner24_stroke2);
        }
    }
}
