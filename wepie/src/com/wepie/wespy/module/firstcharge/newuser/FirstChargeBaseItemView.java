package com.wepie.wespy.module.firstcharge.newuser;

import android.content.Context;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ColorUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.StringUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.configservice.model.WespyGoods;
import com.huiwan.lib.api.plugins.AuthApi;
import com.huiwan.module.authcheck.IDAuthCheckManager;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.lib.api.plugins.track.config.cn.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackString;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.model.entity.ChargePackageInfo;
import com.wepie.wespy.module.firstcharge.FirstChargeConfig;
import com.wepie.wespy.module.firstcharge.util.BarrageAnimUtil;
import com.wepie.wespy.module.firstcharge.util.CountdownUtil;
import com.wepie.wespy.module.firstcharge.util.FirstChargeUtil;
import com.wepie.wespy.module.pay.commonapi.InnerPayApi;
import com.wepie.wespy.module.pay.commonapi.WpPayResult;
import com.wepie.wespy.net.http.api.FirstChargeApi;

import java.util.ArrayList;

public class FirstChargeBaseItemView extends RelativeLayout {

    private Context mContext;
    private ImageView bgIv;
    private ImageView bgTitleIv;
    private TextView themeTv;
    private TextView hourTv;
    private TextView minuteTv;
    private TextView secondTv;
    private RecyclerView itemRv;
    private RecyclerView extraItemRv;
    private View barrageOneView;
    private View barrageTwoView;
    private View barrageThreeView;
    private TextView submitTv;
    private ImageView closeIv;
    private FirstChargeRecycleViewAdapter itemAdapter;
    private FirstChargeRecycleViewAdapter extraItemAdapter;
    private CloseCallback callback;
    private CountDownTimer timer;
    private ChargePackageInfo info;
    private ArrayList<WespyGoods> goodList = new ArrayList<>();
    private Handler handler = new Handler(Looper.getMainLooper());
    private long deadline = 0;
    private boolean barrage = false;

    public FirstChargeBaseItemView(Context context) {
        super(context);
        mContext = context;
    }

    public FirstChargeBaseItemView(Context context, int id, boolean barrage) {
        super(context);
        mContext = context;
        this.barrage = barrage;
        initView(id);
    }

    private void initView(int id) {
        LayoutInflater.from(mContext).inflate(id, this);
        bgIv = findViewById(R.id.first_charge_bg_iv);
        bgIv.setScaleX(mContext.getResources().getInteger(R.integer.image_scale_x));
        bgTitleIv = findViewById(R.id.first_charge_bg_title_iv);
        themeTv = findViewById(R.id.theme_tv);
        themeTv.setLayoutDirection(LAYOUT_DIRECTION_LTR);
        hourTv = findViewById(R.id.hour_tv);
        minuteTv = findViewById(R.id.minute_tv);
        secondTv = findViewById(R.id.second_tv);
        itemRv = findViewById(R.id.item_rv);
        extraItemRv = findViewById(R.id.extra_item_rv);
        submitTv = findViewById(R.id.submit_tv);
        closeIv = findViewById(R.id.close_iv);
        itemAdapter = new FirstChargeRecycleViewAdapter(mContext);
        LinearLayoutManager itemManager = new LinearLayoutManager(mContext);
        itemManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        itemRv.setLayoutManager(itemManager);
        itemRv.setAdapter(itemAdapter);
        extraItemAdapter = new FirstChargeRecycleViewAdapter(mContext);
        LinearLayoutManager extraItemManager = new LinearLayoutManager(mContext);
        extraItemManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        extraItemRv.setLayoutManager(extraItemManager);
        extraItemRv.setAdapter(extraItemAdapter);
        submitTv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (goodList == null || goodList.size() <= 0) {
                    FirstChargeApi.getChargeGoodsList(new LifeDataCallback<>(FirstChargeBaseItemView.this) {
                        @Override
                        public void onSuccess(Result<ArrayList<WespyGoods>> result) {
                            if (goodList == null) {
                                goodList = new ArrayList<>();
                            }
                            goodList.clear();
                            goodList.addAll(result.data);
                            chargeMoney(goodList);
                        }

                        @Override
                        public void onFail(int code, String msg) {
                            ToastUtil.show(msg);
                        }
                    });
                } else {
                    chargeMoney(goodList);
                }
            }
        });
        closeIv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (callback != null) callback.closeAll();
            }
        });
    }

    public void setCallback(CloseCallback callback) {
        this.callback = callback;
    }

    public void refresh(ChargePackageInfo info) {
        this.info = info;
        itemAdapter.setList(info.configData.item);
        itemAdapter.setType(info.packageType);
        extraItemAdapter.setList(info.configData.extraItem);
        extraItemAdapter.setType(info.packageType);
        boolean extraEmpty = info.configData.extraItem == null || info.configData.extraItem.size() <= 0;
        if (info.packageType == FirstChargeConfig.FIRST_WIN) {
            themeTv.setText(Html.fromHtml(ResUtil.getStr(R.string.gift_packet_first_win_tip)));
            themeTv.setTextColor(ColorUtil.getColor("BE4A00"));
            bgIv.setImageResource(extraEmpty ? R.drawable.gift_packet_win_bg_short : R.drawable.gift_packet_win_bg_long);
            bgTitleIv.setImageResource(R.drawable.gift_packet_win_666_title);
        } else if (info.packageType == FirstChargeConfig.SIX_WIN) {
            themeTv.setText(Html.fromHtml(ResUtil.getStr(R.string.gift_packet_six_win_tip)));
            themeTv.setTextColor(ColorUtil.getColor("BE4A00"));
            bgTitleIv.setImageResource(R.drawable.gift_packet_win_666_title);
            bgIv.setImageResource(extraEmpty ? R.drawable.gift_packet_win_bg_short : R.drawable.gift_packet_win_bg_long);
        } else if (info.packageType == FirstChargeConfig.TEN_WIN) {
            themeTv.setText(Html.fromHtml(ResUtil.getStr(R.string.gift_packet_ten_win_tip)));
            themeTv.setTextColor(ColorUtil.getColor("BE4A00"));
            bgIv.setImageResource(extraEmpty ? R.drawable.gift_packet_win_bg_short : R.drawable.gift_packet_win_bg_long);
            bgTitleIv.setImageResource(R.drawable.gift_packet_win_888_title);
        } else if (info.packageType == FirstChargeConfig.CARE_ONE) {
            themeTv.setText(Html.fromHtml(ResUtil.getStr(R.string.gift_packet_guard_2000_tip, checkName(info.configData.description))));
            themeTv.setTextColor(ColorUtil.getColor("B5386E"));
            bgIv.setImageResource(R.drawable.gift_packet_cp_bg);
            bgTitleIv.setImageResource(R.drawable.gift_packet_cp_2000_title);
            if (info.configData.item != null && info.configData.item.size() > 1) {
                TextView tipsTv = findViewById(R.id.tips_tv);
                PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(info.configData.item.get(0).propId);
                if (propItem != null) {
                    tipsTv.setText(propItem.getSimpleDesc());
                }
            }
        } else if (info.packageType == FirstChargeConfig.CARE_TWO) {
            themeTv.setText(Html.fromHtml(ResUtil.getStr(R.string.gift_packet_guard_3000_tip, checkName(info.configData.description))));
            themeTv.setTextColor(ColorUtil.getColor("B5386E"));
            bgIv.setImageResource(R.drawable.gift_packet_cp_bg);
            bgTitleIv.setImageResource(R.drawable.gift_packet_cp_3000_title);
            if (info.configData.item != null && info.configData.item.size() > 1) {
                TextView tipsTv = findViewById(R.id.tips_tv);
                PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(info.configData.item.get(0).propId);
                if (propItem != null) {
                    tipsTv.setText(propItem.getSimpleDesc());
                }
            }
        }
        this.deadline = info.deadlineInSec * 1000L;
        showTime();
        String price = info.configData.currency + " " + info.configData.price;
        submitTv.setText(price);
        if (info.configData.item != null && info.configData.item.size() > 0) {
            itemRv.setVisibility(VISIBLE);
            itemAdapter.setList(info.configData.item);
        } else {
            itemRv.setVisibility(GONE);
        }
        if (info.configData.extraItem != null && info.configData.extraItem.size() > 0) {
            extraItemRv.setVisibility(VISIBLE);
            extraItemAdapter.setList(info.configData.extraItem);
        } else {
            extraItemRv.setVisibility(GONE);
        }
        if (barrage) {
            showBarrage();
        }
    }

    private void showBarrage() {
        barrageOneView = findViewById(R.id.barrage_one_iv);
        barrageTwoView = findViewById(R.id.barrage_two_iv);
        barrageThreeView = findViewById(R.id.barrage_three_iv);
        barrageOneView.setVisibility(INVISIBLE);
        barrageTwoView.setVisibility(INVISIBLE);
        barrageThreeView.setVisibility(INVISIBLE);
        barrageOneView.post(new Runnable() {
            @Override
            public void run() {
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        barrageOneView.setVisibility(VISIBLE);
                        BarrageAnimUtil.showAnim(barrageOneView, 4000, getWidth());
                    }
                }, 1000);
                barrageTwoView.setVisibility(VISIBLE);
                BarrageAnimUtil.showAnim(barrageTwoView, 4500, getWidth());
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        barrageThreeView.setVisibility(VISIBLE);
                        BarrageAnimUtil.showAnim(barrageThreeView, 4500, getWidth());
                    }
                }, 2000);
            }
        });
    }

    private void showTime() {
        long deadlineOffset = deadline - TimeUtil.getServerTime();
        if (deadlineOffset < 1000L) {
            if (callback != null) callback.close();
            return;
        }
        hourTv.setText(CountdownUtil.getHour(deadlineOffset));
        minuteTv.setText(CountdownUtil.getMinute(deadlineOffset));
        secondTv.setText(CountdownUtil.getSecond(deadlineOffset));
        if (timer != null) {
            timer.cancel();
        }
        timer = new CountDownTimer(deadlineOffset, 1000L) {
            @Override
            public void onTick(long millisUntilFinished) {
                hourTv.setText(CountdownUtil.getHour(millisUntilFinished));
                minuteTv.setText(CountdownUtil.getMinute(millisUntilFinished));
                secondTv.setText(CountdownUtil.getSecond(millisUntilFinished));
            }

            @Override
            public void onFinish() {
                if (callback != null) callback.close();
            }
        };
        timer.start();
    }

    private String checkName(String description) {
        return StringUtil.subName(description, 6);
    }

    private void chargeMoney(ArrayList<WespyGoods> goodList) {
        WespyGoods goods = null;
        for (int i = 0; i < goodList.size(); i++) {
            if (info.goodsId == goodList.get(i).goods_id) {
                goods = goodList.get(i);
            }
        }
        if (goods != null) {
            if (info.configData.extraItem != null && info.configData.extraItem.size() > 0) {
                goods.setFirstCharge(1);
            }
            final WespyGoods finalGoods = goods;
            if (info.needReal) {
                IDAuthCheckManager.doTaskOrShowNeedCertificate(mContext, AuthApi.SCENE_CHARGE, new IDAuthCheckManager.TaskCallback() {
                    @Override
                    public void onDoTask() {
                        if (ContextUtil.getActivityFromContext(mContext) != null) {
                            InnerPayApi.showPay(finalGoods, ContextUtil.getActivityFromContext(mContext), new InnerPayApi.PayResultCallback() {
                                @Override
                                public void onSuccess(WpPayResult payResult) {
                                    FirstChargeUtil.showResultDialog(mContext, info);
                                    FirstChargeUtil.showFirstCharge(false, false, FirstChargeConfig.DEFAULT);
                                }

                                @Override
                                public void onFail() {
                                }
                            });
                            clickShenceDot();
                        }
                    }
                });
            } else {
                if (ContextUtil.getActivityFromContext(mContext) != null) {
                    InnerPayApi.showPay(finalGoods, ContextUtil.getActivityFromContext(mContext), new InnerPayApi.PayResultCallback() {
                        @Override
                        public void onSuccess(WpPayResult payResult) {
                            FirstChargeUtil.showResultDialog(mContext, info);
                            FirstChargeUtil.showFirstCharge(false, false, FirstChargeConfig.DEFAULT);
                        }

                        @Override
                        public void onFail() {
                        }
                    });
                    clickShenceDot();
                }
            }
        } else {
            ToastUtil.show(R.string.product_data_error_retry);
            goodList.clear();
        }
    }

    private void clickShenceDot() {
        if (info.packageType == FirstChargeConfig.FIRST_WIN) {
            ShenceEvent.clickBuyFirstCharge(TrackScreenName.FIRST_CHARGE_FIRST_WIN, TrackString.CHARGE_FIRST_WIN, info);
        } else if (info.packageType == FirstChargeConfig.SIX_WIN) {
            ShenceEvent.clickBuyFirstCharge(TrackScreenName.FIRST_CHARGE_SIX_WIN, TrackString.CHARGE_SIX_WIN, info);
        } else if (info.packageType == FirstChargeConfig.TEN_WIN) {
            ShenceEvent.clickBuyFirstCharge(TrackScreenName.FIRST_CHARGE_TEN_WIN, TrackString.CHARGE_TEN_WIN, info);
        } else if (info.packageType == FirstChargeConfig.CARE_ONE) {
            ShenceEvent.clickBuyFirstCharge(TrackScreenName.FIRST_CHARGE_CARE_ONE, TrackString.CHARGE_CARE_2000, info);
        } else if (info.packageType == FirstChargeConfig.CARE_TWO) {
            ShenceEvent.clickBuyFirstCharge(TrackScreenName.FIRST_CHARGE_CARE_TWO, TrackString.CHARGE_CARE_3000, info);
        }
    }


    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        showTime();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (timer != null) {
            timer.cancel();
        }
        if (barrageOneView != null) {
            BarrageAnimUtil.clearAnim(barrageOneView);
        }
        if (barrageTwoView != null) {
            BarrageAnimUtil.clearAnim(barrageTwoView);
        }
        if (barrageThreeView != null) {
            BarrageAnimUtil.clearAnim(barrageThreeView);
        }
        handler.removeCallbacksAndMessages(null);
    }
}
