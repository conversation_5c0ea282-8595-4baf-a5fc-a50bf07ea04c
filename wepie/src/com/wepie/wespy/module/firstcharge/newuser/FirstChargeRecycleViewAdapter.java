package com.wepie.wespy.module.firstcharge.newuser;

import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.R;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.PropItem;
import com.wepie.wespy.model.entity.ChargePropInfo;
import com.wepie.wespy.module.firstcharge.FirstChargeConfig;
import com.wepie.wespy.module.firstcharge.newuser.propitem.FirstChargePropItemImageView;
import com.huiwan.base.util.ColorUtil;
import com.wepie.wespy.utils.PopupUtil;
import com.huiwan.base.util.ScreenUtil;

import java.util.ArrayList;

public class FirstChargeRecycleViewAdapter extends RecyclerView.Adapter<FirstChargeRecycleViewAdapter.FirstChargeRecycleViewHolder> {

    private Context mContext;
    private ArrayList<ChargePropInfo> propInfoList = new ArrayList<>();
    private int type = 1;

    public FirstChargeRecycleViewAdapter(Context context) {
        mContext = context;
    }

    public void setList(ArrayList<ChargePropInfo> list) {
        this.propInfoList.clear();
        this.propInfoList.addAll(list);
    }

    public void setType(int type) {
        this.type = type;
    }

    @NonNull
    @Override
    public FirstChargeRecycleViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.first_charge_gift_item_view, viewGroup, false);
        return new FirstChargeRecycleViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull FirstChargeRecycleViewHolder holder, int i) {
        holder.update(propInfoList.get(i), type);
    }

    @Override
    public int getItemCount() {
        return propInfoList.size();
    }


    static class FirstChargeRecycleViewHolder extends RecyclerView.ViewHolder {

        private static final float TIP_ROTATION = 13F;

        private RelativeLayout bgView;
        private FirstChargePropItemImageView propIv;
        private TextView numTv;
        private TextView nameTv;
        private ImageView tipsIv;
        private TextView tipsTv;

        public FirstChargeRecycleViewHolder(@NonNull View itemView) {
            super(itemView);
            bgView = itemView.findViewById(R.id.content_lay);
            propIv = itemView.findViewById(R.id.prop_iv);
            numTv = itemView.findViewById(R.id.num_tv);
            nameTv = itemView.findViewById(R.id.name_tv);
            tipsIv = itemView.findViewById(R.id.tips_iv);
            tipsTv = itemView.findViewById(R.id.tips_tv);
            if (ScreenUtil.isRtl()) {
                tipsTv.setRotation(-TIP_ROTATION);
            } else {
                tipsTv.setRotation(TIP_ROTATION);
            }
        }

        public void update(ChargePropInfo info, int type) {
            PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(info.propId);
            if (propItem != null) {
                propIv.showPropItem(propItem);
                if(propItem.getType() == PropItem.TYPE_HEAD_DECORATION) {
                    nameTv.setText(ResUtil.getStr(R.string.avatar_frame));
                } else if (propItem.getType() == PropItem.TYPE_GIFT_CARD){
                    nameTv.setText(ResUtil.getStr(R.string.gift_card, propItem.getName()));
                }else {
                    nameTv.setText(propItem.getName());
                }
            }
            bgView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    PopupUtil.showFirstChargePopupView(bgView, info.toastTips);
                }
            });
            if (type > FirstChargeConfig.TEN_WIN) {
                numTv.setBackgroundResource(R.drawable.shape_f6446c_corner9);
            } else {
                numTv.setBackgroundResource(R.drawable.shape_ff6e06_corner9);
            }
            if (info.num != null && !info.num.isEmpty()) {
                numTv.setVisibility(View.VISIBLE);
                tipsIv.setVisibility(View.GONE);
                tipsTv.setVisibility(View.GONE);
                numTv.setText(info.num);
            } else if (info.tips != null && !info.tips.isEmpty()) {
                tipsIv.setVisibility(View.VISIBLE);
                tipsTv.setVisibility(View.VISIBLE);
                tipsTv.setText(info.tips);
                numTv.setVisibility(View.GONE);
            } else {
                tipsIv.setVisibility(View.GONE);
                tipsTv.setVisibility(View.GONE);
                numTv.setVisibility(View.GONE);
            }
            GradientDrawable drawable = new GradientDrawable();
            drawable.setColor(0xffffffff);
            drawable.setCornerRadius(ScreenUtil.dip2px(12));
            drawable.setStroke(ScreenUtil.dip2px(2), ColorUtil.getColor(info.frameColor));
            bgView.setBackground(drawable);
        }
    }
}
