package com.wepie.wespy.module.firstcharge;

import android.content.Context;
import android.content.DialogInterface;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.RelativeLayout;

import androidx.viewpager2.widget.ViewPager2;

import com.huiwan.base.util.TimeUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.model.entity.ChargePackageInfo;
import com.wepie.wespy.model.entity.ChargeUserDataInfo;
import com.wepie.wespy.model.event.FirstChargeBuyEvent;
import com.wepie.wespy.module.firstcharge.util.FirstChargeUtil;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class FirstChargeView extends RelativeLayout {

    private final Context mContext;
    private ViewPager2 firstChargeViewpager;
    private FirstChargeAdapter adapter;
    private FirstChargeCallback callback;
    private static boolean isShow = false;

    private String openType = "";
    private String scene = "";
    private boolean isInGame = false;

    public FirstChargeView(Context context) {
        super(context);
        mContext = context;
        initView();
    }

    public FirstChargeView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initView();
    }

    private void initView() {
        LayoutInflater.from(mContext).inflate(R.layout.first_charge_view, this);
        firstChargeViewpager = findViewById(R.id.first_charge_viewpager);
        firstChargeViewpager.setPageTransformer(new ZoomOutPageTransformer2());
        adapter = new FirstChargeAdapter(mContext);
        firstChargeViewpager.setAdapter(adapter);
        firstChargeViewpager.setOffscreenPageLimit(3);
        firstChargeViewpager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {

            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                ChargePackageInfo packageInfo = adapter.getCurData(position);
                if (packageInfo != null) {
                    FirstChargeUtil.trackGiftPackDialogShow(packageInfo.packageType, scene,
                            FirstChargeConfig.TRACK_SOURCE_AUTO_OPEN.equals(openType) ? 1 : 0, isInGame ? 1 : 0);
                }
            }
        });
    }

    public void setCallback(FirstChargeCallback callback) {
        this.callback = callback;
        adapter.setCallback(callback);
    }

    public int refresh(ChargeUserDataInfo info, int type) {
        if (info == null) return type;
        List<ChargePackageInfo> packageInfoList = new ArrayList<>();
        if (info.data != null && info.data.size() > 0) {
            packageInfoList.addAll(info.data);
        }
        if (info.configData != null && info.configData.size() > 0) {
            packageInfoList.addAll(info.configData);
        }
        if (info.other != null && info.other.size() > 0) {
            packageInfoList.addAll(info.other);
        }
        Collections.sort(packageInfoList, (o1, o2) -> (int) (o1.deadlineInSec - o2.deadlineInSec));
        adapter.refresh(packageInfoList, info.gameTestGroup);
        firstChargeViewpager.requestTransform();
        int index = 0;
        for (int i = 0; i < packageInfoList.size(); i++) {
            if (type == packageInfoList.get(i).packageType) {
                index = i;
            }
        }
        if (packageInfoList.size() > 0 && index < packageInfoList.size()) {
            firstChargeViewpager.setCurrentItem(index);
            return packageInfoList.get(index).packageType;
        } else {
            return 0;
        }
    }

    public static void showDialog(Context context, ChargeUserDataInfo info, int type, String openType, String scene, boolean isInGame) {
        if (isShow) {
            return;
        }
        FirstChargeView firstChargeView = new FirstChargeView(context);
        firstChargeView.scene = scene;
        firstChargeView.openType = openType;
        firstChargeView.isInGame = isInGame;
        int screenType = firstChargeView.refresh(info, type);
        BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_background_80);
        dialog.setContentView(firstChargeView);
        dialog.setCanceledOnTouchOutside(true);
        dialog.initFullWidth();
        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                isShow = false;
            }
        });
        firstChargeView.setCallback(new FirstChargeCallback() {
            @Override
            public void onClose() {
                dialog.dismiss();
            }
        });
        dialog.show();
        isShow = true;
        clickShenceDot(screenType, openType);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        EventDispatcher.registerEventObserver(this);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        EventDispatcher.unregisterEventObserver(this);
        if (isShow) {
            isShow = false;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onFirstChargeBuyEvent(FirstChargeBuyEvent event) {
        if (checkInfo(event.info)) {
            refresh(event.info, event.type);
            if (event.type != FirstChargeConfig.DEFAULT) {
                clickShenceDot(event.type, FirstChargeConfig.TRACK_SOURCE_AUTO_OPEN);
            }
        } else {
            if (callback != null) callback.onClose();
        }
    }

    private boolean checkInfo(ChargeUserDataInfo info) {
        if (info == null || info.data == null || info.data.isEmpty()) {
            return false;
        }
        long recentTime = Long.MAX_VALUE;
        ArrayList<ChargePackageInfo> packageInfoList = new ArrayList<>();
        for (int i = 0; i < info.data.size(); i++) {
            long deadline = info.data.get(i).deadlineInSec * 1000L - TimeUtil.getServerTime();
            if (deadline >= 1000L) {
                packageInfoList.add(info.data.get(i));
                if (recentTime > deadline) {
                    recentTime = deadline;
                }
            }
        }
        info.data = packageInfoList;
        return recentTime != Long.MAX_VALUE;
    }

    private static void clickShenceDot(int screenType, String shenceType) {
        if (screenType == FirstChargeConfig.FIRST_WIN) {
            ShenceEvent.openFirstChargeDialog(TrackScreenName.FIRST_CHARGE_FIRST_WIN, shenceType);
        } else if (screenType == FirstChargeConfig.SIX_WIN) {
            ShenceEvent.openFirstChargeDialog(TrackScreenName.FIRST_CHARGE_SIX_WIN, shenceType);
        } else if (screenType == FirstChargeConfig.TEN_WIN) {
            ShenceEvent.openFirstChargeDialog(TrackScreenName.FIRST_CHARGE_TEN_WIN, shenceType);
        } else if (screenType == FirstChargeConfig.CARE_ONE) {
            ShenceEvent.openFirstChargeDialog(TrackScreenName.FIRST_CHARGE_CARE_ONE, shenceType);
        } else if (screenType == FirstChargeConfig.CARE_TWO) {
            ShenceEvent.openFirstChargeDialog(TrackScreenName.FIRST_CHARGE_CARE_TWO, shenceType);
        } else if (screenType == FirstChargeConfig.QIXI_ONE) {
            ShenceEvent.openFirstChargeDialog(TrackScreenName.FIRST_CHARGE_QIXI_ONE, shenceType);
        } else if (screenType == FirstChargeConfig.QIXI_TWO) {
            ShenceEvent.openFirstChargeDialog(TrackScreenName.FIRST_CHARGE_QIXI_TWO, shenceType);
        }
    }
}
