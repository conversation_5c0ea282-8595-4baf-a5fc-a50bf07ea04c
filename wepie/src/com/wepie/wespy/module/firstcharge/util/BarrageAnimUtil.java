package com.wepie.wespy.module.firstcharge.util;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.view.View;
import android.view.animation.LinearInterpolator;

import com.huiwan.base.util.ScreenUtil;

public class BarrageAnimUtil {

    public static void showAnim(final View view, final long duration, int parentWidth) {
        ValueAnimator showAnimator;
        int width = view.getWidth();
        if (ScreenUtil.isRtl()) {
            view.setX(-width);
            showAnimator = ValueAnimator.ofInt(-width, parentWidth);
        } else {
            view.setX(parentWidth);
            showAnimator = ValueAnimator.ofInt(parentWidth, -width);
        }
        showAnimator.setInterpolator(new LinearInterpolator());
        showAnimator.setDuration(duration);
        showAnimator.addUpdateListener(animation -> view.setX((Integer) animation.getAnimatedValue()));
        showAnimator.setRepeatCount(ValueAnimator.INFINITE);
        showAnimator.start();
        view.setTag(showAnimator);
    }

    public static void clearAnim(final View view) {
        Object tag = view.getTag();
        if (!(tag instanceof Animator)) {
            return;
        }
        Animator animator = (Animator) tag;
        animator.cancel();
    }
}
