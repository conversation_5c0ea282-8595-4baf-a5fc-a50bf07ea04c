package com.wepie.wespy.module.firstcharge.util;

import android.app.Activity;
import android.content.Context;

import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.lib.api.ApiService;
import com.huiwan.user.LoginHelper;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.GlobalLife;
import com.wepie.lib.api.plugins.track.TrackApi;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.model.entity.ChargePackageInfo;
import com.wepie.wespy.model.entity.ChargeUserDataInfo;
import com.wepie.wespy.module.firstcharge.FirstChargeCallback;
import com.wepie.wespy.module.firstcharge.newuser.result.FirstChargeResultView;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.http.api.FirstChargeApi;

import org.json.JSONException;
import org.json.JSONObject;

public class FirstChargeUtil {

    public static void showResultDialog(Context context, ChargePackageInfo info) {
        final FirstChargeResultView view = new FirstChargeResultView(context);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_background_80);
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(false);
        dialog.initFullWidthWithOrientation();
        view.refresh(info);
        view.setCallback(new FirstChargeCallback() {
            @Override
            public void onClose() {
                dialog.dismiss();
            }
        });
        Activity activity = ContextUtil.getActivityFromContext(context);
        if (activity != null && !activity.isFinishing()) {
            dialog.show();
        } else {
            ToastUtil.show(R.string.pop_display_error);
        }
    }

    public static void showFirstCharge(boolean showToast, boolean isShow, int type) {
        FirstChargeApi.getChargeUserDataInfo(LoginHelper.getLoginUid(), new LifeDataCallback<>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<ChargeUserDataInfo> result) {
                EventDispatcher.postFirstChargeBuyEvent(isShow, type, result.data);
            }

            @Override
            public void onFail(int code, String msg) {
                if (showToast) {
                    ToastUtil.show(msg);
                }
            }
        });
    }

    public static void trackGiftPackDialogShow(int packageType, String scene, int isAuto, int status) {
        TrackApi trackApi = ApiService.of(TrackApi.class);
        if (trackApi == null) {
            return;
        }

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(trackApi.getTrackValue().getScreenName(), TrackScreenName.GIFT_PACK_DIALOG);
            jsonObject.put("scene", scene);
            jsonObject.put("giftbag_id", String.valueOf(packageType));
            jsonObject.put("is_auto", String.valueOf(isAuto));
            if (status == 0 || status == 1) {
                jsonObject.put("status", String.valueOf(status));
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        trackApi.trackEvent(trackApi.getTrackValue().getAppViewScreen(), jsonObject);
    }
}
