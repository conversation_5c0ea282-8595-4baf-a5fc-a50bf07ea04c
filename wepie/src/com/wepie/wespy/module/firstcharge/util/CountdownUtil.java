package com.wepie.wespy.module.firstcharge.util;

public class CountdownUtil {
    public static String getHour(long time) {
        long result = time  / 3600000;
        return result >= 10 ? String.valueOf(result) : "0" + result;
    }

    public static String getMinute(long time) {
        long result = time % 3600000 / 60000;
        return result >= 10 ? String.valueOf(result) : "0" + result;
    }

    public static String getSecond(long time) {
        long result = time % 60000 / 1000;
        return result >= 10 ? String.valueOf(result) : "0" + result;
    }
}
