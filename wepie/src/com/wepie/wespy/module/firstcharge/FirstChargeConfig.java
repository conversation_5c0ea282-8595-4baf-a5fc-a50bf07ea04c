package com.wepie.wespy.module.firstcharge;

import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.lib.api.plugins.track.config.os.TrackString;

public class FirstChargeConfig {
    public static int DEFAULT = 0;//默认为第一个展示
    public static int FIRST_WIN = 1;
    public static int SIX_WIN = 2;
    public static int TEN_WIN = 3;
    public static int CARE_ONE = 4;
    public static int CARE_TWO = 5;
    public static int QIXI_ONE = 6;
    public static int QIXI_TWO = 7;

    public static String TRACK_SOURCE_AUTO_OPEN = TrackSource.FIRST_CHARGE_CONFIG_AUTO_OPEN;
    public static String TRACK_SOURCE_CLICK_OPEN = TrackSource.FIRST_CHARGE_CONFIG_CLICK_OPEN;

    public static final String SHOW_SCENE_MAIN = TrackString.SHOW_SCENE_MAIN;
    public static final String SHOW_SCENE_DEEP_LINK = TrackString.SHOW_SCENE_DEEP_LINK;
}
