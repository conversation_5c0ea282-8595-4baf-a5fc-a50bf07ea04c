package com.wepie.wespy.module.accessibilityservice;

import android.accessibilityservice.AccessibilityServiceInfo;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.text.TextUtils;
import android.view.accessibility.AccessibilityManager;

import com.huiwan.base.util.AUtil;
import com.huiwan.base.util.JsonUtil;
import com.three.http.callback.DataCallback;
import com.three.http.callback.Result;
import com.wepie.wespy.net.http.api.AccessibilityServiceApi;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class AccessibilityServiceUtils {

    private static String getEnableAccessibilityServiceList(Context context) {
        AccessibilityManager accessibilityManager = (AccessibilityManager) context.getSystemService(Context.ACCESSIBILITY_SERVICE);
        if (accessibilityManager == null) {
            return "";
        }
        List<AccessibilityServiceInfo> infoList = accessibilityManager.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.DEFAULT);
        if (infoList == null || infoList.size() == 0) {
            return "";
        }
        List<PackageInfo> packageInfoList = new ArrayList<>();
        for (AccessibilityServiceInfo info : infoList) {
            if (info == null || info.getResolveInfo() == null || info.getResolveInfo().serviceInfo == null
                    || info.getResolveInfo().serviceInfo.applicationInfo == null || info.getResolveInfo().serviceInfo.applicationInfo.nonLocalizedLabel == null) continue;
            PackageInfo packageInfo = new PackageInfo();
            packageInfo.description = info.getDescription();
            ApplicationInfo applicationInfo = info.getResolveInfo().serviceInfo.applicationInfo;
            packageInfo.pkg = applicationInfo.packageName;
            packageInfo.name = applicationInfo.nonLocalizedLabel.toString();
            packageInfoList.add(packageInfo);
        }
        if (packageInfoList.size() <= 0) {
            return "";
        } else {
            return JsonUtil.toJson(packageInfoList);
        }
    }

    public static void sendGameCheckout(Context context, int game_type, int model, int level) {
        String list = AccessibilityServiceUtils.getEnableAccessibilityServiceList(context);
        if (TextUtils.isEmpty(list)) {
            return;
        }
        try {
            JSONObject object = new JSONObject();
            object.put("game_type", game_type);
            object.put("mode", model);
            object.put("bet_level", level);
            JSONObject object1 = new JSONObject();
            object1.put("source", "game");
            object1.put("list", list);
            object1.put("payload", object.toString());
            AccessibilityServiceApi.checkout(AUtil.aInTextDx9(object1.toString()), new DataCallback<Object>() {
                @Override
                public void onSuccess(Result<Object> result) {
                }

                @Override
                public void onFail(int code, String msg) {
                }
            });
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public static void sendRainCheckout(Context context, int rid) {
        String list = AccessibilityServiceUtils.getEnableAccessibilityServiceList(context);
        if (TextUtils.isEmpty(list)) {
            return;
        }
        try {
            JSONObject object = new JSONObject();
            object.put("rid", rid);
            JSONObject object1 = new JSONObject();
            object1.put("source", "rp_rain");
            object1.put("list", list);
            object1.put("payload", object.toString());
            AccessibilityServiceApi.checkout(AUtil.aInTextDx9(object1.toString()), new DataCallback<Object>() {
                @Override
                public void onSuccess(Result<Object> result) {
                }

                @Override
                public void onFail(int code, String msg) {
                }
            });
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
