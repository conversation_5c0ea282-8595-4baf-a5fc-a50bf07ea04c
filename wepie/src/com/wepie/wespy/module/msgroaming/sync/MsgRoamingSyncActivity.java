package com.wepie.wespy.module.msgroaming.sync;

import android.os.Bundle;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.constants.IntentConfig;
import com.huiwan.module.webview.WebActivity;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.bgmusic.BgMusicManager;
import com.wepie.wespy.helper.bgmusic.MusicSource;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.module.chat.invitegame.GameInviteHelper;
import com.wepie.wespy.module.chat.invitegame.GameInviteWindow;
import com.wepie.wespy.module.chat.ui.single.ChatAdapterNew;
import com.wepie.wespy.module.game.game.activity.PullListView;
import com.wepie.wespy.module.msgroaming.MsgRoamingCloseRoamingView;
import com.wepie.wespy.module.msgroaming.MsgRoamingUtil;

import java.util.ArrayList;

/**
 * date 2020/8/4
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class MsgRoamingSyncActivity extends BaseActivity implements MsgRoamingSyncView, GameInviteWindow {
    private ChatAdapterNew adapter;
    private SmartRefreshLayout srl;
    private PullListView pullListView;
    private MsgRoamingSyncPresenter presenter;
    private GameInviteHelper helper = new GameInviteHelper();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.msg_roaming_sync_activity);
        presenter = new MsgRoamingSyncPresenter(this);
        initViews();
        initData();
        pointEvent();
    }

    @Override
    protected void onPause() {
        super.onPause();
        BgMusicManager.getInstance().stop(MusicSource.SOURCE_CHAT_AUDIO);
    }

    private void pointEvent() {
        ShenceEvent.appViewScreen(TrackScreenName.CHAT_ROAMING_PAGE);
    }

    private void initData() {
        int uid = getIntent().getIntExtra(IntentConfig.UID, 0);
        if (uid > 0) {
            presenter.init(uid);
            presenter.sync();
            helper.setChatUid(uid);
            helper.attachActivity(this);
        } else {
            ToastUtil.show(R.string.user_info_error_re_try_tip);
        }
    }

    private void initViews() {
        BaseWpActionBar actionBar = findViewById(R.id.action_bar);
        actionBar.addTitleAndBack(getString(R.string.chat_log_roaming));
        actionBar.refreshRightText(R.string.msg_roaming_close, ContextCompat.getColor(this, R.color.color_accent),
                true, v->checkCloseRoaming());
        findViewById(R.id.tip_tv).setOnClickListener(v->gotoHelp());
        srl = findViewById(R.id.srl);
        pullListView = findViewById(R.id.pull_list_view);
        adapter = new ChatAdapterNew(this, pullListView, null);
        adapter.setDisableClick(true);
        pullListView.setLayoutManager(new LinearLayoutManager(this));
        pullListView.setAdapter(adapter);
        srl.setOnRefreshListener(s -> presenter.pullMsg());
        srl.setOnLoadMoreListener(s -> presenter.loadMore());
        srl.setEnableAutoLoadMore(false);
    }

    private void gotoHelp() {
        WebActivity.go(this, ConfigHelper.getInstance().getConstV3Info().chatMsgRoaming.helpUrl);
    }

    private void checkCloseRoaming() {
        MsgRoamingCloseRoamingView.showDialog(this, close ->{
            if (close) {
                MsgRoamingUtil.showChangeSwitchDialog(this, true, b -> {
                    if (!b) {
                        finish();
                    }
                });
            }
        });
    }

    @Override
    public void updateMsgList(ArrayList<WPMessage> list) {
        adapter.updateMsgList(list);
    }

    @Override
    public void hideProgressDialog() {
        super.hideProgressDialog();
        srl.finishRefresh(0);
        srl.finishLoadMore(0);
    }

    @Override
    public void scrollToPosition(int position, boolean anim) {
        if (anim) {
            pullListView.smoothScrollToPosition(position);
        } else {
            pullListView.scrollToPosition(position);
        }
    }

    @Override
    public GameInviteHelper provideInviteHelper() {
        return helper;
    }
}
