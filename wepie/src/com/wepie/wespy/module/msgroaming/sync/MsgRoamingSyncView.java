package com.wepie.wespy.module.msgroaming.sync;

import com.wepie.wespy.helper.dialog.progress.IProgressDialog;
import com.wepie.wespy.model.entity.WPMessage;

import java.util.ArrayList;

/**
 * date 2020/8/4
 * email <EMAIL>
 *
 * <AUTHOR>
 */
interface MsgRoamingSyncView extends IProgressDialog {
    void updateMsgList(ArrayList<WPMessage> list);
    void scrollToPosition(int position, boolean anim);
}
