package com.wepie.wespy.module.msgroaming.sync;

import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.module.chat.manager.ChatManager;
import com.wepie.wespy.module.msgroaming.MsgRoamingUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.base.util.ToastUtil;

import java.util.ArrayList;

/**
 * date 2020/8/4
 * email <EMAIL>
 *
 * <AUTHOR>
 */
class MsgRoamingSyncPresenter {
    private MsgRoamingSyncView syncView;
    private int uid;
    private boolean localEnd = false;
    private ArrayList<WPMessage> msgList = new ArrayList<>();


    MsgRoamingSyncPresenter(MsgRoamingSyncView syncView) {
        this.syncView = syncView;
    }

    void init(int uid) {
        this.uid = uid;
    }

    void sync() {
        pullMsgInternal(true);
    }

    void pullMsg() {
        pullMsgInternal(false);
    }

    private void pullMsgInternal(boolean initSync) {
        long time = TimeUtil.getServerTime();
        if (!msgList.isEmpty()) {
            time = msgList.get(0).getTime();
        }
        final long reqTime = time;
        syncView.showProgressDialogDelay();
        if (localEnd) {
            pullFromServer(reqTime, initSync);
        } else {
            MsgRoamingUtil.loadLocalSyncMsg(uid, time, list -> {
                if (list.isEmpty()) {
                    localEnd = true;
                    pullFromServer(reqTime, initSync);
                } else {
                    msgList.addAll(0, list);
                    updateMsg();
                    syncView.hideProgressDialog();
                    if (initSync) {
                        syncView.scrollToPosition(msgList.size()-1, false);
                    } else {
                        syncView.scrollToPosition(list.size(), false);
                    }
                }
            });
        }
    }

    void loadMore() {
        if (msgList.isEmpty()) {
            pullMsg();
        } else {
            WPMessage msg = msgList.get(msgList.size()-1);
            loadMoreFromServer(msg.getTime());
        }
    }

    private void pullFromServer(long reqTime, boolean initSync) {
        MsgRoamingUtil.syncUserMsgFromServer(uid, reqTime, true, list -> {
            syncView.hideProgressDialog();
            if (!list.isEmpty()) {
                this.msgList.addAll(0, list);
                updateMsg();
                syncView.scrollToPosition(list.size(), false);
            } else if (!initSync){
                ToastUtil.show(R.string.msg_roaming_sync_no_more);
            }
        });
    }

    private void loadMoreFromServer(long reqTime) {
        MsgRoamingUtil.syncUserMsgFromServer(uid, reqTime, false, list -> {
            syncView.hideProgressDialog();
            if (!list.isEmpty()) {
                this.msgList.addAll(list);
                updateMsg();
            } else {
                ToastUtil.show(R.string.msg_roaming_sync_no_more);
            }
        });
    }

    private void updateMsg() {
        if (ChatManager.getChatUid() == uid) {
            ChatManager.getInstance().setMessageAllLoaded(false);
        }
        syncView.updateMsgList(msgList);
    }

}
