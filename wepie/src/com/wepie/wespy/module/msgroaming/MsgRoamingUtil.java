package com.wepie.wespy.module.msgroaming;


import android.app.Activity;
import android.content.Context;

import androidx.annotation.NonNull;

import com.google.android.gms.common.util.CollectionUtils;
import com.huiwan.base.lifecycle.IReference;
import com.huiwan.base.lifecycle.IReferenceKt;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.collection.ListWrapper;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.store.database.WPStore;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.GlobalLife;
import com.wejoy.weplay.ex.ILife;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.interfaces.ISingCallback;
import com.wepie.wespy.helper.redDotHelper.RedDotUtil;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.RedDotInfo;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.model.entity.WPMessageSync;
import com.wepie.wespy.model.entity.msgroaming.MsgRoamingMsg;
import com.wepie.wespy.model.entity.msgroaming.MsgRoamingRecentChat;
import com.wepie.wespy.model.entity.msgroaming.MsgRoamingSwitch;
import com.wepie.wespy.module.chat.conversation.ConversationManager;
import com.wepie.wespy.module.chat.model.ChatAudio;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.pwd.PwdActivity;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.http.api.MsgRoamingApi;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * date 2020/7/31
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class MsgRoamingUtil {
    private static int page = 1;

    public static void checkSwitchStatus(ILife life, @NonNull ISingCallback<Integer> callback) {
        IReference<ISingCallback<Integer>> ref = IReferenceKt.wrap(life, callback);
        MsgRoamingApi.getSwitch(new LifeDataCallback<>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<MsgRoamingSwitch> result) {
                PrefUserUtil.getInstance().setInt(PrefUserUtil.MSG_ROAMING_SWITCH, result.data.state);
                IReferenceKt.run(ref, cb -> cb.onCallback(result.data.state));
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
                IReferenceKt.run(ref, cb -> cb.onCallback(MsgRoamingSwitch.UNDEFINED));
            }
        });
    }

    public static void jumpMsgRoamingMain(Context context) {
        MsgRoamingUtil.checkSwitchStatus(ContextUtil.getLife(context), status -> {
            RedDotUtil.get().reportRead(RedDotInfo.RED_DOT_VIP_MSG_ROAMING);
            if (status == MsgRoamingSwitch.NOT_SET) {
                gotoSetPwd(context);
            } else if (status == MsgRoamingSwitch.OFF || status == MsgRoamingSwitch.ON) {
                gotoRoamingMain(context, status);
            }
        });
    }


    private static void gotoSetPwd(Context context) {
        Activity activity = ContextUtil.getActivityFromContext(context);
        if (activity == null) {
            return;
        }
        JumpUtil.gotoPwdActivity(activity, PwdActivity.TYPE_MSG_ROAMING, PwdActivity.OP_PWD_SET, "", "");
    }

    private static void gotoRoamingMain(Context context, int status) {
        JumpUtil.gotoMsgRoamingMainActivity(context, status);
    }

    public static void checkDeleteMsg(int uid, WPMessage message) {
        delLocalSyncMsg(message);
        MsgRoamingApi.delMsg(uid, message.getMid(), new LifeDataCallback<>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<Object> result) {
                TimeLogger.msgNoStack("delete msg success");
            }

            @Override
            public void onFail(int code, String msg) {
                TimeLogger.msgNoStack("error delete msg: " + msg);
            }
        });
    }

    public static void showCheckPwdDialog(Context context, ISingCallback<Boolean> callback) {
        MsgRoamingChangeSwitchPwdDialogView.showDialog(context, MsgRoamingChangeSwitchPwdDialogView.TYPE_CHECK_PWD, false, callback);
    }

    public static void showChangeSwitchDialog(Context context, boolean curSwitchOn, ISingCallback<Boolean> callback) {
        MsgRoamingChangeSwitchPwdDialogView.showDialog(context, MsgRoamingChangeSwitchPwdDialogView.TYPE_UPDATE_SWITCH, curSwitchOn, callback);
    }

    public static void onLogin() {
        PrefUserUtil.getInstance().setBoolean(PrefUserUtil.MSG_ROAMING_USER_SETTING_INPUT, false);
        syncContact();
    }

    private static void syncContact() {
        page = 1;
        syncContactPage();
    }

    private static void syncContactPage() {
        MsgRoamingApi.getRecentChatUser(getPage(), new LifeDataCallback<>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<MsgRoamingRecentChat> result) {
                MsgRoamingRecentChat data = result.data;
                if (data != null && !data.contactList.isEmpty()) {
                    checkSaveLocalContacts(data);
                    pageAdd();
                    syncContactPage();
                }
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(ResUtil.getStr(R.string.contact_sync_failed_cause_x, msg));
            }
        });
    }

    private static int getPage() {
        return page;
    }

    private static void pageAdd() {
        page++;
    }

    public static void syncUserMsgFromServer(int uid, long time, boolean forward, ISingCallback<List<WPMessage>> callback) {
        MsgRoamingApi.syncMsg(uid, time, forward, new LifeDataCallback<>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<MsgRoamingMsg> result) {
                if (result.data == null || result.data.msgs == null) {
                    ToastUtil.show(R.string.server_data_err);
                    return;
                }
                checkSaveLocalAndTransform(result.data, callback);
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
                if (callback != null) {
                    callback.onCallback(new ArrayList<>());
                }
            }
        });
    }

    private static void checkSaveLocalAndTransform(MsgRoamingMsg msg, ISingCallback<List<WPMessage>> callback) {
        for (WPMessageSync wpMsg : msg.msgs) {
            wpMsg.setRead();
        }
        WPStore.saveListAsyncConflictIgnore(msg.msgs);
        if (callback != null) {
            List<WPMessage> curMsgList = new ListWrapper<>(msg.msgs).map(WPMessageSync::toWpMessage);
            for (WPMessage wpMsg : curMsgList) {
                setMsgRead(wpMsg);
            }
            Collections.sort(curMsgList, (o1, o2) -> Long.compare(o1.getTime(), o2.getTime()));
            Runnable runnable = () -> {
                if (!curMsgList.isEmpty()) {
                    WPMessage lastMsg = curMsgList.get(curMsgList.size() - 1);
                    ConversationManager.getInstance().onSyncSingleMsg(lastMsg, -1);
                }
                callback.onCallback(curMsgList);
                EventDispatcher.postSingleChatRefreshList();
            };
            if (curMsgList.isEmpty()) {
                ThreadUtil.runOnUiThread(runnable);
            } else {
                WPStore.saveListAsyncConflictIgnore(curMsgList, success -> runnable.run());
            }
        }
    }

    private static void checkSaveLocalContacts(MsgRoamingRecentChat chatData) {
        WPStore.transactionAsync(new WPMessage(), transaction -> {
            for (MsgRoamingRecentChat.Contact contact : chatData.contactList) {
                List<WPMessage> curMsgList = new ListWrapper<>(contact.msgList).map(WPMessageSync::toWpMessage);
                for (WPMessage msg : curMsgList) {
                    setMsgRead(msg);
                }
                Collections.sort(curMsgList, (o1, o2) -> Long.compare(o1.getTime(), o2.getTime()));
                transaction.replaceList(curMsgList);
                ThreadUtil.runOnUiThread(() -> {
                    if (!curMsgList.isEmpty()) {
                        WPMessage lastMsg = curMsgList.get(curMsgList.size() - 1);
                        ConversationManager.getInstance().onSyncSingleMsg(lastMsg, contact.dnd);
                    } else {
                        ConversationManager.getInstance().onEmptyPersonSyncConversation(contact.uid, contact.dnd);
                    }
                    EventDispatcher.postSingleChatRefreshList();
                });
            }
        });
    }

    private static void setMsgRead(WPMessage msg) {
        msg.setStatus(getStatus(msg));
        if (msg.getMedia_type() == ChatMsg.MEDIA_TYPE_AUDIO) {
            final ChatAudio chatAudio = ChatAudio.fromString(msg.getContent());
            chatAudio.setRemain(0);
            msg.setContent(chatAudio.toAdepterString());
        }
    }

    public static int getStatus(WPMessage message) {
        int status = WPMessage.STATUS_VIEWED;
        try {
            if (message.isVideoMsg()) {
                JSONObject jsonObject = new JSONObject(message.getExtension());
                status = jsonObject.optInt(WPMessage.STATUS, WPMessage.STATUS_VIEWED);
            }
        } catch (Exception e) {

        }
        return status;
    }

    private static void delLocalSyncMsg(WPMessage message) {
        WPStore.removeAsync(new WPMessageSync(message));
    }

    public static void loadLocalSyncMsg(int peer, long lastTime, ISingCallback<List<WPMessage>> callback) {
        WPMessageSync sync = new WPMessageSync();
        String sql1 = "select * from " + sync.getTableName() + " where "
                + "time < " + lastTime + " and peer = " + peer + " order by time desc limit 200";
        WPStore.fetchListAsync(sync, sql1, (list, e) -> {
            List<WPMessage> msgList;
            if (CollectionUtils.isEmpty(list)) {
                msgList = new ArrayList<>();
            } else {
                msgList = new ArrayList<>(list.size());
                for (WPMessageSync msgSync : list) {
                    msgSync.setRead();
                    msgList.add(msgSync.toWpMessage());
                }
                Collections.sort(msgList, (o1, o2) -> Long.compare(o1.getTime(), o2.getTime()));
                WPStore.saveListAsyncConflictIgnore(msgList);
            }

            ThreadUtil.runOnUiThread(() -> {
                if (!msgList.isEmpty()) {
                    ConversationManager.getInstance().onNewOldSingleMsg(msgList.get(0), 0, null);
                }
                callback.onCallback(msgList);
            });
        });
    }

}
