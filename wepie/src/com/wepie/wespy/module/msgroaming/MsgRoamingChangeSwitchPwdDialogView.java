package com.wepie.wespy.module.msgroaming;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.wespy.R;
import com.huiwan.constants.IntentConfig;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.interfaces.ISingCallback;
import com.wepie.wespy.model.entity.msgroaming.MsgRoamingPwdCheck;
import com.wepie.wespy.model.entity.msgroaming.MsgRoamingSwitch;
import com.wepie.wespy.module.common.SimpleTextWatcher;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.pwd.PwdFourInputView;
import com.wepie.wespy.net.http.api.MsgRoamingApi;
import com.huiwan.base.util.ToastUtil;

/**
 * date 2020/8/3
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class MsgRoamingChangeSwitchPwdDialogView extends FrameLayout {
    static final int TYPE_CHECK_PWD = 1;
    static final int TYPE_UPDATE_SWITCH = 2;

    private ImageView closeIv;
    private PwdFourInputView pwdView;
    private TextView errTip;
    private Callback callback;

    private int type;
    private boolean curSwitchOn = false;

    public MsgRoamingChangeSwitchPwdDialogView(@NonNull Context context) {
        this(context, null);
    }

    public MsgRoamingChangeSwitchPwdDialogView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        LayoutInflater.from(context).inflate(R.layout.msg_roaming_close_roaming, this);
        initViews();
    }

    private void initViews() {
        closeIv = findViewById(R.id.close_iv);
        pwdView = findViewById(R.id.pwd_input_view);
        errTip = findViewById(R.id.pwd_err_tip);
        findViewById(R.id.ok_btn).setOnClickListener(v-> clickOkBtn());
        findViewById(R.id.forget_pwd_tv).setOnClickListener(v->goForget());
        errTip.setVisibility(INVISIBLE);
        pwdView.addTextChangedListener(new SimpleTextWatcher() {
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                findViewById(R.id.ok_btn).setEnabled(s.length() == 4);
                if (s.length() < 4) {
                    errTip.setVisibility(INVISIBLE);
                }
            }
        });
        closeIv.setOnClickListener(v -> {
            if (callback != null) callback.onClose();
        });
    }

    private void updateType(int type) {
        this.type = type;
        if (type == TYPE_CHECK_PWD) {
            ((TextView)findViewById(R.id.ok_btn)).setText(R.string.ok);
        } else if (type == TYPE_UPDATE_SWITCH) {
            ((TextView)findViewById(R.id.ok_btn)).setText(curSwitchOn ? R.string.msg_roaming_close : R.string.msg_roaming_open);
        }
    }

    private void clickOkBtn() {
        if (type == TYPE_CHECK_PWD) {
            checkPwd();
        } else if (type == TYPE_UPDATE_SWITCH) {
            reqChange();
        }
    }

    private void checkPwd() {
        MsgRoamingApi.verifyPwd(pwdView.getInputString(), new LifeDataCallback<>(this) {
            @Override
            public void onSuccess(Result<MsgRoamingPwdCheck> result) {
                if (result.data.ok) {
                    if (callback != null) {
                        callback.onCallback(true);
                    }
                } else {
                    errTip.setVisibility(VISIBLE);
                    ToastUtil.show(R.string.msg_roaming_pwd_error);
                }
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    private void reqChange() {
        int newStatus = curSwitchOn ? MsgRoamingSwitch.OFF : MsgRoamingSwitch.ON;
        final boolean reqSwitchOn = !curSwitchOn;
        MsgRoamingApi.verifyPwd(pwdView.getInputString(), new LifeDataCallback<>(this) {
            @Override
            public void onSuccess(Result<MsgRoamingPwdCheck> result) {
                if (result.data.ok) {
                    errTip.setVisibility(INVISIBLE);
                    MsgRoamingApi.setSwitch(pwdView.getInputString(), newStatus,
                            new LifeDataCallback<>(MsgRoamingChangeSwitchPwdDialogView.this) {
                                @Override
                                public void onSuccess(Result<Object> result) {
                                    if (callback != null) {
                                        callback.onCallback(reqSwitchOn);
                                    }
                                    if (reqSwitchOn) {
                                        ToastUtil.show(R.string.msg_roaming_opened);
                                    }
                                }

                                @Override
                                public void onFail(int code, String msg) {
                                    ToastUtil.show(msg);
                                }
                            });
                } else {
                    errTip.setVisibility(VISIBLE);
                    ToastUtil.show(R.string.msg_roaming_pwd_error);
                }
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    private void goForget() {
        JumpUtil.gotoPwdForgetActivity(getContext(), IntentConfig.TYPE_ROAMING_MSG);
    }

    static void showDialog(Context context, int type, boolean curSwitchOn, ISingCallback<Boolean> callback) {
        MsgRoamingChangeSwitchPwdDialogView view = new MsgRoamingChangeSwitchPwdDialogView(context);
        view.curSwitchOn = curSwitchOn;
        view.updateType(type);
        BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(view);
        dialog.init303();

        ISingCallback<Boolean> wrapperCallback = new ISingCallback<Boolean>() {
            private boolean invoked = false;
            @Override
            public void onCallback(Boolean aBoolean) {
                if (invoked) return;
                invoked = true;
                if (callback != null) callback.onCallback(aBoolean);
            }
        } ;

        dialog.setOnDismissListener(d -> wrapperCallback.onCallback(curSwitchOn));
        view.callback = new Callback() {
            @Override
            public void onCallback(Boolean b) {
                wrapperCallback.onCallback(b);
                dialog.dismiss();
            }

            @Override
            public void onClose() {
                dialog.dismiss();
            }
        };
        dialog.show();
    }

    public interface Callback {
        void onCallback(Boolean b);
        void onClose();
    }
}
