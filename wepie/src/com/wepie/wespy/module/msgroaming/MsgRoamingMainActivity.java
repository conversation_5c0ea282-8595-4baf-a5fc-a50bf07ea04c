package com.wepie.wespy.module.msgroaming;

import android.os.Bundle;
import android.view.View;

import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.R;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.IntentConfig;
import com.huiwan.configservice.ConfigHelper;
import com.wepie.wespy.helper.view.SwitchView;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.wespy.model.entity.msgroaming.MsgRoamingSwitch;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.pwd.PwdActivity;
import com.huiwan.module.webview.WebActivity;

/**
 * date 2020/8/3
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class MsgRoamingMainActivity extends BaseActivity {
    private SwitchView switchView;

    private int status;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.msg_roaming_main_activity);
        status = getIntent().getIntExtra(IntentConfig.STATUS, 0);
        initViews();
        updateViews();
    }

    private void initViews() {
        switchView = findViewById(R.id.roaming_switch);
        BaseWpActionBar actionBar = findViewById(R.id.action_bar);
        actionBar.addTitleRightIconWithBack(ResUtil.getStr(R.string.msg_roaming_pwd_title), R.drawable.ic_help_msg_roaming, v->onBackPressed(), this::clickShowHelp);
        findViewById(R.id.change_pwd_lay).setOnClickListener(this::clickChangePwd);
        switchView.setOnClickListener(this::clickSwitch);
    }

    private void updateViews() {
        if (status == MsgRoamingSwitch.ON) {
            switchView.setDefaultState(SwitchView.ON);
        } else {
            switchView.setDefaultState(SwitchView.OFF);
        }
    }

    private void clickChangePwd(View v) {
        JumpUtil.gotoPwdActivity(this, PwdActivity.TYPE_MSG_ROAMING,
                PwdActivity.OP_PWD_MODIFY, "", "");
    }

    private void clickSwitch(View v) {
        switchView.changeState();
        if (status == MsgRoamingSwitch.ON) {
            MsgRoamingCloseRoamingView.showDialog(this, close ->{
                if (close) {
                    MsgRoamingUtil.showChangeSwitchDialog(this, true, this::changeSwitch);
                } else {
                    changeSwitch(true);
                }
            });
        } else {
            MsgRoamingUtil.showChangeSwitchDialog(this, false, this::changeSwitch);
        }
    }

    private void changeSwitch(boolean switchOn) {
        if (switchOn) {
            status = MsgRoamingSwitch.ON;
            switchView.showAnim(SwitchView.ON);
        } else {
            status = SwitchView.OFF;
            switchView.showAnim(SwitchView.OFF);
        }
    }

    private void clickShowHelp(View view) {
        WebActivity.go(this, ConfigHelper.getInstance().getConstV3Info().chatMsgRoaming.helpUrl);
    }

}
