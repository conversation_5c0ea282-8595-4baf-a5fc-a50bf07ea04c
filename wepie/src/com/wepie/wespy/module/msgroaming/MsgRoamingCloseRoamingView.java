package com.wepie.wespy.module.msgroaming;

import android.content.Context;
import android.text.Html;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.interfaces.ISingCallback;

/**
 * date 2020/8/3
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class MsgRoamingCloseRoamingView extends FrameLayout {
    private ISingCallback<Boolean> callback;
    public MsgRoamingCloseRoamingView(@NonNull Context context) {
        this(context, null);
    }

    public MsgRoamingCloseRoamingView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        LayoutInflater.from(context).inflate(R.layout.msg_roaming_switch_pwd_dialog_view, this);
        String s = getResources().getString(R.string.msg_roaming_close_switch_check_tip);
        ((TextView)findViewById(R.id.tip_tv)).setText(Html.fromHtml(s));
        findViewById(R.id.ok_btn).setOnClickListener(v -> {
            if (callback != null) {
                callback.onCallback(true);
            }
        });
        findViewById(R.id.cancel_btn).setOnClickListener(v -> {
            if (callback != null) {
                callback.onCallback(false);
            }
        });
    }

    public static void showDialog(Context context, ISingCallback<Boolean> callback) {
        MsgRoamingCloseRoamingView view = new MsgRoamingCloseRoamingView(context);
        view.callback = callback;
        BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        dialog.setContentView(view);
        dialog.initCenterDialog();
        view.callback = b -> {
            dialog.dismiss();
            if (callback != null) {
                callback.onCallback(b);
            }
        };
        dialog.setOnDismissListener( d -> {
            callback.onCallback(false);
        });
        dialog.show();
    }
}
