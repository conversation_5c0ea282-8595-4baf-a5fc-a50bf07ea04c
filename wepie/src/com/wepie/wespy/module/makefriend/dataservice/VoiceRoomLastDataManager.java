package com.wepie.wespy.module.makefriend.dataservice;

import com.huiwan.constants.GameType;
import com.huiwan.store.PrefUserUtil;
import com.wepie.wespy.model.entity.RoomInfo;

// Created by <PERSON>wen on 2018/7/4.
public class VoiceRoomLastDataManager {
    private static final VoiceRoomLastDataManager ourInstance = new VoiceRoomLastDataManager();

    public static VoiceRoomLastDataManager getInstance() {
        return ourInstance;
    }

    private VoiceRoomLastDataManager() {
    }

    public void setVoiceRoomName(String name, int gameType) {
        if (isSupportGameType(gameType)) {
            PrefUserUtil.getInstance().setString(PrefUserUtil.VOICE_ROOM_NAME, name);
        }
    }

    public String getVoiceRoomName() {
        return PrefUserUtil.getInstance().getString(PrefUserUtil.VOICE_ROOM_NAME, "");
    }

    public void setVoiceRoomNote(String note, int gameType) {
        if (isSupportGameType(gameType)) {
            PrefUserUtil.getInstance().setString(PrefUserUtil.VOICE_ROOM_NOTE, note);
        }
    }

    public String getVoiceRoomNote() {
        return PrefUserUtil.getInstance().getString(PrefUserUtil.VOICE_ROOM_NOTE, "");
    }

    public void setVoiceRoomBg(int bgId, int gameType) {
        if (isSupportGameType(gameType)) {
            PrefUserUtil.getInstance().setInt(PrefUserUtil.VOICE_ROOM_BG, bgId);
        }
    }

    public int getVoiceRoomBg() {
        return PrefUserUtil.getInstance().getInt(PrefUserUtil.VOICE_ROOM_BG, 0);
    }

    public void setVoiceRoomLabel(int label, int gameType) {
        if (isSupportGameType(gameType)) {
            if (gameType == RoomInfo.GAME_TYPE_VOICE_ROOM) {//兼容老配置
                PrefUserUtil.getInstance().setInt(PrefUserUtil.VOICE_ROOM_LABEL, label);
            } else {
                PrefUserUtil.getInstance().setInt(PrefUserUtil.VOICE_ROOM_LABEL + "_gameType_" + gameType, label);
            }
        }
    }

    public int getVoiceRoomLabel(int gameType) {
        if (gameType == RoomInfo.GAME_TYPE_VOICE_ROOM) {
            return PrefUserUtil.getInstance().getInt(PrefUserUtil.VOICE_ROOM_LABEL, 0);
        } else {
            return PrefUserUtil.getInstance().getInt(PrefUserUtil.VOICE_ROOM_LABEL + "_gameType_" + gameType, 0);
        }
    }

    // 用于显示创建房间时的房间类型
    public int getVoiceRoomGameType() {
        return PrefUserUtil.getInstance().getInt(PrefUserUtil.VOICE_ROOM_GAME_TYPE, GameType.GAME_TYPE_VOICE_ROOM);
    }

    public void setVoiceRoomGameType(int gameType) {
        if (isSupportGameType(gameType)) {
            PrefUserUtil.getInstance().setInt(PrefUserUtil.VOICE_ROOM_GAME_TYPE, gameType);
        }
    }

    public static boolean isSupportGameType(int gameType) {
        return gameType == GameType.GAME_TYPE_VOICE_ROOM || gameType == GameType.GAME_TYPE_CP_ROOM || gameType == GameType.GAME_TYPE_AUCTION
                || GameType.isVideoRoom(gameType);
    }
}
