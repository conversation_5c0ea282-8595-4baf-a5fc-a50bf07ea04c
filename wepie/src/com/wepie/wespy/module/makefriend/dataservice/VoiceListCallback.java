package com.wepie.wespy.module.makefriend.dataservice;

import androidx.annotation.NonNull;

import com.wejoy.weplay.ex.ILifeOwner;
import com.wejoy.weplay.ex.ILifeOwner;
import com.wepie.wespy.model.entity.makefriend.VoiceRoomListInfo;

import java.util.List;

// Created by bigwen on 2018/7/4.
public interface VoiceListCallback extends ILifeOwner {
    void onSuccess(@NonNull List<VoiceRoomListInfo> roomListInfos, int label, String roomListId);

    void onFail(String msg);
}
