package com.wepie.wespy.module.makefriend.dataservice;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.protobuf.GeneratedMessageLite;
import com.huiwan.base.lifecycle.IReference;
import com.huiwan.base.lifecycle.IReferenceKt;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.model.voiceroom.VoiceLabelInfo;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.callback.LifeSeqCallbackProxy;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LoginHelper;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeOwnerKt;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.base.appfyers.AppsFlyerEvent;
import com.wepie.wespy.base.appfyers.AppsFlyerUtil;
import com.wepie.wespy.model.entity.makefriend.VoiceRoomListInfo;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class VoiceRoomListService {

    private static final String TAG = "VoiceRoomListService";
    private boolean needPwd;
    private final HashMap<String, List<VoiceRoomListInfo>> voiceRoomMap = new HashMap<>();
    private final HashMap<String, Long> followTimeMap = new HashMap<>();
    //本地记录语音房关注/joined列表
    private final HashMap<String, ArrayList<Integer>> voiceIdMap = new HashMap<>();
    //关注列表每页分页数量
    private static final int FOLLOW_PAGE_OFFSET = 20;

    public static VoiceRoomListService getInstance() {
        return VoiceRoomListService.SingleHolder.INSTANCE;
    }

    public List<VoiceRoomListInfo> getVoiceRoomListInfos(int label) {
        if (hasKey(label)) {
            return voiceRoomMap.get(String.valueOf(label));
        } else {
            return new ArrayList<>();
        }
    }

    private boolean hasKey(int label) {
        return voiceRoomMap.containsKey(String.valueOf(label));
    }

    public void addVoiceRoomList(List<VoiceRoomListInfo> voiceRoomListInfos, int label, int offset) {
        // 上拉加载，这里需要根据rid做去重
        if (offset > 0) {
            List<VoiceRoomListInfo> tempUniqueList = filterRid(voiceRoomListInfos, label);
            addVoiceList(tempUniqueList, label);
        } else {
            voiceRoomMap.remove(String.valueOf(label));//偏移=0，第一次加载，清空本地数据
            addVoiceList(voiceRoomListInfos, label);
        }
    }

    public void getVoiceRoomList(int offset, int label, String requestRoomListId, final VoiceListCallback callback) {
        getVoiceRoomList(offset, label, 0, requestRoomListId, LoginHelper.getLoginUid(), callback);
    }

    public void getVoiceRoomList(int offset, int label, int subLabel, String requestRoomListId, final VoiceListCallback callback) {
        getVoiceRoomList(offset, label, subLabel, requestRoomListId, LoginHelper.getLoginUid(), callback);
    }

    public void getVoiceRoomList(final int offset, final int label, int subLabel, final String requestRoomListId, int uid, final VoiceListCallback callback) {
        IReference<VoiceListCallback> softReference = IReferenceKt.wrap(callback);
        //如果是关注列表,需要特别处理
        if (label == VoiceLabelInfo.LABEL_FOLLOW || label == VoiceLabelInfo.LABEL_JOINED) {
            List<Integer> idList = getIdList(label);
            //如果offset == 0 说明需要从服务器获取数据
            if (offset == 0) {
                HLog.d(TAG, "getVoiceRoomListByIds offset = 0 clear idList");
                //这里先清除本地的关注/joined列表
                idList.clear();
                LifeSeqCallback seqCallback = new LifeSeqCallbackProxy() {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        //关注列表得到一组房间ID
                        GeneratedMessageLite<?, ?> msg = head.message;
                        String roomListId = "";
                        //把新的关注列表添加进去
                        if (msg instanceof TmpRoomPackets.JoinedRoomListResp) {
                            idList.addAll(((TmpRoomPackets.JoinedRoomListResp) msg).getRoomListRidsList());
                            roomListId = (((TmpRoomPackets.JoinedRoomListResp) msg)).getRoomListId();
                        } else if (msg instanceof TmpRoomPackets.VoiceRoomListRsp) {
                            idList.addAll(((TmpRoomPackets.VoiceRoomListRsp) msg).getRoomListRidsList());
                            roomListId = (((TmpRoomPackets.VoiceRoomListRsp) msg)).getRoomListId();
                        }
                        HLog.d(TAG, "getVoiceRoomListByIds offset = 0 joinedList = {}", idList.size());
                        getVoiceRoomListByIds(offset, label, roomListId, softReference);
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        IReferenceKt.run(softReference, callback -> callback.onFail(head.desc));
                    }
                };

                if ((label == VoiceLabelInfo.LABEL_JOINED)) {
                    VoiceRoomPacketSender.getJoinedVoiceRoomListByUid(uid, seqCallback);
                } else {
                    VoiceRoomPacketSender.getVoiceRoomList(offset, label, requestRoomListId, seqCallback);
                }
            } else {
                HLog.d(TAG, "getVoiceRoomListByIds offset = {} idList = {}", offset, idList.size());
                getVoiceRoomListByIds(offset, label, requestRoomListId, softReference);
            }
        } else {
            //如果不是关注列表走原来逻辑
            VoiceRoomPacketSender.getVoiceRoomList(offset, label, subLabel, requestRoomListId, new LifeSeqCallbackProxy() {
                @Override
                public void onSuccess(RspHeadInfo head) {
                    TmpRoomPackets.VoiceRoomListRsp voiceRoomListRsp = (TmpRoomPackets.VoiceRoomListRsp) head.message;
                    List<TmpRoomPackets.VoiceRoom> voiceRoomList = new ArrayList<>();
                    String roomListId = "";
                    if (voiceRoomListRsp != null) {
                        voiceRoomList = voiceRoomListRsp.getVoiceRoomListList();
                        roomListId = voiceRoomListRsp.getRoomListId();
                    }
                    successUpdateInternal(voiceRoomList, offset, label, roomListId, softReference.get());
                }

                @Override
                public void onFail(RspHeadInfo head) {
                    IReferenceKt.run(softReference, cb -> cb.onFail(head.desc));
                }
            });
        }
    }

    /**
     * 通过房间ID获取房间信息
     *
     * @param offset       获取列表起始值
     * @param label        哪个tab
     */
    private void getVoiceRoomListByIds(final int offset, final int label, final String requestRoomListId, final IReference<VoiceListCallback> reference) {
        //因为两次过滤的关系,这里可能offset跟idList不一致,处理一下异常情况
        ArrayList<Integer> idList = getIdList(label);
        if (idList.isEmpty()) {
            HLog.d(TAG, "getVoiceRoomListByIds idList size = 0, offset = {}", offset);
            successUpdateInternal(new ArrayList<>(), offset, label, requestRoomListId, reference.get());
            return;
        }
        HLog.d(TAG, "getVoiceRoomListByIds idList before size = {}, offset = {}", idList.size(), offset);
        //分批获取语音房信息 一页 按 FOLLOW_PAGE_OFFSET 20来
        //防止越界
        int range = Math.min(idList.size(), FOLLOW_PAGE_OFFSET);
        //获取本次请求房间列表
        List<Integer> roomList = new ArrayList<>();
        for (int i = 0; i < range; i++) {
            roomList.add(idList.get(i));
        }
        idList.removeAll(roomList);
        HLog.d(TAG, "getVoiceRoomListByIds idList after size = {}, offset = {}", idList.size(), offset);
        VoiceRoomPacketSender.getVoiceRoomByRidList(roomList, new LifeSeqCallbackProxy() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                TmpRoomPackets.GetRoomListByRidsRsp voiceRoomListRsp = (TmpRoomPackets.GetRoomListByRidsRsp) head.message;
                List<TmpRoomPackets.VoiceRoom> voiceRoomList = new ArrayList<>();
                if (voiceRoomListRsp != null) {
                    voiceRoomList = voiceRoomListRsp.getVoiceRoomListList();
                }
                successUpdateInternal(voiceRoomList, offset, label, requestRoomListId, reference.get());
            }

            @Override
            public void onFail(RspHeadInfo head) {
                IReferenceKt.run(reference, callback -> callback.onFail(head.desc));
            }
        });
    }

    /**
     * 更新语音房间列表
     *
     * @param rooms        房间列表
     * @param offset       获取列表起始值
     * @param label        哪个tab
     * @param requestRoomListId 是否从列表来的
     * @param callback     回调
     */
    private void successUpdateInternal(List<TmpRoomPackets.VoiceRoom> rooms, final int offset, final int label, final String requestRoomListId, final VoiceListCallback callback) {
        List<VoiceRoomListInfo> voiceRoomListInfos = VoiceRoomListInfo.parseList(rooms, requestRoomListId);
        addVoiceRoomList(voiceRoomListInfos, label, offset);
        if (callback != null) callback.onSuccess(voiceRoomListInfos, label, requestRoomListId);
    }

    public void getCountryVoiceRoomList(final int offset, final int label, String requestRoomListId, String nationId, final VoiceListCallback callback) {
        VoiceRoomPacketSender.requestCountryVoiceRoomList(offset, label, requestRoomListId, nationId, new LifeSeqCallback(ILifeOwnerKt.getLife(callback)) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                if (callback == null) {
                    return;
                }
                TmpRoomPackets.VoiceRoomListRsp voiceRoomListRsp = (TmpRoomPackets.VoiceRoomListRsp) head.message;
                List<VoiceRoomListInfo> voiceRoomListInfos = VoiceRoomListInfo.parseList(voiceRoomListRsp);
                callback.onSuccess(voiceRoomListInfos, label, voiceRoomListRsp.getRoomListId());
            }

            @Override
            public void onFail(RspHeadInfo head) {
                if (callback != null) callback.onFail(head.desc);
            }
        });
    }

    public void searchRoomInfoList(List<Integer> list, final VoiceListCallback callback) {
        VoiceRoomPacketSender.searchRoomInfoList(list, new LifeSeqCallback(ILifeOwnerKt.getLife(callback)) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                if (callback == null) {
                    return;
                }
                TmpRoomPackets.GetRoomInfoBatchRsp rsp = (TmpRoomPackets.GetRoomInfoBatchRsp) head.message;
                List<VoiceRoomListInfo> voiceRoomListInfos = VoiceRoomListInfo.parseList(rsp);
                callback.onSuccess(voiceRoomListInfos, VoiceLabelInfo.LABEL_RECENT, "");
            }

            @Override
            public void onFail(RspHeadInfo head) {
                if (callback != null) callback.onFail(head.desc);
            }
        });
    }

    public void followRoomWithRefreshFollowRoom(final int rid, final boolean isFollow, final LifeSeqCallback callback) {
        Long lastFollowTime = followTimeMap.get(String.valueOf(rid));
        if (lastFollowTime == null) {
            lastFollowTime = 0L;
        }
        if (System.currentTimeMillis() - lastFollowTime < 1000 * 60 && isFollow) {//同一房间关注间隔小于
            ToastUtil.show(ResUtil.getStr(R.string.common_operate_too_fast));
            return;
        }
        if (isFollow) {
            AppsFlyerUtil.trackFirstEvent(AppsFlyerEvent.VOICE_ROOM_FOLLOW);
        }
        IReference<LifeSeqCallback> reference = IReferenceKt.wrap(callback);
        VoiceRoomPacketSender.followRoomReq(rid, isFollow, new LifeSeqCallbackProxy() {
            @Override
            public void onSuccess(final RspHeadInfo head) {
                if (isFollow) {
                    followTimeMap.put(String.valueOf(rid), System.currentTimeMillis());
                }
                getFollowRoomList(new VoiceListCallback() {
                    @Nullable
                    @Override
                    public ILife getLife() {
                        LifeSeqCallback callback = reference.get();
                        if (callback != null) {
                            return callback.getLife();
                        }
                        return null;
                    }

                    @Override
                    public void onSuccess(@NonNull List<VoiceRoomListInfo> roomListInfos, int label, String roomListId) {
                        IReferenceKt.run(reference, callback -> callback.onSuccess(head));
                    }

                    @Override
                    public void onFail(String msg) {
                        IReferenceKt.run(reference, callback -> callback.onSuccess(head));
                    }
                });
            }

            @Override
            public void onFail(RspHeadInfo head) {
                IReferenceKt.run(reference, callback -> callback.onSuccess(head));
            }
        });
    }

    private static class SingleHolder {
        static final VoiceRoomListService INSTANCE = new VoiceRoomListService();
    }

    //rid做去重
    private List<VoiceRoomListInfo> filterRid(List<VoiceRoomListInfo> voiceRoomListInfos, int label) {
        if (!hasKey(label)) return voiceRoomListInfos;
        List<VoiceRoomListInfo> listInfos = voiceRoomMap.get(String.valueOf(label));
        Set<Integer> uidSet = new HashSet<>();
        for (VoiceRoomListInfo containInfo : listInfos) {//遍历本地已有房间
            uidSet.add(containInfo.rid);
        }
        List<VoiceRoomListInfo> tempUniqueList = new ArrayList<>();
        for (VoiceRoomListInfo addRoomInfo : voiceRoomListInfos) {
            if (uidSet.add(addRoomInfo.rid)) {
                tempUniqueList.add(addRoomInfo);
            }
        }
        return tempUniqueList;
    }

    private void addVoiceList(List<VoiceRoomListInfo> voiceRoomListInfos, int label) {
        if (hasKey(label)) {
            List<VoiceRoomListInfo> listInfos = voiceRoomMap.get(String.valueOf(label));
            listInfos.addAll(voiceRoomListInfos);
        } else {
            List<VoiceRoomListInfo> temp = new ArrayList<>(voiceRoomListInfos);
            voiceRoomMap.put(String.valueOf(label), temp);
        }
    }

    //获取关注列表
    private void getFollowRoomList(final VoiceListCallback callback) {
        getVoiceRoomList(0, VoiceLabelInfo.LABEL_FOLLOW, "", new VoiceListCallback() {

            @Nullable
            @Override
            public ILife getLife() {
                return callback.getLife();
            }

            @Override
            public void onSuccess(@NonNull List<VoiceRoomListInfo> roomListInfos, int label, String roomListId) {
                if (callback != null) callback.onSuccess(roomListInfos, label, roomListId);
            }

            @Override
            public void onFail(String msg) {
                getVoiceRoomList(0, VoiceLabelInfo.LABEL_FOLLOW, "", new VoiceListCallback() {
                    @Nullable
                    @Override
                    public ILife getLife() {
                        return callback.getLife();
                    }

                    @Override
                    public void onSuccess(@NonNull List<VoiceRoomListInfo> roomListInfos, int label, String roomListId) {
                        if (callback != null) callback.onSuccess(roomListInfos, label, roomListId);
                    }

                    @Override
                    public void onFail(String msg) {
                        if (callback != null) callback.onFail(msg);
                    }
                });
            }
        });
    }

    //重连调用，暂时去掉
    public void checkNeedGetVoiceRoomList() {
        if (!hasKey(VoiceLabelInfo.LABEL_DEFAULT)) {//本地没有第一页数据
            VoiceRoomListService.getInstance().getVoiceRoomList(0, VoiceLabelInfo.LABEL_DEFAULT, "", null);
        }
    }

    @NonNull
    private ArrayList<Integer> getIdList(int label) {
        ArrayList<Integer> idList = voiceIdMap.get(String.valueOf(label));
        if (idList == null) {
            idList = new ArrayList<>();
            voiceIdMap.put(String.valueOf(label), idList);
        }
        return idList;
    }
}
