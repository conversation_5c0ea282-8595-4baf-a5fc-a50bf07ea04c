package com.wepie.wespy.module.makefriend.viewholder

import android.graphics.Color
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.configservice.ConfigHelper
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.RoomInfo

class VoiceRoomVideoViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView), IVoiceViewHolder {
    companion object {
        fun updateLabelInfo(labelType: Int, gameType: Int, labelTv: TextView) {
            val labelInfo = ConfigHelper.getInstance().getLabelById(labelType)
            if (labelInfo == null) {
                labelTv.isVisible = false
                return
            }
            labelTv.isVisible = true
            if (gameType == RoomInfo.GAME_TYPE_WEDDING) {
                labelTv.setText(R.string.wedding)
                labelTv.setTextColor(Color.parseColor("#FF59A9"))
                labelTv.background = ResUtil.getDrawableWithTintValue(
                    R.drawable.room_label_bg,
                    Color.parseColor("#FFECF8")
                )
            } else if (labelInfo != null && !TextUtils.isEmpty(labelInfo.name)) {
                labelTv.text = labelInfo.name
                if (gameType == RoomInfo.GAME_TYPE_VIDEO_ROOM) {
                    labelTv.setTextColor(Color.parseColor("#FF6516"))
                    labelTv.background = ResUtil.getDrawableWithTintValue(
                        R.drawable.room_label_bg,
                        Color.parseColor("#FFF7F4")
                    )
                } else {
                    labelTv.setTextColor(Color.parseColor(labelInfo.textColor))
                    labelTv.background = ResUtil.getDrawableWithTintValue(
                        R.drawable.room_label_bg,
                        Color.parseColor(labelInfo.bgColor)
                    )
                }
            } else {
                labelTv.isVisible = false
            }
        }
    }
}