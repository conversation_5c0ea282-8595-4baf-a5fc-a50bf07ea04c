package com.wepie.wespy.module.makefriend.viewholder

import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Shader
import android.text.TextUtils
import android.view.View
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.ktx.dp
import com.huiwan.base.str.ResUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.constentity.propextra.MarkingExtra
import com.huiwan.configservice.model.voiceroom.VoiceLabelInfo
import com.huiwan.lib.api.impl
import com.huiwan.lib.api.plugins.IFunctionShieldApi
import com.huiwan.user.LifeUserSimpleInfoCallback
import com.huiwan.user.UserService
import com.huiwan.user.entity.UserSimpleInfo
import com.wejoy.weplay.module.makefriend.WejoyVoiceRoomGameExtensionInfo
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.databinding.WejoyVoiceRoomListGameItemBinding
import com.wepie.wespy.helper.imageLoader.HeadImageLoader
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil
import com.wepie.wespy.model.entity.EnterRoomInfo
import com.wepie.wespy.model.entity.makefriend.PlayingContent
import com.wepie.wespy.model.entity.makefriend.VoiceRoomListInfo
import com.wepie.wespy.module.common.jump.JumpRoomUtil
import com.wepie.wespy.module.voiceroom.voicegame.data.LittleGameUtil
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.PlayingContentTypeEnum

class VoiceRoomGameViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView),
    IVoiceViewHolder {

    private var binding: WejoyVoiceRoomListGameItemBinding =
        WejoyVoiceRoomListGameItemBinding.bind(itemView)

    private var voiceLabelInfo: VoiceLabelInfo? = null

    fun update(
        info: VoiceRoomListInfo, position: Int, voiceLabelInfo: VoiceLabelInfo?
    ) {
        this.voiceLabelInfo = voiceLabelInfo
        binding.roomNameTv.text = info.name
        binding.voiceRoomLock.isVisible = info.need_passwd
        HeadImageLoader.loadCircleHeadImage(info.headImgUrl, binding.headIv)

        // 区分关注房离线状态
        if (info.offline) {
            binding.vrItemUsernumTv.text = ResUtil.getStr(R.string.tab_voice_item_outline)
        } else {
            binding.vrItemUsernumTv.text = info.user_num.toString()
        }

        val simpleInfo = info.userInfo
        if (simpleInfo == null) {
            binding.roomOwnerAreaIv.visibility = View.GONE
            UserService.get()
                .getCacheSimpleUser(info.owner_uid, object : LifeUserSimpleInfoCallback(itemView) {
                    val currentPosition: Int = position

                    override fun onUserInfoSuccess(simpleInfo: UserSimpleInfo) {
                        info.userInfo = simpleInfo
                        if (currentPosition == position) {
                            updateUserSimpleInfoView(simpleInfo, info)
                        }
                    }

                    override fun onUserInfoFailed(description: String?) {
                    }
                })
        } else {
            updateUserSimpleInfoView(simpleInfo, info)
        }
        val playingContent = info.playingContentList.firstOrNull()
        showPlayingContent(playingContent)

        updateVoiceMarking(info)

        itemView.setOnClickListener { v: View ->
            val enterRoomInfo = EnterRoomInfo.buildEnterRoom(v.context, info.rid, info.gameType)
                .setLovelyNewRoom(info.isLovelyNewRoom)
                .setSource(TrackSource.VOICE_ROOM_LIST)
                .setListPosition(position + 1)
                .setListInfo(info)
                .setVoiceLabelInfo(voiceLabelInfo).setSourceView(binding.headIv)
            if (voiceLabelInfo?.isCocosGame == true && !voiceLabelInfo.subLabList.isNullOrEmpty()) {
                enterRoomInfo.addTrackData("sub_tab", voiceLabelInfo.currentSubLabel.subLabelId)
            }
            JumpRoomUtil.getInstance().enterRoom(enterRoomInfo)
        }
    }

    private fun updateUserSimpleInfoView(simpleInfo: UserSimpleInfo, info: VoiceRoomListInfo) {
        val areaUrl = simpleInfo.areaUrl
        val hideArea = IFunctionShieldApi::class.impl().roomHideArea()
        if (info.isShowArea && !TextUtils.isEmpty(areaUrl) && !hideArea) {
            binding.roomOwnerAreaIv.visibility = View.VISIBLE
            WpImageLoader.load(areaUrl, binding.roomOwnerAreaIv)
        } else {
            binding.roomOwnerAreaIv.visibility = View.GONE
        }
    }

    /**
     * showPlayingContent.
     *
     */
    private fun showPlayingContent(playingContent: PlayingContent?) {
        if (playingContent?.type != PlayingContentTypeEnum.PlayingContentWidgetGame) {
            binding.voiceGameInfoLay.isVisible = false
            binding.voiceGameWaitingLay.isVisible = false
            binding.voiceGameGamingLay.isVisible = false
            return
        }
        val ext = playingContent.getExt(WejoyVoiceRoomGameExtensionInfo::class.java)
            ?: WejoyVoiceRoomGameExtensionInfo()
        binding.voiceGameInfoLay.isVisible = true
        WpImageLoader.load(
            ext.getIcon(),
            binding.voiceGameIconIv,
            HeadImageLoader.genHeadLoadInfo().setCornerPixel(3.dp)
        )
        binding.voiceGameModeTv.text = ext.getName()
        val colorArray: IntArray = ext.getTextColorArray()
        if (colorArray.size == 2) {
            val tv = binding.voiceGameModeTv
            tv.post {
                tv.setTextColor(Color.BLACK)
                tv.paint.setShader(
                    LinearGradient(
                        0f, tv.measuredHeight.toFloat(), 0F, 0F,
                        colorArray.reversedArray(), null, Shader.TileMode.CLAMP
                    )
                )
                tv.invalidate()
            }
        } else {
            binding.voiceGameModeTv.setTextColor(ResUtil.getColor(R.color.main_activity_text_secondary))
            binding.voiceGameModeTv.paint.setShader(null)
        }
        binding.voiceGameCoinTv.text = ext.getCoinDesc()
        if (!ext.isGaming) {
            binding.voiceGameWaitingLay.isVisible = true
            binding.voiceGameGamingLay.isVisible = false
            val total = LittleGameUtil.getGamerCount(ext.matchMode)
            binding.voiceGameGamerCountTv.text = "${ext.gamerCount}/${total}"
        } else {
            binding.voiceGameWaitingLay.isVisible = false
            binding.voiceGameGamingLay.isVisible = true
        }
    }

    private fun updateVoiceMarking(info: VoiceRoomListInfo) {
        var markingPic = ""
        if (info.printId != 0) {
            val propItem = ConfigHelper.getInstance().propConfig.getPropItem(info.printId)
            if (propItem != null) {
                markingPic = propItem.getExtraByType(MarkingExtra::class.java)?.markingPic ?: ""
            }
        }
        if (!TextUtils.isEmpty(markingPic)) {
            ImageLoaderUtil.loadNormalImage(markingPic, binding.voiceRoomMarkingIcon)
            binding.voiceRoomMarkingIcon.visibility = View.VISIBLE
        } else {
            binding.voiceRoomMarkingIcon.visibility = View.GONE
        }
    }

    override fun getTrackData(): Map<String, Any> {
        val labelInfo = voiceLabelInfo
        if (labelInfo?.isCocosGame == true && !labelInfo.subLabList.isNullOrEmpty()) {
            return mapOf("sub_tab" to (labelInfo.currentSubLabel?.subLabelId ?: 0))
        }
        return emptyMap()
    }
}