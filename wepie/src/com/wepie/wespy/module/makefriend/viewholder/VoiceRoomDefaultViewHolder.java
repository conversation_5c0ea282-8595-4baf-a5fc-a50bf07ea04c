package com.wepie.wespy.module.makefriend.viewholder;

import static com.huiwan.configservice.model.voiceroom.VoiceLabelInfo.LABEL_FOLLOW;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.propextra.MarkingExtra;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.configservice.model.voiceroom.VoiceLabelInfo;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.Callback;
import com.huiwan.lib.api.plugins.IFunctionShieldApi;
import com.huiwan.lib.api.plugins.IMedalApi;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.CustomCircleImageView;
import com.huiwan.widget.MarqueeTextView;
import com.wejoy.weplay.module.makefriend.WePlayVoiceRoomExtensionInfo;
import com.wejoy.weplay.module.makefriend.WejoyVoiceRoomTimerManager;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.model.entity.EnterRoomInfo;
import com.wepie.wespy.model.entity.RoomInfo;
import com.wepie.wespy.model.entity.makefriend.PlayingContent;
import com.wepie.wespy.model.entity.makefriend.VoiceRoomListInfo;
import com.wepie.wespy.module.common.jump.JumpRoomUtil;
import com.wepie.wespy.module.fdiscover.event.TranslateEvent;
import com.wepie.wespy.module.fdiscover.main.CircleTranslateUtil;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class VoiceRoomDefaultViewHolder extends RecyclerView.ViewHolder implements IVoiceViewHolder {

    private final CustomCircleImageView headImg;
    private final ImageView ownerAreaIv;
    private final TextView roomNameTv;
    private final TextView memberNumTv;
    private ImageView itemIconIv;
    private MarqueeTextView itemInfoMtv;
    private final View itemView;
    private final TextView roomLabelTv;
    private final TextView translate;
    private final TextView translateContent;
    private final ImageView lockIv;
    private final TextView manageTv;
    @Nullable
    private IMedalApi.IMedalListView medalListView;

    private final RelativeLayout rootLay;
    private final ImageView animIv;
    private final ImageView starIv;
    private ImageView markingIv;
    private Callback mCallback;
    // 一个Item中服务器返回的语音房在玩内容列表（内容有3钟类型）
    private List<PlayingContent> playingContentList;
    // 指向playingContentList，表示现在需要展示的内容
    // 使用场景：当红包倒计时为0s时，不请求服务器直接刷新UI，playingContentListPos++，展示下一内容
    private int playingContentListPos = 0;
    private WejoyVoiceRoomTimerManager timerManager;

    public VoiceRoomDefaultViewHolder(@NonNull View itemView) {
        super(itemView);
        this.itemView = itemView;
        headImg = itemView.findViewById(R.id.head_iv);
        ownerAreaIv = itemView.findViewById(R.id.room_owner_area_iv);
        roomNameTv = itemView.findViewById(R.id.room_name_tv);
        memberNumTv = itemView.findViewById(R.id.vr_item_usernum_tv);
        itemIconIv = itemView.findViewById(R.id.vr_item_icon_iv);
        itemInfoMtv = itemView.findViewById(R.id.vr_item_info_mtv);
        roomLabelTv = itemView.findViewById(R.id.room_label_tv);
        lockIv = itemView.findViewById(R.id.voice_room_lock);
        manageTv = itemView.findViewById(R.id.voice_room_manage_tv);

        rootLay = itemView.findViewById(R.id.voice_room_list_lay);
        animIv = itemView.findViewById(R.id.voice_room_list_anim_iv);
        starIv = itemView.findViewById(R.id.voice_room_list_star_iv);
        translate = itemView.findViewById(R.id.translate);
        translateContent = itemView.findViewById(R.id.translate_content);
        FrameLayout medalContainer = itemView.findViewById(R.id.voice_room_medal_container);
        if (LibBaseUtil.isArServer()) {
            medalListView = ApiService.of(IMedalApi.class).genMedalListView(itemView.getContext());
            medalListView.updateGravity(Gravity.END);
            medalContainer.addView(medalListView.getView());
        }
        markingIv = itemView.findViewById(R.id.voice_room_marking_icon);

        playingContentList = new ArrayList<>();
    }

    public void update(Context context, VoiceRoomListInfo info, int position, VoiceLabelInfo voiceLabelInfo) {
        // 维护timerManager
        removeCallback();
        playingContentList = info.playingContentList;
        roomNameTv.setText(info.name);

        // 区分关注房离线状态
        if (info.offline) {
            memberNumTv.setText(ResUtil.getStr(R.string.tab_voice_item_outline));
        } else {
            memberNumTv.setText(String.valueOf(info.user_num));
        }

        roomLabelTv.setVisibility(View.GONE);
        if (info.gameType == RoomInfo.GAME_TYPE_WEDDING) {
            roomLabelTv.setVisibility(View.VISIBLE);
            roomLabelTv.setText(R.string.wedding);
            roomLabelTv.setTextColor(Color.parseColor("#FF59A9"));
            roomLabelTv.setBackground(ResUtil.getDrawableWithTintValue(R.drawable.room_label_bg, Color.parseColor("#1AFF59A9")));
        } else if (info.labelType != VoiceLabelInfo.LABEL_DEFAULT) {
            VoiceLabelInfo labelInfo = ConfigHelper.getInstance().getLabelById(info.labelType);
            if (labelInfo != null && !TextUtils.isEmpty(labelInfo.getName())) {
                roomLabelTv.setVisibility(View.VISIBLE);
                roomLabelTv.setText(labelInfo.getName());
                roomLabelTv.setTextColor(Color.parseColor(labelInfo.getTextColor()));
                roomLabelTv.setBackground(ResUtil.getDrawableWithTintValue(R.drawable.room_label_bg, Color.parseColor(labelInfo.getBgColor())));
            }
        }

        if (info.need_passwd) {
            lockIv.setVisibility(View.VISIBLE);
        } else {
            lockIv.setVisibility(View.GONE);
        }

        if (voiceLabelInfo.getId() == LABEL_FOLLOW && info.AdminUidList != null && info.AdminUidList.contains(LoginHelper.getLoginUid())) {
            manageTv.setVisibility(View.VISIBLE);
        } else {
            manageTv.setVisibility(View.GONE);
        }
        HeadImageLoader.loadCircleHeadImage(info.headImgUrl, headImg);

        //只在推荐列表和热门地区下并且是萌新房才展示萌新背景和动画
        if ((voiceLabelInfo.getId() == VoiceLabelInfo.LABEL_DEFAULT || voiceLabelInfo.getId() == VoiceLabelInfo.LABEL_POPULAR) && info.isLovelyNewRoom) {
            rootLay.setBackgroundResource(R.drawable.voice_room_list_bg);
            starIv.setVisibility(View.VISIBLE);
            animIv.setVisibility(View.VISIBLE);
            if (position == 0 && PrefUserUtil.getInstance().getBoolean(PrefUserUtil.LOVELY_NEW_ANIM, true)) {
                showAnim();
                PrefUserUtil.getInstance().setBoolean(PrefUserUtil.LOVELY_NEW_ANIM, false);
            }
        } else {
            rootLay.setBackgroundResource(R.drawable.voice_room_list_simple_bg);
            starIv.setVisibility(View.GONE);
            animIv.setVisibility(View.GONE);
        }

        itemView.setOnClickListener(v -> {
            EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildEnterRoom(context, info.rid, info.gameType)
                    .setLovelyNewRoom(info.isLovelyNewRoom)
                    .setSource(TrackSource.VOICE_ROOM_LIST)
                    .setListPosition(position + 1)
                    .setListInfo(info)
                    .setVoiceLabelInfo(voiceLabelInfo).setSourceView(headImg);
            JumpRoomUtil.getInstance().enterRoom(enterRoomInfo);
        });

        // 如果用户先有了印记，后续活动到期，印记消失，需要恢复勋章位置
        // 需要将markingIv set GONE提前，否则显示勋章的时候无法根据印记是否显示改变位置
        markingIv.setVisibility(View.GONE);

        TranslateEvent event = TranslateEvent.build(info.rid, roomNameTv.getText()).setTranslateContent(info.translateContent);
        CircleTranslateUtil.configTranslateInfo(translate, translateContent, event);
        UserSimpleInfo simpleInfo = info.userInfo;
        if (simpleInfo == null) {
            ownerAreaIv.setVisibility(View.GONE);
            UserService.get().getCacheSimpleUser(info.owner_uid, new LifeUserSimpleInfoCallback(itemView) {
                final int currentPosition = position;

                @Override
                public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                    info.userInfo = simpleInfo;
                    if (currentPosition == position) {
                        updateUserSimpleInfoView(simpleInfo, info);
                    }
                }

                @Override
                public void onUserInfoFailed(String description) {

                }
            });
        } else {
            updateUserSimpleInfoView(simpleInfo, info);
        }

        updateVoiceMarking(info);
    }

    private void updateUserSimpleInfoView(UserSimpleInfo simpleInfo, VoiceRoomListInfo info) {
        if (medalListView != null) {
            updateNameLayPos(!simpleInfo.wearMedals.isEmpty());
            medalListView.updateMedalList(simpleInfo.wearMedals);
        }
        String areaUrl = simpleInfo.getAreaUrl();
        boolean hideArea = ApiService.of(IFunctionShieldApi.class).roomHideArea();
        if (info.isShowArea && !TextUtils.isEmpty(areaUrl) && !hideArea) {
            ownerAreaIv.setVisibility(View.VISIBLE);
            WpImageLoader.load(areaUrl, ownerAreaIv);
        }
    }

    /**
     * 如果有勋章，则nameLay与勋章的边距设置为10dp
     *
     * @param change 是否需要重新设置边距
     */
    private void updateNameLayPos(boolean change) {
        if (change) {
            ViewGroup nameLay = itemView.findViewById(R.id.room_name_lay);
            RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) nameLay.getLayoutParams();
            params.setMarginEnd(ScreenUtil.dip2px(10));

            if (markingIv.getVisibility() == View.GONE) {
                // 勋章调整为和标签布局居中对齐，默认和房间名 居中对齐
                // 如果用户先有了印记，后续活动到期，印记消失，需要恢复勋章位置
                ViewGroup medalLay = itemView.findViewById(R.id.voice_room_medal_container);
                RelativeLayout.LayoutParams medalParams = (RelativeLayout.LayoutParams) medalLay.getLayoutParams();
                medalParams.topMargin = ScreenUtil.dip2px(17);

                params.removeRule(RelativeLayout.START_OF);
                params.addRule(RelativeLayout.START_OF, R.id.voice_room_medal_container);

                ViewGroup labelLay = itemView.findViewById(R.id.room_label_lay);
                RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) labelLay.getLayoutParams();
                layoutParams.removeRule(RelativeLayout.START_OF);
                ViewGroup infoLay = itemView.findViewById(R.id.item_info_lay);
                LinearLayout.LayoutParams lly = (LinearLayout.LayoutParams) infoLay.getLayoutParams();
                lly.setMarginEnd(ScreenUtil.dip2px(20));
            }
        }
    }

    public void update(Context context, VoiceRoomListInfo info, int position, VoiceLabelInfo voiceLabelInfo, WejoyVoiceRoomTimerManager timerManager) {
        this.timerManager = timerManager;
        hidePlayingUI();
        update(context, info, position, voiceLabelInfo);
        playingContentListPos = 0;
        if (playingContentList.size() > playingContentListPos) {
            showPlayingContent(playingContentList.get(playingContentListPos));
        }
    }

    private void showAnim() {
        animIv.animate().cancel();
        animIv.animate().translationXBy(ScreenUtil.getScreenWidth() + ScreenUtil.dip2px(178)).setDuration(1200).setInterpolator(new AccelerateDecelerateInterpolator()).start();
    }

    public static VoiceRoomDefaultViewHolder create(Context context, ViewGroup parent) {
        return new VoiceRoomDefaultViewHolder(LayoutInflater.from(context).inflate(R.layout.wejoy_voice_room_list_item, parent, false));
    }

    private void updateVoiceMarking(VoiceRoomListInfo info) {
        if (info.printId != 0) {
            PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(info.printId);
            if (propItem != null) {
                MarkingExtra extra = propItem.getExtraByType(MarkingExtra.class);
                if (extra != null) {
                    if (!TextUtils.isEmpty(extra.markingPic)) {
                        ImageLoaderUtil.loadNormalImage(extra.markingPic, markingIv);
                        markingIv.setVisibility(View.VISIBLE);
                        updateRoomNameLay();
                    }
                }
            }
        }
    }

    private void updateRoomNameLay() {
        // 解决房间名字过长，和语音房印记重合
        ViewGroup nameLay = itemView.findViewById(R.id.room_name_lay);
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) nameLay.getLayoutParams();
        params.removeRule(RelativeLayout.START_OF);
        params.addRule(RelativeLayout.START_OF, R.id.voice_room_marking_icon);
        params.setMarginEnd(ScreenUtil.dip2px(10));

        // 勋章调整为和标签布局居中对齐，默认和房间名 居中对齐
        ViewGroup medalLay = itemView.findViewById(R.id.voice_room_medal_container);
        RelativeLayout.LayoutParams medalParams = (RelativeLayout.LayoutParams) medalLay.getLayoutParams();
        // 和标签布局居中对齐，改变距离顶部距离
        medalParams.topMargin = ScreenUtil.dip2px(49);

        ViewGroup labelLay = itemView.findViewById(R.id.room_label_lay);
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) labelLay.getLayoutParams();
        layoutParams.addRule(RelativeLayout.START_OF, medalLay.getId());
        ViewGroup infoLay = itemView.findViewById(R.id.item_info_lay);
        LinearLayout.LayoutParams lly = (LinearLayout.LayoutParams) infoLay.getLayoutParams();
        lly.setMarginEnd(0);
    }

    /**
     * showPlayingContent.
     *
     * @param playingContent 语音房在玩内容+类型
     *
     * 设置语音房在玩展示内容，现有三个类型：红包雨+倒计时、视频内容、游戏名
     */
    private void showPlayingContent(PlayingContent playingContent) {
        removeCallback();
        String title = playingContent.getTitle();
        if (TextUtils.isEmpty(title)) {
            return;
        }
        TmpRoomPackets.PlayingContentTypeEnum type = playingContent.getType();
        if (type == TmpRoomPackets.PlayingContentTypeEnum.PlayingContentRedPacket) {
            WePlayVoiceRoomExtensionInfo extensionInfo = playingContent.getExt(WePlayVoiceRoomExtensionInfo.class);
            showRedPacketContent(extensionInfo.getEndTime(), playingContent.getTitle());
        } else if (type == TmpRoomPackets.PlayingContentTypeEnum.PlayingContentWidgetGame) {
            setContent(title, R.drawable.voice_room_widget_game_icon);

        } else if (type == TmpRoomPackets.PlayingContentTypeEnum.PlayingContentVideo) {
            setContent(title, R.drawable.ic_video_room);

        }
    }

    /**
     * setContent.
     *
     * @param title           语音房在玩 标题
     * @param itemIconIvRes   在玩Icon展示
     *
     * 展示title、icon
     */
    private void setContent(String title, int itemIconIvRes) {
        if (itemIconIvRes == 0 || TextUtils.isEmpty(title)) {
            return;
        }

        itemInfoMtv.setText(title);
        itemIconIv.setImageResource(itemIconIvRes);
        showPlayingUI();
    }

    /**
     * showRedPacketContent.
     *
     * @param time           红包雨开始的时间戳（服务器时间）
     * @param title          红包内容
     *
     * 创建添加callback，展示红包雨的内容、倒计时
     */
    private void showRedPacketContent(long time, String title) {
        int itemIconIvRes = R.drawable.voice_room_red_packet_icon;
        // 红包雨开始剩余时间
        long leftTime = TimeUtil.getLeftServerSecond(time);

        // 如果红包雨已经开始
        if (leftTime <= 0) {
            // 展示下一个在玩内容
            if (playingContentList.size() - 1 > playingContentListPos) {
                playingContentListPos++;
                showPlayingContent(playingContentList.get(playingContentListPos));
            }
            return;
        }

        /* 增加保护，防止返回的列表中存在多个红包
         * 若mCallback不为空又重新创建，会导致
         * 1、timerManager持有之前的mCallback
         * 2、发生java.util.ConcurrentModificationException，在便利timerManager.listner时，创建并添加mCallback
         */
        if (mCallback == null) {
            mCallback = new Callback() {
                @Override
                public void onCall() {
                    long leftTime = TimeUtil.getLeftServerSecond(time);
                    // 如果红包雨已经开始，调用showPlayingContent移除callback并展示下一个语音房在玩内容
                    if (leftTime <= 0) {
                        hidePlayingUI();
                        if (playingContentList.size() > playingContentListPos) {
                            showPlayingContent(playingContentList.get(playingContentListPos));
                        }
                    } else {
                        itemInfoMtv.setText(getRedPacketContent(title, TimeUtil.getLeftServerSecond(time)));
                    }
                }
            };
            timerManager.addCallback(mCallback);
        }

        setContent(getRedPacketContent(title, leftTime), itemIconIvRes);
    }

    /**
     * 红包雨展示内容拼接
     */
    private String getRedPacketContent(String title, long time) {
        return ResUtil.getStr(R.string.voice_room_red_packet_content, title, time);
    }

    private void removeCallback() {
        if (timerManager != null && mCallback != null) {
            timerManager.removeCallback(mCallback);
            mCallback = null;
        }
    }


    /**
     * 展示语音房在玩UI
     */
    private void showPlayingUI() {
        itemIconIv.setVisibility(View.VISIBLE);
        itemInfoMtv.setVisibility(View.VISIBLE);
    }

    /**
     * 隐藏语音房在玩UI
     */
    private void hidePlayingUI() {
        itemIconIv.setVisibility(View.INVISIBLE);
        itemInfoMtv.setVisibility(View.INVISIBLE);
    }

    @Override
    @NonNull
    public Map<String, Object> getTrackData() {
        return Collections.emptyMap();
    }
}
