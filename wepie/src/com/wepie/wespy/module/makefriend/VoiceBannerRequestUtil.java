package com.wepie.wespy.module.makefriend;

import com.huiwan.base.lifecycle.IReference;
import com.huiwan.base.lifecycle.IReferenceKt;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.store.file.FileCacheName;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.GlobalLife;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.makefriend.VoiceBannerInfo;
import com.wepie.wespy.model.entity.makefriend.VoiceRoomBanner;
import com.wepie.wespy.net.http.api.ConstApi;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

// Created by bigwen on 2018/7/3.
public class VoiceBannerRequestUtil {

    private static final String TAG = "VoiceBannerRequestUtil";
    public static final int FAILED_CODE = -1;
    public static final int SUCCESS_CODE = 200;
    private static VoiceBannerInfo bannerInfo;

    public static void loadLocalBanner(final LifeDataCallback<List<VoiceRoomBanner>> callback) {
        IReference<LifeDataCallback<List<VoiceRoomBanner>>> reference = IReferenceKt.wrap(callback);
        FileUtil.readFileAsync(getBannerName(), new FileUtil.LifeCacheCallback(GlobalLife.INSTANCE) {
            @Override
            public void onFinish(String json) {
                try {
                    final VoiceBannerInfo voiceBannerInfo = JsonUtil.fromJson(json, VoiceBannerInfo.class);
                    if (voiceBannerInfo != null) {
                        if (voiceBannerInfo.banner != null && voiceBannerInfo.banner.get(0).id == 0) {
                            IReferenceKt.run(reference, bannerCallback -> bannerCallback.onFail(FAILED_CODE, ResUtil.getStr(R.string.common_local_data_expired)));
                            TimeLogger.msgNoStack(ResUtil.getStr(R.string.common_local_data_expired) + ", error banner id 0");
                            return;
                        }
                        bannerInfo = voiceBannerInfo;
                        IReferenceKt.run(reference, bannerCallback -> {
                            Result<List<VoiceRoomBanner>> result = new Result<>();
                            result.data = voiceBannerInfo.banner;
                            result.code = SUCCESS_CODE;
                            bannerCallback.onSuccess(result);
                        });
                    } else {
                        IReferenceKt.run(reference, bannerCallback -> bannerCallback.onFail(FAILED_CODE, ResUtil.getStr(R.string.common_local_data_error)));
                    }
                } catch (Exception e) {
                    IReferenceKt.run(reference, bannerCallback -> bannerCallback.onFail(FAILED_CODE, ResUtil.getStr(R.string.common_local_data_error)));
                }
            }
        });
    }

    public static void requestVoiceRoomBanner(final LifeDataCallback<List<VoiceRoomBanner>> callback) {
        final int version = bannerInfo == null ? 0 : bannerInfo.version_num;
        IReference<LifeDataCallback<List<VoiceRoomBanner>>> reference = IReferenceKt.wrap(callback);
        ConstApi.getVoiceBanner(version, new LifeDataCallback<>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<VoiceBannerInfo> result) {
                List<VoiceRoomBanner> bannerList = new ArrayList<>();
                if (version < result.data.version_num || !result.data.banner.isEmpty()) {
                    bannerList.addAll(result.data.banner);
                    bannerInfo = result.data;
                    saveFile(bannerInfo);
                } else {
                    if (bannerInfo != null) {
                        bannerList.addAll(bannerInfo.banner);
                    }
                }
                IReferenceKt.run(reference, listLifeDataCallback -> {
                    Result<List<VoiceRoomBanner>> listResult = new Result<>();
                    listResult.data = bannerList;
                    listResult.code = SUCCESS_CODE;
                    listLifeDataCallback.onSuccess(listResult);
                });
            }

            @Override
            public void onFail(int i, String s) {
                IReferenceKt.run(reference, listLifeDataCallback -> listLifeDataCallback.onFail(i, s));
            }
        });
    }

    private static void saveFile(VoiceBannerInfo bannerInfo) {
        String content = JsonUtil.toJson(bannerInfo);
        FileUtil.writeFileAsync(getBannerName(), content);
    }

    /**
     * 退出登录时清理banner数据
     */
    public static void clear() {
        File file = new File(FileUtil.getFullPath(getBannerName()));
        if (file.exists()) {
            HLog.d(TAG, "banner file has been clear path is {}", file.getAbsolutePath());
            file.delete();
        }
    }

    private static String getBannerName() {
        return FileCacheName.DISCOVER_BANNER_LIST;
    }
}
