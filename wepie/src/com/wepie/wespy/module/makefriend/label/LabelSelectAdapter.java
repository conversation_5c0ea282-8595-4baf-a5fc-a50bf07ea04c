package com.wepie.wespy.module.makefriend.label;

import androidx.annotation.NonNull;
import androidx.core.widget.TextViewCompat;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.widget.CustomTextView;
import com.wepie.wespy.R;
import com.huiwan.configservice.model.voiceroom.VoiceLabelInfo;
import com.huiwan.widget.rv.RVHolder;
import com.huiwan.base.util.ScreenUtil;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

import java.util.ArrayList;
import java.util.List;

public class LabelSelectAdapter extends RecyclerView.Adapter<RVHolder> {

    private final List<VoiceLabelInfo> list = new ArrayList<>();
    private int selectedId = VoiceLabelInfo.LABEL_DEFAULT;
    private boolean isRoomSettingView = false;
    private Observer<Integer> selectTypeListener;

    public LabelSelectAdapter() {
    }

    public LabelSelectAdapter(boolean isRoomSettingView) {
        this.isRoomSettingView = isRoomSettingView;
    }

    public void refresh(List<VoiceLabelInfo> list) {
        this.list.clear();
        this.list.addAll(list);
        notifyDataSetChanged();
    }

    public int getSelectedId() {
        return selectedId;
    }

    public int getSelectedLabelType() {
        return selectedId < list.size() ? list.get(selectedId).getId() : VoiceLabelInfo.LABEL_DEFAULT;
    }

    public void setSelectTypeListener(Observer<Integer> selectTypeListener) {
        this.selectTypeListener = selectTypeListener;
    }

    public void setSelected(int id) {
        if (id == selectedId) {
            //点同一个重置为默认
            selectedId = VoiceLabelInfo.LABEL_DEFAULT;
            EventDispatcher.postSelectLabelEvent(selectedId);
            notifyDataSetChanged();
            if (selectTypeListener!= null) {
                selectTypeListener.onChanged(selectedId);
            }
            return;
        }
        int preId = selectedId;
        selectedId = id;
        int size = list.size();
        for (int i = 0; i < size; i++) {
            int itemId = list.get(i).getId();
            if (selectedId == itemId) {
                notifyItemChanged(i, true);
                // 更换标签
                EventDispatcher.postSelectLabelEvent(selectedId);
                if (selectTypeListener!= null) {
                    selectTypeListener.onChanged(selectedId);
                }
            } else if (preId == itemId) {
                notifyItemChanged(i, false);
            }
        }
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    @Override
    public void onBindViewHolder(@NonNull RVHolder holder, int position) {
        final VoiceLabelInfo labelInfo = list.get(position);
        TextView labelTv = (TextView) holder.itemView;
        labelTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                setSelected(labelInfo.getId());
            }
        });
        labelTv.setText(labelInfo.getName());
        updateSelected(labelTv, labelInfo.getId() == selectedId);
    }

    @Override
    public void onBindViewHolder(@NonNull RVHolder holder, int position, @NonNull List<Object> payloads) {
        if (payloads.isEmpty()) {
            onBindViewHolder(holder, position);
        } else {
            Object o = payloads.get(0);
            if (o instanceof Boolean) {
                TextView tv = (TextView) holder.itemView;
                updateSelected(tv, (Boolean) o);
            }
        }
    }

    private void updateSelected(TextView labelTv, boolean selected) {
        labelTv.setSelected(selected);
        if (isRoomSettingView) {
            labelTv.setTextColor(selected ? ResUtil.getColor(R.color.create_room_label_color_selected)
                    : ResUtil.getColor(R.color.white_color));
        } else {
            labelTv.setTextColor(selected ? ResUtil.getColor(R.color.create_room_label_color_selected)
                    : ResUtil.getColor(R.color.color_user_info_detail_name_text));
        }
    }

    @NonNull
    @Override
    public RVHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        TextView labelTv = new CustomTextView(parent.getContext());
        if (isRoomSettingView) {
            labelTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
            labelTv.setLayoutParams(new ViewGroup.LayoutParams(ScreenUtil.dip2px(64), ScreenUtil.dip2px(32)));
            labelTv.setBackgroundResource(R.drawable.sel_voice_room_setting_label_bg_corner16);
        } else {
            if (LibBaseUtil.getLang().isTr()) {
                TextViewCompat.setAutoSizeTextTypeUniformWithConfiguration(labelTv, 8, 14, 1, TypedValue.COMPLEX_UNIT_DIP);
            } else {
                labelTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
            }
            labelTv.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ScreenUtil.dip2px(32)));
            int dp8 = ScreenUtil.dip2px(8f);
            labelTv.setPadding(dp8, 0, dp8, 0);
            labelTv.setBackgroundResource(R.drawable.sel_create_room_bg_corner16);
        }
        labelTv.setGravity(Gravity.CENTER);
        labelTv.setTextAlignment(View.TEXT_ALIGNMENT_GRAVITY);
        labelTv.setMaxLines(1);
        return new RVHolder(labelTv);
    }
}