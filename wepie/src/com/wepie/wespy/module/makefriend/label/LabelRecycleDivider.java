package com.wepie.wespy.module.makefriend.label;

import android.graphics.Rect;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.huiwan.base.util.ScreenUtil;


// Created by bigwen on 2018/4/21.
public class LabelRecycleDivider extends RecyclerView.ItemDecoration {

    private int rightMargin = ScreenUtil.dip2px(16);//px, item距离右边的margin
    private int leftMargin = ScreenUtil.dip2px(16);//px, item距离左边的margin
    private int bottomMargin = 0;
    private int topMargin = 0;
    private int verticalDivider;//px, 竖直方向分割线的宽度
    private int horizontalDivider;//px, 水平方向分割线的宽度

    public LabelRecycleDivider(int v, int h, int rightMargin, int leftMargin) {
        this.verticalDivider = v;
        this.horizontalDivider = h;
        this.rightMargin = rightMargin;
        this.leftMargin = leftMargin;
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        GridLayoutManager layoutManager = (GridLayoutManager) parent.getLayoutManager();
        GridLayoutManager.SpanSizeLookup lookup = layoutManager.getSpanSizeLookup();
        GridLayoutManager.LayoutParams lp = (GridLayoutManager.LayoutParams) view.getLayoutParams();
        int childPosition = parent.getChildAdapterPosition(view);
        int spanCount = layoutManager.getSpanCount();
        int position = parent.getChildAdapterPosition(view);//得到它在总数里面的位置
        int spanIndex = lookup.getSpanIndex(position, layoutManager.getSpanCount());//获取每排的位置
        int spanSize = lookup.getSpanSize(position);//获取它所占有的比重，上面讲的
        int maxDataSize = parent.getAdapter().getItemCount();
        int maxSpan = layoutManager.getSpanSizeLookup().getSpanGroupIndex(maxDataSize > 0 ? maxDataSize - 1 : 0, spanCount);

        if (layoutManager.getOrientation() == GridLayoutManager.VERTICAL) {//这里只处理竖直方向的列表
            //竖直方向
            if (layoutManager.getSpanSizeLookup().getSpanGroupIndex(childPosition, spanCount) == 0) {//第一排的需要上面
                outRect.top = topMargin;
            }
            outRect.bottom = horizontalDivider;
            if (layoutManager.getSpanSizeLookup().getSpanGroupIndex(childPosition, spanCount) == maxSpan){//最后一行
                outRect.bottom = bottomMargin;
            }

            //水平方向，这里只适合一行两个item的情况
            //每个item的宽度是均分的，如果每个item的outRect.left, outRect.right加起来的和不一样，会导致item大小不一致
            outRect.left = verticalDivider / 2;
            outRect.right = verticalDivider / 2;
        }
    }
}
