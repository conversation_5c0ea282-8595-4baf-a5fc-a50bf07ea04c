package com.wepie.wespy.module.advertisement;

import android.graphics.Color;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;
import com.huiwan.store.file.FileCacheName;
import com.huiwan.store.file.FileConfig;
import com.huiwan.store.SdkBaseStore;

import java.io.File;

/**
 * 广告实体
 *
 * <AUTHOR>
 * date 2020-09-11
 */
public class AdvertisementEntity implements Comparable<AdvertisementEntity> {

    public final static int AD_TYPE_IMAGE = 1;
    public final static int AD_TYPE_VIDEO = 2;
    public final static int AD_TYPE_GIF = 3;
    @SerializedName("id")
    private int adId;//广告id

    @SerializedName("url")
    private String adUrl;//广告地址

    @SerializedName("type")
    private int type;//广告类型

    @SerializedName("show_time")
    private int showTime;//展示时长

    @SerializedName("width")
    private int width;//资源宽度

    @SerializedName("height")
    private int height;//资源高度

    @SerializedName("deeplink")
    private String wespydeeplink;//跳转链接

    @SerializedName("start_date")
    private long startDate;//开始展示时间

    @SerializedName("end_date")
    private long endDate;//结束展示时间

    @SerializedName("show_count")
    private int showCount = 999;//剩余展示总次数

    @SerializedName("limit_per_day")
    private int limitPerDay = 1;//同一用户每天展示上限

    @SerializedName("show_count_local")
    private int hasShowedTotal = 0;//已经展示的总次数，本地字段，服务器不传

    @SerializedName("has_shown_day")
    private int showedPerDay = 0;//同一用户每天展示过的次数，本地字段，服务器不传

    @SerializedName("bar_icon_img")
    private String barIconImg = "";//展示icon

    @SerializedName("bar_icon_name")
    private String barIconName = "";//展示icon后面的名字

    @SerializedName("bar_slogan")
    private String barSlogan = "";//展示标语

    @SerializedName("bar_ad_name")
    private String barAdName = "";//展示广告tips

    @SerializedName("bar_color")
    private String barColor = "#00000000";//展示bar的底色

    @SerializedName("bar_is_show")
    private boolean barIsShow = false;//是否展示bar

    @SerializedName("priority")
    private int priority = 0;//优先级

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AdvertisementEntity that = (AdvertisementEntity) o;
        return adId == that.adId;
    }

    @Override
    public int hashCode() {
        return adId;
    }

    @Override
    public String toString() {
        return "AdvertisementEntity{" +
                "adId=" + adId +
                ", adUrl='" + adUrl + '\'' +
                ", type=" + type +
                ", showTime=" + showTime +
                ", width=" + width +
                ", height=" + height +
                ", wespydeeplink='" + wespydeeplink + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", showCount=" + showCount +
                ", limitPerDay=" + limitPerDay +
                ", showedPerDay=" + showedPerDay +
                ", barIconImg='" + barIconImg + '\'' +
                ", barIconName='" + barIconName + '\'' +
                ", barSlogan='" + barSlogan + '\'' +
                ", barAdName='" + barAdName + '\'' +
                ", barColor=" + barColor +
                ", barIsShow=" + barIsShow +
                '}';
    }

    public int getHasShowedTotal() {
        return hasShowedTotal;
    }

    public void setHasShowedTotal(int hasShowedTotal) {
        this.hasShowedTotal = hasShowedTotal;
    }

    public int getShowedPerDay() {
        return showedPerDay;
    }

    public void setShowedPerDay(int showedPerDay) {
        this.showedPerDay = showedPerDay;
    }

    public long getStartDate() {
        return startDate;
    }

    public void setStartDate(long startDate) {
        this.startDate = startDate;
    }

    public long getEndDate() {
        return endDate;
    }

    public void setEndDate(long endDate) {
        this.endDate = endDate;
    }

    public int getShowCount() {
        return showCount;
    }

    public void setShowCount(int showCount) {
        this.showCount = showCount;
    }

    public int getLimitPerDay() {
        return limitPerDay;
    }

    public void setLimitPerDay(int limitPerDay) {
        this.limitPerDay = limitPerDay;
    }

    public int getAdId() {
        return adId;
    }

    public void setAdId(int adId) {
        this.adId = adId;
    }

    public String getAdUrl() {
        return adUrl;
    }

    public void setAdUrl(String adUrl) {
        this.adUrl = adUrl;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getShowTime() {
        return showTime;
    }

    public void setShowTime(int showTime) {
        this.showTime = showTime;
    }

    public String getAdFileName() {
        return FileConfig.getExtraFileFullPath(SdkBaseStore.getUid() +
                File.separator + FileCacheName.ADVERTISEMENT) + adId;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public String getWespydeeplink() {
        return wespydeeplink;
    }

    public void setWespydeeplink(String wespydeeplink) {
        this.wespydeeplink = wespydeeplink;
    }

    public boolean isImage() {
        return type == AD_TYPE_IMAGE;
    }

    public boolean isVideo() {
        return type == AD_TYPE_VIDEO;
    }

    public String getBarIconImg() {
        return barIconImg;
    }

    public void setBarIconImg(String barIconImg) {
        this.barIconImg = barIconImg;
    }

    public String getBarIconName() {
        return barIconName;
    }

    public void setBarIconName(String barIconName) {
        this.barIconName = barIconName;
    }

    public String getBarSlogan() {
        return barSlogan;
    }

    public void setBarSlogan(String barSlogan) {
        this.barSlogan = barSlogan;
    }

    public String getBarAdName() {
        return barAdName;
    }

    public void setBarAdName(String barAdName) {
        this.barAdName = barAdName;
    }

    public int getBarColor() {
        if (TextUtils.isEmpty(barColor)) {
            return Color.TRANSPARENT;
        }
        try {
            return Color.parseColor(barColor);
        } catch (Exception ignored) {
        }
        return Color.TRANSPARENT;
    }

    public void setBarColor(String barColor) {
        this.barColor = barColor;
    }

    public boolean isBarIsShow() {
        return barIsShow;
    }

    public void setBarIsShow(boolean barIsShow) {
        this.barIsShow = barIsShow;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    @Override
    public int compareTo(@NonNull AdvertisementEntity o) {
        return o.priority - priority;
    }
}
