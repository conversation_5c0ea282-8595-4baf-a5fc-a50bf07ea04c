package com.wepie.wespy.module.advertisement;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

public class AdvertisementList {
    @SerializedName("resources")
    public List<AdvertisementEntity> enityList = new ArrayList<>();
    @SerializedName("version")
    public int versionCode = 0;
    @SerializedName("md5_version")
    public String versionMD5 = "";
    @SerializedName("day")
    public long updateDay = Calendar.getInstance().get(Calendar.DAY_OF_YEAR);

    public List<AdvertisementEntity> getEnityList() {
        if (enityList != null)
            return enityList;
        else return new ArrayList<>();
    }

    public void setEnityList(List<AdvertisementEntity> enityList) {
        this.enityList = enityList;
    }
}
