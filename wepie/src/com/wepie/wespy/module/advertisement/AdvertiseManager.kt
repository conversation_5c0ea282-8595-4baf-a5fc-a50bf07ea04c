package com.wepie.wespy.module.advertisement

import com.huiwan.base.util.FileUtil
import com.huiwan.base.util.JsonUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.TimeUtil
import com.huiwan.platform.ThreadUtil
import com.huiwan.store.PrefUserUtil
import com.huiwan.store.SdkBaseStore
import com.huiwan.store.file.FileCacheName
import com.huiwan.store.file.FileConfig.getExtraFileFullPath
import com.three.http.constant.Config
import com.three.http.core.KtResultSuccess
import com.wepie.download.DownloadUtil
import com.wepie.liblog.main.FLog
import com.wepie.liblog.main.HLog
import com.wepie.wespy.net.http.api.AdvertisementApi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.IOException
import java.util.Calendar
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 管理广告资源
 *
 * <AUTHOR>
 * date 2020/09/21
 */
class AdvertiseManager {
    private var adList: MutableList<AdvertisementEntity> = CopyOnWriteArrayList() //所有广告列表
    private val adListNeedShow: MutableList<AdvertisementEntity> = ArrayList() //本地需要展示的广告列表
    private var version = 0
    private var versionMD5 = ""
    fun getAdList(): List<AdvertisementEntity> {
        return adList
    }

    fun getAdListNeedShow(): List<AdvertisementEntity> {
        return adListNeedShow
    }

    private val serviceFile = ThreadUtil.newSingleThreadPool("AdvertiseManager-File")
    private val serviceOther = ThreadUtil.newSingleThreadPool("AdvertiseManager-Other")
    val dispatcherFile = serviceFile.asCoroutineDispatcher()
    val dispatcherOther = serviceOther.asCoroutineDispatcher()

    suspend fun refreshAdInfo(uid: Int): Boolean {
        val result = AdvertisementApi.getAdvertisementList(
            uid, version, versionMD5, ScreenUtil.getScreenWidth(),
            ScreenUtil.getScreenHeight()
        )
        if (result !is KtResultSuccess) {
            return false
        }

        return withContext(dispatcherOther) {
            val json = JsonUtil.toObject(result.data.trim { it <= ' ' })
            val resultObj = JsonUtil.getObj(json, Config.json_data_key)
            var advertisementList =
                JsonUtil.fromJson(resultObj, AdvertisementList::class.java)

            val md5 = advertisementList?.versionMD5 ?: ""
            val version = advertisementList?.versionCode ?: 0
            if (!isNewVersion(md5, version)) {
                return@withContext true
            }

            val localList = withContext(dispatcherFile) {
                // 获取本地记录的AdvertisementEntity list
                getLocalAdList()
            }

            // 获取最新配置的值
            if (advertisementList == null) {
                advertisementList = AdvertisementList()
            }
            var list: MutableList<AdvertisementEntity>? = null
            list = advertisementList.getEnityList()
            if (list == null) {
                list = ArrayList()
            }

            // 更新本地记录的值
            if (!localList.isNullOrEmpty() && list.isNotEmpty()) {
                for (localAd in localList) {
                    list.forEachIndexed { index, entity ->
                        if (localAd.adId == entity.adId) {
                            entity.hasShowedTotal = localAd.hasShowedTotal
                            entity.showedPerDay = localAd.showedPerDay
                            entity.showCount -= localAd.hasShowedTotal
                        }
                        list[index] = entity
                    }
                }
            }
            advertisementList.enityList = list
            val content = JsonUtil.toJson(advertisementList)
            CoroutineScope(dispatcherFile).launch {
                FileUtil.writeFile(File(adConfigFilePath), content)
            }

            // 删除本地无用的开屏文件
            val elementsToRemove = mutableListOf<AdvertisementEntity>()
            adList.forEach { advertisementEntity ->
                list.forEach {
                    if (it.adId == advertisementEntity.adId) {
                        elementsToRemove.add(advertisementEntity)
                    }
                }
            }
            adList.removeAll(elementsToRemove)
            for (advertisementEntity in adList) {
                FileUtil.safeDeleteFile(File(advertisementEntity.adFileName))
            }
            adList = list

            val adListEmpty = adList.isEmpty()
            PrefUserUtil.getInstance().setBoolean(PrefUserUtil.HAS_ADVERTISE, !adListEmpty)
            HLog.d(TAG, HLog.USR, "refreshAdInfo, content={}, adListEmpty={}", content, adListEmpty)
            return@withContext true
        }
    }

    private fun getLocalAdList(): List<AdvertisementEntity>? {
        val advertisementList = getLocalAd()
        return if (advertisementList == null) emptyList() else advertisementList.getEnityList()
    }

    private fun getLocalAd(): AdvertisementList? {
        var advertisementList: AdvertisementList?
        val file = File(adConfigFilePath)
        try {
            if (file.length() > FILE_MAX_SIZE) {
                FileUtil.safeDeleteFile(file)
                return null
            }
            FileReader(adConfigFilePath).use { fileReader ->
                BufferedReader(fileReader).use { reader ->
                    advertisementList =
                        JsonUtil.getGson().fromJson(reader, AdvertisementList::class.java)
                }
            }
        } catch (e: IOException) {
            HLog.d(TAG, HLog.USR, "getLocalAd, e=$e")
            advertisementList = null
        } catch (e: Exception) {
            HLog.d(TAG, HLog.USR, "getLocalAd, e=$e")
            FileUtil.safeDeleteFile(file)
            advertisementList = null
        }
        return advertisementList
    }

    private fun isNewVersion(md5: String, versionCode: Int): Boolean {
        val flag: Boolean = !md5.equals(versionMD5, ignoreCase = true) || versionCode != version
        HLog.d(
            TAG, HLog.USR,
            "flag=$flag, md5=$md5, versionMD5=$versionMD5, versionCode=$versionCode, version=$version"
        )
        return flag
    }

    fun initLocalData(callback: Callback) {
        HLog.d(
            TAG, HLog.USR,
            "initLocalData enter, adList size={${adList.size}}"
        )
        CoroutineScope(dispatcherOther).launch {
            if (adList.isEmpty()) {
                val list = withContext(dispatcherFile) {
                    getLocalAd()
                }
                //本地已下载的广告列表
                val adListHasDownloaded: MutableList<AdvertisementEntity> = ArrayList()
                list?.let {
                    version = it.versionCode
                    versionMD5 = it.versionMD5
                    adList.addAll(it.getEnityList())
                    HLog.d(
                        TAG, HLog.USR,
                        "adList size=${adList.size} adList:${JsonUtil.toJson(adList)}"
                    )
                    for (entity in adList) {
                        try {
                            if (FileUtil.fileExists(entity.adFileName)) {
                                HLog.d(TAG, HLog.USR, "adFileName:${entity.adFileName}")
                                if (Calendar.getInstance()[Calendar.DAY_OF_YEAR].toLong() != it.updateDay) {
                                    entity.showedPerDay = 0
                                }
                                adListHasDownloaded.add(entity)
                            }
                        } catch (e: Exception) {
                            HLog.d(
                                TAG, HLog.USR,
                                "initLocalData enter, entity.adFileName = ${entity.adFileName}, e=$e"
                            )
                        }
                    }
                }
                val currentTime = TimeUtil.getServerTime() / 1000
                val adList: MutableList<AdvertisementEntity> = ArrayList()
                for (advertisementEntity in adListHasDownloaded) {
                    if (advertisementEntity.startDate < currentTime && advertisementEntity.endDate > currentTime
                        && advertisementEntity.limitPerDay > advertisementEntity.showedPerDay
                        && advertisementEntity.showCount > 0
                    ) {
                        adList.add(advertisementEntity)
                    }
                }
                adListNeedShow.clear()
                adListNeedShow.addAll(adList)
                callback.onInitOver(adListNeedShow)
            } else {
                callback.onInitOver(adListNeedShow)
            }
        }
    }

    fun saveViewedAdvert(advertisementEntity: AdvertisementEntity) {
        HLog.d(TAG, HLog.USR, "saveViewedAdvert enter")
        CoroutineScope(dispatcherOther).launch {
            advertisementEntity.showedPerDay += 1
            advertisementEntity.hasShowedTotal += 1
            advertisementEntity.showCount -= 1
            val indexOfEntity = adList.indexOf(advertisementEntity)
            if (indexOfEntity < 0) {
                return@launch
            }
            adList[indexOfEntity] = advertisementEntity
            val advertisementList = AdvertisementList()
            advertisementList.versionCode = version
            advertisementList.versionMD5 = versionMD5
            advertisementList.enityList = adList
            advertisementList.updateDay = Calendar.getInstance()[Calendar.DAY_OF_YEAR].toLong()
            CoroutineScope(dispatcherFile).launch {
                try {
                    FileUtil.writeFile(File(adConfigFilePath), JsonUtil.toJson(advertisementList))
                } catch (exception: Exception) {
                    FLog.e(
                        Exception(
                            "AdvertiseManager Gson OOM, adlist size=${advertisementList.getEnityList().size}",
                            exception
                        )
                    )
                }
            }
        }
    }

    val adConfigFilePath: String
        get() = getExtraFileFullPath(
            SdkBaseStore.getUid().toString() +
                    File.separator + FileCacheName.ADVERTISEMENT
        ) + "config"

    fun clear() {
        adList.clear()
    }

    interface RefreshCallback {
        fun onSuccess()
        fun onFail(msg: String?)
    }

    interface Callback {
        fun onInitOver(adListNeedShow: List<AdvertisementEntity>?)
    }

    companion object {
        private const val TAG = "AdvertiseManager"

        private const val FILE_MAX_SIZE = 10 * 1024 * 1024

        @JvmField
        val instance = AdvertiseManager()
        fun check2DownloadAdvertisement(advertisementEntityList: List<AdvertisementEntity>) {
            HLog.d(TAG, "check2DownloadAdvertisement! size={}", advertisementEntityList.size)
            for (advertisementEntity in advertisementEntityList) {
                DownloadUtil.downloadFile(
                    advertisementEntity.adUrl,
                    advertisementEntity.adFileName,
                    null
                )
            }
        }
    }
}
