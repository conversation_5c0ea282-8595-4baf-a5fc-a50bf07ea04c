package com.wepie.wespy.module.pay.commonapi

import android.os.Build
import android.os.Bundle
import android.widget.ImageView
import android.widget.RelativeLayout
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.StatusBarUtil
import com.huiwan.component.activity.BaseActivity
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil
import com.wepie.wespy.net.http.api.OrderApi.GoodsType

class GoodsListActivity : BaseActivity(), GoodsListRvHelper.UpdateCallback {

    private lateinit var viewPager: ViewPager2
    private lateinit var actionBar: RelativeLayout
    private lateinit var adapter: FragmentStateAdapter
    private val viewmodel: GoodsViewModel by viewModels<GoodsViewModel>()
    private val dialogUtil = ProgressDialogUtil()
    private lateinit var tabLayout: TabLayout
    private val trackEventParamMap = HashMap<String, Any>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        StatusBarUtil.initStatusBar(this)
        StatusBarUtil.setStatusFontDarkColor(this)
        window.setBackgroundDrawableResource(R.color.white_color)
        setContentView(R.layout.activity_new_goods)
        initView()
        initObserver()
        initData()
        var type = intent.getStringExtra(JumpPay.KEY_GOODS_TYPE)
        if (type == null) {
            type = GoodsType.DIAMOND
        }

        trackEventParamMap.clear()
        trackEventParamMap.putAll(
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                intent.getParcelableExtra(JumpPay.KEY_TRACK_PARAM, trackEventParamMap.javaClass)
            } else {
                intent.getParcelableExtra(JumpPay.KEY_TRACK_PARAM) as HashMap<String, Any>?
            } ?: emptyMap()
        )
        val pos = GoodsViewModel.findPosByCategory(type)
        viewPager.setCurrentItem(if (pos < 0) 0 else pos, false)
    }


    private fun initObserver() {
        viewmodel.loading.observe(this) {
            when (it) {
                true -> {
                    dialogUtil.showLoadingDelay(this)
                }

                false -> {
                    dialogUtil.hideLoading()
                }
            }
        }
    }

    private fun initData() {
        for (type in GoodsViewModel.typeList) {
            viewmodel.getGoodsList(type)
        }
        viewmodel.refreshUserInfo()
    }

    private fun initView() {
        tabLayout = findViewById(R.id.tab_layout)
        val backImv = findViewById<ImageView>(R.id.back_imv)
        viewPager = findViewById(R.id.goods_view_viewpager)
        actionBar = findViewById(R.id.action_bar)
        updateActionBarMargin()
        backImv.setOnClickListener { finish() }
        adapter = object : FragmentStateAdapter(this) {

            override fun getItemCount(): Int {
                return GoodsViewModel.getCategorySize()
            }

            override fun createFragment(position: Int): Fragment {
                return GoodsFragment.create(position, trackEventParamMap, this@GoodsListActivity)
            }

        }
        viewPager.setAdapter(adapter)

        val titleStr = arrayOf(
            ResUtil.getStr(R.string.goods_title_gold),
            ResUtil.getStr(R.string.goods_title_diamond)
        )
        TabLayoutMediator(tabLayout, viewPager) { tab: TabLayout.Tab, position: Int ->
            tab.setText(titleStr[position])
        }.attach()

    }

    private fun updateActionBarMargin() {
        val actionBarLayoutParams = actionBar.layoutParams as RelativeLayout.LayoutParams
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            actionBarLayoutParams.setMargins(0, ScreenUtil.getStatusBarHeight(), 0, 0)
        }
        actionBar.setLayoutParams(actionBarLayoutParams)
    }

    override fun onSuccess(payResult: WpPayResult?) {
    }

    override fun onFail() {
    }

    override fun onNeedUpdate() {
        initData()
    }

}


