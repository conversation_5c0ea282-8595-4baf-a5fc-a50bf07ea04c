package com.wepie.wespy.module.voiceroom.hotbadge;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseDialogFragment;

/**
 * 热度徽章奖励预发放弹窗
 * <AUTHOR>
 *
 * date 2021-10-08
 */
public class HotBadgeSendDialog extends BaseDialogFragment {
    private TextView topTv;
    private TextView contentTv;
    private TextView sureTv;
    private TextView cancelTv;

    private Callback cb;
    private String content;
    private int hotNum;


    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        View v = inflater.inflate(R.layout.hot_badge_send_dialog_view, container, false);
        initViews(v);
        updateBottomTv();
        updateCancelable(false);
        updateView();
        return v;
    }

    private void initViews(View v) {
        topTv = v.findViewById(R.id.top_tv);
        contentTv = v.findViewById(R.id.content_tv);
        sureTv = v.findViewById(R.id.sure_tv);
        cancelTv = v.findViewById(R.id.cancel_tv);
    }

    private void updateBottomTv() {
        sureTv.setOnClickListener(v -> {
            cb.onClickSure();
            dismissAllowingStateLoss();
        });

        cancelTv.setOnClickListener(v -> dismissAllowingStateLoss());
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
    }

    public static void show(Context context, String text, int hotNum, Callback cb) {
        HotBadgeSendDialog dialog = new HotBadgeSendDialog();
        dialog.cb = cb;
        dialog.content = text;
        dialog.hotNum = hotNum;
        dialog.show(context);
    }

    @SuppressLint("SetTextI18n")
    private void updateView() {
        if (!TextUtils.isEmpty(content)) {//服务器控制这里显示
            contentTv.setText(content);
        }
        topTv.setText(ResUtil.getStr(R.string.hot_badge_dialog_tips1, hotNum));
    }

    interface Callback {
        void onClickSure();
    }
}
