package com.wepie.wespy.module.voiceroom.hotbadge;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.anim.FrameAnimation;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.constentity.WeddingConfig;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.RoomHeatValueEvent;
import com.wepie.wespy.module.voiceroom.dataservice.HeatCallback;
import com.wepie.wespy.module.voiceroom.dataservice.RoomCallback;
import com.wepie.wespy.module.voiceroom.dataservice.RoomSenderPresenter;
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginFrameLayout;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IWeddingHot;
import com.wepie.wespy.module.voiceroom.wedding.WeddingConfigManager;
import com.wepie.wespy.module.voiceroom.wedding.utils.HeartView;
import com.wepie.wespy.module.voiceroom.wedding.utils.HeatContributeDialog;
import com.wepie.wespy.net.http.api.ConstApi;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.ref.WeakReference;
import java.util.List;
import java.util.Map;

/**
 * 热度徽章控件
 *
 * <AUTHOR>
 *
 * date 2020-10-08
 */
public class HotBadgeView extends PluginFrameLayout implements IWeddingHot, View.OnClickListener {
    private static final int[] HEART_CLICK_FRAME_RES = {
            R.drawable.heart_click_91, R.drawable.heart_click_92, R.drawable.heart_click_93,
            R.drawable.heart_click_94, R.drawable.heart_click_95, R.drawable.heart_click_96,
            R.drawable.heart_click_97, R.drawable.heart_click_98, R.drawable.heart_click_99,
            R.drawable.heart_click_100, R.drawable.heart_click_101, R.drawable.heart_click_102,
            R.drawable.heart_click_103, R.drawable.heart_click_104, R.drawable.heart_click_105,
            R.drawable.heart_click_106, R.drawable.heart_click_107, R.drawable.heart_click_108,
            R.drawable.heart_click_109, R.drawable.heart_click_110, R.drawable.heart_click_111,
            R.drawable.heart_click_112
    };

    private static final int[] HEART_HOTS_RES = {
            R.drawable.hot_badge_level1,
            R.drawable.hot_badge_level2,
            R.drawable.hot_badge_level3,
            R.drawable.hot_badge_level4,
            R.drawable.hot_badge_level5,
            R.drawable.hot_badge_level6
    };

    private static final int PENG_DELAY_MSG_ID = 0x100;

    private ImageView heartBgIv;
    private HeartView heartView;
    private TextView hotTv;
    private ImageView heartAnimIv;

    private FrameAnimation anim;
    private AnimatorSet pengPengPengAnim;

    private int curHot;
    private int curLevel;
    private VoiceRoomInfo roomInfo;
    private WeddingConfig weddingConfig;

    public HotBadgeView(@NonNull Context context) {
        this(context, null);
    }

    public HotBadgeView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void initView() {
        LayoutInflater.from(mContext).inflate(R.layout.hot_badge_view, this);
        heartBgIv = findViewById(R.id.heart_bg_iv);
        heartView = findViewById(R.id.heart_view);
        hotTv = findViewById(R.id.hot_value_tv);
        heartAnimIv = findViewById(R.id.heart_anim_iv);

        initEvent();
    }

    @Override
    protected void initData() {
        update(getMainPlugin().getRoomInfo());
    }

    @Override
    public void update(VoiceRoomInfo roomInfo) {
        this.roomInfo = roomInfo;
        if (!roomInfo.inHotBadge) {
            setVisibility(GONE);
            return;
        } else {
            setVisibility(VISIBLE);
        }
        curHot = roomInfo.heatInfo.value;
        curLevel = roomInfo.heatInfo.level;
        updateViews();
    }

    private void initEvent() {
        heartBgIv.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (v == heartBgIv) {
            getBadgeConfig(new WeddingConfigManager.Callback() {
                @Override
                public void onSuccess(WeddingConfig weddingConfig) {
                    tryReqUpperLevel(weddingConfig);
                }

                @Override
                public void onFail(String msg) {
                    ToastUtil.show(msg);
                }
            });
        }
    }

    private void tryReqUpperLevel(WeddingConfig weddingConfigs) {
        if (curLevel < weddingConfigs.config.size() && curLevel >= 0) {
            float end = weddingConfigs.config.get(curLevel).heatSection;
            if (curHot >= end) {
                if (roomInfo.pkInfo.isInPk()) {
                    ToastUtil.show(R.string.PK_is_progressing_wait);
                } else if (roomInfo.isWeddingRoom() && roomInfo.weddingInfo.isPastorState()) {
                    ToastUtil.show(R.string.priest_is_progressing_wait);
                } else {
                    showHeartBoomAnim();
                    if (isOwner()) {
                        String text = weddingConfigs.config.get(curLevel).heatRewardToast;
                        if (TextUtils.isEmpty(text)) {
                            reqSysRp();
                        } else {
                            HotBadgeSendDialog.show(getContext(), text, weddingConfigs.config.get(curLevel).heatSection, this::reqSysRp);
                        }
                    } else {
                        ToastUtil.show(R.string.remind_owners_ewards);
                    }
                }
            } else {
                showContributeDialog(weddingConfigs.heatHelpH5);
            }
        } else {
            showContributeDialog(weddingConfigs.heatHelpH5);
        }
    }

    private boolean isOwner() {
        if (roomInfo.isFamilyRoom()) {
            return roomInfo.isSelfOwner();
        }
        return false;
    }

    private void reqSysRp() {
        final ProgressDialogUtil progressDialogUtil = new ProgressDialogUtil();
        progressDialogUtil.showLoadingDelay(getContext());
        RoomSenderPresenter.sendSystemRpReq(roomInfo.rid, new RoomCallback(this) {
            @Override
            public void onSuccess(int rid) {
                hidePengPengPengAnim();
                progressDialogUtil.hideLoading();
            }

            @Override
            public void onFail(String msg) {
                ToastUtil.show(msg);
                progressDialogUtil.hideLoading();
            }
        });
    }

    private void showContributeDialog(String url) {
        RoomSenderPresenter.sendGetContributeReq(roomInfo.rid, new HeatCallback() {
            @Nullable
            @Override
            public ILife getLife() {
                return ILifeUtil.toLife(HotBadgeView.this);
            }

            @Override
            public void onSuccess(Map<Integer, Integer> map) {
                HeatContributeDialog.show(getContext(), map, roomInfo.rid, url, true);
            }

            @Override
            public void onFailed(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    private void updateWaveHeight(List<WeddingConfig.HeatInfo> weddingConfigs) {
        hotTv.setText(String.valueOf(curHot));
        float percent = 1f;
        if (curLevel < weddingConfigs.size()) {
            float start = 0;
            if (curLevel != 0) {
                start = weddingConfigs.get(curLevel - 1).heatSection;
            }
            float end = weddingConfigs.get(curLevel).heatSection;
            if (curHot < end && curHot >= start) {
                percent = ((float) curHot - start) / (end - start);
                hidePengPengPengAnim();
            } else if (curHot >= end) {
                showHeartPengPengPengAnim();
            }
        } else {
            hidePengPengPengAnim();
        }
        heartView.updateWaveHeight(percent);
    }


    private final Handler pengHandler = new PengHandler(this);

    private void showHeartPengPengPengAnim() {
        if (pengPengPengAnim == null) {
            ObjectAnimator a1x = ObjectAnimator.ofFloat(heartBgIv, "scaleX", 1.0f, 1.2f).setDuration(150);
            ObjectAnimator a1y = ObjectAnimator.ofFloat(heartBgIv, "scaleY", 1.0f, 1.2f).setDuration(150);
            ObjectAnimator a2x = ObjectAnimator.ofFloat(heartBgIv, "scaleX", 1.2f, 0.94f).setDuration(70);
            ObjectAnimator a2y = ObjectAnimator.ofFloat(heartBgIv, "scaleY", 1.2f, 0.94f).setDuration(70);
            ObjectAnimator a3x = ObjectAnimator.ofFloat(heartBgIv, "scaleX", 0.94f, 1.05f).setDuration(80);
            ObjectAnimator a3y = ObjectAnimator.ofFloat(heartBgIv, "scaleY", 0.94f, 1.05f).setDuration(80);
            ObjectAnimator a4x = ObjectAnimator.ofFloat(heartBgIv, "scaleX", 1.05f, 1f).setDuration(200);
            ObjectAnimator a4y = ObjectAnimator.ofFloat(heartBgIv, "scaleY", 1.05f, 1f).setDuration(200);
            ObjectAnimator a1ax = ObjectAnimator.ofFloat(heartView, "scaleX", 1.0f, 1.2f).setDuration(150);
            ObjectAnimator a1ay = ObjectAnimator.ofFloat(heartView, "scaleY", 1.0f, 1.2f).setDuration(150);
            ObjectAnimator a2ax = ObjectAnimator.ofFloat(heartView, "scaleX", 1.2f, 0.94f).setDuration(70);
            ObjectAnimator a2ay = ObjectAnimator.ofFloat(heartView, "scaleY", 1.2f, 0.94f).setDuration(70);
            ObjectAnimator a3ax = ObjectAnimator.ofFloat(heartView, "scaleX", 0.94f, 1.05f).setDuration(80);
            ObjectAnimator a3ay = ObjectAnimator.ofFloat(heartView, "scaleY", 0.94f, 1.05f).setDuration(80);
            ObjectAnimator a4ax = ObjectAnimator.ofFloat(heartView, "scaleX", 1.05f, 1f).setDuration(200);
            ObjectAnimator a4ay = ObjectAnimator.ofFloat(heartView, "scaleY", 1.05f, 1f).setDuration(200);
            AnimatorSet a1 = new AnimatorSet();
            a1.playTogether(a1x, a1y, a1ax, a1ay);
            AnimatorSet a2 = new AnimatorSet();
            a2.playTogether(a2x, a2y, a2ax, a2ay);
            AnimatorSet a3 = new AnimatorSet();
            a3.playTogether(a3x, a3y, a3ax, a3ay);
            AnimatorSet a4 = new AnimatorSet();
            a4.playTogether(a4x, a4y, a4ax, a4ay);
            pengPengPengAnim = new AnimatorSet();
            pengPengPengAnim.playSequentially(a1, a2, a3, a4);
            pengPengPengAnim.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {

                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    pengHandler.sendEmptyMessageDelayed(PENG_DELAY_MSG_ID, 500);
                }

                @Override
                public void onAnimationCancel(Animator animation) {

                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });
        }
        if (!pengHandler.hasMessages(PENG_DELAY_MSG_ID)) {
            pengPengPengAnim.start();
        }
    }

    private void hidePengPengPengAnim() {
        if (pengPengPengAnim != null) {
            pengPengPengAnim.cancel();
            pengHandler.removeMessages(PENG_DELAY_MSG_ID);
        }
    }

    private void showHeartBoomAnim() {
        if (anim != null) {
            anim.release();
        }
        anim = new FrameAnimation(heartAnimIv, HEART_CLICK_FRAME_RES, 1000 / 30, false);
    }

    private void updateHeartRes(List<WeddingConfig.HeatInfo> weddingConfigs) {
        int showLevel = curLevel;
        if (showLevel < 0) {
            showLevel = 0;
        } else if (showLevel >= weddingConfigs.size()) {
            showLevel = weddingConfigs.size() - 1;
        }

        if (showLevel != curLevel && showLevel != 0 && curLevel != weddingConfigs.size()) {
            showHeartBoomAnim();
        }
        //本地缓存优化
        if (showLevel < HEART_HOTS_RES.length) {
            heartBgIv.setImageResource(HEART_HOTS_RES[showLevel]);
        }
        ImageLoaderUtil.loadNormalImage(weddingConfigs.get(showLevel).heatIcon, heartBgIv);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onHeatValueEvent(RoomHeatValueEvent event) {
        curHot = event.heat;
        curLevel = event.level;
        updateViews();
    }

    private void updateViews() {
        if (weddingConfig != null) {
            updateHeartRes(weddingConfig.config);
            updateWaveHeight(weddingConfig.config);
        }
        getBadgeConfig(new WeddingConfigManager.Callback() {
            @Override
            public void onSuccess(WeddingConfig serverConfig) {
                weddingConfig = serverConfig;
                updateHeartRes(weddingConfig.config);
                updateWaveHeight(weddingConfig.config);
            }

            @Override
            public void onFail(String msg) {
            }
        });
    }

    private void getBadgeConfig(WeddingConfigManager.Callback callback) {
        ConstApi.getHotBadgeConfig(new LifeDataCallback<WeddingConfig>(this) {
            @Override
            public void onSuccess(Result<WeddingConfig> result) {
                if (callback != null) callback.onSuccess(result.data);
            }

            @Override
            public void onFail(int code, String msg) {
                if (callback != null) callback.onFail(msg);
            }
        });
    }


    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        if (anim != null) {
            anim.release();
        }
        hidePengPengPengAnim();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        super.onDetachedFromWindow();
    }

    private static class PengHandler extends Handler {
        private WeakReference<HotBadgeView> weakReference;

        PengHandler(HotBadgeView weddingHotView) {
            this.weakReference = new WeakReference<>(weddingHotView);
        }

        @Override
        public void handleMessage(Message msg) {
            HotBadgeView weddingHotView = weakReference.get();
            if (weddingHotView != null) {
                handleMsg(weddingHotView, msg);
            }
        }

        private void handleMsg(HotBadgeView utilsView, Message msg) {
            if (msg.what == PENG_DELAY_MSG_ID) {
                utilsView.showHeartPengPengPengAnim();
            }
        }
    }
}
