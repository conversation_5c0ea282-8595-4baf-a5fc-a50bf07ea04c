package com.wepie.wespy.module.voiceroom.audiomatch;

import android.graphics.Camera;
import android.graphics.Matrix;
import android.view.animation.Animation;
import android.view.animation.Transformation;

public class RotateAnim extends Animation {
    private float startDegree;
    private float endDegree;
    private float centerX;
    private float centerY;
    private Camera mCamera;
    private float deepZ;
    private boolean mReverse;

    public enum DIRECTION {
        X, Y,
    }

    DIRECTION direction = DIRECTION.Y;

    public RotateAnim(float startDegree, float endDegree, float deepZ, boolean mReverse, DIRECTION direction) {
        this.startDegree = startDegree;
        this.endDegree = endDegree;
        this.deepZ = deepZ;
        this.mReverse = mReverse;
        this.direction = direction;
    }

    @Override
    public void initialize(int width, int height, int parentWidth, int parentHeight) {
        super.initialize(width, height, parentWidth, parentHeight);
        mCamera = new Camera();
        centerX = width >> 1;
        centerY = height >> 1;
    }

    @Override
    protected void applyTransformation(float interpolatedTime, Transformation t) {
        super.applyTransformation(interpolatedTime, t);
        float fromDegree = startDegree;
        float degree = fromDegree + (endDegree - startDegree) * interpolatedTime;
        final Matrix matrix = t.getMatrix();
        mCamera.save();
        if (mReverse) {
            mCamera.translate(0, 0, deepZ * interpolatedTime);
        } else {
            mCamera.translate(0, 0, deepZ * (1 - interpolatedTime));
        }
        if (direction == DIRECTION.Y) {
            mCamera.rotateY(degree);
        } else {
            mCamera.rotateX(degree);
        }
        mCamera.getMatrix(matrix);
        mCamera.restore();
        matrix.preTranslate(-centerX, -centerY);
        matrix.postTranslate(centerX, centerY);
    }
}
