package com.wepie.wespy.module.voiceroom.treasure

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.configservice.constentity.TreasureConfig.TreasureBox
import com.wepie.wespy.R
import java.util.ArrayList

class TreasureBoxAdapter : RecyclerView.Adapter<TreasureHolder>() {
    val datas: MutableList<List<TreasureBox>> = ArrayList()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TreasureHolder {
        if (viewType == TreasureBox.ONE_ITEM) {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.treasure_box_item, parent, false)
            return TreasureSingleHolder(view)
        } else {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.treasure_box_two_item, parent, false)
            return TreasureDoubleHolder(view)
        }
    }

    override fun onBindViewHolder(holder: TreasureHolder, position: Int) {
        holder.bindData(datas[position])
    }

    override fun getItemCount(): Int {
        return datas.size
    }

    override fun getItemViewType(position: Int): Int {
        return if (datas[position].size >= 2) TreasureBox.TWO_ITEM else TreasureBox.ONE_ITEM
    }

    fun updateData(d: List<List<TreasureBox>>) {
        TreasureCountDown.cancel()
        datas.clear()
        datas.addAll(d)
        notifyDataSetChanged()
    }
}