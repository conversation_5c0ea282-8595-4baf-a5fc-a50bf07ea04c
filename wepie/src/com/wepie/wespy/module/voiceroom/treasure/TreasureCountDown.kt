package com.wepie.wespy.module.voiceroom.treasure

import android.os.CountDownTimer
import com.wepie.liblog.main.HLog
import java.util.concurrent.TimeUnit

class TreasureCountDown(millisInFuture: Long, countDownInterval: Long) :
    CountDownTimer(millisInFuture, countDownInterval) {
    var callBack: CountDownCallBack? = null

    override fun onTick(millisUntilFinished: Long) {
        callBack?.onTick(millisUntilFinished)
    }

    override fun onFinish() {
        callBack?.onFinish()
    }

    interface CountDownCallBack {
        fun onTick(millisUntilFinished: Long)
        fun onFinish()
    }

    companion object {
        const val TAG = "TreasureCountDown"
        var countDownTimer: TreasureCountDown? = null

        fun start(millisInFuture: Long, callBack: CountDownCallBack) {
            HLog.d(TAG, "start millisInFuture=$millisInFuture")
            val leftTime = if (millisInFuture < 0) {
                0
            } else {
                millisInFuture
            }
            cancel()
            countDownTimer = TreasureCountDown(leftTime, 1000)
            countDownTimer?.let {
                it.callBack = callBack
                it.start()
            }
        }

        fun cancel() {
            HLog.d(TAG,"cancel")
            countDownTimer?.let {
                it.cancel()
                it.callBack = null
            }
        }

        fun format(milliseconds: Long): String {
            val minutes = TimeUnit.MILLISECONDS.toMinutes(milliseconds)
            val seconds = TimeUnit.MILLISECONDS.toSeconds(milliseconds)
            val leftSeconds = seconds - TimeUnit.MILLISECONDS.toSeconds(minutes * 60 * 1000)
            val minutesStr: String = if (minutes < 10) {
                "0${minutes}"
            } else {
                "$minutes"
            }
            val secondsStr: String = if (leftSeconds < 10) {
                "0${leftSeconds}"
            } else {
                "$leftSeconds"
            }
            return "$minutesStr:$secondsStr"
        }
    }
}