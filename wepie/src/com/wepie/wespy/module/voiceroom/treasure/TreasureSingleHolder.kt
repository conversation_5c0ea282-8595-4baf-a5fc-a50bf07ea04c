package com.wepie.wespy.module.voiceroom.treasure

import android.view.View
import com.huiwan.configservice.constentity.TreasureConfig.TreasureBox
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.module.voiceroom.treasure.TreasureCountDown.CountDownCallBack

class TreasureSingleHolder(itemView: View) : TreasureHolder(itemView) {
    private val TAG = "TreasureOneHolder"
    private lateinit var root: View
    private var isVIPLimit = false

    init {
        initView()
    }

    private fun initView() {
        root = itemView.findViewById(R.id.root)
    }

    override fun bindData(treasureBoxes: List<TreasureBox>) {
        treasureBoxes.forEach {
            updateInfo(it)
        }
        HLog.d(TAG, HLog.USR, "bindData size=${treasureBoxes.size}")
    }

    private fun updateInfo(treasureBox: TreasureBox) {
        TreasureHolderHelper.updateBoxStatus(
            if (treasureBox.isVip) {
                isVIPLimit = TreasureUtil.isVIPLimit(treasureBox)
                root
            } else {
                root
            }, treasureBox, callBack
        )
    }

    val callBack = object : CountDownCallBack {
        override fun onTick(millisUntilFinished: Long) {
            updateTimer(millisUntilFinished)
        }

        override fun onFinish() {
            updateTimer(0)
        }
    }

    private fun updateTimer(leftMilliseconds: Long) {
        val text = TreasureCountDown.format(leftMilliseconds)
        if (!isVIPLimit) {
            TreasureHolderHelper.updateHint(root, "#FFFFFFFF", text, 0)
        }
    }

}