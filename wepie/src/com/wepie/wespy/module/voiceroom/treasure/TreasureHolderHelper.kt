package com.wepie.wespy.module.voiceroom.treasure

import android.graphics.Color
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.huiwan.base.str.ResUtil
import com.huiwan.configservice.constentity.TreasureConfig
import com.huiwan.configservice.constentity.TreasureConfig.TreasureBox
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.model.event.voice.VoiceTreasureEvent
import org.greenrobot.eventbus.EventBus

object TreasureHolderHelper {
    private val TAG = "TreasureHolderHelper"

    fun updateBoxStatus(
        root: View,
        treasureBox: TreasureConfig.TreasureBox,
        callBack: TreasureCountDown.CountDownCallBack
    ) {
        if (TreasureUtil.isVIPLimit(treasureBox)) {
            update(
                root,
                treasureBox,
                TreasureBox.BOX_STATUS_UN_OPEN,
                "#66FFFFFF",
                ResUtil.getStr(R.string.treasure_vip_limit),
                0
            )
        } else {
            updateNormalStatus(root, treasureBox, callBack)
        }
    }

    private fun updateNormalStatus(
        root: View,
        treasureBox: TreasureConfig.TreasureBox,
        callBack: TreasureCountDown.CountDownCallBack
    ) {
        when (treasureBox.status) {
            TreasureBox.CAN_GET -> {
                update(
                    root,
                    treasureBox,
                    TreasureBox.BOX_STATUS_CAN_OPEN,
                    "#FFFFFF",
                    ResUtil.getStr(R.string.treasure_get),
                    R.drawable.treasure_box_text_bg
                )
                addClickListener(root, treasureBox)
            }
            TreasureBox.NEED_COUNT -> {
                update(
                    root,
                    treasureBox,
                    TreasureBox.BOX_STATUS_UN_OPEN,
                    "#FFFFFF",
                    "00:00",
                    0
                )
                startCountDown(treasureBox, callBack)
            }
            TreasureBox.PREPARING -> {
                update(
                    root,
                    treasureBox,
                    TreasureBox.BOX_STATUS_UN_OPEN,
                    "#66FFFFFF",
                    ResUtil.getStr(R.string.treasure_prepare),
                    0
                )
            }
            TreasureBox.GOT -> {
                update(
                    root,
                    treasureBox,
                    TreasureBox.BOX_STATUS_OPEN,
                    "#66FFFFFF",
                    ResUtil.getStr(R.string.treasure_got),
                    0
                )
            }
            else -> {
                HLog.d(TAG, HLog.USR, "bindData error! status=${treasureBox.status}")
            }
        }
    }

    private fun addClickListener(root: View, treasureBox: TreasureConfig.TreasureBox) {
        val boxHint = root.findViewById<TextView>(R.id.box_hint)
        boxHint.setOnClickListener {
            sendBoxEvent(treasureBox, VoiceTreasureEvent.GET_TREASURE)
        }
    }

    private fun startCountDown(
        treasureBox: TreasureConfig.TreasureBox,
        callBack: TreasureCountDown.CountDownCallBack
    ) {
        val leftTime = (treasureBox.cond - treasureBox.progress) * 1000L
        TreasureCountDown.start(
            leftTime,
            object : TreasureCountDown.CountDownCallBack {
                override fun onTick(millisUntilFinished: Long) {
                    callBack.onTick(millisUntilFinished)
                }

                override fun onFinish() {
                    callBack.onFinish()
                    sendBoxEvent(treasureBox, VoiceTreasureEvent.TREASURE_REFRESH)
                }
            })
    }

    fun sendBoxEvent(treasureBox: TreasureConfig.TreasureBox?, eventType: Int) {
        val event = VoiceTreasureEvent(eventType)
        treasureBox?.let {
            event.boxID = it.id
        }
        EventBus.getDefault().post(event)
    }

    private fun update(
        root: View,
        treasureBox: TreasureBox,
        boxStatus: Int,
        textColor: String,
        text: String,
        textBg: Int
    ) {
        updateIcon(root, treasureBox.isVip, boxStatus)
        updateHint(root, textColor, text, textBg)
    }

    private fun updateIcon(root: View, isVIP: Boolean, boxStatus: Int) {
        val boxIcon = root.findViewById<ImageView>(R.id.icon)
        val res: Int = when (boxStatus) {
            TreasureBox.BOX_STATUS_CAN_OPEN -> {
                if (isVIP) {
                    R.drawable.treasure_vip_box_can_open
                } else {
                    R.drawable.treasure_box_can_open
                }
            }
            TreasureBox.BOX_STATUS_OPEN -> {
                if (isVIP) {
                    R.drawable.treasure_vip_box_open
                } else {
                    R.drawable.treasure_box_open
                }
            }
            else -> {
                if (isVIP) {
                    R.drawable.treasure_vip_box_un_open
                } else {
                    R.drawable.treasure_box_un_open
                }
            }
        }
        boxIcon.setImageResource(res)
    }

    fun updateHint(root: View, textColor: String, text: String, bg: Int) {
        val boxHint = root.findViewById<TextView>(R.id.box_hint)
        boxHint.setTextColor(Color.parseColor(textColor))
        boxHint.text = text
        boxHint.setBackgroundResource(bg)
    }

}