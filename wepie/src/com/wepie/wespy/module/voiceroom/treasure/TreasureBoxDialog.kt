package com.wepie.wespy.module.voiceroom.treasure

import android.graphics.Color
import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.component.activity.BaseFragment
import com.huiwan.configservice.constentity.TreasureConfig
import com.huiwan.store.PrefUserUtil
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.api.plugins.track.config.os.TrackSource
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.helper.shence.ShenceEvent
import com.wepie.wespy.model.event.voice.VoiceTreasureEvent
import com.wepie.wespy.module.voiceroom.main.bottom.BottomViewModel
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginUtil
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IRoomBottomPlugin

// https://www.tapd.cn/36010968/prong/stories/view/1136010968001015239
class TreasureBoxDialog : BaseFragment() {
    lateinit var adapter: TreasureBoxAdapter
    lateinit var taskFollowLayout: View
    lateinit var taskIntroduce: TextView
    lateinit var taskGiftLayout: View
    lateinit var taskMsgLayout: View

    val lastReportBoxes: MutableList<Int> = ArrayList()
    var isFirstReport = true

    lateinit var model: BottomViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.treasure_box_dialog_layout, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView(view)
        model = ViewModelProvider(requireActivity()).get(BottomViewModel::class.java)
        refresh()
    }

    private fun initView(view: View) {
        val treasureBoxRecyclerView: RecyclerView =
            view.findViewById(R.id.treasure_box_recyclerview)
        val layoutManager = GridLayoutManager(context, COLUMN_COUNT)
        layoutManager.spanSizeLookup = TreasureSpanSizeLookup(COLUMN_COUNT)
        treasureBoxRecyclerView.layoutManager = layoutManager
        adapter = TreasureBoxAdapter()
        treasureBoxRecyclerView.adapter = adapter
        treasureBoxRecyclerView.addItemDecoration(TreasureItemDecoration(COLUMN_COUNT))

        taskFollowLayout = view.findViewById(R.id.task_room_follow)
        taskGiftLayout = view.findViewById(R.id.task_send_gift)
        taskMsgLayout = view.findViewById(R.id.task_send_msg)
        taskIntroduce = view.findViewById(R.id.task_introduce)
        taskIntroduce.text = Html.fromHtml(ResUtil.getStr(R.string.treasure_task_desc))
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (hidden) {
            PrefUserUtil.getInstance().setBoolean(PrefUserUtil.KEY_FIRST_ENTER_VOICE_ROOM, false)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        TreasureCountDown.cancel()
    }

    private fun refresh() {
        model.treasureConfigLiveData.observe(viewLifecycleOwner) {
            addVIPTag(it)
//            this.config = it
            updateTaskInfo(it.taskInfos)
            updateTreasureBox(it.treasureBoxes)
        }
        TreasureHolderHelper.sendBoxEvent(null, VoiceTreasureEvent.TREASURE_REFRESH)
    }

    private fun addVIPTag(config: TreasureConfig) {
        config.treasureBoxes.forEach { it ->
            it.forEach {
                it.isUserVIP = config.isVIP
            }
        }
    }

    private fun updateTreasureBox(treasureBoxes: List<List<TreasureConfig.TreasureBox>>) {
        adapter.updateData(treasureBoxes)
        showReport(treasureBoxes)
    }

    private fun updateTaskInfo(taskInfos: List<TreasureConfig.TaskInfo>) {
        taskInfos.forEach {
            updateTask(it)
        }
    }

    private fun updateTask(taskInfo: TreasureConfig.TaskInfo) {
        when (taskInfo.id) {
            TreasureConfig.TaskInfo.VOICE_ROOM_FOLLOW_TASK -> {
                updateTaskDetail(
                    taskFollowLayout,
                    R.drawable.treasure_task_follow,
                    R.string.treasure_task_follow,
                    taskInfo
                )
            }
            TreasureConfig.TaskInfo.SEND_GIFT_TASK -> {
                updateTaskDetail(
                    taskGiftLayout,
                    R.drawable.treasure_task_gift,
                    R.string.treasure_task_gift,
                    taskInfo
                )
            }
            TreasureConfig.TaskInfo.SEND_MSG_TASK -> {
                updateTaskDetail(
                    taskMsgLayout,
                    R.drawable.treasure_task_msg,
                    R.string.treasure_task_msg,
                    taskInfo
                )
            }
            else -> {
                HLog.d(TAG, HLog.USR, "updateTask error! id=${taskInfo.id}")
            }
        }
    }

    private fun updateTaskDetail(
        root: View,
        imgRes: Int,
        descText: Int,
        taskInfo: TreasureConfig.TaskInfo
    ) {
        val iconView = root.findViewById<ImageView>(R.id.task_icon)
        val descView = root.findViewById<TextView>(R.id.task_desc)
        val statusView = root.findViewById<TextView>(R.id.task_status)
        iconView.setImageResource(imgRes)
        descView.text = ResUtil.getStr(descText)
        val textRes: Int
        val textColor: Int
        val textBackground: Int
        if (taskInfo.status == TreasureConfig.TaskInfo.COMPLETED) {
            textRes = R.string.treasure_complete
            textColor = Color.parseColor("#4DFFFFFF")
            textBackground = R.drawable.treasure_task_complete_bg
        } else {
            textRes = R.string.treasure_un_complete
            textBackground = R.drawable.treasure_task_un_complete_bg
            textColor = ResUtil.getColor(R.color.color_accent)
            statusView.setOnClickListener {
                handleTaskClick(taskInfo.id)
            }
        }
        statusView.setText(textRes)
        statusView.setBackgroundResource(textBackground)
        statusView.setTextColor(textColor)
    }

    private fun handleTaskClick(taskID: Int) {
        close()
        when (taskID) {
            TreasureConfig.TaskInfo.VOICE_ROOM_FOLLOW_TASK -> {
                handleFollowTask()
            }
            TreasureConfig.TaskInfo.SEND_GIFT_TASK -> {
                handleSendGiftTask()
            }
            TreasureConfig.TaskInfo.SEND_MSG_TASK -> {
                VoicePluginService.getPlugin(IRoomBottomPlugin::class.java)
                    .showMsgSend()
            }
            else -> {
                HLog.d(TAG, HLog.USR, "handleTaskClick error! taskID=${taskID}")
            }
        }
    }

    private fun handleFollowTask() {
        PluginUtil.getMainPlugin(context).showRoomInfoDialog()
    }

    private fun handleSendGiftTask() {
        PluginUtil.getMainPlugin(context).sendOwnerGift()
    }

    private fun close() {
        model.setFragmentState(0)
    }

    private fun showReport(treasureBoxes: List<List<TreasureConfig.TreasureBox>>?) {
        val currentBoxes: MutableList<Int> = getAvailableList(treasureBoxes)
        val dataChange = !lastReportBoxes.containsAll(currentBoxes)
        HLog.d(TAG, "showReport , dataChange=$dataChange isFirstReport=$isFirstReport")
        if (dataChange || isFirstReport) {
            lastReportBoxes.clear()
            lastReportBoxes.addAll(currentBoxes)
            val params: MutableMap<String, Any> = HashMap()
            params["screen_sub_name"] = TrackSource.TASK_DIALOG
            params["available_box_list"] = lastReportBoxes.toString()
            ShenceEvent.appViewScreen(TrackScreenName.VOICE_ROOM_CHAT, params)
            isFirstReport = false
        }
    }

    private fun getAvailableList(treasureBoxes: List<List<TreasureConfig.TreasureBox>>?): MutableList<Int> {
        val list: MutableList<Int> = ArrayList()
        treasureBoxes?.forEach { it ->
            it.forEach {
                if (it.status == TreasureConfig.TreasureBox.CAN_GET) {
                    list.add(it.id)
                }
            }
        }
        return list
    }

    companion object {
        const val COLUMN_COUNT = 4
        const val TAG = "TreasureBoxDialog"
    }
}