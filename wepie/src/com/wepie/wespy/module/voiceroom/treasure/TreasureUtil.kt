package com.wepie.wespy.module.voiceroom.treasure

import android.util.Log
import com.huiwan.base.LibBaseUtil
import com.huiwan.configservice.constentity.TreasureConfig
import com.huiwan.constants.GameType
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.main.MainActivity
import java.util.*

object TreasureUtil {
    const val START_COUNT_DOWN_STATE = 1
    const val CAN_GET_TREASURE_STATE = 2
    const val NORMAL_STATE = 3

    fun getState(config: TreasureConfig): Int {
        var canGetBox = false
        var needCountDown = false
        config.treasureBoxes.forEach {
            it.forEach { treasureBox ->
                if (treasureBox.status == TreasureConfig.TreasureBox.NEED_COUNT) {
                    needCountDown = true
                } else if (treasureBox.status == TreasureConfig.TreasureBox.CAN_GET) {
                    canGetBox = true
                }
                treasureBox.isUserVIP = config.isVIP
            }
        }
        return when {
            canGetBox -> {
                CAN_GET_TREASURE_STATE
            }
            needCountDown -> {
                START_COUNT_DOWN_STATE
            }
            else -> {
                NORMAL_STATE
            }
        }
    }

    fun getCountDownTreasureBox(config: TreasureConfig): TreasureConfig.TreasureBox? {
        var res: TreasureConfig.TreasureBox? = null
        config.treasureBoxes.forEach {
            if (it.isNotEmpty()) {
                val treasureBox = it[0]
                val needCountDown = treasureBox.status == TreasureConfig.TreasureBox.NEED_COUNT
                if (needCountDown) {
                    res = treasureBox
                    return@forEach
                }
            }
        }
        return res
    }

    // cp语音房、语音匹配 不显示宝箱
    fun hideTreasureBox(roomInfo: VoiceRoomInfo?): Boolean {
        var flag = true
        roomInfo?.let {
            val gameType = it.game_type
            if (gameType != GameType.GAME_TYPE_AUDIO_MATCH
                && gameType != GameType.GAME_TYPE_LOVEHOME
                && gameType != GameType.GAME_TYPE_UN_DEFINED
            ) {
                flag = false
            }
        }
        return flag
    }

    fun isVIPLimit(treasureBox: TreasureConfig.TreasureBox): Boolean {
        return treasureBox.isVip && !treasureBox.isUserVIP
    }

    fun getNextDayMillis(): Long {
        val cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 1)
        cal.set(Calendar.HOUR_OF_DAY, 0)
        cal.set(Calendar.SECOND, 0)
        cal.set(Calendar.MINUTE, 0)
        cal.set(Calendar.MILLISECOND, 0)
        return cal.timeInMillis
    }
}