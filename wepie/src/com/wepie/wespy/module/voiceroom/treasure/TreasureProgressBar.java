package com.wepie.wespy.module.voiceroom.treasure;

import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;

import com.huiwan.widget.CircleTimerProgressBar;

// https://codesign.qq.com/s/VbAE958MoPjPlze/6W3G0mqMxG0lOwL/inspect
public class TreasureProgressBar extends CircleTimerProgressBar {

    protected int startAngle = 270;
    protected boolean clockwise = false;

    private static int lastProgress;

    public TreasureProgressBar(Context context) {
        super(context);
    }

    public TreasureProgressBar(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public TreasureProgressBar(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    // 这个倒计时进度条是一点点减少的，参考设计稿
    @Override
    protected void drawDetail(Canvas canvas) {
        setLastProgress(progress);
        int angle = 360 - 360 * lastProgress / max;
        if (!clockwise) {
            angle = -angle;
        }
        canvas.drawArc(oval, startAngle, angle, false, circlePaint);
    }

    public static int getLastProgress() {
        return lastProgress;
    }

    public static void setLastProgress(int lastProgress) {
        TreasureProgressBar.lastProgress = lastProgress;
    }
}
