package com.wepie.wespy.module.voiceroom.treasure

import android.graphics.Color
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.huiwan.base.str.ResUtil
import com.huiwan.configservice.constentity.TreasureConfig.TreasureBox
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.model.event.voice.VoiceTreasureEvent
import org.greenrobot.eventbus.EventBus

class TreasureDoubleHolder(itemView: View) : TreasureHolder(itemView) {
    private val TAG = "TreasureTwoHolder"
    private lateinit var normalBoxLayout: View
    private lateinit var vipBoxLayout: View
    private var isVIPLimit = false

    init {
        initView()
    }

    private fun initView() {
        normalBoxLayout = itemView.findViewById(R.id.normal_box_layout)
        vipBoxLayout = itemView.findViewById(R.id.vip_box_layout)
    }

    override fun bindData(treasureBoxes: List<TreasureBox>) {
        treasureBoxes.forEach {
            updateInfo(it)
        }
        HLog.d(TAG, HLog.USR, "bindData size=${treasureBoxes.size}")
    }

    private fun updateInfo(treasureBox: TreasureBox) {
        TreasureHolderHelper.updateBoxStatus(
            if (treasureBox.isVip) {
                isVIPLimit = TreasureUtil.isVIPLimit(treasureBox)
                vipBoxLayout
            } else {
                normalBoxLayout
            }, treasureBox, callBack
        )
    }

    val callBack = object : TreasureCountDown.CountDownCallBack {
        override fun onTick(millisUntilFinished: Long) {
            updateTimer(millisUntilFinished)
        }

        override fun onFinish() {
            updateTimer(0)
        }

    }

    private fun updateTimer(leftMilliseconds: Long) {
        val text = TreasureCountDown.format(leftMilliseconds)
        TreasureHolderHelper.updateHint(normalBoxLayout, "#FFFFFFFF", text, 0)
        if (!isVIPLimit) {
            TreasureHolderHelper.updateHint(vipBoxLayout, "#FFFFFFFF", text, 0)
        }
    }
}