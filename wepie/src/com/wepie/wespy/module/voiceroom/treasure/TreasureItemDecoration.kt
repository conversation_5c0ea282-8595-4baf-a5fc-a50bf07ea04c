package com.wepie.wespy.module.voiceroom.treasure

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.wepie.wespy.R

class TreasureItemDecoration(_columns: Int) : RecyclerView.ItemDecoration() {
    private val columns = _columns

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        outRect.bottom = view.context.resources.getDimension(R.dimen.treasure_box_top).toInt()
    }

}