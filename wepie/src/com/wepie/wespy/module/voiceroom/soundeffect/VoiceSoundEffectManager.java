package com.wepie.wespy.module.voiceroom.soundeffect;

import android.media.AudioManager;
import android.media.SoundPool;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.format.DateUtils;
import android.util.SparseIntArray;

import com.wepie.download.DownloadCallback;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.voiceroom.VoiceSoundEffect;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.download.DownloadUtil;
import com.huiwan.base.util.log.TimeLogger;

/**
 * date 2019-05-28
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class VoiceSoundEffectManager {
    private static final VoiceSoundEffectManager INSTANCE = new VoiceSoundEffectManager();
    private static final int REQ_REMOVE = 0x101;

    public static VoiceSoundEffectManager get() {
        return INSTANCE;
    }


    public void playEffect(final int effectId) {
        VoiceSoundEffect effect = null;
        for (VoiceSoundEffect effectInfo: ConfigHelper.getInstance().getVoiceRoomConfig().getSoundEffects()) {
            if (effectId == effectInfo.id) {
                effect = effectInfo;
            }
        }
        if (effect == null) {
            TimeLogger.err("effect not found, id: " + effectId);
            return;
        }

        int soundId = soundMap.get(effectId, -1);
        if (soundId == -1) {
            DownloadUtil.downloadFile(effect.url, true, new DownloadCallback() {
                @Override
                public void onSuccess(String url, String path) {
                    loadLocalFileAndPlay(effectId, path);
                }

                @Override
                public void onFail(String msg) {
                    TimeLogger.err(msg);
                }

                @Override
                public void onPercent(int percent) {
                }
            });
        } else {
            play(soundId);
        }
        notifyAnim(effectId);
    }



    private SoundPool pool;
    private SparseIntArray soundMap = new SparseIntArray(8);

    private void loadLocalFileAndPlay(int effectId, final String path) {
        soundMap.append(effectId, getSoundPool().load(path, 0));
    }
    private SoundPool getSoundPool() {
        if (pool == null) {
            if (Build.VERSION_CODES.LOLLIPOP > Build.VERSION.SDK_INT) {
                pool = new SoundPool(1, AudioManager.STREAM_MUSIC, 0);
            } else {
                pool = new SoundPool.Builder().setMaxStreams(1).build();
            }

            pool.setOnLoadCompleteListener(new SoundPool.OnLoadCompleteListener() {
                @Override
                public void onLoadComplete(SoundPool soundPool, int sampleId, int status) {
                    if (status == 0) {
                        play(sampleId);
                    }
                }
            });
        }
        return pool;
    }

    private int lastPlayId;

    private void play(int id) {
        if (!VoiceRoomService.getInstance().isMuteRoom()) {
            getSoundPool().stop(lastPlayId);
            lastPlayId = getSoundPool().play(id, 1, 1, 1,0, 1);
        }
    }

    private void notifyAnim(int toPlayId) {
        handler.removeMessages(REQ_REMOVE);
        handler.sendEmptyMessageDelayed(REQ_REMOVE, DateUtils.SECOND_IN_MILLIS * 2);
        EventDispatcher.postPlayingEffectChange(toPlayId);
    }

    private static Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            if (msg.what == REQ_REMOVE) {
                EventDispatcher.postPlayingEffectChange(0);
            }
        }
    };


    public void stopCur() {
        getSoundPool().stop(lastPlayId);
    }
}
