package com.wepie.wespy.module.voiceroom

import android.content.Context
import android.view.View
import androidx.fragment.app.FragmentActivity
import com.huiwan.configservice.model.voiceroom.VoiceLabelInfo
import com.huiwan.constants.GameType
import com.huiwan.lib.api.DataCallback
import com.wepie.lib.api.plugins.voice.Config
import com.wepie.wespy.model.entity.voiceroom.VoiceUtilItem
import com.wepie.wespy.module.game.room.roomcreate.typeselect.PreviewInfo
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.rootview.FamilyRoomMode
import com.wepie.wespy.module.voiceroom.main.rootview.LoveRoomMode
import com.wepie.wespy.module.voiceroom.main.rootview.SimpleRoomMode
import com.wepie.wespy.module.voiceroom.main.rootview.VideoRoomMode
import com.wepie.wespy.module.voiceroom.main.rootview.WeddingRoomMode
import com.wepie.wespy.module.voiceroom.voicegame.VoiceGameMode
import com.wepie.wespy.voiceroom.IVoiceRoomApi
import com.wepie.wespy.voiceroom.IVoiceRoomMode
import com.wepie.wespy.voiceroom.RoomModeData
import java.util.Collections

class VoiceRoomApiImpl : IVoiceRoomApi {

    private val gameType: Int
        get() = VoiceRoomService.getInstance().gameType

    private val roomModeMap = mutableMapOf<Int, IVoiceRoomMode>()

    init {
        roomModeMap[GameType.GAME_TYPE_VOICE_ROOM] = SimpleRoomMode()
//        roomModeMap[GameType.GAME_TYPE_CP_ROOM] = CpRoomMode()
        roomModeMap[GameType.GAME_TYPE_LOVEHOME] = LoveRoomMode()
        roomModeMap[GameType.GAME_TYPE_VIDEO_ROOM] = VideoRoomMode()
        roomModeMap[GameType.GAME_TYPE_VOICE_GAME_ROOM] = VoiceGameMode()
        roomModeMap[GameType.GAME_TYPE_FAMILY_ROOM] = FamilyRoomMode()
        roomModeMap[GameType.GAME_TYPE_WEDDING] = WeddingRoomMode()
    }

    override fun getRoomViewClass(gameType: Int): Class<out View>? =
        roomModeMap[gameType]?.getViewClass()

    override fun getAllSupportModeList(rid: Int): List<RoomModeData> {
        val list = mutableListOf<RoomModeData>()
        roomModeMap.values.forEach {
            it.getModeData()?.apply {
                list.add(this)
            }
        }
        return list
    }

    override fun getAllPreviewInfoList(rid: Int): List<PreviewInfo> {
        val list = mutableListOf<PreviewInfo>()
        roomModeMap.values.forEach {
            it.getPreviewInfo()?.apply {
                list.add(this)
            }
        }
        return list
    }

    override fun getPreviewInfo(gameType: Int): PreviewInfo? {
        return roomModeMap[gameType]?.getPreviewInfo()
    }

    override fun initPreview() {
        roomModeMap.values.forEach {
            it.initPreview()
        }
    }

    override fun onSelectPreview(previewInfo: PreviewInfo, callback: DataCallback<Any?>) {
        roomModeMap.values.forEach {
            it.onSelectPreview(previewInfo, callback)
        }
    }

    override fun getVoiceUtilList(gameType: Int, type: Int): List<VoiceUtilItem>? {
        return roomModeMap[gameType]?.getVoiceUtilList(type)
    }

    override fun addVoiceUtilItem(sceneType: Int, utilItem: VoiceUtilItem) {
        roomModeMap.filter { it.value.getUtilsType() == sceneType }.forEach {
            it.value.addVoiceUtilItem(utilItem)
        }
    }

    override fun clearUtils() {
        roomModeMap.forEach { (_, u) ->
            u.clearUtils()
        }
    }


    override fun register(mode: IVoiceRoomMode) {
        roomModeMap[mode.getGameType()] = mode
    }

    override fun isSupport(gameType: Int): Boolean {
        return roomModeMap[gameType] != null
    }

    override fun checkAndChangeMode(
        gameType: Int, fromClick: Boolean,
        change: (selectId: Int, obj: Any?) -> Unit
    ) {
        roomModeMap[gameType]?.checkAndChange(fromClick, change)
    }

    override fun filterShowExitRoomDialog(
        context: Context,
        gameType: Int, rid: Int, exitMsg: String,
        exit: () -> Unit
    ): Boolean {
        return roomModeMap[gameType]?.filterShowExitRoomDialog(context, rid, exitMsg, exit) ?: false
    }

    override fun getVoiceScene(gameType: Int): Long {
        return roomModeMap[gameType]?.getVoiceScene() ?: Config.CONTEXT_DEFAULT
    }

    override fun getLabelList(gameType: Int): List<VoiceLabelInfo> {
        return roomModeMap[gameType]?.getLabelList() ?: Collections.emptyList()
    }

    override fun onResume() {
        roomModeMap[VoiceRoomService.getInstance().roomInfo.game_type]?.onResume()
    }

    override fun checkGiftShow(sendUid: Int): Boolean {
        return roomModeMap[gameType]?.checkGiftShow(sendUid) ?: false
    }

    override fun onFilterBack(activity: FragmentActivity): Boolean {
        return roomModeMap[gameType]?.onFilterBack(activity) ?: false
    }
}