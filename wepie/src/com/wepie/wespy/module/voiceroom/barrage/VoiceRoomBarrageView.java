package com.wepie.wespy.module.voiceroom.barrage;

import android.content.Context;
import android.util.AttributeSet;

import com.huiwan.barrage.BarrageAnimView;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg;
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginFrameLayout;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IVipBarrage;

/**
 * date 2020/5/6
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class VoiceRoomBarrageView extends PluginFrameLayout implements IVipBarrage {
    private BarrageAnimView animView;

    public VoiceRoomBarrageView(Context context) {
        super(context);
    }

    public VoiceRoomBarrageView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void initView() {
        int top = ScreenUtil.getStatusBarHeight() + ScreenUtil.dip2px(70);
        setPaddingRelative(0, top, 0, 0);
        animView = new BarrageAnimView(getContext());
        addView(animView);
    }

    @Override
    protected void initData() {

    }

    @Override
    public void showBarrage(VoiceRoomMsg msg) {
        ThreadUtil.runOnUiThread(() -> UserService.get().getCacheSimpleUser(msg.getSend_uid(), new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                animView.showVipAnim(simpleInfo, msg.getMsgContent());
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        }));
    }
}
