package com.wepie.wespy.module.voiceroom.lottery

import com.huiwan.base.util.TextUtil
import com.wepie.wespy.module.voiceroom.IPluginTool
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IBasePlugin
import com.wepie.wespy.net.tcp.handler.VoiceRoomHandler
import com.wepie.wespy.net.tcp.packet.RoomPushPackets

class LotteryBtnDelegateImpl(
    private val pluginTool: IPluginTool<IBasePlugin>,
) : IBasePlugin {

    init {
        initPushIntercept()
    }

    private fun initPushIntercept() {
        val roomIntercept = VoiceRoomHandler.IPushIntercept { pushMsg ->
            when (pushMsg.sysTypeValue) {
                RoomPushPackets.RoomSysType.SYS_ROOM_BUTTON_STATE_CHANGE_VALUE -> {
                    val buttonStateChange = pushMsg.buttonStateChange
                    if (TextUtil.isEmpty(buttonStateChange.buttonUrl)) {
                        pluginTool.loadView(false)
                    } else {
                        pluginTool.loadView(true)
                        pluginTool.getRealView<RoomLotteryBtnView>()?.showRoomLotteryBtn(
                            mainPlugin.roomId,
                            buttonStateChange.buttonUrl,
                            buttonStateChange.data,
                            buttonStateChange.comboTimes
                        ) {
                            pluginTool.loadView(false)
                        }
                    }
                    true
                }

                else -> false
            }
        }
        VoiceRoomHandler.pushInterceptList.add(roomIntercept)
        pluginTool.onDestroy {
            VoiceRoomHandler.pushInterceptList.remove(roomIntercept)
        }
    }
}