package com.wepie.wespy.module.voiceroom.lottery

import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import android.view.View
import androidx.core.animation.addListener
import kotlin.math.min

/* 需要设置android:clipChildren="false"，暂时这样处理*/
open class RoundedRectPathView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : View(context, attrs) {
    protected val path = Path()
    protected var steps: StepsLength = StepsLength(0, 0, 0f)
    protected val animator = ObjectAnimator.ofFloat(100f, 0f)

    /* 自行设置如有需要 */
    val paint = Paint()
    var progress: Float = 0f
        set(value) {
            field = value
            onProgressChanged()
        }

    init {
        paint.color = Color.BLUE
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 12f
        paint.isAntiAlias = true
        paint.strokeCap = Paint.Cap.ROUND
    }

    inner class StepsLength(var w: Int, var h: Int, var r: Float) {
        fun startX() = w / 2f
        fun step1() = w / 2 - r
        fun step2() = (Math.PI * r).toInt()
        fun step3() = 2 * step1()
        fun total() = step1() * 4 + step2() * 2
    }

    fun startCountDown(duration: Long, onEnd: (Boolean) -> Unit) {
        if (animator.isRunning) animator.cancel()
        animator.removeAllUpdateListeners()
        animator.addUpdateListener {
            progress = it.animatedValue as Float
        }
        animator.duration = duration
        var isCancel = false
        animator.addListener(onCancel = {
            isCancel = true
        }, onEnd = {
            onEnd(isCancel)
        })
        animator.start()
    }

    fun stop() {
        animator.cancel()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        steps.apply {
            w = measuredWidth
            h = measuredHeight
            r = measuredHeight.toFloat() / 2
        }
    }

    private fun onProgressChanged() {
        if (progress !in 0f..100f) {
            throw IllegalArgumentException("progress must in 0..100")
        }
        postInvalidate()
    }

    protected open fun drawPath(p: Float) {
        path.reset()
        if (steps.w == 0) return
        val startX = steps.startX()
        path.moveTo(startX, 0f)
        val cur = (p * steps.total()) / 100
        var sum = 0f
        val (step1, step2) = steps.step1() to steps.step2()
        if (cur > sum) {
            val mCur = cur - sum
            val drawLength = min(step1, mCur)
            val x = startX - drawLength
            //Log.d("RoundedRectPathView", "lineTo:($x, 0)")
            path.lineTo(x, 0f)
            sum += drawLength
        }
        if (cur > sum) {
            val mCur = cur - sum
            val l = step2.toFloat()
            val drawLength = min(l, mCur)
            val drawDeg = 180 * drawLength / l
            //Log.d("RoundedRectPathView", "arcTo:$drawDeg")
            path.arcTo(
                startX - step1 - steps.r,
                0f,
                startX - step1 + steps.r,
                steps.h.toFloat(),
                -90f,
                -drawDeg,
                false
            )
            sum += drawLength
        }
        if (cur > sum) {
            val mCur = cur - sum
            val drawLength = min(steps.step3(), mCur)
            val x = startX - step1 + drawLength
            //Log.d("RoundedRectPathView", "lineTo:($x, 0)")
            path.lineTo(x, steps.h.toFloat())
            sum += drawLength
        }
        if (cur > sum) {
            val mCur = cur - sum
            val l = step2.toFloat()
            val drawLength = min(l, mCur)
            val drawDeg = 180 * drawLength / l
            //Log.d("RoundedRectPathView", "arcTo:$drawDeg")
            path.arcTo(
                startX + step1 - steps.r,
                0f,
                startX + step1 + steps.r,
                steps.h.toFloat(),
                90f,
                -drawDeg,
                false
            )
            sum += drawLength
        }
        if (cur > sum) {
            val mCur = cur - sum
            val drawLength = min(step1, mCur)
            val x = startX + step1 - drawLength
            //Log.d("RoundedRectPathView", "lineTo:($x, 0)")
            path.lineTo(x, 0f)
            sum += drawLength
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        drawPath(progress)
        canvas.drawPath(path, paint)
    }
}
