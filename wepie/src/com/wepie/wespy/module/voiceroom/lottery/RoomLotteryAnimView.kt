package com.wepie.wespy.module.voiceroom.lottery

import android.content.Context
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.view.isVisible
import com.huiwan.anim.LifecycleAnimView
import com.huiwan.anim.LifecycleQueueAnimView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.Utils.gone
import com.huiwan.base.ui.background.ViewBackgroundDSL.Companion.buildBackground
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.tencent.qgame.animplayer.util.ScaleType
import com.wejoy.weplay.ex.autoCancel
import com.wejoy.weplay.ex.view.launch
import com.wejoy.weplay.ex.view.toLife
import com.wepie.download.DownloadUtilExt
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.liblog.main.HLog
import com.wepie.wejoy.player.WPAudioPlayer
import com.wepie.wejoy.player.interfaces.IWPAudioPlayer
import com.wepie.wespy.R
import com.wepie.wespy.helper.notice.GiftAudioPlayer
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import java.io.File

/**
 * <AUTHOR> zhangjing
 * @date : 星期一 3/4/24
 */
class RoomLotteryAnimView(context: Context) : FrameLayout(context) {

    companion object {
        private const val TAG = "RoomLotteryAnimView"
    }

    private var roomLotteryResultView: RoomLotteryResultView? = null
    private var backgroundSVGAView: ImageView? = null
    private var backgroundAnimView: LifecycleAnimView? = null
    private var backgroundAnimImageLoadInfo: ImageLoadInfo = ImageLoadInfo().apply {
        setLoopCount(1)
    }

    //终极奖励
    private var prizePoolUpgradeAnimView: LifecycleQueueAnimView? = null
    private val audioPlayer: IWPAudioPlayer by lazy {
        WPAudioPlayer()
    }
    private var lastPath = ""

    init {
        clipChildren = false
    }

    private fun startAudio(path: String) {
        if (path.isEmpty() || !File(path).exists()) {
            return
        }
        if (GiftAudioPlayer.needHideGiftVoice(context)) {
            return
        }
        audioPlayer.stop()
        audioPlayer.setRes(path)
        audioPlayer.play()
    }

    fun showPrizePoolUpgrade(poolUpgradeInfo: ILotteryAnim.PoolUpgradeInfo) {
        HLog.d(TAG, "showPrizePoolUpgrade: poolUpgradeInfo = {}", poolUpgradeInfo)
        launch {
            try {
                val taskList = mutableListOf<Deferred<String?>>()
                if (poolUpgradeInfo.animUrl.isNotEmpty()) {
                    taskList.add(async {
                        DownloadUtilExt.downloadFileCancelableKt(
                            poolUpgradeInfo.animUrl, true
                        )
                    })
                }
                if (poolUpgradeInfo.audioUrl.isNotEmpty()) {
                    taskList.add(async {
                        DownloadUtilExt.downloadFileCancelableKt(
                            poolUpgradeInfo.audioUrl, true
                        )
                    })
                }

                val result = taskList.awaitAll()

                val videoPath = result.getOrNull(0)
                val audioPath = result.getOrNull(1)

                HLog.d(TAG, "showPrizePoolUpgrade: videoPath = $videoPath, audioPath = $audioPath")
                if (!videoPath.isNullOrEmpty()) {
                    startPrizePoolUpgradeAnimView(videoPath)
                }
                if (!audioPath.isNullOrEmpty()) {
                    startAudio(audioPath)
                }

            } catch (e: Exception) {
                HLog.e(TAG, "showPrizePoolUpgrade error: {}", e.message)
            }
        }
    }

    fun showRoomLotteryView(
        rsp: RoomLotteryRspData,
        duration: Long,
        onDismiss: (() -> Unit)
    ) {
        HLog.d(TAG, "show data: $rsp")
        launch {
            if (roomLotteryResultView == null) {
                roomLotteryResultView = RoomLotteryResultView(context)
                roomLotteryResultView!!.setOnClickListener {}
                addView(roomLotteryResultView, 0, LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                ).apply { gravity = Gravity.CENTER })
            }
            if (!roomLotteryResultView!!.enableRefresh) {
                return@launch
            }
            val path = DownloadUtilExt.downloadFileCancelableKt(
                rsp.svga, true
            )
            val data = Sequence { rsp.list.listIterator() }.map {
                val prop = ConfigHelper.getInstance().propConfig.getPropItem(it.propId)
                    ?: return@map RoomLotteryResultData.default
                val background = ConfigHelper.getInstance().voiceRoomConfig.lotteryConfig.baseImages
                    .filter { i -> i.id == it.lotteryBaseImgId }
                    .map { i -> i.url }.firstOrNull() ?: ""
                val price = if (prop.priceList.isEmpty()) 0 else prop.priceList[0].price
                RoomLotteryResultData(
                    background = background,
                    icon = prop.mediaUrl,
                    value = price,
                    number = it.`val`,
                    id = prop.itemId,
                    name = prop.name,
                    bgColor = prop.bgColor
                )
            }.filter {
                it != RoomLotteryResultData.default
            }
                .fold<RoomLotteryResultData, MutableList<RoomLotteryResultData>>(mutableListOf()) { acc, i ->
                    acc.add(i)
                    acc
                }
            roomLotteryResultView!!.refresh(data, rsp.comboTimes, duration) {
                roomLotteryResultView?.enableRefresh = false
                hideBackground()
                <EMAIL> {
                    Solid("%50000000")
                }
                roomLotteryResultView?.showResultDialog(it) {
                    roomLotteryResultView?.enableRefresh = true
                    onDismiss.invoke()
                }
            }
            roomLotteryResultView!!.progress(
                rsp.progressValue,
                rsp.totalProgress,
                rsp.propId,
                rsp.explainImg
            )
            if (path == null) {
                ToastUtil.show(ResUtil.getStr(R.string.cocos_update_down_fail))
                return@launch
            }//下载失败不显示背景与声音
            if (rsp.svga.endsWith(".svga", true)) {
                showSVGABackground(path)
            } else if (rsp.svga.endsWith(".mp4", true)) {
                showVAPBackground(path)
            }
            val audioPath = DownloadUtilExt.downloadFileCancelableKt(
                rsp.audio, true
            ) ?: return@launch
            //优先显示背景视频，声效不需要阻塞流程
            startAudio(audioPath)
        }
    }

    /**
     * 本来是svga，后面要求vap，两者都保留
     */
    private fun showSVGABackground(svga: String) {
        if (backgroundSVGAView == null) {
            backgroundSVGAView = ImageView(context).apply {
                layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
                gone()
            }
            addView(backgroundSVGAView, 0)
        }
        if (backgroundSVGAView?.visibility == View.GONE) {
            backgroundSVGAView?.isVisible = true
            WpImageLoader.load(svga, backgroundSVGAView, backgroundAnimImageLoadInfo)
        }
    }

    private fun showVAPBackground(mp4: String) {
        addBackgroundAnimView()
        if (backgroundAnimView?.visibility == View.GONE) {
            lastPath = mp4
            startPlayBackgroundAnimView(mp4)
        } else if (backgroundAnimView != null && !TextUtils.equals(lastPath, mp4)) {
            HLog.d(TAG, HLog.USR, "mp4 update, value = {}, lastPath = {}", mp4, lastPath)
            removeView(backgroundAnimView)
            backgroundAnimView = null
            addBackgroundAnimView()
            lastPath = mp4
            startPlayBackgroundAnimView(mp4)
        }
    }

    private fun addBackgroundAnimView() {
        if (backgroundAnimView == null) {
            backgroundAnimView = LifecycleAnimView(context).apply {
                layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
                setScaleType(ScaleType.CENTER_CROP)
                setNeedReplay(true)
                setNeedRetainLastFrame(false)
                supportOldVideo(false)
                setLoop(Int.MAX_VALUE)
                gone()
            }
            addView(backgroundAnimView, 0)
            val animView = backgroundAnimView!!
            animView.toLife().autoCancel {
                animView.stopPlay()
            }
        }
    }

    private fun startPlayBackgroundAnimView(mp4: String) {
        backgroundAnimView?.isVisible = true
        //视频有个动画
        backgroundAnimView?.alpha = 0f
        backgroundAnimView?.animate()?.alpha(1f)?.setDuration(150)?.start()
        backgroundAnimView?.startPlay(File(mp4))
    }

    private fun hideBackground() {
        backgroundSVGAView?.gone()
        backgroundAnimView?.stopPlay()
        backgroundAnimView?.gone()
    }

    private suspend fun startPrizePoolUpgradeAnimView(path: String) {
        if (path.isEmpty() || !File(path).exists()) {
            HLog.e(TAG, "startPrizePoolUpgradeAnimView: path is empty or file not exists")
            return
        }
        if (prizePoolUpgradeAnimView == null) {
            prizePoolUpgradeAnimView = LifecycleQueueAnimView(context).apply {
                layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
                setScaleType(ScaleType.CENTER_CROP)
            }
            addView(prizePoolUpgradeAnimView)
        }
        prizePoolUpgradeAnimView?.enqueue(File(path))
    }
}