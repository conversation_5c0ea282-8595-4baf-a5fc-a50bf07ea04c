package com.wepie.wespy.module.voiceroom.lottery

import com.wepie.wespy.model.event.RoomInfoUpdateEvent
import com.wepie.wespy.module.voiceroom.IPluginTool
import com.wepie.wespy.net.tcp.handler.VoiceRoomHandler
import com.wepie.wespy.net.tcp.packet.RoomPushPackets

class LotteryAnimDelegateImpl(
    private val pluginTool: IPluginTool<ILotteryAnim>,
) : ILotteryAnim {

    init {
        initPushIntercept()
    }

    override fun showRoomLotteryView(
        rsp: RoomLotteryRspData,
        duration: Long,
        onDismiss:()->Unit
    ) {
        pluginTool.loadView(true)
        pluginTool.getRealView<RoomLotteryAnimView>()?.showRoomLotteryView(
            rsp, duration
        ) {
            onDismiss.invoke()
            pluginTool.loadView(false)
        }
    }

    private fun initPushIntercept() {
        val roomIntercept = VoiceRoomHandler.IPushIntercept { pushMsg ->
            when (pushMsg.sysTypeValue) {
                RoomPushPackets.RoomSysType.SYS_ROOM_REWARD_POOL_UPGRADE_VALUE -> {
                    pluginTool.loadView(true)
                    pluginTool.getRealView<RoomLotteryAnimView>()?.showPrizePoolUpgrade(
                        ILotteryAnim.PoolUpgradeInfo(
                            pushMsg.roomRewardPoolUpgrade.videoUrl,
                            pushMsg.roomRewardPoolUpgrade.audioUrl
                        )
                    )
                    true
                }

                RoomPushPackets.RoomSysType.SYS_ROOM_ACTIVITY_THEME_CHANGE_VALUE -> {
                    mainPlugin.roomInfo.tempThemeId = pushMsg.activityThemeChange.activityTheme
                    mainPlugin.updateRoomInfo(
                        mainPlugin.roomInfo,
                        RoomInfoUpdateEvent(RoomInfoUpdateEvent.F_ROOM_SETTING_BG)
                    )
                    true
                }

                else -> false
            }
        }
        VoiceRoomHandler.pushInterceptList.add(roomIntercept)
        pluginTool.onDestroy {
            VoiceRoomHandler.pushInterceptList.remove(roomIntercept)
        }
    }
}