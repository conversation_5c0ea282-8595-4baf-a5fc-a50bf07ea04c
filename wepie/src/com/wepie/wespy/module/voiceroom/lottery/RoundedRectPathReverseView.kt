package com.wepie.wespy.module.voiceroom.lottery

import android.content.Context
import android.util.AttributeSet
import kotlin.math.min

/* 需要设置android:clipChildren="false"，暂时这样处理*/
class RoundedRectPathReverseView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
) : RoundedRectPathView(context, attrs) {

    override fun drawPath(p: Float) {
        path.reset()
        if (steps.w == 0) return
        val startX = steps.startX()
        path.moveTo(startX, 0f)
        val cur = (p * steps.total()) / 100
        var sum = 0f
        val (step1, step2) = steps.step1() to steps.step2()
        if (cur > sum) {
            val mCur = cur - sum
            val drawLength = min(step1, mCur)
            val x = startX + drawLength
            //Log.d("RoundedRectPathView", "lineTo:($x, 0)")
            path.lineTo(x, 0f)
            sum += drawLength
        }
        if (cur > sum) {
            val mCur = cur - sum
            val l = step2.toFloat()
            val drawLength = min(l, mCur)
            val drawDeg = 180 * drawLength / l
            //Log.d("RoundedRectPathView", "arcTo:$drawDeg")
            path.arcTo(
                startX + step1 - steps.r,
                0f,
                startX + step1 + steps.r,
                steps.h.toFloat(),
                -90f,
                drawDeg,
                false
            )
            sum += drawLength
        }
        if (cur > sum) {
            val mCur = cur - sum
            val drawLength = min(steps.step3(), mCur)
            val x = startX + step1 - drawLength
            //Log.d("RoundedRectPathView", "lineTo:($x, 0)")
            path.lineTo(x, steps.h.toFloat())
            sum += drawLength
        }
        if (cur > sum) {
            val mCur = cur - sum
            val l = step2.toFloat()
            val drawLength = min(l, mCur)
            val drawDeg = 180 * drawLength / l
            //Log.d("RoundedRectPathView", "arcTo:$drawDeg")
            path.arcTo(
                startX - step1 - steps.r,
                0f,
                startX - step1 + steps.r,
                steps.h.toFloat(),
                90f,
                drawDeg,
                false
            )
            sum += drawLength
        }
        if (cur > sum) {
            val mCur = cur - sum
            val drawLength = min(step1, mCur)
            val x = startX - step1 + drawLength
            //Log.d("RoundedRectPathView", "lineTo:($x, 0)")
            path.lineTo(x, 0f)
            sum += drawLength
        }
    }
}