package com.wepie.wespy.module.voiceroom.lottery

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import android.view.View
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.FontUtil
import com.huiwan.base.util.PaintUtil
import com.huiwan.base.util.ScreenUtil
import com.wepie.wespy.R

class LotteryProgressView(
    context: Context,
    attrs: AttributeSet? = null,
) : View(context, attrs) {
    companion object {
        private val bgColor = ResUtil.getColor(R.color.black_alpha50)
        private val progressColor = Color.parseColor("#ff5c00")
        private val textColor = ResUtil.getColor(R.color.white)

        const val MAX = 1f
    }

    private val progressPadding = ScreenUtil.dip2px(2f).toFloat()
    private var textBaseLine = -1f
    private val paint = Paint()
    private val textPaint = Paint()
    private var textWidth = 0f
    private val path = Path()
    private var textOffsetY = 0f
    private val isRtl = ScreenUtil.isRtl()

    var progress = 0f // 0-1
        set(value) {
            field = if (value < 0) {
                0f
            } else if (value > MAX) {
                MAX
            } else {
                value
            }
        }
    var text = ""
        set(value) {
            field = value
            textWidth = textPaint.measureText(text)
            invalidate()
        }

    init {
        textPaint.setTypeface(FontUtil.getTypeface())
        textPaint.style = Paint.Style.FILL
        textPaint.color = textColor
        textPaint.textSize = ScreenUtil.dip2px(11f).toFloat()
        textOffsetY = textPaint.textSize / 20
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 画背景
        paint.color = bgColor
        val radius = height.toFloat() / 2
        canvas.drawRoundRect(0f, 0f, width.toFloat(), height.toFloat(), radius, radius, paint)
        // 画进度
        paint.color = progressColor
        val innerRadius = radius - progressPadding
        path.reset()
        val left: Float
        val right: Float
        if (isRtl) {
            left = width - progressPadding
            right = left - 2 * innerRadius - (width - 2 * radius) * progress / MAX
        } else {
            left = progressPadding
            right = left + 2 * innerRadius + (width - 2 * radius) * progress / MAX
        }
        path.addRoundRect(
            left,
            progressPadding,
            right,
            height.toFloat() - progressPadding,
            innerRadius,
            innerRadius,
            Path.Direction.CW
        )
        canvas.drawPath(path, paint)
        // 画文本
        if (text.isNotEmpty()) {
            if (textBaseLine < 0) {
                textBaseLine = PaintUtil.getTextBaseLine(textPaint, 0, height).toFloat()
            }
            canvas.drawText(
                text,
                (width - textWidth) / 2,
                textBaseLine + textOffsetY,
                textPaint
            )
        }
    }
}