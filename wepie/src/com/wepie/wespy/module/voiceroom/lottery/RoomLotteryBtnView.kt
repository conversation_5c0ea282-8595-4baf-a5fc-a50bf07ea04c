package com.wepie.wespy.module.voiceroom.lottery

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.text.HtmlCompat
import com.huiwan.base.ui.dialog.HWUIDialogBuilder
import com.huiwan.base.ui.dialog.HWUIDialogBuilder.Companion.show
import com.huiwan.base.util.ColorUtil
import com.huiwan.base.util.ContextUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.base.util.VibrateUtil
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.HwApi
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.wejoy.weplay.ex.view.toLife
import com.wepie.lib.api.plugins.track.TrackApi
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender

class RoomLotteryBtnView(context: Context) : FrameLayout(context) {
    companion object {
        private const val DURATION = 3_000L
    }

    private lateinit var lotteryBtnIv: ImageView
    private lateinit var countdownRing: RoundedRectPathView
    private lateinit var closeIv: ImageView
    private var greyFilter: ColorMatrixColorFilter
    private var colorFilter: ColorMatrixColorFilter
    private var vibrateUtil: VibrateUtil? = null
    private var onClose = {}

    init {
        val greyMatrix = ColorMatrix()
        greyMatrix.setSaturation(0.5f)
        greyFilter = ColorMatrixColorFilter(greyMatrix)
        val colorMatrix = ColorMatrix()
        colorMatrix.setSaturation(1f)
        colorFilter = ColorMatrixColorFilter(colorMatrix)
        val activity = ContextUtil.getActivityFromContext(context)
        if (activity != null) {
            vibrateUtil = VibrateUtil(activity)
        }
        initView()
    }

    private fun initView() {
        LayoutInflater.from(context).inflate(R.layout.room_bottom_lottery_btn_lay, this)
        lotteryBtnIv = findViewById(R.id.bottom_lottery_btn)
        countdownRing = findViewById(R.id.rounded_rect_path_view)
        countdownRing.apply {
            this.paint.apply {
                this.strokeWidth = ScreenUtil.dip2px(4f).toFloat()
                this.color = ColorUtil.getColor("#FF5C00")
            }
        }
        closeIv = findViewById(R.id.bottom_lottery_close)
    }

    override fun onViewRemoved(child: View?) {
        super.onViewRemoved(child)
        countdownRing.stop()
    }

    @SuppressLint("ClickableViewAccessibility")
    fun showRoomLotteryBtn(
        rid: Int,
        btnUrl: String,
        data: String,
        comboTimes: Int,
        onClose: () -> Unit
    ) {
        this.onClose = onClose
        WpImageLoader.load(btnUrl, lotteryBtnIv)
        lotteryBtnIv.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_DOWN) {
                lotteryBtnIv.colorFilter = greyFilter
            } else if (event.action == MotionEvent.ACTION_UP) {
                lotteryBtnIv.colorFilter = colorFilter
            }
            false
        }
        lotteryBtnIv.setOnClickListener {
            vibrateUtil?.shortVibrate()
            sendLotteryRequest(rid, data, comboTimes, onDisable = {
                lotteryBtnIv.isEnabled = false
                lotteryBtnIv.clearColorFilter()
            }, onEnable = {
                lotteryBtnIv.isEnabled = true
                lotteryBtnIv.clearColorFilter()
            })
            ApiService.of(TrackApi::class.java).appClick(
                TrackScreenName.VOICE_MODE_MSG,
                TrackButtonName.VOICE_ROOM_LOTTERY,
                mapOf("combo_num" to comboTimes)
            )
        }
        closeIv.setOnClickListener {
            onClose.invoke()
        }
    }

    private fun sendLotteryRequest(
        rid: Int,
        data: String,
        comboTimes: Int,
        noPopWindow: Boolean = false,
        popType: Int = 0,
        onDisable: () -> Unit,
        onEnable: () -> Unit
    ) {
        VoiceRoomPacketSender.roomLottery(
            rid,
            data,
            comboTimes,
            noPopWindow,
            popType,
            object : LifeSeqCallback(toLife()) {
                override fun onSuccess(head: RspHeadInfo) {
                    if (!head.codeOk() || head.message == null) {
                        return
                    }
                    val clickButtonRsp = head.message as TmpRoomPackets.ClickButtonRsp
                    if (clickButtonRsp.popType > 0) {
                        // 要弹二次确认弹窗
                        HWUIDialogBuilder(context).show {
                            title(clickButtonRsp.popText, HtmlCompat.FROM_HTML_MODE_LEGACY)
                            selectable(R.string.voice_room_lottery_no_pop_tips)
                            positiveButtonWithCheck { selected ->
                                // 确认抽奖
                                sendLotteryRequest(
                                    rid,
                                    data,
                                    comboTimes,
                                    selected,
                                    clickButtonRsp.popType,
                                    onDisable,
                                    onEnable
                                )
                            }
                        }
                    } else {
                        // 抽奖成功
                        val roomLotteryRspData = RoomLotteryRspData(
                            list = clickButtonRsp.rewardListList,
                            progressValue = clickButtonRsp.guaranteedItem.luckyValue,
                            totalProgress = clickButtonRsp.guaranteedItem.luckyNum,
                            propId = clickButtonRsp.guaranteedItem.propId,
                            explainImg = clickButtonRsp.guaranteedItem.explainImg,
                            svga = clickButtonRsp.videoUrl,
                            audio = clickButtonRsp.audioUrl,
                            comboTimes = comboTimes,
                        )
                        VoicePluginService.getPlugin(ILotteryAnim::class.java).showRoomLotteryView(
                            roomLotteryRspData, DURATION
                        ) {
                            countdownRing.stop()
                            onEnable.invoke()
                        }
                        countdownRing.startCountDown(DURATION) { isCancel ->
                            if (!isCancel) {
                                onDisable.invoke()
                            }
                        }
                    }
                }

                override fun onFail(head: RspHeadInfo) {
                    ToastUtil.show(head.desc)
                    when (head.code) {
                        RspHeadInfo.ERROR_CODE_ROOM_COIN_NOT_ENOUGH -> {
                            ApiService.of(HwApi::class.java).showCoinNotEnoughDialog(true)
                        }

                        RspHeadInfo.ERROR_ACTIVITY_END -> {
                            onClose.invoke()
                        }

                        else -> Unit
                    }
                }

            })
    }
}