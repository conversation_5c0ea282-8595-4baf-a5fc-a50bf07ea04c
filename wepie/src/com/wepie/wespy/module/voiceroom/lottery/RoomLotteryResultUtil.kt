package com.wepie.wespy.module.voiceroom.lottery

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.Space
import android.widget.TextView
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.children
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.google.gson.annotations.SerializedName
import com.huiwan.base.ktx.margin
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.HWUIButton
import com.huiwan.base.ui.Utils.gone
import com.huiwan.base.ui.Utils.padding
import com.huiwan.base.ui.Utils.size
import com.huiwan.base.ui.WPUIText
import com.huiwan.base.ui.background.ViewBackgroundDSL
import com.huiwan.base.ui.background.ViewBackgroundDSL.Companion.buildBackground
import com.huiwan.base.util.JsonUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.lib.api.plugins.IWebLotteryApi
import com.peanut.flexbox.AlignItems
import com.peanut.flexbox.FlexDirection
import com.peanut.flexbox.JustifyContent
import com.peanut.flexbox.PeanutFlexboxLayoutManager
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.module.voiceroom.lottery.RoomLotteryResultView.Companion.scaleLotteryAnimation
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 类太散了，只是一个抽奖功能，都放这里了。
 */
class RoomLotteryResultView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
) : ConstraintLayout(context, attrs) {
    private val adapter = RoomLotteryResultAdapter<RoomLotteryResultViewHolder>()
    private var combo = 0
    private val comboLayout: LinearLayout
    private val comboNumLayout: LinearLayout
    private val progressView: LotteryProgressView
    private val progressProp: ImageView
    private val progressDesc: ImageView
    private var lastProgressWidth = 0
    private var lastProgressValue: Float = 0f
    private var clearRunnable: Runnable? = null
    var enableRefresh = true
    private val isRtl = ScreenUtil.isRtl()
    private val comboImages = arrayOf(
        R.drawable.combo_num_0,
        R.drawable.combo_num_1,
        R.drawable.combo_num_2,
        R.drawable.combo_num_3,
        R.drawable.combo_num_4,
        R.drawable.combo_num_5,
        R.drawable.combo_num_6,
        R.drawable.combo_num_7,
        R.drawable.combo_num_8,
        R.drawable.combo_num_9,
    )

    init {
        LayoutInflater.from(context).inflate(R.layout.room_lottery_result_view, this)
        progressView = this.findViewById(R.id.progress)
        progressProp = this.findViewById(R.id.prop)
        progressDesc = this.findViewById(R.id.desc)
        comboLayout = this.findViewById(R.id.combo)
        comboNumLayout = this.findViewById(R.id.combo_num_layout)
        val rv = this.findViewById<RecyclerView>(R.id.rv)
        rv.adapter = adapter
        val spanCount = 5
        val lineSpace =
            (ScreenUtil.getScreenWidth() - spanCount * ScreenUtil.dip2px(59f)) / (spanCount + 1) - 1
        rv.layoutManager = PeanutFlexboxLayoutManager(context).apply {
            setRecyclerView(rv)
            this.spanCount = spanCount
            this.lineSpace = lineSpace
            justifyContent = JustifyContent.CENTER
            flexDirection = FlexDirection.ROW
            alignItems = AlignItems.CENTER
        }
        rv.addItemDecoration(object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State,
            ) {
                super.getItemOffsets(outRect, view, parent, state)
                val holder = parent.getChildViewHolder(view)
                val pos = holder.bindingAdapterPosition
                if (pos % spanCount != 0) {
                    if (isRtl) {
                        outRect.right = lineSpace
                    } else {
                        outRect.left = lineSpace
                    }
                }
            }
        })
    }

    @SuppressLint("SetTextI18n")
    fun progress(progress: Int, luckyNum: Int, propId: Int, explainImg: String) {
        if (luckyNum <= 0) {
            progressView.gone()
            progressProp.gone()
            progressDesc.gone()
        } else {
            progressView.isVisible = true
            progressProp.isVisible = true
            progressDesc.isVisible = true
        }
        val animator = ObjectAnimator.ofFloat(lastProgressValue, progress.toFloat())
        animator.addUpdateListener {
            val v = it.animatedValue as Float
            progressView.progress = v / luckyNum
            progressView.text = "${v.toInt()}/$luckyNum"
            lastProgressValue = v
        }
        animator.duration = 200
        animator.start()
        WpImageLoader.load(explainImg, progressDesc)
        val prop = ConfigHelper.getInstance().propConfig.getPropItem(propId) ?: return
        WpImageLoader.load(prop.mediaUrl, progressProp)
    }

    private fun combo(comboTimes: Int) {
        combo += comboTimes
        comboNumLayout.children.forEach { v -> v.gone() }
        getDigits(combo).forEachIndexed { idx, dig ->
            if (idx > comboNumLayout.childCount - 1) {
                comboNumLayout.addView(
                    ImageView(context).apply {
                        adjustViewBounds = true
                    },
                    LinearLayout.LayoutParams(
                        ViewGroup.LayoutParams.WRAP_CONTENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                )
            }
            val v = comboNumLayout.getChildAt(idx) as ImageView
            v.isVisible = true
            v.setImageResource(comboImages[dig])
        }
        comboLayout.scaleLotteryAnimation()
    }

    private fun getDigits(number: Int): List<Int> {
        if (number == 0) {
            return listOf(0)
        }
        var num = if (number < 0) -number else number
        val digits = mutableListOf<Int>()
        while (num > 0) {
            digits.add(num % 10)
            num /= 10
        }
        if (isRtl) {
            return digits
        }
        return digits.reversed()
    }

    fun refresh(
        data: List<RoomLotteryResultData>,
        comboTimes: Int,
        duration: Long,
        onResult: (List<RoomLotteryResultData>) -> Unit,
    ) {
        this.removeCallbacks(clearRunnable)
        this.findViewById<View>(R.id.lottery_layout)?.isVisible = true
        val (n, changed, added) = combineData(data)
        adapter.submitData(n, changed, added)
        combo(comboTimes)
        this.postDelayed(Runnable {
            HLog.d("RoomLotteryResultView", "result dialog show")
            this.findViewById<View>(R.id.lottery_layout)?.gone()
            onResult.invoke(adapter.currentList.sortedDescending())
            clear()
        }.apply { clearRunnable = this }, duration + 300)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        this.removeCallbacks(clearRunnable)
    }

    private data class CombineDataResult(
        val first: List<RoomLotteryResultData>,
        val second: List<Int>,
        val third: Pair<Int, Int>,
    )

    private fun combineData(data: List<RoomLotteryResultData>): CombineDataResult {
        if (adapter.currentList.isEmpty()) return CombineDataResult(
            data,
            listOf(),
            0 to data.size
        )//all add
        return updateOrAppend(adapter.currentList, data)
    }

    private fun updateOrAppend(
        old: List<RoomLotteryResultData>,
        data: List<RoomLotteryResultData>,
    ): CombineDataResult {
        val tmp = mutableMapOf<Int, RoomLotteryResultData>()
        val append = mutableListOf<RoomLotteryResultData>()
        val changed = mutableListOf<Int>()
        val oldCopy = old.map { it.copy() }
        oldCopy.forEach {
            tmp[it.id] = it
            it.duplicate = false
        }
        for (i in data) {
            if (tmp.containsKey(i.id)) {
                tmp[i.id]!!.number += i.number
                tmp[i.id]!!.duplicate = true
                val idx = oldCopy.indexOf(tmp[i.id]!!)
                changed.add(idx)
            } else {
                append.add(i)
            }
        }
        return CombineDataResult(oldCopy + append, changed, oldCopy.size to append.size)
    }

    fun showResultDialog(data: List<RoomLotteryResultData>, onDismiss: (() -> Unit)) {
        var dialog = this.findViewWithTag<ViewGroup?>("dialog_layout")
        val customView: ViewGroup?
        if (dialog == null) {
            dialog = FrameLayout(this.context).apply {
                tag = "dialog_layout"
                setOnClickListener { }
                val container = LinearLayoutCompat(this.context).apply {
                    gravity = Gravity.CENTER_HORIZONTAL
                    orientation = LinearLayoutCompat.VERTICAL
                    setOnClickListener { }
                    this.padding(
                        ScreenUtil.dip2px(24f),
                        ScreenUtil.dip2px(24f),
                        ScreenUtil.dip2px(24f),
                        ScreenUtil.dip2px(24f)
                    )
                    val title = WPUIText(this.context).apply {
                        setTextColor(ResUtil.getColor(R.color.color_text_primary))
                        text =
                            ResUtil.getStr(R.string.family_main_activity_week_task_got_dialog_title)
                        setFont(WPUIText.TajawalBold)
                        setStanderSize(WPUIText.H4)
                        gravity = Gravity.CENTER
                    }
                    this.addView(
                        title,
                        ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        )
                    )
                    customView = LinearLayout(this.context).apply {
                        id = "bottomCustomView".hashCode()
                        orientation = LinearLayout.HORIZONTAL
                        gravity = Gravity.CENTER_HORIZONTAL
                        margin<LinearLayoutCompat.LayoutParams> {
                            this.topMargin = ScreenUtil.dip2px(12f)
                        }
                    }
                    this.addView(
                        customView,
                        ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        )
                    )
                    val button = HWUIButton(this.context).apply {
                        setText(ResUtil.getStr(R.string.button_ok))
                        setStyle(HWUIButton.MID_BUTTON_STYLE_MAIN)
                        margin<LinearLayoutCompat.LayoutParams> {
                            this.topMargin = ScreenUtil.dip2px(24f)
                        }
                        setOnClickListener {
                            dialog?.gone()
                            onDismiss.invoke()
                        }
                    }
                    this.addView(
                        button,
                        ViewGroup.LayoutParams(
                            ScreenUtil.dip2px(183f),
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        )
                    )
                    this.buildBackground {
                        Solid(ViewBackgroundDSL.SystemColor.WHITE)
                        Corner(12)
                    }
                }
                this.addView(
                    container,
                    FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    ).apply {
                        gravity = Gravity.CENTER
                    })
                this.padding(start = ScreenUtil.dip2px(36f), end = ScreenUtil.dip2px(36f))
            }
            this.addView(
                dialog,
                ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ScreenUtil.getScreenHeight()
                )
            )
        } else {
            customView = dialog.findViewById("bottomCustomView".hashCode())
        }
        customView ?: return
        customView.removeAllViews()
        val maxLineCount = 4
        val adapter = RoomLotteryResultDialogAdapter()
        val paddingHorizontal = ScreenUtil.dip2px(6f)
        if (data.size in 1 until maxLineCount) {
            adapter.submitData(data, emptyList(), 0 to 0)
            customView.post {
                for (i in data.indices) {
                    val onCreateViewHolder = adapter.onCreateViewHolder(customView, 0)
                    adapter.onBindViewHolder(onCreateViewHolder, i)
                    customView.addView(onCreateViewHolder.itemView)
                    if (i != data.size - 1) {
                        customView.addView(
                            Space(context),
                            ViewGroup.LayoutParams(
                                paddingHorizontal,
                                ViewGroup.LayoutParams.WRAP_CONTENT
                            )
                        )
                    }
                }
            }
        } else {
            val view = RecyclerView(context)
            view.addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State,
                ) {
                    super.getItemOffsets(outRect, view, parent, state)
                    val holder = parent.getChildViewHolder(view)
                    val pos = holder.bindingAdapterPosition
                    if (pos % maxLineCount != 0) {
                        if (isRtl) {
                            outRect.right = paddingHorizontal
                        } else {
                            outRect.left = paddingHorizontal
                        }
                    }
                    if (pos / maxLineCount != 0) {
                        outRect.top = ScreenUtil.dip2px(9f)
                    }
                }
            })
            view.adapter = adapter
            view.layoutManager = PeanutFlexboxLayoutManager(context, FlexDirection.ROW).apply {
                justifyContent = JustifyContent.FLEX_START
                alignItems = AlignItems.STRETCH
            }
            customView.addView(
                view.also { adapter.submitData(data, emptyList(), 0 to data.size) },
                ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                )
            )
        }
        dialog.isVisible = true
    }

    private fun clear() {
        combo = 0
        lastProgressValue = 0f
        lastProgressWidth = 0
        adapter.clearData()
    }

    companion object {
        fun View.scaleLotteryAnimation() {
            val scaleOneAnimator = ValueAnimator.ofFloat(0.8f, 1.1f)
            scaleOneAnimator.duration = 160
            scaleOneAnimator.addUpdateListener { animation ->
                val animatedValue = animation.animatedValue as Float
                this.scaleX = animatedValue
                this.scaleY = animatedValue
            }
            val scaleBackAnimator = ValueAnimator.ofFloat(1.1f, 1f)
            scaleBackAnimator.duration = 240
            scaleBackAnimator.addUpdateListener { animation ->
                val animatedValue = animation.animatedValue as Float
                this.scaleX = animatedValue
                this.scaleY = animatedValue
            }
            val animatorSet = AnimatorSet()
            animatorSet.play(scaleOneAnimator).before(scaleBackAnimator)
            animatorSet.start()
        }
    }
}

data class RoomLotteryResultData(
    val id: Int,
    val background: String,
    val icon: String,
    val value: Int,
    var number: Int,
    val name: String,
    val bgColor: Int,
    var duplicate: Boolean = false,
) : Comparable<RoomLotteryResultData> {
    override fun compareTo(other: RoomLotteryResultData): Int {
        if (value != other.value) return other.value.compareTo(value)
        if (number != other.number) return other.number.compareTo(number)
        return other.id.compareTo(id)
    }

    companion object {
        val default = RoomLotteryResultData(-1, "", "", -1, -1, "", -1)
    }
}

data class RoomLotteryRspData(
    val list: MutableList<TmpRoomPackets.RewardItem>,
    val progressValue: Int, val totalProgress: Int,
    val propId: Int, val explainImg: String,
    val svga: String, val audio: String,
    val comboTimes: Int,
)

private open class RoomLotteryResultAdapter<T> :
    RecyclerView.Adapter<T>() where T : RoomLotteryResultViewHolder {
    private val playOnce = ImageLoadInfo().apply { loopCount = 1 }
    val currentList: CopyOnWriteArrayList<RoomLotteryResultData> = CopyOnWriteArrayList()

    @SuppressLint("InflateParams")
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): T {
        @Suppress("UNCHECKED_CAST")
        return RoomLotteryResultViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.room_lottery_result_item, parent, false)
        ) as T
    }

    override fun getItemCount() = currentList.size

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: T, position: Int) {
        val item = currentList[position]
        WpImageLoader.load(item.background, holder.background)
        WpImageLoader.load(item.icon, holder.icon)
        holder.number.text = ResUtil.getStr(R.string.num_x_d, item.number)
        if (item.duplicate) {
            WpImageLoader.load(
                ConfigHelper.getInstance().voiceRoomConfig.lotteryConfig.firstLightResUrl,
                holder.foreground,
                playOnce
            )
            holder.number.scaleLotteryAnimation()
        }
    }

    fun submitData(data: List<RoomLotteryResultData>, changed: List<Int>, add: Pair<Int, Int>) {
        currentList.clear()
        currentList.addAll(data)
        changed.forEach {
            notifyItemChanged(it)
        }
        if (add.second > 0) {
            notifyItemRangeInserted(add.first, add.second)
        }
    }

    fun clearData() {
        currentList.clear()
        notifyDataSetChanged()
    }

}

private class RoomLotteryResultDialogAdapter :
    RoomLotteryResultAdapter<RoomLotteryResultViewDialogHolder>() {
    @SuppressLint("InflateParams")
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): RoomLotteryResultViewDialogHolder {
        return RoomLotteryResultViewDialogHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.room_lottery_result_dialog_item, parent, false).apply {
                    size(
                        (parent.measuredWidth - 3 * (ScreenUtil.dip2px(6f))) / 4,
                        ViewGroup.LayoutParams.WRAP_CONTENT
                    )
                })
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: RoomLotteryResultViewDialogHolder, position: Int) {
        val item = currentList[position]
        holder.background.setImageDrawable(ColorDrawable(item.bgColor))
        WpImageLoader.load(item.icon, holder.icon)
        holder.number.text = ResUtil.getStr(R.string.num_x_d, item.number)
        holder.propName.text = item.name
    }
}

private open class RoomLotteryResultViewHolder(view: View) : RecyclerView.ViewHolder(view) {
    val background: ImageView by lazy { view.findViewById(R.id.background) }
    val foreground: ImageView by lazy { view.findViewById(R.id.foreground) }
    val icon: ImageView by lazy { view.findViewById(R.id.icon) }
    val number: WPUIText by lazy { view.findViewById(R.id.number) }

    init {
        number.buildBackground {
            Solid("%20000000")
            Corner(ScreenUtil.dip2px(10f))
        }
    }
}

private class RoomLotteryResultViewDialogHolder(view: View) : RoomLotteryResultViewHolder(view) {
    val propName: TextView by lazy { view.findViewById(R.id.prop_name) }
}

/* H5的数据结构 */
data class H5Data(
    @SerializedName("rewardList")
    val rewardList: List<RewardItem> = listOf(),
    @SerializedName("videoUrl")
    val videoUrl: String = "",
    @SerializedName("audioUrl")
    val audioUrl: String = "",
    @SerializedName("guaranteedItem")
    val guaranteedItem: GuaranteedItem = GuaranteedItem(),
    @SerializedName("comboTimes")
    val comboTimes: Int = 0,
)

data class RewardItem(
    @SerializedName("propId")
    val propId: Int = 0,
    @SerializedName("val")
    val `val`: Int = 0,
    @SerializedName("lotteryBaseImgId")
    val lotteryBaseImgId: Int = 0,
)

data class GuaranteedItem(
    @SerializedName("propId")
    val propId: Int = 0,
    @SerializedName("luckyValue")
    val luckyValue: Int = 0,
    @SerializedName("luckyNum")
    val luckyNum: Int = 0,
    @SerializedName("explainImg")
    val explainImg: String = "",
)

class WebLotteryApiImpl : IWebLotteryApi {
    companion object {
        private const val DURATION = 3_000L
    }

    override fun showLotteryResult(context: Activity, data: String, onClose: () -> Unit) {
        val viewGroup: ViewGroup = context.findViewById(android.R.id.content)
        val id = "lottery_view".hashCode()
        var view = viewGroup.findViewById<View>(id)
        if (view == null) {
            view = RoomLotteryAnimView(context)
            view.id = id
            viewGroup.addView(
                view,
                ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
            )
        }
        if (view !is RoomLotteryAnimView) {
            HLog.e("showLotteryResult", "view is not RoomLotteryAnimView")
            return
        }
        val rsp = JsonUtil.fromJson(data, H5Data::class.java) ?: return
        view.showRoomLotteryView(
            RoomLotteryRspData(
                list = Sequence { rsp.rewardList.listIterator() }.map {
                    TmpRoomPackets.RewardItem.newBuilder()
                        .setPropId(it.propId)
                        .setVal(it.`val`)
                        .setLotteryBaseImgId(it.lotteryBaseImgId)
                        .build()
                }.fold(mutableListOf()) { acc, rewardItem ->
                    acc.add(rewardItem)
                    acc
                },
                progressValue = rsp.guaranteedItem.luckyValue,
                totalProgress = rsp.guaranteedItem.luckyNum,
                propId = rsp.guaranteedItem.propId,
                explainImg = rsp.guaranteedItem.explainImg,
                svga = rsp.videoUrl,
                audio = rsp.audioUrl,
                comboTimes = rsp.comboTimes
            ), DURATION
        ) {
            onClose()
            viewGroup.removeView(view)
        }
    }

}