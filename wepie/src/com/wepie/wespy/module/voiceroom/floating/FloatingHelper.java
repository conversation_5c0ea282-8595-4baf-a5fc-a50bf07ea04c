package com.wepie.wespy.module.voiceroom.floating;

import android.content.Context;
import android.graphics.PixelFormat;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;

import com.huiwan.base.util.log.TimeLogger;

/**
 * date 2019/3/26
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class FloatingHelper {
    private WindowManager wm;
    private WindowManager.LayoutParams lp;
    private boolean showing = false;

    public FloatingHelper(Context context) {
        this.wm = (WindowManager)context.getSystemService(Context.WINDOW_SERVICE);
        this.lp = new WindowManager.LayoutParams();
        initLp();
    }

    public void showInWindow(View view, int gravity) {
        lp.gravity = gravity;
        if (wm != null && view.getParent() == null) {
            wm.addView(view, lp);
            showing = true;
        }
    }

    public void setPos(int x, int y) {
        lp.x = x;
        lp.y = y;
    }

    private void initLp() {
        lp.type = WindowManager.LayoutParams.TYPE_APPLICATION;
        lp.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;
        lp.gravity = Gravity.TOP|Gravity.START;
        lp.format = PixelFormat.TRANSLUCENT;
        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
    }

    public void hide(View view) {
        if (wm != null && view != null && view.getParent() != null) {
            try {
                wm.removeViewImmediate(view);
                showing = false;
            } catch (Exception e) {
                TimeLogger.err("remove view error: " + view);
            }
        }
    }

    public boolean isShowing() {
        return showing;
    }
}
