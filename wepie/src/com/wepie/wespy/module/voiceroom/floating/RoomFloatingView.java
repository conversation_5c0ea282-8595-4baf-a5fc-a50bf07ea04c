package com.wepie.wespy.module.voiceroom.floating;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.util.ProcessUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StringUtil;
import com.huiwan.base.util.TouchEffectUtil;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.ICompetitionApi;
import com.huiwan.libtcp.callback.SeqDataCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.sdk.floating.FloatingBaseView;
import com.huiwan.widget.CustomCircleImageView;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.bgmusic.BgMusicManager;
import com.wepie.wespy.helper.bgmusic.MusicSource;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.helper.wfloating.FloatingCloseEvent;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.voiceroom.dataservice.RoomSenderPresenter;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.music.VoiceMusicManager;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;
import com.wepie.wespy.voiceroom.IVoiceRoomApi;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import kotlin.jvm.functions.Function1;


/**
 * Created by three on 2017/9/15.
 */

public class RoomFloatingView extends FloatingBaseView {
    private final Context mContext;
    private CustomCircleImageView headImage;
    private TextView nameTx;
    private ImageView exitImage;
    private ImageView dividerIv;

    private final Function1<Context, Boolean> competitionCallback = context -> {
        handleCheckExitInMainProcessDirect(VoiceRoomService.getInstance().getRoomInfo());
        return true;
    };

    public RoomFloatingView(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.room_floating_view, this);
        headImage = findViewById(R.id.room_floating_head_image);
        nameTx = findViewById(R.id.room_floating_name_tx);
        exitImage = findViewById(R.id.room_floating_exit_bt);
        dividerIv = findViewById(R.id.divider_iv);
        dividerIv.setVisibility(GONE);
        headImage.setDefaultHeadBorder();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        EventBus.getDefault().register(this);
        ApiService.of(ICompetitionApi.class).registerEnterCompetitionCallback(competitionCallback);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        ApiService.of(ICompetitionApi.class).unregisterEnterCompetitionCallback(competitionCallback);
    }

    @Override
    protected void onWindowVisibilityChanged(int visibility) {
        super.onWindowVisibilityChanged(visibility);
        if (visibility == View.VISIBLE) {
            ApiService.of(ICompetitionApi.class).registerEnterCompetitionCallback(competitionCallback);
        } else {
            ApiService.of(ICompetitionApi.class).unregisterEnterCompetitionCallback(competitionCallback);
        }
    }

    public void update(VoiceRoomInfo roomInfo) {
        setVisibility(View.VISIBLE);

        String name = roomInfo.name;
        if (name == null) name = "";
        name = StringUtil.subName(name, 5);
        nameTx.setText(name);

        HeadImageLoader.loadCircleHeadImage(roomInfo.headImage, headImage);

        setOnClickListener(view -> {
            if (!ProcessUtil.isMainProcess(ActivityTaskManager.getInstance().getTopActivity())) {

            } else {
                JumpUtil.gotoVoiceRoomActivity(mContext, roomInfo.rid);
            }
        });

        TouchEffectUtil.addTouchEffect(exitImage);
        exitImage.setOnClickListener(view -> {
            //拉去最新房间信息,避免部分数据失效
            VoiceRoomInfo info;
            if (null == VoiceRoomService.getInstance().getRoomInfo()) {
                info = roomInfo;
            } else {
                info = VoiceRoomService.getInstance().getRoomInfo();
            }
            if (!ProcessUtil.isMainProcess(ActivityTaskManager.getInstance().getTopActivity())) {
                handleExitInChildProcess(info);
            } else {
                handleExitInMainProcess(info);
            }
        });
    }

    private void handleExitInChildProcess(VoiceRoomInfo info) {

    }

    private void handleExitInMainProcess(VoiceRoomInfo info) {
        Boolean filter = ApiService.of(IVoiceRoomApi.class).filterShowExitRoomDialog(getContext(), info.game_type, info.rid, info.getExitMsg(), () -> {
            handleCheckExitInMainProcessDirect(info);
            return null;
        });
        if (filter == Boolean.TRUE) {
            return;
        }

        handleCheckExitInMainProcess(info);
    }

    private void handleCheckExitInMainProcessDirect(VoiceRoomInfo info) {
        VoiceRoomPacketSender.checkExit(info.rid, new SeqDataCallback<>(this) {
            @Override
            public void onFail(RspHeadInfo head) {
                super.onFail(head);
                BgMusicManager.getInstance().stop(MusicSource.SOURCE_VOICE_ROOM);
                VoiceMusicManager.get().clearAndStop();
                RoomSenderPresenter.exitRoom(info.rid);
            }

            @Override
            public void onSuccess(TmpRoomPackets.GetOnlineAdminCountRsp rsp) {
                super.onSuccess(rsp);
                info.onlineAdministratorCount = rsp.getCount();
                BgMusicManager.getInstance().stop(MusicSource.SOURCE_VOICE_ROOM);
                VoiceMusicManager.get().clearAndStop();
                RoomSenderPresenter.exitRoom(info.rid);
            }
        });
    }

    private void handleCheckExitInMainProcess(VoiceRoomInfo info) {
        VoiceRoomPacketSender.checkExit(info.rid, new SeqDataCallback<>(this) {
            @Override
            public void onFail(RspHeadInfo head) {
                super.onFail(head);
                showExitDialog(info);
            }

            @Override
            public void onSuccess(TmpRoomPackets.GetOnlineAdminCountRsp rsp) {
                super.onSuccess(rsp);
                info.onlineAdministratorCount = rsp.getCount();
                showExitDialog(info);
            }
        });
    }

    private void showExitDialog(VoiceRoomInfo info) {
        showExitDialog(info, () -> {
            BgMusicManager.getInstance().stop(MusicSource.SOURCE_VOICE_ROOM);
            VoiceMusicManager.get().clearAndStop();
            RoomSenderPresenter.exitRoom(info.rid);
        });
    }

    private void showExitDialog(VoiceRoomInfo info, DialogBuild.DialogCallback dialogCallback) {
        DialogBuild.newBuilder(getContext()).setSingleBtn(false).setTitle("")
                .setContent(info.getExitMsg()).setCanCancel(true).setDialogCallback(dialogCallback).show();
    }


    public void setDivider(boolean isVisibility) {
        dividerIv.setVisibility(isVisibility ? View.VISIBLE : View.GONE);
    }

    @Override
    public int getViewHeight() {
        return ScreenUtil.dip2px(45);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void closeRoom(FloatingCloseEvent event) {
        if (event.getAndSet(true)) {
            return;
        }
        BgMusicManager.getInstance().stop(MusicSource.SOURCE_VOICE_ROOM);
        VoiceMusicManager.get().clearAndStop();
        RoomSenderPresenter.exitRoom(VoiceRoomService.getInstance().getRid());
    }
}
