package com.wepie.wespy.module.voiceroom.floating;

import android.content.Context;
import android.os.CountDownTimer;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.huiwan.base.LibBaseUtil;
import com.wepie.wespy.R;

/**
 * date 2019/3/26
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class FloatingView extends FrameLayout {
    private TextView tipTv;
    private TextView agreeTv;
    private TextView cancelTv;
    private String refuse;
    private FloatingHelper floatingHelper;
    private Callback callback;

    public FloatingView(Context context) {
        super(context);
        inflate(getContext(), R.layout.invite_speak_floating_view, this);
        floatingHelper = new FloatingHelper(context);
        initViews();
        initEvents();
    }

    private void initViews() {
        tipTv    = findViewById(R.id.content);
        agreeTv  = findViewById(R.id.agree_btn);
        cancelTv = findViewById(R.id.refuse_btn);
    }

    private OnClickListener clickListener = new OnClickListener() {
        @Override
        public void onClick(View v) {
            if (v == agreeTv) {
                if (callback != null) {
                    callback.onOk();
                }
                dismiss();
            } else if (v == cancelTv) {
                if (callback != null) {
                    callback.onRefuse(false);
                }
                dismiss();
            }
        }
    };

    private void initEvents() {
        agreeTv.setOnClickListener(clickListener);
        cancelTv.setOnClickListener(clickListener);
    }


    public void show(String tip, String ok, String refuse, int timeLeft, Callback callback) {
        this.refuse = refuse;
        tipTv.setText(tip);
        agreeTv.setText(ok);
        updateRefuse(timeLeft);
        floatingHelper.showInWindow(this, Gravity.TOP | Gravity.START);
        this.callback = callback;
        startTimer(timeLeft);
    }

    private void updateRefuse(int timeLeft) {
        if (timeLeft < 0) {
            cancelTv.setText(refuse);
        } else {
            cancelTv.setText(String.format(LibBaseUtil.getLocale(), "%s(%ds)", refuse, timeLeft));
        }
    }

    private CountDownTimer timer;
    private void startTimer(int totalTime) {
        if (timer != null) {
            timer.cancel();
        }
        timer = new CountDownTimer(totalTime * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                updateRefuse((int)millisUntilFinished / 1000);
            }

            @Override
            public void onFinish() {
                if (callback != null) {
                    callback.onRefuse(true);
                }
                dismiss();
            }
        };
        timer.start();
    }

    public void dismiss() {
        if (timer != null) {
            timer.cancel();
        }
        floatingHelper.hide(this);
        if (callback != null) {
            callback.onDismiss();
        }
    }

    public interface Callback {
        void onOk();
        void onRefuse(boolean timeout);
        void onDismiss();
    }
}
