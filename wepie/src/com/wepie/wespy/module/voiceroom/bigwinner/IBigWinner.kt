package com.wepie.wespy.module.voiceroom.bigwinner

import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.voiceroom.dataservice.BigWinnerViewModel
import com.wepie.wespy.module.voiceroom.dataservice.BigWinnerViewModel.ResultEvent
import com.wepie.wespy.module.voiceroom.dataservice.BigWinnerViewModel.VoteEvent
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IBasePlugin

interface IBigWinner : IBasePlugin {
    fun onUpdateRoomInfo(roomInfo: VoiceRoomInfo)
    fun onUpdateGameInfo(gameInfo: BigWinnerViewModel.BwGameInfo)
    fun onUpdateVoteEvent(gameInfo: BigWinnerViewModel.BwGameInfo, voteEvent: VoteEvent)
    fun onUpdateResultEvent(resultEvent: ResultEvent)
    fun changeVisibility(visible: <PERSON><PERSON><PERSON>)
}