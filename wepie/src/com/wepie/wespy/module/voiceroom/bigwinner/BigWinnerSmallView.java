package com.wepie.wespy.module.voiceroom.bigwinner;

import android.content.Context;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.huiwan.base.str.ResUtil;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.CustomCircleImageView;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.module.voiceroom.dataservice.BigWinnerViewModel;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.net.tcp.packet.BigWinner;

import java.util.List;

public class BigWinnerSmallView extends ConstraintLayout {
    private static final int HEAD_COUNT = 3;
    private final TextView titleTv;
    private final TextView tipTv;
    private final CustomCircleImageView headIv1;
    private final CustomCircleImageView headIv2;
    private final CustomCircleImageView headIv3;
    private final TextView countDownTv;
    private final CustomCircleImageView[] headIvArr = new CustomCircleImageView[3];
    private int resultVersion = -1;
    private CountDownTimer countDownTimer;
    private final Handler handler = new Handler(Looper.getMainLooper());

    private final BigWinnerViewModel viewModel = VoiceRoomService.getInstance().getBigWinnerViewModel();

    public BigWinnerSmallView(@NonNull Context context) {
        this(context, null);
    }

    public BigWinnerSmallView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        LayoutInflater.from(context).inflate(R.layout.room_big_winner_small_view, this);
        titleTv = findViewById(R.id.title_tv);
        tipTv = findViewById(R.id.tip_tv);
        headIv1 = findViewById(R.id.head_1);
        headIv2 = findViewById(R.id.head_2);
        headIv3 = findViewById(R.id.head_3);
        countDownTv = findViewById(R.id.count_down_tv);
        headIvArr[0] = headIv1;
        headIvArr[1] = headIv2;
        headIvArr[2] = headIv3;
    }

    private final Runnable countDownRunner = new Runnable() {
        @Override
        public void run() {
            countDownTv.setVisibility(VISIBLE);
            cancelCountDown();
            countDownTimer = new CountDownTimer(3000, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    countDownTv.setText(String.valueOf((int)Math.rint((float)millisUntilFinished / 1000)));
                }

                @Override
                public void onFinish() {
                    //countDownTv.setText("0");
                    updateHeadWhenBigWinnerEnd();
                    countDownTv.setVisibility(GONE);
                }
            };
            countDownTimer.start();
        }
    };

    private void updateHeadWhenBigWinnerEnd(){
        BigWinnerViewModel.ResultEvent resultEvent = viewModel.getResultEventLiveData().getValue();
        if(resultEvent == null || resultEvent.getResultInfo() == null){
            return ;
        }
        if(resultEvent.getResultInfo().getStatusValue() == BigWinner.BWResultStatus.BW_RESULT_WIN_VALUE){
            showOne();
            displayHead(resultEvent.getResultInfo().getUid(),headIvArr[0]);
            tipTv.setVisibility(GONE);
            resultVersion = -1;
        }
    }

    private void cancelCountDown() {
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }

    public void updateResult(BigWinnerViewModel.ResultEvent resultEvent) {
        BigWinnerViewModel.BwGameInfo gameInfo = viewModel.getGameInfoLiveData().getValue();
        if (gameInfo != null && gameInfo.isOpen() && resultVersion != resultEvent.getVersion() && resultEvent.getVersion() != 0) {
            resultVersion = resultEvent.getVersion();
            long resultTimeGap = System.currentTimeMillis() - resultEvent.getPostResultTime();
            if(resultTimeGap < 400){
                showCountDownDelay();
            } else {
                if (countDownRunner != null) {
                    handler.removeCallbacks(countDownRunner);
                }
                updateHeadWhenBigWinnerEnd();
                countDownTv.setVisibility(GONE);
            }

        }
    }

    private void showCountDownDelay() {
        handler.postDelayed(countDownRunner, 3000);
    }

    public void update() {
        BigWinnerViewModel.BwGameInfo gameInfo = viewModel.getGameInfoLiveData().getValue();
        if (gameInfo == null) {
            return;
        }
        updateHead(gameInfo);
        int stateValue = gameInfo.getGameInfo().getStateValue();
        int num = gameInfo.getGameInfo().getGamerInfosCount();
        if (stateValue == BigWinner.BigWinnerGameState.BW_STATE_INIT_VALUE) {
            handler.removeCallbacks(countDownRunner);
            cancelCountDown();
            countDownTv.setVisibility(GONE);
            if (num > 0) {
                tipTv.setText(ResUtil.getResource().getQuantityString(R.plurals.voice_room_winner_joined, num, num));
            } else {
                tipTv.setText(R.string.voice_room_winner_join_none);
            }
            tipTv.setVisibility(VISIBLE);
            resultVersion = -1;
        } else if (stateValue == BigWinner.BigWinnerGameState.BW_STATE_SHOW_RESULT_VALUE ||
                stateValue == BigWinner.BigWinnerGameState.BW_STATE_GAMING_VALUE){
            if (stateValue == BigWinner.BigWinnerGameState.BW_STATE_GAMING_VALUE) {
                countDownTv.setVisibility(GONE);
                handler.removeCallbacks(countDownRunner);
                cancelCountDown();
            }
            if (num == 1) {
                tipTv.setVisibility(VISIBLE);
                UserService.get().getCacheSimpleUser(gameInfo.getGameInfo().getGamerInfos(0).getUid(), new LifeUserSimpleInfoCallback(this) {
                    @Override
                    public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                        tipTv.setText(simpleInfo.getRemarkName());
                    }
                    @Override
                    public void onUserInfoFailed(String description) { }
                });
            } else if (num == 2) {
                tipTv.setVisibility(VISIBLE);
                tipTv.setText(R.string.voice_room_winner_combo_2);
            } else {
                tipTv.setText(ResUtil.getResource().getQuantityString(R.plurals.voice_room_winner_left_x, num, num));
                tipTv.setVisibility(VISIBLE);
            }
        } else {
            tipTv.setText(ResUtil.getResource().getQuantityString(R.plurals.voice_room_winner_joined, num, num));
            tipTv.setVisibility(VISIBLE);
        }

        BigWinnerViewModel.ResultEvent resultEvent = viewModel.getResultEventLiveData().getValue();
        if (resultEvent != null && resultEvent.getResultInfo() != null && resultEvent.getResultInfo().getStatusValue() == BigWinner.BWResultStatus.BW_RESULT_WIN_VALUE) {
            updateHeadWhenBigWinnerEnd();
            countDownTv.setVisibility(GONE);
        }
    }

    private void updateHead(BigWinnerViewModel.BwGameInfo gameInfo) {
        List<BigWinner.BWGamerInfo> gamerInfoList = gameInfo.getGameInfo().getGamerInfosList();
        if (gamerInfoList.size() <= 0) {
            err();
        } else if (gamerInfoList.size() == 1) {
            showOne();
        } else if (gamerInfoList.size() == 2) {
            showTwo();
        } else {
            showThree();
        }
        int showCount = Math.min(gamerInfoList.size(), HEAD_COUNT);
        for (int i = 0; i < showCount; i++) {
            displayHead(gamerInfoList.get(i).getUid(), headIvArr[i]);
        }
    }

    private void displayHead(int uid, ImageView headIv) {
        UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                HeadImageLoader.loadCircleHeadImage(simpleInfo.headimgurl, headIv);
            }

            @Override
            public void onUserInfoFailed(String description) { }
        });
    }

    private void err() {
        headIv1.setVisibility(GONE);
        headIv2.setVisibility(GONE);
        headIv3.setVisibility(GONE);
    }

    private void showOne() {
        headIv2.setVisibility(GONE);
        headIv3.setVisibility(GONE);
        headIv1.setVisibility(VISIBLE);
    }

    private void showTwo() {
        headIv3.setVisibility(GONE);
        headIv1.setVisibility(VISIBLE);
        headIv2.setVisibility(VISIBLE);
    }

    private void showThree() {
        headIv3.setVisibility(VISIBLE);
        headIv1.setVisibility(VISIBLE);
        headIv2.setVisibility(VISIBLE);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        handler.removeCallbacks(countDownRunner);
        cancelCountDown();
    }
}
