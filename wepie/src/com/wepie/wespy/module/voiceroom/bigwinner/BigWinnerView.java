package com.wepie.wespy.module.voiceroom.bigwinner;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.drawable.BitmapDrawable;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;

import com.huiwan.anim.SVGAUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.WebApi;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.User;
import com.huiwan.user.entity.UserSimpleInfo;
import com.opensource.svgaplayer.SVGAImageView;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wejoy.weplay.ex.lifecycle.LiveDataExKt;
import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.dialog.coin.PublicCoinPayBottomDialogView;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.media.sound.SoundPoolCallback;
import com.wepie.wespy.module.media.sound.SoundUtil;
import com.wepie.wespy.module.voiceroom.bigwinner.widget.WinnerRouletteView;
import com.wepie.wespy.module.voiceroom.dataservice.BigWinnerViewModel;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.ISmallFrameUtil;
import com.wepie.wespy.net.tcp.packet.BigWinner;
import com.wepie.wespy.net.tcp.sender.BigWinnerPacketSender;

import java.util.Date;
import java.util.List;
import java.util.Objects;

public class BigWinnerView extends RelativeLayout implements IBigWinner {
    private static final String TAG = "BigWinnerView";
    private static final String SVGA_RESULT_FAIL = "svga/lang/bw_result_fail.svga";
    private static final String SVGA_RESULT_WIN = "svga/lang/bw_result_win.svga";
    private static final String SVGA_START = "svga/bigwinner/bw_start_count_down.svga";
    private static final String SVGA_VOTE_COUNT_DOWN = "svga/bigwinner/bw_vote_count_down.svga";
    private static final String SVGA_VOTE_ANIM = "svga/bigwinner/bw_vote_anim.svga";
    private static final long VOTE_COUNT_DOWN_DURATION = 3000;
    private static final int BULLET_LIMIT_GIFT = 100_000;
    private View helpIv;
    private View smallIv;
    private WinnerRouletteView rouletteView;
    private TextView controlTv;
    private TextView shortJoinTv;
    private TextView startTv;
    private TextView tipTv;
    private TextView shortTipTv;
    private View closeIv;
    private ImageView voiceSwitchIv;
    private ViewGroup coinLay;
    private TextView coinTv;
    private SVGAImageView voteCountDownView;
    private SVGAImageView voteAnimView;
    private SVGAImageView resultAnimView;
    private SVGAImageView startCountDownView;
    private BigWinnerViewModel viewModel;
    private ImageView resultHeadIv;
    private TextView resultNameTv;
    private TextView resultWinTipTv;
    private ImageView resultWinTipImage;
    private TextView resultWinNumTipTv;
    private TextView resultWinTimeTv;
    private TextView resultLoseTipTv;
    private ImageView resultCrown;
    private ViewGroup resultLay;
    private ViewGroup bullet_tip;
    private TextView bullet_tip_content;
    private ImageView bullet_tip_gift;
    private TextView bullet_tip_count;
    private int resultVersion = 0;
    private long voteCountDownTime = 0;
    private Handler handler;
    private BitmapCoverProgressDrawable progressDrawable;
    private boolean isSelfAdminOrOwner = false;
    private CountDownTimer voteCountDownTimer;
    private BigWinnerSmallView winnerSmallView;
    private Runnable resultAnimRunner;
    boolean isVoiceOn = true;

    public BigWinnerView(Context context) {
        this(context, null);
    }

    public BigWinnerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    protected void initView() {
        initProgressDrawableIfNeed();
        initViewIfNeed();
        handler = new Handler(Looper.getMainLooper());
        viewModel = VoiceRoomService.getInstance().getBigWinnerViewModel();
        LiveDataExKt.observe(UserService.get().getSelfUser(), Objects.requireNonNull(ILifeUtil.toLife(this)), this::checkUpdateCoin);
    }

    private void initProgressDrawableIfNeed() {
        if (progressDrawable == null) {
            progressDrawable = new BitmapCoverProgressDrawable();
            progressDrawable.setBgBmp(((BitmapDrawable) ContextCompat.getDrawable(getContext(), R.drawable.big_winner_vote_progress_bg_0)).getBitmap());
            progressDrawable.setFgBmp(((BitmapDrawable) ContextCompat.getDrawable(getContext(), R.drawable.big_winner_vote_progress_bg_1)).getBitmap());
        }
    }

    private void updateAndShowView(BigWinnerViewModel.BwGameInfo gameInfo) {
        BigWinner.BWGameInfo info = gameInfo.getGameInfo();
        BigWinner.BWGamerInfo selfGamer = selfInGame(info);
        rouletteView.update(info, selfGamer);
        updateView(info, selfGamer, gameInfo);
    }

    private BigWinner.BWGamerInfo selfInGame(BigWinner.BWGameInfo gameInfo) {
        for (BigWinner.BWGamerInfo gamerInfo : gameInfo.getGamerInfosList()) {
            if (gamerInfo.getUid() == LoginHelper.getLoginUid()) {
                return gamerInfo;
            }
        }
        return null;
    }

    private void updateView(BigWinner.BWGameInfo gameInfo, BigWinner.BWGamerInfo selfGamer, BigWinnerViewModel.BwGameInfo viewModelgameInfo) {
        if (gameInfo.getStateValue() == BigWinner.BigWinnerGameState.BW_STATE_INIT_VALUE) {
            removeVoteAnimTask();
            showInitState(gameInfo, selfGamer);
        } else if (gameInfo.getStateValue() == BigWinner.BigWinnerGameState.BW_STATE_START_COUNTDOWN_VALUE) {
            showStartCountDownState(gameInfo, selfGamer);
        } else if (gameInfo.getStateValue() == BigWinner.BigWinnerGameState.BW_STATE_GAMING_VALUE) {
            showGamingStat(gameInfo, selfGamer);
        } else if (gameInfo.getStateValue() == BigWinner.BigWinnerGameState.BW_STATE_SHOW_RESULT_VALUE) {
            removeVoteAnimTask();
            showRoundResultState(gameInfo, selfGamer, viewModelgameInfo);
        }
    }

    private void showInitState(BigWinner.BWGameInfo gameInfo, BigWinner.BWGamerInfo selfGamer) {
        HLog.d(TAG, "showInitState");
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
        if (progressDrawable != null) {
            progressDrawable.setProgress(0);
        }
        isSelfAdminOrOwner = roomInfo != null && roomInfo.isSelfAdminOrOwner();
        startCountDownView.setVisibility(GONE);
        resultLay.setVisibility(GONE);
        voteAnimView.setVisibility(INVISIBLE);
        controlTv.setEnabled(true);
        bullet_tip.setVisibility(VISIBLE);
        bullet_tip_content.setText(R.string.voice_room_winner_bullet_before_tip);
        bullet_tip_gift.setVisibility(GONE);
        bullet_tip_count.setVisibility(GONE);
        if (isSelfAdminOrOwner) {
            startTv.setVisibility(VISIBLE);
            shortJoinTv.setVisibility(VISIBLE);
            controlTv.setVisibility(INVISIBLE);
            shortJoinTv.setBackgroundResource(selfGamer != null ? R.drawable.big_winner_short_grey : R.drawable.big_winner_short_join);
            shortJoinTv.setText(selfGamer != null ? R.string.voice_room_winner_joined : R.string.voice_room_winner_join);
            shortTipTv.setVisibility(selfGamer == null ? VISIBLE : GONE);
            tipTv.setVisibility(GONE);
        } else {
            shortTipTv.setVisibility(GONE);
            tipTv.setVisibility(selfGamer == null ? VISIBLE : GONE);
            startTv.setVisibility(GONE);
            shortJoinTv.setVisibility(GONE);
            controlTv.setVisibility(VISIBLE);
            controlTv.setBackgroundResource(selfGamer != null ? R.drawable.big_winner_ctrl_long_grey : R.drawable.big_winner_vote_progress_bg_0);
            controlTv.setText(selfGamer != null ? R.string.voice_room_winner_joined : R.string.voice_room_winner_join);
        }
        coinLay.setVisibility(VISIBLE);
        Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(gameInfo.getGiftId());
        if (gift != null) {
            String tip = ResUtil.getResource().getString(R.string.voice_room_winner_join_cost_tip, gift.getPrice(), 1, gift.getUnit(), gift.getName());
            tipTv.setText(tip);
            shortTipTv.setText(tip);
        } else {
            ToastUtil.debugShow("error big winner gift not found: " + gameInfo.getGiftId());
        }
        closeIv.setVisibility(isSelfAdminOrOwner ? VISIBLE : GONE);
        resultVersion = 0;
    }

    private void showStartCountDownState(BigWinner.BWGameInfo gameInfo, BigWinner.BWGamerInfo selfGamer) {
        HLog.d(TAG, "showStartCountDownState");
        resultLay.setVisibility(GONE);
        controlTv.setVisibility(GONE);
        closeIv.setVisibility(GONE);
        tipTv.setVisibility(GONE);
        shortTipTv.setVisibility(GONE);
        bullet_tip.setVisibility(GONE);

        if (selfGamer != null) {
            coinLay.setVisibility(VISIBLE);
        } else {
            coinLay.setVisibility(GONE);
            controlTv.setVisibility(GONE);
        }
        shortJoinTv.setVisibility(GONE);
        startTv.setVisibility(GONE);
        SVGAUtil.playSvga(SVGA_START, 1, startCountDownView);
        startCountDownView.setVisibility(VISIBLE);
        playSound(SoundUtil.TYPE_BIG_WINNER_3_2_1_GO, new BigWinnerSoundPoolCallback());
    }

    private void showGamingStat(BigWinner.BWGameInfo gameInfo, BigWinner.BWGamerInfo selfGamer) {
        HLog.d(TAG, "showGamingStat");
        shortTipTv.setVisibility(GONE);
        tipTv.setVisibility(GONE);
        closeIv.setVisibility(GONE);
        rouletteView.resetRotate();
        startTv.setVisibility(GONE);
        shortJoinTv.setVisibility(GONE);
        startCountDownView.setVisibility(GONE);
        tipTv.setVisibility(VISIBLE);
        Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(gameInfo.getGiftId());
        if (gift != null) {
            int left = BULLET_LIMIT_GIFT / gift.getPrice();
            if (gift.getPrice() * left < BULLET_LIMIT_GIFT) {
                left++;
            }
            left -= gameInfo.getRewardPool();
            if (left <= 0) {
                bullet_tip.setVisibility(GONE);
            } else {
                bullet_tip.setVisibility(VISIBLE);
                bullet_tip_content.setText(R.string.voice_room_winnner_bullet_tip);
                bullet_tip_gift.setVisibility(VISIBLE);
                WpImageLoader.load(gift.getMedia_url(), bullet_tip_gift, ImageLoadInfo.getGiftInfo());
                bullet_tip_count.setVisibility(VISIBLE);
                bullet_tip_count.setText(ResUtil.getResource().getString(R.string.x_number, left));
            }
        }
        if (selfGamer != null) {
            controlTv.setBackground(progressDrawable);
            controlTv.setBackgroundTintList(null);
            controlTv.setText(R.string.voice_room_winner_vote);
            controlTv.setVisibility(VISIBLE);
            voteAnimView.setVisibility(VISIBLE);
            updateProgressPos(selfGamer.getRoundWinPro());
            tipTv.setVisibility(VISIBLE);
            controlTv.setEnabled(true);
            if (gift != null) {
                tipTv.setVisibility(VISIBLE);
                tipTv.setText(ResUtil.getResource().getString(R.string.voice_room_winner_round_info_tip, selfGamer.getRoundVote(), gift.getUnit(), gift.getName(), (int) (selfGamer.getRoundWinPro() * 100)));
            } else {
                tipTv.setVisibility(GONE);
            }
        } else {
            controlTv.setText(R.string.voice_room_winner_player_vote);
            controlTv.setBackgroundResource(R.drawable.big_winner_ctrl_long_grey);
            controlTv.setVisibility(VISIBLE);
            controlTv.setEnabled(false);
            voteAnimView.setVisibility(GONE);
            tipTv.setVisibility(GONE);
        }
        resultLay.setVisibility(GONE);
        coinLay.setVisibility(selfGamer == null ? GONE : VISIBLE);

        updateVoteCountDown(gameInfo.getNextStateTimestamp());
    }

    private void showRoundResultState(BigWinner.BWGameInfo gameInfo, BigWinner.BWGamerInfo selfGamer, BigWinnerViewModel.BwGameInfo viewModelgameInfo) {
        HLog.d(TAG, "showRoundResultState, has round result={}", gameInfo.hasRoundResult());
        shortTipTv.setVisibility(GONE);
        tipTv.setVisibility(GONE);
        closeIv.setVisibility(GONE);
        resultLay.setVisibility(GONE);
        startCountDownView.setVisibility(GONE);
        bullet_tip.setVisibility(GONE);
        if (selfGamer != null) {
            tipTv.setVisibility(VISIBLE);
            controlTv.setVisibility(VISIBLE);
            controlTv.setBackgroundResource(R.drawable.big_winner_ctrl_long_grey);
            controlTv.setEnabled(false);
        } else {
            tipTv.setVisibility(GONE);
            controlTv.setVisibility(GONE);
            controlTv.setEnabled(true);
        }
        controlTv.setText(R.string.voice_room_winner_wait_for_result);
        shortTipTv.setVisibility(GONE);
        shortJoinTv.setVisibility(GONE);
        startTv.setVisibility(GONE);
        rouletteView.update(gameInfo, selfGamer);
        voteAnimView.setVisibility(INVISIBLE);
        coinLay.setVisibility(selfGamer == null ? GONE : VISIBLE);
        if (gameInfo.hasRoundResult()) {
            showResultWithGame(gameInfo.getRoundResult(), gameInfo, viewModelgameInfo);
        }
    }

    private void updateProgressPos(float rate) {
        if (progressDrawable != null) {
            progressDrawable.setProgress(rate);
        }
        MarginLayoutParams lp = (MarginLayoutParams) voteAnimView.getLayoutParams();
        int pw = ((ViewGroup) voteAnimView.getParent()).getWidth();
        if (pw > 0) {
            float pos = pw * rate;
            lp.setMarginStart((int) (pos - voteAnimView.getWidth() / 2) - ScreenUtil.dip2px(4));
            HLog.d(TAG, "self lp.start={}", lp.getMarginStart());
            voteAnimView.setLayoutParams(lp);
            voteAnimView.requestLayout();
            if (!voteAnimView.isAnimating()) {
                SVGAUtil.playSvga(SVGA_VOTE_ANIM, ValueAnimator.INFINITE, voteAnimView);
            }
        } else {
            lp.setMarginStart(0);
        }
    }

    private void showResultAnim(BigWinner.BWRoundResultInfo resultInfo, BigWinnerViewModel.BwGameInfo gameInfo, long resultTime) {
        HLog.d(TAG, "showResultAnim, {}, uid:{} ", resultInfo.getStatus(), resultInfo.getUid());
        rouletteView.startAnim(resultInfo);
        int delayTime = 6000;
        long resultTimeGap = System.currentTimeMillis() - resultTime;
        if (resultTimeGap > 200) {
            delayTime -= resultTimeGap;
            if (delayTime < 0) {
                delayTime = 0;
            }
            rouletteView.showJoinIcon(gameInfo.getGameInfo());
        } else {
            playSound(SoundUtil.TYPE_BIG_WINNER_TURNTABLE_START, new BigWinnerSoundPoolCallback());
        }
        controlTv.setText(R.string.voice_room_winner_wait_for_result);
        if (resultAnimRunner != null) {
            handler.removeCallbacks(resultAnimRunner);
        }
        resultAnimRunner = () -> showResult(resultInfo, gameInfo);
        handler.postDelayed(resultAnimRunner, delayTime);
    }

    private void showResultWithGame(BigWinner.BWRoundResultInfo resultInfo, BigWinner.BWGameInfo gameInfo, BigWinnerViewModel.BwGameInfo viewModelgameInfo) {
        HLog.d(TAG, "showResultWithGame, {}, uid:{} ,getStatusValue:{}", resultInfo.getStatus(), resultInfo.getUid(), resultInfo.getStatusValue());
        resultLay.setVisibility(VISIBLE);
        resultHeadIv.setImageDrawable(null);
        resultNameTv.setText("");
        UserService.get().getCacheSimpleUser(resultInfo.getUid(), new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                HeadImageLoader.loadCircleHeadImage(simpleInfo.headimgurl, resultHeadIv);
                resultNameTv.setText(simpleInfo.getRemarkName());
            }

            @Override
            public void onUserInfoFailed(String description) {
            }
        });
        if (resultInfo.getStatusValue() == BigWinner.BWResultStatus.BW_RESULT_WIN_VALUE) {
            resultWinTipTv.setVisibility(VISIBLE);
            resultWinTipImage.setVisibility(VISIBLE);
            resultWinNumTipTv.setVisibility(VISIBLE);
            Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(gameInfo.getGiftId());
            if (gift == null) {
                gift = new Gift();
                ToastUtil.debugShow("Error Gift Not Found: " + gameInfo.getGiftId());
            }
            setWinInfo(gift, resultInfo);
            resultWinTimeTv.setVisibility(VISIBLE);
            resultCrown.setVisibility((VISIBLE));
            String timeStr = TimeUtil.getFormat("yyyy-MM-dd HH:mm:ss").format(new Date(TimeUtil.getServerTime()));
            resultWinTimeTv.setText(ResUtil.getResource().getString(R.string.voice_room_winner_win_achieve, timeStr));
            resultLoseTipTv.setVisibility(GONE);
            SVGAUtil.playSvga(SVGA_RESULT_WIN, false, resultAnimView, null);
            playSound(SoundUtil.TYPE_BIG_WINNER_WIN, new BigWinnerSoundPoolCallback());
            if (viewModelgameInfo.getWinnerUid() == resultInfo.getUid()) return;
            viewModelgameInfo.setLastLoseUid(-1);
            viewModelgameInfo.setWinnerUid(resultInfo.getUid());
        } else {
            resultWinTipTv.setVisibility(GONE);
            resultWinTipImage.setVisibility(GONE);
            resultWinNumTipTv.setVisibility(GONE);
            resultWinTimeTv.setVisibility(GONE);
            resultCrown.setVisibility((GONE));
            resultLoseTipTv.setVisibility(VISIBLE);
            SVGAUtil.playSvga(SVGA_RESULT_FAIL, false, resultAnimView, null);
            playSound(SoundUtil.TYPE_BIG_WINNER_FAILED, new BigWinnerSoundPoolCallback());
            if (viewModelgameInfo.getLastLoseUid() == resultInfo.getUid()) return;
            viewModelgameInfo.setLastLoseUid(resultInfo.getUid());
            if (viewModelgameInfo.getWinnerUid() != -1) {
                viewModelgameInfo.setWinnerUid(-1);
            }
        }
    }

    private void showResult(BigWinner.BWRoundResultInfo resultInfo, BigWinnerViewModel.BwGameInfo gameInfo) {
        List<BigWinner.BWGamerInfo> gamerInfosList = gameInfo.getGameInfo().getGamerInfosList();
        if (isLoginUidInGamerInfosList(gamerInfosList) || (gameInfo.isOpen() && (!gameInfo.isShowSmall()))) {
            viewModel.changeSmall(false);
        }
        HLog.d(TAG, "showResult, {}, uid:{} ,getStatusValue:{} , lastLose:{}", resultInfo.getStatus(), resultInfo.getUid(), resultInfo.getStatusValue(), gameInfo.getLastLoseUid());
        resultLay.setVisibility(VISIBLE);
        resultHeadIv.setImageDrawable(null);
        resultNameTv.setText("");
        UserService.get().getCacheSimpleUser(resultInfo.getUid(), new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                HeadImageLoader.loadCircleHeadImage(simpleInfo.headimgurl, resultHeadIv);
                resultNameTv.setText(simpleInfo.getRemarkName());
            }

            @Override
            public void onUserInfoFailed(String description) {
            }
        });
        if (resultInfo.getStatusValue() == BigWinner.BWResultStatus.BW_RESULT_WIN_VALUE) {
            resultWinTipTv.setVisibility(VISIBLE);
            resultWinTipImage.setVisibility(VISIBLE);
            resultWinNumTipTv.setVisibility(VISIBLE);
            Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(gameInfo.getGameInfo().getGiftId());
            if (gift == null) {
                gift = new Gift();
                ToastUtil.debugShow("Error Gift Not Found: " + gameInfo.getGameInfo().getGiftId());
            }
            setWinInfo(gift, resultInfo);
            resultWinTimeTv.setVisibility(VISIBLE);
            resultCrown.setVisibility((VISIBLE));
            String timeStr = TimeUtil.getFormat("yyyy-MM-dd HH:mm:ss").format(new Date(TimeUtil.getServerTime()));
            resultWinTimeTv.setText(ResUtil.getResource().getString(R.string.voice_room_winner_win_achieve, timeStr));
            resultLoseTipTv.setVisibility(GONE);
            SVGAUtil.playSvga(SVGA_RESULT_WIN, false, resultAnimView, null);
            playSound(SoundUtil.TYPE_BIG_WINNER_WIN, new BigWinnerSoundPoolCallback());
            if (gameInfo.getWinnerUid() == resultInfo.getUid()) return;
            gameInfo.setLastLoseUid(-1);
            gameInfo.setWinnerUid(resultInfo.getUid());
        } else {
            resultWinTipTv.setVisibility(GONE);
            resultWinTipImage.setVisibility(GONE);
            resultWinNumTipTv.setVisibility(GONE);
            resultWinTimeTv.setVisibility(GONE);
            resultCrown.setVisibility(GONE);
            resultLoseTipTv.setVisibility(VISIBLE);
            SVGAUtil.playSvga(SVGA_RESULT_FAIL, false, resultAnimView, null);
            playSound(SoundUtil.TYPE_BIG_WINNER_FAILED, new BigWinnerSoundPoolCallback());
            if (gameInfo.getLastLoseUid() == resultInfo.getUid()) return;
            gameInfo.setLastLoseUid(resultInfo.getUid());
            if (gameInfo.getWinnerUid() != -1) {
                gameInfo.setWinnerUid(-1);
            }
        }
    }

    private boolean isLoginUidInGamerInfosList(List<BigWinner.BWGamerInfo> gamerInfosList) {
        for (BigWinner.BWGamerInfo gamerInfo : gamerInfosList) {
            if (gamerInfo.getUid() == LoginHelper.getLoginUid()) {
                return true;
            }
        }
        return false;
    }

    private void setWinInfo(Gift gift, BigWinner.BWRoundResultInfo resultInfo) {
        resultWinTipTv.setText(R.string.big_winner_won);
        WpImageLoader.load(gift.getMedia_url(), resultWinTipImage, ImageLoadInfo.getGiftInfo());
        resultWinNumTipTv.setText("x".concat(String.valueOf(resultInfo.getWinNum())));
    }

    /*private void setWinInfoTx(Gift gift, BigWinner.BWRoundResultInfo resultInfo) { //span 实现，加载url需要实现
        SpannableStringBuilder ssb = new SpannableStringBuilder();
        ssb.append(mContext.getString(R.string.big_winner_won));
        int start = ssb.length();
        int fontSizePx1 = (int)sp2px(mContext, 14);
        ssb.setSpan(new VerticalCenterSpan(fontSizePx1), 0, start, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        ForegroundColorSpan colorWin = new ForegroundColorSpan(0xffffffff);
        ssb.setSpan(colorWin, 0, start - 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        ImageSpan imageSpan = new ImageSpan(mContext, gift.url);
        ssb.setSpan(imageSpan,start - 1 ,start, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        start = ssb.length();
        ssb.append("x".concat(String.valueOf(resultInfo.getWinNum())));
        int end = ssb.length();
        ForegroundColorSpan colorSpanFirst = new ForegroundColorSpan(0xffFFEB84);
        ssb.setSpan(colorSpanFirst, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        int fontSizePx2 = (int)sp2px(mContext, 24);
        ssb.setSpan(new VerticalCenterSpan(fontSizePx2), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        resultWinTipTv.setMovementMethod(LinkMovementMethod.getInstance());
        resultWinTipTv.setText(ssb);
    }*/

    public static float sp2px(Context context, float sp) {
        float scaledDensity = context.getResources().getDisplayMetrics().scaledDensity;
        return sp * scaledDensity;
    }

    private void showVoteAnim(BigWinner.VoteInfo voteInfo) {
        HLog.d(TAG, "showVoteAnim uid={}", voteInfo.getUid());
    }

    private final Runnable doVoteCountDownAnimTask = new Runnable() {
        @Override
        public void run() {
            SVGAUtil.playSvga(SVGA_VOTE_COUNT_DOWN, voteCountDownView);
            startVoteCountDown();
        }
    };

    private void stopVoteCountDown() {
        if (voteCountDownTimer != null) {
            voteCountDownTimer.cancel();
            voteCountDownTimer = null;
        }
    }

    private void startVoteCountDown() {
        stopVoteCountDown();
        voteCountDownTimer = new CountDownTimer(3000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                playSound(SoundUtil.TYPE_BIG_WINNER_SEND_COUNT_DOWN, new BigWinnerSoundPoolCallback());
            }

            @Override
            public void onFinish() {
                //playSound(SoundUtil.TYPE_BIG_WINNER_SEND_COUNT_DOWN);
            }
        }.start();
    }

    private void removeVoteAnimTask() {
        voteCountDownTime = 0;
        handler.removeCallbacks(doVoteCountDownAnimTask);
    }

    private void updateVoteCountDown(long roundEndTimeSec) {
        long startTime = roundEndTimeSec * 1000 - VOTE_COUNT_DOWN_DURATION;
        long delayMill = startTime - TimeUtil.getElapsedServerTime();
        HLog.d(TAG, "update vote count down, {}, last when:{} ", delayMill, voteCountDownTime);
        if (delayMill > 0) {
            if (startTime != voteCountDownTime) {
                voteCountDownTime = startTime;
                handler.postDelayed(doVoteCountDownAnimTask, delayMill);
            }
        }
    }

    private void initViewIfNeed() {
        View.inflate(getContext(), R.layout.voice_big_winner_view, this);
        helpIv = findViewById(R.id.help_iv);
        smallIv = findViewById(R.id.hide_iv);
        rouletteView = findViewById(R.id.roulette_view);
        shortJoinTv = findViewById(R.id.join_short_tv);
        startTv = findViewById(R.id.start_tv);
        controlTv = findViewById(R.id.control_tv);
        tipTv = findViewById(R.id.tip_tv);
        shortTipTv = findViewById(R.id.short_tip_tv);
        voteCountDownView = findViewById(R.id.vote_count_down_iv);
        voteAnimView = findViewById(R.id.vote_anim_svga);
        resultAnimView = findViewById(R.id.result_anim_view);
        startCountDownView = findViewById(R.id.start_count_down_iv);
        closeIv = findViewById(R.id.close_iv);
        voiceSwitchIv = findViewById(R.id.big_winner_voice_iv);
        coinLay = findViewById(R.id.coin_lay);
        coinTv = findViewById(R.id.coin_tv);
        resultLay = findViewById(R.id.result_lay);
        resultHeadIv = findViewById(R.id.result_head_iv);
        resultNameTv = findViewById(R.id.result_name_tv);
        resultWinTipTv = findViewById(R.id.result_win_tv);
        resultWinTipImage = findViewById(R.id.result_win_image);
        resultWinNumTipTv = findViewById(R.id.result_win_num_tv);
        resultWinTimeTv = findViewById(R.id.result_time_tv);
        resultCrown = findViewById(R.id.popularity_item_crown);
        resultLoseTipTv = findViewById(R.id.result_lose_tip);
        bullet_tip = findViewById(R.id.bullet_tip);
        bullet_tip_content = findViewById(R.id.bullet_tip_content);
        bullet_tip_gift = findViewById(R.id.bullet_tip_gift);
        bullet_tip_count = findViewById(R.id.bullet_tip_count);
        coinTv.setText(String.valueOf(LoginHelper.getCoin()));

        initProgressDrawableIfNeed();
        initVoiceIcon();
        controlTv.setBackground(progressDrawable);
        progressDrawable.setProgress(0);

        View.OnClickListener listener = this::onClick;
        setOnClickListener(listener);
        helpIv.setOnClickListener(listener);
        startTv.setOnClickListener(listener);
        shortJoinTv.setOnClickListener(listener);
        controlTv.setOnClickListener(listener);
        smallIv.setOnClickListener(listener);
        closeIv.setOnClickListener(listener);
        voiceSwitchIv.setOnClickListener(listener);
        coinLay.setOnClickListener(listener);
        HLog.d(TAG, "initView");
    }

    private void checkUpdateCoin(User user) {
        if (coinTv != null) {
            coinTv.setText(String.valueOf(user.getCoin()));
        }
    }

    private void onClick(View v) {
        if (v == startTv) {
            reqStart();
        } else if (v == shortJoinTv) {
            reqJoin(v);
        } else if (v == controlTv) {
            BigWinnerViewModel.BwGameInfo info = viewModel.getGameInfoLiveData().getValue();
            boolean join = info != null && info.getGameInfo().getStateValue() == BigWinner.BigWinnerGameState.BW_STATE_INIT_VALUE;
            if (join) {
                reqJoin(v);
            } else {
                reqVote();
            }
        } else if (v == smallIv) {
            viewModel.changeSmall(true);
        } else if (v == closeIv) {
            DialogBuild.newBuilder(getContext()).setTitle(R.string.tip).setCancelTx(R.string.cancel).setSureTx(R.string.ok)
                    .setContent(R.string.voice_room_winner_close_tip).setDialogCallback(() ->
                            viewModel.reqClose(new LifeSeqCallback(getMainPlugin().getLife()) {
                                @Override
                                public void onSuccess(RspHeadInfo head) {
                                }

                                @Override
                                public void onFail(RspHeadInfo head) {
                                    ToastUtil.show(head.desc);
                                }
                            })).show();
        } else if (v == voiceSwitchIv) {
            updateVoiceIconState();
        } else if (v == coinLay) {
            PublicCoinPayBottomDialogView.showBottomDialog(getContext());
        } else if (v == helpIv) {
            ApiService.of(WebApi.class).gotoWebActivity(v.getContext(), ConfigHelper.getInstance().getConstV3Info().voiceRoomBigWinnerHelpUrl);
        }
    }

    private void initVoiceIcon() {
        isVoiceOn = PrefUserUtil.getInstance().getBoolean(PrefUserUtil.KEY_BIG_WINNER_VOICE_ON, true);
        voiceSwitchIv.setImageResource(isVoiceOn ? R.drawable.big_winner_voice_on : R.drawable.big_winner_voice_off);
    }

    private void updateVoiceIconState() {
        boolean newVoiceState = !isVoiceOn;
        isVoiceOn = newVoiceState;
        voiceSwitchIv.setImageResource(newVoiceState ? R.drawable.big_winner_voice_on : R.drawable.big_winner_voice_off);
        PrefUserUtil.getInstance().setBoolean(PrefUserUtil.KEY_BIG_WINNER_VOICE_ON, newVoiceState);
    }

    private void reqStart() {
        startTv.setEnabled(false);
        BigWinnerPacketSender.reqStart(VoiceRoomService.getInstance().getRid(), new LifeSeqCallback(getMainPlugin().getLife()) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                startTv.setEnabled(true);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                startTv.setEnabled(true);
                ToastUtil.show(head.desc);
            }
        });
    }

    private void reqJoin(View v) {
        v.setEnabled(false);
        viewModel.reqJoin(new LifeSeqCallback(getMainPlugin().getLife()) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                v.setEnabled(true);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                v.setEnabled(true);
                ToastUtil.show(head.desc);
                if (head.code == RspHeadInfo.ERROR_CODE_ROOM_COIN_NOT_ENOUGH) {
                    PublicCoinPayBottomDialogView.showBottomDialog(getContext());
                }
            }
        });
    }

    private void reqVote() {
        playSound(SoundUtil.TYPE_BIG_WINNER_CLICK, new BigWinnerSoundPoolCallback());
        controlTv.setEnabled(false);
        viewModel.reqVote(new LifeSeqCallback(getMainPlugin().getLife()) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                controlTv.setEnabled(true);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                controlTv.setEnabled(true);
                ToastUtil.show(head.desc);
                if (head.code == RspHeadInfo.ERROR_CODE_ROOM_COIN_NOT_ENOUGH) {
                    PublicCoinPayBottomDialogView.showBottomDialog(getContext());
                }
            }
        });
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        initSmallView();
    }

    @Override
    protected void onDetachedFromWindow() {
        handler.removeCallbacksAndMessages(null);
        viewModel.changeSmall(false);
        super.onDetachedFromWindow();
    }

    private void initSmallView() {
        Observer<Boolean> observer = show -> {
            if (show) {
                VoicePluginService.getPlugin(ISmallFrameUtil.class).add(getWinnerSmallView());
                getWinnerSmallView().update();
                setVisibility(GONE);
            } else {
                VoicePluginService.getPlugin(ISmallFrameUtil.class).remove(getWinnerSmallView());
                setVisibility(VISIBLE);
            }
        };
        viewModel.getShowSmallLiveData().observeForever(observer);
        ViewExKt.toLife(this).onDestroy(() -> {
            viewModel.getShowSmallLiveData().removeObserver(observer);
            return null;
        });
    }

    private void playSound(String type) {
        SoundUtil.getInstance().playSound(type);
    }

    private void playSound(String type, SoundPoolCallback callback) {
        if (isVoiceOn) {
            HLog.d(TAG, HLog.USR, "playSound, isVoiceOn=true, type={}", type);
            SoundUtil.getInstance().playSound(type, callback);
        } else {
            HLog.d(TAG, HLog.USR, "playSound, isVoiceOn=false, type={}", type);
        }
    }

    private void resumePlay(int streamId) {
        SoundUtil.getInstance().resumePlay(streamId);
    }

    @Override
    public void onUpdateRoomInfo(@NonNull VoiceRoomInfo roomInfo) {
        if (roomInfo.isSelfAdminOrOwner() != isSelfAdminOrOwner) {
            viewModel.reqSync();
        }
    }

    @Override
    public void onUpdateGameInfo(@NonNull BigWinnerViewModel.BwGameInfo gameInfo) {
        updateAndShowView(gameInfo);
        if (gameInfo.isShowSmall()) {
            getWinnerSmallView().update();
        }
    }

    @Override
    public void onUpdateVoteEvent(@NonNull BigWinnerViewModel.BwGameInfo gameInfo, @NonNull BigWinnerViewModel.VoteEvent voteEvent) {
        HLog.d(TAG, "voteEvent version={}, uid={}", voteEvent.getVersion(), voteEvent.getVoteInfo().getUid());
        updateAndShowView(gameInfo);
        rouletteView.updateVote(voteEvent);
        showVoteAnim(voteEvent.getVoteInfo());
    }

    @Override
    public void onUpdateResultEvent(@NonNull BigWinnerViewModel.ResultEvent resultEvent) {
        BigWinnerViewModel.BwGameInfo gameInfo = viewModel.getGameInfoLiveData().getValue();
        if (gameInfo != null && gameInfo.isOpen() && resultVersion != resultEvent.getVersion() && resultEvent.getVersion() != 0) {
            resultVersion = resultEvent.getVersion();
            showResultAnim(resultEvent.getResultInfo(), gameInfo, resultEvent.getPostResultTime());
        }
        if (gameInfo != null && gameInfo.isShowSmall()) {
            getWinnerSmallView().updateResult(resultEvent);
        }
    }

    @Override
    public void changeVisibility(boolean visible) {
        if (visible) {
            setVisibility(VISIBLE);
        } else {
            setVisibility(GONE);
        }
    }

    private BigWinnerSmallView getWinnerSmallView() {
        if (winnerSmallView == null) {
            winnerSmallView = new BigWinnerSmallView(getContext());
            winnerSmallView.setOnClickListener(v -> {
                viewModel.changeSmall(false);
            });
        }
        return winnerSmallView;
    }

    public static class BigWinnerSoundPoolCallback implements SoundPoolCallback {

        @Override
        public void onPlay(int streamId) {
            SoundUtil.getInstance().resumePlay(streamId);
        }
    }
}
