package com.wepie.wespy.module.voiceroom.bigwinner

import android.view.View
import androidx.lifecycle.Observer
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.voiceroom.IPluginTool
import com.wepie.wespy.module.voiceroom.dataservice.BigWinnerViewModel
import com.wepie.wespy.module.voiceroom.dataservice.BigWinnerViewModel.ResultEvent
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService

class BigWinnerDelegateImpl(
    private val pluginTool: IPluginTool<IBigWinner>
) : IBigWinner by pluginTool.proxy() {

    init {
        val viewModel = VoiceRoomService.getInstance().bigWinnerViewModel
        initUpdateRoomInfo(viewModel)
        initUpdateGameInfo(viewModel)
        initUpdateVoteEvent(viewModel)
        initUpdateResultEvent(viewModel)
        viewModel.trigger()
    }

    private fun initUpdateRoomInfo(viewModel: BigWinnerViewModel) {
        val observer = Observer { roomInfo: VoiceRoomInfo? ->
            roomInfo?.let {
                if (pluginTool.getRealView<View>() == null) {
                    if (roomInfo.isBigWinnerOpen) {
                        viewModel.postOpen(true)
                    }
                } else {
                    onUpdateRoomInfo(roomInfo)
                }
            }
        }
        VoiceRoomService.getInstance().liveData.observeForever(observer)
        pluginTool.onDestroy {
            VoiceRoomService.getInstance().liveData.removeObserver(observer)
        }
    }

    private fun initUpdateGameInfo(viewModel: BigWinnerViewModel) {
        val observer = Observer { gameInfo: BigWinnerViewModel.BwGameInfo ->
            val canLoad = gameInfo.isOpen
            pluginTool.loadView(canLoad)
            if (canLoad) {
                onUpdateGameInfo(gameInfo)
            }
        }
        viewModel.gameInfoLiveData.observeForever(observer)
        pluginTool.onDestroy {
            viewModel.gameInfoLiveData.removeObserver(observer)
        }
    }

    private fun initUpdateVoteEvent(viewModel: BigWinnerViewModel) {
        val observer = Observer { voteEvent: BigWinnerViewModel.VoteEvent? ->
            val gameInfo: BigWinnerViewModel.BwGameInfo? = viewModel.gameInfoLiveData.value
            val canLoad = gameInfo != null && gameInfo.isOpen
            pluginTool.loadView(canLoad)
            if (canLoad && voteEvent != null && voteEvent.voteInfo != null) {
                onUpdateVoteEvent(gameInfo!!, voteEvent)
            }
        }
        viewModel.voteEvent.observeForever(observer)
        pluginTool.onDestroy {
            viewModel.voteEvent.removeObserver(observer)
        }
    }

    private fun initUpdateResultEvent(viewModel: BigWinnerViewModel) {
        val observer = Observer { resultEvent: ResultEvent ->
            onUpdateResultEvent(resultEvent)
        }
        viewModel.resultEventLiveData.observeForever(observer)
        pluginTool.onDestroy {
            viewModel.resultEventLiveData.removeObserver(observer)
        }
    }
}