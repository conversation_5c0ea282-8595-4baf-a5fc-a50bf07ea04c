package com.wepie.wespy.module.voiceroom.bigwinner;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.WebApi;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.widget.rv.SimpleRvAdapter;
import com.huiwan.widget.rv.SimpleRvHolder;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.androidx.BaseDialog;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.net.tcp.sender.BigWinnerPacketSender;

import java.util.Collections;
import java.util.List;

public class BigWinnerOpenDialogFragment extends BaseDialog {
    private static final String TAG = "B_WINNER";

    private ImageView closeIv;
    private ImageView helpIv;
    private RecyclerView giftListRv;
    private TextView startTv;
    private final Adapter adapter = new Adapter();

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.voice_big_winner_open_dialog_view, container, false);
        initViews(view);
        initData();
        initEvents();
        return view;
    }

    private void initViews(View v) {
        closeIv = v.findViewById(R.id.close_iv);
        helpIv = v.findViewById(R.id.help_iv);
        giftListRv = v.findViewById(R.id.gift_rv);
        startTv = v.findViewById(R.id.start_tv);
    }

    private void initData() {
        giftListRv.setLayoutManager(new LinearLayoutManager(getContext(), RecyclerView.HORIZONTAL, false));
        giftListRv.setAdapter(adapter);
        List<Gift> showGifts = ConfigHelper.getInstance().getGiftConfig().getShownListByScene(Gift.GIFT_SCENE_BIG_WINNER);
        Collections.sort(showGifts, (o1, o2) -> o1.getPrice() - o2.getPrice());
        adapter.refresh(showGifts);
        if (!showGifts.isEmpty()) {
            adapter.selectedId = showGifts.get(0).getGift_id();
        }
    }

    private void initEvents() {
        closeIv.setOnClickListener(v -> dismissAllowingStateLoss());
        helpIv.setOnClickListener(v -> ApiService.of(WebApi.class).gotoWebActivity(v.getContext(), ConfigHelper.getInstance().getConstV3Info().voiceRoomBigWinnerHelpUrl));
        startTv.setOnClickListener(v -> {
            if (VoiceRoomService.getInstance().getRoomInfo().bigWinnerOpen) {
                dismissAllowingStateLoss();
                return;
            }
            if (adapter.selectedId > 0) {
                startTv.setEnabled(false);
                BigWinnerPacketSender.reqChangeBigWinnerStatus(VoiceRoomService.getInstance().getRid(), adapter.selectedId, true, new LifeSeqCallback(getViewLifecycleOwner()) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        startTv.setEnabled(true);
                        // 等服务器 push 然后开启具体弹窗
                        dismissAllowingStateLoss();
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        startTv.setEnabled(true);
                        ToastUtil.show(head.desc);
                    }
                });
            } else {
                ToastUtil.debugShow("GIFT ID ERROR " + adapter.selectedId);
            }
        });
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        VoiceRoomService.getInstance().getLiveData().observe(getViewLifecycleOwner(), roomInfo -> {
            if (roomInfo == null || !roomInfo.isSelfAdminOrOwner()) {
                dismissAllowingStateLoss();
            }
        });
    }

    public static void showDialog(Context context) {
        BigWinnerOpenDialogFragment dialogFragment = new BigWinnerOpenDialogFragment();
        dialogFragment.initBottom();
        dialogFragment.initFullWidth();
        dialogFragment.show(context, TAG);
    }


    private static class Adapter extends SimpleRvAdapter<Gift> {

        int selectedId = -1;

        @Override
        public int getLayoutRes() {
            return R.layout.voice_big_winner_open_dialog_list_item;
        }

        @Override
        public void onBindViewHolder(@NonNull SimpleRvHolder holder, int position) {
            if (position == 0) {
                holder.itemView.setPaddingRelative(ScreenUtil.dip2px(20), 0, 0, 0);
            } else {
                holder.itemView.setPaddingRelative(ScreenUtil.dip2px(8), 0, 0, 0);
            }
            super.onBindViewHolder(holder, position);
        }

        @Override
        public void convert(SimpleRvHolder holder, Gift data) {
            holder.itemView.findViewById(R.id.bg_iv).setSelected(data.getGift_id() == selectedId);
            holder.loadImgUrl(R.id.gift_iv, data.getMedia_url(), ImageLoadInfo.getGiftInfo());
            holder.setText(R.id.gift_price_tv, String.valueOf(data.getPrice()));
            holder.setText(R.id.gift_name_tv, data.getName());
            holder.itemView.setOnClickListener(v -> {
                selectedId = data.getGift_id();
                notifyDataSetChanged();
            });
        }
    }
}
