package com.wepie.wespy.module.voiceroom.bigwinner;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.os.SystemClock;

import androidx.annotation.Nullable;

public class BitmapCoverProgressDrawable extends Drawable {

    private Bitmap mBgBmp;
    private Bitmap mFgBmp;
    private float mProgress = 0.5f;
    private float mTargetProgress = 0.5f;
    private final Paint mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private final Rect srcRect = new Rect();
    private final RectF dstRectF = new RectF();


    public void setBgBmp(Bitmap bitmap) {
        mBgBmp = bitmap;
        invalidateSelf();
    }

    public void setFgBmp(Bitmap bitmap) {
        mFgBmp = bitmap;
        invalidateSelf();
    }

    public void animToProgress(float progress) {
        this.mTargetProgress = progress;
        if (mTargetProgress > 1) {
            mTargetProgress = 1;
        }
        if (mTargetProgress < 0) {
            mTargetProgress = 0;
        }
        invalidateSelf();
        unscheduleSelf(changeAnimRunner);
        scheduleSelf(changeAnimRunner, SystemClock.uptimeMillis() + 16);
    }

    public void setProgress(float progress) {
        this.mTargetProgress = progress;
        if (mTargetProgress > 1) {
            mTargetProgress = 1;
        }
        if (mTargetProgress < 0) {
            mTargetProgress = 0;
        }
        mProgress = mTargetProgress;
        invalidateSelf();
    }

    private final Runnable changeAnimRunner = new Runnable() {
        @Override
        public void run() {
            float diff = mTargetProgress - mProgress;
            if (Math.abs(mTargetProgress - mProgress) > 0.001) {
                float change = diff;
                float changeFa = Math.min(Math.abs(diff)/8, 0.03f);
                do{
                    change = change / 2;
                } while (Math.abs(change) > changeFa);
                mProgress = mProgress + change;
                scheduleSelf(changeAnimRunner, SystemClock.uptimeMillis() + 16);
                invalidateSelf();
            }
        }
    };


    @Override
    public void draw(Canvas canvas) {
        if (mBgBmp == null || mFgBmp == null) {
            return;
        }
        srcRect.top = 0;
        srcRect.left = 0;
        srcRect.right = mBgBmp.getWidth();
        srcRect.bottom = mBgBmp.getHeight();
        dstRectF.set(0, 0, getBounds().width(), getBounds().height());
        canvas.drawBitmap(mBgBmp, srcRect, dstRectF, mPaint);

        srcRect.top = 0;
        srcRect.left = 0;
        srcRect.right = (int)(mFgBmp.getWidth() * Math.min(mProgress, 1));
        srcRect.bottom = mFgBmp.getHeight();
        dstRectF.set(0, 0, (int)(getBounds().width() * Math.min(mProgress, 1)), getBounds().height());
        canvas.drawBitmap(mFgBmp, srcRect, dstRectF, mPaint);
    }

    @Override
    public void setAlpha(int alpha) {
        mPaint.setAlpha(alpha);
    }

    @Override
    public void setColorFilter(@Nullable ColorFilter colorFilter) {
        mPaint.setColorFilter(colorFilter);
    }

    @Override
    public int getOpacity() {
        return PixelFormat.TRANSPARENT;
    }
}
