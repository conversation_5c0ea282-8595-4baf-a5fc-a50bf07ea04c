package com.wepie.wespy.module.voiceroom.bigwinner.widget;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.TimeInterpolator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.SystemClock;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.PathInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.CustomCircleImageView;
import com.huiwan.widget.HeadImageLoader;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.libimageloader.WpSimpleImageLoadListener;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.voiceroom.dataservice.BigWinnerViewModel;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.net.tcp.packet.BigWinner;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class WinnerRouletteView extends FrameLayout {
    private static final String TAG = "WinnerRouletteView";
    private static final int USER_COUNT = 8;
    private static final int DEFAULT_CHILD_GRAVITY = Gravity.TOP | Gravity.START;
    private static final long GIFT_ANIM_TIME = 500;

    private double mDiffDegree = 0.0f;
    private final Paint mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private ValueAnimator animator;
    private final List<UserView> userViewList = new ArrayList<>();
    private int voteAnimVersion = 0;

    private final List<GiftAnim> animList = new ArrayList<>();
    private ImageView giftIv;
    private TextView totalNumTv;
    private Bitmap giftBmp;
    private int gameStatus = 0;
    private final View.OnClickListener clickHeadListener = this::clickHead;
    private final TimeInterpolator animInterpolator;

    public WinnerRouletteView(Context context) {
        this(context, null);
    }

    public WinnerRouletteView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public WinnerRouletteView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        Path path = new Path();
        path.moveTo(0, 0);
        path.cubicTo(0.2f, 0, 0, 1f, 1f, 1f);
        animInterpolator = new PathInterpolator(path);
    }

    public void startAnim(BigWinner.BWRoundResultInfo resultInfo) {
        stopAnim();

        for (UserView userView : userViewList) {
            if (userView.uid == resultInfo.getUid()) {
                LayoutParams lp = (LayoutParams) userView.frameLayout.getLayoutParams();
                int curDegree = lp.curDegree;
                int toRotate = 1350 - curDegree;
                if (resultInfo.getStatusValue() == BigWinner.BWResultStatus.BW_RESULT_WIN_VALUE) {
                    toRotate += 180;
                }
                HLog.d(TAG, "find uid={}, curDegree={}, toRotate={}", userView.uid, curDegree, toRotate);
                animator = ValueAnimator.ofFloat((int) mDiffDegree, toRotate);
                animator.addUpdateListener(animation -> {
                    mDiffDegree = (Float) animation.getAnimatedValue();
                    invalidate();
                    requestLayout();
                });
                animator.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        animator = null;
                    }
                });
                animator.setInterpolator(animInterpolator);
                animator.setDuration(5000);
                animator.start();
                break;
            }
        }
    }

    public void showJoinIcon(BigWinner.BWGameInfo gameInfo){
        updatePlayers(gameInfo);
        stopAnim();
    }

    void stopAnim() {
        if (animator != null) {
            animator.cancel();
        }
        animator = null;
    }

    public void resetRotate() {
        mDiffDegree = 0;
        requestLayout();
        invalidate();
    }


    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        final int count = getChildCount();
        if (count < 1) {
            return;
        }

        final int parentLeft = getPaddingLeft();
        final int parentRight = r - l - getPaddingRight();

        final int parentTop = getPaddingTop();
        final int parentBottom = b - t - getPaddingTop();

        int cx = (parentLeft + parentRight) / 2;
        int cy = (parentBottom + parentTop) / 2;

        double personRadius = ScreenUtil.dip2px(90);

        int circleCount = 0;
        for (int i = 0; i < count; i++) {
            View child = getChildAt(i);
            LayoutParams lp = (LayoutParams) child.getLayoutParams();
            if (lp.isCircleItem && child.getVisibility() != GONE) {
                circleCount++;
            }
        }
        double personRadians = Math.PI;
        if (circleCount > 0) {
            personRadians = 2 * Math.PI / circleCount;
        }


        int cCount = 0;
        for (int i = 0; i < count; i++) {
            final View child = getChildAt(i);
            if (child.getVisibility() != GONE) {
                final int width = child.getMeasuredWidth();
                final int height = child.getMeasuredHeight();
                LayoutParams lp = (LayoutParams) child.getLayoutParams();
                if (lp.isCircleItem) {
                    int childLeft;
                    int childTop;
                    double baseRadians = personRadians * cCount - Math.PI / 2 + Math.PI / circleCount;
                    lp.curDegree = (int) Math.toDegrees(baseRadians);
                    double radians = baseRadians + Math.toRadians(mDiffDegree);
                    double y = personRadius * Math.sin(radians);
                    double x = personRadius * Math.cos(radians);

                    childLeft = (int) (cx + x - width / 2) + getPaddingLeft();
                    childTop = (int) (cy + y - height / 2) + getPaddingTop();

                    child.layout(childLeft, childTop, childLeft + width, childTop + height);
                    cCount++;
                } else {

                    int childLeft;
                    int childTop;

                    int gravity = lp.gravity;
                    if (gravity == -1) {
                        gravity = DEFAULT_CHILD_GRAVITY;
                    }

                    final int layoutDirection = getLayoutDirection();
                    final int absoluteGravity = Gravity.getAbsoluteGravity(gravity, layoutDirection);
                    final int verticalGravity = gravity & Gravity.VERTICAL_GRAVITY_MASK;

                    switch (absoluteGravity & Gravity.HORIZONTAL_GRAVITY_MASK) {
                        case Gravity.CENTER_HORIZONTAL:
                            childLeft = parentLeft + (parentRight - parentLeft - width) / 2 +
                                    lp.getMarginStart() - lp.getMarginEnd();
                            break;
                        case Gravity.END:
                        case Gravity.RIGHT:
                            childLeft = parentRight - width - lp.getMarginEnd();
                            break;
                        case Gravity.START:
                        case Gravity.LEFT:
                        default:
                            childLeft = parentLeft + lp.getMarginStart();
                    }

                    switch (verticalGravity) {
                        case Gravity.TOP:
                            childTop = parentTop + lp.topMargin;
                            break;
                        case Gravity.CENTER_VERTICAL:
                            childTop = parentTop + (parentBottom - parentTop - height) / 2 +
                                    lp.topMargin - lp.bottomMargin;
                            break;
                        case Gravity.BOTTOM:
                            childTop = parentBottom - height - lp.bottomMargin;
                            break;
                        default:
                            childTop = parentTop + lp.topMargin;
                    }

                    child.layout(childLeft, childTop, childLeft + width, childTop + height);
                }

            }
        }
    }

    @Override
    protected void dispatchDraw(Canvas canvas) {

        mPaint.setColor(0xff6d50e1);
        float radius = getWidth() / 2.0f;
        canvas.drawCircle(getWidth() / 2.0f, getHeight() / 2.0f, radius, mPaint);
        mPaint.setColor(0xfff9de55);
        radius = radius - dp2px(20);
        canvas.drawCircle(getWidth() / 2.0f, getHeight() / 2.0f, radius, mPaint);
        drawOvalsAndLines(canvas);
        super.dispatchDraw(canvas);
        drawAnim(canvas);
        if (gameStatus == BigWinner.BigWinnerGameState.BW_STATE_GAMING_VALUE) {
            postInvalidate();
        }
    }

    private void drawAnim(Canvas canvas) {
        if (giftBmp == null) {
            return;
        }
        Iterator<GiftAnim> ite = animList.iterator();
        while (ite.hasNext()) {
            GiftAnim anim = ite.next();
            if (SystemClock.elapsedRealtime() - anim.startTime > GIFT_ANIM_TIME) {
                ite.remove();
            } else {
                drawAnimItem(canvas, anim);
            }
        }
    }

    private final Rect rect = new Rect();
    private final Paint paint = new Paint();

    private void drawAnimItem(Canvas canvas, GiftAnim anim) {
        int cx = getWidth() / 2;
        int cy = getHeight() / 2;
        float fraction = (SystemClock.elapsedRealtime() - anim.startTime) / (float) GIFT_ANIM_TIME;
        if (fraction < 0.3f) {
            paint.setAlpha((int) (fraction * 3.33333 * 255));
        } else {
            paint.setAlpha(255);
        }
        int l = (int) ((cx - anim.from.width() / 2 - anim.from.left) * fraction + anim.from.left);
        int t = (int) ((cy - anim.from.height() / 2 - anim.from.top) * fraction + anim.from.top);
        rect.set(l, t, l + anim.from.width(), t + anim.from.height());
        canvas.drawBitmap(giftBmp, null, rect, paint);
    }

    private void drawOvalsAndLines(Canvas canvas) {
        Drawable oBlue = ContextCompat.getDrawable(getContext(), R.drawable.roulette_oval_blue);
        Drawable oRed = ContextCompat.getDrawable(getContext(), R.drawable.roulette_oval_red);
        Drawable d1 = oBlue;
        Drawable d2 = oRed;
        if (animator != null) {
            if ((System.currentTimeMillis() / 100) % 2 == 1) {
                d1 = oBlue;
                d2 = oRed;
            } else {
                d1 = oRed;
                d2 = oBlue;
            }
        }
        double radian = Math.PI / 12;
        int cx = getWidth() / 2;
        int cy = getHeight() / 2;
        int r = Math.min(getHeight(), getWidth()) / 2 - dp2px(10) - 2;
        for (int i = 0; i < 24; i++) {
            Drawable d = i % 2 == 1 ? d1 : d2;
            assert d != null;
            double rx = Math.cos(radian * i) * r + cx - (float) d.getIntrinsicWidth() / 2;
            double ry = Math.sin(radian * i) * r + cy - (float) d.getIntrinsicHeight() / 2;
            int l = (int) rx;
            int t = (int) ry;
            int right = l + d.getIntrinsicWidth();
            int bottom = t + d.getIntrinsicHeight();
            d.setBounds(l, t, right, bottom);
            d.draw(canvas);
        }
        int count = getChildCount();
        if (count < 2) {
            return;
        }
        int vCount = 0;
        for (int i = 0; i < count; i++) {
            View child = getChildAt(i);
            LayoutParams lp = (LayoutParams) child.getLayoutParams();
            if (child.isShown() && lp.isCircleItem) {
                vCount++;
            }
        }
        if (vCount <= 0) {
            return;
        }
        radian = Math.PI * 2 / vCount;
        double lineStartRadians = Math.toRadians(mDiffDegree) - Math.PI / 2;
        r = Math.min(getHeight(), getWidth()) / 2 - dp2px(20);
        mPaint.setColor(0x80ffffff);
        mPaint.setStyle(Paint.Style.FILL);
        mPaint.setStrokeWidth(1);
        for (int i = 0; i < vCount; i++) {
            double radians = lineStartRadians + radian * i;
            double ex = Math.cos(radians) * r + cx;
            double ey = Math.sin(radians) * r + cy;
            canvas.drawLine(cx, cy, (float) ex, (float) ey, mPaint);
        }
    }


    @Override
    protected void onDetachedFromWindow() {
        stopAnim();
        super.onDetachedFromWindow();
    }

    private int dp2px(float dpValue) {
        final float scale = getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }

    public void update(BigWinner.BWGameInfo gameInfo, BigWinner.BWGamerInfo selfGamer) {
        HLog.d(TAG, "update: {}", gameInfo);
        gameStatus = gameInfo.getStateValue();
        updateGiftInfo(gameInfo);
        if (gameInfo.getStateValue() == BigWinner.BigWinnerGameState.BW_STATE_INIT_VALUE) {
            showInit(gameInfo);
            mDiffDegree = 0;
        } else if (gameInfo.getStateValue() == BigWinner.BigWinnerGameState.BW_STATE_START_COUNTDOWN_VALUE) {
            updatePlayers(gameInfo);
        } else if (gameInfo.getStateValue() == BigWinner.BigWinnerGameState.BW_STATE_GAMING_VALUE) {
            updatePlayers(gameInfo);
        } else if (gameInfo.getStateValue() == BigWinner.BigWinnerGameState.BW_STATE_SHOW_RESULT_VALUE) {
            if (gameInfo.hasRoundResult()) {
                updatePlayers(gameInfo);
                int resUser = gameInfo.getRoundResult().getUid();
                HLog.d(TAG, "has round result and res User: {}", resUser);
            }
        }
    }

    private void updateGiftInfo(BigWinner.BWGameInfo gameInfo) {
        if (giftIv != null) {
            Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(gameInfo.getGiftId());
            if (gift != null) {
                WpImageLoader.load(gift.getMedia_url(), giftIv, ImageLoadInfo.getGiftInfo());
                WpImageLoader.load(gift.getMedia_url(), null, ImageLoadInfo.newGiftInfo().owner(this), new WpSimpleImageLoadListener() {
                    @Override
                    public boolean onComplete(String uri, Drawable data) {
                        if (data instanceof BitmapDrawable) {
                            giftBmp = ((BitmapDrawable) data).getBitmap();
                        }
                        return super.onComplete(uri, data);
                    }
                });
            } else {
                ToastUtil.debugShow("GIFT NULL ID " + gameInfo.getGiftId());
            }
        }
        if (totalNumTv != null) {
            totalNumTv.setText(ResUtil.getResource().getString(R.string.x_number, gameInfo.getRewardPool()));
        }
    }

    private void showInit(BigWinner.BWGameInfo gameInfo) {
        int div = USER_COUNT - userViewList.size();
        for (int i = 0; i < div; i++) {
            UserView userView = new UserView(getContext());
            userView.frameLayout.setOnClickListener(clickHeadListener);
            addView(userView.frameLayout, new LayoutParams(ScreenUtil.dip2px(70), ScreenUtil.dip2px(70), true));
            userViewList.add(userView);
        }
        for (int i = 0; i < USER_COUNT; i++) {
            UserView userView = userViewList.get(i);
            if (i < gameInfo.getGamerInfosCount()) {
                BigWinner.BWGamerInfo gamerInfo = gameInfo.getGamerInfos(i);
                userView.uid = gamerInfo.getUid();
                loadHead(userView);
            } else {
                userView.uid = 0;
                userView.headIv.setImageDrawable(null);
            }
            userView.frameLayout.setVisibility(VISIBLE);
        }
        requestLayout();
        invalidate();
    }

    private void updatePlayers(BigWinner.BWGameInfo gameInfo) {
        List<BigWinner.BWGamerInfo> playerInfoList = gameInfo.getGamerInfosList();
        int div = playerInfoList.size() - userViewList.size();
        for (int i = 0; i < div; i++) {
            UserView userView = new UserView(getContext());
            userView.frameLayout.setOnClickListener(clickHeadListener);
            userViewList.add(userView);
            addView(userView.frameLayout, new LayoutParams(ScreenUtil.dip2px(70), ScreenUtil.dip2px(70), true));
        }
        int start = 0;
        for (; start < playerInfoList.size(); start++) {
            BigWinner.BWGamerInfo playerInfo = playerInfoList.get(start);
            UserView userView = userViewList.get(start);
            if (!userView.frameLayout.isShown()) {
                userView.frameLayout.setVisibility(VISIBLE);
            }
            if (userView.uid != playerInfo.getUid()) {
                userView.uid = playerInfo.getUid();
                loadHead(userView);
            }
        }
        for (; start < userViewList.size(); start++) {
            UserView userView = userViewList.get(start);
            userView.uid = 0;
            userView.frameLayout.setVisibility(GONE);
        }
        requestLayout();
        invalidate();
    }

    private void clickHead(View v) {
        for (UserView userView : userViewList) {
            if (userView.frameLayout == v) {
                if (userView.uid > 0) {
                    JumpUtil.enterUserInfoDetailFromVoiceRoom(getContext(), userView.uid, VoiceRoomService.getInstance().getRid());
                }
                break;
            }
        }
    }


    public void updateVote(BigWinnerViewModel.VoteEvent voteEvent) {
        if (voteEvent.getVersion() == voteAnimVersion) {
            return;
        }
        voteAnimVersion = voteEvent.getVersion();
        int uid = voteEvent.getVoteInfo().getUid();
        for (UserView userView : userViewList) {
            if (userView.uid == uid) {
                checkAddAnimTask(userView);
                break;
            }
        }
    }

    private void checkAddAnimTask(UserView userView) {
        if (!userView.frameLayout.isShown()) {
            return;
        }
        GiftAnim anim = new GiftAnim();
        Rect rect = new Rect();
        rect.left = (int) userView.frameLayout.getX();
        rect.top = (int) userView.frameLayout.getY();
        rect.right = rect.left + userView.frameLayout.getWidth();
        rect.bottom = rect.top + userView.frameLayout.getHeight();
        rect.left += dp2px(13);
        rect.right -= dp2px(13);
        rect.top += dp2px(13);
        rect.bottom -= dp2px(13);
        anim.from.set(rect);
        anim.startTime = SystemClock.elapsedRealtime();
        HLog.d(TAG, "checkAddAnimTask from={}", rect);
        animList.add(anim);
    }

    private void loadHead(UserView userView) {
        UserService.get().getCacheSimpleUser(userView.uid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                WpImageLoader.load(simpleInfo.getHeadimgurl(), userView.headIv, HeadImageLoader.genHeadLoadInfo());
            }

            @Override
            public void onUserInfoFailed(String description) {
            }
        });
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        giftIv = findViewById(R.id.gift_iv);
        totalNumTv = findViewById(R.id.num_tv);
    }

    @Override
    protected boolean checkLayoutParams(ViewGroup.LayoutParams p) {
        return p instanceof LayoutParams;
    }

    @Override
    public FrameLayout.LayoutParams generateLayoutParams(AttributeSet attrs) {
        return new LayoutParams(getContext(), attrs);
    }

    @Override
    protected ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams lp) {
        return new LayoutParams(lp);
    }

    @Override
    protected WinnerRouletteView.LayoutParams generateDefaultLayoutParams() {
        return new LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
    }

    private static class UserView {
        public int uid;
        public CustomCircleImageView headIv;
        FrameLayout frameLayout;

        public UserView(Context context) {
            frameLayout = new FrameLayout(context);
            headIv = new CustomCircleImageView(context);
            frameLayout.setBackgroundResource(R.drawable.big_winner_sit_empty);
            FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(ScreenUtil.dip2px(45), ScreenUtil.dip2px(45));
            lp.gravity = Gravity.CENTER;
            frameLayout.addView(headIv, lp);
        }
    }

    public static class LayoutParams extends FrameLayout.LayoutParams {
        public boolean isCircleItem = false;
        public int curDegree = 0;

        public LayoutParams(int width, int height) {
            super(width, height);
        }

        public LayoutParams(int width, int height, boolean isCircleItem) {
            super(width, height);
            this.isCircleItem = isCircleItem;
        }

        public LayoutParams(@NonNull Context c, @Nullable AttributeSet attrs) {
            super(c, attrs);
        }

        public LayoutParams(@NonNull ViewGroup.LayoutParams source) {
            super(source);
        }
    }

    static class GiftAnim {
        Rect from = new Rect();
        long startTime;
    }
}
