package com.wepie.wespy.module.voiceroom.wedding;

import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.os.Handler;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.View;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.IMMHelper;
import com.huiwan.base.util.PressUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.net.tcp.sender.MarryPacketSender;

public class WeddingPastorActionDialog extends FrameLayout {
    public static final int STATE_IDO = 1;
    public static final int STATE_CONFESS = 2;
    private int state;
    private TextView contentTv;
    private ImageView micIcon;
    private TextView confessBtn;
    private EditText editText;
    private TextView sendBtn;
    private View confessBottomPadding;
    private TextView textConfessTv;
    private Handler confessHandler = new Handler();
    private Handler sendHandler = new Handler();
    private boolean canConfess;
    private View sendBottomPadding;
    private AnimationDrawable speakAnim;

    public WeddingPastorActionDialog(Context context) {
        super(context);
        init();
    }

    public WeddingPastorActionDialog(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.wedding_pastor_action_dialog, this);
        initView();
    }

    private void initView() {
        contentTv = (TextView) findViewById(R.id.content_tv);
        micIcon = (ImageView) findViewById(R.id.mic_icon);
        confessBtn = (TextView) findViewById(R.id.confess_btn);
        editText = (EditText) findViewById(R.id.edit_text);
        sendBtn = (TextView) findViewById(R.id.send_btn);
        confessBottomPadding = (View) findViewById(R.id.confess_bottom_padding);
        sendBottomPadding = (View) findViewById(R.id.send_bottom_padding);
        textConfessTv = (TextView) findViewById(R.id.text_confess_tv);

        micIcon.setImageResource(R.drawable.wedding_room_voice_anim);

        setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {}
        });
        PressUtil.addPressEffect(confessBtn);
        PressUtil.addPressEffect(sendBtn);
        confessBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (state == STATE_IDO) {
                    MarryPacketSender.weddingIdoReq(new LifeSeqCallback(WeddingPastorActionDialog.this) {
                        @Override
                        public void onSuccess(RspHeadInfo head) {
                            hideDialog();
                        }

                        @Override
                        public void onFail(RspHeadInfo head) {
                            ToastUtil.show(head.desc);
                        }
                    });
                } else if (state == STATE_CONFESS) {
                    MarryPacketSender.weddingConfessReq("", new LifeSeqCallback(WeddingPastorActionDialog.this) {
                        @Override
                        public void onSuccess(RspHeadInfo head) {
                            hideDialog();
                        }

                        @Override
                        public void onFail(RspHeadInfo head) {
                            ToastUtil.show(head.desc);
                        }
                    });
                }
            }
        });
        sendBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                MarryPacketSender.weddingConfessReq(String.valueOf(editText.getText()), new LifeSeqCallback(WeddingPastorActionDialog.this) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        hideDialog();
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                });
            }
        });
        textConfessTv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                showSelected(editText, sendBtn, sendBottomPadding);
            }
        });
        editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() > 0) {
                    setSendBtnEnable(true);
                } else {
                    setSendBtnEnable(false);
                }
            }
        });
        setSendBtnEnable(false);
    }

    private void setSendBtnEnable(boolean enable) {
        sendBtn.setEnabled(enable);
        sendBtn.setBackgroundResource(enable ? R.drawable.shape_ff92a9_ff5a7c_border20 : R.drawable.shape_cfcfcf_corner20);
    }

    public void showIdo(int currLeftTime) {
        DialogUtil.hideWeddingDialog();

        state = STATE_IDO;
        confessHandler.removeCallbacksAndMessages(null);
        setVisibility(VISIBLE);

        showSelected(contentTv, micIcon, confessBtn, confessBottomPadding);
        contentTv.setText(R.string.wedding_pastor_action_c_say_i_do);
        showConfessBtnCountDown(currLeftTime / 1000, ResUtil.getStr(R.string.wedding_i_do), 7);

        showSpeakAnim();
    }

    private void showSpeakAnim() {
        speakAnim = (AnimationDrawable) micIcon.getDrawable();
        speakAnim.start();
    }

    public void showConfessDialog(int currLeftTime) {
        DialogUtil.hideWeddingDialog();

        state = STATE_CONFESS;
        confessHandler.removeCallbacksAndMessages(null);
        sendHandler.removeCallbacksAndMessages(null);
        setVisibility(VISIBLE);

        showSelected(contentTv, micIcon, confessBtn, textConfessTv);
        contentTv.setText(R.string.wedding_pastor_action_confession_say);
        showConfessBtnCountDown(currLeftTime / 1000, ResUtil.getStr(R.string.wedding_pastor_action_confession_end), 55);
        showSendBtnCountDown(currLeftTime / 1000);

        showSpeakAnim();
    }

    private void showConfessBtnCountDown(final int currLeftTime, final String content, final int cantClickTime) {
        if (currLeftTime <= 0) {
            setVisibility(GONE);
            return;
        }

        String btnStr = ResUtil.getStr(R.string.wedding_pastor_action_end_count_down_s_x, content, currLeftTime);
        confessBtn.setText(btnStr);
        if (currLeftTime <= cantClickTime) {
            confessBtn.setBackgroundResource(R.drawable.shape_ff92a9_ff5a7c_border20);
            confessBtn.setEnabled(true);
        } else {
            confessBtn.setBackgroundResource(R.drawable.shape_cfcfcf_corner20);
            confessBtn.setEnabled(false);
        }

        confessHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                showConfessBtnCountDown(currLeftTime - 1, content, cantClickTime);
            }
        }, 1000);
    }

    private void showSendBtnCountDown(final int currLeftTime) {
        if (currLeftTime <= 0) {
            setVisibility(GONE);
            return;
        }
        String btnStr = ResUtil.getStr(R.string.wedding_pastor_action_end_count_down_s_x, ResUtil.getStr(R.string.sure), currLeftTime);
        sendBtn.setText(btnStr);
        sendHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                showSendBtnCountDown(currLeftTime - 1);
            }
        }, 1000);
    }


    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        removeCountDownCallbackAndAnim();
    }

    private void removeCountDownCallbackAndAnim() {
        confessHandler.removeCallbacksAndMessages(null);
        sendHandler.removeCallbacksAndMessages(null);

        if (speakAnim != null && speakAnim.isRunning()) speakAnim.stop();
    }

    private void showSelected(View... views) {
        hideAll();

        for (View view : views) {
            view.setVisibility(VISIBLE);
        }
    }

    public void hideDialog() {
        setVisibility(GONE);
        removeCountDownCallbackAndAnim();
        IMMHelper.hideSoftInput(getContext(), getWindowToken());
    }

    private void hideAll() {
        contentTv.setVisibility(GONE);
        micIcon.setVisibility(GONE);
        confessBtn.setVisibility(GONE);
        editText.setVisibility(GONE);
        sendBtn.setVisibility(GONE);
        confessBottomPadding.setVisibility(GONE);
        sendBottomPadding.setVisibility(GONE);
        textConfessTv.setVisibility(GONE);
    }

    public boolean isTextConfessShowing() {
        return getVisibility() == VISIBLE && editText.getVisibility() == VISIBLE;
    }

    public void showVoiceConfessDialog() {
        showSelected(contentTv, micIcon, confessBtn, textConfessTv);
    }
}
