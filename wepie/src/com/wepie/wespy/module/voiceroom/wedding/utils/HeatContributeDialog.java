package com.wepie.wespy.module.voiceroom.wedding.utils;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.decorate.NameTextView;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.OnItemClickListener;
import com.huiwan.widget.rv.SimpleRvAdapter;
import com.huiwan.widget.rv.SimpleRvHolder;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.TitleWebViewDialog;
import com.wepie.wespy.helper.dialog.BaseDialogFragment;
import com.wepie.wespy.helper.view.SimpleDividerDecoration;
import com.wepie.wespy.module.common.jump.JumpUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * date 2019/1/12
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class HeatContributeDialog extends BaseDialogFragment {
    @SuppressWarnings("all")
    private Map<Integer, Integer> map = new HashMap<>();
    private int rid;
    private String url = "";
    private boolean isHotBadge = false;
    private TextView titleTv;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        View v = inflater.inflate(R.layout.room_contribute_view, container, false);
        init(v);
        refresh(v);

        v.findViewById(R.id.close_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissAllowingStateLoss();
            }
        });
        v.findViewById(R.id.help_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String title;
                if (isHotBadge){
                    title = ResUtil.getStr(R.string.hotbadge_help);
                } else {
                    title = ResUtil.getStr(R.string.about_wedding_popularity);
                }
                TitleWebViewDialog.show(v.getContext(), title, url, R.drawable.ic_close_3);
            }
        });

        titleTv = v.findViewById(R.id.title_tv);
        if (isHotBadge){
            titleTv.setText(R.string.hotbadge_contribution_list);
        } else {
            titleTv.setText(R.string.room_contribute_title);
        }
        return v;
    }

    private void init(View v) {
        RecyclerView rv = v.findViewById(R.id.contribute_rv);
        rv.setLayoutManager(new LinearLayoutManager(rv.getContext()));
        rv.addItemDecoration(new SimpleDividerDecoration());
        rv.setAdapter(adapter);
    }

    private void refresh(final View v) {
        List<Map.Entry<Integer, Integer>> list = new ArrayList<>(map.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<Integer, Integer>>() {
            @Override
            public int compare(Map.Entry<Integer, Integer> o1, Map.Entry<Integer, Integer> o2) {
                return o2.getValue().compareTo(o1.getValue());
            }
        });
        if (list.size() > 10) {
            list = list.subList(0, 10);
        }
        adapter.refresh(list);
        adapter.setOnItemClickListener(new OnItemClickListener<Map.Entry<Integer, Integer>>() {
            @Override
            public void onClickItem(Map.Entry<Integer, Integer> data, int index) {
                JumpUtil.enterUserInfoDetailFromVoiceRoom(v.getContext(), data.getKey(), rid);
            }
        });
    }

    private SimpleRvAdapter<Map.Entry<Integer, Integer>> adapter = new SimpleRvAdapter<Map.Entry<Integer, Integer>>() {
        @Override
        public int getLayoutRes() {
            return R.layout.room_contribute_item_view;
        }

        @Override
        public void convert(final SimpleRvHolder holder, Map.Entry<Integer, Integer> data) {
            if (holder.getAdapterPosition() == 0) {
                holder.setText(R.id.rank_tv, "");
                holder.setBgRes(R.id.rank_tv, R.drawable.contribute_rank_1);
            } else {
                holder.setText(R.id.rank_tv, String.valueOf(holder.getAdapterPosition() + 1));
                holder.setBgRes(R.id.rank_tv, R.drawable.transparent_background);
            }
            holder.setText(R.id.contribute_tv, String.valueOf(data.getValue()));
            UserService.get().getCacheSimpleUser(data.getKey(), new LifeUserSimpleInfoCallback(holder.itemView) {
                @Override
                public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                    ((NameTextView)holder.itemView.findViewById(R.id.name_tv)).setUserName(simpleInfo);
                    holder.loadCircleHeadImg(R.id.head_iv, simpleInfo.getHeadimgurl());
                }

                @Override
                public void onUserInfoFailed(String description) {
                }
            });
        }
    };


    public static void show(Context context, Map<Integer,Integer> map, int rid, String url) {
        HeatContributeDialog dialog = new HeatContributeDialog();
        dialog.map = map;
        dialog.rid = rid;
        dialog.url = url;
        dialog.show(context);
    }

    public static void show(Context context, Map<Integer,Integer> map, int rid, String url, boolean isHotBadge) {
        HeatContributeDialog dialog = new HeatContributeDialog();
        dialog.map = map;
        dialog.rid = rid;
        dialog.url = url;
        dialog.isHotBadge = isHotBadge;
        dialog.show(context);
    }
}
