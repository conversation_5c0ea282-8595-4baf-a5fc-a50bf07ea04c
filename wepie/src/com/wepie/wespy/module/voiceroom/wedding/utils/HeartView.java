package com.wepie.wespy.module.voiceroom.wedding.utils;

import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.LinearInterpolator;

import com.wepie.wespy.R;

/**
 * date 2019/1/10
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class HeartView extends View {
    private Paint sinPaint = new Paint();
    private Paint heartPaint = new Paint();
    private Path pathSin = new Path();
    private ValueAnimator animator;
    private float waveHeightPercent = 0.5f;
    private Bitmap heartBmp;
    private int frontWaveColor;
    private int behindWaveColor;
    private Rect drawingRect = new Rect();

    public HeartView(Context context) {
        this(context, null);
    }

    public HeartView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.HeartView, 0 ,0);
        int resImg = typedArray.getResourceId(R.styleable.HeartView_backgroundClipImg, R.drawable.heart_white);
        frontWaveColor = typedArray.getColor(R.styleable.HeartView_frontWaveColor, 0xccffc8d8);
        behindWaveColor = typedArray.getColor(R.styleable.HeartView_behindWaveColor, 0x99ff3174);
        heartPaint.setColor(Color.WHITE);
        heartPaint.setAntiAlias(true);
        sinPaint.setAntiAlias(true);
        sinPaint.setStyle(Paint.Style.FILL);
        heartBmp = BitmapFactory.decodeResource(getResources(), resImg);
        typedArray.recycle();
        initAnimator();
    }

    public void updateWaveHeight(float percent) {
        waveHeightPercent = percent;
        updatePathSin();
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        getDrawingRect(drawingRect);
        updatePathSin();
    }

    private void updatePathSin() {
        pathSin.reset();
        int width = getWidth();
        int height = getHeight();
        if (width == 0 || height == 0) {
            return;
        }

        float cy = (1-waveHeightPercent) * height;
        float sh = height / 8;
        pathSin.moveTo(0, cy);
        pathSin.quadTo(width/4, cy-sh, width / 2, cy);
        pathSin.quadTo(width * 3 / 4, cy + sh, width, cy);

        pathSin.quadTo(width * 5 / 4, cy-sh, width * 3 / 2, cy);
        pathSin.quadTo(width * 7 / 4, cy + sh, width * 2, cy);

        pathSin.lineTo(width * 2, height * 2);
        pathSin.lineTo(0, height * 2);
        pathSin.close();
    }


    private PorterDuffXfermode clipMode = new PorterDuffXfermode(PorterDuff.Mode.MULTIPLY);

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int saveCount = canvas.saveLayer(0, 0, getWidth(), getHeight(), null, Canvas.ALL_SAVE_FLAG);

        drawSin1(canvas);
        drawSin2(canvas);

        heartPaint.setXfermode(clipMode);
        canvas.drawBitmap(heartBmp, null, drawingRect, heartPaint);
        heartPaint.setXfermode(null);

        canvas.restoreToCount(saveCount);
    }



    private long lastUpdateTime;
    private void initAnimator() {
        animator = ValueAnimator.ofInt(0, 1000);
        lastUpdateTime = System.currentTimeMillis();
        animator.setDuration(5000);
        animator.setInterpolator(new LinearInterpolator());
        animator.setRepeatMode(ValueAnimator.RESTART);
        animator.setRepeatCount(ValueAnimator.INFINITE);
        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                long now = System.currentTimeMillis();
                int dt = (int)(now - lastUpdateTime);
                lastUpdateTime = now;
                updateTranslates(dt);
                invalidate();
            }
        });

    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        animator.start();
    }

    private void drawSin1(Canvas canvas) {
        sinPaint.setColor(frontWaveColor);
        canvas.save();
        canvas.translate(translate1, 0);
        canvas.drawPath(pathSin, sinPaint);
        canvas.restore();
    }

    private void drawSin2(Canvas canvas) {
        sinPaint.setColor(behindWaveColor);
        canvas.save();
        canvas.translate(translate2, 0);
        canvas.drawPath(pathSin, sinPaint);
        canvas.restore();
    }

    private float translate1 = 0f;
    private float translate2 = 0f;

    private void updateTranslates(int dt) {
        translate1 -= dt * 0.03f;
        translate2 -= dt * 0.04f;
        if (translate1 < -getWidth()) {
            translate1 += getWidth();
        }
        if (translate2 < -getWidth()) {
            translate2 += getWidth();
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (animator != null && animator.isRunning()) {
            animator.cancel();
        }
    }
}
