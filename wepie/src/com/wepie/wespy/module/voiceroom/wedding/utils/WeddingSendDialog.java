package com.wepie.wespy.module.voiceroom.wedding.utils;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;

import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseDialogFragment;
import com.wepie.wespy.helper.dialog.BaseFragCb;
import com.wepie.wespy.module.game.game.activity.TextJumpExtra;
import com.wepie.wespy.module.game.game.activity.TextSpanUtil;

/**
 * date 2019/1/16
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class WeddingSendDialog extends BaseDialogFragment {
    private TextView topTv;
    private TextView bottomTv;

    private BaseFragCb<Object> cb;
    private String text = "";

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        View v = inflater.inflate(R.layout.wedding_receive_dialog_view, container, false);
        initViews(v);
        updateBottomTv();
        updateCancelable(false);
        return v;
    }

    private void initViews(View v) {
        topTv = v.findViewById(R.id.top_tv);
        bottomTv = v.findViewById(R.id.bottom_tv);
        if (TextUtils.isEmpty(text)) {//服务器控制这里显示
            dismissAllowingStateLoss();
        } else {
            topTv.setText(TextSpanUtil.updateDeepLinkSpan(text, new TextJumpExtra(0xffff6512)));
        }
    }

    private void updateBottomTv() {
        bottomTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissAllowingStateLoss();
            }
        });
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        if (cb != null) {
            cb.onDismiss();
        }
    }

    public static void show(Context context, String text, BaseFragCb<Object> cb) {
        WeddingSendDialog dialog =  new WeddingSendDialog();
        dialog.text = text;
        dialog.cb = cb;
        dialog.show(context);
    }
}
