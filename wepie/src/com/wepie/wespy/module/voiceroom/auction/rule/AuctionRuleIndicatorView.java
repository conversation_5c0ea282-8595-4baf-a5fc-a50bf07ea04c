package com.wepie.wespy.module.voiceroom.auction.rule;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.View;

import com.huiwan.base.util.ScreenUtil;

/**
 * Created by bigwen on 2017/11/13.
 */

public class AuctionRuleIndicatorView extends View {

    private Context mContext;
    private Paint emptyPaint;
    private Paint choosePaint;
    private int radius;
    private int emptyRadius;
    private int interval;
    private int nums = 3;
    private int choosePosition = 0;

    private int startX = 1;
    private int startY = 2;

    public AuctionRuleIndicatorView(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public AuctionRuleIndicatorView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        emptyRadius = ScreenUtil.dip2px(mContext, 3.5f);
        radius = ScreenUtil.dip2px(mContext, 4);
        interval = ScreenUtil.dip2px(mContext, 20);

        startX = radius + ScreenUtil.dip2px(mContext, 1);
        startY = radius + ScreenUtil.dip2px(mContext, 1);

        emptyPaint = new Paint();
        emptyPaint.setColor(Color.parseColor("#44D8D8D8"));
        emptyPaint.setAntiAlias(true);

        choosePaint = new Paint();
        choosePaint.setColor(Color.parseColor("#ffffff"));
        choosePaint.setAntiAlias(true);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        setMeasuredDimension(measureWidth(widthMeasureSpec), ScreenUtil.dip2px(12));
    }

    private int measureWidth(int measureSpec) {
        int specMode = MeasureSpec.getMode(measureSpec);
        int specSize = MeasureSpec.getSize(measureSpec);
        //设置一个默认值，就是这个View的默认宽度为500，这个看我们自定义View的要求
        int result = 500;
        if (specMode == MeasureSpec.AT_MOST) {//相当于我们设置为wrap_content
            result = specSize;
        } else if (specMode == MeasureSpec.EXACTLY) {//相当于我们设置为match_parent或者为一个具体的值
            result = specSize;
        }
        return result;
    }

    private int measureHeight(int measureSpec) {
        int specMode = MeasureSpec.getMode(measureSpec);
        int specSize = MeasureSpec.getSize(measureSpec);
        int result = 500;
        if (specMode == MeasureSpec.AT_MOST) {
            result = specSize;
        } else if (specMode == MeasureSpec.EXACTLY) {
            result = specSize;
        }
        return result;
    }

    public void setNums(int num) {
        this.nums = num;
        postInvalidate();
    }

    public void refresh(int position) {
        if (position >= nums) return;
        this.choosePosition = position;
        postInvalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (nums <= 0) return;
        startX = (ScreenUtil.getScreenWidth() - ((radius + ScreenUtil.dip2px(1)) * 2 + interval * (nums - 1))) / 2;
        for (int i = 0; i < nums; i++) {
            if (choosePosition == i) {
                canvas.drawCircle(startX + interval * i, startY, radius + ScreenUtil.dip2px(1), choosePaint);
            } else {
                canvas.drawCircle(startX + interval * i, startY, emptyRadius, emptyPaint);
            }
        }
    }
}
