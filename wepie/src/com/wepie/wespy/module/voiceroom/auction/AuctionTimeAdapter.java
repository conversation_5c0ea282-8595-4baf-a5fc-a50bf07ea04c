package com.wepie.wespy.module.voiceroom.auction;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.AuGameInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2019-05-28.
 */
public class AuctionTimeAdapter extends RecyclerView.Adapter<AuctionTimeAdapter.ContentHolder> {

    private Context mContext;
    private LayoutInflater mLayoutInflater;
    private int mSelectIndex;
    private List<Integer> mContentList = new ArrayList<>();
    private AuctionListCallback auctionListCallback;

    public AuctionTimeAdapter(Context mContext) {
        this.mContext = mContext;
        mLayoutInflater = LayoutInflater.from(mContext);
    }

    public void refreshContentList(List<Integer> contentList) {
        this.mContentList.clear();
        this.mContentList.addAll(contentList);
        notifyDataSetChanged();
    }

    public void setAuctionListCallback(AuctionListCallback auctionListCallback) {
        this.auctionListCallback = auctionListCallback;
    }

    @NonNull
    @Override
    public ContentHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int position) {
        return new ContentHolder(mLayoutInflater.inflate(R.layout.auction_time_item, viewGroup, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ContentHolder contentHolder, int position) {
        contentHolder.refresh(mContentList.get(position), position, mSelectIndex, mContentList.size(), new AuctionListCallback() {
            @Override
            public void onSelect(int position, @Nullable Object object) {
                if (auctionListCallback != null) auctionListCallback.onSelect(position, object);
                mSelectIndex = position;
                notifyDataSetChanged();
            }
        });
    }

    @Override
    public int getItemCount() {
        return mContentList.size();
    }

    public int getSelectPosition() {
        return mSelectIndex;
    }

    public int getSelectContent() {
        if (mSelectIndex >= 0 && mSelectIndex < mContentList.size()) {
            return mContentList.get(mSelectIndex);
        } else {
            return 0;
        }
    }

    public static class ContentHolder extends RecyclerView.ViewHolder {

        private TextView textView;

        public ContentHolder(@NonNull View itemView) {
            super(itemView);
            textView = itemView.findViewById(R.id.item_name_tv);
        }

        public void refresh(int timeSec, final int position, int mSelectIndex, int totalSize, final AuctionListCallback auctionListCallback) {
            String timeStr = AuGameInfo.getDurationText(timeSec);
            if (TextUtils.isEmpty(timeStr)) {
                textView.setText(R.string.auction_setting_time_no_limit);
            } else {
                textView.setText(timeStr);
            }

            if (mSelectIndex == position) {
                textView.setSelected(true);
                textView.setTextColor(0xFFFFFFFF);
            } else {
                textView.setSelected(false);
                textView.setTextColor(0xFF575757);
            }
            textView.setOnClickListener(v -> {
                if (auctionListCallback != null) auctionListCallback.onSelect(position, null);
            });
        }
    }
}
