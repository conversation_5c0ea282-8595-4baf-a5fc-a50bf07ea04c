package com.wepie.wespy.module.voiceroom.auction.anim;

import android.content.Context;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.anim.SVGAUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.user.LifeUserListSimpleInfoCallback;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.opensource.svgaplayer.SVGACallback;
import com.opensource.svgaplayer.SVGADynamicEntity;
import com.opensource.svgaplayer.SVGAImageView;
import com.opensource.svgaplayer.SVGAParser;
import com.opensource.svgaplayer.SVGAVideoEntity;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.model.entity.voiceroom.AuGameInfo;
import com.wepie.wespy.module.media.sound.SoundUtil;
import com.wepie.wespy.utils.BitmapCallback;

import java.util.Arrays;
import java.util.List;

/**
 * Created by bigwen on 2019-06-04.
 */
public class AuctionResultAnimView extends FrameLayout {

    private Context mContext;
    private ImageView bgIv;
    private SVGAImageView svgaImageView;
    private SVGAImageView hammerView;
    private LinearLayout infoLay;
    private TextView nameTv;
    private TextView contentTv;
    private TextView contentTimeTv;
    private ImageView giftIv;
    private TextView giftNumTv;
    private TextView timeTv;
    private LinearLayout contentLay;
    private TextView failNameTv;
    private TextView centerSpace;

    public AuctionResultAnimView(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public AuctionResultAnimView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        setVisibility(GONE);
        LayoutInflater.from(mContext).inflate(R.layout.auction_result_anim_view, this);
        bgIv = findViewById(R.id.bg_iv);
        infoLay = findViewById(R.id.info_lay);
        svgaImageView = findViewById(R.id.anim_view);
        nameTv = findViewById(R.id.name_tv);
        contentTimeTv = findViewById(R.id.content_time_tv);
        giftIv = findViewById(R.id.gift_iv);
        giftNumTv = findViewById(R.id.gift_num_tv);
        contentTv = findViewById(R.id.content_tv);
        timeTv = findViewById(R.id.time_tv);
        contentLay = findViewById(R.id.content_lay);
        failNameTv = findViewById(R.id.fail_name_tv);
        hammerView = findViewById(R.id.hammer_view);
        centerSpace = findViewById(R.id.center_view);
    }

    public void playBiddingSuccess(final AuGameInfo auGameInfo) {
        //动画在屏幕中的位置
        int offset = ScreenUtil.dip2px(30);//居中并向上偏移
        int marginTop = ScreenUtil.getScreenHeight() / 2 - ScreenUtil.getScreenWidth() / 2 - offset;
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) svgaImageView.getLayoutParams();
        layoutParams.width = ScreenUtil.getScreenWidth();
        layoutParams.height = ScreenUtil.getScreenWidth();
        layoutParams.topMargin = marginTop;
        svgaImageView.setLayoutParams(layoutParams);

        //用于定位动画中间的头像
        int avatarOffset = -ScreenUtil.dip2px(0);//头像在动画中的偏移
        int centerHeight = (int) (252f / 1125 * ScreenUtil.getScreenWidth()) + ScreenUtil.dip2px(18);
        int centerViewMarginTop = ScreenUtil.getScreenHeight() / 2 - centerHeight / 2 - offset + avatarOffset;
        RelativeLayout.LayoutParams centerParams = (RelativeLayout.LayoutParams) centerSpace.getLayoutParams();
        centerParams.height = centerHeight;
        centerParams.topMargin = centerViewMarginTop;
        centerSpace.setLayoutParams(centerParams);

        infoLay.setVisibility(INVISIBLE);
        failNameTv.setVisibility(INVISIBLE);
        bgIv.setVisibility(INVISIBLE);

        UserService.get().getCacheSimpleUserList(Arrays.asList(auGameInfo.getMaxPriceGamer().getUid(), auGameInfo.getAuctionUid()), new LifeUserListSimpleInfoCallback(this) {

            @Override
            public void onUserInfoSuccess(List<UserSimpleInfo> userSimpleInfos) {
                UserSimpleInfo bidderInfo = null;
                UserSimpleInfo sellerInfo = null;
                for (UserSimpleInfo info : userSimpleInfos) {
                    if (info.uid == auGameInfo.getMaxPriceGamer().getUid()) {
                        bidderInfo = info;
                    }
                    if (info.uid == auGameInfo.getAuctionUid()) {
                        sellerInfo = info;
                    }
                }
                if (bidderInfo == null || sellerInfo == null) {
                    return;
                }
                String s = ResUtil.getStr(R.string.auction_bidding_win_x_x, bidderInfo.getRemarkNameLimit6(), sellerInfo.getRemarkNameLimit6());
                nameTv.setText(s);
                if (TextUtils.isEmpty(auGameInfo.getAuctionContent())) {
                    contentLay.setVisibility(GONE);
                } else {
                    contentLay.setVisibility(VISIBLE);
                    contentTv.setText(auGameInfo.getAuctionContent());
                    contentTimeTv.setText(auGameInfo.getAuctionDurationText());
                }
                Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(auGameInfo.getAuctionGiftId());
                if (gift != null) {
                    ImageLoaderUtil.loadNormalImage(gift.getMedia_url(), giftIv, ImageLoadInfo.getGiftInfo());
                }

                giftNumTv.setText(ResUtil.getStr(R.string.x_number, auGameInfo.getMaxPriceGamer().getGiftNum()));
                timeTv.setText(ResUtil.getStr(R.string.auction_result_sold_time_x, TimeUtil.longToYearAndDayAndMin(auGameInfo.getGameEndTimeMs())));

                ImageLoaderUtil.loadCircleImg(bidderInfo.getHeadimgurl(), hammerView, new BitmapCallback() {
                    @Override
                    public void onSuccess(Bitmap bitmap) {
                        SVGADynamicEntity svgaDynamicEntity = new SVGADynamicEntity();
                        svgaDynamicEntity.setDynamicImage(bitmap, "tx");
                        SVGAUtil.playSvga("svga/auction/hammer.svga", hammerView, svgaDynamicEntity, new SVGAParser.ParseCompletion() {
                            @Override
                            public void onComplete(@NonNull SVGAVideoEntity svgaVideoEntity) {
                                setVisibility(VISIBLE);
                                SoundUtil.getInstance().playSoundCheckMuteRoom(SoundUtil.TYPE_AUCTION_HAMMER, auGameInfo.getRid());
                            }

                            @Override
                            public void onError() {

                            }
                        });
                    }

                    @Override
                    public void onFail(String msg) {

                    }
                });
                String bidderUrl = bidderInfo.getHeadimgurl();
                String sellerUrl = sellerInfo.getHeadimgurl();
                hammerView.setCallback(new SVGACallback() {
                    @Override
                    public void onPause() {

                    }

                    @Override
                    public void onFinished() {
                        playBiddingUser(bidderUrl, sellerUrl, auGameInfo.getRid());
                    }

                    @Override
                    public void onRepeat() {

                    }

                    @Override
                    public void onStep(int i, double v) {

                    }
                });
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });
    }

    private void playBiddingUser(String bidder, final String seller, int rid) {
        ImageLoaderUtil.loadCircleImg(bidder, svgaImageView, new BitmapCallback() {
            @Override
            public void onSuccess(final Bitmap bidderBitmap) {
                ImageLoaderUtil.loadCircleImg(seller, svgaImageView, new BitmapCallback() {
                    @Override
                    public void onSuccess(Bitmap sellerBitmap) {
                        SVGADynamicEntity svgaDynamicEntity = new SVGADynamicEntity();
                        svgaDynamicEntity.setDynamicImage(bidderBitmap, "img_674");
                        svgaDynamicEntity.setDynamicImage(sellerBitmap, "img_675");
                        svgaImageView.setCallback(new SVGACallback() {
                            @Override
                            public void onPause() {

                            }

                            @Override
                            public void onFinished() {
                                setVisibility(GONE);
                            }

                            @Override
                            public void onRepeat() {

                            }

                            @Override
                            public void onStep(int i, double v) {

                            }
                        });

                        SVGAUtil.playSvga("svga/lang/bidding_success.svga", svgaImageView, svgaDynamicEntity, new SVGAParser.ParseCompletion() {
                            @Override
                            public void onComplete(SVGAVideoEntity svgaVideoEntity) {
                                infoLay.setVisibility(VISIBLE);
                                bgIv.setVisibility(VISIBLE);
                                SoundUtil.getInstance().playSoundCheckMuteRoom(SoundUtil.TYPE_AUCTION_SUCCESS, rid);
                            }

                            @Override
                            public void onError() {

                            }
                        });
                    }

                    @Override
                    public void onFail(String msg) {

                    }
                });
            }

            @Override
            public void onFail(String msg) {

            }
        });


    }

    public void playBiddingFail(int seller, int rid) {
        //动画在屏幕中的位置
        int offset = ScreenUtil.dip2px(30);//居中并向上偏移
        int marginTop = ScreenUtil.getScreenHeight() / 2 - ScreenUtil.getScreenWidth() / 2 - offset;
        RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) svgaImageView.getLayoutParams();
        layoutParams.width = ScreenUtil.getScreenWidth();
        layoutParams.height = ScreenUtil.getScreenWidth();
        layoutParams.topMargin = marginTop;
        svgaImageView.setLayoutParams(layoutParams);

        //用于定位动画中间的头像
        int avatarOffset = -ScreenUtil.dip2px(16);//头像在动画中的偏移
        int centerHeight = (int) (252f / 1125 * ScreenUtil.getScreenWidth()) + ScreenUtil.dip2px(18);
        int centerViewMarginTop = ScreenUtil.getScreenHeight() / 2 - centerHeight / 2 - offset + avatarOffset;
        RelativeLayout.LayoutParams centerParams = (RelativeLayout.LayoutParams) centerSpace.getLayoutParams();
        centerParams.height = centerHeight;
        centerParams.topMargin = centerViewMarginTop;
        centerSpace.setLayoutParams(centerParams);

        infoLay.setVisibility(INVISIBLE);
        failNameTv.setVisibility(INVISIBLE);
        bgIv.setVisibility(VISIBLE);
        svgaImageView.setCallback(new SVGACallback() {
            @Override
            public void onPause() {

            }

            @Override
            public void onFinished() {
                setVisibility(GONE);
            }

            @Override
            public void onRepeat() {

            }

            @Override
            public void onStep(int i, double v) {

            }
        });

        UserService.get().getCacheSimpleUser(seller, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                failNameTv.setText(simpleInfo.getRemarkName());
                ImageLoaderUtil.loadCircleImg(simpleInfo.getHeadimgurl(), failNameTv, new BitmapCallback() {
                    @Override
                    public void onSuccess(Bitmap bitmap) {
                        SVGADynamicEntity svgaDynamicEntity = new SVGADynamicEntity();
                        svgaDynamicEntity.setDynamicImage(bitmap, "img_724");
                        SVGAUtil.playSvga("svga/lang/bidding_fail.svga", svgaImageView, svgaDynamicEntity, new SVGAParser.ParseCompletion() {
                            @Override
                            public void onComplete(SVGAVideoEntity svgaVideoEntity) {
                                setVisibility(VISIBLE);
                                failNameTv.setVisibility(VISIBLE);
                                SoundUtil.getInstance().playSoundCheckMuteRoom(SoundUtil.TYPE_AUCTION_FAIL, rid);
                            }

                            @Override
                            public void onError() {

                            }
                        });
                    }

                    @Override
                    public void onFail(String msg) {

                    }
                });
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });

    }
}