package com.wepie.wespy.module.voiceroom.auction;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.configservice.model.gift.Gift;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2019-05-28.
 */
public class AuctionGiftAdapter extends RecyclerView.Adapter<AuctionGiftAdapter.GiftHolder> {

    private final Context mContext;
    private final LayoutInflater mLayoutInflater;
    private int mSelectIndex;
    private final List<Gift> mContentList = new ArrayList<>();
    private AuctionListCallback auctionListCallback;

    public AuctionGiftAdapter(Context mContext) {
        this.mContext = mContext;
        mLayoutInflater = LayoutInflater.from(mContext);
    }

    public void refreshContentList(List<Gift> contentList) {
        this.mContentList.clear();
        this.mContentList.addAll(contentList);
        notifyDataSetChanged();
    }

    public void setAuctionListCallback(AuctionListCallback auctionListCallback) {
        this.auctionListCallback = auctionListCallback;
    }

    @NonNull
    @Override
    public AuctionGiftAdapter.GiftHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int position) {
        return new AuctionGiftAdapter.GiftHolder(mLayoutInflater.inflate(R.layout.auction_gift_item, viewGroup, false));
    }

    @Override
    public void onBindViewHolder(@NonNull AuctionGiftAdapter.GiftHolder contentHolder, int position) {
        contentHolder.refresh(mContentList.get(position), position, mSelectIndex, mContentList.size(), new AuctionListCallback() {
            @Override
            public void onSelect(int position, @Nullable Object object) {
                if (auctionListCallback != null) auctionListCallback.onSelect(position, object);
                mSelectIndex = position;
                notifyDataSetChanged();
            }
        });
    }

    @Override
    public int getItemCount() {
        return mContentList.size();
    }

    public int getSelectPosition() {
        return mSelectIndex;
    }

    public int getSelectGiftId() {
        if (mSelectIndex >= 0 && mSelectIndex < mContentList.size()) {
            return mContentList.get(mSelectIndex).getGift_id();
        } else {
            return 0;
        }
    }

    public static class GiftHolder extends RecyclerView.ViewHolder {

        private final TextView nameTv;
        private final ImageView iconIv;
        private final TextView coinTv;

        public GiftHolder(@NonNull View itemView) {
            super(itemView);
            iconIv = itemView.findViewById(R.id.gift_icon);
            coinTv = itemView.findViewById(R.id.gift_price_tv);
            nameTv = itemView.findViewById(R.id.gift_name_tv);
        }

        public void refresh(Gift gift, final int position, int mSelectIndex, int totalSize, final AuctionListCallback auctionListCallback) {
            nameTv.setText(gift.getName());
            ImageLoaderUtil.loadNormalImage(gift.getMedia_url(), iconIv, ImageLoadInfo.getGiftInfo());
            String price = ResUtil.getStr(R.string.gold, Integer.toString(gift.getPrice()));
            coinTv.setText(price);

            itemView.setSelected(mSelectIndex == position);
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (auctionListCallback != null) auctionListCallback.onSelect(position, null);
                }
            });
        }
    }
}
