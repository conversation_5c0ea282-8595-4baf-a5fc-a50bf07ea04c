package com.wepie.wespy.module.voiceroom.auction.anim;

import android.content.Context;
import android.graphics.Bitmap;
import android.util.AttributeSet;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;

import com.huiwan.anim.SVGAUtil;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.opensource.svgaplayer.SVGACallback;
import com.opensource.svgaplayer.SVGADynamicEntity;
import com.opensource.svgaplayer.SVGAImageView;
import com.opensource.svgaplayer.SVGAParser;
import com.opensource.svgaplayer.SVGAVideoEntity;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.utils.BitmapCallback;

import java.util.ArrayDeque;

/**
 * Created by <PERSON>wen on 2019-06-03.
 */
public class AuctionBiddingAnimView extends FrameLayout {

    private Context mContext;
    private ArrayDeque<SVGAImageView> viewDeque = new ArrayDeque<>();

    public AuctionBiddingAnimView(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public AuctionBiddingAnimView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
    }

    public void showBiddingAnim(int uid, final int addNum) {
        SVGAImageView imageView = null;
        if (viewDeque.size() != 0) {
            imageView = viewDeque.poll();
        } else {
            imageView = new SVGAImageView(mContext);
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            addView(imageView, layoutParams);
        }

        String res = "svga/auction/bidding1.svga";
        if (addNum == 1) {
            res = "svga/auction/bidding1.svga";
        } else if (addNum == 5) {
            res = "svga/auction/bidding2.svga";
        } else if (addNum == 10) {
            res = "svga/auction/bidding3.svga";
        }
        final String finalRes = res;
        final SVGAImageView finalImageView = imageView;
        getAvatar(uid, imageView, new BitmapCallback() {
            @Override
            public void onSuccess(Bitmap bitmap) {
                finalImageView.setCallback(new SVGACallback() {
                    @Override
                    public void onPause() {

                    }

                    @Override
                    public void onFinished() {
                        viewDeque.add(finalImageView);
                    }

                    @Override
                    public void onRepeat() {

                    }

                    @Override
                    public void onStep(int i, double v) {

                    }
                });
                SVGADynamicEntity svgaDynamicEntity = new SVGADynamicEntity();
                svgaDynamicEntity.setDynamicImage(bitmap, "tx");
                SVGAUtil.playSvga(finalRes, finalImageView, svgaDynamicEntity, new SVGAParser.ParseCompletion() {
                    @Override
                    public void onComplete(@NonNull SVGAVideoEntity svgaVideoEntity) {
                        setVisibility(VISIBLE);

                    }

                    @Override
                    public void onError() {

                    }
                });
            }

            @Override
            public void onFail(String msg) {

            }
        });
    }

    private void getAvatar(int uid, SVGAImageView imageView, final BitmapCallback callback) {
        UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(imageView) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                ImageLoaderUtil.loadCircleImg(simpleInfo.getHeadimgurl(), imageView, callback);
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });
    }
}