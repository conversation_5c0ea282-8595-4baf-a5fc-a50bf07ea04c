package com.wepie.wespy.module.voiceroom.auction;

import android.content.Context;
import android.graphics.Rect;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.PressUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.widget.decoration.SpaceItemDecoration;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.net.tcp.sender.AuctionPacketSender;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Created by bigwen on 2019-05-28.
 */
public class AuctionSettingView extends FrameLayout {

    private Context mContext;
    private AuctionContentAdapter contentAdapter;
    private AuctionTimeAdapter timeAdapter;
    private AuctionGiftAdapter giftAdapter;
    private TextView enterTv;
    private int mRid;
    private SettingCallback mSettingCallback;
    private ImageView closeIv;

    public AuctionSettingView(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public AuctionSettingView(Context context, int rid) {
        super(context);
        mContext = context;
        mRid = rid;
        init();
    }

    public AuctionSettingView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.auction_setting_view, this);
        RecyclerView contentListView = findViewById(R.id.content_list_view);
        RecyclerView timeListView = findViewById(R.id.time_list_view);
        RecyclerView giftListView = findViewById(R.id.gift_list_view);
        enterTv = findViewById(R.id.enter_iv);
        closeIv = findViewById(R.id.close_icon);

        int dp8 = ScreenUtil.dip2px(8F);
        int dp24 = ScreenUtil.dip2px(24F);
        SpaceItemDecoration decoration = new SpaceItemDecoration(new Rect(dp8, 0, dp8, 0), new Rect(dp24, 0, dp24, 0));
        contentListView.addItemDecoration(decoration);
        contentListView.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false));
        contentAdapter = new AuctionContentAdapter(mContext);
        contentListView.setAdapter(contentAdapter);

        timeListView.addItemDecoration(decoration);
        timeListView.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false));
        timeAdapter = new AuctionTimeAdapter(mContext);
        timeListView.setAdapter(timeAdapter);

        giftListView.addItemDecoration(decoration);
        giftListView.setLayoutManager(new LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false));
        giftAdapter = new AuctionGiftAdapter(mContext);
        giftListView.setAdapter(giftAdapter);

        List<String> stringList = new ArrayList<>();
        stringList.add(ResUtil.getStr(R.string.custom_defined));
        stringList.addAll(ConfigHelper.getInstance().getVoiceRoomConfig().getAuctionRoomConfig().getContent());
        contentAdapter.refreshContentList(stringList);
        timeAdapter.refreshContentList(ConfigHelper.getInstance().getVoiceRoomConfig().getAuctionRoomConfig().getTimeSec());
        List<Gift> auctionGift = ConfigHelper.getInstance().getGiftConfig().getAuctionGift();
        Collections.sort(auctionGift, new Comparator<Gift>() {
            @Override
            public int compare(Gift o1, Gift o2) {
                return o1.getPrice() - o2.getPrice();
            }
        });
        giftAdapter.refreshContentList(auctionGift);

        enterTv.setOnClickListener(new View.OnClickListener(){
            @Override
            public void onClick(View v) {
                String text = contentAdapter.getSelectContent();
                if (TextUtils.isEmpty(text)) {
                    ToastUtil.show(R.string.auction_item_cannot_empty);
                    return;
                }
                AuctionPacketSender.setContentReq(mRid, text, timeAdapter.getSelectContent(), giftAdapter.getSelectGiftId(), new LifeSeqCallback(AuctionSettingView.this) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        ToastUtil.show(R.string.set_success);
                        if (mSettingCallback != null) mSettingCallback.onClose();
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                });
            }
        });

        closeIv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mSettingCallback != null) mSettingCallback.onClose();
            }
        });

        PressUtil.addPressEffect(enterTv);
    }

    public void setSettingCallback(SettingCallback mSettingCallback) {
        this.mSettingCallback = mSettingCallback;
    }

    public interface SettingCallback {
        void onClose();
    }

    public static void showDialog(Context mContext, int rid) {
        AuctionSettingView view = new AuctionSettingView(mContext, rid);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(mContext, R.style.dialog_style_custom);
        view.setSettingCallback(new SettingCallback() {
            @Override
            public void onClose() {
                dialog.dismiss();
            }
        });
        dialog.setContentView(view);
        dialog.setCanceledOnTouchOutside(true);
        dialog.initBottomDialog();
        dialog.show();
    }
}