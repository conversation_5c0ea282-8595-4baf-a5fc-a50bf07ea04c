package com.wepie.wespy.module.voiceroom.auction;

import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.util.IMMHelper;
import com.wepie.wespy.R;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by bigwen on 2019-05-28.
 */
public class AuctionContentAdapter extends RecyclerView.Adapter<AuctionContentAdapter.AbsContentHolder> {

    private Context mContext;
    private final LayoutInflater mLayoutInflater;
    private int mSelectIndex = 1;
    private final List<String> mContentList = new ArrayList<>();
    private AuctionListCallback auctionListCallback;
    private static String customString = "";
    private static boolean isNeedEdit = false;

    public AuctionContentAdapter(Context mContext) {
        this.mContext = mContext;
        mLayoutInflater = LayoutInflater.from(mContext);
        customString = "";
    }

    public void refreshContentList(List<String> contentList) {
        this.mContentList.clear();
        if (contentList != null && !contentList.isEmpty()) {
            this.mContentList.addAll(contentList);
        }
        notifyItemRangeChanged(0, this.mContentList.size());
    }

    public void setAuctionListCallback(AuctionListCallback auctionListCallback) {
        this.auctionListCallback = auctionListCallback;
    }

    @NonNull
    @Override
    public AbsContentHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == 1) {
            return new EditContentHolder(mLayoutInflater.inflate(R.layout.auction_edit_content_item, parent, false));
        }
        return new ContentHolder(mLayoutInflater.inflate(R.layout.auction_content_item, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull AbsContentHolder contentHolder, int position) {
        contentHolder.refresh(mContentList.get(position), position, mSelectIndex, mContentList.size(), new AuctionListCallback() {
            @Override
            public void onSelect(int position, @Nullable Object object) {
                if (auctionListCallback != null) auctionListCallback.onSelect(position, object);
                int lastIndex = mSelectIndex;
                mSelectIndex = position;
                if (lastIndex >= 0) {
                    notifyItemChanged(lastIndex);
                }
                notifyItemChanged(mSelectIndex);
            }
        });
    }

    @Override
    public int getItemCount() {
        return mContentList.size();
    }

    @Override
    public int getItemViewType(int position) {
        if (position == 0) {
            return 1;
        }
        return 2;
    }

    public int getSelectPosition() {
        return mSelectIndex;
    }

    public String getSelectContent() {
        if (mSelectIndex >= 0 && mSelectIndex < mContentList.size()) {
            if (mSelectIndex == 0) {
                return customString;
            }
            return mContentList.get(mSelectIndex);
        } else {
            return "";
        }
    }

    public static abstract class AbsContentHolder extends RecyclerView.ViewHolder {

        protected TextView textView;

        public AbsContentHolder(@NonNull View itemView) {
            super(itemView);
            textView = itemView.findViewById(R.id.item_name_tv);
        }

        public void refresh(String s, int position, int selectIndex, int size, AuctionListCallback auctionListCallback) {
            textView.setText(s);
            textView.setVisibility(View.VISIBLE);
            if (selectIndex == position) {
                textView.setSelected(true);
                textView.setTextColor(0xFFFFFFFF);
            } else {
                textView.setSelected(false);
                textView.setTextColor(0xFF575757);
            }
        }
    }

    public static class ContentHolder extends AbsContentHolder {

        public ContentHolder(@NonNull View itemView) {
            super(itemView);
        }

        public void refresh(String s, final int position, int selectIndex, int totalSize, final AuctionListCallback auctionListCallback) {
            super.refresh(s, position, selectIndex, totalSize, auctionListCallback);
            textView.setOnClickListener(v -> {
                if (auctionListCallback != null) auctionListCallback.onSelect(position, null);
            });

        }
    }

    public static class EditContentHolder extends AbsContentHolder {

        private final EditText editText;
        private final TextWatcher textWatcher;

        public EditContentHolder(@NonNull View itemView) {
            super(itemView);
            editText = itemView.findViewById(R.id.item_edit_name_tv);
            editText.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    if (hasFocus) {
                        editText.setText(customString);
                        try {
                            editText.setSelection(customString.length());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        editText.setSelected(true);
                        editText.addTextChangedListener(textWatcher);
                        IMMHelper.showSoftInputDelay(editText, 200);
                    } else {
                        editText.setSelected(false);
                        editText.removeTextChangedListener(textWatcher);
                    }
                }
            });
            editText.setOnEditorActionListener((v, actionId, event) -> {
                if (actionId == EditorInfo.IME_ACTION_DONE || actionId == EditorInfo.IME_ACTION_NEXT ||
                        (event != null && KeyEvent.KEYCODE_ENTER == event.getKeyCode())) {
                    isNeedEdit = false;
                    showText();
                }
                return false;
            });
            textWatcher = new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {
                    customString = s.toString();
                }
            };
        }

        @Override
        public void refresh(String s, int position, int selectIndex, int size, AuctionListCallback auctionListCallback) {
            super.refresh(s, position, selectIndex, size, auctionListCallback);
            if (position == selectIndex && isNeedEdit) {
                editText.setVisibility(View.VISIBLE);
                textView.setVisibility(View.GONE);
                editText.requestFocus();
            } else {
                showText();
            }

            textView.setOnClickListener(v -> {
                isNeedEdit = true;
                if (auctionListCallback != null) auctionListCallback.onSelect(position, null);
            });
        }

        private void showText() {
            textView.setVisibility(View.VISIBLE);
            editText.setVisibility(View.INVISIBLE);
            if (TextUtils.isEmpty(customString)) {
                textView.setText(R.string.custom_defined);
            } else {
                textView.setText(customString);
            }
        }
    }
}
