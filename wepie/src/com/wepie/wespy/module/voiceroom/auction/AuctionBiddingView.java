package com.wepie.wespy.module.voiceroom.auction;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.wespy.R;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.voiceroom.AuctionRoomConfig;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.huiwan.configservice.model.gift.Gift;
import com.wepie.wespy.model.entity.voiceroom.AuGameInfo;
import com.huiwan.base.util.PressUtil;

import java.util.List;

/**
 * Created by <PERSON>wen on 2019-05-30.
 */
public class AuctionBiddingView extends FrameLayout {

    private LinearLayout biddingLay1, biddingLay2, biddingLay3;
    private TextView biddingNumTv1, biddingNumTv2, biddingNumTv3;
    private TextView biddingCoinTv1, biddingCoinTv2, biddingCoinTv3;
    private ImageView giftIv;
    private TextView giftCoinTv;
    private TextView giftNumTv;
    private ImageView closeIv;
    private AuctionBiddingCallback callback;
    private Context mContext;

    public AuctionBiddingView(Context context) {
        super(context);
        mContext = context;
        initView();
    }

    public AuctionBiddingView(Context context, AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initView();
    }

    private void initView() {
        LayoutInflater.from(mContext).inflate(R.layout.auction_bidding_view, this);
        biddingLay1 = findViewById(R.id.bidding_lay1);
        biddingLay2 = findViewById(R.id.bidding_lay2);
        biddingLay3 = findViewById(R.id.bidding_lay3);
        biddingNumTv1 = findViewById(R.id.bidding_num_tv1);
        biddingNumTv2 = findViewById(R.id.bidding_num_tv2);
        biddingNumTv3 = findViewById(R.id.bidding_num_tv3);
        biddingCoinTv1 = findViewById(R.id.bidding_coin_tv1);
        biddingCoinTv2 = findViewById(R.id.bidding_coin_tv2);
        biddingCoinTv3 = findViewById(R.id.bidding_coin_tv3);
        giftIv = findViewById(R.id.gift_iv);
        giftCoinTv = findViewById(R.id.gift_price_tv);
        giftNumTv = findViewById(R.id.gift_num_tv);
        closeIv = findViewById(R.id.close_icon);

        closeIv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (callback != null) callback.onClose();
            }
        });
        PressUtil.addPressEffect(biddingNumTv1);
        PressUtil.addPressEffect(biddingNumTv2);
        PressUtil.addPressEffect(biddingNumTv3);
    }

    public void refreshAuGameInfo(AuGameInfo auGameInfo) {
        Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(auGameInfo.getAuctionGiftId());
        if (gift != null) {
            ImageLoaderUtil.loadNormalImage(gift.getMedia_url(), giftIv, ImageLoadInfo.getGiftInfo());
            giftCoinTv.setText(ResUtil.getStr(R.string.common_x_golds, gift.getPrice()));
            giftNumTv.setText(ResUtil.getStr(R.string.x_number, auGameInfo.getMaxPriceGamer().getGiftNum()));
            refreshBiddingData(auGameInfo, gift);
        }
    }

    private void refreshBiddingData(final AuGameInfo auGameInfo, Gift gift) {
        AuctionRoomConfig auctionRoomConfig = ConfigHelper.getInstance().getVoiceRoomConfig().getAuctionRoomConfig();
        int maxCoin = auGameInfo.getMaxPriceGamer().getCoin();
        List<Integer> biddingList = auctionRoomConfig.getBidding();
        if (biddingList.size() == 0) {
            biddingList.add(1);
            biddingList.add(5);
            biddingList.add(10);
        }
        if (biddingList.size() > 0) {
            final int biddingNum = biddingList.get(0);
            biddingLay1.setVisibility(VISIBLE);
            biddingNumTv1.setText("+" + biddingNum);
            biddingCoinTv1.setText(ResUtil.getStr(R.string.common_x_golds, maxCoin + gift.getPrice() * biddingNum));
            biddingLay1.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (callback != null) callback.bidding(auGameInfo, biddingNum);
                }
            });
        } else {
            biddingLay1.setVisibility(GONE);
        }
        if (biddingList.size() > 1) {
            final int biddingNum = biddingList.get(1);
            biddingLay2.setVisibility(VISIBLE);
            biddingNumTv2.setText("+" + biddingNum);
            biddingCoinTv2.setText(ResUtil.getStr(R.string.common_x_golds, maxCoin + gift.getPrice() * biddingNum));
            biddingLay2.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (callback != null) callback.bidding(auGameInfo, biddingNum);
                }
            });
        } else {
            biddingLay2.setVisibility(GONE);
        }
        if (biddingList.size() > 2) {
            final int biddingNum = biddingList.get(2);
            biddingLay3.setVisibility(VISIBLE);
            biddingNumTv3.setText("+" + biddingNum);
            biddingCoinTv3.setText(ResUtil.getStr(R.string.common_x_golds, maxCoin + gift.getPrice() * biddingNum));
            biddingLay3.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (callback != null) callback.bidding(auGameInfo, biddingNum);
                }
            });
        } else {
            biddingLay3.setVisibility(GONE);
        }
    }

    public void setCallback(AuctionBiddingCallback callback) {
        this.callback = callback;
    }
}