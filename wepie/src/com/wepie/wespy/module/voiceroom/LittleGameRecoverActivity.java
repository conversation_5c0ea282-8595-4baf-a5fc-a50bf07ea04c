package com.wepie.wespy.module.voiceroom;

import android.os.Bundle;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.lib.api.ApiService;
import com.wejoy.littlegame.ILittleGameApi;
import com.wepie.wespy.R;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.huiwan.base.util.ToastUtil;
import com.wepie.wespy.module.main.manage.MainDialogManage;

public class LittleGameRecoverActivity extends BaseActivity {

    private TextView sureBt;
    private TextView contentTv;
    private TextView cancelBt;
    private final ProgressDialogUtil progressUtil = new ProgressDialogUtil();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.litter_game_recover_view);
        sureBt = findViewById(R.id.id_tip_double_bt_sure_bt);
        contentTv = findViewById(R.id.id_tip_double_bt_content_tx);
        cancelBt = findViewById(R.id.id_tip_double_bt_cancel_bt);

        final VoiceRoomInfo voiceRoomInfo = (VoiceRoomInfo) getIntent().getSerializableExtra("voice_room_info");
        if (voiceRoomInfo != null) {
            contentTv.setText(ResUtil.getStr(R.string.litter_game_recover_req_game, ConfigHelper.getInstance().getGameName(voiceRoomInfo.game_type)));
        } else {
            contentTv.setText(R.string.litter_game_recover_req);
        }
        sureBt.setOnClickListener(view -> {
            if (voiceRoomInfo == null) {
                ToastUtil.show(R.string.jumping_failed_due_to_data_error);
                return;
            }
            progressUtil.showLoading(LittleGameRecoverActivity.this, null, true);
            JumpUtil.gotoLittleGameAfterRecover(LittleGameRecoverActivity.this, voiceRoomInfo);
            progressUtil.hideLoading();
            MainDialogManage.getInstance().getCallback().clearAll();
            finish();
        });

        cancelBt.setOnClickListener(v -> {
            MainDialogManage.getInstance().getCallback().showNext();
            finish();
        });
        ApiService.of(ILittleGameApi.class).preload(this);
    }

    @Override
    public int supportFloatView() {
        return 0;
    }
}
