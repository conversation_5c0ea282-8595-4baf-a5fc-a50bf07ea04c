package com.wepie.wespy.module.voiceroom.smallframe;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.ViewPager;
import androidx.viewpager2.widget.ViewPager2;

import com.huiwan.widget.IndicatorView;
import com.huiwan.widget.SimplePagerAdapter;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.CubePagerTransformer;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.ISmallFrameUtil;

import java.lang.ref.WeakReference;
import java.util.ArrayList;

/**
 * Created by bigwen on 2019/4/23.
 */
public class VoiceSmallFrameView extends FrameLayout implements ISmallFrameUtil {

    private static final int MSG_AUTO_SCROLL = 1;
    private ViewPager2 smallFramePager;
    private IndicatorView smallIndicatorView;
    private SimplePagerAdapter<View> adapter;
    private Handler handler;

    public VoiceSmallFrameView(Context context) {
        this(context, null);
    }

    public VoiceSmallFrameView(Context context, AttributeSet attrs) {
        super(context, attrs);
        try {
            doInitView();
        } catch (Exception e) {
            HLog.e("VoiceSmallFrameView", HLog.CLR, "doInit failed，{}", Log.getStackTraceString(e));
        }
    }

    private void doInitView() {
        handler = new UtilsHandler(this);
        LayoutInflater.from(getContext()).inflate(R.layout.voice_small_frame_view, this);
        smallFramePager = findViewById(R.id.room_small_f_pager);
        smallIndicatorView = findViewById(R.id.indicator_view);
        smallIndicatorView.setRadius(2);
        smallIndicatorView.setDist(4);
        adapter = new SimplePagerAdapter<>(new ArrayList<>());
        smallFramePager.setAdapter(adapter);
        smallFramePager.setPageTransformer(new CubePagerTransformer());
        smallFramePager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                smallIndicatorView.refresh(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                if (state == ViewPager.SCROLL_STATE_DRAGGING) {
                    handler.removeMessages(MSG_AUTO_SCROLL);
                } else {
                    checkAutoScrollSmallFrame();
                }
            }
        });
    }

    private void showSmallView(View view) {
        if (!adapter.contains(view)) {
            adapter.add(view);
            smallFramePager.setAdapter(adapter);
            smallFramePager.setCurrentItem(adapter.getItemCount() - 1);
        }
        if (adapter.getItemCount() != 0) {
            smallFramePager.setVisibility(VISIBLE);
            smallIndicatorView.setTotal(adapter.getItemCount());
        }
        if (adapter.getItemCount() > 1) {
            smallIndicatorView.setVisibility(VISIBLE);
            smallIndicatorView.refresh(smallFramePager.getCurrentItem());
        }
        checkAutoScrollSmallFrame();
    }

    private void hideSmallView(View view) {
        if (adapter.contains(view)) {
            adapter.remove(view);
            smallFramePager.setAdapter(adapter);
            smallIndicatorView.setTotal(adapter.getItemCount());
        }
        if (adapter.getItemCount() == 0) {
            smallFramePager.setVisibility(GONE);
        }
        if (adapter.getItemCount() <= 1) {
            smallIndicatorView.setVisibility(INVISIBLE);
        } else {
            smallIndicatorView.refresh(smallFramePager.getCurrentItem());
        }
        checkAutoScrollSmallFrame();
    }

    private void checkAutoScrollSmallFrame() {
        if (adapter.getItemCount() > 1) {
            if (!handler.hasMessages(MSG_AUTO_SCROLL)) {
                handler.sendEmptyMessageDelayed(MSG_AUTO_SCROLL, 3000);
            }
        } else {
            handler.removeMessages(MSG_AUTO_SCROLL);
        }
    }


    private void doSmallAutoScroll() {
        int index = smallFramePager.getCurrentItem();
        index++;
        if (index >= adapter.getItemCount()) {
            index = 0;
        }
        smallFramePager.setCurrentItem(index);
    }

    @Override
    protected void onDetachedFromWindow() {
        handler.removeCallbacksAndMessages(null);
        super.onDetachedFromWindow();
    }

    @Override
    public void add(@NonNull View view) {
        showSmallView(view);
    }

    @Override
    public void remove(@NonNull View view) {
        hideSmallView(view);
    }

    @Override
    public boolean has() {
        return adapter.getItemCount() > 0;
    }

    private static class UtilsHandler extends Handler {

        WeakReference<VoiceSmallFrameView> ref;

        UtilsHandler(VoiceSmallFrameView ref) {
            super(Looper.getMainLooper());
            this.ref = new WeakReference<>(ref);
        }

        @Override
        public void handleMessage(Message msg) {
            VoiceSmallFrameView utilsView = ref.get();
            if (utilsView != null) {
                handleMsg(utilsView, msg);
            }
        }

        private void handleMsg(VoiceSmallFrameView utilsView, Message msg) {
            if (msg.what == MSG_AUTO_SCROLL) {
                utilsView.doSmallAutoScroll();
            }
        }
    }
}
