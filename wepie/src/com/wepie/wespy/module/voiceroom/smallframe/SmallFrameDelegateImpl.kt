package com.wepie.wespy.module.voiceroom.smallframe

import android.view.View
import com.wepie.wespy.module.voiceroom.IPluginTool
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.ISmallFrameUtil

class SmallFrameDelegateImpl(
    private val pluginTool: IPluginTool<ISmallFrameUtil>
) : ISmallFrameUtil by pluginTool.proxy() {

    override fun add(view: View) {
        pluginTool.loadView(true)
        pluginTool.proxy().add(view)
    }

    override fun remove(view: View) {
        pluginTool.proxy().remove(view)
        if (!has()) {
            pluginTool.loadView(false)
        }
    }
}