package com.wepie.wespy.module.voiceroom

import android.content.Context
import android.util.Log
import android.view.View
import androidx.core.view.isVisible
import com.wejoy.weplay.ex.ILife
import com.wejoy.weplay.ex.ILifeRegistry
import com.wepie.liblog.main.HLog
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IBasePlugin
import com.wepie.wespy.module.voiceroom.main.rootview.PluginLayoutManager
import java.lang.reflect.Proxy

/**
 * 该接口会被代理类持有，用来调用实际的View
 */
abstract class IPluginTool<E> : ILifeRegistry() {
    /**
     * @param load
     * 此方法被调用，会将实际的View加载到界面上或从界面移除
     * 调用多次，不会加载多个
     * 请在主线程调用，线程不安全
     */
    abstract fun loadView(load: Boolean)

    abstract fun <T : View> getRealView(): T?

    abstract fun context(): Context

    /**
     * 这个proxy实际上是代理类和实际View沟通的桥梁，是一个动态代理对象
     * 添加这个的原因是动态代理代理类，减少重复性代码
     */
    abstract fun proxy(): E
}

/**
 * 插件初始化
 */
class ViewPluginsProxy(private val layoutPlugin: PluginLayoutManager) {
    private inner class ViewPluginWrapper(
        val viewPlugin: ViewPlugin<*, out IBasePlugin>
    ) : IPluginTool<IBasePlugin>() {
        private var proxy: IBasePlugin? = null
        var instance: IBasePlugin? = null

        override fun loadView(load: Boolean) {
            if (load) {
                when {
                    !layoutPlugin.has(viewPlugin) -> {
                        layoutPlugin.add(viewPlugin)
                    }

                    viewPlugin.persistence -> {
                        layoutPlugin.get(viewPlugin)?.isVisible = true
                    }
                }
            } else {
                if (!layoutPlugin.has(viewPlugin)) {
                    return
                }
                if (viewPlugin.persistence) {
                    layoutPlugin.get(viewPlugin)?.isVisible = false
                } else {
                    layoutPlugin.remove(viewPlugin)
                }
            }
        }

        @Suppress("UNCHECKED_CAST")
        override fun <T : View> getRealView(): T? {
            return layoutPlugin.get(viewPlugin) as? T
        }

        override fun context(): Context {
            return layoutPlugin.context
        }

        override fun proxy(): IBasePlugin {
            if (proxy == null) {
                proxy = getProxy()
            }
            return proxy!!
        }

        private fun getProxy(): IBasePlugin {
            return Proxy.newProxyInstance(
                viewPlugin.cls.classLoader,
                arrayOf(viewPlugin.superCls)
            ) { _, method, args: Array<Any?>? ->
                val view = layoutPlugin.get(viewPlugin)
                if (view == null) {
                    val returnValue = when (method.returnType.name) {
                        "boolean" -> {
                            false
                        }

                        "byte", "short", "int", "long", "float", "double" -> {
                            0
                        }

                        else -> {
                            null
                        }
                    }
                    return@newProxyInstance returnValue
                }
                return@newProxyInstance method.invoke(view, *args.orEmpty())
            } as IBasePlugin
        }
    }

    private val pluginWrapperList = mutableListOf<ViewPluginWrapper>()

    fun init(life: ILife) {
        HLog.d("ViewPluginsProxy", HLog.USR, "init")
        layoutPlugin.viewPlugins.keys.forEach { viewPlugin ->
            val viewPluginWrapper = ViewPluginWrapper(viewPlugin)
            val instance = viewPlugin.onInit(viewPluginWrapper)
            viewPluginWrapper.instance = instance
            if (viewPlugin.superCls != IBasePlugin::class.java) {
                VoicePluginService.registerPlugin(instance)
            }
            pluginWrapperList.add(viewPluginWrapper)
        }
        life.onDestroy {
            destroy()
        }
    }

    private fun destroy() {
        HLog.d("ViewPluginsProxy", HLog.USR, Log.getStackTraceString(Throwable("destroy")))
        pluginWrapperList.forEach { viewPluginWrapper ->
            val viewPlugin = viewPluginWrapper.viewPlugin
            val instance = viewPluginWrapper.instance
            if (viewPlugin.superCls != IBasePlugin::class.java && instance != null) {
                VoicePluginService.unregisterPlugin(instance)
            }
            viewPluginWrapper.markDestroyed()
        }
        pluginWrapperList.clear()
        layoutPlugin.destroy()
    }
}
