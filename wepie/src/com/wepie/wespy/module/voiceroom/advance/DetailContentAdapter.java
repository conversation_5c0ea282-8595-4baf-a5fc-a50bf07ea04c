package com.wepie.wespy.module.voiceroom.advance;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.PagerAdapter;

import com.huiwan.configservice.model.voiceroom.AdvancedDetailInfo;

import java.util.ArrayList;
import java.util.List;

public class DetailContentAdapter extends PagerAdapter {

    private final Context context;
    private final List<AdvancedRoomDetailItem> viewList = new ArrayList<>();

    public DetailContentAdapter(Context context) {
        this.context = context;
    }

    public void refresh(List<AdvancedDetailInfo> detailInfos) {
        viewList.clear();
        for (int i = 0; i < detailInfos.size(); i++) {
            AdvancedRoomDetailItem item = new AdvancedRoomDetailItem(context);
            item.refresh(detailInfos.get(i));
            viewList.add(item);
        }
        notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return viewList.size();
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return view == object;
    }

    @NonNull
    @Override
    public Object instantiateItem(@NonNull ViewGroup container, int position) {
        container.addView(viewList.get(position));
        return viewList.get(position);
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        container.removeView(viewList.get(position));
    }

    @Nullable
    public AdvancedRoomDetailItem getViewFromIndex(int index) {
        if (index >= 0 && index < viewList.size()) {
            return viewList.get(index);
        } else {
            return null;
        }
    }
}
