package com.wepie.wespy.module.voiceroom.advance;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.configservice.model.voiceroom.AdvancedDetailInfo;
import com.wepie.wespy.R;

import java.util.ArrayList;
import java.util.List;

public class DetailTitleAdapter extends RecyclerView.Adapter<DetailTitleAdapter.TitleViewHolder> {

    private Context context;
    private List<AdvancedDetailInfo> tabList = new ArrayList<>();
    private int selectIndex = 0;
    private Callback callback;

    public DetailTitleAdapter(Context context) {
        this.context = context;
    }

    public void refresh(List<AdvancedDetailInfo> detailInfoList, int selectIndex) {
        this.tabList.clear();
        this.tabList.addAll(detailInfoList);
        this.selectIndex = selectIndex;
        notifyDataSetChanged();
    }

    public void setSelectIndex(int selectIndex) {
        this.selectIndex = selectIndex;
        notifyDataSetChanged();
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    @NonNull
    @Override
    public TitleViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new TitleViewHolder(LayoutInflater.from(context).inflate(R.layout.superior_room_title_item, null));
    }

    @Override
    public void onBindViewHolder(@NonNull TitleViewHolder holder, int position) {
        holder.update(tabList.get(position).getButtonText(), position == selectIndex, position, callback);
    }

    @Override
    public int getItemCount() {
        return tabList.size();
    }

    public interface Callback {
        void onClick(int position);
    }

    static class TitleViewHolder extends RecyclerView.ViewHolder {

        private final ImageView tabIv;
        private final TextView tabTv;

        public TitleViewHolder(@NonNull View itemView) {
            super(itemView);
            tabIv = itemView.findViewById(R.id.tab_iv);
            tabTv = itemView.findViewById(R.id.tab_tv);
        }

        public void update(String text, boolean selected, int position, Callback callback) {
            tabTv.setText(text);
            if (selected) {
                tabTv.setTextColor(0xffffffff);
                tabIv.setImageResource(R.drawable.superior_room_tab_select);
            } else {
                tabTv.setTextColor(0xff333333);
                tabIv.setImageResource(R.drawable.superior_room_tab_not_select);
            }
            itemView.setOnClickListener(view -> {
                if (!selected && callback != null) callback.onClick(position);
            });
        }
    }
}
