package com.wepie.wespy.module.voiceroom.advance;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.configservice.international.regoin.IDRegionConfig;
import com.huiwan.configservice.international.regoin.IDRegionUtil;
import com.wepie.wespy.R;

import java.util.ArrayList;
import java.util.List;

public class CreateAdvancedRoomAdapter extends RecyclerView.Adapter<CreateAdvancedRoomAdapter.CreateSuperiorRoomViewHolder> implements View.OnClickListener {

    private int selectedIndex;
    private final List<Integer> numberList = new ArrayList<>();
    private final Context mContext;
    public static final int NONE_NUMBER = -1;

    public CreateAdvancedRoomAdapter(Context context) {
        this.mContext = context;
    }

    public void addList(List<Integer> numberList) {
        if (numberList == null || numberList.isEmpty()) return;
        this.numberList.clear();
        this.numberList.addAll(numberList);
        selectedIndex = NONE_NUMBER;
        notifyDataSetChanged();
    }

    public int getSelectNumber() {
        if (selectedIndex < 0 || selectedIndex > numberList.size() - 1) return NONE_NUMBER;
        return numberList.get(selectedIndex);
    }

    @NonNull
    @Override
    public CreateSuperiorRoomViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(mContext).inflate(R.layout.create_superior_room_item, parent, false);
        itemView.setOnClickListener(this);
        return new CreateSuperiorRoomViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull CreateSuperiorRoomViewHolder holder, int position) {
        holder.itemView.setTag(position);
        holder.update(String.valueOf(numberList.get(position)), position == selectedIndex);
    }

    @Override
    public int getItemCount() {
        return numberList.size();
    }

    @Override
    public void onClick(View v) {
        selectedIndex = (int) v.getTag();
        notifyDataSetChanged();
    }

    public static class CreateSuperiorRoomViewHolder extends RecyclerView.ViewHolder {
        private final TextView numberTv;

        public CreateSuperiorRoomViewHolder(@NonNull View itemView) {
            super(itemView);
            numberTv = itemView.findViewById(R.id.number_tv);
        }

        public void update(String number, boolean selected) {
            if (IDRegionUtil.INSTANCE.isNumerical(number)) {
                numberTv.setText(IDRegionUtil.INSTANCE.getFinalIDStrByType(Long.parseLong(number), IDRegionConfig.ID_REGION_TYPE_TMP_ROOM));
            } else {
                numberTv.setText(number);
            }

            if (selected) {
                numberTv.setTextColor(0xffffffff);
                numberTv.setBackgroundResource(R.drawable.sel_accent_corner4);
            } else {
                numberTv.setTextColor(0xff333333);
                numberTv.setBackgroundResource(R.drawable.shape_00000000_corner4_stroke_e7e7e7);
            }
        }
    }
}
