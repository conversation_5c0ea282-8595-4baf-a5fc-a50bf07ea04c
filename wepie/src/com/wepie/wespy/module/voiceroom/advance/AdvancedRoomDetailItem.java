package com.wepie.wespy.module.voiceroom.advance;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.configservice.model.voiceroom.AdvancedDetailInfo;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;

public class AdvancedRoomDetailItem extends FrameLayout {

    private final Context context;
    private ImageView detailIv;
    private TextView titleTv, contentTv;

    public AdvancedRoomDetailItem(@NonNull Context context) {
        super(context);
        this.context = context;
        initView();
    }

    public AdvancedRoomDetailItem(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.context = context;
        initView();
    }

    private void initView() {
        LayoutInflater.from(context).inflate(R.layout.advanced_room_content_item, this);
        detailIv = findViewById(R.id.detail_iv);
        titleTv = findViewById(R.id.title_tv);
        contentTv = findViewById(R.id.content_tv);
    }

    public void refresh(AdvancedDetailInfo info) {
        WpImageLoader.load(info.getImage(), detailIv);
        titleTv.setText(info.getTitle());
        contentTv.setText(info.getContent());
    }
}
