package com.wepie.wespy.module.voiceroom.advance;

import android.os.Bundle;
import android.text.Html;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.international.regoin.IDRegionConfig;
import com.huiwan.configservice.international.regoin.IDRegionUtil;
import com.huiwan.configservice.model.voiceroom.VoiceLabelInfo;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackString;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.dialog.coin.PublicCoinPayBottomDialogView;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.common.jump.JumpRoomUtil;
import com.wepie.wespy.module.family.FamilyManager;
import com.wepie.wespy.module.game.room.create.CreateRoomTaskManager;
import com.wepie.wespy.module.makefriend.dataservice.VoiceRoomLastDataManager;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.main.RoomTypeConfig;
import com.wepie.wespy.module.voiceroom.main.RoomViewLogic;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

import java.util.HashMap;
import java.util.Map;

public class CreateAdvancedRoomActivity extends BaseActivity {

    private boolean showCancelCheck = false;
    private CreateAdvancedRoomAdapter adapter;
    private TextView changeIv;
    private int curPrice = 0;
    public static final String ROOM_TYPE = "room_type";
    public static final String RID = "rid";
    private int oldRid;
    private int roomType;
    private TextView titleTv;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setBackgroundDrawableResource(R.color.white);
        setContentView(R.layout.activity_create_advanced_room);
        final BaseWpActionBar actionBar = findViewById(R.id.action_bar);
        actionBar.addTitleRightTextWithBack(ResUtil.getStr(R.string.voice_room_advance_select_room_id), ResUtil.getStr(R.string.confirm),
                view -> checkCancel(), null);
        actionBar.refreshRightText(R.string.confirm, ResUtil.getColor(R.color.color_accent), true, view -> doEnter());
        changeIv = findViewById(R.id.change_tv);
        changeIv.setOnClickListener(view -> doChange());
        final RecyclerView numberRv = findViewById(R.id.number_rv);
        numberRv.setLayoutManager(new GridLayoutManager(this, 3));
        adapter = new CreateAdvancedRoomAdapter(this);
        numberRv.setAdapter(adapter);

        roomType = getIntent().getIntExtra(ROOM_TYPE, RoomTypeConfig.ROOM_TYPE_ADVANCED);
        oldRid = getIntent().getIntExtra(RID, 0);
        titleTv = findViewById(R.id.title_tv);
        initData();
        refresh(true);
    }

    private void refresh(boolean isFree) {
        VoiceRoomPacketSender.getAdvancedRoomRids(isFree, curPrice, new LifeSeqCallback(this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                if (!isFree) {
                    showCancelCheck = true;
                    addSensorClick(TrackString.SUCCESS);
                }
                TmpRoomPackets.GetCreateAdvancedRidsRsp advancedRidsRsp = (TmpRoomPackets.GetCreateAdvancedRidsRsp) head.message;
                curPrice = advancedRidsRsp.getNextPrice();
                changeIv.setText(ResUtil.getStr(R.string.voice_room_advance_change_room_id_cost_d, curPrice));
                adapter.addList(advancedRidsRsp.getRidsList());
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
                if (head.code == RspHeadInfo.ERROR_CODE_ROOM_COIN_NOT_ENOUGH) {
                    PublicCoinPayBottomDialogView.showBottomDialog(CreateAdvancedRoomActivity.this);
                }
                if (!isFree) {
                    addSensorClick(head.desc);
                }
            }
        });
    }

    private void initData() {
        if (isAdvanceRoom()) {
            titleTv.setText(R.string.activity_create_advanced_room_1);
        } else if (isFamilyRoom()) {
            titleTv.setText(Html.fromHtml(ResUtil.getStr(R.string.only_clan_elder_edit)));
        } else {
            titleTv.setText(R.string.activity_create_advanced_room_1);
        }
    }


    private boolean isAdvanceRoom() {
        return roomType == RoomTypeConfig.ROOM_TYPE_ADVANCED;
    }

    private void doChange() {
        String content = ResUtil.getStr(R.string.voice_room_advance_change_room_id_cost_tip_d, curPrice);
        DialogBuild.newBuilder(CreateAdvancedRoomActivity.this).setSingleBtn(false).setContent(content).setCanCancel(false)
                .setDialogCallback(new DialogBuild.DialogCallback() {
                    @Override
                    public void onClickSure() {
                        refresh(false);
                    }

                    @Override
                    public void onClickCancel() {

                    }
                }).show();
    }

    private void doEnter() {
        int rid = adapter.getSelectNumber();
        if (rid == CreateAdvancedRoomAdapter.NONE_NUMBER) {
            ToastUtil.show(R.string.voice_room_advance_select_room_id_first);
            return;
        }
        String content = "";
        String roomId = IDRegionUtil.INSTANCE.getFinalIDStrByType(rid, IDRegionConfig.ID_REGION_TYPE_TMP_ROOM);
        if (isAdvanceRoom()) {
            int coin = ConfigHelper.getInstance().getVoiceRoomConfig().getAdvancedRoomConfig().getPrice();
            content = ResUtil.getResource().getString(R.string.voice_room_advance_create_cost_d_s, coin, roomId);
        } else if (isFamilyRoom()) {
            content = ResUtil.getResource().getString(R.string.voice_room_advance_sure_select, roomId);
        }
        DialogBuild.newBuilder(CreateAdvancedRoomActivity.this).setSingleBtn(false).setContent(content).setCanCancel(false)
                .setDialogCallback(new DialogBuild.DialogCallback() {
                    @Override
                    public void onClickSure() {
                        CreateRoomTaskManager.getInstance().pushActivity(CreateAdvancedRoomActivity.this);
                        if (isAdvanceRoom()) {
                            createRoom(rid);
                        } else if (isFamilyRoom()) {
                            setRid(rid);
                        }
                    }

                    @Override
                    public void onClickCancel() {

                    }
                }).show();
    }

    private void setRid(int ridNew) {
        getProDialogUtil().showLoadingDelay(this);
        VoiceRoomPacketSender.setFamilyRid(oldRid, FamilyManager.getInstance().getSelfFamilyInfo().getFamily().getFamilyId(), ridNew, new LifeSeqCallback(this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                getProDialogUtil().hideLoading();
                VoiceRoomInfo voiceRoomInfo = VoiceRoomService.getInstance().getRoomInfo(head.rid);
                CreateRoomTaskManager.getInstance().finishAllActivitys();
                JumpRoomUtil.getInstance().jumpNewActivityAfterEnterRoom(CreateAdvancedRoomActivity.this, voiceRoomInfo, 0, true, null, false);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
                getProDialogUtil().hideLoading();
            }
        });
    }

    private void checkCancel() {
        if (showCancelCheck) {
            String content;
            if (isAdvanceRoom()) {
                content = ResUtil.getStr(R.string.voice_room_advance_spent_change_cancel_tip);
            } else {
                content = ResUtil.getStr(R.string.voice_room_family_spent_change_cancel_tip);
            }
            DialogBuild.newBuilder(CreateAdvancedRoomActivity.this).setSingleBtn(false).setContent(content).setCanCancel(false)
                    .setDialogCallback(new DialogBuild.DialogCallback() {
                        @Override
                        public void onClickSure() {
                            finish();
                        }

                        @Override
                        public void onClickCancel() {

                        }
                    }).show();
        } else {
            finish();
        }
    }

    private boolean requesting = false;

    private void createRoom(int rid) {
        if (requesting) {
            return;
        }
        getProDialogUtil().showLoadingDelay(this);
        requesting = true;
        int gameType = isAdvanceRoom() ? VoiceRoomLastDataManager.getInstance().getVoiceRoomGameType() : 0;
        int labelId = VoiceLabelInfo.LABEL_DEFAULT;
        RoomViewLogic.createRoom(rid, "", "", gameType, labelId, 0, "",
                roomType, new LifeSeqCallback(this) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        getProDialogUtil().hideLoading();
                        VoiceRoomInfo voiceRoomInfo = VoiceRoomService.getInstance().getRoomInfo(head.rid);
                        if (isAdvanceRoom()) {
                            if (PrefUserUtil.getInstance().getInt(PrefUserUtil.ADVANCED_ROOM_TIPS, PrefUserUtil.DEFAULT) == PrefUserUtil.DEFAULT) {
                                PrefUserUtil.getInstance().setInt(PrefUserUtil.ADVANCED_ROOM_TIPS, PrefUserUtil.NEED_SHOW);
                            }
                        }
                        CreateRoomTaskManager.getInstance().finishAllActivitys();
                        JumpRoomUtil.getInstance().jumpNewActivityAfterEnterRoom(CreateAdvancedRoomActivity.this, voiceRoomInfo, 0, true, null, false);
                        requesting = false;
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        getProDialogUtil().hideLoading();
                        if (head.code == RspHeadInfo.ERROR_CODE_RID_NOT_VALID) {
                            refresh(true);
                        } else if (head.code == RspHeadInfo.ERROR_CODE_CREATE_ROOM_COIN_NOT_ENOUGH) {
                            PublicCoinPayBottomDialogView.showBottomDialog(CreateAdvancedRoomActivity.this);
                        }
                        ToastUtil.show(head.desc);
                        requesting = false;
                    }
                });
    }

    public void addSensorClick(String result) {
        String btnName = TrackButtonName.CHANGE_OTHER;
        Map<String, Object> map = new HashMap<>();
        map.put("scene", TrackScreenName.CREATE_ADVANCED_ROOM);
        map.put("click_result", result);
        ShenceEvent.appClick(TrackScreenName.SELECT_ROOM_RID, btnName, map);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        CreateRoomTaskManager.getInstance().popActivity(this);
    }

    private boolean isFamilyRoom() {
        return roomType == RoomTypeConfig.ROOM_TYPE_FAMILY;
    }

    @Override
    public void onBackPressed() {
        checkCancel();
    }
}
