package com.wepie.wespy.module.voiceroom.advance;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.voiceroom.AdvancedDetailInfo;
import com.huiwan.configservice.model.voiceroom.AdvancedRoomConfig;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserInfoLoadCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.User;
import com.huiwan.widget.SimplePagerAdapter;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackString;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.coin.PublicCoinPayBottomDialogView;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.game.room.create.CreateRoomTaskManager;
import com.wepie.wespy.module.voiceroom.main.RoomTypeConfig;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AdvancedRoomDetailActivity extends BaseActivity {

    private RecyclerView titleRv;
    private ImageView titleShadowIv;
    private ViewPager2 contentViewPager;
    private DetailTitleAdapter titleAdapter;
    private SimplePagerAdapter<AdvancedRoomDetailItem> contentAdapter;
    private LinearLayoutManager manager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setBackgroundDrawableResource(R.color.white);
        setContentView(R.layout.activity_advanced_room_detail);
        final BaseWpActionBar actionBar = findViewById(R.id.action_bar);
        final TextView enterTv = findViewById(R.id.enter_tv);
        final TextView tips = findViewById(R.id.tips_tv);
        titleRv = findViewById(R.id.title_rv);
        titleShadowIv = findViewById(R.id.title_shadow_iv);
        contentViewPager = findViewById(R.id.content_view_pager);

        titleAdapter = new DetailTitleAdapter(this);
        manager = new LinearLayoutManager(this);
        manager.setOrientation(LinearLayoutManager.HORIZONTAL);
        titleRv.setLayoutManager(manager);
        titleRv.setAdapter(titleAdapter);
        contentAdapter = new SimplePagerAdapter<>();
        contentViewPager.setAdapter(contentAdapter);

        actionBar.addTitleAndBack(getString(R.string.open_advanced_room), view -> finish());
        AdvancedRoomConfig advancedRoomConfig = ConfigHelper.getInstance().getVoiceRoomConfig().getAdvancedRoomConfig();
        enterTv.setText(ResUtil.getStr(R.string.activity_advanced_room_create_now_cost_d, advancedRoomConfig.getPrice()));
        tips.setText(ResUtil.getStr(R.string.activity_advanced_room_create_condition_d, Integer.toString(advancedRoomConfig.getCharmLimit())));
        enterTv.setOnClickListener(view -> {
            UserService.get().getCacheUserFromServer(LoginHelper.getLoginUid(), new UserInfoLoadCallback() {
                @Override
                public void onUserInfoSuccess(User userInfo) {
                    if (userInfo.flower < advancedRoomConfig.getCharmLimit()) {
                        ToastUtil.show(ResUtil.getStr(R.string.activity_advanced_room_create_condition_charm_means_not, advancedRoomConfig.getCharmLimit()));
                        addSensorClick(TrackString.FLOWER_NOT_ENOUGH);
                        return;
                    }
                    if (userInfo.coin < advancedRoomConfig.getPrice()) {
                        ToastUtil.show(R.string.coin_not_enough_simple);
                        PublicCoinPayBottomDialogView.showBottomDialog(AdvancedRoomDetailActivity.this);
                        addSensorClick(TrackString.COIN_NOT_ENOUGH);
                        return;
                    }
                    addSensorClick(TrackString.JUMP_SUCCESS);
                    CreateRoomTaskManager.getInstance().pushActivity(AdvancedRoomDetailActivity.this);
                    JumpUtil.gotoCreateAdvanceRoomActivity(AdvancedRoomDetailActivity.this,  RoomTypeConfig.ROOM_TYPE_ADVANCED);
                }

                @Override
                public void onUserInfoFailed(String description) {
                    ToastUtil.show(description);
                }
            });
        });
        titleShadowIv.setVisibility(View.GONE);
        titleRv.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                if (titleRv.computeHorizontalScrollOffset() < ScreenUtil.dip2px(76)) {
                    if (titleShadowIv.getVisibility() != View.GONE) {
                        titleShadowIv.setVisibility(View.GONE);
                    }
                } else {
                    if (titleShadowIv.getVisibility() != View.VISIBLE) {
                        titleShadowIv.setVisibility(View.VISIBLE);
                    }
                }
            }
        });
        update(advancedRoomConfig.getDetailInfoList());
    }

    public void addSensorClick(String result) {
        Map<String, Object> map = new HashMap<>();
        map.put("click_result", result);
        ShenceEvent.appClick(TrackScreenName.CREATE_ADVANCED_ROOM, TrackButtonName.OPEN_IMMEDIATELY, map);
    }

    private void update(final List<AdvancedDetailInfo> detailInfoList) {
        int selectIndex = 0;
        List<AdvancedRoomDetailItem> viewList = new ArrayList<>();
        for (int i = 0; i < detailInfoList.size(); i++) {
            AdvancedRoomDetailItem item = new AdvancedRoomDetailItem(this);
            item.refresh(detailInfoList.get(i));
            viewList.add(item);
        }
        contentAdapter.refresh(viewList);
        titleAdapter.refresh(detailInfoList, selectIndex);
        titleAdapter.setCallback(position -> {
            if (position >= 0 && position < detailInfoList.size()) {
                contentViewPager.setCurrentItem(position, false);
            } else {
                if (LibBaseUtil.buildDebug())
                    ToastUtil.show(R.string.sorry_doesnot_exit_current_display);
            }
        });
        contentViewPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                if (position < 0 || position >= detailInfoList.size()) return;
                titleRv.scrollToPosition(position);
                titleRv.post(() -> {
                    View view = manager.findViewByPosition(position);
                    if (view != null) {
                        moveToMiddle(view);
                    }
                    titleAdapter.setSelectIndex(position);
                });
            }
        });
    }

    /**
     * 滚动到中间位置
     *
     * @param clkView 被点击的View
     */
    public void moveToMiddle(View clkView) {
        int itemWidth = clkView.getWidth();
        int screenWidth = getResources().getDisplayMetrics().widthPixels;
        int scrollWidth = clkView.getLeft() - (screenWidth / 2 - itemWidth / 2);
        titleRv.smoothScrollBy(scrollWidth, 0);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        CreateRoomTaskManager.getInstance().popActivity(this);
    }
}
