package com.wepie.wespy.module.voiceroom.sgift;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.HorizontalScrollView;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;

import com.huiwan.anim.SVGAUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.lib.api.ApiService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.opensource.svgaplayer.SVGADrawable;
import com.opensource.svgaplayer.SVGAImageView;
import com.opensource.svgaplayer.SVGAParser;
import com.opensource.svgaplayer.SVGAVideoEntity;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wepie.download.DownloadUtil;
import com.wepie.download.LifeDownloadCallback;
import com.wepie.lib.api.plugins.track.TrackApi;
import com.wepie.lib.api.plugins.track.config.cn.TrackSource;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.model.entity.EnterRoomInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceGiftInfo;
import com.wepie.wespy.module.common.jump.JumpRoomUtil;
import com.wepie.wespy.module.gift.GiftNumberView;
import com.wepie.wespy.utils.SvgaAssets;

import java.io.File;

public class BroadcastAnimItemView extends RelativeLayout implements BroadcastAnimContentView.Callback {
    private GiftNumberView numberView;
    private ImageView giftIv;
    private SVGAImageView svgaAnim;
    private BroadcastAnimContentView contentView1;
    private BroadcastAnimContentView contentView2;

    private HorizontalScrollView scrollView;
    private static final int RANGE = 10;
    private float mDownX;
    private float mDownY;

    public BroadcastAnimItemView(Context context) {
        super(context);
        init();
    }

    public BroadcastAnimItemView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        inflate(getContext(), R.layout.broadcast_anim_item_view, this);
        numberView = findViewById(R.id.gift_num_view);
        giftIv = findViewById(R.id.gift_iv);
        svgaAnim = findViewById(R.id.gift_svga);
        scrollView = findViewById(R.id.scroll_lay);
        contentView1 = findViewById(R.id.content_view1);
        contentView2 = findViewById(R.id.content_view2);

        contentView1.setCallback(this);
        contentView2.setCallback(this);

        SVGAImageView svgaImageView = findViewById(R.id.bg_svga);
        SVGAUtil.playSvga(SvgaAssets.VOICE_ROOM_BARRAGE_BG, -1, svgaImageView);
    }

    public void scrollTo(int position) {
        scrollView.scrollTo(position, 0);
    }

    public int getScrollLength() {
        return scrollView.getMaxScrollAmount();
    }

    private void trackJump() {
        ApiService.of(TrackApi.class).appClick(TrackSource.VOICE_ALL_NOTIFY, TrackButtonName.GOTO);
    }

    void touchListener(BroadcastAnimView broadcastAnimView) {
        this.setOnTouchListener((v, ev) -> {
            switch (ev.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    mDownX = ev.getX();
                    mDownY = ev.getY();
                    break;
                case MotionEvent.ACTION_MOVE:
                    break;
                case MotionEvent.ACTION_UP:
                    if (ev.getY() < mDownY + RANGE) {
                        broadcastAnimView.getHandler().removeCallbacks(broadcastAnimView.exitRunnable);
                        broadcastAnimView.getExitAnimator().start();
                    }
                    break;
            }
            return true;
        });
    }
    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {

        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mDownX = ev.getX();
                mDownY = ev.getY();
                return false;
            case MotionEvent.ACTION_MOVE:
                if (Math.abs(ev.getX() - mDownX) < RANGE && Math.abs(ev.getY() - mDownY) < RANGE) {
                    return false;
                } else {
                    return true;
                }
        }
        return super.onInterceptTouchEvent(ev);
    }

    @Override
    public void onJumpClick(VoiceGiftInfo giftInfo) {
        trackJump();
        EnterRoomInfo enterRoomInfo = EnterRoomInfo.buildEnterRoom(getContext(), giftInfo.broadRid, giftInfo.gameType).setSource(TrackSource.VOICE_ROOM_BROAD_GIFT);
        JumpRoomUtil.getInstance().enterRoom(enterRoomInfo);
    }

    public void update(UserSimpleInfo sender, UserSimpleInfo receiver, final VoiceGiftInfo giftInfo) {
        final Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(giftInfo.gift_id);
        if (gift == null) {
            return;
        }

        ImageLoaderUtil.loadNormalImage(gift.getMedia_url(), giftIv, ImageLoadInfo.getGiftInfo());
        numberView.showStaticNum(giftInfo.getComboInfo().getComboTimes());
        updateAnimIfNeed(gift);
        contentView1.update(sender, receiver, giftInfo);
        contentView2.update(sender, receiver, giftInfo);
        scrollTo(0);
        requestLayout();
    }

    private void updateAnimIfNeed(Gift gift) {
        if (!TextUtils.isEmpty(gift.broadcastAnimUrl) && gift.broadcastAnimUrl.endsWith("svga")) {
            final File file = new File(gift.getLocalBroadAnimPath());
            if (file.exists()) {
                svgaAnim.setVisibility(VISIBLE);
                giftIv.setVisibility(INVISIBLE);
                playSvga(file);
            } else {
                DownloadUtil.downloadFile(gift.broadcastAnimUrl, gift.getLocalBroadAnimPath(), new LifeDownloadCallback(ILifeUtil.toLife(this)) {
                    @Override
                    public void onSuccess(String url, String path) {
                        playSvga(file);
                    }

                    @Override
                    public void onFail(String msg) {
                        svgaAnim.setVisibility(INVISIBLE);
                        giftIv.setVisibility(VISIBLE);
                        TimeLogger.err("download broad anim error: " + msg);
                    }

                    @Override
                    public void onPercent(int percent) {

                    }
                });
            }
        } else {
            svgaAnim.setVisibility(INVISIBLE);
            giftIv.setVisibility(VISIBLE);
        }
    }

    private void playSvga(File file) {
        try {
            SVGAUtil.getFileSvgaAnim(file.getAbsolutePath(), new SVGAParser.ParseCompletion() {
                @Override
                public void onComplete(@NonNull SVGAVideoEntity svgaVideoEntity) {
                    svgaAnim.setVisibility(VISIBLE);
                    giftIv.setVisibility(INVISIBLE);
                    svgaAnim.setImageDrawable(new SVGADrawable(svgaVideoEntity));
                    svgaAnim.startAnimation();
                }

                @Override
                public void onError() {
                    svgaAnim.setVisibility(INVISIBLE);
                    giftIv.setVisibility(VISIBLE);
                    TimeLogger.err("parse svga anim error");
                }
            });
        } catch (Exception e) {
            TimeLogger.err("error" + e.getMessage());
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        svgaAnim.stopAnimation(true);
        super.onDetachedFromWindow();
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        TimeLogger.msg("touch event");
        return super.dispatchTouchEvent(ev);
    }
}
