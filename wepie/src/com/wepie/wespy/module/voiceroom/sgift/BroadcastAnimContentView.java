package com.wepie.wespy.module.voiceroom.sgift;

import android.content.Context;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.international.regoin.IDRegionConfig;
import com.huiwan.configservice.international.regoin.IDRegionUtil;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.decorate.CharmLabelView;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.lib.api.plugins.track.config.cn.TrackSource;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceGiftInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;

import java.util.Locale;

public class BroadcastAnimContentView extends RelativeLayout {
    private CharmLabelView sendLevelIv;
    private View jump;
    private TextView sendNameTv;
    private TextView roomTv;
    private CharmLabelView receiverLevelIv;
    private TextView receiverNameTv;
    private TextView giftTv;
    private DecorHeadImgView senderHeadIv;
    private DecorHeadImgView receiverHeadIv;
    private Callback callback;

    public BroadcastAnimContentView(Context context) {
        super(context);
        init();
    }

    public BroadcastAnimContentView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    private void init() {
        inflate(getContext(), R.layout.broadcast_anim_content_view, this);
        jump = findViewById(R.id.jump_view);
        sendLevelIv = findViewById(R.id.sender_level_iv);
        sendNameTv = findViewById(R.id.sender_name_tv);
        roomTv = findViewById(R.id.send_room_tv);
        receiverLevelIv = findViewById(R.id.receiver_level_iv);
        receiverNameTv = findViewById(R.id.receiver_name_tv);
        giftTv = findViewById(R.id.gift_info_tv);
        senderHeadIv = findViewById(R.id.sender_head);
        receiverHeadIv = findViewById(R.id.receiver_head);
    }

    public void update(UserSimpleInfo sender, UserSimpleInfo receiver, final VoiceGiftInfo giftInfo) {
        final Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(giftInfo.gift_id);
        if (gift == null) {
            return;
        }
        jump.setVisibility(giftInfo.hasPassword ? GONE : VISIBLE);

        jump.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (callback != null) callback.onJumpClick(giftInfo);
            }
        });
        sendNameTv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                JumpUtil.enterUserInfoDetailActivity(v.getContext(), giftInfo.sender, TrackSource.VOICE_ALL_NOTIFY);
            }
        });
        receiverNameTv.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                JumpUtil.enterUserInfoDetailActivity(v.getContext(), giftInfo.receiver, TrackSource.VOICE_ALL_NOTIFY);
            }
        });
        roomTv.setText(ResUtil.getStr(R.string.broad_anim_send_in_room, IDRegionUtil.INSTANCE.getFinalIDStrByType(giftInfo.broadRid, IDRegionConfig.ID_REGION_TYPE_TMP_ROOM)));

        sendLevelIv.setFlower(sender.flower);
        sendNameTv.setText(sender.getRemarkName()); //改为展示朴素文本
        senderHeadIv.showUserHead(sender.uid);

        receiverLevelIv.setFlower(receiver.flower);
        receiverNameTv.setText(receiver.getRemarkName());
        receiverHeadIv.showUserHead(receiver.uid);

        SpannableStringBuilder ssb = new SpannableStringBuilder();
        ssb.append(gift.getName());
        ssb.append(ResUtil.getStr(R.string.char_period));
        if (!TextUtils.isEmpty(giftInfo.broadContent.trim())) {
            int start = ssb.length();
            ssb.append(ResUtil.getStr(R.string.broad_note, giftInfo.broadContent));
            ssb.setSpan(new ForegroundColorSpan(0xff43fffc), start, ssb.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        giftTv.setText(ssb);
        requestLayout();
    }

    public interface Callback {
        void onJumpClick(VoiceGiftInfo giftInfo);
    }
}
