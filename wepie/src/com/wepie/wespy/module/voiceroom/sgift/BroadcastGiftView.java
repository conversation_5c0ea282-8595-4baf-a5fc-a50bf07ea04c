package com.wepie.wespy.module.voiceroom.sgift;

import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.DecelerateInterpolator;

import com.huiwan.base.util.ContextUtil;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceGiftInfo;
import com.wepie.wespy.model.event.ContentViewChildChangeEvent;
import com.huiwan.user.UserListSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.wepie.wespy.module.notify.BaseNotifyView;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginFrameLayout;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IBroadcastGiftView;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.log.TimeLogger;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

/**
 * date 2019-05-30
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class BroadcastGiftView extends PluginFrameLayout implements IBroadcastGiftView, BroadcastAnimView.Callback {
    private boolean hasAppNotifyContent = false;
    private ValueAnimator animator;
    private BroadcastAnimView broadcastAnimView;
    private float mDownX;
    private float mDownY;

    public BroadcastGiftView(Context context) {
        this(context, null);
    }

    public BroadcastGiftView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public void onVoiceBroadcastGift(final VoiceGiftInfo giftInfo) {
        UserService.get().getCacheSimpleUserList(Arrays.asList(giftInfo.sender, giftInfo.receiver), new UserListSimpleInfoCallback() {
            @Override
            public void onUserInfoSuccess(List<UserSimpleInfo> userSimpleInfoList) {
                if (userSimpleInfoList.size() >= 2) {
                    showAnim(userSimpleInfoList.get(0), userSimpleInfoList.get(1), giftInfo);
                }
            }

            @Override
            public void onUserInfoFailed(String description) {
                TimeLogger.err("user info failed: " + description);
            }
        });
    }

    @Override
    protected void initView() {
        TimeLogger.msg("init broadcast gift view");
        setClipChildren(false);

        broadcastAnimView = new BroadcastAnimView(getContext());
        broadcastAnimView.setCallback(this);
        addView(broadcastAnimView, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
    }

    @Override
    protected void initData() {
        TimeLogger.msg("init data broadcast gift view");
    }

    private void showAnim(UserSimpleInfo sender, UserSimpleInfo receiver, VoiceGiftInfo giftInfo) {
        if (broadcastAnimView.canAdd()) {
            broadcastAnimView.showAnim(sender, receiver, giftInfo);
            broadcastAnimView.getItemView().touchListener(broadcastAnimView);
        } else {
            pendingBroadItemInfoList.add(new PendingBroadItemInfo(sender, receiver, giftInfo));
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onContentViewChildChange(ContentViewChildChangeEvent event) {
        checkAppNotifyContent();
    }

    @Override
    protected void onAttachedToWindow() {
        checkAppNotifyContent();
        EventDispatcher.registerEventObserver(this);
        super.onAttachedToWindow();
    }

    @Override
    protected void onDetachedFromWindow() {
        cancelAnim();
        EventDispatcher.unregisterEventObserver(this);
        super.onDetachedFromWindow();
    }

    private final List<PendingBroadItemInfo> pendingBroadItemInfoList = new LinkedList<>();

    private void cancelAnim() {
        if (animator != null) {
            animator.cancel();
        }
    }

    private void checkAppNotifyContent() {
        Activity activity = ContextUtil.getActivityFromContext(getContext());
        if (activity != null) {
            ViewGroup root = activity.findViewById(android.R.id.content);
            if (root != null) {
                int count = root.getChildCount();
                boolean hasNotify = false;
                for (int i = 0; i < count; i++) {
                    View child = root.getChildAt(i);
                    if (child instanceof BaseNotifyView) {
                        hasNotify = true;
                        break;
                    }
                }
                if (hasAppNotifyContent != hasNotify) {
                    hasAppNotifyContent = hasNotify;
                    updateVoiceRoomChildren();
                }
            }
        }
    }

    private void updateVoiceRoomChildren() {
        int top = hasAppNotifyContent ? ScreenUtil.dip2px(48) : 0;
        int oriTop = getPaddingTop();
        if (oriTop != top) {
            startAnim(oriTop, top);
        } else {
            cancelAnim();
        }
    }

    private void startAnim(int from, int to) {
        cancelAnim();
        animator = ValueAnimator.ofInt(from, to);
        animator.setDuration(200);
        animator.setInterpolator(new DecelerateInterpolator());
        animator.addUpdateListener((animation) -> {
            int topPadding = (Integer) animation.getAnimatedValue();
            setPaddingRelative(0, topPadding, 0, 0);
        });
        animator.start();
    }

    @Override
    public void onShowEnd() {
        if (pendingBroadItemInfoList.size() > 0) {
            PendingBroadItemInfo itemInfo = pendingBroadItemInfoList.remove(0);
            broadcastAnimView.showAnim(itemInfo.sender, itemInfo.receiver, itemInfo.giftInfo);
        }
    }

    private static class PendingBroadItemInfo {
        UserSimpleInfo sender;
        UserSimpleInfo receiver;
        VoiceGiftInfo giftInfo;

        PendingBroadItemInfo(UserSimpleInfo sender, UserSimpleInfo receiver, VoiceGiftInfo giftInfo) {
            this.sender = sender;
            this.receiver = receiver;
            this.giftInfo = giftInfo;
        }
    }
}
