package com.wepie.wespy.module.voiceroom.sgift;

import android.animation.ValueAnimator;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.animation.LinearInterpolator;
import android.widget.RelativeLayout;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceGiftInfo;

public class BroadcastAnimView extends RelativeLayout {
    private ValueAnimator enterAnimator;
    private ValueAnimator scrollAnimator;
    private ValueAnimator exitAnimator;
    private BroadcastAnimItemView itemView;
    private Handler handler = new Handler(Looper.getMainLooper());
    private boolean occupied = false;
    private Callback callback;

    private int enterDuration = 250;
    private int scrollDuration = 8000;
    private int exitDuration = 250;

    private float scrollSpeed = 0.12f;
    public Runnable exitRunnable = () -> exitAnimator.start();

    public BroadcastAnimView(Context context) {
        this(context, null);
    }

    public BroadcastAnimView(Context context, AttributeSet attrs) {
        super(context, attrs);
        setClipChildren(false);
        if (StatusBarUtil.isSupportFullScreen()) {
            setPadding(0, ScreenUtil.dip2px(22) + ScreenUtil.getStatusBarHeight(), 0, 0);
        } else {
            setPadding(0, ScreenUtil.dip2px(22), 0, 0);
        }
        initAnimator();
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    private void initAnimator() {
        int screenWidth = ScreenUtil.getScreenWidth(getContext());
        int len = ScreenUtil.dip2px(scrollDuration * scrollSpeed);
        if (ScreenUtil.isRtl()) {
            enterAnimator = ValueAnimator.ofFloat(-screenWidth, 0);
            scrollAnimator = ValueAnimator.ofInt(len - screenWidth, - screenWidth);
            exitAnimator = ValueAnimator.ofFloat(0, screenWidth);
        } else {
            enterAnimator = ValueAnimator.ofFloat(screenWidth, 0);
            scrollAnimator = ValueAnimator.ofInt(0, len);
            exitAnimator = ValueAnimator.ofFloat(0, -screenWidth);
        }
        enterAnimator.setInterpolator(new LinearInterpolator());
        enterAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if (itemView == null) return;
                float x = (float) animation.getAnimatedValue();
                itemView.setTranslationX(x);
            }
        });
        enterAnimator.setDuration(enterDuration);

        scrollAnimator.setInterpolator(new LinearInterpolator());
        scrollAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if (itemView == null) return;
                int x = (int) animation.getAnimatedValue();
                itemView.scrollTo(x);
            }
        });
        scrollAnimator.setDuration(scrollDuration);

        exitAnimator.setInterpolator(new LinearInterpolator());
        exitAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if (itemView == null) return;
                float x = (float) animation.getAnimatedValue();
                itemView.setTranslationX(x);
            }
        });
        exitAnimator.setDuration(enterDuration);
    }

    private void cancelAllAnim() {
        if (enterAnimator != null) {
            enterAnimator.cancel();
        }
        if (exitAnimator != null) {
            exitAnimator.cancel();
        }
        if (scrollAnimator != null) {
            scrollAnimator.cancel();
        }
    }

    public void showAnim(UserSimpleInfo sender, UserSimpleInfo receiver, VoiceGiftInfo giftInfo) {
        occupied = true;
        cancelAllAnim();
        if (itemView == null) {
            itemView = new BroadcastAnimItemView(getContext());
            addView(itemView);
        }
        itemView.setVisibility(VISIBLE);
        itemView.update(sender, receiver, giftInfo);
        int translationX = ScreenUtil.isRtl() ? -ScreenUtil.getScreenWidth(getContext()) : ScreenUtil.getScreenWidth(getContext());
        itemView.setTranslationX(translationX);
        enterAnimator.start();

        handler.postDelayed(() -> {
            scrollAnimator.start();
        }, enterDuration);

        handler.postDelayed(exitRunnable, enterDuration + scrollDuration);

        handler.postDelayed(() -> {
            cancelAllAnim();
            occupied = false;
            itemView.setVisibility(INVISIBLE);
            if (callback != null) callback.onShowEnd();
        }, enterDuration + scrollDuration + exitDuration);
    }

    public boolean canAdd() {
        return !occupied;
    }

    public interface Callback {
        void onShowEnd();
    }

    public ValueAnimator getExitAnimator() {
        return exitAnimator;
    }

    public BroadcastAnimItemView getItemView() {
        return itemView;
    }

    public Handler getHandler() {
        return handler;
    }
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        cancelAllAnim();
        handler.removeCallbacksAndMessages(null);
    }
}
