package com.wepie.wespy.module.voiceroom

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.LayoutRes

object ViewPluginLayoutHelper {
    private val viewPlugins: MutableMap<ViewPlugin<*, *>, View> = mutableMapOf()

    /**
     * 根View使用此方式加载
     * 每次都是全新的数据
     */
    @JvmStatic
    fun inflateForRoot(
        @LayoutRes layout: Int,
        root: ViewGroup,
        layoutInflater: LayoutInflater? = null
    ): MutableMap<ViewPlugin<*, *>, View> {
        viewPlugins.clear()
        inflateForChild(layout, root, layoutInflater)
        ViewPlugin.tagToPluginMap.forEach {
            if (viewPlugins[it.value] == null) {
                viewPlugins[it.value] = ViewPluginStubNonAdd(root.context)
            }
        }
        val viewPluginsCopy = viewPlugins.toMutableMap()
        viewPlugins.clear()
        return viewPluginsCopy
    }

    /**
     * 根View内的子View使用此方式加载，解决子View的ViewPluginStub无法索引到的问题
     * 可以调用多次，数据依附于inflateForRoot
     */
    fun inflateForChild(
        @LayoutRes layout: Int,
        root: ViewGroup,
        layoutInflater: LayoutInflater? = null
    ): View {
        val cloneInContext: LayoutInflater
        val factory2: ViewPluginLayoutFactory2
        if (layoutInflater != null) {
            cloneInContext = layoutInflater.cloneInContext(layoutInflater.context)
            factory2 = ViewPluginLayoutFactory2(layoutInflater.factory2)
        } else {
            val pre = LayoutInflater.from(root.context)
            cloneInContext = pre.cloneInContext(pre.context)
            factory2 = ViewPluginLayoutFactory2(pre.factory2)
        }
        cloneInContext.factory2 = factory2
        val view = cloneInContext.inflate(layout, root)
        viewPlugins.putAll(factory2.viewPlugins)
        return view
    }
}

private class ViewPluginLayoutFactory2(
    private val base: LayoutInflater.Factory2?
) : LayoutInflater.Factory2 {
    val viewPlugins = mutableMapOf<ViewPlugin<*, *>, View>()

    override fun onCreateView(
        parent: View?,
        name: String,
        context: Context,
        attrs: AttributeSet
    ): View? {
        var v: View? = when (name) {
            ViewPluginStub::class.java.name -> {
                val viewPluginStub = ViewPluginStub(context, attrs)
                val viewPlugin = ViewPlugin.tagToPluginMap[viewPluginStub.tag]
                if (viewPlugin != null) {
                    viewPlugins[viewPlugin] = viewPluginStub
                }
                viewPluginStub
            }

            else -> null
        }
        if (v == null) {
            v = base?.onCreateView(parent, name, context, attrs)
        }
        return v
    }

    override fun onCreateView(name: String, context: Context, attrs: AttributeSet): View? {
        return base?.onCreateView(name, context, attrs)
    }
}

/**
 * 用于定义在xml文件中，类似于ViewStub
 * 不同的是，ViewStub只能是单向的，而ViewPluginStub可以双向
 */
open class ViewPluginStub(context: Context, attrs: AttributeSet? = null) : View(context, attrs) {
    init {
        visibility = GONE
        setWillNotDraw(true)
    }
}

/**
 * 用于非定义在xml中，自己处理显示
 * PluginLayoutManager只负责创建
 */
class ViewPluginStubNonAdd(context: Context) : ViewPluginStub(context)