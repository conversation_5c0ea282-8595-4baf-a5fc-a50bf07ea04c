package com.wepie.wespy.module.voiceroom.roomgroup

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog
import com.wepie.wespy.helper.dialog.bottomsheet.BottomSheetView
import com.wepie.wespy.helper.dialog.bottomsheet.WpDragDialog

class RoomOwnerGroupHelpDialog(context: Context, onClose: () -> Unit) : ConstraintLayout(context) {

    companion object {
        fun show(context: Context) {
            val baseFullScreenDialog = BaseFullScreenDialog(context, R.style.dialog_style_custom)
            val wpDragDialog = WpDragDialog(context, BottomSheetView.BottomSheetState.Disable)
            wpDragDialog.setContentView(RoomOwnerGroupHelpDialog(context) {
                baseFullScreenDialog.dismiss()
            }) {}
            baseFullScreenDialog.setContentView(wpDragDialog)
            baseFullScreenDialog.initBottomDialog()
            baseFullScreenDialog.show()
        }
    }

    init {
        View.inflate(context, R.layout.room_owner_group_help_view, this)
        layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        findViewById<View>(R.id.room_owner_group_help_close).setOnClickListener {
            onClose.invoke()
        }
    }

}

