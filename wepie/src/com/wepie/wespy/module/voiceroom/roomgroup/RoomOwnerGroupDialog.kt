package com.wepie.wespy.module.voiceroom.roomgroup

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.huiwan.base.LibBaseUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.base.util.inputfilter.LangEnterLengthFilter
import com.huiwan.libtcp.callback.SeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.user.LifeUserSimpleInfoCallback
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.huiwan.user.entity.UserSimpleInfo
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog
import com.wepie.wespy.helper.dialog.bottomsheet.WpDragDialog
import com.wepie.wespy.model.entity.group.GroupInfo
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.chat.ui.group.interest.GroupInfoItemView
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.net.tcp.packet.GroupPackets
import com.wepie.wespy.net.tcp.packet.GroupPackets.GetGroupInfoByGidsRsp
import com.wepie.wespy.net.tcp.sender.ChatPacketSenderNew

class RoomOwnerGroupDialog(
    context: Context,
    private val rid: Int,
    bindGids: List<Int>
) : ConstraintLayout(context) {

    companion object {
        fun show(context: Context, rid: Int) {
            val bindGids = RoomOwnerGroupData.bindGids.value
            if (bindGids.isNullOrEmpty()) {
                return
            }
            val baseFullScreenDialog = BaseFullScreenDialog(context, R.style.dialog_style_custom)
            val wpDragDialog = WpDragDialog(context)
            wpDragDialog.setContentView(RoomOwnerGroupDialog(context, rid, bindGids)) {
                baseFullScreenDialog.dismiss()
            }
            baseFullScreenDialog.setContentView(wpDragDialog)
            baseFullScreenDialog.initBottomDialog()
            baseFullScreenDialog.show()
        }
    }

    private val roomOwnerSetting: TextView
    private val roomOwnerContainer: LinearLayout
    private val roomOwnerTitleLay: ViewGroup
    private val roomOwnerTitle: TextView

    init {
        View.inflate(context, R.layout.room_owner_group_view, this)
        roomOwnerContainer = findViewById(R.id.room_owner_group_container)
        roomOwnerTitleLay = findViewById(R.id.room_owner_group_title_lay)
        roomOwnerTitle = findViewById(R.id.room_owner_group_title)
        if (LibBaseUtil.getLang().isAr) {
            val suffix = roomOwnerTitleLay.getChildAt(1)
            roomOwnerTitleLay.removeView(suffix)
            roomOwnerTitleLay.addView(suffix, 0)
        }
        roomOwnerSetting = findViewById(R.id.room_owner_group_setting)
        roomOwnerSetting.setOnClickListener {
            RoomOwnerGroupActivity.start(context, rid)
        }
        val roomInfo = VoiceRoomService.getInstance().getRoomInfo(rid)
        UserService.get().getCacheSimpleUser(roomInfo.owner, object : LifeUserSimpleInfoCallback(this@RoomOwnerGroupDialog) {
            override fun onUserInfoSuccess(userInfo: UserSimpleInfo) {
                updateTitle(userInfo.nickname)
            }

            override fun onUserInfoFailed(description: String?) = Unit
        })

        ChatPacketSenderNew.getInstance().getGroupInfoByGidsReq(bindGids, object : SeqCallback {
            override fun onSuccess(head: RspHeadInfo) {
                val groupList: MutableList<GroupPackets.SimpleGroupInfo> =
                    (head.message as GetGroupInfoByGidsRsp).groupListList
                addView(groupList, roomInfo)
            }

            override fun onFail(head: RspHeadInfo?) = ToastUtil.show(head?.desc)

        })
    }

    private fun addView(groupList: List<GroupPackets.SimpleGroupInfo>, roomInfo: VoiceRoomInfo) {
        roomOwnerContainer.removeAllViews()
        val isOwner = roomInfo.isOwner(LoginHelper.getLoginUid())
        roomOwnerSetting.isVisible = isOwner
        groupList.forEach { groupInfo ->
            val view = GroupInfoItemView(context)
            view.setScreenName(TrackScreenName.GROUP_IN_VOICE_ROOM_DIALOG)
            view.setRid(rid)
            view.updateMain(GroupInfo.SimpleToGroupInfo(groupInfo))
            if (isOwner) {
                view.hideOptionBtn()
            }
            roomOwnerContainer.addView(view)
        }
    }

    private fun updateTitle(userName: String) {
        val maxLength = 10
        var length = 0
        var index = -1
        // 去除一些零宽字符
        val name = userName.replace(
            Regex("[\\u200b-\\u200f]|[\\u200e-\\u200f]|[\\u202a-\\u202e]|[\\u2066-\\u2069]|\ufeff|\u06ec"),
            ""
        )
        name.toCharArray().forEachIndexed { _index, _char ->
            length += LangEnterLengthFilter.getCharLen(_char, false)
            if (length <= maxLength) {
                index = _index
            }
        }
        val text = if (length <= maxLength) {
            name
        } else {
            name.substring(0, index + 1) + "..."
        }
        roomOwnerTitle.text = text
    }
}

