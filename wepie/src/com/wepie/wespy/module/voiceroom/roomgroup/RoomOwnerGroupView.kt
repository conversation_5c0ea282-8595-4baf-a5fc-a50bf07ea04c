package com.wepie.wespy.module.voiceroom.roomgroup

import android.content.Context
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.wepie.wespy.R
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IBasePlugin

class RoomOwnerGroupView(
    context: Context
) : ConstraintLayout(context), IBasePlugin {
    init {
        View.inflate(context, R.layout.room_owner_state_view, this)
        setOnClickListener {
            val voiceRoomService = VoiceRoomService.getInstance()
            RoomOwnerGroupDialog.show(
                context,
                voiceRoomService.rid
            )
        }
    }


}