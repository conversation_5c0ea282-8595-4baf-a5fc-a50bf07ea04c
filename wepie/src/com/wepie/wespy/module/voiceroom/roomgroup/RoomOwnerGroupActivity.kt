package com.wepie.wespy.module.voiceroom.roomgroup

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.isVisible
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.empty.HWUIEmptyView
import com.huiwan.base.util.ToastUtil
import com.huiwan.component.activity.BaseActivity
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.user.LoginHelper
import com.huiwan.widget.actionbar.BaseWpActionBar
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.group.GroupInfo
import com.wepie.wespy.module.chat.dataservice.group.GroupService
import com.wepie.wespy.module.chat.ui.group.interest.GroupInfoItemView
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.family.FamilyManager
import com.wepie.wespy.net.tcp.sender.ChatPacketSenderNew
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender
import com.wepie.wespy.utils.ViewOnClickListenerUtils

class RoomOwnerGroupActivity : BaseActivity() {
    companion object {
        @JvmStatic
        fun start(context: Context, rid: Int) {
            val starter = Intent(context, RoomOwnerGroupActivity::class.java)
                .putExtra("rid", rid)
            context.startActivity(starter)
        }
    }

    private lateinit var roomOwnerTitle: BaseWpActionBar
    private lateinit var roomOwnerTips: TextView
    private lateinit var roomOwnerContainer: LinearLayout
    private lateinit var groupEmptyView: HWUIEmptyView
    private var rid: Int = -1

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_room_owner_group)
        rid = intent.getIntExtra("rid", -1)
        initView()
    }

    override fun onResume() {
        super.onResume()
        initData()
    }

    private fun initView() {
        roomOwnerTitle = findViewById(R.id.room_owner_title)
        roomOwnerTips = findViewById(R.id.room_owner_tips)
        roomOwnerContainer = findViewById(R.id.room_owner_container)
        roomOwnerTitle.addTitleRightIcon12WithBackComponentStyle(
            ResUtil.getStr(R.string.interest_group_associate_group),
            R.drawable.icon_interest_group_create, R.drawable.icon_interest_help, false,
            { finish() },
            { JumpUtil.gotoInterestGroupCreateActivity(this, 0) },
            { RoomOwnerGroupHelpDialog.show(this) })
        groupEmptyView = findViewById(R.id.group_empty_view)
    }

    private fun initData() {
        ChatPacketSenderNew.getInstance().getGroupContactList(1, object : LifeSeqCallback(this) {
            override fun onSuccess(head: RspHeadInfo) {
                addView(rid)
            }

            override fun onFail(head: RspHeadInfo) {
                ToastUtil.show(head.desc)
            }
        })
    }

    private fun addView(rid: Int) {
        val bindGids = RoomOwnerGroupData.bindGids.value ?: listOf()
        roomOwnerTips.isVisible = false
        roomOwnerContainer.removeAllViews()
        val iCreated = iCreated(GroupService.getInstance().contactGroupList)
        if (iCreated.isEmpty()) {
            groupEmptyView.isVisible = true
            return
        }
        groupEmptyView.isVisible = false
        var size = bindGids.size
        roomOwnerTips.isVisible = true
        updateTitle(size)
        iCreated.forEach { groupInfo ->
            val view = RoomOwnerGroupItemView(this, bindGids.contains(groupInfo.gid), rid) {
                if (it) {
                    size++
                } else {
                    size--
                }
                updateTitle(size)
            }
            view.updateGroupInfo(groupInfo)
            roomOwnerContainer.addView(view)
        }
    }

    private fun iCreated(groupInfoList: List<GroupInfo>): List<GroupInfo> {
        return groupInfoList.filter { groupInfo ->
            !FamilyManager.getInstance().checkFamilyGroupAvailable(
                groupInfo.gid,
                groupInfo.familyId
            )
        }.filter { groupInfo ->
            groupInfo.owner == LoginHelper.getLoginUid()
        }
    }

    private fun updateTitle(size: Int) {
        roomOwnerTitle.setTitle(
            ResUtil.getStr(R.string.interest_group_associate_group_title, size, 3)
        )
    }
}

class RoomOwnerGroupItemView(
    context: Context,
    private var hasAssociated: Boolean,
    private val rid: Int,
    private val onAssociate: (Boolean) -> Unit
) : GroupInfoItemView(context) {
    private lateinit var groupInfo: GroupInfo
    private val associateOption: TextView = findViewById(R.id.interest_group_associate_option)
    override fun layoutId(): Int {
        return R.layout.room_owner_group_item_view
    }

    fun updateGroupInfo(groupInfo: GroupInfo) {
        super.updateMain(groupInfo) {}
        this.groupInfo = groupInfo
        if (hasAssociated) {
            associateSuccess()
        } else {
            disassociateSuccess()
        }
    }

    private fun associate() {
        VoiceRoomPacketSender.associateGroup(rid, groupInfo.gid, object : LifeSeqCallback(this) {
            override fun onSuccess(head: RspHeadInfo?) {
                associateSuccess()
                onAssociate.invoke(true)
            }

            override fun onFail(head: RspHeadInfo?) = ToastUtil.show(head?.desc)

        })
    }

    private fun associateSuccess() {
        associateOption.setBackgroundResource(R.drawable.shape_f7f8fa_corner16)
        associateOption.text = ResUtil.getStr(R.string.interest_group_disassociate)
        associateOption.setTextColor(Color.parseColor("#68697a"))
        associateOption.setOnClickListener(object : ViewOnClickListenerUtils() {
            override fun onProhibitRepeatClick(v: View?) {
                disassociate()
            }
        })
    }

    private fun disassociate() {
        VoiceRoomPacketSender.disassociateGroup(rid, groupInfo.gid, object : LifeSeqCallback(this) {
            override fun onSuccess(head: RspHeadInfo?) {
                disassociateSuccess()
                onAssociate.invoke(false)
            }

            override fun onFail(head: RspHeadInfo?) = ToastUtil.show(head?.desc)

        })
    }

    private fun disassociateSuccess() {
        associateOption.setBackgroundResource(R.drawable.shape_00ccf9_corner16)
        associateOption.text = ResUtil.getStr(R.string.interest_group_associate)
        associateOption.setTextColor(ResUtil.getColor(R.color.white))
        associateOption.setOnClickListener(object : ViewOnClickListenerUtils() {
            override fun onProhibitRepeatClick(v: View?) {
                associate()
            }
        })
    }
}