package com.wepie.wespy.module.voiceroom.roomgroup

import com.wejoy.weplay.ex.lifecycle.observe
import com.wepie.wespy.module.voiceroom.IPluginTool
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IBasePlugin

class RoomOwnerGroupDelegateImpl(
    private val pluginTool: IPluginTool<IBasePlugin>
) : IBasePlugin by pluginTool.proxy() {

    init {
        RoomOwnerGroupData.bindGids.observe(pluginTool) {
            pluginTool.loadView(it.isNotEmpty())
        }
    }
}