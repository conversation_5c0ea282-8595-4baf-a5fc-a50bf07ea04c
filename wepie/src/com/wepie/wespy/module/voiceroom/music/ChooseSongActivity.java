package com.wepie.wespy.module.voiceroom.music;

import static com.wepie.wespy.net.tcp.sender.VoiceMusicSender.SEARCH_TYPE_SEARCH;

import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.IMMHelper;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.base.util.StringUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.IntentConfig;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.widget.SimplePagerAdapter;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceMusicInfo;
import com.wepie.wespy.model.event.ExitRoomEvent;
import com.wepie.wespy.module.common.SimpleTextWatcher;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.tcp.packet.MusicPackets;
import com.wepie.wespy.net.tcp.sender.VoiceMusicSender;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Arrays;
import java.util.List;

/**
 * date 2019-05-10
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class ChooseSongActivity extends BaseActivity implements RecentChooseManager.Observer {
    private TabLayout musicTab;
    private TabLayoutMediator mediator;
    private ChooseSongListView searchListView;
    private ChooseSongListView sharedListView;
    private ChooseSongListView recentListView;
    private ChooseSongListView favoriteListView;
    public static final List<String> titleList = Arrays.asList(
            ResUtil.getStr(R.string.voice_room_music_favorite),
            ResUtil.getStr(R.string.voice_room_music_shared),
            ResUtil.getStr(R.string.voice_room_music_recent));
    private EditText searchEt;
    private View cancelSearchBtn;
    private View backBtn;
    private LinearLayout searchLayout;

    private int rid = 0;
    private boolean searching = false;
    private ViewPager2 pager;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_choose_song);
        initArg();
        initViews();
        initEvents();
        updateData();
        EventDispatcher.registerEventObserver(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mediator.detach();
    }

    private void initArg() {
        rid = getIntent().getIntExtra(IntentConfig.INT_ROOM_ID, 0);
    }

    private void initViews() {
        searchListView = findViewById(R.id.search_list_view);
        searchEt = findViewById(R.id.search_et);
        musicTab = findViewById(R.id.music_tab);
        cancelSearchBtn = findViewById(R.id.cancel_search_btn);
        pager = findViewById(R.id.view_pager);
        backBtn = findViewById(R.id.back_btn);
        searchLayout = findViewById(R.id.view_item_search_lay);
        recentListView = new ChooseSongListView(this);
        sharedListView = new ChooseSongListView(this);
        favoriteListView = new ChooseSongListView(this);
        favoriteListView.setCanLoadMore(true, ChooseSongListView.FAVORITE_MUSIC);
        favoriteListView.setCanRefresh(true, ChooseSongListView.FAVORITE_MUSIC);
        PlayingAdapter.OnClickMusicListener onClickMusicListener = musicId -> {
            //列表item过多的话可以考虑根据musicId刷新单个item
            recentListView.notifyFavoriteChanged();
            sharedListView.notifyFavoriteChanged();
            favoriteListView.favoriteMusicChange(musicId);
        };
        recentListView.init(rid, false, PlayingAdapter.RECENT_PAGE, onClickMusicListener);
        searchListView.init(rid, false, PlayingAdapter.SEARCH_PAGE, null);
        sharedListView.init(rid, true, PlayingAdapter.SHARE_PAGE, onClickMusicListener);
        favoriteListView.init(rid, false, PlayingAdapter.FAVORITE_PAGE, onClickMusicListener);
        updateActionBarSpace();

        List<ChooseSongListView> listViews = Arrays.asList(favoriteListView, sharedListView, recentListView);
        pager.setAdapter(new SimplePagerAdapter<>(listViews));
        pager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                selectTab(position);
            }
        });
        mediator = new TabLayoutMediator(musicTab, pager, (tab, position) -> {
            tab.setText(titleList.get(position));
        });
        //要执行这一句才是真正将两者绑定起来
        mediator.attach();
        selectTab(1);
        showPager();
    }

    private void selectTab(int position) {
        TabLayout.Tab tab = musicTab.getTabAt(position);
        if (tab != null) {
            tab.select();
        }
    }

    private void updateData() {
        sharedListView.updateShared();
        recentListView.updateRecent();
        favoriteListView.refreshFavorite();
    }

    private void updateActionBarSpace() {
        if (StatusBarUtil.isSupportFullScreen()) {
            View space = findViewById(R.id.action_bar_space);
            space.getLayoutParams().height = ScreenUtil.getStatusBarHeight();
        }
    }

    private void initEvents() {
        searchEt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showSearch();
            }
        });
        searchEt.addTextChangedListener(new SimpleTextWatcher() {
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (StringUtil.isValid(s.toString()) && StringUtil.getCharNumber(s, ' ') < 4) {
                    search(s.toString());
                }
            }
        });
        cancelSearchBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPager();
            }
        });
        backBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        searchEt.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                search(v.getText().toString());
                IMMHelper.hideSoftInput(searchEt.getContext(), searchEt.getWindowToken());
                return true;
            }
        });
    }

    private void showPager() {
        searching = false;
        searchEt.setText("");
        searchEt.setCursorVisible(false);
        searchEt.setFocusable(false);
        searchEt.setFocusableInTouchMode(false);
        LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) searchLayout.getLayoutParams();
        lp.setMarginStart(0);
        lp.setMarginEnd(ScreenUtil.dip2px(10));
        cancelSearchBtn.requestLayout();
        cancelSearchBtn.setVisibility(View.GONE);
        backBtn.setVisibility(View.VISIBLE);
        searchListView.setVisibility(View.GONE);
        IMMHelper.hideSoftInput(searchEt.getContext(), searchEt.getWindowToken());
    }

    private void showSearch() {
        searching = true;
        LinearLayout.LayoutParams lp = (LinearLayout.LayoutParams) searchLayout.getLayoutParams();
        lp.setMarginStart(ScreenUtil.dip2px(10));
        lp.setMarginEnd(0);
        searchEt.setFocusable(true);
        searchEt.setFocusableInTouchMode(true);
        searchEt.setCursorVisible(true);
        searchEt.requestLayout();
        cancelSearchBtn.setVisibility(View.VISIBLE);
        backBtn.setVisibility(View.GONE);
        searchListView.setVisibility(View.VISIBLE);
        IMMHelper.showSoftInput(searchEt);
        if (TextUtils.isEmpty(searchEt.getText().toString())) {
            showNothingSearch();
        }
    }

    private void showNothingSearch() {
        searchListView.setBackgroundColor(ContextCompat.getColor(this, R.color.color_transparent50));
        searchListView.clear();
        searchListView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPager();
            }
        });
    }

    private String pendingSearchKey = "";
    private boolean searchRequesting = false;

    private void checkPendingSearch() {
        if (!TextUtils.isEmpty(pendingSearchKey)) {
            String key = pendingSearchKey;
            pendingSearchKey = "";
            search(key);
        }
    }

    private void search(String key) {
        if (searching) {
            if (TextUtils.isEmpty(key)) {
                showNothingSearch();
            } else {
                if (searchRequesting) {
                    pendingSearchKey = key;
                } else {
                    searchRequesting = true;
                    VoiceMusicSender.search(rid, SEARCH_TYPE_SEARCH, key, 0, new LifeSeqCallback(this) {
                        @Override
                        public void onSuccess(RspHeadInfo head) {
                            searchRequesting = false;
                            if (head.message instanceof MusicPackets.MSSearchRsp) {
                                searchListView.setBackgroundColor(Color.WHITE);
                                searchListView.setOnClickListener(null);
                                MusicPackets.MSSearchRsp rsp = (MusicPackets.MSSearchRsp) head.message;
                                if (rsp.getMusicListCount() == 0) {
                                    searchListView.showEmpty(true);
                                } else {
                                    searchListView.updateContent(VoiceMusicInfo.parse(rsp.getMusicListList()));
                                    searchListView.showBottomNone();
                                }
                            }
                            checkPendingSearch();
                        }

                        @Override
                        public void onFail(RspHeadInfo head) {
                            ToastUtil.show(head.desc);
                            searchListView.setBackgroundColor(Color.WHITE);
                            searchListView.showEmpty(true);
                            searchRequesting = false;
                        }
                    });
                }
            }
        }
    }

    @Override
    public void onBackPressed() {
        if (searching) {
            showPager();
        } else {
            super.onBackPressed();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onExitRoom(ExitRoomEvent event) {
        finish();
    }

    @Override
    public void onChanged() {
        recentListView.updateRecent();
    }

    @Override
    protected void onResume() {
        super.onResume();
        RecentChooseManager.get().registerObserver(this);
    }

    @Override
    protected void clearMemory() {
        super.clearMemory();
        EventDispatcher.unregisterEventObserver(this);
        RecentChooseManager.get().unregisterObserver(this);
    }

    @Override
    public int supportFloatView() {
        return 0;
    }
}
