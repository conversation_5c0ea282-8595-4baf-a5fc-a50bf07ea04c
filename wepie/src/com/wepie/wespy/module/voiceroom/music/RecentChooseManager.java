package com.wepie.wespy.module.voiceroom.music;

import com.huiwan.base.util.lru.SimpleLruHelper;
import com.huiwan.store.file.FileStoreUtil;
import com.wepie.wespy.model.entity.voiceroom.VoiceMusicInfo;
import com.huiwan.user.LoginHelper;
import com.huiwan.store.file.FileCacheName;
import com.huiwan.base.util.FileUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * date 2019-05-14
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class RecentChooseManager {
    private static final RecentChooseManager INSTANCE = new RecentChooseManager();
    private RecentChooseManager() { }

    private int uid = -1;
    private SimpleLruHelper<VoiceMusicInfo, Object> recentPlay = new SimpleLruHelper<>(8, 0.75f);
    private Observer observer;

    public static RecentChooseManager get() {
        if (INSTANCE.uid != LoginHelper.getLoginUid()) {
            INSTANCE.uid = LoginHelper.getLoginUid();
            INSTANCE.recentPlay.setMaxSize(20);
            INSTANCE.recentPlay.clear();
            INSTANCE.initFromLocal();
        }
        return INSTANCE;
    }

    void addChooseInfo(VoiceMusicInfo musicInfo) {
        // op-put will not replace the key, the music info may change, remove it first
        recentPlay.remove(musicInfo);
        recentPlay.put(musicInfo, null);
        saveLocal();
        if (observer != null) {
            observer.onChanged();
        }
    }

    void registerObserver(Observer observer) {
        this.observer = observer;
    }

    void unregisterObserver(Observer observer) {
        this.observer = null;
    }

    List<VoiceMusicInfo> getRecentPlay() {
        List<VoiceMusicInfo> recent = new ArrayList<>(recentPlay.keySet());
        Collections.reverse(recent);
        return recent;
    }

    private void saveLocal() {
        // linked hash map key set 老的在前
        FileUtil.writeEntityAsync(getFileName(), recentPlay.keySet());
    }

    private void initFromLocal() {
        List<VoiceMusicInfo> musicList = FileUtil.loadEntityListFromFile(getFileName(), VoiceMusicInfo.class);
        if (musicList != null && !musicList.isEmpty()) {
            for (VoiceMusicInfo musicInfo:musicList) {
                recentPlay.put(musicInfo, null);
            }
        }
    }

    private String getFileName() {
        return FileStoreUtil.getUserFileName(FileCacheName.USER_RECENT_CHOOSE_MUSIC);
    }

    interface Observer{
        void onChanged();
    }
}
