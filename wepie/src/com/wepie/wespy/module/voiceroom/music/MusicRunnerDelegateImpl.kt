package com.wepie.wespy.module.voiceroom.music

import com.huiwan.base.ktx.dp
import com.wepie.wespy.model.event.RoomInfoUpdateEvent
import com.wepie.wespy.model.event.voice.MusicWidgetChange
import com.wepie.wespy.module.voiceroom.IPluginTool
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IMainPlugin
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IMusicRunner
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class MusicRunnerDelegateImpl(
    private val pluginTool: IPluginTool<IMusicRunner>
) : IMusicRunner by pluginTool.proxy() {

    private var marginStart: Int = 0
    private var marginEnd: Int = 0
    private var marginTop: Int = 134.dp
    private var marginBottom: Int = 0

    init {
        val plugin = mainPlugin
        initEventBus()
        initOnUpdateRoomInfo(plugin)
    }

    private fun initOnUpdateRoomInfo(plugin: IMainPlugin) {
        val listener = IMainPlugin.OnVoiceRoomInfoChangeListener { voiceRoomInfo, _ ->
            val canLoad = voiceRoomInfo.inMusic
            pluginTool.loadView(canLoad)
            pluginTool.proxy().setMargin(marginStart, marginTop, marginEnd, marginBottom)
            if (canLoad) {
                pluginTool.proxy().onUpdateRoomInfo(voiceRoomInfo)
            }
        }
        plugin.addOnVoiceRoomInfoChangeListener(pluginTool, listener)
    }

    override fun setMargin(start: Int, top: Int, end: Int, bottom: Int) {
        this.marginStart = start
        this.marginTop = top
        this.marginEnd = end
        this.marginBottom = bottom
        pluginTool.proxy().setMargin(start, top, end, bottom)
    }

    private fun initEventBus() {
        EventBus.getDefault().register(this)
        pluginTool.onDestroy {
            EventBus.getDefault().unregister(this)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMusicWidgetChange(event: MusicWidgetChange) {
        mainPlugin.updateRoomInfo(
            mainPlugin.roomInfo,
            RoomInfoUpdateEvent(RoomInfoUpdateEvent.F_WIDGET_MUSIC_RUNNER)
        )
    }
}