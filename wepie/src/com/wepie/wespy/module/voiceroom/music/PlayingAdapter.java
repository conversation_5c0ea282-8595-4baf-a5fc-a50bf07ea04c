package com.wepie.wespy.module.voiceroom.music;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.anim.SVGAUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LoginHelper;
import com.opensource.svgaplayer.SVGADrawable;
import com.opensource.svgaplayer.SVGAImageView;
import com.opensource.svgaplayer.SVGAParser;
import com.opensource.svgaplayer.SVGAVideoEntity;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackSource;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.model.entity.voiceroom.VoiceMusicInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.http.api.VoiceMusicApi;
import com.wepie.wespy.net.tcp.sender.VoiceMusicSender;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * date 2019-05-09
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class PlayingAdapter extends RecyclerView.Adapter<PlayingAdapter.Holder> {
    private final List<VoiceMusicInfo> musicInfoList = new ArrayList<>();
    private VoiceMusicInfo.PlayInfo curPlayInfo = new VoiceMusicInfo.PlayInfo();
    private final int fromPage;
    private final int rid;
    private int playMode = 0;
    private int curPlayPosition;
    private boolean selfAdmin = false;
    private final OnClickMusicListener onClickMusicListener;

    public static final int PLAY_DIALOG = 1;
    public static final int FAVORITE_PAGE = 2;
    public static final int SHARE_PAGE = 3;
    public static final int RECENT_PAGE = 4;
    public static final int SEARCH_PAGE = 5;

    public PlayingAdapter(int rid, int fromPage, OnClickMusicListener onClickMusicListener) {
        this.fromPage = fromPage;
        this.rid = rid;
        this.onClickMusicListener = onClickMusicListener;
    }

    public void clear() {
        this.musicInfoList.clear();
        notifyDataSetChanged();
    }

    public List<VoiceMusicInfo> getMusicInfoList() {
        return musicInfoList;
    }

    public int getPlayMode() {
        return playMode;
    }

    public void setPlayMode(int playMode) {
        this.playMode = playMode;
        notifyDataSetChanged();
    }

    public int getCurPlayPosition() {
        return curPlayPosition;
    }

    public void refresh(List<VoiceMusicInfo> musicInfoList, VoiceMusicInfo.PlayInfo curPlayInfo, boolean selfAdmin) {
        this.musicInfoList.clear();
        this.musicInfoList.addAll(musicInfoList);
        this.curPlayInfo = curPlayInfo;
        this.selfAdmin = selfAdmin;
        notifyDataSetChanged();
    }

    public void refresh(List<VoiceMusicInfo> musicInfoList) {
        this.musicInfoList.clear();
        this.musicInfoList.addAll(musicInfoList);
        notifyDataSetChanged();
    }

    public void addList(List<VoiceMusicInfo> musicInfoList) {
        this.musicInfoList.addAll(musicInfoList);
        notifyDataSetChanged();
    }

    public void deleteMusic(int index) {
        this.musicInfoList.remove(index);
        notifyDataSetChanged();
    }

    void refreshCurPlaying(VoiceMusicInfo.PlayInfo curPlayInfo) {
        notifyItemChanged(0, new CurPlaying(curPlayInfo));
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup viewGroup, int layoutType) {
        View v = LayoutInflater.from(viewGroup.getContext()).inflate(layoutType, viewGroup, false);
        if (layoutType == R.layout.room_music_play_list_item) {
            return new ItemHolder(v, fromPage, rid);
        } else {
            return new EmptyHolder(v);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position, @NonNull List<Object> payloads) {
        if (payloads.isEmpty()) {
            super.onBindViewHolder(holder, position, payloads);
        } else {
            if (payloads.get(0) instanceof CurPlaying && holder instanceof ItemHolder) {
                CurPlaying status = (CurPlaying) payloads.get(0);
                ((ItemHolder) holder).updateCurPlaying(status.curPlay);
            }
        }
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int i) {
        if (holder instanceof ItemHolder) {
            int pos = holder.getBindingAdapterPosition();
            boolean fullLine = pos == musicInfoList.size() - 1;
            boolean showNext = playMode != VoiceMusicInfo.LOOP_RANDOM;
            if (musicInfoList.get(pos).musicId == curPlayInfo.musicInfo.musicId) {
                curPlayPosition = pos;
            }
            ((ItemHolder) holder).bind(musicInfoList.get(pos), curPlayInfo.musicInfo.musicId, fullLine, curPlayInfo.isPlaying, selfAdmin, showNext);
            ((ItemHolder) holder).setOnClickMusicListener(onClickMusicListener);
        } else if (holder instanceof EmptyHolder) {
            ((EmptyHolder) holder).bind(musicInfoList.size() > 1 && selfAdmin);
        }
    }

    @Override
    public int getItemViewType(int position) {
        return (fromPage == PLAY_DIALOG && musicInfoList.size() == position) ? R.layout.room_music_play_list_item_empty : R.layout.room_music_play_list_item;
    }

    @Override
    public int getItemCount() {
        return fromPage != PLAY_DIALOG ? musicInfoList.size() : musicInfoList.size() + 1;
    }

    public static class Holder extends RecyclerView.ViewHolder {
        Holder(@NonNull View itemView) {
            super(itemView);
        }
    }

    static class EmptyHolder extends Holder {
        LinearLayout delete_ll;

        EmptyHolder(@NonNull View itemView) {
            super(itemView);
            delete_ll = itemView.findViewById(R.id.delete_ll);
            delete_ll.setOnClickListener(v ->
                    DialogUtil.showDoubleBtnDialog(itemView.getContext(), ResUtil.getStr(R.string.voice_room_music_list_clear_tip), "", "", "",
                            () -> VoiceMusicManager.get().clearPlayList()));
        }

        public void bind(boolean showDelete) {
            if (showDelete) {
                delete_ll.setVisibility(View.VISIBLE);
            } else {
                delete_ll.setVisibility(View.GONE);
            }
        }
    }

    static class CurPlaying {
        VoiceMusicInfo.PlayInfo curPlay;

        CurPlaying(VoiceMusicInfo.PlayInfo curPlay) {
            this.curPlay = curPlay;
        }
    }

    static class ItemHolder extends Holder implements View.OnClickListener {
        ImageView headIv;
        TextView songTv;
        TextView tipTv;
        SVGAImageView playingIv;
        View delBtn;
        View nextPalyIv;
        View chooseBtn;
        View bottomLine;
        View bottomLineFull;
        ImageView favoriteIv;
        View playingLine;
        View rootLay;

        private final int formPage;
        private final int rid;
        private boolean canClickPlay;

        private VoiceMusicInfo musicInfo;
        private OnClickMusicListener onClickMusicListener;

        /**
         *
         * @param view view
         * @param formPage 来自哪个页面
         * @param rid 房间号
         */
        ItemHolder(View view, int formPage, int rid) {
            super(view);
            this.rid = rid;
            this.formPage = formPage;
            rootLay = view;
            headIv = view.findViewById(R.id.head_iv);
            songTv = view.findViewById(R.id.song_tv);
            tipTv = view.findViewById(R.id.tip_tv);
            playingIv = view.findViewById(R.id.song_playing_iv);
            delBtn = view.findViewById(R.id.delete_iv);
            nextPalyIv = view.findViewById(R.id.next_playing_iv);
            chooseBtn = view.findViewById(R.id.choose_tv);
            bottomLine = view.findViewById(R.id.bottom_line);
            bottomLineFull = view.findViewById(R.id.bottom_line_w);
            favoriteIv = view.findViewById(R.id.favorite_iv);
            playingLine = view.findViewById(R.id.play_music_line);
            playingIv.setVisibility(View.GONE);
            playingLine.setVisibility(View.GONE);
            boolean canChoose = formPage != PLAY_DIALOG;
            chooseBtn.setVisibility(canChoose ? View.VISIBLE : View.GONE);
            headIv.setVisibility(canChoose ? View.GONE : View.VISIBLE);
            nextPalyIv.setVisibility(canChoose ? View.GONE : View.VISIBLE);
            delBtn.setVisibility(canChoose ? View.GONE : View.VISIBLE);
            nextPalyIv.setOnClickListener(this);
            delBtn.setOnClickListener(this);
            headIv.setOnClickListener(this);
            chooseBtn.setOnClickListener(this);
            favoriteIv.setOnClickListener(this);
            rootLay.setOnClickListener(this);
        }

        public void setOnClickMusicListener(OnClickMusicListener onClickMusicListener) {
            this.onClickMusicListener = onClickMusicListener;
        }

        void bind(VoiceMusicInfo musicInfo, int playMusicId, boolean fullLine, boolean playing, boolean selfAdmin, boolean showNext) {
            this.musicInfo = musicInfo;
            if (formPage == PLAY_DIALOG) {
                if (playMusicId == musicInfo.musicId) {
                    updatePlayingDrawable(playing);
                } else {
                    hidePlayingView();
                }
                HeadImageLoader.loadCircleHeadImage(musicInfo.selectUserHeader, headIv);
                if (selfAdmin) {
                    nextPalyIv.setVisibility(playMusicId != musicInfo.musicId && showNext ? View.VISIBLE : View.GONE);
                    delBtn.setVisibility(View.VISIBLE);
                } else {
                    nextPalyIv.setVisibility(View.GONE);
                    delBtn.setVisibility(LoginHelper.getLoginUid() == musicInfo.selectUserId ? View.VISIBLE : View.GONE);
                }
            }
            favoriteIv.setImageResource(isFavorite(musicInfo) ? R.drawable.music_like_icon : R.drawable.music_dislike_icon);
            updateTip(musicInfo);
            songTv.setText(musicInfo.musicName);
            bottomLineFull.setVisibility(fullLine ? View.VISIBLE : View.GONE);
            bottomLine.setVisibility(fullLine ? View.GONE : View.VISIBLE);
            canClickPlay = selfAdmin && playMusicId != musicInfo.musicId && formPage == PLAY_DIALOG;
        }

        private void hidePlayingView() {
            playingIv.setVisibility(View.GONE);
            playingLine.setVisibility(View.GONE);
        }

        private void updateTip(VoiceMusicInfo musicInfo) {
            if (TextUtils.isEmpty(musicInfo.uploaderName)) {
                tipTv.setText(musicInfo.singer);
            } else {
                tipTv.setText(ResUtil.getStr(R.string.voice_room_music_info, musicInfo.singer, musicInfo.uploaderName));
            }
        }

        void updateCurPlaying(VoiceMusicInfo.PlayInfo curPlay) {
            if (this.musicInfo.listId == curPlay.musicInfo.listId) {
                this.musicInfo = curPlay.musicInfo;
                updatePlayingDrawable(curPlay.isPlaying);
            } else {
                hidePlayingView();
            }
        }

        private void updatePlayingDrawable(final boolean playing) {
            playingIv.setVisibility(View.VISIBLE);
            playingLine.setVisibility(View.VISIBLE);
            if (!(playingIv.getDrawable() instanceof SVGADrawable)) {
                SVGAUtil.getAssetSvgaAnim("svga/music_playing.svga", new SVGAParser.ParseCompletion() {
                    @Override
                    public void onComplete(@NonNull SVGAVideoEntity svgaVideoEntity) {
                        playingIv.setImageDrawable(new SVGADrawable(svgaVideoEntity));
                        updatePlaying(playing);
                    }

                    @Override
                    public void onError() {
                    }
                });
            } else {
                updatePlaying(playing);
            }
        }

        private void updatePlaying(boolean playing) {
            SVGADrawable drawable = (SVGADrawable) playingIv.getDrawable();
            if (playing) {
                playingIv.stepToFrame(drawable.getCurrentFrame(), true);
            } else {
                playingIv.pauseAnimation();
            }
        }

        @Override
        public void onClick(final View v) {
            if (musicInfo == null) {
                return;
            }
            if (v == chooseBtn) {
                VoiceMusicSender.choose(rid, musicInfo.toProto(), new LifeSeqCallback(v) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        RecentChooseManager.get().addChooseInfo(musicInfo);
                        ToastUtil.show(R.string.voice_room_music_order_success);
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                });
                VoiceMusicHelper.downloadIfNeed(musicInfo);
            } else if (v == delBtn) {
                DialogBuild.newBuilder(v.getContext()).setSingleBtn(false).setContent(R.string.voice_room_music_del_tip).setDialogCallback(
                        () -> VoiceMusicSender.delete(rid, musicInfo.listId, new LifeSeqCallback(v) {
                            @Override
                            public void onSuccess(RspHeadInfo head) {
                            }

                            @Override
                            public void onFail(RspHeadInfo head) {
                                ToastUtil.show(head.desc);
                            }
                        })).show();

            } else if (v == nextPalyIv) {
                VoiceMusicSender.stick(rid, musicInfo.listId, new LifeSeqCallback(v) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        EventDispatcher.postPlayNextEvent();
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                });
            } else if (v == headIv) {
                JumpUtil.enterUserInfoDetailActivity(v.getContext(), musicInfo.selectUserId, TrackSource.VOICE_MAKE_FRIEDNS);
            } else if (v == favoriteIv) {
                if (isFavorite(musicInfo)) {
                    if (formPage == FAVORITE_PAGE) {
                        DialogUtil.showDoubleBtnDialog(itemView.getContext(), ResUtil.getStr(R.string.voice_room_music_list_remove_favorite_tip), "",
                                "", "", this::deleteFavorite);
                    } else {
                        deleteFavorite();
                    }

                } else {
                    VoiceMusicApi.addFavoriteMusic(LoginHelper.getLoginUid(), musicInfo.musicId, new LifeDataCallback<Object>(favoriteIv) {
                        @Override
                        public void onSuccess(Result<Object> result) {
                            VoiceMusicManager.getInstance().addFavorite(musicInfo.musicId);
                            ToastUtil.show(ResUtil.getStr(R.string.voice_room_music_list_favorite_success_toast));
                            if (onClickMusicListener != null) {
                                onClickMusicListener.onFavoriteMusic(musicInfo.musicId);
                            }
                            favoriteIv.setImageResource(R.drawable.music_like_icon);
                            Map<String, Object> map = new HashMap<>();
                            map.put("song_id", musicInfo.musicId);
                            ShenceEvent.appClick(TrackScreenName.VOICE_MUSIC_CLI, ResUtil.getStr(R.string.voice_room_music_list_favorite_report_text), map);
                        }

                        @Override
                        public void onFail(int code, String msg) {
                            ToastUtil.show(msg);
                        }
                    });
                }
            } else if (v == rootLay) {
                if (canClickPlay) {
                    VoiceMusicManager.get().playMusic(musicInfo.listId);
                }
            }
        }

        private void deleteFavorite() {
            VoiceMusicApi.delFavoriteMusic(LoginHelper.getLoginUid(), musicInfo.musicId, new LifeDataCallback<Object>(favoriteIv) {
                @Override
                public void onSuccess(Result<Object> result) {
                    VoiceMusicManager.getInstance().removeFavorite(musicInfo.musicId);
                    ToastUtil.show(ResUtil.getStr(R.string.voice_room_music_list_favorite_cancle_success_toast));
                    if (onClickMusicListener != null) {
                        onClickMusicListener.onFavoriteMusic(musicInfo.musicId);
                    }
                    favoriteIv.setImageResource(R.drawable.music_dislike_icon);
                }

                @Override
                public void onFail(int code, String msg) {
                    ToastUtil.show(msg);
                }
            });
        }
    }

    private static boolean isFavorite(VoiceMusicInfo musicInfo) {
        return VoiceMusicManager.getInstance().isFavorite(musicInfo.musicId);
    }

    public interface OnClickMusicListener {
        void onFavoriteMusic(int musicId);
    }
}
