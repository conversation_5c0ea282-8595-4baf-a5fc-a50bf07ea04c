package com.wepie.wespy.module.voiceroom.music;

import android.content.Context;
import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.user.LoginHelper;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseDialogFragment;
import com.wepie.wespy.model.entity.voiceroom.VoiceMusicInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.MusicModeChangeEvent;
import com.wepie.wespy.model.event.PlayNextEvent;
import com.wepie.wespy.model.event.RoomInfoUpdateEvent;
import com.wepie.wespy.model.event.voice.MusicCurPlayChange;
import com.wepie.wespy.model.event.voice.MusicPlayInfoChange;
import com.wepie.wespy.model.event.voice.MusicWidgetChange;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Collections;
import java.util.List;

/**
 * date 2019-05-09
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class VoiceMusicPlayingDialog extends BaseDialogFragment implements View.OnClickListener, VoiceMusicManager.PlayTickObserver {
    private static final String KEY_RID = "rid";
    private TextView playingNumTv;
    private View closeBtn;
    private RecyclerView rv;
    private ImageView playBtn;
    private ImageView nextBtn;
    private ImageView loopModeIv;
    private ImageView volumeBtn;
    private ImageView lycBtn;
    private ProgressBar progressBar;
    private TextView curTv;
    private TextView totalTv;
    private TextView chooseBtn;
    private PlayingAdapter adapter;
    private ViewGroup emptyLay;
    private PopupWindow volumePop;

    private int rid;
    private boolean selfAdmin = false;
    private boolean selfInSit = false;
    private boolean speakerOff = true;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        View v = inflater.inflate(R.layout.room_music_playing, container, false);
        rid = getArguments().getInt(KEY_RID, 0);
        initViews(v);
        initEvents();
        updateData();
        EventDispatcher.registerEventObserver(this);
        VoiceMusicManager.get().registerTickObserver(this);
        return v;
    }

    private void initViews(View v) {
        playingNumTv = v.findViewById(R.id.playing_num_tv);
        closeBtn = v.findViewById(R.id.close_btn);
        loopModeIv = v.findViewById(R.id.music_loop_mode_iv);
        rv = v.findViewById(R.id.play_list);
        playBtn = v.findViewById(R.id.play_btn);
        nextBtn = v.findViewById(R.id.next_btn);
        volumeBtn = v.findViewById(R.id.volume_btn);
        lycBtn = v.findViewById(R.id.lyc_btn);
        progressBar = v.findViewById(R.id.playing_progress);
        curTv = v.findViewById(R.id.cur_time_tv);
        totalTv = v.findViewById(R.id.total_time_tv);
        chooseBtn = v.findViewById(R.id.choose_song_btn);
        emptyLay = v.findViewById(R.id.empty_lay);
        adapter = new PlayingAdapter(rid, PlayingAdapter.PLAY_DIALOG, null);
        rv.setLayoutManager(new LinearLayoutManager(v.getContext()));
        rv.setAdapter(adapter);
    }


    private void updateData() {
        updateInfo();
        VoiceMusicInfo.PlayInfo playInfo = VoiceMusicManager.get().getCurPlayInfo();
        final List<VoiceMusicInfo> list = VoiceMusicManager.get().getMusicInfoList();
        updateCurPlay(playInfo);
        updateListInfo(list, playInfo);
        VoiceMusicManager.get().refreshCurPlaying(rid, new VoiceMusicManager.CurPlayCallback() {
            @Nullable
            @Override
            public ILife getLife() {
                return ILifeUtil.toLife(getViewLifecycleOwner());
            }

            @Override
            public void onRefresh(@Nullable final VoiceMusicInfo.PlayInfo curPlayInfo) {
                if (curPlayInfo != null) {
                    updateCurPlay(curPlayInfo);
                    VoiceMusicManager.get().refreshPlayingList(rid, new VoiceMusicManager.MusicListCallback() {
                        @Nullable
                        @Override
                        public ILife getLife() {
                            return ILifeUtil.toLife(getViewLifecycleOwner());
                        }

                        @Override
                        public void onRefresh(@Nullable List<VoiceMusicInfo> musicInfoList, int playMode) {
                            if (musicInfoList != null) {
                                updateListInfo(musicInfoList, curPlayInfo);
                                updatePlayMode(playMode);
                            }
                        }
                    });
                }
            }
        });
        updateProgress();
    }

    private void updateInfo() {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo(rid);
        selfAdmin = roomInfo.isSelfAdmin() || roomInfo.isSelfOwner();
        selfInSit = roomInfo.isInSeat(LoginHelper.getLoginUid());
        speakerOff = VoiceRoomService.getInstance().isMuteRoom(rid);
    }

    private void updateListInfo(List<VoiceMusicInfo> list, VoiceMusicInfo.PlayInfo playInfo) {
        Collections.sort(list);
        updateCurPlay(playInfo);
        adapter.refresh(list, playInfo, selfAdmin);
        int count = list.size();
        String text = ResUtil.getQuantityStr(R.plurals.voice_room_music_ordered, count, count);
        String countStr = String.valueOf(count);
        SpannableStringBuilder ssb = new SpannableStringBuilder(text);
        int start = text.indexOf(countStr);
        if (start >= 0) {
            ssb.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.color_accent_ex)), start, start + countStr.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        playingNumTv.setText(ssb);
        emptyLay.setVisibility(list.isEmpty() ? View.VISIBLE : View.GONE);
    }


    private void updateCurPlay(VoiceMusicInfo.PlayInfo playInfo) {
        if (playInfo.musicInfo.listId < 1 && VoiceMusicManager.get().getMusicInfoList().size() == 0) {
            disableAllCtrl();
            return;
        }

        playBtn.setImageResource(playInfo.isPlaying ? R.drawable.voice_music_pause : R.drawable.voice_music_play);
        playBtn.setEnabled((playInfo.selfChoose() || selfAdmin) && playInfo.musicInfo.listId > 0);
        nextBtn.setEnabled(playInfo.selfChoose() || selfAdmin);
        loopModeIv.setEnabled(selfAdmin);
        lycBtn.setEnabled(true);
        lycBtn.setSelected(PrefUserUtil.getInstance().getBoolean(PrefUserUtil.MUSIC_LRC_OPEN, false));
        volumeBtn.setEnabled(playInfo.selfChoose() && !speakerOff);
        hideVolumePop(!playInfo.selfChoose());
    }

    private void disableAllCtrl() {
        playBtn.setEnabled(false);
        nextBtn.setEnabled(false);
        volumeBtn.setEnabled(false);
        lycBtn.setEnabled(false);
        loopModeIv.setEnabled(false);
        playBtn.setImageResource(R.drawable.voice_music_play);
    }

    @Override
    public void onDestroyView() {
        VoiceMusicManager.get().unregisterTickObserver(this);
        EventDispatcher.unregisterEventObserver(this);
        super.onDestroyView();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMusicWidgetChange(MusicWidgetChange event) {
        if (event.rid == rid) {
            if (!event.open) {
                dismissAllowingStateLoss();
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayingMusicChange(MusicPlayInfoChange event) {
        if (event.rid == rid) {
            updateListInfo(VoiceMusicManager.get().getMusicInfoList(), VoiceMusicManager.get().getCurPlayInfo());
            updateProgress();
            updatePlayMode(VoiceMusicManager.get().getPlayMode());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onCurPlayingMusicChange(MusicCurPlayChange event) {
        if (event.rid == rid) {
            VoiceMusicInfo.PlayInfo curPlay = VoiceMusicManager.get().getCurPlayInfo();
            adapter.refreshCurPlaying(curPlay);
            updateCurPlay(curPlay);
            updateProgress();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRoomInfoChange(RoomInfoUpdateEvent event) {
        updateInfo();
        updateListInfo(VoiceMusicManager.get().getMusicInfoList(), VoiceMusicManager.get().getCurPlayInfo());
        updateProgress();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayNext(PlayNextEvent event) {
        rv.smoothScrollToPosition(adapter.getCurPlayPosition());
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onModeChange(MusicModeChangeEvent event) {
        if (event.rid == rid) {
            updatePlayMode(VoiceMusicManager.get().getPlayMode());
        }
    }

    @Override
    public void onTick(long cur, long total) {
        updateProgress(cur, total);
        if (total == 0) {
            updateListInfo(VoiceMusicManager.get().getMusicInfoList(), VoiceMusicManager.get().getCurPlayInfo());
        }
    }

    private void updateProgress() {
        updateProgress(VoiceMusicManager.get().getTickCurMs(), VoiceMusicManager.get().getTickTotalMs());
    }

    private void updateProgress(long cur, long total) {
        totalTv.setText(TimeUtil.formTotalTime((int) total / 1000));
        curTv.setText(TimeUtil.formTotalTime((int) cur / 1000));
        progressBar.setMax((int) total / 1000);
        progressBar.setProgress((int) cur / 1000);
    }


    @Override
    public void onClick(View v) {
        if (v == closeBtn) {
            dismissAllowingStateLoss();
        } else if (v == playBtn) {
            checkChangePlayStatus();
        } else if (v == nextBtn) {
            checkPlayNext();
        } else if (v == volumeBtn) {
            showVolumePop();
        } else if (v == lycBtn) {
            changeLycStat();
        } else if (v == chooseBtn) {
            if (selfInSit) {
                gotoChooseSong(v.getContext());
            } else {
                ToastUtil.show(R.string.cannot_order_while_not_in_seat);
            }
        } else if (v == loopModeIv) {
            VoiceMusicManager.get().changePlayMode();
        }
    }

    private void updatePlayMode(int playMode) {
        if (playMode == VoiceMusicInfo.LOOP_LIST) {
            loopModeIv.setImageResource(R.drawable.voice_music_loop_list);
        } else if (playMode == VoiceMusicInfo.LOOP_RANDOM) {
            loopModeIv.setImageResource(R.drawable.voice_music_loop_random);
        } else if (playMode == VoiceMusicInfo.LOOP_SINGLE) {
            loopModeIv.setImageResource(R.drawable.voice_music_loop_single);
        } else if (playMode == VoiceMusicInfo.LOOP_SEQUENCE) {
            loopModeIv.setImageResource(R.drawable.voice_music_loop_sequence);
        }
        adapter.setPlayMode(playMode);
    }

    private void checkChangePlayStatus() {
        if (selfAdmin || VoiceMusicManager.get().getCurPlayInfo().selfChoose()) {
            VoiceMusicManager.get().checkChangePlayStatus(rid);
        }
    }

    private void checkPlayNext() {
        VoiceMusicManager.get().playNext();
    }

    private void showVolumePop() {
        volumePop = MusicVolumePop.showPop(volumeBtn);
    }

    private void hideVolumePop(boolean needHide) {
        if (needHide && volumePop != null && volumePop.isShowing()) {
            volumePop.dismiss();
        }
    }

    private void changeLycStat() {
        boolean open = PrefUserUtil.getInstance().getBoolean(PrefUserUtil.MUSIC_LRC_OPEN, false);
        PrefUserUtil.getInstance().setBoolean(PrefUserUtil.MUSIC_LRC_OPEN, !open);
        lycBtn.setSelected(!open);
        EventDispatcher.postLrcChangeEvent();
    }

    private void gotoChooseSong(Context context) {
        JumpUtil.gotoVoiceRoomChooseSongActivity(context, rid);
    }

    private void initEvents() {
        closeBtn.setOnClickListener(this);
        playBtn.setOnClickListener(this);
        loopModeIv.setOnClickListener(this);
        nextBtn.setOnClickListener(this);
        volumeBtn.setOnClickListener(this);
        lycBtn.setOnClickListener(this);
        chooseBtn.setOnClickListener(this);
    }

    public static void show(Context context, int rid) {
        VoiceMusicPlayingDialog dialog = new VoiceMusicPlayingDialog();
        dialog.initBottom();
        dialog.initDialogSize(ScreenUtil.getScreenWidth(), ScreenUtil.getScreenHeight() - ScreenUtil.dip2px(72));
        dialog.setWindowAnim(R.style.bottom_dialog_anim);
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_RID, rid);
        dialog.show(context, bundle);
    }

    @Override
    public void onResume() {
        super.onResume();
        adapter.notifyDataSetChanged();
    }
}
