package com.wepie.wespy.module.voiceroom.music;

import static com.wepie.wespy.net.tcp.sender.VoiceMusicSender.SEARCH_TYPE_SHARED;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.util.ToastUtil;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LoginHelper;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceMusicInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceMusicList;
import com.wepie.wespy.net.http.api.VoiceMusicApi;
import com.wepie.wespy.net.tcp.packet.MusicPackets;
import com.wepie.wespy.net.tcp.sender.VoiceMusicSender;

import java.util.List;

/**
 * date 2019-05-16
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class ChooseSongListView extends RelativeLayout {
    private RecyclerView rv;
    private ViewGroup emptyLay;
    private ConstraintLayout musicEmptyLay;
    private View emptyTv;
    private View refreshBtn;
    private ViewGroup bottomNoneLay;
    private SmartRefreshLayout refreshLayout;
    private LinearLayoutManager layoutManager;

    private PlayingAdapter adapter;

    private int rid;
    private int offset;
    public static final int FAVORITE_MUSIC = 1;

    public ChooseSongListView(Context context) {
        this(context, null);
    }

    public ChooseSongListView(Context context, AttributeSet attrs) {
        super(context, attrs);
        View.inflate(context, R.layout.choose_song_list_view, this);
        rv = findViewById(R.id.rv);
        emptyLay = findViewById(R.id.empty_lay);
        emptyTv = findViewById(R.id.empty_tv);
        refreshBtn = findViewById(R.id.refresh);
        bottomNoneLay = findViewById(R.id.bottom_none_lay);
        refreshLayout = findViewById(R.id.refresh_lay);
        musicEmptyLay = findViewById(R.id.music_empty_lay);
        layoutManager = new LinearLayoutManager(getContext());
        rv.setLayoutManager(layoutManager);
        refreshBtn.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                updateShared();
            }
        });
        refreshLayout.setEnableLoadMore(false);
        refreshLayout.setEnableRefresh(false);
    }

    void init(int rid, boolean showRefresh, int fromPage, PlayingAdapter.OnClickMusicListener onClickMusicListener) {
        this.rid = rid;
        adapter = new PlayingAdapter(rid, fromPage, onClickMusicListener);
        rv.setAdapter(adapter);
        showRefresh(showRefresh);
    }

    public void notifyFavoriteChanged(){
        adapter.notifyDataSetChanged();
    }

    public void setCanLoadMore(boolean canLoad, int type){
        refreshLayout.setEnableLoadMore(canLoad);
        refreshLayout.setOnLoadMoreListener(refreshlayout -> {
            if (type == FAVORITE_MUSIC) {
                loadMoreFavorite(true);
            }
        });
    }

    public void setCanRefresh(boolean canRefresh, int type){
        refreshLayout.setEnableRefresh(canRefresh);
        refreshLayout.setOnRefreshListener(refreshlayout -> {
            if (type == FAVORITE_MUSIC) {
                refreshFavorite();
            }
        });
    }

    void updateShared() {
        VoiceMusicSender.search(rid, SEARCH_TYPE_SHARED, "", offset, new LifeSeqCallback(this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                if (head.message instanceof MusicPackets.MSSearchRsp) {
                    MusicPackets.MSSearchRsp rsp = (MusicPackets.MSSearchRsp)head.message;
                    offset += rsp.getMusicListCount();
                    updateContent(VoiceMusicInfo.parse(rsp.getMusicListList()));
                    if (adapter.getItemCount() > 0) {
                        rv.smoothScrollToPosition(0);
                    }
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    void loadMoreFavorite(boolean showToast) {
        VoiceMusicApi.getFavoriteMusicList(LoginHelper.getLoginUid(), adapter.getMusicInfoList().size(), new LifeDataCallback<>(this) {
            @Override
            public void onSuccess(Result<VoiceMusicList> result) {
                refreshLayout.finishLoadMore();
                if (result.data.musicInfoList.isEmpty()) {
                    if (showToast) {
                        ToastUtil.show(R.string.no_more);
                    }
                    return;
                }
                addContent(result.data.musicInfoList);
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    void refreshFavorite(){
        VoiceMusicApi.getFavoriteMusicList(LoginHelper.getLoginUid(), 0, new LifeDataCallback<>(this) {
            @Override
            public void onSuccess(Result<VoiceMusicList> result) {
                updateContent(result.data.musicInfoList);
                refreshLayout.finishRefresh();
                if (result.data.musicInfoList.isEmpty()) {
                    musicEmptyLay.setVisibility(VISIBLE);
                } else {
                    musicEmptyLay.setVisibility(GONE);
                }
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    void favoriteMusicChange(int musicId){
        List<VoiceMusicInfo> tempMusicList = adapter.getMusicInfoList();
        for (int i = 0; i<tempMusicList.size(); i++){
            VoiceMusicInfo voiceMusicInfo = tempMusicList.get(i);
            if (voiceMusicInfo.musicId == musicId){
                adapter.deleteMusic(i);
                int lastVisible = layoutManager.findLastVisibleItemPosition();
                if (adapter.getMusicInfoList().size() == 0){
                    musicEmptyLay.setVisibility(VISIBLE);
                } else if (lastVisible == adapter.getMusicInfoList().size()){
                    loadMoreFavorite(false);
                }
                return;
            }
        }
        refreshFavorite();
    }

    void updateRecent() {
        List<VoiceMusicInfo> recent = RecentChooseManager.get().getRecentPlay();
        if (recent.isEmpty()) {
            showEmpty(false);
        } else {
            emptyLay.setVisibility(GONE);
            adapter.refresh(recent);
        }
    }

    void updateContent(List<VoiceMusicInfo> list) {
        adapter.refresh(list);
        rv.setVisibility(VISIBLE);
        emptyLay.setVisibility(GONE);
        if (adapter.getItemCount() > 0) {
            rv.scrollToPosition(0);
        }
    }
    void addContent(List<VoiceMusicInfo> list) {
        adapter.addList(list);
        rv.setVisibility(VISIBLE);
        emptyLay.setVisibility(GONE);
    }

    void showBottomNone() {
        bottomNoneLay.setVisibility(VISIBLE);
        emptyLay.setVisibility(GONE);
    }

    void clear() {
        adapter.clear();
        emptyLay.setVisibility(GONE);
        rv.setVisibility(GONE);
        bottomNoneLay.setVisibility(GONE);
    }

    void showEmpty(boolean showText) {
        rv.setVisibility(GONE);
        emptyLay.setVisibility(VISIBLE);
        emptyTv.setVisibility(showText ? VISIBLE :GONE);
        bottomNoneLay.setVisibility(GONE);

    }

    private void showRefresh(boolean show) {
        refreshBtn.setVisibility(show ? VISIBLE:GONE);
    }
}
