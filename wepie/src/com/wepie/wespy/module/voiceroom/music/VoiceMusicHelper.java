package com.wepie.wespy.module.voiceroom.music;

import com.wepie.download.DownloadCallback;
import com.wepie.wespy.model.entity.voiceroom.VoiceMusicInfo;
import com.wepie.download.DownloadUtil;
import com.huiwan.base.util.log.TimeLogger;

/**
 * date 2019-06-03
 * email <EMAIL>
 *
 * <AUTHOR>
 */
class VoiceMusicHelper {
    static void downloadIfNeed(VoiceMusicInfo info) {
        if (!info.getCacheFile().exists()) {
            DownloadUtil.downloadFile(info.musicUrl, info.getCacheFile().getAbsolutePath(), new DownloadCallback() {
                @Override
                public void onSuccess(String url, String path) {
                    TimeLogger.msg("download success at: " + path);
                }

                @Override
                public void onFail(String msg) {
                    TimeLogger.err(msg);
                }

                @Override
                public void onPercent(int percent) {

                }
            });
        }
    }
}
