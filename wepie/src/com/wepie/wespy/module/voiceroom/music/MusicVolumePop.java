package com.wepie.wespy.module.voiceroom.music;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.PopupWindow;

import com.wepie.wespy.R;
import com.wepie.wespy.helper.view.VerticalSeekBar;
import com.huiwan.base.util.ScreenUtil;

/**
 * date 2019-05-15
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class MusicVolumePop extends FrameLayout {

    public MusicVolumePop(@NonNull Context context) {
        this(context, null);
    }

    public MusicVolumePop(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        LayoutInflater.from(context).inflate(R.layout.room_music_volume_pop, this);
        VerticalSeekBar seekBar = findViewById(R.id.seek_bar);
        seekBar.setMax(VoiceMusicManager.MAX_MIX_VOLUME);
        seekBar.setProgress(VoiceMusicManager.get().getMusicVolume());
        seekBar.setOnProgressChangeListener(new VerticalSeekBar.OnProgressChangeListener() {

            @Override
            public void onProgressChange(VerticalSeekBar seekBar, int max, int progress, boolean fromUser) {
                if (fromUser) {
                    VoiceMusicManager.get().setMusicVolume(progress);
                }
            }
        });
    }

    public static PopupWindow showPop(View anchor) {
        MusicVolumePop menu = new MusicVolumePop(anchor.getContext());
        PopupWindow popup = new PopupWindow(menu);

        popup.setHeight(WindowManager.LayoutParams.WRAP_CONTENT);
        popup.setWidth(WindowManager.LayoutParams.WRAP_CONTENT);
        popup.setOutsideTouchable(true);
        popup.setFocusable(true);
        popup.setBackgroundDrawable(ContextCompat.getDrawable(anchor.getContext(), R.color.transparent));
        popup.showAsDropDown(anchor, 0, ScreenUtil.dip2px(-148) - anchor.getMeasuredHeight());
        return popup;
    }
}
