package com.wepie.wespy.module.voiceroom.music.lrc;

import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.util.FileUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.base.util.lru.SimpleLruHelper;
import com.wejoy.weplay.ex.ILife;
import com.wepie.download.DownloadUtil;
import com.wepie.download.LifeDownloadCallback;

/**
 * date 2019-05-24
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class LrcHelper {
    private static final SimpleLruHelper<String, String> LRU_HELPER = new SimpleLruHelper<>(8, 0.25f);

    public static void getLrc(String url, @NonNull LrcCallback callback) {
        String lrc = LRU_HELPER.get(url);
        if (TextUtils.isEmpty(lrc)) {
            downloadLrc(url, callback);
        } else {
            callback.onLrcAvailable(url, lrc);
        }
    }

    private static void downloadLrc(final String oriUrl, @NonNull final LrcCallback callback) {
        String encodedUrl = getEncodeUrl(oriUrl);
        DownloadUtil.downloadFile(encodedUrl, true, new LifeDownloadCallback(callback.life) {
            @Override
            public void onSuccess(String url, String path) {
                String lrc = FileUtil.detectEncoderAndRead(path);
                LRU_HELPER.put(oriUrl, lrc);
                callback.onLrcAvailable(oriUrl, lrc);
            }

            @Override
            public void onFail(String msg) {
                callback.onLrcAvailable(oriUrl, "");
                TimeLogger.err(msg);
            }

            @Override
            public void onPercent(int percent) {
                TimeLogger.msg("download lrc percent: " + percent);
            }
        });
    }

    private static String getEncodeUrl(String ori) {
        String url = Uri.decode(ori);
        String[] s = url.split("/");
        String end = "";
        if (s.length > 0) {
            end = s[s.length - 1];
        }
        if (!TextUtils.isEmpty(end)) {
            String endEncode = Uri.encode(end);
            url = url.replace(end, endEncode);
        }
        return url;
    }


    public abstract static class LrcCallback {
        private ILife life;

        public LrcCallback(ILife life) {
            this.life = life;
        }

        public abstract void onLrcAvailable(String url, @Nullable String lrc);
    }
}
