package com.wepie.wespy.module.voiceroom.music;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.os.Build;
import android.text.TextUtils;
import android.text.format.DateUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StatusBarUtil;
import com.huiwan.base.util.ViewUtil;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.widget.MarqueeTextView;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.model.entity.voiceroom.VoiceMusicInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.voice.LrcStatusChangeEvent;
import com.wepie.wespy.model.event.voice.MusicCurPlayChange;
import com.wepie.wespy.model.event.voice.MusicPlayInfoChange;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IMusicRunner;
import com.wepie.wespy.module.voiceroom.music.lrc.LrcHelper;
import com.wepie.wespy.module.voiceroom.music.lrc.LrcView;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;


/**
 * date 2019-05-09
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class VoiceMusicRunner extends RelativeLayout implements IMusicRunner, View.OnClickListener {
    private ViewGroup songInfoLay;
    private ImageView singerIv;
    private MarqueeTextView songTv;
    private View lrcBgView;
    private ViewGroup lrcContainer;
    private LrcView lrcView;
    private View lrcMoreView;
    private View lrcCloseView;
    private TextView lrcNoneTv;
    private ValueAnimator playingAnimator;
    private boolean hasLrc = true;

    public VoiceMusicRunner(Context context) {
        this(context, null);
    }

    public VoiceMusicRunner(Context context, AttributeSet attrs) {
        super(context, attrs);

        initView();
        int gravity;
        try (TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.SingleSideFadeOutRecyclerView)) {
            int resImg = typedArray.getResourceId(R.styleable.VoiceMusicRunner_lay_background, R.drawable.shape_4c000000_cornerleft);
            songInfoLay.setBackgroundResource(resImg);
            gravity = typedArray.getInt(R.styleable.VoiceMusicRunner_lay_gravity, 1);
        }
        ViewGroup.LayoutParams params = songInfoLay.getLayoutParams();
        if (params instanceof RelativeLayout.LayoutParams) {
            RelativeLayout.LayoutParams realParams = (RelativeLayout.LayoutParams) params;
            if (gravity == 0) {
                realParams.addRule(RelativeLayout.ALIGN_PARENT_START);
                realParams.removeRule(RelativeLayout.ALIGN_PARENT_END);
            } else {
                realParams.addRule(RelativeLayout.ALIGN_PARENT_END);
                realParams.removeRule(RelativeLayout.ALIGN_PARENT_START);
            }
        }
    }

    private void initView() {
        View.inflate(getContext(), R.layout.room_music_runner, this);
        songInfoLay = findViewById(R.id.song_info_lay);

        singerIv = findViewById(R.id.singer_iv);
        songTv = findViewById(R.id.song_tv);
        lrcContainer = findViewById(R.id.lrc_container);
        lrcView = findViewById(R.id.lrc_view);
        lrcMoreView = findViewById(R.id.lrc_more_view);
        lrcCloseView = findViewById(R.id.lrc_close_btn);
        lrcNoneTv = findViewById(R.id.lrc_none_tv);
        lrcBgView = findViewById(R.id.lrc_bg);

        songInfoLay.setOnClickListener(this);
        lrcMoreView.setOnClickListener(this);
        lrcCloseView.setOnClickListener(this);
        lrcContainer.setOnClickListener(this);
        checkMovement();
        checkUpdateInfoPos();
        EventDispatcher.registerEventObserver(this);
    }

    @Override
    public void onClick(View v) {
        if (v == songInfoLay) {
            VoiceMusicPlayingDialog.show(v.getContext(), getMainPlugin().getRoomId());
        } else if (v == lrcMoreView) {
            MusicReportHelper.get().checkShowReportList(v.getContext());
        } else if (v == lrcCloseView) {
            PrefUserUtil.getInstance().setBoolean(PrefUserUtil.MUSIC_LRC_OPEN, false);
            checkShowLrc();
        } else if (v == lrcContainer) {
            showLrcBg();
        }
    }

    @Override
    public void updatePlayingMusic(VoiceMusicInfo.PlayInfo playInfo) {
        boolean inMusic = getMainPlugin().getRoomInfo().inMusic;
        updateVisible(inMusic);
        if (inMusic) {
            updatePlayInfo(playInfo);
        }
    }

    @Override
    public void onUpdateRoomInfo(VoiceRoomInfo roomInfo) {
        boolean inMusic = roomInfo.inMusic;
        updateVisible(inMusic);
    }

    @Override
    public void setMargin(int start, int top, int end, int bottom) {
        ViewUtil.setMargins(songInfoLay, start, top, end, bottom);
    }

    private void updateVisible(boolean open) {
        if (open) {
            setVisibility(VISIBLE);
        } else {
            setVisibility(GONE);
        }
    }

    private void updatePlayInfo(VoiceMusicInfo.PlayInfo playInfo) {
        lrcNoneTv.setVisibility(GONE);
        if (playInfo.valid()) {
            songTv.setText(playInfo.musicInfo.musicName);
            HeadImageLoader.loadCircleHeadImage(playInfo.musicInfo.selectUserHeader, singerIv);
            updateLrc(playInfo);
            if (playInfo.isPlaying) {
                showAnim();
            } else {
                pauseAnim();
                lrcView.pause();
            }
            checkShowLrc();
        } else {
            songTv.setText(R.string.voice_room_music_need_order);
            singerIv.setImageDrawable(null);
            lrcContainer.setVisibility(GONE);
            lrcView.clearLrc();
            cancelAnim();
        }
    }

    private void updateLrc(VoiceMusicInfo.PlayInfo playInfo) {
        if (TextUtils.isEmpty(playInfo.musicInfo.lyricUrl)) {
            lrcView.clearLrc();
            hasLrc = false;
            lrcNoneTv.setAlpha(0.3f);
            lrcNoneTv.setVisibility(VISIBLE);
        } else {
            hasLrc = true;
            lrcContainer.setVisibility(VISIBLE);
            LrcHelper.getLrc(playInfo.musicInfo.lyricUrl, new LrcHelper.LrcCallback(ILifeUtil.toLife(this)) {
                @Override
                public void onLrcAvailable(String url, @Nullable String lrc) {
                    if (TextUtils.isEmpty(lrc)) {
                        hasLrc = false;
                        lrcView.clearLrc();
                        lrcNoneTv.setAlpha(0.3f);
                        lrcNoneTv.setVisibility(VISIBLE);
                    } else {
                        showLrc(lrc);
                    }
                }
            });
        }
    }

    private void checkMovement() {
        lrcContainer.setOnTouchListener(new OnTouchListener() {
            private float downY = 0;
            private float y = 0;
            private long downTime = 0;

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                int action = event.getActionMasked();
                if (action == MotionEvent.ACTION_DOWN) {
                    downY = event.getRawY();
                    y = event.getRawY();
                    downTime = System.currentTimeMillis();
                } else if (action == MotionEvent.ACTION_MOVE) {
                    float movedY = event.getRawY() - y;
                    float newTranslationY = lrcContainer.getTranslationY() + movedY;
                    y = event.getRawY();
                    float top = ScreenUtil.dip2px(44) + ScreenUtil.getStatusBarHeight();
                    float bottom = ScreenUtil.getScreenHeight() - ScreenUtil.dip2px(50);
                    if (newTranslationY < top) {
                        newTranslationY = top;
                    } else if (newTranslationY > bottom - lrcContainer.getHeight()) {
                        newTranslationY = bottom - lrcContainer.getHeight();
                    }
                    lrcContainer.setTranslationY(newTranslationY);
                    PrefUserUtil.getInstance().setInt(PrefUserUtil.MUSIC_LRC_CONTAINER_POS, (int) newTranslationY);
                } else if (action == MotionEvent.ACTION_UP) {
                    if (System.currentTimeMillis() - downTime < 100 && Math.abs(event.getRawY() - downY) < 10) {
                        v.performClick();
                    }
                }
                return true;
            }
        });
        int height = PrefUserUtil.getInstance().getInt(PrefUserUtil.MUSIC_LRC_CONTAINER_POS, ScreenUtil.dip2px(150) + ScreenUtil.getStatusBarHeight());
        lrcContainer.setTranslationY(height);
    }

    private void showLrc(@NonNull String lrc) {
        hasLrc = true;
        VoiceMusicInfo.PlayInfo playInfo = VoiceMusicManager.get().getCurPlayInfo();
        if (playInfo.valid()) {
            if (playInfo.isPlaying) {
                lrcView.loadLrc(lrc, (int) playInfo.getCurPlayTimeMs(), true);
            } else {
                lrcView.loadLrc(lrc, (int) playInfo.getCurPlayTimeMs(), false);
            }
        } else {
            lrcView.clearLrc();
        }
    }


    private void showLrcBg() {
        setLrcBgEnable();
        lrcBgView.setVisibility(VISIBLE);
        if (!hasLrc) {
            lrcNoneTv.setVisibility(VISIBLE);
            lrcNoneTv.setAlpha(0.3f);
        }
    }

    private void setLrcBgEnable() {
        lrcBgView.setEnabled(true);
        lrcMoreView.setEnabled(true);
        lrcCloseView.setEnabled(true);
        lrcBgView.setVisibility(VISIBLE);
        lrcMoreView.setVisibility(VISIBLE);
        lrcCloseView.setVisibility(VISIBLE);
    }

    private void checkShowLrc() {
        boolean needShow = PrefUserUtil.getInstance().getBoolean(PrefUserUtil.MUSIC_LRC_OPEN, false);
        lrcContainer.setVisibility(needShow ? VISIBLE : GONE);
    }

    private void cancelAnim() {
        songTv.setEllipsize(TextUtils.TruncateAt.END);
        if (Build.VERSION_CODES.KITKAT > Build.VERSION.SDK_INT) {
            singerIv.clearAnimation();
        } else {
            if (playingAnimator != null) {
                playingAnimator.cancel();
                playingAnimator = null;
                singerIv.setRotation(0);
            }
        }
    }

    private void pauseAnim() {
        songTv.setEllipsize(TextUtils.TruncateAt.END);
        if (Build.VERSION_CODES.KITKAT > Build.VERSION.SDK_INT) {
            singerIv.clearAnimation();
        } else {
            if (playingAnimator != null) {
                playingAnimator.pause();
            }
        }
    }

    private void showAnim() {
        songTv.setEllipsize(TextUtils.TruncateAt.MARQUEE);
        if (playingAnimator == null) {
            playingAnimator = ObjectAnimator.ofFloat(singerIv, "rotation", 0, 360);
            playingAnimator.setDuration(DateUtils.SECOND_IN_MILLIS * 10);
            playingAnimator.setRepeatCount(ValueAnimator.INFINITE);
            playingAnimator.setInterpolator(new LinearInterpolator());
            playingAnimator.start();
        } else {
            playingAnimator.resume();
        }
    }

    private void checkUpdateInfoPos() {
        if (getMainPlugin().getRoomInfo().isAuctionRoom()) {
            ((RelativeLayout.LayoutParams) songInfoLay.getLayoutParams()).topMargin = ScreenUtil.dip2px(204);
        }

        if (getMainPlugin().getRoomInfo().isCpRoom()) {
            ((RelativeLayout.LayoutParams) songInfoLay.getLayoutParams()).topMargin = ScreenUtil.dip2px(168);
        }

        if (!StatusBarUtil.isSupportFullScreen()) {
            ((RelativeLayout.LayoutParams) songInfoLay.getLayoutParams()).topMargin -= ScreenUtil.getStatusBarHeight();
        }
        songInfoLay.requestLayout();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onLrcStatusChange(LrcStatusChangeEvent event) {
        checkShowLrc();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onCurPlayChange(MusicCurPlayChange event) {
        boolean inMusic = getMainPlugin().getRoomInfo().inMusic;
        if (inMusic) {
            updatePlayInfo(VoiceMusicManager.get().getCurPlayInfo());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayingMusicChange(MusicPlayInfoChange event) {
        updatePlayingMusic(VoiceMusicManager.get().getCurPlayInfo());
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        updatePlayingMusic(VoiceMusicManager.get().getCurPlayInfo());
    }

    @Override
    protected void onDetachedFromWindow() {
        EventDispatcher.unregisterEventObserver(this);
        singerIv.clearAnimation();
        if (playingAnimator != null) {
            playingAnimator.cancel();
        }
        super.onDetachedFromWindow();
    }
}
