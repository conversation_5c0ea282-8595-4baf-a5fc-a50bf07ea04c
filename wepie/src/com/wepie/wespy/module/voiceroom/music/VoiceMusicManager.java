package com.wepie.wespy.module.voiceroom.music;

import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.text.format.DateUtils;

import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;

import com.huiwan.base.lifecycle.IReference;
import com.huiwan.base.lifecycle.IReferenceKt;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.libtcp.callback.LifeSeqCallbackProxy;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.user.LoginHelper;
import com.huiwan.voiceservice.VoiceManager;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.GlobalLife;
import com.wepie.lib.api.plugins.voice.VoiceEffect;
import com.wejoy.weplay.ex.ILifeOwner;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceMusicIdList;
import com.wepie.wespy.model.entity.voiceroom.VoiceMusicInfo;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.http.api.VoiceMusicApi;
import com.wepie.wespy.net.tcp.packet.MusicPackets;
import com.wepie.wespy.net.tcp.sender.VoiceMusicSender;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * date 2019-05-09
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class VoiceMusicManager {
    private static final String TAG = "VoiceMusicManager";
    public static final int MAX_MIX_VOLUME = 70;
    private static final int DEFAULT_MIX_VOLUME = 35;
    private static final VoiceMusicManager INSTANCE = new VoiceMusicManager();

    private final ExecutorService singleThreadPool = ThreadUtil.newSingleThreadPool("VoiceMusic");

    private VoiceMusicManager() {
        volume = PrefUserUtil.getInstance().getInt(PrefUserUtil.MUSIC_VOLUME, DEFAULT_MIX_VOLUME);
        if (volume > MAX_MIX_VOLUME) {
            volume = MAX_MIX_VOLUME;
        }
    }

    private int rid = -1;
    private VoiceMusicInfo.PlayInfo curPlayInfo = new VoiceMusicInfo.PlayInfo();
    private List<VoiceMusicInfo> musicInfoList = new ArrayList<>();
    private final List<PlayTickObserver> timeObserverList = new LinkedList<>();
    private final Handler handler = new Handler(Looper.getMainLooper());
    private boolean widgetOpen = false;
    private CountDownTimer timer;
    private int volume;
    private int playMode;
    private int version = 0;

    public HashSet<Integer> favoriteMusics = new HashSet<>();

    public static VoiceMusicManager getInstance() {
        return INSTANCE;
    }

    public int getPlayMode() {
        return playMode;
    }

    public void changePlayMode() {
        int nextPlayMode = getNextMode(playMode);
        VoiceMusicSender.changePlayMode(rid, nextPlayMode, new LifeSeqCallbackProxy() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                HLog.d(TAG, HLog.USR, "changePlayMode success To {}", nextPlayMode);
                if (nextPlayMode == VoiceMusicInfo.LOOP_SINGLE) {
                    ToastUtil.show(ResUtil.getStr(R.string.voice_room_music_mode_loop_single));
                } else if (nextPlayMode == VoiceMusicInfo.LOOP_RANDOM) {
                    ToastUtil.show(ResUtil.getStr(R.string.voice_room_music_mode_loop_random));
                } else if (nextPlayMode == VoiceMusicInfo.LOOP_LIST) {
                    ToastUtil.show(ResUtil.getStr(R.string.voice_room_music_mode_loop_list));
                } else if (nextPlayMode == VoiceMusicInfo.LOOP_SEQUENCE) {
                    ToastUtil.show(ResUtil.getStr(R.string.voice_room_music_mode_loop_sequence));
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                HLog.d(TAG, HLog.USR, "changePlayMode fail To {}", nextPlayMode);
                ToastUtil.show(head.desc);
            }
        });
    }

    private int getNextMode(int playMode) {
        int nextMode = 0;
        if (playMode == VoiceMusicInfo.LOOP_SEQUENCE) {
            nextMode = VoiceMusicInfo.LOOP_LIST;
        } else {
            nextMode = playMode + 1;
        }
        return nextMode;
    }

    public void addFavorite(int musicId) {
        favoriteMusics.add(musicId);
    }

    public void removeFavorite(int musicId) {
        favoriteMusics.remove(musicId);
    }

    public boolean isFavorite(int musicId) {
        return favoriteMusics.contains(musicId);
    }

    public void clearFavorite() {
        version = 0;
        favoriteMusics.clear();
    }


    public void bindRid(int rid, boolean widgetOpen) {
        if (this.rid != rid) {
            handler.removeCallbacksAndMessages(null);
            if (widgetOpen) {
                refreshCurPlaying(rid, null);
            } else {
                clearWidget();
            }
        }
        this.widgetOpen = widgetOpen;
        this.rid = rid;
        refreshFavoriteIds();
    }

    public void refreshFavoriteIds() {
        if (version == 0) {
            VoiceMusicApi.getFavoriteMusicIds(LoginHelper.getLoginUid(), version, new LifeDataCallback<>(GlobalLife.INSTANCE) {
                @Override
                public void onSuccess(Result<VoiceMusicIdList> result) {
                    if (version < result.data.version) {
                        favoriteMusics.clear();
                        favoriteMusics.addAll(result.data.idList);
                        version = result.data.version;
                    }
                }

                @Override
                public void onFail(int code, String msg) {
                    ToastUtil.show(msg);
                }
            });
        }
    }

    private void clearSelfPlaying() {
        curPlayInfo.clear();
        lastPlayingId = 0;
        voiceStop();
    }

    private void clearWidget() {
        clearSelfPlaying();
        clearTimer();
        tickCurrentMs = 0;
        tickTotalMs = 0;
        musicInfoList.clear();
        widgetOpen = false;
        errTimes = 0;
        reportId = 0;
        retryId = 0;
        lastPlayingId = 0;
        voiceStarted = false;
        playMode = 0;
        handler.removeCallbacksAndMessages(null);
    }

    private void clearList() {
        clearSelfPlaying();
        clearTimer();
        tickCurrentMs = 0;
        tickTotalMs = 0;
        musicInfoList.clear();
        errTimes = 0;
        reportId = 0;
        retryId = 0;
        lastPlayingId = 0;
        voiceStarted = false;
        handler.removeCallbacksAndMessages(null);
    }

    public void clearAndStop() {
        rid = -1;
        clearWidget();
    }

    public void checkChangePlayStatus(int rid) {
        if (this.rid == rid) {
            final boolean newStatus = !curPlayInfo.isPlaying;
            VoiceMusicSender.modePlayStatus(rid, lastPlayingId, newStatus, curPlayInfo.getCurPlayTimeMs(), new LifeSeqCallbackProxy() {
                @Override
                public void onSuccess(RspHeadInfo head) {
                    // do it in the push logic
                }

                @Override
                public void onFail(RspHeadInfo head) {
                    ToastUtil.show(head.desc);
                }
            });
            curPlayInfo.clear();
        }
    }

    public void refreshCurPlaying(final int rid, CurPlayCallback callback) {
        IReference<CurPlayCallback> reference = IReferenceKt.wrap(callback);
        VoiceMusicSender.syncPlayInfo(rid, new LifeSeqCallbackProxy() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                if (head.message instanceof MusicPackets.MSSyncPlayInfoRsp) {
                    MusicPackets.MSSyncPlayInfoRsp rsp = (MusicPackets.MSSyncPlayInfoRsp) head.message;
                    VoiceMusicInfo.PlayInfo newPlayInfo = VoiceMusicInfo.parse(rsp.getPlayInfo());
                    HLog.d(TAG, HLog.USR, "refreshCurPlaying: {}, {}, {}", newPlayInfo.isPlaying, newPlayInfo.musicInfo.musicId, newPlayInfo.musicInfo.musicName);
                    IReferenceKt.run(reference, callback -> callback.onRefresh(newPlayInfo));
                    checkMusicNeedPlay(curPlayInfo, newPlayInfo);
                    curPlayInfo = newPlayInfo;
                    tickCurrentMs = curPlayInfo.getCurPlayTimeMs();
                    tickTotalMs = curPlayInfo.getTotalTimeMs();
                    EventDispatcher.postMusicListInfoChangeEvent(rid);
                } else {
                    IReferenceKt.run(reference, callback -> callback.onRefresh(null));
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                HLog.d(TAG, HLog.USR, "refreshCurPlaying failed: {}, {}", head.code, head.desc);
                CurPlayCallback callback = reference.get();
                if (callback != null) {
                    callback.onRefresh(null);
                }
            }
        });
    }

    public void refreshPlayingList(int rid, MusicListCallback callback) {
        IReference<MusicListCallback> reference = IReferenceKt.wrap(callback);
        VoiceMusicSender.syncMusicList(rid, new LifeSeqCallbackProxy() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                if (head.message instanceof MusicPackets.MSSyncListRsp) {
                    MusicPackets.MSSyncListRsp rsp = (MusicPackets.MSSyncListRsp) head.message;
                    musicInfoList = VoiceMusicInfo.parse(rsp.getMusicListList());
                    HLog.d(TAG, HLog.USR, "refreshPlayingList {}", musicInfoList.size());
                    playMode = rsp.getPlayMode();
                    IReferenceKt.run(reference, callback -> callback.onRefresh(musicInfoList, playMode));
                } else {
                    HLog.d(TAG, HLog.USR, "error msg type:" + head.message);
                    IReferenceKt.run(reference, callback -> callback.onRefresh(null, playMode));
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                HLog.d(TAG, head.desc);
                IReferenceKt.run(reference, callback -> callback.onRefresh(null, playMode));
            }
        });
    }

    public void setPlayMode(int rid, int playMode) {
        if (this.rid == rid) {
            this.playMode = playMode;
        }
    }

    public void playNext() {
        VoiceMusicSender.playNext(rid, curPlayInfo.musicInfo.listId, new LifeSeqCallbackProxy() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                HLog.d(TAG, "notify 2 play next");
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public void clearPlayList() {
        VoiceMusicSender.clearMusicList(rid, new LifeSeqCallbackProxy() {
            @Override
            public void onSuccess(RspHeadInfo head) {

            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public void playMusic(int musicListId) {
        VoiceMusicSender.playMusic(rid, musicListId, new LifeSeqCallbackProxy() {
            @Override
            public void onSuccess(RspHeadInfo head) {
//                clearList();
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private void check2NotifyPlayComplete() {
        curPlayInfo.isPlaying = false;
        if (curPlayInfo.musicInfo.selectUserId == LoginHelper.getLoginUid()) {
            VoiceMusicSender.playComplete(rid, curPlayInfo.musicInfo.listId, new LifeSeqCallbackProxy() {
                @Override
                public void onSuccess(RspHeadInfo head) {
                    HLog.d(TAG, "notify 2 play next");
                }

                @Override
                public void onFail(RspHeadInfo head) {
                    HLog.d(TAG, "notify error: " + head.desc);
                }
            });
        }
    }

    private void checkMusicNeedPlay(final VoiceMusicInfo.PlayInfo curPlayInfo, final VoiceMusicInfo.PlayInfo newPlayInfo) {
        if (voiceOnline()) {
            if (checkMusicNeedPlayTask != null) {
                handler.removeCallbacksAndMessages(checkMusicNeedPlayTask);
            }
            doCheckMusicNeedPlay(curPlayInfo, newPlayInfo);
        } else {
            checkMusicNeedPlayTask = new CheckMusicNeedPlayTask(curPlayInfo, newPlayInfo);
            handler.postDelayed(checkMusicNeedPlayTask, 500);
        }
    }
    private CheckMusicNeedPlayTask checkMusicNeedPlayTask = null;

    private class CheckMusicNeedPlayTask implements Runnable {
        final VoiceMusicInfo.PlayInfo curPlayInfo;
        final VoiceMusicInfo.PlayInfo newPlayInfo;

        CheckMusicNeedPlayTask(VoiceMusicInfo.PlayInfo curPlayInfo, VoiceMusicInfo.PlayInfo newPlayInfo) {
            this.curPlayInfo = curPlayInfo;
            this.newPlayInfo = newPlayInfo;
        }

        @Override
        public void run() {
            checkMusicNeedPlay(curPlayInfo, newPlayInfo);
        }
    }

    private void doCheckMusicNeedPlay(final VoiceMusicInfo.PlayInfo curPlayInfo, final VoiceMusicInfo.PlayInfo newPlayInfo) {
        HLog.d(TAG, HLog.USR, "checkVoiceStart, curListId={}, curMusicId={}, newListId={}, newMusicId={},volume={}", curPlayInfo.musicInfo.listId, curPlayInfo.musicInfo.musicId, newPlayInfo.musicInfo.listId, newPlayInfo.musicInfo.musicId, volume);
        if (newPlayInfo.isPlaying) {
            startTimer(newPlayInfo.getCurPlayTimeMs(), newPlayInfo.getTotalTimeMs());
            if (newPlayInfo.selfChoose() && VoiceRoomService.getInstance().getRoomInfo().isInSeat(LoginHelper.getLoginUid())) {
                if (curPlayInfo.musicInfo.listId == newPlayInfo.musicInfo.listId && lastPlayingId == newPlayInfo.musicInfo.listId) {
                    if (!curPlayInfo.isPlaying) {
                        voiceResume();
                        setMusicVolume(volume);
                    }
                    check2UpdatePosition(newPlayInfo);
                } else {
                    checkVoiceStart(newPlayInfo);
                    setMusicVolume(volume);
                }
            } else {
                voiceStop();
            }
        } else {
            clearTimer();
            if (newPlayInfo.musicInfo.listId == 0) {
                // stop
                tickCurrentMs = 0;
                tickTotalMs = 0;
                voiceStop();
            } else {
                tickCurrentMs = newPlayInfo.getCurPlayTimeMs();
                tickTotalMs = newPlayInfo.getTotalTimeMs();
                // pause
                if (newPlayInfo.selfChoose()) {
                    voicePause();
                }
            }
        }
    }

    private void check2UpdatePosition(final VoiceMusicInfo.PlayInfo newPlayInfo) {
        if (interceptUploadProgress()) {
            return;
        }
        postSlowChecker();
        singleThreadPool.execute(() -> {
            long voicePosition = getVoicePosition();
            long diffTime = newPlayInfo.getCurPlayTimeMs() - voicePosition;
            if (diffTime > DateUtils.SECOND_IN_MILLIS * 5) {
                voiceSeekMs(newPlayInfo.progressMillis);
            } else if (diffTime < -DateUtils.SECOND_IN_MILLIS) {
                doUploadProgress(newPlayInfo.musicInfo.listId, voicePosition);
            }
            removeSlowChecker();
        });
    }

    private long lastUploadProgressTime = 0;

    private boolean interceptUploadProgress() {
        if (System.currentTimeMillis() - lastUploadProgressTime < DateUtils.SECOND_IN_MILLIS) {
            return true;
        }
        lastUploadProgressTime = System.currentTimeMillis();
        return false;
    }

    private void uploadProgress(final int listId) {
        if (interceptUploadProgress()) {
            return;
        }
        postSlowChecker();
        singleThreadPool.execute(() -> {
            doUploadProgress(listId, getVoicePosition());
            removeSlowChecker();
        });
    }

    @WorkerThread
    private void doUploadProgress(int listId, long position) {
        if (listId == curPlayInfo.musicInfo.listId) {
            VoiceMusicSender.uploadProgress(rid, listId, position, new LifeSeqCallbackProxy() {
                @Override
                public void onSuccess(RspHeadInfo head) {

                }

                @Override
                public void onFail(RspHeadInfo head) {
                    HLog.d(TAG, HLog.USR, "error upload progress msg={}", head.desc);
                }
            });
        }
        HLog.d(TAG, HLog.USR, "upload progress {}", listId);
    }

    public void update(int rid, VoiceMusicInfo.PlayInfo newPlayInfo, List<VoiceMusicInfo> musicInfoList) {
        if (this.rid == rid) {
            checkMusicNeedPlay(this.curPlayInfo, newPlayInfo);
            this.curPlayInfo = newPlayInfo;
            this.musicInfoList.clear();
            this.musicInfoList.addAll(musicInfoList);
            tickCurrentMs = curPlayInfo.progressMillis;
            tickTotalMs = curPlayInfo.getTotalTimeMs();
        }
    }

    public void update(int rid, VoiceMusicInfo.PlayInfo newPlayInfo) {
        if (this.rid == rid) {
            checkMusicNeedPlay(this.curPlayInfo, newPlayInfo);
            this.curPlayInfo = newPlayInfo;
            tickCurrentMs = curPlayInfo.progressMillis;
            tickTotalMs = curPlayInfo.getTotalTimeMs();
        }
    }

    public void update(int rid, boolean widgetOpen) {
        if (this.rid == rid) {
            this.widgetOpen = widgetOpen;
            if (!widgetOpen) {
                clearWidget();
            }
        }
    }

    public List<VoiceMusicInfo> getMusicInfoList() {
        return musicInfoList;
    }

    public VoiceMusicInfo.PlayInfo getCurPlayInfo() {
        return curPlayInfo;
    }

    public void registerTickObserver(PlayTickObserver observer) {
        synchronized (PlayTickObserver.class) {
            if (!timeObserverList.contains(observer)) {
                timeObserverList.add(observer);
            }
        }
    }

    public void unregisterTickObserver(PlayTickObserver observer) {
        synchronized (PlayTickObserver.class) {
            timeObserverList.remove(observer);
        }
    }

    public static VoiceMusicManager get() {
        return INSTANCE;
    }

    private void clearTimer() {
        if (timer != null) {
            timer.cancel();
        }
    }

    private void voiceStop() {
        boolean res = VoiceManager.getInstance().stopAudioMixing();
        lastVoiceStartTime = 0;
        voiceStarted = false;
        handler.removeCallbacks(startPlayCurTask);
        HLog.d(TAG, HLog.USR, "stop mixing {}", res);
    }

    private void voicePause() {
        boolean res = VoiceManager.getInstance().pauseAudioMixing();
        handler.removeCallbacks(startPlayCurTask);
        HLog.d(TAG, HLog.USR, "pause mixing {}", res);
    }

    private int lastPlayingId;
    private long lastVoiceStartTime = 0;
    private int errTimes;

    private void checkVoiceStart(final VoiceMusicInfo.PlayInfo playInfo) {
        HLog.d(TAG, HLog.USR, "checkVoiceStart, lastListId={}, curListId={}", lastPlayingId, playInfo.musicInfo.listId);
        if (lastPlayingId != playInfo.musicInfo.listId) {
            lastPlayingId = playInfo.musicInfo.listId;
            errTimes = 0;
            voiceStop();
        } else {
            if (System.currentTimeMillis() - lastVoiceStartTime < DateUtils.SECOND_IN_MILLIS) {
                return;
            }
            lastVoiceStartTime = System.currentTimeMillis();
        }

        if (VoiceRoomService.getInstance().getRoomInfo().isCanSpeak() && !VoiceManager.getInstance().isLocalMicOn()) {
            VoiceManager.getInstance().closeMic(false);
        }
        // 即刻开始播放下一首歌时，上一首歌会卡一下重放其中的一部分
        postStartPlayCur();
    }

    private void postStartPlayCur() {
        handler.removeCallbacks(startPlayCurTask);
        handler.postDelayed(startPlayCurTask, DateUtils.SECOND_IN_MILLIS);
    }

    private Runnable startPlayCurTask = new Runnable() {
        @Override
        public void run() {
            int musicId = curPlayInfo.musicInfo.musicId;
            int listId = curPlayInfo.musicInfo.listId;
            boolean valid = curPlayInfo.valid();
            boolean selfChoose = curPlayInfo.selfChoose();
            boolean playing = curPlayInfo.isPlaying;
            HLog.d(TAG, HLog.USR, "startPlayCurTask, musicId={}, valid={}, selfChoose={}, playing={}, listId={}", musicId, valid, selfChoose, playing, listId);
            if (valid && selfChoose && playing) {
                voiceStart(curPlayInfo, curPlayInfo.getCurPlayTimeMs());
            }
        }
    };

    private boolean voiceStarted = false;

    private void voiceStart(final VoiceMusicInfo.PlayInfo playInfo, long seekTime) {
        long start = System.currentTimeMillis();
        if (seekTime <= DateUtils.SECOND_IN_MILLIS) {
            seekTime = 0L;
        }
        VoiceEffect effect = new VoiceEffect();
        effect.setUrl(playInfo.musicInfo.musicUrl);
        effect.setPath(playInfo.musicInfo.getCacheFile().getAbsolutePath());
        effect.setVolume(volume);
        effect.setPosition((int) seekTime);
        boolean res = VoiceManager.getInstance().startAudioMixing(effect);
        if (res) {
            voiceStarted = true;
        }
        handler.removeCallbacks(uploadProgressTask);
        handler.postDelayed(uploadProgressTask, DateUtils.SECOND_IN_MILLIS * 5);
        voiceResume();
        HLog.d(TAG, HLog.USR, "start mixing url({}):{}, spent: {}", playInfo.musicInfo.musicUrl, res, (System.currentTimeMillis() - start));
    }

    private Runnable uploadProgressTask = new Runnable() {
        @Override
        public void run() {
            if (curPlayInfo.valid() && curPlayInfo.selfChoose() && curPlayInfo.isPlaying) {
                uploadProgress(curPlayInfo.musicInfo.listId);
            }
        }
    };

    private void voiceResume() {
        if (voiceStarted) {
            setMusicVolume(volume);
            boolean res = VoiceManager.getInstance().resumeAudioMixing();
            HLog.d(TAG, "start resume {}", res);
        } else {
            postStartPlayCur();
        }
    }

    private void voiceSeekMs(long targetPosition) {
        long start = System.currentTimeMillis();
        boolean res = VoiceManager.getInstance().seekAudioMixingPosition((int) targetPosition);
        HLog.d(TAG, "seek audio result: " + res + ", spent: " + (System.currentTimeMillis() - start));
    }

    private boolean voiceOnline() {
        return VoiceManager.getInstance().isOnlineLoose();
    }

    private long getVoicePosition() {
        int p = VoiceManager.getInstance().getAudioMixingPosition();
        return Math.max(p, 0);
    }

    public int getMusicVolume() {
        return volume;
    }

    public void setMusicVolume(int volume) {
        boolean res = VoiceManager.getInstance().setAudioMixingVolume(volume);
        if (res) {
            this.volume = volume;
            handler.removeCallbacks(saveVolumeRunner);
            handler.postDelayed(saveVolumeRunner, 400);
        }
    }

    public void ensureMusicVolume() {
        setMusicVolume(volume);
    }

    private Runnable saveVolumeRunner = new Runnable() {
        @Override
        public void run() {
            PrefUserUtil.getInstance().setInt(PrefUserUtil.MUSIC_VOLUME, getMusicVolume());
        }
    };

    private void postSlowChecker() {
        handler.removeCallbacks(progressSlowChecker);
        handler.postDelayed(progressSlowChecker, DateUtils.SECOND_IN_MILLIS);
    }

    private void removeSlowChecker() {
        handler.removeCallbacks(progressSlowChecker);
    }

    private Runnable progressSlowChecker = new Runnable() {
        @Override
        public void run() {
            ToastUtil.show(R.string.network_unstable_please_switch_another);
            voiceStop();
            check2NotifyPlayComplete();
        }
    };

    public long getTickCurMs() {
        return tickCurrentMs;
    }

    public long getTickTotalMs() {
        return tickTotalMs;
    }

    public void onJoinChannelSuccess() {
        if (curPlayInfo.isPlaying && curPlayInfo.selfChoose()) {
            lastPlayingId = curPlayInfo.musicInfo.listId;
            lastVoiceStartTime = System.currentTimeMillis();
            voiceStart(curPlayInfo, curPlayInfo.getCurPlayTimeMs());
        }
    }

    private int reportId = 0;
    private int retryId = 0;

    public void onPlayMusicError() {
        HLog.d(TAG, "onPlayMusicError");
        if (lastPlayingId != retryId) {
            if (lastPlayingId == curPlayInfo.musicInfo.listId &&
                    curPlayInfo.musicInfo.selectUserId == LoginHelper.getLoginUid()) {
                retryId = lastPlayingId;
                HLog.d(TAG, "retry to play cur play info");
                checkVoiceStart(curPlayInfo);
            }
        } else {
            if (reportId != lastPlayingId) {
                reportId = lastPlayingId;
                MusicReportHelper.get().reportPlayFailed(curPlayInfo.musicInfo.musicId);
                // 上一曲播放失败切换太快自己马上接着播放下一首大概率会失败，这里延时 1 秒
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        check2NotifyPlayComplete();
                    }
                }, DateUtils.SECOND_IN_MILLIS);
                ToastUtil.show(R.string.playing_failed);
            }
            HLog.d(TAG, "report play music failed");
        }
    }

    private long tickTotalMs = 0;
    private long tickCurrentMs = 0;

    private void startTimer(final long startPosition, final long totalMs) {
        clearTimer();
        long duration = totalMs - startPosition;
        HLog.d(TAG, "music time update, total={}, start={}", totalMs, startPosition);
        if (duration > 1) {
            timer = new CountDownTimer(duration, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    tickTotalMs = totalMs;
                    tickCurrentMs = totalMs - millisUntilFinished;
                    synchronized (PlayTickObserver.class) {
                        for (PlayTickObserver observer : timeObserverList) {
                            observer.onTick(tickCurrentMs, tickTotalMs);
                        }
                    }
                }

                @Override
                public void onFinish() {
                    check2NotifyPlayComplete();
                    tickCurrentMs = 0;
                    tickTotalMs = 0;
                    voiceStop();
                    synchronized (PlayTickObserver.class) {
                        for (PlayTickObserver observer : timeObserverList) {
                            observer.onTick(tickCurrentMs, tickTotalMs);
                        }
                    }
                }
            };
            timer.start();
        }
    }

    public interface CurPlayCallback extends ILifeOwner {
        void onRefresh(@Nullable VoiceMusicInfo.PlayInfo curPlayInfo);
    }

    public interface MusicListCallback extends ILifeOwner {
        void onRefresh(@Nullable List<VoiceMusicInfo> musicInfoList, int playMode);
    }

    public interface PlayTickObserver {
        void onTick(long curMs, long totalMs);
    }
}