package com.wepie.wespy.module.voiceroom.music;

import android.content.Context;
import androidx.annotation.NonNull;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.huiwan.base.util.collection.IMap;
import com.huiwan.base.util.collection.ListWrapper;
import com.wejoy.weplay.ex.GlobalLife;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFragCb;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.helper.dialog.SingleInputDialog;
import com.wepie.wespy.model.entity.voiceroom.MusicReportReason;
import com.wepie.wespy.module.voiceroom.util.view.ListViewDialog;
import com.wepie.wespy.net.http.api.MusicApi;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.base.util.ToastUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * date 2019-05-20
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class MusicReportHelper {
    private static final MusicReportHelper HELPER = new MusicReportHelper();
    private MusicReportHelper(){}
    public static MusicReportHelper get() {
        return MusicReportHelper.HELPER;
    }

    private final ListWrapper<MusicReportReason> reasons = new ListWrapper<>(new ArrayList<>());

    void checkShowReportList(final Context context) {
        if (reasons.isEmpty()) {
            MusicApi.getReportTypes(new LifeDataCallback<>(ContextUtil.getLife(context)) {
                @Override
                public void onSuccess(Result<List<MusicReportReason>> result) {
                    reasons.clear();
                    reasons.addAll(result.data);
                    showDialog(context);
                }

                @Override
                public void onFail(int i, String s) {
                    ToastUtil.show(s);
                }
            });
        } else {
            showDialog(context);
        }
    }

    private void showDialog(final Context context) {
        List<String> reasonString = new ListWrapper<>(reasons).map(I_MAP);
        reasonString.remove(0);
        reasonString.add(ResUtil.getStr(R.string.cancel));
        final int musicId = VoiceMusicManager.get().getCurPlayInfo().musicInfo.musicId;
        DialogUtil.showFragBottomDialog(context, reasonString, new ListViewDialog.Callback() {
            @Override
            public void onClickItem(final int position) {
                if (position + 1 < reasons.size()) {
                    final MusicReportReason reason = reasons.get(position+1);
                    if (position == reasons.size()-2) {
                        SingleInputDialog.showDialog(context, ResUtil.getStr(R.string.voice_room_music_feedback), ResUtil.getStr(R.string.voice_room_music_feedback_tip), ResUtil.getStr(R.string.sure),
                                "", ResUtil.getInteger(R.integer.voice_room_music_feedback_input_limit), true, true, false, new BaseFragCb<String>() {
                            @Override
                            public void onClickSure(@NonNull String what) {
                                doReport(musicId, reason.type, what);
                            }
                        });
                    } else {
                        doReport(musicId, reason.type, reason.name);
                    }
                }
            }
        });
    }

    private void doReport(int musicId, int type, String reason) {
        MusicApi.report(musicId, type, reason, new LifeDataCallback<>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<String> result) {
                ToastUtil.show(R.string.voice_room_music_feedback_success);
            }

            @Override
            public void onFail(int i, String s) {
                ToastUtil.show(s);
            }
        });
    }

    void reportPlayFailed(final int musicId) {
        if (reasons.isEmpty()) {
            MusicApi.getReportTypes(new LifeDataCallback<>(GlobalLife.INSTANCE) {
                @Override
                public void onSuccess(Result<List<MusicReportReason>> result) {
                    reasons.clear();
                    reasons.addAll(result.data);
                    reportPlayFailed(musicId, reasons.get(0).type, reasons.get(0).name);
                }

                @Override
                public void onFail(int i, String s) {
                    ToastUtil.show(s);
                }
            });
        } else {
            reportPlayFailed(musicId, reasons.get(0).type, reasons.get(0).name);
        }
    }

    private void reportPlayFailed(int musicId, int type, String reason) {
        MusicApi.report(musicId, type, reason, new LifeDataCallback<>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<String> result) {
                TimeLogger.msg("report play success");
            }

            @Override
            public void onFail(int i, String s) {
                TimeLogger.err("report play music error failed: " + s);
            }
        });
    }

    private static final IMap<MusicReportReason, String> I_MAP = musicReportReason -> musicReportReason.name;
}
