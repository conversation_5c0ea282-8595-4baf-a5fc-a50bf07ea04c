package com.wepie.wespy.module.voiceroom.rocket.result;

import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.huiwan.widget.rv.BaseRvAdapter;
import com.huiwan.widget.rv.RVHolder;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketRecordInfo;

/**
 * date 2019/1/9
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class RocketAllAwardAdapter extends BaseRvAdapter<RocketRecordInfo, RVHolder> {

    private int rid;

    public void setRid(int rid) {
        this.rid = rid;
    }

    @NonNull
    @Override
    public RVHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new RVHolder(new RocketAllAwardItem(parent.getContext()));
    }

    @Override
    public void onBindViewHolder(@NonNull RVHolder holder, int position) {
        ((RocketAllAwardItem) holder.itemView).onBind(dataList.get(position), rid, 0);
    }
}
