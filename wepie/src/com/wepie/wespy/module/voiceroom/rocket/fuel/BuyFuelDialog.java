package com.wepie.wespy.module.voiceroom.rocket.fuel;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.HwApi;
import com.huiwan.user.LoginHelper;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.model.event.RefreshSelfSuccessEvent;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.rocket.RocketViewModel;
import com.wepie.wespy.module.voiceroom.rocket.model.FuelInfo;
import com.wepie.wespy.net.http.api.StarRocketApi;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class BuyFuelDialog extends ConstraintLayout {
    private TextView myFuelTv;
    private TextView myCoinTv;
    private ImageView closeIv;
    private LinearLayout buyCoinLay;
    private RecyclerView recyclerView;
    private FuelGoodsAdapter adapter;
    private CallBack callBack;

    public BuyFuelDialog(@NonNull Context context) {
        super(context);
        initView();
        EventBus.getDefault().register(this);
    }

    public BuyFuelDialog(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public void setCallBack(CallBack callBack) {
        this.callBack = callBack;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRefreshSelfSuccess(RefreshSelfSuccessEvent event) {
        myCoinTv.setText(String.valueOf(LoginHelper.getCoin()));
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.buy_fuel_dialog, this);
        myFuelTv = findViewById(R.id.my_fuel_tv);
        myCoinTv = findViewById(R.id.my_coin_tv);
        buyCoinLay = findViewById(R.id.buy_fuel_coin_lay);
        recyclerView = findViewById(R.id.buy_fuel_rv);
        closeIv = findViewById(R.id.close_iv);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        adapter = new FuelGoodsAdapter(getContext());
        recyclerView.setAdapter(adapter);
        setListener();
        loadData(false);
    }

    private void setListener() {
        adapter.setCallback(() -> {
            loadData(true);
        });
        buyCoinLay.setOnClickListener(v -> {
            ApiService.of(HwApi.class).gotoGoodsListActivity(v.getContext());
        });
        closeIv.setOnClickListener(v -> {
            if (callBack != null) {
                callBack.close();
            }
        });
    }

    @Override
    protected void onDetachedFromWindow() {
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        super.onDetachedFromWindow();
    }

    private void loadData(boolean onlyRefreshMine) {
        StarRocketApi.getFuelInfo(LoginHelper.getLoginUid(), new LifeDataCallback<FuelInfo>(this) {
            @Override
            public void onSuccess(Result<FuelInfo> result) {
                FuelInfo fuelInfo = result.data;
                myFuelTv.setText(getContext().getResources().getString(R.string.my_fuel, fuelInfo.getLeftFuel()));
                if (onlyRefreshMine) {
                    EventDispatcher.postMyFuelNumChangedEvent(fuelInfo.getLeftFuel());
                    RocketViewModel.get(getContext()).setMyFuelNum(fuelInfo.getLeftFuel());
                } else {
                    adapter.setFuelGoodsList(fuelInfo.getFuelGoodsList());
                    adapter.notifyDataSetChanged();
                }
            }

            @Override
            public void onFail(int code, String msg) {

            }
        });
        myCoinTv.setText(String.valueOf(LoginHelper.getCoin()));
    }

    public static void show(Context context) {
        BuyFuelDialog buyFuelDialog = new BuyFuelDialog(context);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_theme_full_no_bg);
        dialog.setContentView(buyFuelDialog);
        dialog.initCenterDialog();
        dialog.setCanceledOnTouchOutside(true);
        buyFuelDialog.setCallBack(dialog::dismiss);
        dialog.show();
    }

    private interface CallBack {
        void close();
    }

}
