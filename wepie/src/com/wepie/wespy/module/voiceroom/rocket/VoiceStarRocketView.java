package com.wepie.wespy.module.voiceroom.rocket;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.utils.widget.ImageFilterView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentResultListener;
import androidx.lifecycle.ViewModelProvider;

import com.huiwan.anim.SVGAUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.opensource.svgaplayer.SVGACallback;
import com.opensource.svgaplayer.SVGADynamicEntity;
import com.opensource.svgaplayer.SVGAImageView;
import com.wejoy.weplay.ex.cancellable.Timer;
import com.wepie.lib.api.plugins.track.config.cn.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IStarRocketAnimView;
import com.wepie.wespy.module.voiceroom.rocket.fuel.BuyFuelDialog;
import com.wepie.wespy.module.voiceroom.rocket.fuel.RocketFuelDialogView;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketConfig;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketStatus;
import com.wepie.wespy.module.voiceroom.rocket.util.RocketSnackBar;
import com.wepie.wespy.voiceroom.giftgame.GiftGameViewModel;

import java.util.TimerTask;

/**
 * 语音房小火箭
 *
 * <AUTHOR>
 * date 2021/05/07
 */
public class VoiceStarRocketView extends Fragment {

    private TextView tipsTv, arrowTipsTv;
    private SVGAImageView rocketSVGA;
    private final SVGADynamicEntity svgaDynamicEntity = new SVGADynamicEntity();
    private RocketStatus rocketStatus = new RocketStatus();
    private Timer timer;
    private int playTimes = 0;
    private RocketSnackBar snackBar;
    private ImageFilterView headIv;
    private long lastClickTimes = 0;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.voice_star_rocket_view, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
        initData();
    }

    private void initView(View view) {
        LinearLayout tipsLay = view.findViewById(R.id.rocket_tips_lay);
        tipsTv = view.findViewById(R.id.rocket_tips_tv);
        arrowTipsTv = view.findViewById(R.id.rocket_arrow_tips_tv);
        rocketSVGA = view.findViewById(R.id.rocket_svga);
        headIv = view.findViewById(R.id.head_iv);
        ImageView rocketIv = view.findViewById(R.id.rocket_icon_iv);
        View.OnClickListener showDialogClickListener = v -> {
            if (System.currentTimeMillis() - lastClickTimes > 1000) {
                lastClickTimes = System.currentTimeMillis();
                ShenceEvent.appClickWithBtnName(TrackScreenName.VOICE_ROOM_CHAT, TrackButtonName.ROCKET_FUEL_DETAIL);
                showFuelDialog();
            }
        };
        tipsLay.setOnClickListener(showDialogClickListener);
        rocketIv.setOnClickListener(showDialogClickListener);
    }

    private void initData() {
        RocketViewModel rocketViewModel = new ViewModelProvider(requireActivity()).get(RocketViewModel.class);
        rocketViewModel.getRocketStatus().observe(getViewLifecycleOwner(), rocketStatus -> {
            if (VoiceStarRocketView.this.rocketStatus.getSendUid() != rocketStatus.getSendUid()) {
                getSenderInfo(rocketStatus.getSendUid());
            }
            VoiceStarRocketView.this.rocketStatus = rocketStatus;
            starTimer();
            VoicePluginService.getPlugin(IStarRocketAnimView.class).updateRocketStatus(rocketStatus);
        });
        rocketViewModel.init(new ViewModelProvider(requireActivity()).get(GiftGameViewModel.class));
        FragmentManager manager = getParentFragmentManager();
        FragmentResultListener listener = (requestKey, result) -> {
            switch (requestKey) {
                case "fuelNotEnough":
                    BuyFuelDialog.show(getContext());
                    break;
                case "addFuel":
                    addFuel(result);
                    break;
                default:
            }

        };
        manager.setFragmentResultListener("fuelNotEnough", getViewLifecycleOwner(), listener);
        manager.setFragmentResultListener("addFuel", getViewLifecycleOwner(), listener);
    }

    private void getSenderInfo(int uid) {
        headIv.setOnClickListener(v -> JumpUtil.enterUserInfoDetailFromVoiceRoom(requireContext(), uid, VoiceRoomService.getInstance().getRid()));
        setSVGA();
        UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                HeadImageLoader.loadNormalHeadImageNoRound(simpleInfo.headimgurl, headIv);
            }

            @Override
            public void onUserInfoFailed(String description) {
                headIv.setImageResource(R.drawable.default_head_icon);
            }
        });
    }

    private void addFuel(@NonNull Bundle result) {
        int fuelCriticalHit = result.getInt("fuelCriticalHit");
        if (fuelCriticalHit > 0) {
            String text = ResUtil.getResource().getString(R.string.fuel_critical_tip, result.getInt("giftNum"), fuelCriticalHit);
            RocketSnackBar.showSnackBar(requireView(), text, 1000);
            HLog.d("", "rocket need ext anim");
        }
        playTimes++;
        playTimes = Math.min(playTimes, 5);
        if (!rocketSVGA.isAnimating()) {
            rocketSVGA.startAnimation();
        }
    }

    private void setSVGA() {
        playTimes = 0;
        rocketSVGA.setLoops(0);
        rocketSVGA.setCallback(new SVGACallback() {
            @Override
            public void onPause() {

            }

            @Override
            public void onFinished() {

            }

            @Override
            public void onRepeat() {
                playTimes--;
            }

            @Override
            public void onStep(int i, double v) {
                if (i == 0 && playTimes <= 0) {
                    rocketSVGA.pauseAnimation();
                }
            }
        });
        SVGAUtil.playSvga(RocketConfig.ROCKET_ADD_FUEL_SVGA, rocketSVGA, svgaDynamicEntity);
    }

    private void updateView(RocketStatus rocketStatus) {
        Context mContext = getContext();
        if (mContext == null) {
            return;
        }
        int status = rocketStatus.getStatus();
        int remainTime = rocketStatus.getLeftTimes();
        String timeString = TimeUtil.formTotalTime(remainTime);
        if (status == RocketStatus.FUEL_WAITING) {
            tipsTv.setText(timeString);
            arrowTipsTv.setText(ResUtil.getStr(R.string.fuel_progress, rocketStatus.getCurrentProgress() + "/" + rocketStatus.getTotalProgress()));
        } else if (status == RocketStatus.FUEL_FULL) {
            tipsTv.setText(R.string.fuel_full);
            arrowTipsTv.setText(mContext.getString(R.string.ready_to_fly, remainTime));
        } else if (status == RocketStatus.FUEL_NOT_ENOUGH) {
            tipsTv.setText(R.string.fuel_not_enough);
            arrowTipsTv.setText(mContext.getString(R.string.ready_to_return, remainTime));
        } else if (status == RocketStatus.FINAL_STAR) {
            tipsTv.setText(R.string.reach_ending);
            arrowTipsTv.setText(mContext.getString(R.string.times_to_return, timeString));
        } else if (status == RocketStatus.ENDING) {
            this.rocketStatus = new RocketStatus();
            resetRocket();
        } else {
            resetRocket();
        }
        if (rocketStatus.getStatus() == RocketStatus.FUEL_NOT_ENOUGH || rocketStatus.getStatus() == RocketStatus.FINAL_STAR) {
            if (remainTime == 3) {
                snackBar = RocketSnackBar.showSnackBar(requireView(), ResUtil.getStr(R.string.rocket_toast_time, remainTime), 3000);
            } else if (remainTime < 3) {
                if (snackBar != null && snackBar.isShow())
                    snackBar.setText(ResUtil.getStr(R.string.rocket_toast_time, remainTime));
            }
        }
    }

    private void resetRocket() {
        releaseTimer();
        playTimes = 0;
    }

    private void starTimer() {
        if (timer == null) {
            timer = new Timer();
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    ThreadUtil.runOnUiThread(() -> updateView(rocketStatus));
                }
            }, 0, 1000);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        releaseTimer();
    }

    private void releaseTimer() {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }

    private void showFuelDialog() {
        Context mContext = requireContext();
        RocketDialogContainerView rocketDialogContainerView = new RocketDialogContainerView(mContext);
        RocketFuelDialogView rocketFuelDialogView = new RocketFuelDialogView(mContext);
        rocketDialogContainerView.addDialogView(rocketFuelDialogView, ScreenUtil.dip2px(340), ViewGroup.LayoutParams.WRAP_CONTENT);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(mContext, R.style.dialog_style_custom);
        rocketFuelDialogView.updateView(rocketStatus);
        rocketDialogContainerView.setDialog(dialog);
        dialog.setContentView(rocketDialogContainerView);
        dialog.initBottomDialog();
        dialog.setCanceledOnTouchOutside(true);
        dialog.show();
    }
}
