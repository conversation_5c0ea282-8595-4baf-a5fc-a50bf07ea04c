package com.wepie.wespy.module.voiceroom.rocket.fuel;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.util.PressUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.lib.api.ApiService;
import com.huiwan.user.LoginHelper;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.lib.api.plugins.track.TrackApi;
import com.wepie.lib.api.plugins.track.config.cn.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.module.voiceroom.rocket.model.FuelGoods;
import com.wepie.wespy.net.http.api.StarRocketApi;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FuelGoodsAdapter extends RecyclerView.Adapter<FuelGoodsAdapter.ViewHolder> {
    private List<FuelGoods> fuelGoodsList = new ArrayList<>();
    private final Context context;
    private Callback callback;

    public FuelGoodsAdapter(Context context) {
        this.context = context;
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public void setFuelGoodsList(List<FuelGoods> fuelGoodsList) {
        this.fuelGoodsList = fuelGoodsList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(context).inflate(R.layout.buy_fuel_list_item, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        FuelGoods fuelGoods = fuelGoodsList.get(position);
        holder.fuelPriceTv.setText(context.getString(R.string.fuel_price, fuelGoods.getPrice()));
        holder.fuelNameTv.setText(fuelGoods.getName());
        ImageLoaderUtil.loadNormalImage(fuelGoods.getImgUrl(), holder.fuelIv);
        PressUtil.addPressEffect(holder.buyTv);
        holder.buyTv.setOnClickListener(v -> StarRocketApi.buyFuel(LoginHelper.getLoginUid(), fuelGoods.getId(), new LifeDataCallback<>(holder.itemView) {
            @Override
            public void onSuccess(Result<Object> result) {
                Map<String, Object> map = new HashMap<>();
                map.put("fuel_name", fuelGoods.getName());
                map.put("use_coin", fuelGoods.getPrice());
                ApiService.of(TrackApi.class).appClick(TrackScreenName.VOICE_ROOM_CHAT, TrackButtonName.ROCKET_FUEL_BUG, map);
                BuyFuelSuccessDialog.show(context, fuelGoods);
                if (callback != null) {
                    callback.buySuccess();
                }
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        }));
    }

    @Override
    public int getItemCount() {
        return fuelGoodsList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder{
        private final ImageView fuelIv;
        private final TextView fuelNameTv;
        private final TextView fuelPriceTv;
        private final TextView buyTv;
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            fuelIv = itemView.findViewById(R.id.fuel_iv);
            fuelNameTv = itemView.findViewById(R.id.fuel_name_tv);
            fuelPriceTv = itemView.findViewById(R.id.fuel_price_tv);
            buyTv = itemView.findViewById(R.id.buy_tv);
        }
    }

    interface Callback{
        void buySuccess();
    }
}
