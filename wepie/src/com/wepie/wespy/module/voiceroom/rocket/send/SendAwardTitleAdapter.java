package com.wepie.wespy.module.voiceroom.rocket.send;

import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.widget.rv.BaseRvAdapter;
import com.huiwan.widget.rv.RVHolder;
import com.wepie.wespy.R;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketStar;

public class SendAwardTitleAdapter extends BaseRvAdapter<RocketStar, SendAwardTitleAdapter.SendAwardTitleViewHolder> {

    private int selectPosition = 0;
    private Callback callback;

    public int getSelectPosition() {
        return selectPosition;
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    @NonNull
    @Override
    public SendAwardTitleViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new SendAwardTitleViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.rocket_send_award_title_item, null));
    }

    @Override
    public void onBindViewHolder(@NonNull SendAwardTitleViewHolder holder, int position) {
        holder.update(dataList.get(position).getStarName(), selectPosition == position, dataList.size() - 1 == position, () -> {
            selectPosition = holder.getBindingAdapterPosition();
            notifyDataSetChanged();
            if (callback != null) callback.onClick();
        });
    }

    public static class SendAwardTitleViewHolder extends RVHolder {
        private final TextView nameTv;
        private final View lineView;
        private final LinearLayout rootView;

        public SendAwardTitleViewHolder(View view) {
            super(view);
            nameTv = view.findViewById(R.id.name_tv);
            lineView = view.findViewById(R.id.line_view);
            rootView = view.findViewById(R.id.root_view);
        }

        public void update(String name, boolean select, boolean isEnd, Callback callback) {
            itemView.setOnClickListener(v -> {
                if (callback != null) callback.onClick();
            });
            nameTv.setText(name);
            if (select) {
                nameTv.setTextColor(0xff2ddcf4);
                nameTv.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
                lineView.setVisibility(View.VISIBLE);
            } else {
                nameTv.setTextColor(0x96ffffff);
                nameTv.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
                lineView.setVisibility(View.INVISIBLE);
            }
            LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) rootView.getLayoutParams();
            if (isEnd) {
                layoutParams.setMarginEnd(ScreenUtil.dip2px(16));
            } else {
                layoutParams.setMarginEnd(ScreenUtil.dip2px(4));
            }
        }
    }

    public interface Callback {
        void onClick();
    }
}
