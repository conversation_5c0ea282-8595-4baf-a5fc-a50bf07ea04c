package com.wepie.wespy.module.voiceroom.rocket.fuel;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Outline;
import android.os.Build;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.huiwan.anim.AnimatorUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.gift.send.ComboInfo;
import com.huiwan.component.gift.send.GiftSendInfo;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.lib.api.ApiService;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.user.LoginHelper;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wejoy.weplay.ex.cancellable.Timer;
import com.wepie.lib.api.plugins.track.TrackApi;
import com.wepie.lib.api.plugins.track.config.cn.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.model.event.voice.BuyFuelEvent;
import com.wepie.wespy.model.event.voice.RocketStatusChangedEvent;
import com.wepie.wespy.module.gift.IGiftCombo;
import com.wepie.wespy.module.voiceroom.dataservice.RoomSenderPresenter;
import com.wepie.wespy.module.voiceroom.dataservice.SendGiftCallback;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService;
import com.wepie.wespy.module.voiceroom.rocket.RocketDialogContainerView;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketStatus;
import com.wepie.wespy.module.voiceroom.rocket.send.RocketSendAwardView;
import com.wepie.wespy.module.voiceroom.rocket.send.RocketSendHelpView;
import com.wepie.wespy.module.voiceroom.rocket.supply.RocketSupplyDetailView;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.text.DecimalFormat;
import java.text.Format;
import java.util.TimerTask;

public class RocketFuelDialogView extends RelativeLayout {
    private TextView currentStarTv;
    private TextView supplyProTv;
    private TextView timeHourTv;
    private TextView timeMinTv;
    private TextView timeSecTv;
    private TextView supplyOneTv;
    private TextView supplyTenTv;
    private TextView supplyHundredTv;
    private TextView myFuelTv;
    private TextView mySupplyNumTv;
    private TextView rewardPreviewTv;
    private TextView rateCriticalTv;
    private ProgressBar currentSupplyBar;
    private ConstraintLayout progressLay;
    private ImageView progressIv;
    private Timer timer;
    private ValueAnimator valueAnimator;
    private RocketStatus rocketStatus = new RocketStatus();
    private long lastClickTimes = 0;


    public RocketFuelDialogView(Context context) {
        super(context);
        initView();
    }

    public RocketFuelDialogView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRocketStatusChanged(RocketStatusChangedEvent event) {
        updateView(event.rocketStatus);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onFuelBuy(BuyFuelEvent event) {
        setMyFuelNum(event.myFuelNum);
    }

    public void updateView(RocketStatus rocketStatus) {
        this.rocketStatus = rocketStatus;
        if (rocketStatus.getStatus() == RocketStatus.ENDING) {
            releaseTimer();
            return;
        }
        starTimer();
        currentSupplyBar.setProgress((int) (rocketStatus.getProcess() * 100));
        if ((rocketStatus.getProcess() * 100) < 1) {
            progressLay.setVisibility(INVISIBLE);
        } else {
            progressLay.setVisibility(VISIBLE);
            progressLay.getLayoutParams().width = (int) (ScreenUtil.dip2px(331) * rocketStatus.getProcess());
        }
        progressAnimateOn();
        if (rocketStatus.getCurrentStar() >= RocketStatus.CORAL) {
            currentStarTv.setText(getContext().getString(R.string.reach_to_end_star, rocketStatus.getCurrentStarName()));
            supplyProTv.setText(R.string.progress_fly_end);
            supplyProTv.setBackgroundResource(R.drawable.shape_595ace_corner10);
        } else {
            currentStarTv.setText(getContext().getString(R.string.reach_to_star, rocketStatus.getCurrentStarName()));
            supplyProTv.setText(getContext().getString(R.string.supply_progress, rocketStatus.getCurrentProgress() + "/" + rocketStatus.getTotalProgress()));
            supplyProTv.setBackgroundResource(R.drawable.transparent);
        }
        myFuelTv.setText(getContext().getString(R.string.my_fuel, rocketStatus.getMyFuelNum()));
        setSupplyString();
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.rocket_fuel_dialog_view, this);
        currentStarTv = findViewById(R.id.title_tv);
        supplyProTv = findViewById(R.id.supply_pro_tv);
        timeHourTv = findViewById(R.id.fuel_timer_hour_tv);
        timeMinTv = findViewById(R.id.fuel_timer_min_tv);
        timeSecTv = findViewById(R.id.fuel_timer_sec_tv);
        supplyOneTv = findViewById(R.id.fuel_supply_one_tv);
        supplyTenTv = findViewById(R.id.fuel_supply_ten_tv);
        supplyHundredTv = findViewById(R.id.fuel_supply_hundred_tv);
        myFuelTv = findViewById(R.id.my_fuel_tv);
        mySupplyNumTv = findViewById(R.id.my_supply_time_tv);
        rewardPreviewTv = findViewById(R.id.reward_preview_tv);
        currentSupplyBar = findViewById(R.id.supply_progress);
        rateCriticalTv = findViewById(R.id.fuel_rate_critical_tv);
        progressLay = findViewById(R.id.progress__cover_lay);
        progressIv = findViewById(R.id.progress_iv);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            progressLay.setOutlineProvider(new ViewOutlineProvider() {
                @Override
                public void getOutline(View view, Outline outline) {
                    outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), ScreenUtil.dip2px(9));
                }
            });
            progressLay.setClipToOutline(true);
        }
        setListener();
    }

    private void setListener() {
        supplyOneTv.setOnClickListener(v -> supplyFuel(1));
        supplyTenTv.setOnClickListener(v -> supplyFuel(10));
        supplyHundredTv.setOnClickListener(v -> supplyFuel(100));
        rateCriticalTv.setOnClickListener(v -> showHelpDialog());
        rewardPreviewTv.setOnClickListener(v -> {
            if (System.currentTimeMillis() - lastClickTimes > 1000) {
                lastClickTimes = System.currentTimeMillis();
                showRewardDialog();
            }
        });
        mySupplyNumTv.setOnClickListener(v -> {
            if (System.currentTimeMillis() - lastClickTimes > 1000) {
                lastClickTimes = System.currentTimeMillis();
                ApiService.of(TrackApi.class).appClick(TrackScreenName.VOICE_ROOM_CHAT, TrackButtonName.ROCKET_DETAIL);
                showSupplyDialog();
            }
        });
        myFuelTv.setOnClickListener(v -> showBuyFuel());
    }

    private void showHelpDialog() {
        RocketSendHelpView.showPureHelpDialog(getContext());
    }

    private void starTimer() {
        if (timer == null) {
            timer = new Timer();
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    ThreadUtil.runOnUiThread(() -> updateTime());
                }
            }, 0, 1000);
        }
    }

    private void updateTime() {
        Format format = new DecimalFormat("00");
        timeHourTv.setText(format.format(rocketStatus.getLeftTimeHour()));
        timeMinTv.setText(format.format(rocketStatus.getLeftTimeMin()));
        timeSecTv.setText(format.format(rocketStatus.getLeftTimeSec()));
    }

    private void setSupplyString() {
        String mySupplyNumStr = String.valueOf(rocketStatus.getMySupplyNum());
        String totalSupplyNumStr = String.valueOf(rocketStatus.getTotalSupplyNum());
        String str = ResUtil.getStr(R.string.my_supply_time, mySupplyNumStr, totalSupplyNumStr);
        int firstStartIndex = str.indexOf(mySupplyNumStr);//找到第一个填充文案起始位置
        int secondStartIndex = str.indexOf(totalSupplyNumStr);//找到第二个填充文案起始位置
        int firstEndIndex = firstStartIndex;//找到第一个填充文案结束位置
        int secondEndIndex = secondStartIndex;//找到第二个填充文案结束位置
        int preLength = mySupplyNumStr.length();//第一个填充文案长度
        int sufLength = totalSupplyNumStr.length();//第二个填充文案长度
        //判断填充文案哪个在前，后面的填充文案其实位置需要校正
        if (firstStartIndex < secondStartIndex) {
            secondStartIndex = secondStartIndex + preLength - 4;
        } else {
            firstStartIndex = firstStartIndex + sufLength - 4;
        }
        if (firstStartIndex < 0) {
            firstStartIndex = 0;
        }
        if (secondStartIndex < 0) {
            secondStartIndex = 0;
        }
        firstEndIndex = firstStartIndex + preLength;
        secondEndIndex = secondStartIndex + sufLength;

        SpannableString sp = new SpannableString(str);
        sp.setSpan(new ForegroundColorSpan(Color.parseColor("#ffffffff")), firstStartIndex,
                firstEndIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        sp.setSpan(new ForegroundColorSpan(Color.parseColor("#ffffffff")), secondStartIndex,
                secondEndIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        mySupplyNumTv.setText(sp);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        EventDispatcher.registerEventObserver(this);
    }

    @Override
    protected void onDetachedFromWindow() {
        releaseRes();
        super.onDetachedFromWindow();
    }

    private void releaseRes() {
        EventDispatcher.unregisterEventObserver(this);
        releaseTimer();
        if (valueAnimator != null) {
            valueAnimator.end();
            valueAnimator = null;
        }
    }

    private void releaseTimer() {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }

    private void progressAnimateOn() {
        if (valueAnimator == null) {
            int transWidth = ScreenUtil.dip2px((float) 18.5);
            valueAnimator = AnimatorUtil.getAnimator(true, ValueAnimator.INFINITE, 500, new LinearInterpolator());
            valueAnimator.addUpdateListener(animation -> {
                float ratio = (float) animation.getAnimatedValue();
                progressIv.setTranslationX(transWidth * ratio);
            });
            valueAnimator.start();
        }
    }

    private void supplyFuel(int num) {
        GiftSendInfo sendInfo = new GiftSendInfo();
        sendInfo.giftId = Gift.GIFT_FUEL_GOOD_ID;
        sendInfo.giftNum = num;
        sendInfo.comboId = ComboInfo.makeComboId(LoginHelper.getLoginUid(), sendInfo.recUid);
        sendInfo.rid = VoiceRoomService.getInstance().getRid();
        sendInfo.setGift(ConfigHelper.getInstance().getGiftConfig().getGift(Gift.GIFT_FUEL_GOOD_ID));

        RoomSenderPresenter.sendTempRoomGift(sendInfo, new SendGiftCallback() {
            @Nullable
            @Override
            public ILife getLife() {
                return ILifeUtil.toLife(RocketFuelDialogView.this);
            }

            @Override
            public void onSuccess(int rid, String comboId, int comboTimes) {
                VoicePluginService.getPlugin(IGiftCombo.class).sendGiftSuccess(sendInfo);
                EventDispatcher.postRocketDialogDismissEvent();
            }

            @Override
            public void onFail(String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    private void showBuyFuel() {
        BuyFuelDialog.show(getContext());
    }

    private void setMyFuelNum(int myFuelNum) {
        myFuelTv.setText(getContext().getString(R.string.my_fuel, myFuelNum));
    }

    private void showRewardDialog() {
        RocketDialogContainerView rocketDialogContainerView = new RocketDialogContainerView(getContext());
        RocketSendAwardView rocketSendAwardView = new RocketSendAwardView(getContext());
        rocketSendAwardView.refreshData();
        rocketDialogContainerView.addDialogView(rocketSendAwardView, ScreenUtil.dip2px(512), ViewGroup.LayoutParams.WRAP_CONTENT);
        rocketDialogContainerView.setBg(R.drawable.rocket_reward_bg);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(getContext(), R.style.dialog_no_floating);
        rocketDialogContainerView.setDialog(dialog);
        dialog.setContentView(rocketDialogContainerView);
        dialog.initBottomDialog();
        dialog.setCanceledOnTouchOutside(true);
        rocketSendAwardView.setCallback(dialog::dismiss);
        dialog.show();
    }

    private void showSupplyDialog() {
        RocketDialogContainerView rocketDialogContainerView = new RocketDialogContainerView(getContext());
        RocketSupplyDetailView rocketSupplyDetailView = new RocketSupplyDetailView(getContext());
        rocketDialogContainerView.addDialogView(rocketSupplyDetailView, ScreenUtil.dip2px(340), ViewGroup.LayoutParams.WRAP_CONTENT);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(getContext(), R.style.dialog_no_floating);
        rocketDialogContainerView.setDialog(dialog);
        dialog.setContentView(rocketDialogContainerView);
        dialog.initBottomDialog();
        dialog.setCanceledOnTouchOutside(true);
        rocketSupplyDetailView.setCallback(dialog::dismiss);
        dialog.show();
    }
}
