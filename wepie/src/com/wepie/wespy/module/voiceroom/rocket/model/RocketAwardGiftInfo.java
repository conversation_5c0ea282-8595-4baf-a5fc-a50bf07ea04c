package com.wepie.wespy.module.voiceroom.rocket.model;

import com.google.gson.annotations.SerializedName;
import com.wepie.wespy.net.tcp.packet.Interstellar;

import java.util.ArrayList;
import java.util.List;

public class RocketAwardGiftInfo {
    @SerializedName("prop_id")
    public int propId = 0;
    @SerializedName("valid_days")
    public int validDays = 0;
    @SerializedName("count")
    public int count = 0;
    @SerializedName("extra_prop_id")
    public int extraPropId = 0;

    public RocketAwardGiftInfo() {
    }

    public static RocketAwardGiftInfo parse(Interstellar.GiftProp giftProp) {
        RocketAwardGiftInfo info = new RocketAwardGiftInfo();
        info.propId = giftProp.getPropId();
        info.validDays = giftProp.getValidDays();
        info.count = giftProp.getCount();
        return info;
    }

    public static List<RocketAwardGiftInfo> parseList(List<Interstellar.GiftProp> giftPropList) {
        List<RocketAwardGiftInfo> giftInfoList = new ArrayList<>();
        for (Interstellar.GiftProp giftProp : giftPropList) {
            giftInfoList.add(parse(giftProp));
        }
        return giftInfoList;
    }
}
