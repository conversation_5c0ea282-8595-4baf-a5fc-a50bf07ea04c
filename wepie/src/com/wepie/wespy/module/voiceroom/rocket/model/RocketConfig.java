package com.wepie.wespy.module.voiceroom.rocket.model;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.google.gson.annotations.SerializedName;
import com.huiwan.base.LibBaseUtil;
import com.huiwan.store.file.FileCacheName;
import com.huiwan.store.file.FileConfig;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.GlobalLife;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.lifecycle.LiveDataExKt;
import com.wepie.download.DownloadUtil;
import com.wepie.wespy.net.http.api.StarRocketApi;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class RocketConfig {
    public static volatile RocketConfig instance;

    @SerializedName("destinations")
    private List<RocketStar> rocketConfigList = new ArrayList<>();

    @SerializedName("rocket_cross_bg")
    private String rocketCrossBg = "";

    @SerializedName("rocket_gift_rule_url")
    private String rocketGiftRuleUrl = "";

    private transient final MutableLiveData<Boolean> inited = new MutableLiveData<>(false);

    public static final String ROCKET_ADD_FUEL_SVGA = "svga/voiceroom/rocket_add_fuel.svga";

    public static RocketConfig getInstance() {
        if (instance == null) {
            instance = new RocketConfig();
        }
        return instance;
    }

    public void init() {
        if (isValid()) {
            return;
        }
        StarRocketApi.getRocketConfig(new LifeDataCallback<>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<RocketConfig> result) {
                rocketConfigList = result.data.rocketConfigList;
                rocketCrossBg = result.data.rocketCrossBg;
                rocketGiftRuleUrl = result.data.rocketGiftRuleUrl;
                inited.setValue(true);
            }

            @Override
            public void onFail(int code, String msg) {

            }
        });
    }

    public void observerInitStatus(ILife life, Observer<Boolean> observer) {
        LiveDataExKt.observe(inited, life, observer);
    }

    public boolean isValid() {
        return rocketConfigList != null && !rocketConfigList.isEmpty();
    }

    public List<RocketStar> getRocketConfigList() {
        return rocketConfigList;
    }

    public RocketStar getStarById(int starId) {
        if (!isValid()) {
            init();
        }
        for (RocketStar rocketStar : rocketConfigList) {
            if (rocketStar.getStarId() == starId) {
                return rocketStar;
            }
        }
        return new RocketStar();
    }

    public void checkToDownload() {
        DownloadUtil.downloadFile(rocketCrossBg, getRocketCrossAnimPath(), null);
    }

    public String getRocketCrossBg() {
        return rocketCrossBg;
    }

    public String getRocketGiftRuleUrl() {
        return rocketGiftRuleUrl;
    }

    public static String getRocketCrossAnimPath() {
        return FileConfig.getPropDirPath(FileCacheName.VOICE_ROOM_BG + File.separator + "rocket");
    }
}
