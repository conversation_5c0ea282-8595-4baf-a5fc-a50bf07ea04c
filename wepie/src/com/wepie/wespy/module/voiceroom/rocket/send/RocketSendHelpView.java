package com.wepie.wespy.module.voiceroom.rocket.send;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.module.webview.WespyWebView;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.module.voiceroom.rocket.RocketDialogContainerView;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketConfig;

public class RocketSendHelpView extends FrameLayout {

    private Callback callback;
    private WespyWebView webView;

    public RocketSendHelpView(@NonNull Context context) {
        super(context);
        initView();
    }

    public RocketSendHelpView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.rocket_send_help_view, this);
        findViewById(R.id.back_iv).setOnClickListener(v -> {
            if (callback != null) callback.onBack();
        });
        String url = RocketConfig.getInstance().getRocketGiftRuleUrl();
        webView = findViewById(R.id.web_view);
        webView.initCore(false);
        webView.hideBackground();
        webView.initWebView(url, false);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (webView != null) {
            webView.stopLoading();
            webView.removeAllViews();
            webView.releaseRes();
            webView.destroy();
        }
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public void helpOnly() {
        findViewById(R.id.back_iv).setVisibility(GONE);
    }

    public interface Callback {
        void onBack();
    }

    public static void showPureHelpDialog(Context context) {
        RocketDialogContainerView rocketDialogContainerView = new RocketDialogContainerView(context);
        RocketSendHelpView contentView = new RocketSendHelpView(context);
        contentView.helpOnly();
        int height = ScreenUtil.dip2px(512);
        int viewHeight = height - ScreenUtil.dip2px(30);
        rocketDialogContainerView.addDialogView(contentView, height, viewHeight);
        rocketDialogContainerView.setBg(R.drawable.rocket_reward_bg);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_no_floating);
        rocketDialogContainerView.setDialog(dialog);
        dialog.setContentView(rocketDialogContainerView);
        dialog.initBottomDialog();
        dialog.show();
    }
}
