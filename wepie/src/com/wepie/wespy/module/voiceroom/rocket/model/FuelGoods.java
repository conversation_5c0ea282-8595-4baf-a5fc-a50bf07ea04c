package com.wepie.wespy.module.voiceroom.rocket.model;

import com.google.gson.annotations.SerializedName;

public class FuelGoods {
    @SerializedName("id")
    private int id;
    @SerializedName("icon")
    private String imgUrl;
    @SerializedName("name")
    private String name;
    @SerializedName("coin")
    private int price;

    public FuelGoods() {
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }
}
