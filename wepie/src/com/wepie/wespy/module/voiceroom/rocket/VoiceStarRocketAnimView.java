package com.wepie.wespy.module.voiceroom.rocket;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.util.AttributeSet;

import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;

import com.huiwan.base.lifecycle.FullLifecycleObserver;
import com.huiwan.base.lifecycle.FullLifecycleObserverAdapter;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.glview.FrameSprite;
import com.huiwan.glview.SpriteGLView;
import com.huiwan.glview.VideoSprite;
import com.huiwan.store.PrefUtil;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wepie.download.DownloadUtil;
import com.wepie.download.LifeDownloadCallback;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.notice.MediaPlayerUtil;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginRelativeLayout;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IStarRocketAnimView;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketConfig;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketStatus;
import com.wepie.wespy.module.voiceroom.rocket.util.RocketSnackBar;

/**
 * 语音房火箭转场d
 * <AUTHOR>
 * date 2021/05/07
 */
public class VoiceStarRocketAnimView extends PluginRelativeLayout implements IStarRocketAnimView, FullLifecycleObserver {
    private Callback callback;
    private boolean isForeground = true;


    public VoiceStarRocketAnimView(Context context) {
        super(context);
    }

    public VoiceStarRocketAnimView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public void updateRocketStatus(RocketStatus rocketStatus) {
        if (rocketStatus.shouldPlayRocketAnim()) {
            EventDispatcher.postRocketDialogDismissEvent();
            playStarCrossing(ILifeUtil.toLife(this));
            setCallback(() -> checkShowToast(rocketStatus));
        } else {
            checkShowToast(rocketStatus);
        }

    }

    @Override
    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public void playStarCrossing(ILife life) {
        DownloadUtil.downloadFile(RocketConfig.getInstance().getRocketCrossBg(), RocketConfig.getRocketCrossAnimPath(), new LifeDownloadCallback(life) {
            @Override
            public void onSuccess(String url, String path) {
                doPlayAlphaVideo(path);
            }

            @Override
            public void onFail(String msg) {
            }

            @Override
            public void onPercent(int percent) {
                HLog.e("downloadVideo", "percent:" + percent);
            }
        });
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void initView() {
        Activity activity = ContextUtil.getActivityFromContext(mContext);
        if (activity instanceof LifecycleOwner) {
            Lifecycle lifecycle = ((LifecycleOwner) activity).getLifecycle();
            FullLifecycleObserverAdapter adapter = new FullLifecycleObserverAdapter(this);
            lifecycle.addObserver(adapter);
            ILife life = ILifeUtil.toLife(this);
            if (life != null) {
                life.onDestroy(() -> {
                    lifecycle.removeObserver(adapter);
                    return null;
                });
            }
        }
    }

    private void doPlayAlphaVideo(final String videoUrl) {
        if (!isForeground) {
            return;
        }
        playAudio();
        final SpriteGLView glView = new SpriteGLView(mContext);
        LayoutParams params;
        params = new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
        addView(glView, params);
        final VideoSprite videoSprite = new VideoSprite(glView, videoUrl, mContext);
        videoSprite.setOnFrameEndListener(new FrameSprite.OnFrameEndListener() {
            @Override
            public void onFrameEnd() {
                doAnimationFinish(glView, videoSprite);
            }

            @Override
            public void onFail(int what, int extra) {
                doAnimationFinish(glView, videoSprite);
            }
        });
        glView.addSprite(videoSprite);
    }

    private void doAnimationFinish(SpriteGLView glView, VideoSprite videoSprite) {
        glView.removeSprite(videoSprite);
        glView.setVisibility(GONE);
        removeAllViews();
        if (callback != null) {
            callback.onAnimEnd();
        }
    }

    @Override
    public void onPause() {
        isForeground = false;
        removeAllViews();
    }

    @Override
    public void onResume() {
        isForeground = true;
    }

    private void playAudio() {
        boolean open = PrefUtil.getInstance().getBoolean(PrefUtil.KEY_VOICE_GIFT_AUDIO_OPEN, true);
        if (open) {
            MediaPlayerUtil.playRawRes(R.raw.rocket_cross, false);
        }
    }

    @Override
    protected void initData() {

    }

    private void checkShowToast(RocketStatus rocketStatus) {
        if (rocketStatus.getStatus() == RocketStatus.FUEL_NOT_ENOUGH) {
            //燃料不足
            RocketSnackBar.showSnackBar(this, ResUtil.getStr(R.string.rocket_toast_back), 3000);
            return;
        }
        if (rocketStatus.getCurrentStar() == RocketStatus.ATMOSPHERE && rocketStatus.getOldStatus() == RocketStatus.START) {
            //第一次到达大气层
            RocketSnackBar.showSnackBar(this, ResUtil.getStr(R.string.rocket_toast_start, rocketStatus.getCurrentStarName(), rocketStatus.getCurrentStageTimeMin()), 3000);
            return;
        }
        if (rocketStatus.shouldPlayRocketAnim() && rocketStatus.getStatus() != RocketStatus.ENDING) {
            String text;
            if (rocketStatus.getCurrentStar() == RocketStatus.BLUE_WHALE) {
                //到达蓝鲸星系（终点）
                text = ResUtil.getResource().getString(R.string.rocket_toast_blue_whale, rocketStatus.getCurrentStarName(), rocketStatus.getCurrentStageTimeMin());
            } else if (rocketStatus.getCurrentStar() == RocketStatus.CORAL) {
                //到达珊瑚星系（终点）
                text = ResUtil.getStr(R.string.rocket_toast_coral, rocketStatus.getCurrentStarName(), rocketStatus.getCurrentStageTimeMin());
            } else if (rocketStatus.getStatus() == RocketStatus.FUEL_FULL) {
                //到达非终点，且燃料已满
                text = ResUtil.getStr(R.string.rocket_toast_fuel_full, rocketStatus.getCurrentStarName(), rocketStatus.getCurrentStageTimeSec());
            } else {
                //到达非终点，且燃料未满
                if (rocketStatus.getCurrentStar() == RocketStatus.BLACK_HOLE) {
                    text = ResUtil.getResource().getString(R.string.rocket_toast_start_black_hole, rocketStatus.getCurrentStageTimeMin());
                } else {
                    text = ResUtil.getStr(R.string.rocket_toast_start, rocketStatus.getCurrentStarName(), rocketStatus.getCurrentStageTimeMin());
                }
            }
            RocketSnackBar.showSnackBar(this, text, 3000);
        } else if (rocketStatus.getStatus() == RocketStatus.FUEL_FULL && rocketStatus.getOldStatus() != RocketStatus.FUEL_FULL) {
            String text = ResUtil.getStr(R.string.rocket_toast_current_fuel_full, rocketStatus.getCurrentStageTimeSec());
            RocketSnackBar.showSnackBar(this, text, 3000);
        }
    }

}
