package com.wepie.wespy.module.voiceroom.rocket.util;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.google.android.material.snackbar.Snackbar;
import com.wepie.wespy.R;

public class RocketSnackBar {
    private Snackbar snackbar;
    private TextView textView;

    public RocketSnackBar() {

    }

    public void setText(String text) {
        textView.setText(text);
    }

    @SuppressLint("ClickableViewAccessibility")
    public void show(View root, String text, int duration) {
        if (!root.isAttachedToWindow()) {
            return;
        }
        snackbar = Snackbar.make(root, "", duration);
        ViewGroup layout = (ViewGroup) snackbar.getView();
        ViewGroup snackParent = (ViewGroup) layout.getParent();
        if (snackParent != null) {
            snackParent.removeView(layout);
        }
        layout.removeAllViews();
        layout.setBackgroundColor(Color.TRANSPARENT);
        layout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        View itemView = LayoutInflater.from(root.getContext()).inflate(R.layout.rocket_toast_view, null);
        textView = itemView.findViewById(R.id.toast_tv);
        textView.setText(text);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        layout.addView(itemView, params);
        layout.setOnTouchListener((v, event) -> false);
        if (root instanceof ViewGroup) {
            ((ViewGroup) root).addView(layout, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        }
        snackbar.setAnimationMode(Snackbar.ANIMATION_MODE_FADE);
        snackbar.show();
    }

    public boolean isShow() {
        return snackbar.isShown();
    }

    public static RocketSnackBar showSnackBar(View root, String text, int duration) {
        final RocketSnackBar bar = new RocketSnackBar();
        bar.show(root, text, duration);
        return bar;
    }
}
