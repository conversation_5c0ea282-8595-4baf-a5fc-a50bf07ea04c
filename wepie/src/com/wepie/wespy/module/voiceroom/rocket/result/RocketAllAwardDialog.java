package com.wepie.wespy.module.voiceroom.rocket.result;

import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseDialogFragment;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketAllAwardGiftInfo;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketRecordInfo;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketStatus;
import com.wepie.wespy.module.voiceroom.util.DividerItemDecoration;
import com.wepie.wespy.net.http.api.StarRocketApi;

import java.util.List;

/**
 * date 2019/1/9
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class RocketAllAwardDialog extends BaseDialogFragment {
    private RocketAllAwardItem myRewardItemView;
    private RocketAllAwardAdapter adapter;
    private TextView contentTv;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        View v = inflater.inflate(R.layout.rocket_all_award_dialog, container, false);
        updateView(v);
        return v;
    }

    private void updateView(View v) {
        contentTv = v.findViewById(R.id.content_tv);
        RecyclerView rv = v.findViewById(R.id.reward_lv);
        rv.setAdapter(adapter = new RocketAllAwardAdapter());
        rv.setLayoutManager(new LinearLayoutManager(rv.getContext()));
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(rv.getContext(), DividerItemDecoration.VERTICAL);
        ColorDrawable drawable = new DividerDrawable(0xffeeeeee);
        dividerItemDecoration.setDrawable(drawable);
        rv.addItemDecoration(dividerItemDecoration);
        myRewardItemView = v.findViewById(R.id.my_reward);
        v.findViewById(R.id.close_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissAllowingStateLoss();
            }
        });
    }

    static class DividerDrawable extends ColorDrawable {
        public DividerDrawable(int color) {
            super(color);
        }

        @Override
        public int getIntrinsicHeight() {
            return ScreenUtil.dip2px(1);
        }
    }

    private void refresh(String withdrawId, int rid) {
        StarRocketApi.getGiftDetail(withdrawId, new LifeDataCallback<>(this) {
            @Override
            public void onSuccess(Result<RocketAllAwardGiftInfo> result) {
                List<RocketRecordInfo> infoList = result.data.recordList;
                adapter.setRid(rid);
                adapter.refresh(infoList);
                boolean findLoginUid = false;
                for (RocketRecordInfo info : infoList) {
                    if (info.uid == LoginHelper.getLoginUid()) {
                        findLoginUid = true;
                        myRewardItemView.onBind(info, rid, 0);
                    }
                }
                if (!findLoginUid) {
                    RocketRecordInfo info = new RocketRecordInfo();
                    info.uid = LoginHelper.getLoginUid();
                    myRewardItemView.onBind(info, rid, 0);
                }
                UserService.get().getCacheSimpleUser(result.data.sender, new LifeUserSimpleInfoCallback(RocketAllAwardDialog.this) {
                    @Override
                    public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                        contentTv.setVisibility(View.VISIBLE);
                        String currentStarName = RocketStatus.getCurrentStarName(result.data.destination);
                        contentTv.setText(ResUtil.getStr(R.string.rocket_reward_content, simpleInfo.getRemarkNameLimit6(), currentStarName));
                    }

                    @Override
                    public void onUserInfoFailed(String description) {
                        contentTv.setVisibility(View.GONE);
                    }
                });
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    public static void showDialog(Context context, String withdrawId, int rid) {
        RocketAllAwardDialog dialog = new RocketAllAwardDialog();
        dialog.refresh(withdrawId, rid);
        dialog.initNormalStyle();
        dialog.show(context);
    }
}
