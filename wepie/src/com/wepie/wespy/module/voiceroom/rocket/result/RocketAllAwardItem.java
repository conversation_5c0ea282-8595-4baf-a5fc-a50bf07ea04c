package com.wepie.wespy.module.voiceroom.rocket.result;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.str.ResUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.decorate.NameTextView;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.CustomCircleImageView;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketAwardGiftInfo;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketRecordInfo;

public class RocketAllAwardItem extends FrameLayout {
    private CustomCircleImageView headIv;
    private NameTextView nameTv;
    private TextView descTv;

    public RocketAllAwardItem(@NonNull Context context) {
        super(context);
        initView();
    }

    public RocketAllAwardItem(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.room_all_gift_item_view, this);
        headIv = findViewById(R.id.head_iv);
        nameTv = findViewById(R.id.name_tv);
        descTv = findViewById(R.id.desc_tv);
    }

    public void onBind(final RocketRecordInfo info, final int rid, final int gid) {
        UserService.get().getCacheSimpleUser(info.uid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                HeadImageLoader.loadCircleHeadImage(simpleInfo.headimgurl, headIv);
                nameTv.setUserName(simpleInfo);
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });
        if (info.giftInfoList.isEmpty()) {
            descTv.setText(R.string.rocket_all_award_null);
        } else {
            StringBuilder msg = new StringBuilder(ResUtil.getStr(R.string.rocket_all_award_title, info.fuels));
            //不同地区需要追加空格的情况不一样,英语,阿拉伯语需要追加,日语汉语不需要
            String space = ResUtil.getStr(R.string.char_append_space);

            for (int i = 0; i < info.giftInfoList.size(); i++) {
                RocketAwardGiftInfo giftInfo = info.giftInfoList.get(i);
                PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(giftInfo.propId);
                if (propItem != null) {
                    //这两个类型交换 阿拉伯国家 %2$s%3$s%1$s  其他国家 %1$s%3$s%2$s
                    String gift = String.format(ResUtil.getStr(R.string.rocket_detail_gift_info,propItem.getName(),propItem.getTypeName(),space));
                    msg.append(space + gift + space);
                    if (giftInfo.validDays > 0) {
                        msg.append(giftInfo.validDays).append(space + ResUtil.getStr(R.string.rocket_day));
                    } else {
                        if (giftInfo.count > 1) {
                            msg.append(ResUtil.getStr(R.string.x_number, giftInfo.count));
                        }
                    }
                    if (i != info.giftInfoList.size() - 1) {
                        msg.append(ResUtil.getResource().getString(R.string.char_append_split));
                    }
                }
            }
            descTv.setText(msg.toString());
        }
        setOnClickListener(v -> {
            if (rid > 0) {
                JumpUtil.enterUserInfoDetailFromVoiceRoom(getContext(), info.uid, rid);
            } else if (gid > 0) {
                JumpUtil.enterUserInfoDetailActivityFromGroup(getContext(), info.uid, gid);
            }
        });
    }
}
