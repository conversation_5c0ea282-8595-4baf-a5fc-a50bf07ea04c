package com.wepie.wespy.module.voiceroom.rocket.supply;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.decorate.NameTextView;
import com.huiwan.user.UserService;
import com.huiwan.user.UserSimpleInfoCallback;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.voiceroom.rocket.model.SupplyItem;

public class SupplyDetailItemView extends ConstraintLayout {
    private NameTextView nameTv;
    private TextView supplyTimesTv;
    private TextView rankTv;
    private ImageView rankIv;
    private DecorHeadImgView headIv;

    public SupplyDetailItemView(@NonNull Context context) {
        super(context);
        initView();
    }

    public SupplyDetailItemView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public void updateView(SupplyItem supplyItem) {
        headIv.showUserHeadWithDecoration(supplyItem.getUid());
        headIv.setOnClickListener(v -> JumpUtil.enterUserInfoDetailActivity(v.getContext(), supplyItem.getUid(), ""));
        nameTv.setRemarkUser(supplyItem.getUid(), "");
        supplyTimesTv.setText(getContext().getString(R.string.supply_times_d, supplyItem.getSupplyTimes()));
        if (supplyItem.getRank() > 50 || supplyItem.getRank() <= 0) {
            rankTv.setText(R.string.not_in_rank);
            rankTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP,12);
            rankIv.setVisibility(GONE);
            rankTv.setVisibility(VISIBLE);
        } else {
            if (supplyItem.getRank() > 3) {
                rankTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP,14);
                rankTv.setText(String.valueOf(supplyItem.getRank()));
                rankIv.setVisibility(GONE);
                rankTv.setVisibility(VISIBLE);
            } else if (supplyItem.getRank() == 1){
                rankIv.setImageResource(R.drawable.voice_gurad_no1_icon);
                rankTv.setVisibility(GONE);
                rankIv.setVisibility(VISIBLE);
            } else if (supplyItem.getRank() == 2){
                rankIv.setImageResource(R.drawable.voice_guard_no2_icon);
                rankTv.setVisibility(GONE);
                rankIv.setVisibility(VISIBLE);
            } else if (supplyItem.getRank() == 3){
                rankIv.setImageResource(R.drawable.voice_guard_no3_icon);
                rankTv.setVisibility(GONE);
                rankIv.setVisibility(VISIBLE);
            }
        }
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.supply_detail_item_view, this);
        nameTv = findViewById(R.id.user_nickname_tv);
        supplyTimesTv = findViewById(R.id.supply_times_tv);
        rankTv = findViewById(R.id.supply_rank_tv);
        rankIv = findViewById(R.id.supply_rank_iv);
        headIv = findViewById(R.id.head_image_iv);
        headIv.setDefaultHeadBorder();
    }
}
