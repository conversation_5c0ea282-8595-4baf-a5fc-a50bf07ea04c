package com.wepie.wespy.module.voiceroom.rocket

import android.content.Context
import com.huiwan.base.util.ToastUtil
import com.huiwan.component.gift.IGiftApi
import com.huiwan.component.gift.send.GiftDialogCallback
import com.huiwan.configservice.model.gift.Gift
import com.wepie.wespy.module.voiceroom.rocket.send.RocketSendDetailDialog
import com.wepie.wespy.voiceroom.nationflag.R

class RocketDetailFilter : IGiftApi.IGiftDetailFilter {
    override fun filter(
        context: Context,
        gift: Gift,
        uid: Int,
        rid: Int,
        callback: GiftDialogCallback?
    ): <PERSON><PERSON><PERSON> {
        if (gift.gift_id == Gift.GIFT_ROCKET && gift.dialogExt != null) {
            if (uid > 0) {
                RocketSendDetailDialog.showDialog(context, rid, uid, gift, callback)
            } else {
                ToastUtil.show(R.string.gift_not_support_mul_tip)
            }
            return true
        }
        return false
    }

    override fun priority(): Int = 2
}