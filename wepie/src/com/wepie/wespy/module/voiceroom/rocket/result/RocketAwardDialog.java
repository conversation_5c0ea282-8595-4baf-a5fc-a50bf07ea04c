package com.wepie.wespy.module.voiceroom.rocket.result;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketAwardGiftInfo;

import java.util.List;

public class RocketAwardDialog extends FrameLayout {

    private Callback callback;
    private RocketAwardAdapter adapter;
    private RecyclerView recyclerView;

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    public RocketAwardDialog(@NonNull Context context) {
        super(context);
        initView();
    }

    public RocketAwardDialog(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.rocket_result_award_dialog, this);
        TextView titleTv = findViewById(R.id.tl_title_tv);
        titleTv.setText(R.string.rocket_award_title);
        recyclerView = findViewById(R.id.tl_lv);
        recyclerView.setAdapter(adapter = new RocketAwardAdapter());
        findViewById(R.id.ok_btn).setOnClickListener(v -> {
            if (callback != null) callback.onDismiss();
        });
    }

    public void refresh(List<RocketAwardGiftInfo> giftInfoList) {
        if (giftInfoList.size() > 3) {
            recyclerView.setLayoutManager(new GridLayoutManager(getContext(), 4));
        } else {
            recyclerView.setLayoutManager(new LinearLayoutManager(getContext(), RecyclerView.HORIZONTAL, false));
        }
        adapter.refresh(giftInfoList);
    }

    public static void showDialog(Context context, List<RocketAwardGiftInfo> giftInfoList) {
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        final RocketAwardDialog awardView = new RocketAwardDialog(context);
        awardView.setCallback(dialog::dismiss);
        awardView.refresh(giftInfoList);
        dialog.setContentView(awardView);
        dialog.initCenterDialog();
        dialog.setCanceledOnTouchOutside(true);
        dialog.show();
    }

    private interface Callback {
        void onDismiss();
    }
}
