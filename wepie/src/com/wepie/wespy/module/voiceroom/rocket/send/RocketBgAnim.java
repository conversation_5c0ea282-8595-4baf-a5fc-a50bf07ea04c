package com.wepie.wespy.module.voiceroom.rocket.send;

import android.graphics.Matrix;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.view.animation.Transformation;

public class RocketBgAnim extends Animation {

    private int translationY;
    private int duration;

    public RocketBgAnim(int translationY, int duration) {
        this.translationY = translationY;
        this.duration = duration;
    }

    @Override
    public void initialize(int width, int height, int parentWidth, int parentHeight) {
        super.initialize(width, height, parentWidth, parentHeight);
        // 设置默认时长
        setDuration(duration);
        // 保持动画的结束状态
        setFillAfter(true);
        setRepeatCount(Animation.INFINITE);
        // 设置默认插值器
        setInterpolator(new LinearInterpolator());
    }


    @Override
    protected void applyTransformation(float interpolatedTime, Transformation t) {
        super.applyTransformation(interpolatedTime, t);
        final Matrix matrix = t.getMatrix();
        matrix.postTranslate(0, translationY * interpolatedTime);
    }
}
