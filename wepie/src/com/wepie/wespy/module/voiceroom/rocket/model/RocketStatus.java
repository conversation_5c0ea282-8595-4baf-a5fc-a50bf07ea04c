package com.wepie.wespy.module.voiceroom.rocket.model;

import com.huiwan.base.util.TimeUtil;
import com.wepie.wespy.net.tcp.packet.Interstellar;

public class RocketStatus {
    public final static int UNDEFINED = 0;
    public final static int START = 1;//开启火箭
    public final static int FUEL_FULL = 2;//燃料已满
    public final static int FUEL_NOT_ENOUGH = 3;//燃料不足
    public final static int FUEL_WAITING = 4;//等待填充燃料
    public final static int FINAL_STAR = 5;//抵达终点星系
    public final static int ENDING = 6;//结束

    public final static int ATMOSPHERE = 1;//大气层
    public final static int SOLAR = 2;
    public final static int GALACTIC = 3;
    public final static int BLACK_HOLE = 4;
    public final static int CORAL = 5;
    public final static int BLUE_WHALE = 6;

    private int currentStar = UNDEFINED;
    private int status = UNDEFINED;
    private int sendUid = UNDEFINED;
    private int currentProgress;
    private int totalProgress;
    private long starTime;
    private long endTime;
    private int mySupplyNum;
    private int totalSupplyNum;
    private int myFuelNum;
    private int oldStatus = UNDEFINED;
    private int oldStar = UNDEFINED;

    public RocketStatus() {
    }

    public static RocketStatus parseFromProto(Interstellar.InterstellarInfo interstellarInfo) {
        RocketStatus rocketStatus = new RocketStatus();
        rocketStatus.setSendUid(interstellarInfo.getSender());
        rocketStatus.setStatus(interstellarInfo.getState());
        rocketStatus.setCurrentStar(interstellarInfo.getDeparture());
        rocketStatus.setTotalProgress(interstellarInfo.getNeedFuel());
        rocketStatus.setCurrentProgress(interstellarInfo.getCurrentFuel());
        rocketStatus.setStarTime(interstellarInfo.getStartTimeMs());
        rocketStatus.setEndTime(interstellarInfo.getEndTimeMs());
        rocketStatus.setTotalSupplyNum(interstellarInfo.getTotalFuelCount());
        rocketStatus.setMySupplyNum(interstellarInfo.getUserFuelInfo().getAddFuelAmount());
        rocketStatus.setMyFuelNum(interstellarInfo.getUserFuelInfo().getAvailableFuelAmount());
        return rocketStatus;
    }

    public void setOldRocketStatus(RocketStatus oldRocketStatus){
        oldStatus = oldRocketStatus.status;
        oldStar = oldRocketStatus.currentStar;
    }

    public int getOldStatus() {
        return oldStatus;
    }

    public int getOldStar() {
        return oldStar;
    }

    public int getMyFuelNum() {
        return myFuelNum;
    }

    public void setMyFuelNum(int myFuelNum) {
        this.myFuelNum = myFuelNum;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getCurrentStar() {
        return currentStar;
    }

    public void setCurrentStar(int currentStar) {
        this.currentStar = currentStar;
    }

    public int getCurrentProgress() {
        return currentProgress;
    }

    public void setCurrentProgress(int currentProgress) {
        this.currentProgress = currentProgress;
    }

    public int getTotalProgress() {
        return totalProgress;
    }

    public void setTotalProgress(int totalProgress) {
        this.totalProgress = totalProgress;
    }

    public long getStarTime() {
        return starTime;
    }

    public void setStarTime(long starTime) {
        this.starTime = starTime;
    }

    public int getMySupplyNum() {
        return mySupplyNum;
    }

    public void setMySupplyNum(int mySupplyNum) {
        this.mySupplyNum = mySupplyNum;
    }

    public int getTotalSupplyNum() {
        return totalSupplyNum;
    }

    public void setTotalSupplyNum(int totalSupplyNum) {
        this.totalSupplyNum = totalSupplyNum;
    }

    public int getSendUid() {
        return sendUid;
    }

    public void setSendUid(int sendUid) {
        this.sendUid = sendUid;
    }

    public String getCurrentStarName() {
        return getCurrentStarName(currentStar);
    }

    public static String getCurrentStarName(int starId) {
        return RocketConfig.getInstance().getStarById(starId).getStarName();
    }

    /**
     * 获取进度比例
     *
     * @return 浮点类型的比例，范围0～1
     */
    public float getProcess() {
        float process = (float) currentProgress / totalProgress;
        return Math.min(Math.max(process, 0), 1);
    }

    /**
     * 获取阶段剩余时间，单位秒
     *
     * @return time
     */
    public int getLeftTimes() {
        int leftTime = (int) ((endTime - TimeUtil.getServerTime()) / 1000);
        return Math.max(leftTime, 0);
    }

    public int getLeftTimeHour() {
        return getLeftTimes() / 3600;
    }

    public int getLeftTimeMin() {
        return (getLeftTimes() % 3600) / 60;
    }

    public int getLeftTimeSec() {
        return (getLeftTimes() % 3600) % 60;
    }

    public int getCurrentStageTimeMin() {
        return (int) (((endTime - starTime) /1000) %3600 / 60);
    }

    public int getCurrentStageTimeSec() {
        return (int) (((endTime - starTime) /1000) %3600 % 60);
    }

    public boolean isOnStarCross() {
        return status != UNDEFINED && status != ENDING;
    }

    public boolean shouldPlayRocketAnim(){
        return (oldStar >= RocketStatus.ATMOSPHERE && oldStar < currentStar) ||
                        (oldStatus != status && status == RocketStatus.ENDING);
    }

    public boolean shouldChangeBg(){
        return oldStar != currentStar;
    }
}
