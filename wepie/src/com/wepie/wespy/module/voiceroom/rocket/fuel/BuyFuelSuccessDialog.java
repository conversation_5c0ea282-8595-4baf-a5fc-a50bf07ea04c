package com.wepie.wespy.module.voiceroom.rocket.fuel;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.huiwan.base.util.PressUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.module.voiceroom.rocket.model.FuelGoods;

public class BuyFuelSuccessDialog extends ConstraintLayout {
    private ImageView fuelIv;
    private TextView fuelNameTv;
    private Callback callback;
    public BuyFuelSuccessDialog(@NonNull Context context) {
        super(context);
        initView();
    }

    public BuyFuelSuccessDialog(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.buy_fuel_success_dialog, this);
        fuelIv = findViewById(R.id.fuel_iv);
        fuelNameTv = findViewById(R.id.fuel_name_tv);
        TextView sureTv = findViewById(R.id.sure_tv);
        PressUtil.addPressEffect(sureTv);
        sureTv.setOnClickListener(v -> {
            if (callback != null){
                callback.sure();
            }
        });
    }

    private void updateView(FuelGoods fuelGoods){
        ImageLoaderUtil.loadNormalImage(fuelGoods.getImgUrl(), fuelIv);
        fuelNameTv.setText(fuelGoods.getName());
    }

    public static void show(Context context, FuelGoods fuelGoods){
        BuyFuelSuccessDialog buyFuelSuccessDialog = new BuyFuelSuccessDialog(context);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_theme_full_no_bg);
        buyFuelSuccessDialog.updateView(fuelGoods);
        dialog.setContentView(buyFuelSuccessDialog);
        dialog.initCenterDialog();
        dialog.setCanceledOnTouchOutside(true);
        buyFuelSuccessDialog.setCallback(dialog::dismiss);
        dialog.show();
    }

    private interface Callback{
        void sure();
    }
}
