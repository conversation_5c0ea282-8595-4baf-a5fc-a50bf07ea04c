package com.wepie.wespy.module.voiceroom.rocket.result;

import android.graphics.drawable.ShapeDrawable;
import android.graphics.drawable.shapes.RoundRectShape;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.log.TimeLogger;
import com.huiwan.component.prop.PropItemImageView;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.widget.rv.BaseRvAdapter;
import com.huiwan.widget.rv.RVHolder;
import com.wepie.wespy.R;
import com.wepie.wespy.module.family.FamilyUiConst;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketAwardGiftInfo;

public class RocketAwardAdapter extends BaseRvAdapter<RocketAwardGiftInfo, RocketAwardAdapter.RocketAwardViewHolder> {

    @NonNull
    @Override
    public RocketAwardViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (dataList.size() > 3) {
            return new RocketAwardViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.rocket_result_reward_item_54, parent, false));
        } else {
            return new RocketAwardViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.rocket_result_reward_item_80, parent, false));
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RocketAwardViewHolder holder, int position) {
        holder.update(dataList.get(position));
    }

    static class RocketAwardViewHolder extends RVHolder {

        private final PropItemImageView iconIv;
        private final TextView numTv;
        private final TextView nameTv;
        private final TextView typeTv;

        public RocketAwardViewHolder(View view) {
            super(view);
            iconIv = view.findViewById(R.id.icon_iv);
            numTv = view.findViewById(R.id.num_tv);
            nameTv = view.findViewById(R.id.name_tv);
            typeTv = view.findViewById(R.id.type_name_tv);
            numTv.setVisibility(View.GONE);
        }

        public void update(RocketAwardGiftInfo info) {
            PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(info.propId);
            if (propItem == null) {
                typeTv.setVisibility(View.GONE);
                return;
            }
            iconIv.showPropItem(propItem);
            iconIv.setInterceptor(FamilyUiConst.FAMILY_ITEM_INTERCEPTOR);
            String text = propItem.getName();
            if (info.validDays > 0) {
                text += info.validDays + ResUtil.getStr(R.string.rocket_day);
            } else if (info.count > 1) {
                text += ResUtil.getStr(R.string.x_number, info.count);
            }
            nameTv.setText(text);
            String typeName = FamilyUiConst.getPropTypeName(propItem);
            if (TextUtils.isEmpty(typeName)) {
                typeTv.setVisibility(View.GONE);
            } else {
                typeTv.setVisibility(View.VISIBLE);
                typeTv.setText(typeName);
                updateTypeBg();
            }
        }

        private void updateTypeBg() {
            ViewGroup.LayoutParams lp = iconIv.getLayoutParams();
            int radius = iconIv.getMeasuredWidth() * 8 / 108;
            if (lp != null) {
                int mode = View.MeasureSpec.getMode(lp.width);
                if (mode == View.MeasureSpec.EXACTLY || mode == View.MeasureSpec.UNSPECIFIED) {
                    radius = View.MeasureSpec.getSize(lp.width) * 8 / 108;
                }
            }
            TimeLogger.msg("bg radius: " + radius);
            RoundRectShape shape = new RoundRectShape(new float[]{0, 0, 0, 0, radius, radius, radius, radius}, null, null);
            ShapeDrawable shapeDrawable = new ShapeDrawable(shape);
            shapeDrawable.getPaint().setColor(0x66000000);
            typeTv.setBackground(shapeDrawable);
        }
    }
}
