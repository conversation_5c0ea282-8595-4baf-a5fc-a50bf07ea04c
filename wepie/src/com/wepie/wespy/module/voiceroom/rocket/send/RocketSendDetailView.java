package com.wepie.wespy.module.voiceroom.rocket.send;

import android.content.Context;
import android.text.Html;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.view.animation.LinearInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.anim.SVGAUtil;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.AssetPathUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.CustomCircleImageView;
import com.opensource.svgaplayer.SVGAImageView;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.model.entity.RocketAwardShowInfo;
import com.wepie.wespy.model.entity.RocketShowInfo;
import com.wepie.wespy.net.http.api.StarRocketApi;
import com.wepie.wespy.utils.SvgaAssets;

import java.util.ArrayList;
import java.util.List;

public class RocketSendDetailView extends FrameLayout {

    private Callback callback;
    private CustomCircleImageView headIv;
    private SVGAImageView rocketIv;
    private TextView nameTv;
    private TextView sendTv;
    private TextView fuelTv;
    private TextView[] showTvList;
    private ImageView bgOneIv, bgTwoIv, bgThreeIv, bgFourIv, bgFiveIv, bgSixIv;
    private List<RocketAwardShowInfo> giftInfoList = new ArrayList<>();
    private boolean isTaskPostDelayed = false;
    private int position = -1;
    private static final int DURATION_TIME = 15000;


    public RocketSendDetailView(@NonNull Context context) {
        super(context);
        initView();
    }

    public RocketSendDetailView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.rocket_send_detail_view, this);
        headIv = findViewById(R.id.head_iv);
        nameTv = findViewById(R.id.name_tv);
        sendTv = findViewById(R.id.send_tv);
        fuelTv = findViewById(R.id.fuel_tv);
        rocketIv = findViewById(R.id.rocket_iv);
        showTvList = new TextView[2];
        showTvList[0] = findViewById(R.id.show_tv_1);
        showTvList[1] = findViewById(R.id.show_tv_2);
        bgOneIv = findViewById(R.id.bg_1);
        bgTwoIv = findViewById(R.id.bg_2);
        bgThreeIv = findViewById(R.id.bg_3);
        bgFourIv = findViewById(R.id.bg_4);
        bgFiveIv = findViewById(R.id.bg_5);
        bgSixIv = findViewById(R.id.bg_6);
        findViewById(R.id.help_tv).setOnClickListener(v -> {
            if (callback != null) callback.showHelp();
        });
        findViewById(R.id.award_tv).setOnClickListener(v -> {
            if (callback != null) callback.showAward();
        });
        initData();
        SVGAUtil.playSvga(SvgaAssets.ROCKET_SEND_FLY, -1, rocketIv);

    }

    private void initAnim() {
        refreshItem(bgOneIv, bgTwoIv, getRes(1), 22000);
        refreshItem(bgThreeIv, bgFourIv, getRes(2), 12000);
        refreshItem(bgFiveIv, bgSixIv, getRes(3), 37000);
    }

    private String getRes(int position) {
        if (position == 1) {
            return AssetPathUtil.getImgUriFromAsset("send_detail_anim_one.webp");
        } else if (position == 2) {
            return AssetPathUtil.getImgUriFromAsset("send_detail_anim_two.webp");
        } else {
            return AssetPathUtil.getImgUriFromAsset("send_detail_anim_three.webp");
        }
    }

    private void refreshItem(ImageView view1, ImageView view2, String res, int duration) {
        int width = ScreenUtil.getScreenWidth();
        int height = width * ScreenUtil.dip2px(972) / ScreenUtil.dip2px(375);
        RocketBgAnim anim = new RocketBgAnim(height, duration);

        ViewGroup.LayoutParams layoutParams1 = view1.getLayoutParams();
        layoutParams1.width = width;
        layoutParams1.height = height;
        WpImageLoader.load(res, view1);
        view2.setTranslationY(-height);
        view1.setAnimation(anim);


        ViewGroup.LayoutParams layoutParams2 = view2.getLayoutParams();
        layoutParams2.width = width;
        layoutParams2.height = height;
        WpImageLoader.load(res, view2);
        view2.setAnimation(anim);
    }

    private void initData() {
        StarRocketApi.showUserGifts(new LifeDataCallback<>(this) {
            @Override
            public void onSuccess(Result<RocketShowInfo> result) {
                RocketShowInfo info = result.data;
                fuelTv.setText(ResUtil.getStr(R.string.rocket_detail_my_fuel, info.fuel));
                giftInfoList = info.awardGiftInfoList;
                startAnim();
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    public void refresh(int rid, int uid, Gift gift) {
        UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                HeadImageLoader.loadCircleHeadImage(simpleInfo.headimgurl, headIv);
                nameTv.setText(simpleInfo.getRemarkName());
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });
        sendTv.setText(ResUtil.getStr(R.string.rocket_detail_send, gift.getPrice()));
        sendTv.setOnClickListener(v -> {
            if (callback != null) callback.sendGift(rid, uid, gift);
        });
    }


    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    private int lastMeasureWidth = 0;
    private final Runnable task = () -> {
        position++;
        if (giftInfoList.isEmpty()) {
            return;
        }
        TextView animView = showTvList[position % showTvList.length];
        animView.setVisibility(INVISIBLE);
        RocketAwardShowInfo giftInfo = giftInfoList.get(position % giftInfoList.size());
        UserService.get().getCacheSimpleUser(giftInfo.uid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(giftInfo.prop_id);
                String propName = "";
                String propTypeName = "";
                if (propItem != null) {
                    propName = propItem.getName();
                    propTypeName = propItem.getTypeName();
                }
                String text = ResUtil.getStr(R.string.rocket_detail_award, simpleInfo.getRemarkNameLimit6(), propName, propTypeName);
                animView.setText(Html.fromHtml(text));
                if (position == 0) {
                    ViewExKt.postAutoCancel(RocketSendDetailView.this, DURATION_TIME / 2, task);
                }
                int screenWidth = ScreenUtil.getScreenWidth();
                int startX;
                float endX;
                if (ScreenUtil.isRtl()) {
                    startX = -1 * screenWidth;
                    if (screenWidth < lastMeasureWidth) {
                        startX = -lastMeasureWidth * 2 - screenWidth - ScreenUtil.dip2px(24);
                        endX = startX + screenWidth * 2;
                    } else {
                        endX = screenWidth;
                    }

                } else {
                    startX = screenWidth;
                    if (screenWidth < lastMeasureWidth) {
                        startX = lastMeasureWidth + ScreenUtil.dip2px(24);
                    }
                    endX = -startX;
                }

                animView.setVisibility(VISIBLE);
                animView.measure(MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED), MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));
                lastMeasureWidth = animView.getMeasuredWidth();
                animView.setTranslationX(startX);
                animView.animate().translationX(endX).setInterpolator(new LinearInterpolator()).setDuration(DURATION_TIME).start();
                ViewExKt.postAutoCancel(RocketSendDetailView.this, DURATION_TIME, task);
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });
    };

    public void startAnim() {
        stopAnim();
        post(task);
        isTaskPostDelayed = true;
    }

    public void stopAnim() {
        if (isTaskPostDelayed) {
            removeCallbacks(task);
            isTaskPostDelayed = false;
        }
        bgOneIv.clearAnimation();
        bgTwoIv.clearAnimation();
        bgThreeIv.clearAnimation();
        bgFourIv.clearAnimation();
        bgFiveIv.clearAnimation();
        bgSixIv.clearAnimation();
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        post(this::initAnim);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        stopAnim();
    }

    public interface Callback {
        void showHelp();

        void showAward();

        void sendGift(int rid, int uid, Gift gift);
    }
}
