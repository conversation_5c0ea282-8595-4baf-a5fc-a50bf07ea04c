package com.wepie.wespy.module.voiceroom.rocket.send;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.component.gift.send.BroadGiftInputDialog;
import com.huiwan.component.gift.send.ComboInfo;
import com.huiwan.component.gift.send.FuelSelectSendDialog;
import com.huiwan.component.gift.send.GiftDialogCallback;
import com.huiwan.component.gift.send.GiftSendInfo;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.user.LoginHelper;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFragCb;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;

public class RocketSendDetailDialog extends FrameLayout {
    private ImageView imageBg;
    private RocketSendDetailView detailView;
    private RocketSendHelpView helpView;
    private RocketSendAwardView awardView;
    private Callback callback;
    private int rid;
    private int uid;
    private Gift gift;


    public RocketSendDetailDialog(@NonNull Context context) {
        super(context);
        initView();
    }

    public RocketSendDetailDialog(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.rocket_send_detail_dialog, this);
        imageBg = findViewById(R.id.image_bg);
        detailView = findViewById(R.id.rocket_detail_view);
        helpView = findViewById(R.id.rocket_help_view);
        awardView = findViewById(R.id.rocket_award_view);
        awardView.showSpace();
        initEvent();
        showDetailView();
    }

    private void initEvent() {
        awardView.setCallback(this::showDetailView);
        helpView.setCallback(this::showDetailView);
        detailView.setCallback(new RocketSendDetailView.Callback() {
            @Override
            public void showHelp() {
                showHelpView();
            }

            @Override
            public void showAward() {
                showAwardView();
            }

            @Override
            public void sendGift(int rid, int uid, Gift gift) {
                checkSend();
            }
        });
    }

    public void update(int rid, int uid, Gift gift) {
        this.rid = rid;
        this.uid = uid;
        this.gift = gift;
        detailView.refresh(rid, uid, gift);
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    private void checkSend() {
        showDoubleCheckSendBroad();
    }


    private void showDoubleCheckSendBroad() {
        FuelSelectSendDialog.showDialog(getContext(), new BaseFragCb<Boolean>() {
            @Override
            public void onClickSure(@NonNull Boolean what) {
                showAppInputExt();
            }

            @Override
            public void onClickCancel() {
                doSend("", false);
            }
        });
    }

    private void showAppInputExt() {
        BroadGiftInputDialog.showDialog(getContext(), true, new BaseFragCb<String>() {
            @Override
            public void onClickSure(@NonNull String what) {
                doSend(what, true);
            }
        });
    }

    private void doSend(String msg, boolean broadcast) {
        GiftSendInfo sendInfo = getDefaultSendInfo(rid, uid, gift);
        sendInfo.extMsg = msg;
        sendInfo.broadcast = broadcast;
        if (callback != null) callback.sendGift(sendInfo);
    }

    private GiftSendInfo getDefaultSendInfo(int rid, int uid, Gift gift) {
        GiftSendInfo sendInfo = new GiftSendInfo();
        sendInfo.recUid = uid;
        sendInfo.giftId = gift.getGift_id();
        sendInfo.giftNum = 1;
        sendInfo.isPrivate = false;
        sendInfo.comboId = ComboInfo.makeComboId(LoginHelper.getLoginUid(), sendInfo.recUid);
        sendInfo.rid = rid;
        sendInfo.isGiftCard = false;
        sendInfo.setGift(gift);
        return sendInfo;
    }

    private void showAwardView() {
        awardView.setVisibility(VISIBLE);
        detailView.setVisibility(INVISIBLE);
        helpView.setVisibility(INVISIBLE);
        imageBg.setImageResource(R.drawable.send_detail_bg_two);
        awardView.refreshData();
    }

    private void showDetailView() {
        awardView.setVisibility(INVISIBLE);
        detailView.setVisibility(VISIBLE);
        helpView.setVisibility(INVISIBLE);
        imageBg.setImageResource(R.drawable.send_detail_bg_one);
    }

    private void showHelpView() {
        awardView.setVisibility(INVISIBLE);
        detailView.setVisibility(INVISIBLE);
        helpView.setVisibility(VISIBLE);
        imageBg.setImageResource(R.drawable.send_detail_bg_two);
    }

    public static void showHelpDialog(Context context) {
        final RocketSendDetailDialog view = new RocketSendDetailDialog(context);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        view.showHelpView();
        view.helpView.helpOnly();
        dialog.setContentView(view);
        dialog.initBottomDialog();
        dialog.setCanceledOnTouchOutside(true);
        dialog.show();
    }

    public static void showDialog(Context context, int rid, int uid, Gift gift, GiftDialogCallback callback) {
        final RocketSendDetailDialog view = new RocketSendDetailDialog(context);
        final BaseFullScreenDialog dialog = new BaseFullScreenDialog(context, R.style.dialog_style_custom);
        view.setCallback(info -> {
            if (rid != 0) {
                if (callback != null) callback.onGiftSend(info);
            } else {
                HLog.e("RocketFuel", HLog.USR, "send fuel rid error 0");
            }
            dialog.dismiss();
        });
        view.update(rid, uid, gift);
        dialog.setContentView(view);
        dialog.initBottomDialog();
        dialog.setCanceledOnTouchOutside(true);
        dialog.show();
    }

    public interface Callback {
        void sendGift(GiftSendInfo info);
    }
}
