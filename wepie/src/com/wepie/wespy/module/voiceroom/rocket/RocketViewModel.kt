package com.wepie.wespy.module.voiceroom.rocket

import android.content.Context
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.huiwan.base.util.ContextUtil
import com.wepie.wespy.module.voiceroom.rocket.model.RocketStatus
import com.wepie.wespy.voiceroom.giftgame.GiftGameViewModel
import kotlinx.coroutines.launch

class RocketViewModel : ViewModel() {
    private val _rocketStatus: MutableLiveData<RocketStatus> = MutableLiveData()
    val rocketStatus: LiveData<RocketStatus> = _rocketStatus

    fun init(gameViewModel: GiftGameViewModel) {
        viewModelScope.launch {
            gameViewModel.flow.collect {
                if (it is RocketStatus) {
                    updateRocketStatus(it)
                }
            }
        }
    }

    private fun updateRocketStatus(status: RocketStatus) {
        _rocketStatus.value?.let {
            status.setOldRocketStatus(it)
        }
        _rocketStatus.value = status
    }

    fun setMyFuelNum(myFuelNum: Int) {
        _rocketStatus.value?.let {
            it.myFuelNum = myFuelNum
            _rocketStatus.value = it
        }
    }

    fun isOnStarCross(): Boolean {
        return _rocketStatus.value?.isOnStarCross ?: false
    }

    fun getCurrentStatus(): RocketStatus = _rocketStatus.value!!

    companion object {
        @JvmStatic
        fun get(context: Context): RocketViewModel {
            val activity = ContextUtil.getFragmentActivityFromContext(context)
            return get(activity!!)
        }

        @JvmStatic
        fun get(activity: FragmentActivity): RocketViewModel {
            return ViewModelProvider(activity).get(RocketViewModel::class.java)
        }
    }

}