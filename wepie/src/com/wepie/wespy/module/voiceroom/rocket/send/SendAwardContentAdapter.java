package com.wepie.wespy.module.voiceroom.rocket.send;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.widget.rv.BaseRvAdapter;
import com.huiwan.widget.rv.RVHolder;
import com.wepie.wespy.R;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketAwardGiftInfo;

public class SendAwardContentAdapter extends BaseRvAdapter<RocketAwardGiftInfo, SendAwardContentAdapter.SendAwardContentViewHolder> {

    @NonNull
    @Override
    public SendAwardContentViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new SendAwardContentViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.rocket_send_award_content_item, null));
    }

    @Override
    public void onBindViewHolder(@NonNull SendAwardContentViewHolder holder, int position) {
        if (dataList.size() < 2) {
            PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(dataList.get(position).propId);
            holder.update(propItem);
            return;
        }
        int fixPosition;
        int realItemCount = dataList.size();
        if (position < 2) {
            fixPosition = realItemCount - 2 + position;
        } else if (position > realItemCount + 1) {
            fixPosition = position - realItemCount - 2;
        } else {
            fixPosition = position - 2;
        }
        PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(dataList.get(fixPosition).propId);
        holder.update(propItem);
    }


    @Override
    public int getItemCount() {
        return dataList.size() > 1 ? dataList.size() + 4 : dataList.size();
    }

    static class SendAwardContentViewHolder extends RVHolder {
        private final RocketPropItemImageView giftIv;

        public SendAwardContentViewHolder(View view) {
            super(view);
            ViewGroup.LayoutParams lp = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            view.setLayoutParams(lp);
            giftIv = view.findViewById(R.id.gift_iv);
        }

        public void update(PropItem propItem) {
            giftIv.showPropItem(propItem);
        }
    }
}
