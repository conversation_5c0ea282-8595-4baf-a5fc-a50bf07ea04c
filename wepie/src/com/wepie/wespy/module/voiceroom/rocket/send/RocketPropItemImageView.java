package com.wepie.wespy.module.voiceroom.rocket.send;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageView;

import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.constentity.propextra.HeadDecorExtra;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.widget.CustomCircleImageView;
import com.wepie.wespy.BuildConfig;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;

public class RocketPropItemImageView extends ViewGroup {
    private ImageView mediaIv;
    private int type = 0;

    public RocketPropItemImageView(@NonNull Context context) {
        this(context, null);
    }

    public RocketPropItemImageView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initViews(context);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        int count = getChildCount();
        for (int i = 0; i < count; i++) {
            View view = getChildAt(i);
            if (view.getVisibility() != View.GONE) {
                int h = view.getMeasuredHeight();
                int w = view.getMeasuredWidth();
                int childLeft = (r - l - w) / 2;
                int childTop = (b - t - h) / 2;
                view.layout(childLeft, childTop, childLeft + w, childTop + h);
            }
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int widthMode = MeasureSpec.getMode(widthMeasureSpec);
        int heightMode = MeasureSpec.getMode(heightMeasureSpec);
        int widthSize = MeasureSpec.getSize(widthMeasureSpec);
        int heightSize = MeasureSpec.getSize(heightMeasureSpec);

        if (widthMode != MeasureSpec.EXACTLY && heightMode != MeasureSpec.EXACTLY) {
            setMeasuredDimension(0, 0);
            if (BuildConfig.DEBUG) {
                ToastUtil.show("PropItemImageView need exactly measure mode");
            }
            return;
        }

        int measureSize;
        if (widthMode == MeasureSpec.EXACTLY) {
            measureSize = widthSize;
        } else {
            measureSize = heightSize;
        }
        showItemWithGivenSize(measureSize);
        int count = getChildCount();
        for (int i = 0; i < count; i++) {
            View view = getChildAt(i);
            if (view.getVisibility() != View.GONE) {
                measureChild(view, MeasureSpec.makeMeasureSpec(measureSize, MeasureSpec.EXACTLY), MeasureSpec.makeMeasureSpec(measureSize, MeasureSpec.EXACTLY));
            }
        }
        setMeasuredDimension(measureSize, measureSize);
    }

    public void showPropItem(int propId) {
        showPropItem(ConfigHelper.getInstance().getPropConfig().getPropItem(propId));
    }

    public void showPropItem(PropItem item) {
        if (item == null) {
            clear();
        } else {
            showPropItemInternal(item);
        }
    }

    private void clear() {
        mediaIv.setImageDrawable(null);
        type = 0;
    }

    private void showPropItemInternal(PropItem item) {
        type = item.getType();
        ImageLoaderUtil.loadNormalImage(item.getMediaUrl(), mediaIv);
        if (getMeasuredHeight() * getMeasuredWidth() != 0) {
            showItemWithGivenSize(getMeasuredHeight());
        }
    }

    private void showItemWithGivenSize(final int givenSize) {
        updateMediaLp(type, givenSize);
    }

    private boolean updateMediaLp(int type, int measuredSize) {
        LayoutParams lp = mediaIv.getLayoutParams();
        boolean res = false;
        int lpSize;
        switch (type) {
            case PropItem.TYPE_RING:
                lpSize = measuredSize * 68 / 80;
                break;
            case PropItem.TYPE_PROP:
            case PropItem.TYPE_HOME_ANIM:
                lpSize = measuredSize * 60 / 80;
                break;
            case PropItem.TYPE_HEAD_DECORATION:
                lpSize = measuredSize * 73 / 93;
                break;
            case PropItem.TYPE_FAMILY_AVATAR_DECOR:
                lpSize = measuredSize * 73 / 80;
                break;
            case PropItem.TYPE_VOICE_ENTER_ANIM:
                lpSize = measuredSize * 90 / 80;
                break;
            case PropItem.TYPE_CHAT_BUBBLE:
            case PropItem.TYPE_GIFT_CARD:
            case PropItem.TYPE_FAMILY_BOX:
            case PropItem.TYPE_DRAW_BOARD:
            case PropItem.TYPE_VOICE_BUBBLE:
            case PropItem.TYPE_DISCOVER_DECOR:
            case PropItem.TYPE_FAMILY_CHAT_BUBBLE:
            case PropItem.TYPE_USER_TAG:
            case PropItem.TYPE_VOICE_THEME:
            case PropItem.TYPE_VOICE_MIC:
            case PropItem.TYPE_GIFT_CARD_LIMITED:
                lpSize = measuredSize;
                break;
            default:
                lpSize = measuredSize;
                res = true;
        }
        lp.width = lp.height = lpSize;
        return res;
    }

    private void initViews(Context context) {
        mediaIv = new AppCompatImageView(context);

        addView(mediaIv, new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));
    }
}
