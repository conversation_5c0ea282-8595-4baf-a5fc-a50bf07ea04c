package com.wepie.wespy.module.voiceroom.rocket.send;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Rect;
import android.os.Bundle;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.Space;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.accessibility.AccessibilityNodeInfoCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.PropItem;
import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.wespy.R;
import com.wepie.wespy.module.shop.dialogs.PropDialogCallback;
import com.wepie.wespy.module.shop.dialogs.PropInfoConfig;
import com.wepie.wespy.module.shop.dialogs.PropInfoDialog;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketAwardGiftInfo;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketConfig;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketStar;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;

public class RocketSendAwardView extends FrameLayout {

    private Callback callback;
    private RecyclerView awardRv;
    private ViewPager2 awardPager;
    private SendAwardTitleAdapter titleAdapter;
    private SendAwardContentAdapter contentAdapter;
    private LinearLayoutManager manager;
    private LinearLayout previewLay;
    private TextView nameTv, tipsTv, previewTv, rocketAwardDetailTv;
    private Space spaceView1, spaceView2;
    private boolean isTaskPostDelayed = false;
    private int tempPosition = 0;
    private static final int DURATION_TIME = 3000;
    private static final long DEFAULT_PAGER_DURATION = 600;

    public RocketSendAwardView(@NonNull Context context) {
        super(context);
        initView();
    }

    public RocketSendAwardView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.rocket_send_award_view, this);
        findViewById(R.id.back_iv).setOnClickListener(v -> {
            if (callback != null) callback.onBack();
        });
        nameTv = findViewById(R.id.name_tv);
        tipsTv = findViewById(R.id.tips_tv);
        previewTv = findViewById(R.id.preview_tv);
        previewLay = findViewById(R.id.preview_lay);
        spaceView1 = findViewById(R.id.space_view1);
        spaceView2 = findViewById(R.id.space_view2);
        rocketAwardDetailTv = findViewById(R.id.rocket_award_detail_tv);
        awardRv = findViewById(R.id.award_rv);
        awardRv.setLayoutManager(manager = new LinearLayoutManager(getContext(), RecyclerView.HORIZONTAL, false));
        titleAdapter = new SendAwardTitleAdapter();
        awardRv.setAdapter(titleAdapter);
        awardPager = findViewById(R.id.award_pager);
        awardPager.setPageTransformer(new RocketPageTransformer());
        int width = (ScreenUtil.getScreenWidth() - ScreenUtil.dip2px(180)) / 2;
        setPageMargin(width);
        contentAdapter = new SendAwardContentAdapter();
        awardPager.setOffscreenPageLimit(1);
        initViewPagerScrollProxy();

        if (LibBaseUtil.getLang().isChinese()) {
            rocketAwardDetailTv.setLineSpacing(0f, 1.5f);
        } else {
            rocketAwardDetailTv.setLineSpacing(0f, 1.1f);
        }
    }

    public void showSpace() {
        spaceView1.setVisibility(VISIBLE);
        spaceView2.setVisibility(VISIBLE);
    }

    public void refreshData() {
        List<RocketStar> rocketConfigList = RocketConfig.getInstance().getRocketConfigList();
        titleAdapter.refresh(rocketConfigList);
        refreshViewPageData(rocketConfigList, titleAdapter.getSelectPosition());
        titleAdapter.setCallback(() -> {
            //先把title部分居中
            int position = titleAdapter.getSelectPosition();
            awardRv.scrollToPosition(position);
            awardRv.post(() -> {
                View view = manager.findViewByPosition(position);
                if (view != null) {
                    moveToMiddle(view);
                }
            });
            //刷新content部分数据
            refreshViewPageData(rocketConfigList, position);
        });
        awardPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            private int realPosition = 0;
            private boolean needChange = false;

            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                int size = rocketConfigList.get(titleAdapter.getSelectPosition()).getGiftInfoList().size();
                if (position < 2) {
                    realPosition = size + 1;
                    needChange = true;
                } else if (position > size + 1) {
                    realPosition = 2;
                    needChange = true;
                } else {
                    realPosition = position;
                    needChange = false;
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                refreshDesc(rocketConfigList, realPosition - 2);
                if (state == ViewPager2.SCROLL_STATE_IDLE && needChange) {
                    awardPager.setCurrentItem(realPosition, false);
                }
            }
        });
    }

    private void setPageMargin(int width) {
        RecyclerView recyclerView = (RecyclerView) awardPager.getChildAt(0);
        recyclerView.setPaddingRelative(width, awardPager.getPaddingTop(), width, awardPager.getPaddingBottom());
        recyclerView.setClipToPadding(false);
    }

    private void refreshViewPageData(List<RocketStar> urlList, int position) {
        //viewpager2不重新更新adapter在数据实时刷新状态下会和设置的PageTransformer冲突。
        awardPager.setAdapter(contentAdapter);

        if (urlList.size() > position) {
            contentAdapter.refresh(urlList.get(position).getGiftInfoList());
        }
        if (urlList.size() > 1) awardPager.setCurrentItem(2, false);
        refreshDesc(urlList, 0);
    }

    private void refreshDesc(List<RocketStar> urlList, int position) {
        if (urlList.isEmpty() || urlList.get(titleAdapter.getSelectPosition()).getGiftInfoList().size() <= position)
            return;

        int size = urlList.get(titleAdapter.getSelectPosition()).getGiftInfoList().size();

        if (size > 1) {
            tempPosition = position + 2;
            startTurning();
        }
        if (position >= 0 && position < size) {
            RocketAwardGiftInfo info = urlList.get(titleAdapter.getSelectPosition()).getGiftInfoList().get(position);
            PropItem propItem = ConfigHelper.getInstance().getPropConfig().getPropItem(info.propId);
            if (propItem != null) {
                showPropItem(info, propItem);
            } else {
                showNoProp();
            }
        } else {
            showNoProp();
        }
    }

    private void showNoProp() {
        nameTv.setText("");
        tipsTv.setText("");
        previewTv.setVisibility(GONE);
    }

    private void showPropItem(RocketAwardGiftInfo info, PropItem propItem) {
        nameTv.setText(propItem.getName());
        tipsTv.setText(propItem.getSimpleDesc());
        if (propItem.isGiftCard()) {
            if (info.extraPropId != 0) {
                previewTv.setVisibility(VISIBLE);
                previewLay.setOnClickListener(v -> {
                    PropInfoConfig config = PropInfoConfig.fromOutOfShop(info.extraPropId);
                    config.setPreview(true);
                    config.setBpPreview(true);
                    PropInfoDialog.show(getContext(), config, true, new PropDialogCallback());
                });
            } else {
                previewTv.setVisibility(GONE);
                previewLay.setOnClickListener(v -> {
                });
            }
        } else {
            previewTv.setVisibility(VISIBLE);
            previewLay.setOnClickListener(v -> {
                PropInfoConfig config = PropInfoConfig.fromOutOfShop(propItem.getItemId());
                config.setPreview(true);
                config.setBpPreview(true);
                PropInfoDialog.show(getContext(), config, true, new PropDialogCallback());
            });
        }
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    /**
     * 滚动到中间位置
     *
     * @param clkView 被点击的View
     */
    public void moveToMiddle(View clkView) {
        int itemWidth = clkView.getWidth();
        int screenWidth = getResources().getDisplayMetrics().widthPixels;
        int scrollWidth = clkView.getLeft() - (screenWidth / 2 - itemWidth / 2);
        awardRv.smoothScrollBy(scrollWidth, 0);
    }

    private final Runnable task = new Runnable() {
        @Override
        public void run() {
            tempPosition++;
            awardPager.setCurrentItem(tempPosition);
            ViewExKt.postAutoCancel(RocketSendAwardView.this, task, DURATION_TIME);
        }
    };

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        startTurning();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        stopTurning();
    }

    public void startTurning() {
        stopTurning();
        ViewExKt.postAutoCancel(this, task, DURATION_TIME);
        isTaskPostDelayed = true;
    }

    public void stopTurning() {
        if (isTaskPostDelayed) {
            removeCallbacks(task);
            isTaskPostDelayed = false;
        }
    }

    private void initViewPagerScrollProxy() {
        try {
            //控制切换速度，采用反射方。法方法只会调用一次，替换掉内部的RecyclerView的LinearLayoutManager
            RecyclerView recyclerView = (RecyclerView) awardPager.getChildAt(0);
            recyclerView.setOverScrollMode(RecyclerView.OVER_SCROLL_NEVER);
            LinearLayoutManager o = (LinearLayoutManager) recyclerView.getLayoutManager();
            ProxyLayoutManger proxyLayoutManger = new ProxyLayoutManger(getContext(), o);
            recyclerView.setLayoutManager(proxyLayoutManger);

            Field mRecyclerView = RecyclerView.LayoutManager.class.getDeclaredField("mRecyclerView");
            mRecyclerView.setAccessible(true);
            mRecyclerView.set(o, recyclerView);

            Field LayoutMangerField = ViewPager2.class.getDeclaredField("mLayoutManager");
            LayoutMangerField.setAccessible(true);
            LayoutMangerField.set(awardPager, proxyLayoutManger);

            Field pageTransformerAdapterField = ViewPager2.class.getDeclaredField("mPageTransformerAdapter");
            pageTransformerAdapterField.setAccessible(true);
            Object mPageTransformerAdapter = pageTransformerAdapterField.get(awardPager);
            if (mPageTransformerAdapter != null) {
                Class<?> aClass = mPageTransformerAdapter.getClass();
                Field layoutManager = aClass.getDeclaredField("mLayoutManager");
                layoutManager.setAccessible(true);
                layoutManager.set(mPageTransformerAdapter, proxyLayoutManger);
            }
            Field scrollEventAdapterField = ViewPager2.class.getDeclaredField("mScrollEventAdapter");
            scrollEventAdapterField.setAccessible(true);
            Object mScrollEventAdapter = scrollEventAdapterField.get(awardPager);
            if (mScrollEventAdapter != null) {
                Class<?> aClass = mScrollEventAdapter.getClass();
                Field layoutManager = aClass.getDeclaredField("mLayoutManager");
                layoutManager.setAccessible(true);
                layoutManager.set(mScrollEventAdapter, proxyLayoutManger);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static class ProxyLayoutManger extends LinearLayoutManager {
        private final RecyclerView.LayoutManager layoutManager;

        ProxyLayoutManger(Context context, LinearLayoutManager layoutManager) {
            super(context, layoutManager.getOrientation(), false);
            this.layoutManager = layoutManager;
        }

        @Override
        public boolean performAccessibilityAction(@NonNull RecyclerView.Recycler recycler,
                                                  @NonNull RecyclerView.State state, int action, @Nullable Bundle args) {
            return layoutManager.performAccessibilityAction(recycler, state, action, args);
        }

        @Override
        public void onInitializeAccessibilityNodeInfo(@NonNull RecyclerView.Recycler recycler,
                                                      @NonNull RecyclerView.State state, @NonNull AccessibilityNodeInfoCompat info) {
            layoutManager.onInitializeAccessibilityNodeInfo(recycler, state, info);
        }

        @Override
        public boolean requestChildRectangleOnScreen(@NonNull RecyclerView parent,
                                                     @NonNull View child, @NonNull Rect rect, boolean immediate,
                                                     boolean focusedChildVisible) {
            return layoutManager.requestChildRectangleOnScreen(parent, child, rect, immediate, focusedChildVisible);
        }

        @Override
        protected void calculateExtraLayoutSpace(@NonNull RecyclerView.State state,
                                                 @NonNull int[] extraLayoutSpace) {
            try {
                Method method = layoutManager.getClass().getDeclaredMethod("calculateExtraLayoutSpace", state.getClass(), extraLayoutSpace.getClass());
                method.setAccessible(true);
                method.invoke(layoutManager, state, extraLayoutSpace);
            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void smoothScrollToPosition(RecyclerView recyclerView, RecyclerView.State state, int position) {
            LinearSmoothScroller linearSmoothScroller = new LinearSmoothScroller(recyclerView.getContext()) {
                @Override
                protected int calculateTimeForDeceleration(int dx) {
                    return (int) (DEFAULT_PAGER_DURATION * (1 - .3356));
                }
            };
            linearSmoothScroller.setTargetPosition(position);
            startSmoothScroll(linearSmoothScroller);
        }
    }


    public interface Callback {
        void onBack();
    }
}
