package com.wepie.wespy.module.voiceroom.rocket;

import android.app.Dialog;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.utils.widget.ImageFilterView;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.ImageLoaderUtil;
import com.wepie.wespy.model.event.voice.RocketDialogDismissEvent;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class RocketDialogContainerView extends FrameLayout {
    private ConstraintLayout container;
    private ImageView dialogBg;
    private Dialog dialog;
    private ImageFilterView headIv;
    private ImageView dismissView;

    public RocketDialogContainerView(@NonNull Context context) {
        super(context);
        initView();
    }

    public RocketDialogContainerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRocketStatusChanged(RocketDialogDismissEvent event) {
        if (dialog != null) {
            dialog.dismiss();
        }
    }

    public void setDialog(Dialog dialog) {
        this.dialog = dialog;
    }

    public void addDialogView(View view, int height, int viewHeight) {
        container.getLayoutParams().height = height;
        ConstraintLayout.LayoutParams layoutParams = new ConstraintLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, viewHeight);
        layoutParams.bottomToBottom = container.getId();
        container.addView(view, layoutParams);
        container.requestLayout();
        setSender();
    }

    public void setBg(int res) {
        dialogBg.setImageResource(res);
    }

    public void setSender() {
        int uid = RocketViewModel.get(getContext()).getCurrentStatus().getSendUid();
        UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                ImageLoaderUtil.loadNormalImage(simpleInfo.headimgurl, headIv);
                headIv.setOnClickListener(v -> JumpUtil.enterUserInfoDetailFromVoiceRoom(getContext(), uid, VoiceRoomService.getInstance().getRid()));
            }

            @Override
            public void onUserInfoFailed(String description) {
            }
        });
    }

    private void initView() {
        EventDispatcher.registerEventObserver(this);
        LayoutInflater.from(getContext()).inflate(R.layout.rocket_dialog_container_view, this);
        container = findViewById(R.id.dialog_content_lay);
        dialogBg = findViewById(R.id.dialog_bg_iv);
        headIv = findViewById(R.id.head_iv);
        dismissView = findViewById(R.id.dismiss_view);
        dismissView.setOnClickListener(v -> {
            if (dialog != null) {
                dialog.dismiss();
            }
        });
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        EventDispatcher.registerEventObserver(this);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        EventDispatcher.unregisterEventObserver(this);
    }
}
