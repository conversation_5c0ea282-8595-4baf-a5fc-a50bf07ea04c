package com.wepie.wespy.module.voiceroom.rocket.model;

import com.wepie.wespy.net.tcp.packet.Interstellar;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;

import java.util.ArrayList;
import java.util.List;

public class SupplyItem {
    private int uid;
    private int rank;
    private int supplyTimes;

    public SupplyItem() {
    }

    public SupplyItem(int uid, int rank, int supplyTimes) {
        this.uid = uid;
        this.rank = rank;
        this.supplyTimes = supplyTimes;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public int getSupplyTimes() {
        return supplyTimes;
    }

    public void setSupplyTimes(int supplyTimes) {
        this.supplyTimes = supplyTimes;
    }

    public static List<SupplyItem> parseList(Interstellar.GetInterstellarFuelRecordsRsp fuelRecordsRsp){
        List<SupplyItem> supplyItemList = new ArrayList<>();
        if (fuelRecordsRsp == null) return supplyItemList;
        return parseList(fuelRecordsRsp.getRecordsList());
    }

    public static List<SupplyItem> parseList(List<Interstellar.InterstellarFuelRecord> fuelRecordList){
        List<SupplyItem> supplyItemList = new ArrayList<>();
        for (Interstellar.InterstellarFuelRecord  fuelRecord : fuelRecordList){
            supplyItemList.add(SupplyItem.parseItem(fuelRecord));
        }
        return supplyItemList;
    }

    public static SupplyItem parseItem(Interstellar.InterstellarFuelRecord fuelRecord){
        if (fuelRecord == null) return new SupplyItem();
        return new SupplyItem(fuelRecord.getUid(), fuelRecord.getRank(), fuelRecord.getAmount());
    }
}
