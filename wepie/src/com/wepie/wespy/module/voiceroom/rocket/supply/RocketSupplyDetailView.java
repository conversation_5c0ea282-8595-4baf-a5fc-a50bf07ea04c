package com.wepie.wespy.module.voiceroom.rocket.supply;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.rocket.model.SupplyItem;
import com.wepie.wespy.net.tcp.packet.Interstellar;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

import java.util.List;

public class RocketSupplyDetailView extends ConstraintLayout {
    private ImageView backIv;
    private SupplyDetailItemView myDetailItem;
    private SupplyTimesAdapter supplyTimesAdapter;
    private Callback callback;

    public RocketSupplyDetailView(@NonNull Context context) {
        super(context);
        initView();
    }

    public RocketSupplyDetailView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    public void setCallback(Callback callback) {
        this.callback = callback;
    }

    private void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.rocket_supply_detail_view, this);
        backIv = findViewById(R.id.back_iv);
        RecyclerView supplyListRv = findViewById(R.id.supply_list_rv);
        myDetailItem = findViewById(R.id.my_supply_item);
        supplyTimesAdapter = new SupplyTimesAdapter(getContext());
        supplyListRv.setAdapter(supplyTimesAdapter);
        supplyListRv.setLayoutManager(new LinearLayoutManager(getContext()));
        setListener();
        loadData();
    }

    private void setListener() {
        backIv.setOnClickListener(v -> {
            if (callback != null) {
                callback.onBack();
            }
        });
    }

    private void loadData() {
        VoiceRoomPacketSender.getFuelRecords(VoiceRoomService.getInstance().getRid(), new LifeSeqCallback(this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                Interstellar.GetInterstellarFuelRecordsRsp fuelRecordsRsp = (Interstellar.GetInterstellarFuelRecordsRsp) head.message;
                List<SupplyItem> supplyItemList = SupplyItem.parseList(fuelRecordsRsp);
                SupplyItem mySupply = SupplyItem.parseItem(fuelRecordsRsp.getMine());
                supplyTimesAdapter.setSupplyItemList(supplyItemList);
                supplyTimesAdapter.notifyDataSetChanged();
                myDetailItem.updateView(mySupply);
            }

            @Override
            public void onFail(RspHeadInfo head) {

            }
        });
    }

    public interface Callback {
        void onBack();
    }
}
