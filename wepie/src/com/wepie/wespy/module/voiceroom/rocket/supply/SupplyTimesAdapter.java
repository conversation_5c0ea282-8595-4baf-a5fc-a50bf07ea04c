package com.wepie.wespy.module.voiceroom.rocket.supply;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.wepie.wespy.R;
import com.wepie.wespy.module.voiceroom.rocket.model.SupplyItem;

import java.util.ArrayList;
import java.util.List;

public class SupplyTimesAdapter extends RecyclerView.Adapter<SupplyTimesAdapter.ViewHolder> {
    private final Context context;
    private List<SupplyItem> supplyItemList = new ArrayList<>();

    public SupplyTimesAdapter(Context context) {
        this.context = context;
    }

    public void setSupplyItemList(List<SupplyItem> supplyItemList) {
        this.supplyItemList = supplyItemList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(context).inflate(R.layout.supply_list_adapter_item, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        holder.supplyDetailItemView.updateView(supplyItemList.get(position));
    }

    @Override
    public int getItemCount() {
        return supplyItemList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder{
        SupplyDetailItemView supplyDetailItemView;
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            supplyDetailItemView = itemView.findViewById(R.id.supply_detail_item);
        }
    }
}
