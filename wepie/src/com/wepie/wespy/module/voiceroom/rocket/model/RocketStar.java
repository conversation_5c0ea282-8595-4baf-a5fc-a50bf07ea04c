package com.wepie.wespy.module.voiceroom.rocket.model;

import com.google.gson.annotations.SerializedName;
import com.huiwan.base.str.ResUtil;
import com.huiwan.store.file.FileCacheName;
import com.huiwan.store.file.FileConfig;
import com.wepie.wespy.R;

import java.util.ArrayList;
import java.util.List;

public class RocketStar {
    @SerializedName("destination")
    private int starId;
    @SerializedName("name")
    private String starName = ResUtil.getString(R.string.unknown_star);
    @SerializedName("bg_video_url")
    private String bgVideoUrl = "";
    @SerializedName("bg_image_url")
    private String bgImageUrl = "";
    @SerializedName("gifts")
    private List<RocketAwardGiftInfo> giftInfoList = new ArrayList<>();

    public int getStarId() {
        return starId;
    }

    public void setStarId(int starId) {
        this.starId = starId;
    }

    public String getStarName() {
        return starName;
    }

    public void setStarName(String starName) {
        this.starName = starName;
    }

    public String getBgVideoUrl() {
        return bgVideoUrl;
    }

    public void setBgVideoUrl(String bgVideoUrl) {
        this.bgVideoUrl = bgVideoUrl;
    }

    public String getBgImageUrl() {
        return bgImageUrl;
    }

    public void setBgImageUrl(String bgImageUrl) {
        this.bgImageUrl = bgImageUrl;
    }

    public List<RocketAwardGiftInfo> getGiftInfoList() {
        if (giftInfoList == null) {
            giftInfoList = new ArrayList<>();
        }
        return giftInfoList;
    }

    public void setGiftInfoList(List<RocketAwardGiftInfo> giftInfoList) {
        this.giftInfoList = giftInfoList;
    }

    public String getImageBgFileName() {
        return FileConfig.getPropDirPath(FileCacheName.VOICE_ROOM_BG + "image" + starName);
    }
}
