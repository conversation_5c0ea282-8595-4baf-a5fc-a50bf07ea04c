package com.wepie.wespy.module.voiceroom.dataservice;

import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.marry.WeddingInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;

public class VoiceRoomLiveGiftDescHelper {

    public static String getSeatDesc(VoiceRoomInfo roomInfo, VoiceRoomInfo.SeatInfo seatInfo) {
        int seatNum = seatInfo.seat_num;
        String desc;
        if (roomInfo.isWeddingRoom()) {
            WeddingInfo weddingInfo = roomInfo.weddingInfo;
            if (seatNum == 1) {
                desc = ResUtil.getStr(R.string.mc_people);
            } else if (weddingInfo.brideUid == seatInfo.uid) {
                desc = ResUtil.getStr(R.string.bride);
            } else if (weddingInfo.groomUid == seatInfo.uid) {
                desc = ResUtil.getStr(R.string.groom);
            } else {
                desc = String.valueOf(seatNum - 3);
            }
        } else if (roomInfo.isAuctionRoom()) {
            if (roomInfo.owner == seatInfo.uid) {
                desc = ResUtil.getStr(R.string.host);
            } else if (seatNum == VoiceRoomInfo.SeatInfo.AUCTION_GUEST_1_SEAT_NUM) {
                desc = ResUtil.getStr(R.string.voice_room_mic_aution_vip_seat_1);
            } else if (seatNum == VoiceRoomInfo.SeatInfo.AUCTION_GUEST_2_SEAT_NUM) {
                desc = ResUtil.getStr(R.string.voice_room_mic_aution_vip_seat_2);
            } else if (seatNum == VoiceRoomInfo.SeatInfo.AUCTION_GUEST_3_SEAT_NUM) {
                desc = ResUtil.getStr(R.string.voice_room_mic_aution_vip_seat_3);
            } else if (seatNum == VoiceRoomInfo.SeatInfo.AUCTION_AUCTION_SEAT_NUM) {
                desc = ResUtil.getStr(R.string.voice_room_mic_aution_seat);
            } else {
                desc = String.valueOf(seatNum - 4);
            }
        } else {
            if (roomInfo.owner == seatInfo.uid) {
                desc = ResUtil.getStr(R.string.host);
            } else if ((seatNum == VoiceRoomInfo.SeatInfo.INTIMATE_SEAT_NUM && !roomInfo.isFamilyRoom() && !roomInfo.isVideoRoom())
                    || (seatNum == 14 && roomInfo.isFamilyRoom())) {
                desc = ResUtil.getStr(R.string.voice_room_mic_owner_intimacy_seat);
            } else if (VoiceRoomInfo.SeatInfo.isHonorSeat(seatInfo.seatType)) {
                if (roomInfo.isAdvancedOrAnchorRoom()) {
                    desc = String.valueOf(seatNum - 12);
                } else {
                    desc = String.valueOf(seatNum - 8);
                }
            } else {
                desc = String.valueOf(seatNum - 1);
            }
        }
        return desc;
    }

    public static int getIndex(VoiceRoomInfo roomInfo, VoiceRoomInfo.SeatInfo seatInfo) {
        if (roomInfo.owner == seatInfo.uid) {
            return 0;
        }
        int seatNum = seatInfo.seat_num;
        if (seatNum == VoiceRoomInfo.SeatInfo.INTIMATE_SEAT_NUM && roomInfo.isVoiceRoom()) {
            return 1;
        }
        if (roomInfo.isWeddingRoom()) {
            WeddingInfo weddingInfo = roomInfo.weddingInfo;
            if (seatNum == 1) {
                return 1;
            } else if (weddingInfo.brideUid == seatInfo.uid) {
                return 3;
            } else if (weddingInfo.groomUid == seatInfo.uid) {
                return 2;
            }
        }
        if (roomInfo.isAuctionRoom()) {
            if (seatNum == VoiceRoomInfo.SeatInfo.AUCTION_GUEST_1_SEAT_NUM) {
                return 2;
            } else if (seatNum == VoiceRoomInfo.SeatInfo.AUCTION_GUEST_2_SEAT_NUM) {
                return 3;
            } else if (seatNum == VoiceRoomInfo.SeatInfo.AUCTION_GUEST_3_SEAT_NUM) {
                return 4;
            } else if (seatNum == VoiceRoomInfo.SeatInfo.AUCTION_AUCTION_SEAT_NUM) {
                return 1;
            }
        }
        return seatNum + 5;
    }
}
