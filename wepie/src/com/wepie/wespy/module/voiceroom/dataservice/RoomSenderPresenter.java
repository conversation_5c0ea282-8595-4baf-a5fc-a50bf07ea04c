package com.wepie.wespy.module.voiceroom.dataservice;

import android.content.Context;
import android.text.TextUtils;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.lifecycle.IReference;
import com.huiwan.base.lifecycle.IReferenceKt;
import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.gift.send.GiftSendInfo;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.AuthApi;
import com.huiwan.libtcp.callback.DefaultSeqCallback;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.callback.LifeSeqCallbackProxy;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.module.authcheck.IDAuthCheckManager;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.voiceservice.VoiceManager;
import com.wejoy.weplay.ex.ILifeOwnerKt;
import com.wepie.lib.api.plugins.track.TrackApi;
import com.wepie.lib.api.plugins.track.config.cn.TrackScreenName;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.RoomInfoUpdateEvent;
import com.wepie.wespy.module.draw.pkt.DrawGuessSenderPresenter;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.dataservice.event.VoiceRoomFuelNotEnoughEvent;
import com.wepie.wespy.net.tcp.handler.VoiceRoomHandler;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;
import com.wepie.wespy.net.tcp.sender.DrawGuessPacketSender;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

import org.greenrobot.eventbus.EventBus;

import java.util.List;
import java.util.Map;

/**
 * Created by geeksammao on 24/10/2017.
 */

public class RoomSenderPresenter {

    public static void enterRoom(final int rid, final String pwd, boolean keyConfirm, boolean isLoveRoom, int follow_uid, int gameType, int index,
                                 int source, final RoomInfoCallback callback) {
        VoiceRoomPacketSender.enterRoom(rid, pwd, keyConfirm, isLoveRoom, follow_uid, gameType, index, source, new LifeSeqCallback(ILifeOwnerKt.getLife(callback)) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                TmpRoomPackets.EnterRoomRsp enterRoomRsp = (TmpRoomPackets.EnterRoomRsp) head.message;
                VoiceRoomInfo voiceRoomInfo = VoiceRoomHandler.parseRoomInfo(enterRoomRsp.getRoomInfo());
                if (callback != null) callback.onSuccess(voiceRoomInfo);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                if (callback == null) {
                    return;
                }
                boolean hasPwd = !TextUtils.isEmpty(pwd);
                if (head.errorType != null && head.errorType.isRoomEnterNeedPwd() && !hasPwd) {
                    callback.onNeedPwd();
                } else if (head.errorType != null && head.errorType.isPlatformNotMatch()) {
                    callback.onPlatformNotMatch(head.desc);
                } else if (head.code == RspHeadInfo.ROOM_ERROR_CODE_VERSION_TOO_LOW) {
                    TmpRoomPackets.EnterRoomRsp enterRoomRsp = (TmpRoomPackets.EnterRoomRsp) head.message;
                    callback.onGameVersionTooLow(VoiceRoomHandler.parseRoomInfo(enterRoomRsp.getRoomInfo()));
                } else if (head.code == RspHeadInfo.ERROR_CODE_HOST_NOT_ONLINE) {
                    TmpRoomPackets.EnterRoomRsp enterRoomRsp = (TmpRoomPackets.EnterRoomRsp) head.message;
                    callback.hostNotOnline(rid, enterRoomRsp.getHostInfo(), head.desc);
                } else if (head.code == RspHeadInfo.ERROR_CODE_BLOCKED) {
                    callback.onBlocked();
                } else {
                    callback.onFail(head.desc);
                }
            }
        });
    }

    public static void enterRoom(final int rid, final String pwd, boolean keyConfirm, boolean isLoveRoom, int follow_uid, int gameType, final RoomInfoCallback callback) {
        enterRoom(rid, pwd, keyConfirm, isLoveRoom, follow_uid, gameType, 0, 0, callback);
    }

    public static void createRoom(int rid, String roomName, String pwd, int gameType, int labelType, int bgThemeId, String note, int roomType, TmpRoomPackets.TmpGameBasicInfo tmpGameBasicInfo, final RoomCallback callback) {
        VoiceRoomPacketSender.createRoomReq(rid, roomName, pwd, gameType, labelType, bgThemeId, note, roomType, tmpGameBasicInfo, new RoomSeqCb(callback));
    }

    public static void createRoom(int rid, String roomName, String pwd, int gameType, int labelType, int bgThemeId, String note, int roomType, final LifeSeqCallback callback) {
        VoiceRoomPacketSender.createRoomReq(rid, roomName, pwd, gameType, labelType, bgThemeId, note, roomType, null, callback);
    }

    public static void quickMatch(final RoomCallback callback) {
        VoiceRoomPacketSender.quickMatch(new RoomSeqCb(callback));
    }

    public static void sit(final Context context, final int rid, final int seat_num, final RoomCallback callback) {
        IDAuthCheckManager.doTaskOrShowNeedCertificate(context, AuthApi.SCENE_SIT, () -> VoiceRoomPacketSender.sit(rid, seat_num, new RoomSeqCb(callback)));
    }

    public static void unseat(final int rid, int seat_num, final RoomCallback callback) {
        VoiceRoomPacketSender.unseat(rid, seat_num, new RoomSeqCb(callback));
    }

    public static void forceUp(int rid, int seat_num, int uid) {
        VoiceRoomPacketSender.forceUp(rid, seat_num, uid, new DefaultSeqCallback());
    }

    public static void sendTrickFace(int rid, int faceId, int toUid, LifeSeqCallback callback) {
        VoiceRoomPacketSender.sendTrickGift(rid, faceId, toUid, callback);
    }

    public static void forbidSpeak(int rid, int seat_num, boolean is_forbidden) {
        VoiceRoomPacketSender.forbidSpeak(rid, seat_num, is_forbidden, new DefaultSeqCallback());
    }

    public static void sealSeat(int rid, int seat_num, boolean sealed) {
        VoiceRoomPacketSender.sealSeat(rid, seat_num, sealed, new DefaultSeqCallback());
    }

    public static void exitRoom(int rid) {
        VoiceRoomPacketSender.exitRoom(rid, new DefaultSeqCallback() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                VoiceManager.getInstance().resetPushStreamUrl();
            }
        });
    }


    //-------------mod------------------

    public static void modRoomName(final int rid, final String roomName, final RoomCallback callback) {
        VoiceRoomPacketSender.modRoomName(rid, roomName, new RoomSeqCb(callback));
    }

    public static void modPwd(final int rid, final String pwd, boolean force, final LifeSeqCallback callback) {
        VoiceRoomPacketSender.modPwd(rid, pwd, force, new LifeSeqCallbackProxy(callback) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                VoiceRoomService.getInstance().modPwd(rid, pwd);
                EventDispatcher.postRoomInfoUpdateEvent(RoomInfoUpdateEvent.F_ROOM_SETTINGS);
                super.onSuccess(head);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                super.onFail(head);
            }
        });
    }

    public static void modRoomNote(final int rid, String content, final RoomCallback callback) {
        VoiceRoomPacketSender.modRoomNote(rid, content, new RoomSeqCb(callback));
    }


    public static void createPk(final int rid, int lUid, int rUid, int timeInSec, int mode, final RoomCallback callback) {
        VoiceRoomPacketSender.createPk(rid, lUid, rUid, timeInSec, mode, new RoomSeqCb(callback));
    }


    public static void pkVote(final int rid, int uid, int coin, final RoomCallback callback) {
        VoiceRoomPacketSender.pkVote(rid, uid, coin, new RoomSeqCb(callback));
    }

    public static void sendTempRoomGift(final GiftSendInfo sendInfo, final SendGiftCallback callback) {
        checkSendRoomGift(sendInfo, new RoomCallback(ILifeOwnerKt.getLife(callback)) {
            @Override
            public void onSuccess(int rid) {
                if (callback != null) {
                    callback.onSuccess(rid, sendInfo.comboId, sendInfo.comboTimes);
                }
            }

            @Override
            public void onFail(String msg) {
                if (callback != null) {
                    callback.onFail(msg);
                }
            }
        });
    }

    public static void sendGift(final GiftSendInfo sendInfo, final boolean tmpRoom, final RoomCallback callback) {
        UserService.get().getCacheSimpleUser(sendInfo.recUid, new LifeUserSimpleInfoCallback(callback) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                doSendGift(sendInfo, tmpRoom, callback, simpleInfo.nickname);
            }

            @Override
            public void onUserInfoFailed(String description) {
                doSendGift(sendInfo, tmpRoom, callback, String.valueOf(sendInfo.recUid));
            }
        });
    }

    private static void doSendGift(GiftSendInfo sendInfo, boolean tmpRoom, RoomCallback callback, String s) {
        IReference<RoomCallback> reference = IReferenceKt.wrap(callback);
        VoiceRoomPacketSender.sendGift(sendInfo, s, tmpRoom, new LifeSeqCallbackProxy() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                if (sendInfo.giftId == Gift.GIFT_FUEL_GOOD_ID) {
                    ApiService.of(TrackApi.class).appClick(TrackScreenName.VOICE_ROOM_CHAT, ResUtil.getStr(R.string.track_supply_times, sendInfo.giftNum));
                }
                IReferenceKt.run(reference, callback -> callback.onSuccess(sendInfo.rid));
            }

            @Override
            public void onFail(RspHeadInfo head) {
                if (head.code == RspHeadInfo.ERROR_CODE_FUEL_NOT_ENOUGH) {
                    EventBus.getDefault().post(new VoiceRoomFuelNotEnoughEvent());
                }
                IReferenceKt.run(reference, callback -> callback.onFail(head.desc));
            }
        });
    }

    public static void modRoomTheme(final int rid, int themeId, String url, final RoomCallback callback) {
        VoiceRoomPacketSender.modRoomTheme(rid, themeId, url, new RoomSeqCb(callback));
    }

    public static void modRoomLabel(final int rid, int labelType, final RoomCallback callback) {
        VoiceRoomPacketSender.modRoomLabel(rid, labelType, new RoomSeqCb(callback));
    }

    public static void setAdmin(final int rid, int uid, final RoomCallback callback) {
        VoiceRoomPacketSender.setAdmin(rid, uid, new RoomSeqCb(callback));
    }

    public static void delAdmin(final int rid, List<Integer> uids, final RoomCallback callback) {
        VoiceRoomPacketSender.delAdminList(rid, uids, new RoomSeqCb(callback));
    }

    public static void delBlack(final int rid, List<Integer> uids, final RoomCallback callback) {
        VoiceRoomPacketSender.delBlackList(rid, uids, new RoomSeqCb(callback));
    }

    public static void inviteSpeak(final int rid, int uid, int seatNum, RoomCallback callback) {
        VoiceRoomPacketSender.inviteSpeak(rid, uid, seatNum, new RoomSeqCb(callback));
    }

    public static void inviteSpeakReply(final int rid, final boolean isAccept, final int seatNum, final int inviteUid, final RoomCallback callback) {
        if (isAccept) {
            IDAuthCheckManager.doTaskOrShowNeedCertificate(ActivityTaskManager.getInstance().getTopActivity(), AuthApi.SCENE_SIT, () ->
                    VoiceRoomPacketSender.sitInviteReply(rid, true, seatNum, inviteUid, new RoomSeqCb(callback)));
        } else {
            VoiceRoomPacketSender.sitInviteReply(rid, false, seatNum, inviteUid, new RoomSeqCb(callback));
        }
    }

    public static void setBgMusic(int rid, int musicId) {
        VoiceRoomPacketSender.setBgMusicReq(rid, musicId, new DefaultSeqCallback());
    }

    public static void grabRedPacketReq(int rid, int totalWeight, List<Integer> redRandNums) {
        VoiceRoomPacketSender.grabRedPacketReq(rid, totalWeight, redRandNums, new DefaultSeqCallback());
    }

    public static void sendUserRedPacketReq(int rid, int stuffId, int rpType, String password, boolean isSendBarrage, int delaySec, int rpSkinId, final RoomCallback callback) {
        VoiceRoomPacketSender.sendRedPacketReq(rid, stuffId, rpType, password, false, delaySec, isSendBarrage, rpSkinId, new RoomSeqCb(callback));
    }

    public static void sendSystemRpReq(int rid, final RoomCallback callback) {
        VoiceRoomPacketSender.sendRedPacketReq(rid, 0, 0, "", true, 0, false, 0, new RoomSeqCb(callback));
    }

    public static void sendGetContributeReq(int rid, final HeatCallback callback) {
        VoiceRoomPacketSender.getHeatContributeReq(rid, new LifeSeqCallback(ILifeOwnerKt.getLife(callback)) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                if (callback != null) {
                    Map<Integer, Integer> map = ((TmpRoomPackets.GetHeatContributionRsp) head.message).getHeatContributionMap();
                    callback.onSuccess(map);
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                callback.onFailed(head.code, head.desc);
            }
        });
    }

    public static void checkSendRoomGift(GiftSendInfo sendInfo, final RoomCallback callback) {
        if (sendInfo.scope == GiftSendInfo.VOICE_SCOPE_SIT || sendInfo.scope == GiftSendInfo.VOICE_SCOPE_ALL) {
            sendMulGift(sendInfo, callback);
        } else {
            sendGift(sendInfo, true, callback);
        }
    }

    public static void sendMulGift(GiftSendInfo info, final RoomCallback callback) {
        VoiceRoomPacketSender.multiGiftReq(info, new LifeSeqCallback(ILifeOwnerKt.getLife(callback)) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                if (callback != null) {
                    callback.onSuccess(head.rid);
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public static void sendSlot(int rid, int scope, int count, final RoomCallback callback) {
        VoiceRoomPacketSender.lotteryDrawReq(rid, scope, count, new LifeSeqCallback(ILifeOwnerKt.getLife(callback)) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                callback.onSuccess(head.rid);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
                callback.onFail(head.desc);
            }
        });
    }

    public static void inviteDraw(final int rid, int uid, final RoomCallback callback) {
        VoiceRoomPacketSender.drawInvite(rid, uid, new RoomSeqCb(callback));
    }

    public static void drawInviteConfirm(int rid, int whoInvite, boolean agree, RoomCallback callback) {
        VoiceRoomPacketSender.drawInviteConfirm(rid, whoInvite, agree, new RoomSeqCb(callback));
    }

    public static void closeDrawWidget(int rid, RoomCallback callback) {
        VoiceRoomPacketSender.closeDrawWidget(rid, new RoomSeqCb(callback));
    }

    public static void syncDraw(int version, int traceSeq, int rid) {
        DrawGuessPacketSender.setRid(rid);
        DrawGuessSenderPresenter.syncGame(version, traceSeq);
    }

    public static void setDrawWord(int rid, String word, RoomCallback callback) {
        DrawGuessPacketSender.setRid(rid);
        DrawGuessPacketSender.selectWord(word, "", new RoomSeqCb(callback));
    }

    public static void endDraw(int rid, RoomCallback callback) {
        DrawGuessPacketSender.setRid(rid);
        DrawGuessPacketSender.tmpEndDraw(new RoomSeqCb(callback));
    }

    public static void showIdentity(int rid, RoomCallback callback) {
        VoiceRoomPacketSender.audioMatchShowReq(rid, new RoomSeqCb(callback));
    }

    public static void requestShowIdentity(int rid, RoomCallback callback) {
        VoiceRoomPacketSender.audioMatchRequestShowReq(rid, new RoomSeqCb(callback));
    }

    static class RoomSeqCb extends LifeSeqCallback {
        private final RoomCallback cb;

        RoomSeqCb(RoomCallback cb) {
            super(ILifeOwnerKt.getLife(cb));
            this.cb = cb;
        }

        @Override
        public void onSuccess(RspHeadInfo head) {
            if (cb != null) {
                cb.onSuccess(head.rid);
            }
        }

        @Override
        public void onFail(RspHeadInfo head) {
            if (cb != null) {
                cb.onFail(head.desc);
            }
        }
    }
}