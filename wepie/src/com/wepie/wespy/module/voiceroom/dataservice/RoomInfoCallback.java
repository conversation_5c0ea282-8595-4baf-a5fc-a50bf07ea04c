package com.wepie.wespy.module.voiceroom.dataservice;

import com.wejoy.weplay.ex.ILifeOwner;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;

/**
 * Created by geeksammao on 24/10/2017.
 */

public abstract class RoomInfoCallback implements ILifeOwner {
    public abstract void onSuccess(VoiceRoomInfo voiceRoomInfo);

    public abstract void onFail(String msg);

    public void onNeedPwd() {

    }

    public void onPlatformNotMatch(String msg) {

    }

    public void onGameVersionTooLow(VoiceRoomInfo roomInfo) {

    }

    public void hostNotOnline(int rid, TmpRoomPackets.HostRoomInfo hostRoomInfo, String msg) {

    }

    public void onBlocked() {

    }
}