package com.wepie.wespy.module.voiceroom.dataservice;

import android.content.Context;
import android.view.View;

import androidx.annotation.Nullable;

import com.huiwan.base.util.ContextUtil;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeOwner;
import com.wejoy.weplay.ex.ILifeUtil;

/**
 * Created by geeksammao on 24/10/2017.
 */

public abstract class RoomCallback implements ILifeOwner {
    private final ILife mLife;

    public RoomCallback(ILife life) {
        this.mLife = life;
    }

    public RoomCallback(View view) {
        this.mLife = ILifeUtil.toLife(view);
    }

    public RoomCallback(Context context) {
        this.mLife = ContextUtil.getLife(context);
    }

    public abstract void onSuccess(int rid);

    public abstract void onFail(String msg);

    @Nullable
    @Override
    public ILife getLife() {
        return mLife;
    }
}