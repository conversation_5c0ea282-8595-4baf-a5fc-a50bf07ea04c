package com.wepie.wespy.module.voiceroom.dataservice

import android.graphics.Rect
import android.graphics.RectF
import android.os.CountDownTimer
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.huiwan.base.util.TimeUtil
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import java.util.*

class RpDelayViewModel {
    private val _rpLiveData = MutableLiveData<VoiceRoomInfo.RedPacket?>()
    private val _showBig = MutableLiveData<BigRpInfo?>()
    private val _initSmallRect = MutableLiveData<Rect>()
    private val _countDownLiveData = MutableLiveData<Long>(0)
    val liveData: LiveData<VoiceRoomInfo.RedPacket?> = _rpLiveData
    val countDownLiveData: LiveData<Long> = _countDownLiveData
    val showBig: LiveData<BigRpInfo?> = _showBig
    val initSmallRect: LiveData<Rect> = _initSmallRect
    var showFirstMap = Hashtable<String, Boolean>()
    private var timer: CountDownTimer? = null
    private var fiveSecondTimer: CountDownTimer? = null

    fun updateRedPacket(redPacket: VoiceRoomInfo.RedPacket?) {
        val cur = _rpLiveData.value
        val redPacketInfo = VoiceRoomInfo.RedPacket()
        redPacketInfo.list = mutableListOf<VoiceRoomInfo.WaitingRedPacket>()
        redPacket?.list?.let {
            redPacketInfo.list.addAll(it)
        }

        if (cur == null) {
            if (redPacket != null) {
                _rpLiveData.value = redPacketInfo
                setShowFirstMapValue();
            }
        } else {
            if (cur != redPacket) {
                _rpLiveData.value = redPacketInfo
                setShowFirstMapValue();
            }
        }
        checkCountDown()
        checkShowFirstMap()
    }

    private fun setShowFirstMapValue() {
        val rp = _rpLiveData.value
        rp?.list?.forEach {
            if (it != null && !showFirstMap.containsKey(it.rpId)) {
                showFirstMap[it.rpId] = false
            }
        }
    }

    private fun checkShowFirstMap() {
        val rp = _rpLiveData.value
        rp?.list?.forEach {
            if (it != null && !showFirstMap.getValue(it.rpId)) {
                val rpPositionInWaitingList = getRpPositionByRpId(it.rpId, rp)
                triggerShowFirstMap(it.rpId, rpPositionInWaitingList)
                return@forEach
            }
        }
    }

    private fun getRpPositionByRpId(rpId: String?, rp: VoiceRoomInfo.RedPacket): Int {
        var count = 0;
        var tempCount = 0
        for (redPacket in rp.list) {
            if (redPacket.rpId.equals(rpId)) {
                count = tempCount
                break
            }
            tempCount++;
        }
        return count
    }

    private fun triggerShowFirstMap(rpId: String?, rpPositionInWaitingList: Int) {
        val bigRpInfo = BigRpInfo(true, rpPositionInWaitingList)
        showBig(bigRpInfo)
        if (rpId != null) {
            showFirstMap[rpId] = true
        };
        newRedPacketChangSmallAfter5s()
    }

    fun deleteRpInShowFirstMap(rpId: String?) {
        showFirstMap.remove(rpId)
    }

    private fun newRedPacketChangSmallAfter5s() {
        cancelTimerWhenFiveSecondTimerNotNull()

        if (fiveSecondTimer == null) {
            fiveSecondTimer = object : CountDownTimer(5 * 1000, 1000) {
                override fun onTick(millisUntilFinished: Long) {}

                override fun onFinish() {
                    fiveSecondTimer = null
                    showBig(BigRpInfo(false, 0))
                }
            }
            fiveSecondTimer?.start()
        }
    }

    private fun checkCountDown() {
        val rp = _rpLiveData.value
        if (rp?.list == null || rp.list.isEmpty()) {
            return
        }
        val div = rp.list[0].startTimeInMs - TimeUtil.getServerTime()
        val newCountDownTime = div / 1000
        val currentCountDownTime = _countDownLiveData.value
        if (newCountDownTime < currentCountDownTime!! && timer != null) {
            timer!!.cancel()
            timer = null
        }

        if (timer == null) {
            if (div <= 0) {
                return
            }
            timer = object : CountDownTimer(div, 500) {
                override fun onTick(millisUntilFinished: Long) {
                    _countDownLiveData.value = millisUntilFinished / 1000
                }

                override fun onFinish() {
                    timer = null
                    checkCountDown()
                }
            }
            timer?.start()
        }
    }

    fun showBig(bigRpInfo: BigRpInfo) {
        _showBig.value = bigRpInfo
        if (!bigRpInfo.showBig) {
            cancelTimerWhenFiveSecondTimerNotNull()
        }
    }

    fun initSmallRect(rect: Rect) {
        _initSmallRect.value = rect
    }

    private fun cancelTimerWhenFiveSecondTimerNotNull() {
        if (fiveSecondTimer != null) {
            fiveSecondTimer!!.cancel()
            fiveSecondTimer = null
        }
    }
}