package com.wepie.wespy.module.voiceroom.dataservice;

import androidx.annotation.MainThread;
import androidx.annotation.WorkerThread;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.huiwan.base.str.ResUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.callback.SeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.user.LoginHelper;
import com.wejoy.weplay.ex.GlobalLife;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.net.tcp.packet.BigWinner;
import com.wepie.wespy.net.tcp.sender.BigWinnerPacketSender;

public class BigWinnerViewModel {
    private static final String TAG = "BigWinnerViewModel";
    private final BwGameInfo gameInfo = new BwGameInfo(BigWinner.BWGameInfo.newBuilder().build(), false);
    private final MutableLiveData<BwGameInfo> gameInfoLiveData = new MutableLiveData<>(gameInfo);
    private final MutableLiveData<VoteEvent> voteInfoLiveData = new MutableLiveData<>(new VoteEvent());
    private final MutableLiveData<ResultEvent> resultEventLiveData = new MutableLiveData<>(new ResultEvent());
    private int voteVersion = 0;
    private int resultVersion = 0;

    public BigWinnerViewModel() {
    }

    public MutableLiveData<BwGameInfo> getGameInfoLiveData() {
        return gameInfoLiveData;
    }

    public MutableLiveData<VoteEvent> getVoteEvent() {
        return voteInfoLiveData;
    }

    public MutableLiveData<ResultEvent> getResultEventLiveData() {
        return resultEventLiveData;
    }

    public LiveData<Boolean> getShowSmallLiveData() {
        return gameInfo.showSmall;
    }

    public void reset() {
        ThreadUtil.runOnUiThread(this::doReset);
    }

    @MainThread
    private void doReset() {
        gameInfo.gameInfo = BigWinner.BWGameInfo.newBuilder().build();
        gameInfo.showSmall.setValue(false);
        voteVersion = 0;
        resultVersion = 0;
        voteInfoLiveData.setValue(new VoteEvent());
        resultEventLiveData.setValue(new ResultEvent());
        gameInfoLiveData.setValue(gameInfo);
        HLog.d(TAG, "reset");
    }

    @WorkerThread
    public void postPushInfo(BigWinner.BWGameInfoPush push) {
        ThreadUtil.runOnUiThread(() -> {
            HLog.d(TAG, "postPushInfo: {}", push);
            if (push.hasBwInfo()) {
                gameInfo.gameInfo = push.getBwInfo();
                if (gameInfo.gameInfo.getStateValue() == BigWinner.BigWinnerGameState.BW_STATE_GAMING_VALUE) {
                    if (!push.hasVoteInfo()) {
                        for (BigWinner.BWGamerInfo gamerInfo : gameInfo.gameInfo.getGamerInfosList()) {
                            if (gamerInfo.getUid() == LoginHelper.getLoginUid()) {
                                gameInfo.showSmall.setValue(false);
                                break;
                            }

                        }
                    }
                }
                gameInfoLiveData.setValue(gameInfo);
            }
            if (push.hasVoteInfo()) {
                voteInfoLiveData.setValue(new VoteEvent(++voteVersion, push.getVoteInfo()));
            }
        });
    }

    @WorkerThread
    public void postResultInfo(BigWinner.BWRoundResultInfo resultInfo) {
        ThreadUtil.runOnUiThread(() -> {
            HLog.d(TAG, "postResultInfo: {}", resultInfo);
            if (gameInfo.isOpen()) {
                ResultEvent resultEvent = new ResultEvent();
                resultEvent.version = ++resultVersion;
                resultEvent.resultInfo = resultInfo;
                resultEvent.postResultTime = System.currentTimeMillis();
                gameInfo.gameInfo = BigWinner.BWGameInfo.newBuilder(gameInfo.gameInfo).setState(BigWinner.BigWinnerGameState.BW_STATE_SHOW_RESULT).build();
                gameInfoLiveData.setValue(gameInfo);
                resultEventLiveData.setValue(resultEvent);
            }
        });
    }

    @WorkerThread
    public void postOpen(boolean bigWinnerOpen) {
        ThreadUtil.runOnUiThread(() -> {
            HLog.d(TAG, "postOpen: {}", bigWinnerOpen);
            if (bigWinnerOpen && !gameInfo.isOpen()) {
                sync();
            } else if (!bigWinnerOpen) {
                doReset();
            }
        });
    }

    private void sync() {
        BigWinnerPacketSender.reqSync(VoiceRoomService.getInstance().getRid(), gameInfo.getGameInfo().getVersion(), new LifeSeqCallback(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                BigWinner.BWGameInfo info = (BigWinner.BWGameInfo) head.message;
                if (info.getVersion() == gameInfo.getGameInfo().getVersion()) {
                    HLog.d(TAG, "sync version same, keep value: {}", info);
                } else {
                    HLog.d(TAG, "sync version change, update value: {}", info);
                    gameInfo.gameInfo = info;
                }
                if (info.getStateValue() != 0) {
                    gameInfoLiveData.setValue(gameInfo);
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
            }
        });
    }

    public boolean changeSmall(boolean showSmall) {
        if (Boolean.TRUE.equals(gameInfo.showSmall.getValue()) == showSmall) {
            return false;
        }
        gameInfo.showSmall.setValue(showSmall);
        return true;
    }

    public void reqSync() {
        sync();
    }

    public void reqClose(SeqCallback callback) {
        BigWinnerPacketSender.reqChangeBigWinnerStatus(VoiceRoomService.getInstance().getRid(), 0, false, callback);
    }

    public void reqVote(SeqCallback callback) {
        reqJoinOrVote(false, callback);
    }

    public void reqJoin(SeqCallback callback) {
        reqJoinOrVote(true, callback);
    }

    private void reqJoinOrVote(boolean join, SeqCallback callback) {
        boolean ret = interceptCoinReq(callback);
        if (ret) {
            return;
        }
        BigWinnerPacketSender.reqVote(VoiceRoomService.getInstance().getRid(), 1, join, callback);
    }

    private boolean interceptCoinReq(SeqCallback callback) {
        Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(gameInfo.gameInfo.getGiftId());
        if (gift != null) {
            if (gift.getPrice() > LoginHelper.getCoin()) {
                RspHeadInfo headInfo = new RspHeadInfo(RspHeadInfo.ERROR_CODE_ROOM_COIN_NOT_ENOUGH, "");
                headInfo.desc = ResUtil.getStr(R.string.coin_not_enough_simple);
                callback.onFail(headInfo);
                return true;
            }
        }
        return false;
    }

    public void trigger() {
        gameInfoLiveData.setValue(gameInfoLiveData.getValue());
        voteInfoLiveData.setValue(voteInfoLiveData.getValue());
        resultEventLiveData.setValue(resultEventLiveData.getValue());
    }


    public static class BwGameInfo {
        BigWinner.BWGameInfo gameInfo;
        MutableLiveData<Boolean> showSmall = new MutableLiveData<>(false);
        int lastLoseUid = -1;
        int winnerUid = -1;

        public BwGameInfo(BigWinner.BWGameInfo gameInfo, boolean showSmall) {
            this.gameInfo = gameInfo;
            this.showSmall.setValue(showSmall);
        }

        public int getLastLoseUid() {
            return lastLoseUid;
        }

        public void setLastLoseUid(int uid) {
            lastLoseUid = uid;
        }

        public int getWinnerUid() {
            return winnerUid;
        }

        public void setWinnerUid(int uid) {
            winnerUid = uid;
        }

        public BigWinner.BWGameInfo getGameInfo() {
            return gameInfo;
        }

        public boolean isShowSmall() {
            return Boolean.TRUE.equals(showSmall.getValue());
        }

        public boolean isOpen() {
            return gameInfo.getStateValue() != 0;
        }
    }

    public static class VoteEvent {
        int version;
        BigWinner.VoteInfo voteInfo;

        public VoteEvent() {
        }

        public VoteEvent(int version, BigWinner.VoteInfo voteInfo) {
            this.version = version;
            this.voteInfo = voteInfo;
        }

        public int getVersion() {
            return version;
        }

        public BigWinner.VoteInfo getVoteInfo() {
            return voteInfo;
        }
    }

    public static class ResultEvent {
        int version;
        long postResultTime = 0;
        BigWinner.BWRoundResultInfo resultInfo;

        public int getVersion() {
            return version;
        }

        public long getPostResultTime() {
            return postResultTime;
        }

        public BigWinner.BWRoundResultInfo getResultInfo() {
            return resultInfo;
        }
    }
}
