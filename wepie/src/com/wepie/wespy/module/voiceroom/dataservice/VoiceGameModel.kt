package com.wepie.wespy.module.voiceroom.dataservice

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.huiwan.configservice.ConfigHelper
import com.huiwan.base.ActivityTaskManager
import com.huiwan.constants.Common
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.littlegame.model.GameChessboardRes
import com.huiwan.littlegame.util.CocosSoundUtil
import com.huiwan.user.LoginHelper
import com.wejoy.littlegame.LittleGameSimpleInfo
import com.wejoy.weplay.ex.GlobalLife
import com.wepie.lib.api.plugins.track.ITrackExt
import com.wepie.liblog.main.HLog
import com.wepie.wespy.model.entity.match.TeamInfo
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.voiceroom.main.BaseVoiceRoomActivity
import com.wepie.wespy.net.tcp.sender.VoiceGameRoomPacketSender
import tmproom.Littlegame
import javax.annotation.Nullable

class VoiceGameModel {

    //littleGameInfo  的version
    var version: Int = 0

    private var _littleGameInfoLiveData: MutableLiveData<LittleGameInfo> =
        MutableLiveData(LittleGameInfo())

    var littleGameInfoLiveData: LiveData<LittleGameInfo> = _littleGameInfoLiveData

    val littleGameInfo: LittleGameInfo
        get() = littleGameInfoLiveData.value!!

    val littleGameSimpleInfo: LittleGameSimpleInfo
        get() = littleGameInfo.getLittleGameSimpleInfo()

    val littleGameType: Int
        get() = littleGameInfo.getLittleGameType()


    private fun handleLittleGameSyncRoom(syncRsp: Littlegame.GameSyncRsp): Boolean {
        if (version <= syncRsp.version) {
            version = syncRsp.version
            littleGameInfo.update(syncRsp)
            _littleGameInfoLiveData.postValue(littleGameInfo)
            return true
        }
        HLog.e(TAG, HLog.USR, "handleLittleGameSyncRoom $version ${syncRsp.version}")
        return false
    }

    /**
     * @return 玩家是否在游戏房的 游戏座位上
     */
    fun isInLittleGameSeat(uid: Int): Boolean {
        if (uid <= 0) {
            return false
        }
        littleGameInfo.gameSeats.forEach {
            if (it.uid == uid) {
                return true
            }
        }
        return false
    }

    fun isSelfInSeat() = isInLittleGameSeat(LoginHelper.getLoginUid())

    fun isUserReady(uid: Int): Boolean {
        littleGameInfo.gameSeats.forEach {
            if (it.uid == uid) {
                return it.ready
            }
        }
        return false
    }

    fun clear() {
        _littleGameInfoLiveData.value = LittleGameInfo()
        reset()
    }

    fun reset() {
        version = 0
    }

    fun update(room: VoiceRoomInfo) {
        if (version == 0) {
            checkAndReqSync()
        }
    }

    fun checkAndReqSync() {
        val roomInfo = VoiceRoomService.getInstance().roomInfo
        if (roomInfo.isVoiceGameRoom && roomInfo.rid > 0) {
            reqSync(roomInfo.rid)
        }
    }

    private fun reqSync(rid: Int) {
        HLog.d(TAG, HLog.USR, "req sync rid:{}", rid)
        VoiceGameRoomPacketSender.syncLittleGameInfo(rid, object : LifeSeqCallback(GlobalLife) {
            override fun onSuccess(head: RspHeadInfo?) {
                val syncRsp = head?.message as? Littlegame.GameSyncRsp
                if (syncRsp == null) {
                    HLog.d(TAG, HLog.USR, "handleLittleGameSyncRoom: SyncRoomInfoRsp = null")
                    return
                }
                handleLittleGameSyncRoom(syncRsp)
            }

            override fun onFail(head: RspHeadInfo?) {
                HLog.d(
                    TAG, HLog.USR,
                    "sync failed code ={},msg={}", (head?.code ?: -1), (head?.desc).toString()
                )
            }
        }
        )
    }

    companion object {
        const val TAG = "VoiceGameViewModel"

        fun gamerNum(mode: Int): Int {
            return when (mode) {
                Common.MATCH_MATE_ONE_GAMER_TWO, Common.ROOM_MATE_ONE_GAMER_TWO, Common.MATCH_MATE_TWO_GAMER_TWO, Common.ROOM_MATE_TWO_GAMER_TWO, Common.MATCH_MATE_KING_MATE, Common.ROOM_MATE_KING_MATE -> 2
                Common.MATCH_MATE_ONE_GAMER_THREE, Common.ROOM_MATE_ONE_GAMER_THREE -> 3
                Common.MATCH_MATE_ONE_GAMER_FOUR, Common.ROOM_MATE_ONE_GAMER_FOUR, Common.MATCH_MATE_TWO_GAMER_FOUR, Common.ROOM_MATE_TWO_GAMER_FOUR -> 4
                Common.MATCH_MATE_ONE_GAMER_FIVE, Common.ROOM_MATE_ONE_GAMER_FIVE -> 5
                Common.MATCH_MATE_ONE_GAMER_SIX, Common.ROOM_MATE_ONE_GAMER_SIX, Common.MATCH_MATE_TWO_GAMER_SIX, Common.ROOM_MATE_TWO_GAMER_SIX -> 6
                else -> {
                    HLog.d("VoiceGameRoomViewModel", " error mode not support: $mode")
                    0
                }
            }
        }
    }

    /**
     * 小游戏房 的 游戏信息
     */
    class LittleGameInfo {
        var gameSeats: List<GameSeat> = emptyList()
            private set
        var gameBasicInfo: GameBasicInfo = GameBasicInfo()
            private set

        // 是否仅麦上玩家允许游戏
        var isOnlySeatAllowGame: Boolean = false
            private set
        var littleGameState: LittleGameState = LittleGameState.Unknown
            private set
        var rid: Int = 0

        fun update(syncRsp: Littlegame.GameSyncRsp) {
            val protoBufLittleGameInfo = syncRsp.gameInfo
            isOnlySeatAllowGame = protoBufLittleGameInfo.onlySeatAllowGame
            littleGameState = createLittleGameState(protoBufLittleGameInfo.littleGameStateValue)
            rid = protoBufLittleGameInfo.rid
            gameBasicInfo = parseGameBasicInfo(protoBufLittleGameInfo.gameBasicInfo)
            parseGameSeats(
                protoBufLittleGameInfo.gameSeatsList, gamerNum(gameBasicInfo.mode)
            )
            HLog.d(
                TAG, HLog.USR,
                "update LittleGame rid:{},version:{},isOnlySeatAllowGame:{},littleGameState:{},gameBasicInfo:{},seats={}",
                rid, syncRsp.version, isOnlySeatAllowGame,
                littleGameState.name, gameBasicInfo,
                gameSeats.joinToString { "[uid:${it.uid},seatId:${it.seatId},isReady:${it.ready}]" }
            )
            checkGameState()
        }

        private fun checkGameState() {
            if (ActivityTaskManager.getInstance().topActivity !is BaseVoiceRoomActivity &&
                littleGameState != LittleGameState.Started
            ) {
                CocosSoundUtil.releaseAll()
            }
        }

        fun changeOnlySeatAllowGame(isOnlySeatAllowGame: Boolean) {
            this.isOnlySeatAllowGame = isOnlySeatAllowGame
        }

        fun getLittleGameType(): Int = gameBasicInfo.littleGameType

        fun getLittleGameSimpleInfo(): LittleGameSimpleInfo {
            return TrackVoiceLittleGameSimpleInfo(
                rid,
                gameBasicInfo.littleGameType,
                gameBasicInfo.mode,
                gameBasicInfo.gameMode,
                gameBasicInfo.betLevel,
                gameBasicInfo.currencyType
            )
        }

        fun isGaming(): Boolean {
            return littleGameState == LittleGameState.Started || littleGameState == LittleGameState.Created
        }

        class TrackVoiceLittleGameSimpleInfo(
            val rid: Int,
            littleGameType: Int,
            mode: Int,
            gameMode: Int,
            betLevel: Int,
            currencyType: Int
        ) : LittleGameSimpleInfo(
            littleGameType, mode, gameMode, betLevel, currencyType
        ), ITrackExt {
            override fun getTrackExt(): MutableMap<String, Any> {
                val matchInfo = ConfigHelper.getInstance().getGameConfig(gameType)
                    .getLittleGameMatchInfo(betLevel, mode, gameMode, currencyType)
                return mutableMapOf(
                    "rid" to rid,
                    "game_type" to VoiceRoomService.getInstance().roomInfo.game_type,
                    "sub_game_type" to gameType,
                    "bet_level" to betLevel,
                    "match_mode" to TeamInfo.trackMatchMode(gameMode, mode),
                    "mode_type" to TeamInfo.trackModeType(gameType, gameMode),
                    "enter_coin" to (matchInfo?.coin ?: -1)
                )
            }
        }

        data class GameSeat(
            val uid: Int = 0,
            val seatId: Int = 0,
            val ready: Boolean = false,
        ) {
            override fun toString(): String {
                return "GameSeat(uid=$uid, seatId=$seatId, ready=$ready)"
            }
        }

        //游戏基本信息
        data class GameBasicInfo(
            val littleGameType: Int = 0,
            val betLevel: Int = 0,
            val mode: Int = 0,
            val gameMode: Int = 0,
            val currencyType: Int = 0,
        ) {
            fun getMatchInfoID(): Int {
                return "${this.betLevel}_${this.mode}_${this.currencyType}_${this.gameMode}".hashCode()
            }

            override fun toString(): String {
                return "littleGameType:${littleGameType},betLevel:${betLevel},mode:${mode},gameMode:${gameMode},currencyType:${currencyType}"
            }
        }

        fun kicGameUser(kickedUid: Int) {
            clearGameSeatByUid(kickedUid)
        }

        fun parseGameSeats(protoBufSeats: List<Littlegame.GameSeat>, seatNum: Int) {
            val tempList = if (gameSeats.size == seatNum) {
                ArrayList(gameSeats)
            } else {
                val freshGameSeats = ArrayList<GameSeat>(seatNum)
                for (i in 0 until seatNum) {
                    freshGameSeats.add(GameSeat(0, i, false))
                }
                freshGameSeats
            }
            val beforeFirstUid = gameSeats.find { it.seatId == 1 }?.uid ?: 0
            val afterFirst = protoBufSeats.find { it.seatId == 1 }?.uid ?: 0
            if (afterFirst != beforeFirstUid && afterFirst != 0) {
                GameChessboardRes.tryPreloadUidChessRes(afterFirst)
            }
            gameSeats = tempList
            for (seat in protoBufSeats) {
                if (seat.seatId > 0 && seat.seatId <= gameSeats.size) {
                    tempList[(seat.seatId - 1)] =
                        tempList[(seat.seatId - 1)].copy(
                            uid = seat.uid,
                            seatId = seat.seatId,
                            ready = seat.ready
                        )
                }
            }
        }


        @Nullable
        fun getGameSeatByUid(uid: Int): GameSeat? {
            return gameSeats.firstOrNull {
                it.uid == uid
            }
        }

        private fun clearGameSeatByUid(uid: Int) {
            var targetIndex = -1
            for (i in gameSeats.indices) {
                if (gameSeats[i].uid == uid) {
                    targetIndex = i
                    break
                }
            }
            if (targetIndex >= 0) {
                val tempList = ArrayList(gameSeats)
                tempList[targetIndex] = tempList[targetIndex].copy(uid = 0, ready = false)
                gameSeats = tempList
            }
        }

        fun updateGameBasicInfo(protoGameBasicInfo: Littlegame.GameBasicInfo) {
            gameBasicInfo = parseGameBasicInfo(protoGameBasicInfo)
        }

        private fun parseGameBasicInfo(protoGameBasicInfo: Littlegame.GameBasicInfo): GameBasicInfo {
            return GameBasicInfo(
                protoGameBasicInfo.littleGameType,
                protoGameBasicInfo.betLevel,
                protoGameBasicInfo.mode,
                protoGameBasicInfo.gameMode,
                protoGameBasicInfo.currencyType
            )
        }

        companion object {
            fun createLittleGameState(value: Int): LittleGameState {
                when (value) {
                    1 -> return LittleGameState.Init
                    2 -> return LittleGameState.Waiting
                    3 -> return LittleGameState.Created
                    4 -> return LittleGameState.Started
                    5 -> return LittleGameState.Over
                }
                return LittleGameState.Unknown
            }
        }
    }

    enum class LittleGameState {
        Unknown,

        //初始化
        Init,

        //等待游戏服务器返回开局结果
        Waiting,

        //创房成功
        Created,

        //开局
        Started,

        //结束
        Over
    }

    //游戏开局状态
    enum class LittleGameStartResult {
        Undefined,

        // 开局成功处理
        StartSuccess,

        // 开局失败
        StartFail,

        //有用户拉起cocos失败进房超时, 客户端关闭cocos
        CloseCocos

    }
}