package com.wepie.wespy.module.voiceroom.dataservice;

import androidx.lifecycle.MutableLiveData;

import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.voiceroom.galaWeb.GalaWebAppClickInfo;
import com.wepie.wespy.module.voiceroom.galaWeb.GalaWebShowInfo;
import com.wepie.wespy.module.voiceroom.galaWeb.GalaWebToastInfo;

import java.util.List;
import java.util.Objects;

public class GalaWebViewModel {
    private static final String TAG = "GalaWebViewModel";
    public MutableLiveData<List<VoiceRoomInfo.OperatingActivityInfo>> h5InfoLiveData = new MutableLiveData<>();
    public MutableLiveData<GalaWebToastInfo> isToast = new MutableLiveData<>();
    public MutableLiveData<Integer> showSmallItem = new MutableLiveData<>();
    public GalaWebAppClickInfo appClickInfo;
    public MutableLiveData<Boolean> showH5Activity = new MutableLiveData<>();

    public GalaWebViewModel() {
    }

    /**
     * sync 时，数据内容发生变化，才更新 h5InfoLiveData, 不然每次 sync 都会刷新对应的页面
     */
    public void updateGalaWebInfo(List<VoiceRoomInfo.OperatingActivityInfo> h5Info, GalaWebShowInfo galaWebShowInfo) {
        if (galaWebShowInfo.isSync()) {
            if (!Objects.equals(h5InfoLiveData.getValue(), h5Info)) {
                h5InfoLiveData.setValue(h5Info);
            }
            if (!Objects.equals(showH5Activity.getValue(), true)) {
                showH5Activity.setValue(true);
            }
            if (galaWebShowInfo.isOptChanged()) {
                isToast.setValue(getGalaWebInfo(h5Info));
                showSmallItem.setValue(getGalaWebItem(h5Info));
            }
        } else {
            h5InfoLiveData.setValue(h5Info);
            isToast.setValue(getGalaWebInfo(h5Info));
            showSmallItem.setValue(getGalaWebItem(h5Info));
            showH5Activity.setValue(true);
        }
    }

    private GalaWebToastInfo getGalaWebInfo(List<VoiceRoomInfo.OperatingActivityInfo> h5Info) {
        for (int i = 0; i < h5Info.size(); i++) {
            if (h5Info.get(i).isToast) {
                return new GalaWebToastInfo(true, h5Info.get(i));
            }
        }
        return new GalaWebToastInfo(false, new VoiceRoomInfo.OperatingActivityInfo());
    }

    private int getGalaWebItem(List<VoiceRoomInfo.OperatingActivityInfo> h5Info) {
        for (int i = 0; i < h5Info.size(); i++) {
            if (h5Info.get(i).isToast) {
                return i;
            }
        }
        return 0;
    }

    public void updateVoiceRoomInfoForAppClick(VoiceRoomInfo appClick) {
        if (this.appClickInfo == null) {
            if (appClick != null) {
                this.appClickInfo = new GalaWebAppClickInfo(appClick.game_type, appClick.rid);
            }
        } else {
            if (isAppClickInfoChanged(this.appClickInfo, appClick)) {
                this.appClickInfo = new GalaWebAppClickInfo(appClick.game_type, appClick.rid);
            }
        }

    }

    private boolean isAppClickInfoChanged(GalaWebAppClickInfo cur, VoiceRoomInfo appClick) {
        return cur.getGameType() != appClick.game_type || cur.getRoomRid() != appClick.rid;
    }

    public void hideGalaWeb() {
        if (showH5Activity.getValue() != null && showH5Activity.getValue()) {
            showH5Activity.setValue(false);
        }
    }
}
