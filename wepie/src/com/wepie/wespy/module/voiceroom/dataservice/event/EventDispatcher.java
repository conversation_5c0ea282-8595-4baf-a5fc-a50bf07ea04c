package com.wepie.wespy.module.voiceroom.dataservice.event;

import android.content.Intent;

import androidx.annotation.Nullable;

import com.google.protobuf.GeneratedMessageLite;
import com.huiwan.base.util.GlobalEventFlow;
import com.huiwan.component.gift.show.GiftShowInfo;
import com.huiwan.decorate.UserInfoChangeEvent;
import com.huiwan.user.entity.NewFriendRecord;
import com.huiwan.user.entity.User;
import com.huiwan.user.entity.UserTagStatus;
import com.huiwan.voiceservice.AgoraJoinChannelSuccessEvent;
import com.wepie.liblog.main.HLog;
import com.wepie.wejoy.utils.VideoEvent;
import com.wepie.wespy.base.ICareApiFactory;
import com.wepie.wespy.config.BroadcastConfig;
import com.wepie.wespy.helper.broadcast.BroadcastHelper;
import com.wepie.wespy.helper.wfloating.providers.AudioMatchProvider;
import com.wepie.wespy.model.FinishSingleChatSettingEvent;
import com.wepie.wespy.model.chat.event.NewFriendEvent;
import com.wepie.wespy.model.entity.ChargeUserDataInfo;
import com.wepie.wespy.model.entity.ChatMsg;
import com.wepie.wespy.model.entity.GroupChatMsg;
import com.wepie.wespy.model.entity.WPMessage;
import com.wepie.wespy.model.entity.family.FamilyAvailableBox;
import com.wepie.wespy.model.entity.fixroom.DrawGuessGameInfo;
import com.wepie.wespy.model.entity.iceball.IceGameBeforeStart;
import com.wepie.wespy.model.entity.marry.AuctionRoom;
import com.wepie.wespy.model.entity.marry.InvitationInfo;
import com.wepie.wespy.model.entity.marry.WeddingInfo;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.model.entity.other.MarryRemind2;
import com.wepie.wespy.model.entity.voiceroom.AuPushInfo;
import com.wepie.wespy.model.entity.voiceroom.CpGameInfo;
import com.wepie.wespy.model.entity.voiceroom.CpPushInfo;
import com.wepie.wespy.model.entity.voiceroom.RoomRpResultEventV2;
import com.wepie.wespy.model.entity.voiceroom.VideoPlayInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceGiftInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg;
import com.wepie.wespy.model.event.AdvanceRoomTipsEvent;
import com.wepie.wespy.model.event.BgMusicEvent;
import com.wepie.wespy.model.event.ChangeMicStatus;
import com.wepie.wespy.model.event.ChatMsgRspEvent;
import com.wepie.wespy.model.event.CoMatchStatusUpdateEvent;
import com.wepie.wespy.model.event.CoSeatUpdateEvent;
import com.wepie.wespy.model.event.ContentViewChildChangeEvent;
import com.wepie.wespy.model.event.ConversationUpdateEvent;
import com.wepie.wespy.model.event.EmoticonFavoriteChangeEvent;
import com.wepie.wespy.model.event.ExitMemberGroupEvent;
import com.wepie.wespy.model.event.FinishSendRedPacketEvent;
import com.wepie.wespy.model.event.FinishSingleChatEvent;
import com.wepie.wespy.model.event.FirstChargeBuyEvent;
import com.huiwan.littlegame.event.GiftCommonSendEvent;
import com.wepie.wespy.model.event.JoinChannelEvent;
import com.wepie.wespy.model.event.LeaveChannelEvent;
import com.wepie.wespy.model.event.MusicModeChangeEvent;
import com.wepie.wespy.model.event.NodeJsGameMsgChangeEvent;
import com.wepie.wespy.model.event.PlayNextEvent;
import com.wepie.wespy.model.event.ProtoMessageEvent;
import com.wepie.wespy.model.event.PushChatMsgEvent;
import com.wepie.wespy.model.event.PushRoomMsgEvent;
import com.wepie.wespy.model.event.PushRoomUserKickEvent;
import com.wepie.wespy.model.event.RedDotUpdateEvent;
import com.wepie.wespy.model.event.RefreshSelfSuccessEvent;
import com.wepie.wespy.model.event.ReportSuccessEvent;
import com.wepie.wespy.model.event.RoomGameTypeChangeEvent;
import com.wepie.wespy.model.event.RoomHeatValueEvent;
import com.wepie.wespy.model.event.RoomInfoUpdateEvent;
import com.wepie.wespy.model.event.RoomMsgRspEvent;
import com.wepie.wespy.model.event.RoomOwnerExitEvent;
import com.wepie.wespy.model.event.RoomPkEvent;
import com.wepie.wespy.model.event.RoomRpCountDownEvent;
import com.wepie.wespy.model.event.RoomRpEvent;
import com.wepie.wespy.model.event.RoomRpResultEvent;
import com.wepie.wespy.model.event.RoomSlotsEvent;
import com.wepie.wespy.model.event.ServerSendGiftEvent;
import com.wepie.wespy.model.event.ShowTabReddotEvent;
import com.wepie.wespy.model.event.SitDownEvent;
import com.wepie.wespy.model.event.TcpBindUserEvent;
import com.wepie.wespy.model.event.UpdateUserInfoEvent;
import com.wepie.wespy.model.event.UserEnterEvent;
import com.wepie.wespy.model.event.VideoPlayStateChangeEvent;
import com.wepie.wespy.model.event.VideoPlaySyncEvent;
import com.wepie.wespy.model.event.chat.AddMsg2PlayNextEvent;
import com.wepie.wespy.model.event.chat.ChangePlayNextStateEvent;
import com.wepie.wespy.model.event.chat.ChatGameInviteAcceptEvent;
import com.wepie.wespy.model.event.chat.ChatGameInviteDownloadedEvent;
import com.wepie.wespy.model.event.chat.ChatGameInviteErrorEvent;
import com.wepie.wespy.model.event.chat.ChatInviteCancelEvent;
import com.wepie.wespy.model.event.chat.ChatInviteEnterTeam;
import com.wepie.wespy.model.event.chat.ChatInviteRoomStartEvent;
import com.wepie.wespy.model.event.chat.CheckPlayNextEvent;
import com.wepie.wespy.model.event.chat.GameStateRefresh;
import com.wepie.wespy.model.event.chat.GroupNoteChangeEvent;
import com.wepie.wespy.model.event.chat.GroupSyncRspEvent;
import com.wepie.wespy.model.event.chat.GroupTeamMatchUpdateEvent;
import com.wepie.wespy.model.event.chat.GroupVideoBlockMsgEvent;
import com.wepie.wespy.model.event.chat.NetWorkChange;
import com.wepie.wespy.model.event.chat.PushNewFriend;
import com.wepie.wespy.model.event.chat.RecallGroupMsgEvent;
import com.wepie.wespy.model.event.chat.SingleChatDeleteMsgEvent;
import com.wepie.wespy.model.event.chat.SingleChatRefreshList;
import com.wepie.wespy.model.event.chat.SingleChatResendMsgEvent;
import com.wepie.wespy.model.event.chat.StopPlayNextEvent;
import com.wepie.wespy.model.event.chat.UnreadMsgNumChangeEvent;
import com.wepie.wespy.model.event.family.FamilyDissolveEvent;
import com.wepie.wespy.model.event.family.FamilyGroupBoxRefreshEvent;
import com.wepie.wespy.model.event.family.FamilyJoinEvent;
import com.wepie.wespy.model.event.family.FamilyKickedEvent;
import com.wepie.wespy.model.event.family.FamilyMainRefreshEvent;
import com.wepie.wespy.model.event.family.FamilyMemberChangeEvent;
import com.wepie.wespy.model.event.family.FamilyNoteRefreshEvent;
import com.wepie.wespy.model.event.family.FamilyRoleChangeEvent;
import com.wepie.wespy.model.event.family.FamilySelfRoleChangeEvent;
import com.wepie.wespy.model.event.family.FamilyShieldCountEvent;
import com.wepie.wespy.model.event.family.FamilyTipsContentEvent;
import com.wepie.wespy.model.event.iceball.IceBeforeStartEvent;
import com.wepie.wespy.model.event.iceball.IceCancelMatchEvent;
import com.wepie.wespy.model.event.iceball.IceStartMatchEvent;
import com.wepie.wespy.model.event.iceball.IceSwitchSeatEvent;
import com.wepie.wespy.model.event.iceball.IceSyncEvent;
import com.wepie.wespy.model.event.match.AudioMatchSuccessEvent;
import com.wepie.wespy.model.event.match.CancelMatchEvent;
import com.wepie.wespy.model.event.match.FinishMatchPrepareEvent;
import com.wepie.wespy.model.event.match.FinishTeamViewEvent;
import com.wepie.wespy.model.event.match.JoinTeamVoiceEvent;
import com.wepie.wespy.model.event.match.LeaveTeamEvent;
import com.wepie.wespy.model.event.match.RequestShowIdentityEvent;
import com.wepie.wespy.model.event.match.ShowIdentityEvent;
import com.wepie.wespy.model.event.match.SyncTeamRspEvent;
import com.wepie.wespy.model.event.match.TeamKickOutEvent;
import com.wepie.wespy.model.event.match.UpdateSuspendFloatEvent;
import com.wepie.wespy.model.event.other.AuctionListEvent;
import com.wepie.wespy.model.event.other.ChangeTabEvent;
import com.wepie.wespy.model.event.other.CircleRemindNumChangeEvent;
import com.wepie.wespy.model.event.other.DeepLinkEvent;
import com.wepie.wespy.model.event.other.GameNetWorkFailEvent;
import com.wepie.wespy.model.event.other.GameNetWorkReConnectedEvent;
import com.wepie.wespy.model.event.other.LoverHomeEvent;
import com.wepie.wespy.model.event.other.MarryRefreshEvent;
import com.wepie.wespy.model.event.other.MarryRemainEvent;
import com.wepie.wespy.model.event.other.PropBuyEvent;
import com.wepie.wespy.model.event.other.RefreshInvitationEvent;
import com.wepie.wespy.model.event.other.RefreshSelfTagEvent;
import com.wepie.wespy.model.event.other.RefreshUserCircleEvent;
import com.wepie.wespy.model.event.other.SidErrorEvent;
import com.wepie.wespy.model.event.voice.AdminChangeEvent;
import com.wepie.wespy.model.event.voice.AuPushEvent;
import com.wepie.wespy.model.event.voice.BroadcastVoiceGiftEvent;
import com.wepie.wespy.model.event.voice.BuyFuelEvent;
import com.wepie.wespy.model.event.voice.CareRankEvent;
import com.wepie.wespy.model.event.voice.CpGameInfoEvent;
import com.wepie.wespy.model.event.voice.CpPushInfoEvent;
import com.wepie.wespy.model.event.voice.LangFavoritesEvent;
import com.wepie.wespy.model.event.voice.LrcStatusChangeEvent;
import com.wepie.wespy.model.event.voice.MusicCurPlayChange;
import com.wepie.wespy.model.event.voice.MusicPlayInfoChange;
import com.wepie.wespy.model.event.voice.MusicWidgetChange;
import com.wepie.wespy.model.event.voice.RocketDialogDismissEvent;
import com.wepie.wespy.model.event.voice.RocketMeAwardGiftEvent;
import com.wepie.wespy.model.event.voice.RocketStatusChangedEvent;
import com.wepie.wespy.model.event.voice.VoiceRefreshMicStatusEvent;
import com.wepie.wespy.model.event.voice.VoiceRoomGameStateEvent;
import com.wepie.wespy.model.event.voice.VoiceSeatQueueRefreshEvent;
import com.wepie.wespy.module.discover.event.UpdateSquareBannerEvent;
import com.wepie.wespy.module.family.FamilyBenefitRedDotUpdateEvent;
import com.wepie.wespy.module.fdiscover.entity.CCommentInfo;
import com.wepie.wespy.module.fdiscover.event.DiscoverCommentEvent;
import com.wepie.wespy.module.fdiscover.event.DiscoverLikeEvent;
import com.wepie.wespy.module.fdiscover.event.DiscoverPostEvent;
import com.wepie.wespy.module.fdiscover.event.DiscoverPrivacyEvent;
import com.wepie.wespy.module.voiceroom.guards.GuardItem;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketAwardGiftInfo;
import com.wepie.wespy.module.voiceroom.rocket.model.RocketStatus;
import com.wepie.wespy.module.voiceroom.video.event.SelectLabelEvent;
import com.wepie.wespy.module.voiceroom.video.event.SyncMicResEvent;
import com.wepie.wespy.module.voiceroom.video.event.SyncVolumeResEvent;
import com.wepie.wespy.module.voiceroom.video.event.VideoPlayListUpdateEvent;
import com.wepie.wespy.module.voiceroom.video.event.YoutubeExitFullScreenEvent;
import com.wepie.wespy.module.voiceroom.video.event.YoutubeLoadUrlEvent;
import com.wepie.wespy.module.voiceroom.video.event.YoutubeNetDialogCloseEvent;
import com.wepie.wespy.module.voiceroom.video.event.YoutubePlayNewVideoEvent;
import com.wepie.wespy.module.voiceroom.video.event.YoutubeRiskSwitchChanged;
import com.wepie.wespy.module.voiceroom.video.event.YoutubeUpdatePositionByPushEvent;
import com.wepie.wespy.module.voiceroom.video.event.YoutubeUploadPositionToServerEvent;
import com.wepie.wespy.module.voiceroom.video.event.YoutubeVideoEndEvent;
import com.wepie.wespy.net.tcp.packet.ChatPackets;
import com.wepie.wespy.net.tcp.packet.GameMatchPushPackets;
import com.wepie.wespy.net.tcp.packet.NationFlagPacket;
import com.wepie.wespy.net.tcp.packet.RoomPushPackets;
import com.wepie.wespy.net.tcp.packet.VideoPushPackets;
import com.wepie.wespy.voiceroom.VoiceStatus;
import com.wepie.wespy.voiceroom.nationflag.UpdateNationFlagInfoEvent;

import org.greenrobot.eventbus.EventBus;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by geeksammao on 23/10/2017.
 */

public class EventDispatcher {

    public static void registerEventObserver(Object observer) {
        if (!EventBus.getDefault().isRegistered(observer)) {
            EventBus.getDefault().register(observer);
        }
    }

    public static void unregisterEventObserver(Object observer) {
        if (EventBus.getDefault().isRegistered(observer)) {
            EventBus.getDefault().unregister(observer);
        }
    }

    public static void postExitRoomEvent(boolean isForceExit) {
        postExitRoomEvent(isForceExit, "");
    }

    public static void postExitRoomEvent(boolean isForceExit, String reason) {
        postExitRoomEvent(isForceExit, reason, null, false);
    }

    public static void postExitRoomEvent(boolean isForceExit, String reason, VoiceRoomInfo beforeInfo, boolean selfExit) {
        postExitRoomEvent(isForceExit, reason, beforeInfo, selfExit, true);
    }

    public static void postExitRoomEvent(boolean isForceExit, String reason, VoiceRoomInfo beforeInfo, boolean selfExit, boolean notifyChildProcess) {
        EventBus.getDefault().post(new com.wepie.wespy.model.event.ExitRoomEvent(isForceExit, reason, beforeInfo, selfExit));
    }

    public static void postExitMemberGroup(RoomPushPackets.ExitMemberGroup exitMemberGroup) {
        EventBus.getDefault().post(new ExitMemberGroupEvent(exitMemberGroup));
    }

    public static void postLeaveChannelEvent(int rid, int source) {
        EventBus.getDefault().post(new LeaveChannelEvent(rid, source));
    }

    public static void postRoomMsgRspEvent(VoiceRoomMsg chatMsg, int code, String desc) {
        RoomMsgRspEvent roomMsgRspEvent = new RoomMsgRspEvent(chatMsg, code, desc);
        EventBus.getDefault().post(roomMsgRspEvent);
    }

    public static void postPushRoomMsgEvent(VoiceRoomMsg chatMsg) {
        PushRoomMsgEvent pushRoomMsgEvent = new PushRoomMsgEvent(chatMsg);
        EventBus.getDefault().post(pushRoomMsgEvent);
    }

    public static void postRoomInfoUpdateEvent(long features) {
        EventBus.getDefault().post(new RoomInfoUpdateEvent(features));
    }

    public static void postRoomInfoUpdateEvent(RoomInfoUpdateEvent event) {
        EventBus.getDefault().post(event);
    }

    public static void postRoomOwnerExitEvent() {
        EventBus.getDefault().post(new RoomOwnerExitEvent());
    }

    public static void postVoiceRoomGameTypeChangeEvent(int gameType) {
        EventBus.getDefault().post(new RoomGameTypeChangeEvent(gameType));
    }

    public static void postPlayNextEvent() {
        EventBus.getDefault().post(new PlayNextEvent());
    }

    public static void postMusicModeChangeEvent(int rid) {
        EventBus.getDefault().post(new MusicModeChangeEvent(rid));
    }

    public static void postRoomPkEvent(int pkType) {
        EventBus.getDefault().post(new RoomPkEvent(pkType));
    }

    public static void postVoiceRoomInviteSpeak(int seat_num, int inviteUid) {
        EventBus.getDefault().post(new VoiceRoomInviteSpeakEvent(seat_num, inviteUid));
    }

    public static void postVoiceRoomInviteDraw(int rid, int whoInvite) {
        EventBus.getDefault().post(new VoiceRoomInviteDrawEvent(rid, whoInvite));
    }

    public static void postVoiceRoomCloseDraw(int rid) {
        EventBus.getDefault().post(new VoiceRoomDrawCloseEvent(rid));
    }

    public static void postRoomRpEvent(RoomRpEvent event) {
        EventBus.getDefault().post(event);
    }

    public static void postRoomRpCountDownEvent(RoomRpCountDownEvent event) {
        EventBus.getDefault().post(event);
    }

    public static void postBgMusicEvent(BgMusicEvent event) {
        EventBus.getDefault().post(event);
    }

    public static void postSlotsResultEvent(RoomSlotsEvent event) {
        EventBus.getDefault().post(event);
    }

    public static void postRpResultEvent(RoomRpResultEvent event) {
        EventBus.getDefault().post(event);
    }

    public static void postRpResultEventV2(RoomRpResultEventV2 event) {
        EventBus.getDefault().post(event);
    }

    public static void postRoomHeatEvent(RoomHeatValueEvent event) {
        EventBus.getDefault().post(event);
    }

    public static void postPushRoomUserKickEvent(List<Integer> kicked_users) {
        EventBus.getDefault().post(new PushRoomUserKickEvent(kicked_users));
    }

    public static void postChangeMicStatus(boolean mic_on, int source) {
        EventBus.getDefault().post(new ChangeMicStatus(mic_on, source));
    }

    public static void postJoinChannel(VoiceRoomInfo roomInfo, boolean mic_on, int source, boolean isRejoin) {
        postJoinChannel(roomInfo, mic_on, source, isRejoin, false);
    }

    public static void postJoinChannel(VoiceRoomInfo roomInfo, boolean mic_on, int source, boolean isRejoin, boolean mainRestore) {
        EventBus.getDefault().post(new JoinChannelEvent(roomInfo, mic_on, source, isRejoin, mainRestore));
    }

    public static void postUserEnterEvent(int uid, String nickname, int enterAnimId, int userCarId) {
        EventBus.getDefault().post(new UserEnterEvent(uid, nickname, enterAnimId, userCarId));
    }

    public static void postAgoraJoinChannelSuccessEvent() {
        EventBus.getDefault().post(new AgoraJoinChannelSuccessEvent());
    }

    public static void postSitDownEvent() {
        EventBus.getDefault().post(new SitDownEvent());
    }

    public static void postIceBallMatch(IceGameBeforeStart iceGameBeforeStart) {
        EventBus.getDefault().post(new IceBeforeStartEvent(iceGameBeforeStart));
    }

    public static void postIceSwitchSeatEvent(IceSwitchSeatEvent event) {
        EventBus.getDefault().post(event);
    }

    public static void postIceCancelMatchEvent(IceCancelMatchEvent event) {
        EventBus.getDefault().post(event);
    }

    public static void postIceStartMatchEvent(IceStartMatchEvent event) {
        EventBus.getDefault().post(event);
    }

    public static void postIceSyncEvent() {
        EventBus.getDefault().post(new IceSyncEvent());
    }

    public static void postCancelMatch() {
        EventBus.getDefault().post(new CancelMatchEvent());
    }

    public static void postLeaveTeam(boolean isCancelMatch) {
        EventBus.getDefault().post(new LeaveTeamEvent(isCancelMatch));
    }

    public static void postSyncTeam(TeamInfo teamInfo) {
        EventBus.getDefault().post(new SyncTeamRspEvent(teamInfo));
    }

    public static void postJoinTeamVoice(TeamInfo teamInfo, boolean micOn, int source) {
        EventBus.getDefault().post(new JoinTeamVoiceEvent(teamInfo, micOn, source));
    }

    public static void postTeamKickOut() {
        EventBus.getDefault().post(new TeamKickOutEvent());
    }

    public static void sendBroadcast(String intentText, Serializable message) {
        Intent intent = new Intent(intentText);
        intent.putExtra(intentText, message);
        BroadcastHelper.sendBroadcast(intent);
    }

    public static void postConversationUpdateEvent() {
        EventBus.getDefault().post(new ConversationUpdateEvent());
    }

    public static void postGroupSyncResEvent() {
        EventBus.getDefault().post(new GroupSyncRspEvent());
    }

    public static void postGroupNoteChangeEvent() {
        EventBus.getDefault().post(new GroupNoteChangeEvent());
    }

    public static void postPushChatMsgEvent(ArrayList<ChatMsg> msgArray) {
        EventBus.getDefault().post(new PushChatMsgEvent(msgArray));
    }

    public static void postChatMsgRspEvent(ChatMsg chatMsg, int code, String desc) {
        EventBus.getDefault().post(new ChatMsgRspEvent(chatMsg, code, desc));
    }

    public static void postGameStateRefreshEvent() {
        EventBus.getDefault().post(new GameStateRefresh());
    }

    public static void postNodeJsMsgChangeEvent() {
        EventBus.getDefault().post(new NodeJsGameMsgChangeEvent());
        BroadcastHelper.sendBroadcast(BroadcastConfig.GAME_PU_MESSAGE_TOTAL_NUM);//WespyTabsActivity去掉后这个事件可以去掉
    }

    public static void postNetWorkChangeEvent(boolean isConnected) {
        EventBus.getDefault().post(new NetWorkChange(isConnected));
    }

    public static void postRefreshTabReddot(boolean isShow, int index) {
        EventBus.getDefault().post(new ShowTabReddotEvent(isShow, index));
    }

    public static void postPushNewFriendEvent() {
        EventBus.getDefault().post(new PushNewFriend());
    }

    public static void postFinishEventFromCreateGroup() {
        EventBus.getDefault().post(new FinishSingleChatSettingEvent());
        EventBus.getDefault().post(new FinishSingleChatEvent());
    }

    public static void postUpdateSuspendFloat() {
        EventBus.getDefault().post(new UpdateSuspendFloatEvent());
    }

    public static void postLittleGameSuspend(boolean enter) {
        EventBus.getDefault().post(new LittleGameSuspendEvent(enter));
    }

    public static void postFinishMatchPrepareActivity() {
        EventBus.getDefault().post(new FinishMatchPrepareEvent());
    }

    public static void postFinishTeamView() {
        EventBus.getDefault().post(new FinishTeamViewEvent());
    }

    public static void postGroupTeamMatchUpdate() {
        EventBus.getDefault().post(new GroupTeamMatchUpdateEvent());
    }

    public static void postGroupRecallMsg(List<GroupChatMsg> groupChatMsgs) {
        EventBus.getDefault().post(new RecallGroupMsgEvent(groupChatMsgs));
    }

    public static void postGroupVideoBlocklMsg(List<GroupChatMsg> groupChatMsgs) {
        EventBus.getDefault().post(new GroupVideoBlockMsgEvent(groupChatMsgs));
    }

    public static void postProtoMsg(String key, GeneratedMessageLite<?, ?> message) {
        EventBus.getDefault().post(new ProtoMessageEvent(key, message));
    }

    public static void postRefreshSelfSuccess() {
        GlobalEventFlow.INSTANCE.postEvent(new RefreshSelfSuccessEvent());
        EventBus.getDefault().post(new RefreshSelfSuccessEvent());
    }

    public static void postSidError(String msg) {
        EventBus.getDefault().post(new SidErrorEvent(msg));
    }

    public static void postCircleRemindNumChange() {
        EventBus.getDefault().post(new CircleRemindNumChangeEvent());
    }

    public static void postChangeTabEvent(int tabNum, int subIndex) {
        EventBus.getDefault().postSticky(new ChangeTabEvent(tabNum, subIndex));
    }

    public static void postChangeTabEvent(int tabNum, int subIndex, int roomScene) {
        postChangeTabEvent(tabNum, subIndex, roomScene, null);
    }

    public static void postChangeTabEvent(int tabNum, int subIndex, int roomScene, @Nullable ChangeTabEvent.JumpDetail jumpDetail) {
        EventBus.getDefault().postSticky(new ChangeTabEvent(tabNum, subIndex, roomScene, jumpDetail));
    }

    public static void postUpdateSquareBannerEvent() {
        EventBus.getDefault().post(new UpdateSquareBannerEvent());
    }

    public static void postDelCommentEvent(int discoverId, int commentId) {
        EventBus.getDefault().post(new DiscoverCommentEvent(discoverId, commentId, DiscoverCommentEvent.CONTROL_TYPE_DEL));
    }

    public static void postAddCommentEvent(int discoverId, CCommentInfo comment) {
        EventBus.getDefault().post(new DiscoverCommentEvent(discoverId, comment.getId(), DiscoverCommentEvent.CONTROL_TYPE_ADD, comment));
    }

    public static void postNewDiscoverPostEvent(int discoverId) {
        EventBus.getDefault().post(new DiscoverPostEvent(discoverId, DiscoverPostEvent.CONTROL_TYPE_ADD));
    }

    public static void postDelDiscoverPostEvent(int discoverId) {
        EventBus.getDefault().post(new DiscoverPostEvent(discoverId, DiscoverPostEvent.CONTROL_TYPE_DEL));
    }

    public static void postDiscoverLikeEvent(int discover_id) {
        EventBus.getDefault().post(new DiscoverLikeEvent(discover_id, DiscoverLikeEvent.TYPE_LIKE));
    }

    public static void postDiscoverPrivacyEvent(int discoverId) {
        EventBus.getDefault().post(new DiscoverPrivacyEvent(discoverId));
    }

    public static void postDiscoverDislikeEvent(int discover_id) {
        EventBus.getDefault().post(new DiscoverLikeEvent(discover_id, DiscoverLikeEvent.TYPE_DISLIKE));
    }

    public static void postShowCareGiftAnimEvent(ChatPackets.chat_rs_msg_send message, WPMessage wpMessage) {
        ShowCareGiftAnimEvent event = new ShowCareGiftAnimEvent(message, wpMessage);
        EventBus.getDefault().post(event);
        ICareApiFactory.onSendGift(event);
    }

    public static void postRefreshUserCircle() {
        EventBus.getDefault().post(new RefreshUserCircleEvent());
    }

    public static void postDeepLinkEvent() {
        EventBus.getDefault().post(new DeepLinkEvent());
    }

    public static void postUserInfoUpdate(User userInfo) {
        EventBus.getDefault().post(new UpdateUserInfoEvent(userInfo));
    }

    public static void postSingleChatDeleteMsg(WPMessage msg) {
        EventBus.getDefault().post(new SingleChatDeleteMsgEvent(msg));
    }

    public static void postSingleChatResendMsg(WPMessage wpMessage) {
        EventBus.getDefault().post(new SingleChatResendMsgEvent(wpMessage));
    }

    public static void postVideoBlock(int type, List<String> contents) {
        EventBus.getDefault().post(new VideoEvent(type, contents));
    }

    public static void postUnreadMsgNumChange() {
        EventBus.getDefault().post(new UnreadMsgNumChangeEvent());
    }

    public static void postSingleChatRefreshList() {
        EventBus.getDefault().post(new SingleChatRefreshList());
    }

    public static void postGameNetWorkFail() {
        EventBus.getDefault().post(new GameNetWorkFailEvent());
    }

    public static void postGameNetWorkReConnected() {
        EventBus.getDefault().post(new GameNetWorkReConnectedEvent());
    }

    public static void postTcpBindUser() {
        EventBus.getDefault().post(new TcpBindUserEvent());
    }

    public static void postSendRedPacketFinish(int type, int code, String desc) {
        EventBus.getDefault().post(new FinishSendRedPacketEvent(type, code, desc));
    }

    public static void postDrawLikeEvent(int rid, int curUid, int totalNum) {
        EventBus.getDefault().post(new DrawLikeEvent(rid, totalNum, curUid));
    }

    public static void postDrawGameNeedSync(int rid) {
        EventBus.getDefault().post(new DrawGuessNeedSyncEvent(rid));
    }

    public static void postDrawGameSync(DrawGuessGameInfo gameInfo) {
        EventBus.getDefault().post(new DrawGuessSyncEvent(gameInfo));
    }

    public static void postUpdateRing(int propId) {
        EventBus.getDefault().post(new LoverHomeEvent(LoverHomeEvent.TYPE_UPDATE_RING, propId));
    }

    public static void postDeleteRing() {
        EventBus.getDefault().post(new LoverHomeEvent(LoverHomeEvent.TYPE_DELETE_RING));
    }

    public static void postUpdateNestBg() {
        EventBus.getDefault().post(new LoverHomeEvent(LoverHomeEvent.TYPE_UPDATE_NEST_BG));
    }

//    public static void postSpyGameSyncErrorEvent() {
//        EventBus.getDefault().post(new SpySyncGameErrorEvent());
//    }

    public static void postShowTaskReddot(boolean isTaskReddotShow) {
        GlobalEventFlow.INSTANCE.postEvent(new ShowTaskReddotEvent(isTaskReddotShow));
        EventBus.getDefault().post(new ShowTaskReddotEvent(isTaskReddotShow));
    }

    public static void postRefreshSelfTag(List<UserTagStatus> userTagStatusList) {
        EventBus.getDefault().post(new RefreshSelfTagEvent(userTagStatusList));
    }

    public static void postRefreshInvitation(InvitationInfo invitationInfo2) {
        EventBus.getDefault().post(new RefreshInvitationEvent(invitationInfo2));
    }

    public static void postSyncWeddingInfo(WeddingInfo weddingInfo, int actionType, int fireWorkNum) {
        EventBus.getDefault().post(new SyncWeddingInfoEvent(weddingInfo, actionType, fireWorkNum));
    }

    public static void postWeddingSyncError() {
        EventBus.getDefault().post(new SyncWeddingErrorEvent());
    }

    public static void poseMarryRemainEvent(MarryRemind2 remind) {
        EventBus.getDefault().post(new MarryRemainEvent(remind));
    }

    public static void postMarryEvent(int type) {
        EventBus.getDefault().post(new MarryRefreshEvent(type));
    }

    public static void postShowUserEndWeddingDialog(boolean isBride) {
        EventBus.getDefault().post(new UserEndWeddingEvent(isBride));
    }

    public static void postShowWeddingDialog(boolean isEndWedding, boolean isBrideGroomExit, String msg) {
        EventBus.getDefault().post(new WeddingDialogEvent(isEndWedding, isBrideGroomExit, msg));
    }

    public static void postAuctionListEvent(List<AuctionRoom> auctionRoom2List) {
        EventBus.getDefault().post(new AuctionListEvent(auctionRoom2List));
    }

    public static void postShowWeddingPastorDialog(boolean isIdo, int speakTime) {
        EventBus.getDefault().post(new ShowWeddingPastorDialogEvent(isIdo, speakTime));
    }

    public static void postShowWeddingFireWork(int fireWorkNum) {
        EventBus.getDefault().post(new ShowWeddingFireworkEvent(fireWorkNum));
    }

    public static void postRoomUpdateBottomView() {
        EventBus.getDefault().post(new UpdateRoomBottomViewEvent());
    }

    public static void postCpGameInfoEvent(CpGameInfo cpGameInfo) {
        EventBus.getDefault().post(new CpGameInfoEvent(cpGameInfo));
    }

    public static void postCpPushInfoEvent(CpPushInfo cpPushInfo) {
        EventBus.getDefault().post(new CpPushInfoEvent(cpPushInfo));
    }

    public static void postRedDotUpdateEvent() {
        GlobalEventFlow.INSTANCE.postEvent(new RedDotUpdateEvent());
        EventBus.getDefault().post(new RedDotUpdateEvent());
    }

    public static void postMusicWidgetStatusChangeEvent(int rid, boolean open) {
        EventBus.getDefault().post(new MusicWidgetChange(rid, open));
    }

    public static void postMusicListInfoChangeEvent(int rid) {
        EventBus.getDefault().post(new MusicPlayInfoChange(rid));
    }

    public static void postMusicCurInfoChangeEvent(int rid) {
        EventBus.getDefault().post(new MusicCurPlayChange(rid));
    }

    public static void postLrcChangeEvent() {
        EventBus.getDefault().post(new LrcStatusChangeEvent());
    }

    public static void postVoiceRoomGameState(boolean isStart) {
        EventBus.getDefault().post(new VoiceRoomGameStateEvent(isStart));
    }

    public static void poseAuctionPush(AuPushInfo auctionPush) {
        EventBus.getDefault().post(new AuPushEvent(auctionPush));
    }

    public static void postPlayingEffectChange(int playingId) {
        EventBus.getDefault().post(new PlayingEffectChange(playingId));
    }

    public static void postBroadcastGiftEvent(VoiceGiftInfo giftInfo) {
        EventBus.getDefault().post(new BroadcastVoiceGiftEvent(giftInfo));
    }

    public static void postChangeHomeTabEvent(boolean showReturnTop) {
        EventBus.getDefault().post(new ChangeHomeTabEvent(showReturnTop));
    }

    public static void postHomeReturnToTop() {
        EventBus.getDefault().post(new HomeReturnToTopEvent());
    }

    public static void postCouponUpdateEvent() {
        GlobalEventFlow.INSTANCE.postEvent(new CouponUpdateEvent());
        EventBus.getDefault().post(new CouponUpdateEvent());
    }

    public static void postFamilyMainRefreshEvent() {
        EventBus.getDefault().post(new FamilyMainRefreshEvent());
    }

    public static void postFamilyMemberRefreshEvent() {
        EventBus.getDefault().post(new FamilyMemberChangeEvent());
    }

    public static void postFamilyBenefitRedDotUpdateEvent(boolean hasBenefitRedDot) {
        EventBus.getDefault().post(new FamilyBenefitRedDotUpdateEvent(hasBenefitRedDot));
    }

    public static void postFamilyDissolveEvent() {
        EventBus.getDefault().post(new FamilyDissolveEvent());
    }

    public static void postFamilyKickedEvent() {
        EventBus.getDefault().post(new FamilyKickedEvent());
    }

    public static void postFamilySelfRoleChangeEvent(int newRole) {
        EventBus.getDefault().post(new FamilySelfRoleChangeEvent(newRole));
    }

    public static void postFamilyRoleChangeEvent(int familyId) {
        EventBus.getDefault().post(new FamilyRoleChangeEvent(familyId));
    }

    public static void postFamilyBroadcast(int gid, String broadContent) {
        EventBus.getDefault().post(new FamilyTipsContentEvent(gid, broadContent));
    }

    public static void postFamilyGroupBoxRefresh(List<FamilyAvailableBox> familyAvailableBoxList) {
        FamilyGroupBoxRefreshEvent familyGroupBoxRefreshEvent;
        if (familyAvailableBoxList == null) {
            familyGroupBoxRefreshEvent = new FamilyGroupBoxRefreshEvent();
        } else {
            familyGroupBoxRefreshEvent = new FamilyGroupBoxRefreshEvent(familyAvailableBoxList);
        }
        EventBus.getDefault().post(familyGroupBoxRefreshEvent);
    }

    public static void postFamilyShieldCountChange(int count) {
        EventBus.getDefault().post(new FamilyShieldCountEvent(count));
    }

    public static void postFamilyJoin() {
        EventBus.getDefault().post(new FamilyJoinEvent());
    }

    public static void postFamilyNoteRefreshEvent() {
        EventBus.getDefault().post(new FamilyNoteRefreshEvent());
    }

    public static void postCommonGiftEvent(GiftShowInfo showInfo) {
        EventBus.getDefault().post(new GiftCommonSendEvent(showInfo));
    }

    public static void postPropBuyEvent(int propId) {
        EventBus.getDefault().post(new PropBuyEvent(propId));
    }

    public static void postActivityContentViewChildCountChange(boolean add) {
        EventBus.getDefault().post(new ContentViewChildChangeEvent(add));
    }

    public static void postFirstChargeBuyEvent(boolean isShow, int type, ChargeUserDataInfo info) {
        EventBus.getDefault().post(new FirstChargeBuyEvent(isShow, type, info));
    }

    public static void postReportSuccessEvent(String source) {
        EventBus.getDefault().post(new ReportSuccessEvent(source));
    }

    public static void postCoMatchStatusUpdateEvent(AudioMatchProvider.Status status) {
        EventBus.getDefault().post(new CoMatchStatusUpdateEvent(status, 0, 0));
    }

    public static void postCoMatchStartEvent(int timeoutTime, int tid) {
        EventBus.getDefault().post(new CoMatchStatusUpdateEvent(AudioMatchProvider.Status.Matching, timeoutTime, tid));
    }

    public static void postCoSeatUpdateEvent() {
        EventBus.getDefault().post(new CoSeatUpdateEvent());
    }

    public static void postChatGameInviteAcceptEvent(String mid, boolean accept) {
        EventBus.getDefault().post(new ChatGameInviteAcceptEvent(mid, accept));
    }

    public static void postChatGameInviteDownloadedEvent(String mid, boolean success) {
        EventBus.getDefault().post(new ChatGameInviteDownloadedEvent(mid, success));
    }

    public static void postChatGameInviteEnterRoomEvent(int rid, int gameType, int code, String toast, String mid) {
        EventBus.getDefault().post(new ChatInviteRoomStartEvent(rid, gameType, code, toast, mid));
    }

    public static void postChatGameInviteEnterTeamEvent(int tid, int gameType, String mid) {
        EventBus.getDefault().post(new ChatInviteEnterTeam(tid, gameType, mid));
    }

    public static void postChatGameInviteCancelEvent() {
        EventBus.getDefault().post(new ChatInviteCancelEvent());
    }

    public static void postEvent(Object event) {
        EventBus.getDefault().post(event);
    }

    public static void postChatGameInviteErrorEvent(GameMatchPushPackets.GMInviteGameError err) {
        HLog.d("EventDispatcher", HLog.USR, "postChatGameInviteErrorEvent " + err);
        EventBus.getDefault().post(new ChatGameInviteErrorEvent(err.getMid(), err.getUid(), err.getCode(),
                err.getToast(), err.getGameType(), err.getGameMode(), err.getMode(), err.getBetLevel(), err.getGameCurrencyTypeValue()));
    }

    public static void postCheckPlayNext(String path) {
        EventBus.getDefault().post(new CheckPlayNextEvent(path));
    }

    public static void postAddMsg2PlayNextEvent(ChatMsg msg) {
        EventBus.getDefault().post(new AddMsg2PlayNextEvent(msg));
    }

    public static void postChangePlayNextStateEvent(boolean isPlay) {
        EventBus.getDefault().post(new ChangePlayNextStateEvent(isPlay));
    }

    public static void postStopPlayNextEvent() {
        EventBus.getDefault().post(new StopPlayNextEvent());
    }

    public static void postEmoticonFavoriteChangeEvent() {
        EventBus.getDefault().post(new EmoticonFavoriteChangeEvent());
    }

    public static void postShowIdentityEvent(boolean isMyself) {
        EventBus.getDefault().post(new ShowIdentityEvent(isMyself));
    }

    public static void postReqShowIdentityEvent() {
        EventBus.getDefault().post(new RequestShowIdentityEvent());
    }

    public static void postAudioMatchSuccessEvent(VoiceRoomInfo voiceRoomInfo) {
        EventBus.getDefault().post(new AudioMatchSuccessEvent(voiceRoomInfo));
    }

    public static void postAdminChangeEvent(List<Integer> adminList) {
        EventBus.getDefault().post(new AdminChangeEvent(adminList));
    }

    public static void postCareRankEvent(List<GuardItem> guardItemList) {
        EventBus.getDefault().post(new CareRankEvent(guardItemList));
    }

    public static void postAdvanceRoomTips() {
        EventBus.getDefault().post(new AdvanceRoomTipsEvent());
    }

    public static void postRefreshSeatQueue(VoiceRoomInfo roomInfo, List<Integer> uidList) {
        EventBus.getDefault().post(new VoiceSeatQueueRefreshEvent(roomInfo, uidList));
    }

    public static void postRefreshMicStatus(int rid) {
        EventBus.getDefault().post(new VoiceRefreshMicStatusEvent(rid));
    }


    public static void postRocketStatusChangedEvent(RocketStatus rocketStatus) {
        EventBus.getDefault().post(new RocketStatusChangedEvent(rocketStatus));
    }

    public static void postRocketDialogDismissEvent() {
        EventBus.getDefault().post(new RocketDialogDismissEvent());
    }

    public static void postRocketMeAwardGiftEvent(List<RocketAwardGiftInfo> infoList) {
        EventBus.getDefault().post(new RocketMeAwardGiftEvent(infoList));
    }

    public static void postMyFuelNumChangedEvent(int myFuelNum) {
        EventBus.getDefault().post(new BuyFuelEvent(myFuelNum));
    }

    public static void postUserInfoChangeEvent(int uid, String name, String headUrl) {
        EventBus.getDefault().post(new UserInfoChangeEvent(uid, name, headUrl));
    }

    public static void postNewFriendEvent(NewFriendRecord record) {
        EventBus.getDefault().post(new NewFriendEvent(record));
    }

    public static void postUpdateLangFavoritesEvent(boolean showLangFavorIv) {
        EventBus.getDefault().post(new LangFavoritesEvent(showLangFavorIv));
    }

    public static void postUpdateNationFlagEvent(NationFlagPacket.NationFlagGameInfo info) {
        EventBus.getDefault().post(new UpdateNationFlagInfoEvent(info));
    }

    public static void postYoutubeLoadUrlEvent(String url, boolean isVideoListView, String videoId) {
        EventBus.getDefault().post(new YoutubeLoadUrlEvent(url, isVideoListView, videoId));
    }

    public static void postVideoPlayStateChangeEvent(VideoPushPackets.PlayStateUpdatePush playStateUpdatePush) {
        EventBus.getDefault().post(new VideoPlayStateChangeEvent(playStateUpdatePush));
    }

    public static void postVideoPlaySyncEvent() {
        EventBus.getDefault().post(new VideoPlaySyncEvent());
    }

    public static void postPlayListUpdateEvent() {
        EventBus.getDefault().post(new VideoPlayListUpdateEvent());
    }

    public static void postSyncVolumeResEvent() {
        EventBus.getDefault().post(new SyncVolumeResEvent());
    }

    public static void postSyncMicResEvent() {
        EventBus.getDefault().post(new SyncMicResEvent());
    }

    public static void postSelectLabelEvent(int selectLabelId) {
        EventBus.getDefault().post(new SelectLabelEvent(selectLabelId));
    }

    public static void postYoutubeNetDialogCloseEvent() {
        EventBus.getDefault().post(new YoutubeNetDialogCloseEvent());
    }

    public static void postExitFullScreenEvent(boolean pauseVideo) {
        EventBus.getDefault().post(new YoutubeExitFullScreenEvent(pauseVideo));
    }

    public static void postYoutubePlayNewVideoEvent(VideoPlayInfo videoPlayInfo) {
        EventBus.getDefault().post(new YoutubePlayNewVideoEvent(videoPlayInfo));
    }

    public static void postYoutubeVideoEndEvent() {
        EventBus.getDefault().post(new YoutubeVideoEndEvent());
    }

    public static void postYoutubeUpdatePositionByPushEvent(int intervalMs) {
        EventBus.getDefault().post(new YoutubeUpdatePositionByPushEvent(intervalMs));
    }

    public static void postYoutubeUpdatePositionToServerEvent() {
        EventBus.getDefault().post(new YoutubeUploadPositionToServerEvent());
    }

    public static void postYoutubeRiskSwitchChanged(boolean hasRisk) {
        EventBus.getDefault().post(new YoutubeRiskSwitchChanged(hasRisk));
    }


    public static void postServerSendGiftEvent(WPMessage wpMessage) {
        EventBus.getDefault().post(new ServerSendGiftEvent(wpMessage));
    }

    public static void postDragonResultDeepLinkEvent(String gid) {
//        EventBus.getDefault().post(new DragonResultDeepLinkEvent(gid));
    }

    public static void poseVoiceSwitchOpen(boolean openVoice) {
        EventBus.getDefault().post(new VoiceStatus(openVoice));
    }
}
