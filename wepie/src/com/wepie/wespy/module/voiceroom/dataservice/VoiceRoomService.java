package com.wepie.wespy.module.voiceroom.dataservice;

import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Log;
import android.util.SparseArray;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.google.gson.reflect.TypeToken;
import com.huiwan.base.util.JsonUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.lru.SimpleLruHelper;
import com.huiwan.component.gift.send.liveuser.LiveUser;
import com.huiwan.configservice.model.voiceroom.VoiceMemberMsgExtInfo;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.platform.ThreadUtil;
import com.huiwan.store.PrefUserUtil;
import com.huiwan.user.LoginHelper;
import com.huiwan.voiceservice.VoiceManager;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wejoy.littlegame.LittleGame;
import com.wejoy.weplay.ex.GlobalLife;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.helper.bgmusic.BgMusicManager;
import com.wepie.wespy.helper.bgmusic.MusicSource;
import com.wepie.wespy.helper.bgmusic.VocMusicManager;
import com.wepie.wespy.model.entity.family.VoiceRoomFamilyInfo;
import com.wepie.wespy.model.entity.marry.WeddingInfo;
import com.wepie.wespy.model.entity.voiceroom.ActivityAnnounceInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg;
import com.wepie.wespy.model.event.RoomActivityStateUpdateEvent;
import com.wepie.wespy.model.event.RoomInfoUpdateEvent;
import com.wepie.wespy.model.event.RoomInviteMemberEvent;
import com.wepie.wespy.model.event.RoomLevelUpdateEvent;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.guards.GuardItem;
import com.wepie.wespy.module.voiceroom.msg.item.VoiceMsgUtil;
import com.wepie.wespy.module.voiceroom.music.VoiceMusicManager;
import com.wepie.wespy.module.voiceroom.roomgroup.RoomOwnerGroupData;
import com.wepie.wespy.net.http.api.FamilyApi;
import com.wepie.wespy.net.tcp.packet.RoomPushPackets;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by geeksammao on 23/10/2017.
 */

public class VoiceRoomService {
    private VoiceRoomInfo roomInfo;
    private ArrayList<VoiceRoomMsg> msgArray = new ArrayList<>();
    private boolean isInVoiceRoom;
    private int mutedRid;
    private int inviteSpeakInviteUid;
    private static VoiceRoomService voiceRoomService;
    private HashMap<Integer, Boolean> rcvRecommendMap = new HashMap<>();
    private long enterRoomTime;
    /**
     * 重新进入语音频道的时间
     */
    private long voiceChannelOpenTime = 0;
    private boolean hasLeadedFocus = false;
    private final HashMap<Integer, Long> memberEnterMap = new HashMap<>();

    private final BigWinnerViewModel bigWinnerViewModel = new BigWinnerViewModel();
    private final RpDelayViewModel rpDelayViewModel = new RpDelayViewModel();
    private final GalaWebViewModel galaWebViewModel = new GalaWebViewModel();
    private final VoiceGameModel voiceGameModel = new VoiceGameModel();
    private final MutableLiveData<VoiceRoomInfo> liveData = new MutableLiveData<>(new VoiceRoomInfo());
    private final MutableLiveData<List<LiveUser>> giftLiveUsers = new MutableLiveData<>(Collections.emptyList());
    // 监听座位上用户改变
    private final MutableLiveData<List<VoiceRoomInfo.SeatInfo>> seatsInfoList = new MutableLiveData<>(new ArrayList<>());

    private static final String TAG = "VoiceRoomService";
    private final SimpleLruHelper<Integer, Integer> recentRidLru = new SimpleLruHelper<>(10, 0.75F);
    private final MutableLiveData<SimpleLruHelper<Integer, Integer>> recentRidLruLiveData = new MutableLiveData<>(recentRidLru);

    public SparseArray<VoiceRoomInfo.RoomMemberInfo> roomMemberInfoCache = new SparseArray<>();

    private VoiceRoomService() {
        recentRidLru.setMaxSize(10);
        String ridsStr = PrefUserUtil.getInstance().getString(PrefUserUtil.RECENT_RID_LIST, "");
        if (!TextUtils.isEmpty(ridsStr)) {
            Set<Integer> ridSet = JsonUtil.fromJson(ridsStr, new TypeToken<Set<Integer>>() {
            }.getType());
            for (Integer i : ridSet) {
                recentRidLru.put(i, i);
            }
        }
    }

    @NonNull
    public VoiceRoomInfo getRoomInfo() {
        return getRoomInfo(-1);
    }

    public boolean isVoiceRoomRidValid() {
        return getRoomInfo().rid > 0;
    }

    private int inviteSpeakSeatNum;

    public int getInviteSpeakReplyTimeLeft() {
        return inviteSpeakReplyTimeLeft;
    }

    private int inviteSpeakReplyTimeLeft;

    public boolean isMuteRoom(int rid) {
        return mutedRid == rid;
    }

    public boolean isMuteRoom() {
        return roomInfo != null && isMuteRoom(roomInfo.rid);
    }

    public static VoiceRoomService getInstance() {
        if (voiceRoomService == null) {
            voiceRoomService = new VoiceRoomService();
        }
        return voiceRoomService;
    }

    public LiveData<List<LiveUser>> getGiftLiveUsers() {
        return giftLiveUsers;
    }

    public void bigWinnerSwitch(boolean open) {
        getRoomInfo().bigWinnerOpen = open;
        getBigWinnerViewModel().postOpen(open);
    }

    @NonNull
    public BigWinnerViewModel getBigWinnerViewModel() {
        return bigWinnerViewModel;
    }

    public RpDelayViewModel getRpDelayViewModel() {
        return rpDelayViewModel;
    }

    public GalaWebViewModel getGalaWebViewModel() {
        return galaWebViewModel;
    }


    public LiveData<VoiceRoomInfo> getLiveData() {
        return liveData;
    }

    public int getGameType() {
        return getRoomInfo().game_type;
    }

    public MutableLiveData<List<VoiceRoomInfo.SeatInfo>> getSeatsInfoList() {
        return seatsInfoList;
    }

    public void setLiveData(VoiceRoomInfo room) {
        liveData.setValue(room);
    }

    public static void clear() {
        voiceRoomService = null;
    }

    public void setIsInVoiceRoom(boolean isInVoiceRoom) {
        this.isInVoiceRoom = isInVoiceRoom;
    }

    public boolean isInVoiceRoom() {
        return isInVoiceRoom;
    }

    public void setMuteRoom(int rid) {
        mutedRid = rid;
    }

    public void setInviteSpeakReplyTimeLeft(int timeLeft) {
        this.inviteSpeakReplyTimeLeft = timeLeft;
    }

    public void setInviteSpeakSeatNum(int inviteSpeakSeatNum) {
        this.inviteSpeakSeatNum = inviteSpeakSeatNum;
    }

    public int getInviteSpeakSeatNum() {
        return inviteSpeakSeatNum;
    }

    public int getInviteSpeakInviteUid() {
        return inviteSpeakInviteUid;
    }

    public void setInviteSpeakInviteUid(int inviteSpeakInviteUid) {
        this.inviteSpeakInviteUid = inviteSpeakInviteUid;
    }

    public HashMap<Integer, Boolean> getRcvRecommendMap() {
        return rcvRecommendMap;
    }

    public void setRcvRecommendMap(HashMap<Integer, Boolean> rcvRecommendMap) {
        this.rcvRecommendMap = rcvRecommendMap;
    }

    public void addRoomInfo(VoiceRoomInfo room) {
        if (roomInfo == null || roomInfo.rid != room.rid) {
            this.mutedRid = 0;
            msgArray.clear();
            //第一次进房，添加一个房间公告
            if (!TextUtils.isEmpty(room.note)) {
                VoiceRoomMsg roomMsg = new VoiceRoomMsg();
                roomMsg.setRid(room.rid);
                roomMsg.setSend_uid(1);
                roomMsg.setMid(RoomMsgStructUtil.generateRoomMid(1, room.rid, SystemClock.uptimeMillis()));
                roomMsg.setMsgContent(room.note);
                roomMsg.setMediaType(VoiceRoomMsg.MEDIA_TYPE_TEXT);
                roomMsg.setSubType(VoiceRoomMsg.SUBTYPE_SHOW_RNOTE);
                roomMsg.setTime(System.currentTimeMillis());
                msgArray.add(roomMsg);
            }
            updateRoomFamilyInfo(room.familyId);
            enterRoomTime = System.currentTimeMillis();
            voiceChannelOpenTime = enterRoomTime;
            hasLeadedFocus = false;
            bigWinnerViewModel.reset();
        }

        if (roomInfo != null && roomInfo.rid == room.rid) {
            room.forceUpdateSeatUserInfo = roomInfo.forceUpdateSeatUserInfo;
            room.weddingInfo = roomInfo.weddingInfo;
            room.musicPaused = roomInfo.musicPaused;
            room.drawGameInfo = roomInfo.drawGameInfo;
            room.isHighQuality = roomInfo.isHighQuality;
            room.lastQualityChangeTime = roomInfo.lastQualityChangeTime;
            room.familyRoleInfo = roomInfo.familyRoleInfo;
            room.blackList = roomInfo.blackList;
            room.guardItemList = roomInfo.guardItemList;
            room.isFollowed = roomInfo.isFollowed;
            room.uidList = roomInfo.uidList;
            room.roomMemberInfo = roomInfo.roomMemberInfo;
            room.closeActivityDialog = roomInfo.closeActivityDialog;
            room.roomListId = roomInfo.roomListId;
            room.familyLevel = roomInfo.familyLevel;
            room.closeVoiceChannel = roomInfo.closeVoiceChannel;
            if (room.isOnline && !roomInfo.isOnline) {
                updateVoiceChannelOpenTime();
            }
        } else {
            roomMemberInfoCache.clear();
            voiceGameModel.clear();
        }

        this.roomInfo = room;
        saveLocalRid(roomInfo.rid);
        check2playBgMusic(room.bgMusicId, room.musicPaused, room.isWeddingRoom() && room.weddingInfo.isPastorState());
        VoiceMusicManager.get().bindRid(roomInfo.rid, roomInfo.inMusic);
        if (!isSupportVoc(roomInfo)) {
            VocMusicManager.getInstance().stop();
        }
        liveData.setValue(room);
        updateSeatsLiveDataIfNeeded(roomInfo.seatInfos, seatsInfoList.getValue());

        rpDelayViewModel.updateRedPacket(room.redPacket);
        voiceGameModel.update(room);
        updateLiveUser();
        HLog.d(TAG, HLog.USR, "addRoomInfo, room=" + room);
    }

    private void updateLiveUser() {
        List<LiveUser> liveUserList = new ArrayList<>();

        for (VoiceRoomInfo.SeatInfo seatInfo : roomInfo.seatInfos) {
            if (seatInfo.isUserSeat()) {
                String desc = VoiceRoomLiveGiftDescHelper.getSeatDesc(roomInfo, seatInfo);
                int index = VoiceRoomLiveGiftDescHelper.getIndex(roomInfo, seatInfo);
                LiveUser liveUser = new LiveUser(index, seatInfo.uid, desc);
                liveUserList.add(liveUser);
            }
        }
        Collections.sort(liveUserList, (o1, o2) -> o1.getIndex() - o2.getIndex());
        giftLiveUsers.setValue(liveUserList);
    }

    public void removeRoom(int rid) {
        if (roomInfo != null) {
            BgMusicManager.getInstance().stop(MusicSource.SOURCE_VOICE_ROOM);
            VoiceMusicManager.get().clearAndStop();
        }
        roomInfo = null;
        this.mutedRid = 0;
        msgArray.clear();
        saveLocalRid(-1);
        RoomOwnerGroupData.INSTANCE.getBindGids().setValue(new ArrayList<>());
        LittleGame.clearVoiceGameInfo(rid);
        HLog.d(TAG, HLog.USR, "removeRoom! rid=" + rid + "\n" + Log.getStackTraceString(new Throwable()));
    }

    public int getCurrentBg() {
        if (roomInfo != null) {
            return roomInfo.themeId;
        }
        return -1;
    }

    public VoiceGameModel getVoiceGameViewModel() {
        return voiceGameModel;
    }

    public int getLocalRid() {
        return PrefUserUtil.getInstance().getInt(PrefUserUtil.LOCAL_ROOM, -1);
    }

    public void saveLocalRid(int rid) {
        PrefUserUtil.getInstance().setInt(PrefUserUtil.LOCAL_ROOM, rid);
    }

    public void saveLocalPushStream(String stream) {
        PrefUserUtil.getInstance().setString(PrefUserUtil.LOCAL_ROOM_STREAM, stream);
    }

    public String getLocalPushStream() {
        return PrefUserUtil.getInstance().getString(PrefUserUtil.LOCAL_ROOM_STREAM, "");
    }

    @NonNull
    public VoiceRoomInfo getRoomInfo(int rid) {
        if (roomInfo == null) {
            roomInfo = new VoiceRoomInfo();
            this.mutedRid = 0;
            HLog.d(TAG, HLog.USR, "getRoomInfo, roomInfo is null!\n" + Log.getStackTraceString(new Throwable()));
        }
        return roomInfo;
    }

    public void addMsg(int rid, VoiceRoomMsg roomMsg) {
        HLog.d(TAG, HLog.USR, "roomInfo=" + roomInfo + ", rid=" + rid + ", roomMsg=" + roomMsg);
        if (roomInfo != null && roomInfo.rid != rid) {//过滤消息
            return;
        }
        if (roomMsg.getSubType() == VoiceRoomMsg.SUBTYPE_EMOTION) {
            return;
        }
        if (msgArray.size() >= 8192) {
            msgArray = new ArrayList<>(msgArray.subList(4096, msgArray.size()));
        }
        msgArray.add(roomMsg);
    }

    public boolean isMemberEnterMsg(VoiceRoomMsg roomMsg) {
        boolean flag = false;
        int subType = roomMsg.getSubType();
        if (subType == VoiceRoomMsg.SUB_MEMBER_MSG_CARD) {
            VoiceMemberMsgExtInfo info = VoiceMsgUtil.getMemberMsgInfo(roomMsg.getExt());
            if (info.getScene() == VoiceRoomMsg.SCENE_MEMBER_ENTER_ROOM) {
                flag = true;
            }
        }
        return flag;
    }

    // 30秒显示一次
    public boolean handlerMemberEnterMsg(VoiceRoomMsg roomMsg) {
        boolean isNeedShow = true;
        VoiceMemberMsgExtInfo info = VoiceMsgUtil.getMemberMsgInfo(roomMsg.getExt());
        int enterUid = info.getMentionedUid();
        // 自己的进房消息一直展示
        if (enterUid == LoginHelper.getLoginUid()) {
            return true;
        }
        Long curTime = TimeUtil.getServerTime();
        if (!memberEnterMap.containsKey(enterUid)) {
            memberEnterMap.put(enterUid, curTime);
        } else {
            Long lastTime = memberEnterMap.get(enterUid);
            // 如果上次显示时间获取异常，或者距离上次显示时间超过了30秒，需要展示，更新展示时间，否则不展示
            if (lastTime == null || curTime - lastTime > 30000) {
                memberEnterMap.put(enterUid, curTime);
            } else {
                isNeedShow = false;
            }
        }
        return isNeedShow;
    }

    public boolean isOwner(int rid) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        return roomInfo != null && roomInfo.owner == LoginHelper.getLoginUid();
    }

    public ArrayList<VoiceRoomMsg> getMsgArray() {
        return msgArray;
    }

    public void modRoomName(int rid, String roomName) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.name = roomName;
    }

    public void modRoomNote(int rid, String note) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.note = note;
    }

    public void updateTheme(int rid, int themeId, String bgUrl) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.themeId = themeId;
        roomInfo.roomBgUrl = bgUrl;
    }

    public void updateLabel(int rid, int labelType) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.labelType = labelType;
    }

    public void updateHeadImage(int rid, String img) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.headImage = img;
    }

    public void updateGuardList(int rid, List<GuardItem> list) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.guardItemList.clear();
        roomInfo.guardItemList.addAll(list);
    }

    public VoiceRoomInfo.PkInfo getPkInfo(int rid) {
        return getRoomInfo(rid).pkInfo;
    }

    public void modPromise(int rid, String promise) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
    }

    public void modPublicChat(int rid, boolean is_public) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.public_chat = is_public ? VoiceRoomInfo.PUBLIC_CHAT_ON : VoiceRoomInfo.PUBLIC_CHAT_OFF;
    }

    public void modPwd(int rid, String pwd) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.passwd = pwd;
    }

    public void kickUsers(int rid, List<Integer> kicked_users) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.kickUsers(kicked_users);
    }

    public void userExit(int rid, int exit_user, int new_owner) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.userExit(exit_user, new_owner);
    }

    public void sit(int rid, int sit_uid, int seat_num) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.sit(sit_uid, seat_num);
    }

    public void seal(int rid, int seat_num, boolean sealed) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.seal(seat_num, sealed);
    }

    public void unseat(int rid, int seat_num) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.unseat(seat_num);
    }

    public void forceUnseat(int rid, int forced_uid) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.forceUnseat(forced_uid);
    }

    public void forbidSpeak(int rid, int seat_num) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.forbidSpeak(seat_num);
    }

    public int getSeatUid(int rid, int seat_num) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        VoiceRoomInfo.SeatInfo seatInfo = roomInfo.getSeatInfoByNum(seat_num);
        return seatInfo.uid;
    }

    public VoiceRoomInfo.SeatInfo getSeatInfo(int rid, int uid) {
        return getRoomInfo(rid).getSeatInfoByUid(uid);
    }

    public void exitLocal() {
        bigWinnerViewModel.postOpen(false);
        if (roomInfo == null) {
            FLog.e(new Throwable("exitLocal, roomInfo is null"));
            return;
        }
        VoiceRoomPacketSender.exitRoom(roomInfo.rid, new LifeSeqCallback(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                VoiceManager.getInstance().resetPushStreamUrl();
                VoiceRoomService.getInstance().saveLocalPushStream("");
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private void check2playBgMusic(int id, boolean musicPaused, boolean weddingPastorState) {
        if (weddingPastorState) {
            BgMusicManager.getInstance().pause(MusicSource.SOURCE_VOICE_ROOM);
        } else if (!musicPaused) {
            BgMusicManager.getInstance().play(MusicSource.SOURCE_VOICE_ROOM, id, true);
        }
    }

    public void setWeddingInfo(WeddingInfo weddingInfo) {
        if (roomInfo == null) return;
        roomInfo.setWeddingInfo(weddingInfo);
        updateLiveUser();
        check2playBgMusic(roomInfo.bgMusicId, roomInfo.musicPaused, weddingInfo.isPastorState());
    }

    private void updateRoomFamilyInfo(int familyId) {
        if (familyId <= 0) return;
        FamilyApi.getVoiceRoomFamilyInfo(familyId, new LifeDataCallback<VoiceRoomFamilyInfo>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<VoiceRoomFamilyInfo> result) {
                if (roomInfo != null) {
                    roomInfo.familyLevel = result.data.getFamilyLevel();
                    roomInfo.familyRoleInfo.clear();
                    for (Map.Entry<Integer, Integer> entry : result.data.positionMap.entrySet()) {
                        roomInfo.familyRoleInfo.put(entry.getKey(), entry.getValue());
                    }
                }
                EventDispatcher.postRoomInfoUpdateEvent(RoomInfoUpdateEvent.F_FAMILY_INFO);
            }

            @Override
            public void onFail(int i, String s) {
                ToastUtil.show(s);
            }
        });
    }

    public void updateRoomFamilyInfoIfIn(int familyId) {
        if (roomInfo == null || familyId != roomInfo.familyId) return;
        FamilyApi.getVoiceRoomFamilyInfo(roomInfo.familyId, new LifeDataCallback<VoiceRoomFamilyInfo>(GlobalLife.INSTANCE) {
            @Override
            public void onSuccess(Result<VoiceRoomFamilyInfo> result) {
                if (roomInfo != null) {
                    roomInfo.familyLevel = result.data.getFamilyLevel();
                    roomInfo.familyRoleInfo.clear();
                    for (Map.Entry<Integer, Integer> entry : result.data.positionMap.entrySet()) {
                        roomInfo.familyRoleInfo.put(entry.getKey(), entry.getValue());
                    }
                }
                EventDispatcher.postRoomInfoUpdateEvent(RoomInfoUpdateEvent.F_FAMILY_INFO);
            }

            @Override
            public void onFail(int i, String s) {
                ToastUtil.show(s);
            }
        });
    }

    public void updateMic(int rid, boolean isOpen) {
        VoiceRoomInfo roomInfo = getRoomInfo(rid);
        roomInfo.inSeatQueueOpen = isOpen;
    }

    public int getRid() {
        if (roomInfo != null) {
            return roomInfo.rid;
        }
        return 0;
    }

    private static boolean isSupportVoc(VoiceRoomInfo roomInfo) {
        if (roomInfo == null) return true;//空则没有语音房
        if (roomInfo.game_type <= 0) return true;//空数据，异常数据
        return roomInfo.isSupportVoiceRoom() && !roomInfo.isWeddingRoom();
    }

    public long getEnterRoomTime() {
        return enterRoomTime;
    }

    public boolean isHasLeadedFocus() {
        return hasLeadedFocus;
    }

    public void setHasLeadedFocus(boolean hasLeadedFocus) {
        this.hasLeadedFocus = hasLeadedFocus;
    }

    public void updateUidList(List<Integer> uidList) {
        if (roomInfo == null) {
            return;
        }
        Set<Integer> set = new HashSet<>(roomInfo.uidList);
        for (Integer i : uidList) {
            if (set.add(i)) {
                roomInfo.uidList.add(i);
            }
        }
    }

    public void updateUserNumber(int userNum) {
        roomInfo.onlineNum = userNum;
    }

    /**
     * 语音房升级
     */
    public void updateRoomLevel(RoomPushPackets.RoomLevelUpgrade roomLevelUpgrade) {
        roomInfo.advanceRoomLevel = roomLevelUpgrade.getLevel();
        EventBus.getDefault().post(new RoomLevelUpdateEvent(roomInfo.advanceRoomLevel));
    }

    public LiveData<SimpleLruHelper<Integer, Integer>> getRecentRidLruLiveData() {
        return recentRidLruLiveData;
    }

    public void notifyEnterRoom(VoiceRoomInfo info) {
        int rid = info.rid;
        if (info.owner == LoginHelper.getLoginUid() || rid <= 0) {
            return;
        }
        recentRidLru.lruPut(rid, rid);
        recentRidLruLiveData.postValue(recentRidLru);
        Set<Integer> set = recentRidLru.keySet();
        PrefUserUtil.getInstance().setString(PrefUserUtil.RECENT_RID_LIST, JsonUtil.toJson(set));
    }

    /**
     * 语音房活动状态变更
     */
    public void updateActivityState(RoomPushPackets.ActivityStateChangedPush activityStateChangedPush) {
        roomInfo.activityAnnounceInfo = ActivityAnnounceInfo.parse(activityStateChangedPush);
        EventBus.getDefault().post(new RoomActivityStateUpdateEvent(roomInfo.activityAnnounceInfo, roomInfo.rid));
    }

    public void handlerInviteMember() {
        EventBus.getDefault().post(new RoomInviteMemberEvent());
    }

    public void updateSelfRoomInfo(TmpRoomPackets.RoomMemberInfo roomMemberInfo) {
        VoiceRoomInfo.RoomMemberInfo info = VoiceRoomInfo.RoomMemberInfo.parse(roomMemberInfo);
        if (info != null && info.equals(roomInfo.roomMemberInfo)) {
            return;
        }
        roomInfo.roomMemberInfo = info;
        ThreadUtil.runOnUiThread(() -> liveData.setValue(roomInfo));
        EventDispatcher.postRoomInfoUpdateEvent(RoomInfoUpdateEvent.F_ALL);
    }

    /**
     * 加入房间
     */
    public void joinMemberGroup(RoomPushPackets.JoinMemberGroup joinMemberGroup) {
        boolean isNotify = false;
        int uid = joinMemberGroup.getUid();
        VoiceRoomInfo.RoomMemberInfo info = VoiceRoomInfo.RoomMemberInfo.parse(joinMemberGroup.getMemberInfo());
        if (joinMemberGroup.getUid() == LoginHelper.getLoginUid()) {
            isNotify = true;
            roomInfo.roomMemberInfo = info;
            roomInfo.isFollowed = true;
        }
        VoiceRoomInfo.SeatInfo seatInfo = roomInfo.getSeatInfoByUid(uid);
        if (seatInfo != null) {
            seatInfo.room_member_info = info;
            isNotify = true;
        }
        roomMemberInfoCache.append(uid, info);
        if (roomInfo.memberCount != joinMemberGroup.getMemberCount()) {
            roomInfo.memberCount = joinMemberGroup.getMemberCount();
            isNotify = true;
        }

        if (isNotify) {
            ThreadUtil.runOnUiThread(() -> liveData.setValue(roomInfo));
            EventDispatcher.postRoomInfoUpdateEvent(RoomInfoUpdateEvent.F_ALL);
        }
    }

    /**
     * 退出房间
     */
    public void exitMemberGroup(RoomPushPackets.ExitMemberGroup exitMemberGroup) {
        List<Integer> removeUidList = exitMemberGroup.getRemoveUidListList();
        boolean isNotify = false;
        for (int uid : removeUidList) {
            if (uid == LoginHelper.getLoginUid()) {
                roomInfo.roomMemberInfo = null;
                isNotify = true;
            } else {
                VoiceRoomInfo.SeatInfo seatInfo = roomInfo.getSeatInfoByUid(uid);
                if (seatInfo != null) {
                    seatInfo.room_member_info = null;
                    isNotify = true;
                }
            }
            roomMemberInfoCache.remove(uid);
        }
        if (roomInfo.memberCount != exitMemberGroup.getMemberCount()) {
            roomInfo.memberCount = exitMemberGroup.getMemberCount();
            isNotify = true;
        }

        if (isNotify) {
            ThreadUtil.runOnUiThread(() -> liveData.setValue(roomInfo));
            EventDispatcher.postRoomInfoUpdateEvent(RoomInfoUpdateEvent.F_ALL);
        }
    }

    /**
     * 房间成员升级
     */
    public void updateRoomMemberLevelUpdate(RoomPushPackets.RoomMemberLevelUp roomMemberLevelUp) {
        int uid = roomMemberLevelUp.getUid();
        int level = roomMemberLevelUp.getLevel();
        refreshRoomMemberLevel(uid, level);
    }

    /**
     * 房间成员解冻
     */
    public void roomMemberUnFrozen(RoomPushPackets.RoomMemberUnFrozen roomMemberUnfrozen) {
        int uid = roomMemberUnfrozen.getUid();
        int level = roomMemberUnfrozen.getLevel();
        refreshRoomMemberLevel(uid, level);
    }

    public void refreshRoomMemberLevel(int uid, int level) {
        boolean isNotify = false;
        if (uid == LoginHelper.getLoginUid()) {
            VoiceRoomInfo.RoomMemberInfo roomMemberInfo = roomInfo.roomMemberInfo;
            if (roomMemberInfo == null) {
                roomMemberInfo = new VoiceRoomInfo.RoomMemberInfo();
                roomInfo.roomMemberInfo = roomMemberInfo;
            }
            roomMemberInfo.level = level;
            roomMemberInfo.isFreeze = false;
            isNotify = true;
        }
        VoiceRoomInfo.SeatInfo seatInfo = roomInfo.getSeatInfoByUid(uid);
        if (seatInfo != null) {
            VoiceRoomInfo.RoomMemberInfo roomMemberInfo = seatInfo.room_member_info;
            if (roomMemberInfo == null) {
                roomMemberInfo = new VoiceRoomInfo.RoomMemberInfo();
                seatInfo.room_member_info = roomMemberInfo;
            }
            roomMemberInfo.level = level;
            roomMemberInfo.isFreeze = false;
            isNotify = true;
        }
        VoiceRoomInfo.RoomMemberInfo roomMemberInfo = roomMemberInfoCache.get(uid);
        if (roomMemberInfo == null) {
            roomMemberInfo = new VoiceRoomInfo.RoomMemberInfo();
            roomMemberInfoCache.append(uid, roomMemberInfo);
        }
        roomMemberInfo.level = level;
        roomMemberInfo.isFreeze = false;

        if (isNotify) {
            ThreadUtil.runOnUiThread(() -> liveData.setValue(roomInfo));
            EventDispatcher.postRoomInfoUpdateEvent(RoomInfoUpdateEvent.F_ALL);
        }
    }

    public boolean isMemberFromOnlineList(int uid) {
        if (roomInfo == null || !roomInfo.uidList.contains(uid)) {
            return false;
        }
        VoiceRoomInfo.RoomMemberInfo info = roomMemberInfoCache.get(uid);
        return info != null && info.level > 0;
    }

    public void addMemberCache(VoiceRoomInfo.RoomMemberInfo info) {
        int index = roomMemberInfoCache.indexOfKey(info.memberUid);
        if (index < 0) {
            roomMemberInfoCache.put(info.memberUid, info);
        } else {
            VoiceRoomInfo.RoomMemberInfo oldInfo = roomMemberInfoCache.valueAt(index);
            oldInfo.update(info);
        }
    }

    public VoiceRoomInfo.RoomMemberInfo addMemberCache(TmpRoomPackets.RoomMemberInfo info) {
        VoiceRoomInfo.RoomMemberInfo roomMemberInfo = VoiceRoomInfo.RoomMemberInfo.parse(info);
        if (roomMemberInfo == null) {
            return new VoiceRoomInfo.RoomMemberInfo();
        }
        addMemberCache(roomMemberInfo);
        return roomMemberInfo;
    }

    public void removeMemberCache(int uid) {
        roomMemberInfoCache.delete(uid);
    }

    @Nullable
    public VoiceRoomInfo.RoomMemberInfo getMemberInfo(int uid) {
        return roomMemberInfoCache.get(uid);
    }

    private void updateSeatsLiveDataIfNeeded(List<VoiceRoomInfo.SeatInfo> list, List<VoiceRoomInfo.SeatInfo> seatInfoList) {
        if (!isSeatListEqual(list, seatInfoList)) {
            seatsInfoList.setValue(list);
        }
    }

    private boolean isSeatListEqual(List<VoiceRoomInfo.SeatInfo> list, List<VoiceRoomInfo.SeatInfo> seatInfoList) {
        if (list == seatInfoList) {
            return true;
        }

        if (list == null || seatInfoList == null) {
            return false;
        }

        if (list.size() != seatInfoList.size()) {
            return false;
        }

        for (int i = 0; i < list.size(); i++) {
            if (!list.get(i).equals(seatInfoList.get(i))) {
                return false;
            }
        }

        return true;
    }

    public boolean isInVoiceGameRoom() {
        return roomInfo != null && roomInfo.isVoiceGameRoom();
    }

    public long getVoiceChannelOpenTime() {
        return voiceChannelOpenTime;
    }

    public void updateVoiceChannelOpenTime() {
        voiceChannelOpenTime = TimeUtil.getElapsedServerTime();
    }
}
