package com.wepie.wespy.module.voiceroom.dataservice;

import android.os.Handler;
import android.os.Looper;

import com.huiwan.base.str.ResUtil;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.callback.LifeSeqCallbackProxy;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

/**
 * Created by geeksammao on 24/10/2017.
 */

public class RoomMsgSendUtil {

    private static Handler handler = new Handler(Looper.getMainLooper());

    public static void sendRoomMsg(VoiceRoomMsg roomMsg) {
        int media_type = roomMsg.getMediaType();
        switch (media_type) {
            case VoiceRoomMsg.MEDIA_TYPE_UNDEFINED:
            case VoiceRoomMsg.MEDIA_TYPE_TEXT:
            case VoiceRoomMsg.MEDIA_TYPE_PHOTO:
            case VoiceRoomMsg.MEDIA_TYPE_EMOJI:
                sendRequest(roomMsg);
                break;
        }
    }

    public static void sendRoomMsg(VoiceRoomMsg roomMsg, RoomMsgSendCallback callback) {
        int media_type = roomMsg.getMediaType();
        switch (media_type) {
            case VoiceRoomMsg.MEDIA_TYPE_UNDEFINED:
            case VoiceRoomMsg.MEDIA_TYPE_TEXT:
                sendRequest(roomMsg, callback);
                break;
        }
    }

    private static void sendRequest(final VoiceRoomMsg roomMsg) {
        final Runnable runnable = new Runnable() {
            @Override
            public void run() {
                if(roomMsg.getStatus() == VoiceRoomMsg.STATUS_SENDING) onSendFail(roomMsg, ResUtil.getStr(R.string.common_send_out_time));
            }
        };

        LifeSeqCallback callback = new LifeSeqCallbackProxy() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                handler.removeCallbacks(runnable);
                onSendSuccess(roomMsg);
            }

            @Override
            public void onFail(RspHeadInfo headInfo) {
                onSendFail(roomMsg, headInfo.desc, headInfo.code);
            }
        };

        VoiceRoomPacketSender.sendRoomMsg(roomMsg, callback);
        handler.postDelayed(runnable, 10 * 1000);
    }

    private static void sendRequest(final VoiceRoomMsg roomMsg, final RoomMsgSendCallback callback) {
        final Runnable runnable = new Runnable() {
            @Override
            public void run() {
                if(roomMsg.getStatus() == VoiceRoomMsg.STATUS_SENDING) onSendFail(roomMsg, ResUtil.getStr(R.string.common_send_out_time));
            }
        };

        LifeSeqCallback seqCallback = new LifeSeqCallbackProxy() {
            @Override
            public void onSuccess(RspHeadInfo head) {
                handler.removeCallbacks(runnable);
                onSendSuccess(roomMsg);
                callback.onSendSuccess();
            }

            @Override
            public void onFail(RspHeadInfo headInfo) {
                onSendFail(roomMsg, headInfo.desc, headInfo.code);
            }
        };

        VoiceRoomPacketSender.sendRoomMsg(roomMsg, seqCallback);
        handler.postDelayed(runnable, 10 * 1000);
    }

    private static void onSendFail(VoiceRoomMsg chatMsg, String desc, int code) {
        chatMsg.setStatus(VoiceRoomMsg.STATUS_FAIL);
        EventDispatcher.postRoomMsgRspEvent(chatMsg, code, desc);
    }

    private static void onSendSuccess(VoiceRoomMsg chatMsg) {
        chatMsg.setStatus(VoiceRoomMsg.STATUS_OK);
        chatMsg.setTime(System.currentTimeMillis());
        EventDispatcher.postRoomMsgRspEvent(chatMsg, 200, "");
    }

    private static void onSendFail(VoiceRoomMsg chatMsg, String msg) {
        chatMsg.setStatus(VoiceRoomMsg.STATUS_FAIL);
        EventDispatcher.postRoomMsgRspEvent(chatMsg, 500, msg);
    }

    public interface RoomMsgSendCallback {
        void onSendSuccess();
    }
}