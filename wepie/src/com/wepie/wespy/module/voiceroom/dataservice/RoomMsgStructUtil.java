package com.wepie.wespy.module.voiceroom.dataservice;

import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsg;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomMsgSendInfo;
import com.huiwan.user.LoginHelper;

import org.json.JSONObject;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by geeksammao on 23/10/2017.
 */

public class RoomMsgStructUtil {

    public static VoiceRoomMsg structMsg(VoiceRoomMsgSendInfo sendInfo) {
        int media_type = sendInfo.getMediaType();
        String content = sendInfo.getContent();
        return structMsg(sendInfo, media_type, content);
    }

    private static long lastMsgTime = 0;

    private static VoiceRoomMsg structMsg(VoiceRoomMsgSendInfo sendInfo, int media_type, String content) {
        long time = System.currentTimeMillis();
        if (time <= lastMsgTime) time = lastMsgTime + 1;

        VoiceRoomMsg msg = new VoiceRoomMsg();
        msg.setSend_uid(LoginHelper.getLoginUid());
        msg.setRid(sendInfo.room_id);
        msg.setMid(generateRoomMid(msg.getSend_uid(), msg.getRid(), time));

        if (sendInfo.isSubTypeRoomCard()) {
            msg.setMsgContent(ResUtil.getStr(R.string.my_nickname));
        } else {
            msg.setMsgContent(content);
        }
        msg.setMediaType(media_type);
        msg.setTime(time);
        msg.setStatus(VoiceRoomMsg.STATUS_SENDING);
        msg.setSubType(sendInfo.sub_type);
        msg.setSourceType(sendInfo.getSourceType());
        if (sendInfo.bubble) {
            try {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("bullet_screen", 1);
                msg.setExt(jsonObject.toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        lastMsgTime = time;
        return msg;
    }

    public static VoiceRoomMsg structFaceMsg(int rid, String ext) {
        long time = System.currentTimeMillis();
        if (time <= lastMsgTime) time = lastMsgTime + 1;

        VoiceRoomMsg msg = new VoiceRoomMsg();
        msg.setRid(rid);
        msg.setSend_uid(LoginHelper.getLoginUid());
        msg.setMid(generateRoomMid(msg.getSend_uid(), msg.getRid(), time));
        msg.setSubType(VoiceRoomMsg.SUBTYPE_EMOTION);
        msg.setMediaType(VoiceRoomMsg.MEDIA_TYPE_TEXT);
        msg.setTime(time);
        msg.setStatus(VoiceRoomMsg.STATUS_SENDING);
        msg.setMsgContent("");
        msg.setExt(ext);
        lastMsgTime = time;
        return msg;
    }

    public static VoiceRoomMsg structShareQQLinkMsg(int rid, String link) {
        VoiceRoomMsgSendInfo sendInfo = new VoiceRoomMsgSendInfo();
        sendInfo.room_id = rid;
        VoiceRoomMsgSendInfo.TextInfo textInfo = new VoiceRoomMsgSendInfo.TextInfo(link);
        sendInfo.setTextInfo(textInfo);
        int mediaType = VoiceRoomMsg.MEDIA_TYPE_TEXT;
        sendInfo.sub_type = VoiceRoomMsg.SUBTYPE_SEND_QQ_SHARETEAM_URL;

        return structMsg(sendInfo, mediaType, link);
    }

    private static final AtomicInteger sIdGen = new AtomicInteger(0);

    public static String generateRoomMid(int send_uid, int room_id, long time, int num) {
        return send_uid + "_" + room_id + "_" + time + "_" + num;
    }

    public static String generateRoomMid(int send_uid, int room_id, long time) {
        return generateRoomMid(send_uid, room_id, time, sIdGen.getAndIncrement());
    }
}