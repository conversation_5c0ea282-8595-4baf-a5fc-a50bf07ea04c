package com.wepie.wespy.module.voiceroom

import com.wepie.wespy.module.voiceroom.bigwinner.BigWinnerDelegateImpl
import com.wepie.wespy.module.voiceroom.bigwinner.BigWinnerView
import com.wepie.wespy.module.voiceroom.bigwinner.IBigWinner
import com.wepie.wespy.module.voiceroom.bingo.BingoDefaultView
import com.wepie.wespy.module.voiceroom.bingo.BingoDelegateImpl
import com.wepie.wespy.module.voiceroom.bingo.IBingo
import com.wepie.wespy.module.voiceroom.draw.DrawGuessDelegateImpl
import com.wepie.wespy.module.voiceroom.draw.IDrawView
import com.wepie.wespy.module.voiceroom.draw.RoomDrawView
import com.wepie.wespy.module.voiceroom.galaWeb.GalaH5DelegateImpl
import com.wepie.wespy.module.voiceroom.galaWeb.GalaH5WebView
import com.wepie.wespy.module.voiceroom.guide.IRoomGuidePlugin
import com.wepie.wespy.module.voiceroom.guide.RoomGuidView
import com.wepie.wespy.module.voiceroom.guide.RoomGuideDelegateImpl
import com.wepie.wespy.module.voiceroom.lottery.ILotteryAnim
import com.wepie.wespy.module.voiceroom.lottery.LotteryAnimDelegateImpl
import com.wepie.wespy.module.voiceroom.lottery.LotteryBtnDelegateImpl
import com.wepie.wespy.module.voiceroom.lottery.RoomLotteryAnimView
import com.wepie.wespy.module.voiceroom.lottery.RoomLotteryBtnView
import com.wepie.wespy.module.voiceroom.main.leave.RoomLeaveDelegateImpl
import com.wepie.wespy.module.voiceroom.main.leave.RoomLeaveView
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IBasePlugin
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IDialogPlugin
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IMusicRunner
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IRoomH5
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IRoomLeave
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IRoomSimpleView
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IRoomSlot
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.ISettingPlugin
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.ISmallFrameUtil
import com.wepie.wespy.module.voiceroom.music.MusicRunnerDelegateImpl
import com.wepie.wespy.module.voiceroom.music.VoiceMusicRunner
import com.wepie.wespy.module.voiceroom.pk445.DialogDelegateImpl
import com.wepie.wespy.module.voiceroom.pk445.DialogView
import com.wepie.wespy.module.voiceroom.pk445.IPk
import com.wepie.wespy.module.voiceroom.pk445.PkView
import com.wepie.wespy.module.voiceroom.rain.IRoomRain
import com.wepie.wespy.module.voiceroom.rain.RoomRainDelegateImpl
import com.wepie.wespy.module.voiceroom.rain.RoomRainView
import com.wepie.wespy.module.voiceroom.roomgroup.RoomOwnerGroupDelegateImpl
import com.wepie.wespy.module.voiceroom.roomgroup.RoomOwnerGroupView
import com.wepie.wespy.module.voiceroom.scoreboard.RoomUpdateDialog
import com.wepie.wespy.module.voiceroom.scoreboard.RoomUpdateDialogDelegateImpl
import com.wepie.wespy.module.voiceroom.scoreboard.ScoreBoardDelegateImpl
import com.wepie.wespy.module.voiceroom.scoreboard.ScoreBoardSmallView
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomSettingDelegateImpl
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomSettingView
import com.wepie.wespy.module.voiceroom.slot.RoomSlotView
import com.wepie.wespy.module.voiceroom.slot.SlotDelegateImpl
import com.wepie.wespy.module.voiceroom.smallframe.SmallFrameDelegateImpl
import com.wepie.wespy.module.voiceroom.smallframe.VoiceSmallFrameView

/**
 * 语音房插件抽象出的数据，单例对象，不持有其他UI
 * @param persistence 是否持久化，意思是当第一次View add之后不会remove，选择改变visibility
 */
sealed class ViewPlugin<T, E : IBasePlugin>(
    val tag: String,
    val cls: Class<T>, // 实际的View
    val superCls: Class<E>, // 接口类型，被调用的接口
    val persistence: Boolean = false
) {
    companion object {
        private val allPlugins = listOf(
            MusicRunner,
            DrawGuess,
            Pk445,
            Dialog,
            Slot,
            BigWinner,
            OwnerGroup,
            RoomUpdate,
            RoomSettings,
            RoomLeave,
            RoomRain,
            GalaH5,
            ScoreBoard,
            SmallFrame,
            RoomGuide,
            Bingo,
            LotteryBtn,
            LotteryAnim
        )
        val tagToPluginMap = mutableMapOf<String, ViewPlugin<*, *>>()

        init {
            allPlugins.forEach {
                tagToPluginMap[it.tag] = it
            }
        }
    }

    @Suppress("UNCHECKED_CAST")
    fun onInit(pluginTool: IPluginTool<IBasePlugin>): E {
        return delegate(pluginTool as IPluginTool<E>)
    }

    /**
     * 返回代理类，该类监听数据，决定是否应该初始化View
     * 同时，该类也会注入到VoicePluginService中
     */
    abstract fun delegate(pluginTool: IPluginTool<E>): E

    /**
     * 音乐
     */
    object MusicRunner : ViewPlugin<VoiceMusicRunner, IMusicRunner>(
        "MusicRunner",
        VoiceMusicRunner::class.java,
        IMusicRunner::class.java
    ) {
        override fun delegate(pluginTool: IPluginTool<IMusicRunner>): IMusicRunner {
            return MusicRunnerDelegateImpl(pluginTool)
        }
    }

    /**
     * 画猜
     */
    object DrawGuess : ViewPlugin<RoomDrawView, IDrawView>(
        "DrawGuess",
        RoomDrawView::class.java,
        IDrawView::class.java
    ) {
        override fun delegate(pluginTool: IPluginTool<IDrawView>): IDrawView {
            return DrawGuessDelegateImpl(pluginTool)
        }
    }

    /**
     * Bingo
     */
    object Bingo : ViewPlugin<BingoDefaultView, IBingo>(
        "Bingo",
        BingoDefaultView::class.java,
        IBingo::class.java
    ) {
        override fun delegate(pluginTool: IPluginTool<IBingo>): IBingo {
            return BingoDelegateImpl(pluginTool)
        }
    }

    /**
     * pk
     */
    object Pk445 : ViewPlugin<PkView, IPk>(
        "Pk445",
        PkView::class.java,
        IPk::class.java
    ) {
        override fun delegate(pluginTool: IPluginTool<IPk>): IPk {
            return com.wepie.wespy.module.voiceroom.pk445.PkDelegateImpl(pluginTool)
        }
    }

    /**
     * 抽奖
     */
    object Slot : ViewPlugin<RoomSlotView, IRoomSlot>(
        "Slot",
        RoomSlotView::class.java,
        IRoomSlot::class.java
    ) {
        override fun delegate(pluginTool: IPluginTool<IRoomSlot>): IRoomSlot {
            return SlotDelegateImpl(pluginTool)
        }
    }

    /**
     * 大赢家
     */
    object BigWinner : ViewPlugin<BigWinnerView, IBigWinner>(
        "BigWinner",
        BigWinnerView::class.java,
        IBigWinner::class.java
    ) {
        override fun delegate(pluginTool: IPluginTool<IBigWinner>): IBigWinner {
            return BigWinnerDelegateImpl(pluginTool)
        }
    }

    /**
     * 房主群聊
     */
    object OwnerGroup : ViewPlugin<RoomOwnerGroupView, IBasePlugin>(
        "OwnerGroup",
        RoomOwnerGroupView::class.java,
        IBasePlugin::class.java
    ) {
        override fun delegate(pluginTool: IPluginTool<IBasePlugin>): IBasePlugin {
            return RoomOwnerGroupDelegateImpl(pluginTool)
        }
    }

    /**
     * 房间升级
     */
    object RoomUpdate : ViewPlugin<RoomUpdateDialog, IRoomSimpleView>(
        "RoomUpdate",
        RoomUpdateDialog::class.java,
        IRoomSimpleView::class.java
    ) {

        override fun delegate(pluginTool: IPluginTool<IRoomSimpleView>): IRoomSimpleView {
            return RoomUpdateDialogDelegateImpl(pluginTool)
        }
    }

    /**
     * 房间设置
     */
    object RoomSettings : ViewPlugin<VoiceRoomSettingView, ISettingPlugin>(
        "RoomSettings",
        VoiceRoomSettingView::class.java,
        ISettingPlugin::class.java,
        true
    ) {
        override fun delegate(pluginTool: IPluginTool<ISettingPlugin>): ISettingPlugin {
            return VoiceRoomSettingDelegateImpl(pluginTool)
        }
    }

    /**
     * 房间退出
     */
    object RoomLeave : ViewPlugin<RoomLeaveView, IRoomLeave>(
        "RoomLeave", RoomLeaveView::class.java, IRoomLeave::class.java
    ) {
        override fun delegate(pluginTool: IPluginTool<IRoomLeave>): IRoomLeave {
            return RoomLeaveDelegateImpl(pluginTool)
        }
    }

    object RoomRain : ViewPlugin<RoomRainView, IRoomRain>(
        "RoomRain", RoomRainView::class.java, IRoomRain::class.java
    ) {
        override fun delegate(pluginTool: IPluginTool<IRoomRain>): IRoomRain {
            return RoomRainDelegateImpl(pluginTool)
        }
    }

    object GalaH5 : ViewPlugin<GalaH5WebView, IRoomH5>(
        "GalaH5", GalaH5WebView::class.java, IRoomH5::class.java,
        true
    ) {
        override fun delegate(pluginTool: IPluginTool<IRoomH5>): IRoomH5 {
            return GalaH5DelegateImpl(pluginTool)
        }
    }

    object ScoreBoard : ViewPlugin<ScoreBoardSmallView, IBasePlugin>(
        "ScoreBoard", ScoreBoardSmallView::class.java, IBasePlugin::class.java,
        true
    ) {
        override fun delegate(pluginTool: IPluginTool<IBasePlugin>): IBasePlugin {
            return ScoreBoardDelegateImpl(pluginTool)
        }
    }

    object SmallFrame : ViewPlugin<VoiceSmallFrameView, ISmallFrameUtil>(
        "SmallFrame", VoiceSmallFrameView::class.java, ISmallFrameUtil::class.java,
        true
    ) {
        override fun delegate(pluginTool: IPluginTool<ISmallFrameUtil>): ISmallFrameUtil {
            return SmallFrameDelegateImpl(pluginTool)
        }
    }

    object Dialog : ViewPlugin<DialogView, IDialogPlugin>(
        "Dialog", DialogView::class.java, IDialogPlugin::class.java,
        true
    ) {
        override fun delegate(pluginTool: IPluginTool<IDialogPlugin>): IDialogPlugin {
            return DialogDelegateImpl(pluginTool)
        }
    }

    object RoomGuide : ViewPlugin<RoomGuidView, IRoomGuidePlugin>(
        "RoomGuide", RoomGuidView::class.java, IRoomGuidePlugin::class.java,
        true
    ) {
        override fun delegate(pluginTool: IPluginTool<IRoomGuidePlugin>): IRoomGuidePlugin {
            return RoomGuideDelegateImpl(pluginTool)
        }
    }

    /**
     * 抽奖按钮
     */
    object LotteryBtn : ViewPlugin<RoomLotteryBtnView, IBasePlugin>(
        "LotteryBtn", RoomLotteryBtnView::class.java, IBasePlugin::class.java
    ) {
        override fun delegate(pluginTool: IPluginTool<IBasePlugin>): IBasePlugin {
            return LotteryBtnDelegateImpl(pluginTool)
        }
    }

    /**
     * 抽奖面板
     */
    object LotteryAnim : ViewPlugin<RoomLotteryAnimView, ILotteryAnim>(
        "LotteryAnim", RoomLotteryAnimView::class.java, ILotteryAnim::class.java
    ) {
        override fun delegate(pluginTool: IPluginTool<ILotteryAnim>): ILotteryAnim {
            return LotteryAnimDelegateImpl(pluginTool)
        }
    }
}