package com.wepie.wespy.module.voiceroom.guards

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.*
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.util.ToastUtil
import com.huiwan.component.activity.BaseFragment
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.wejoy.weplay.ex.lifecycle.LifeViewModel
import com.wejoy.weplay.ex.lifecycle.toLife
import com.wepie.wespy.module.voiceroom.guards.GuardDetailFragment.Companion.PAGE_SIZE
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.GetConsumerRankResp
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender
import kotlinx.coroutines.launch

class GuardDetailFragment : BaseFragment() {

    private var index: Int = 0
    private var rid: Int = 0

    private lateinit var model: GuardDetailViewModel
    private lateinit var adapter: VoiceGuardAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            index = it.getInt("index")
            rid = it.getInt("rid")
        }
        model = ViewModelProvider(this).get(GuardDetailViewModel::class.java)
        model.init(
            rid,
            TYPE_ARRAY[index], 0,
            ViewModelProvider(requireParentFragment()).get(VoiceRoomGuardsView.VoiceGuardViewModel::class.java)
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val context = requireContext()
        val params = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        val rv = RecyclerView(context)
        rv.layoutParams = params
        val manager = WrapContentLinearLayoutManager(context)
        manager.initialPrefetchItemCount = 20
        rv.layoutManager = manager
        rv.setItemViewCacheSize(20)
        rv.setHasFixedSize(true)
        rv.isNestedScrollingEnabled = false
        adapter = VoiceGuardAdapter(context)
        rv.adapter = adapter
        return rv
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        model.getGuardListLiveData().observe(viewLifecycleOwner) {
            val newGuardList = it
            val oldGuardList = adapter.data
            val diffResult = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
                override fun getOldListSize(): Int {
                    return adapter.data.size
                }

                override fun getNewListSize(): Int {
                    return newGuardList.size
                }

                override fun areItemsTheSame(
                    oldItemPosition: Int,
                    newItemPosition: Int
                ): Boolean {
                    val old = oldGuardList[oldItemPosition]
                    val newItem = newGuardList[newItemPosition]
                    return old != null && old.uid == newItem.uid && old.rank == newItem.rank
                }

                override fun areContentsTheSame(
                    oldItemPosition: Int,
                    newItemPosition: Int
                ): Boolean {
                    val old = oldGuardList[oldItemPosition]
                    val newItem = newGuardList[newItemPosition]
                    return old != null && old.score == newItem.score
                }
            }, true)
            adapter.updateList(newGuardList)
            diffResult.dispatchUpdatesTo(adapter)
        }
    }

    fun setData(index: Int, rid: Int) {
        val bundle = Bundle()
        bundle.putInt("index", index)
        bundle.putInt("rid", rid)
        arguments = bundle
    }

    companion object {
        val TYPE_ARRAY = arrayOf("day", "week", "total")
        const val PAGE_SIZE = 100
    }
}

/**
 * LinearLayoutManager 包装类
 * 主要解决Adapter getItemCount时为了显示emptyview 在size == 0的时候依然返回1的时候,导致的下标出界
 */
class WrapContentLinearLayoutManager(context: Context?) :
    LinearLayoutManager(context) {
    override fun onLayoutChildren(recycler: RecyclerView.Recycler, state: RecyclerView.State) {
        try {
            super.onLayoutChildren(recycler, state)
        } catch (e: IndexOutOfBoundsException) {
        }
    }
}

class GuardDetailViewModel : LifeViewModel() {

    private val consumerListLiveData = MutableLiveData<MutableList<ConsumerItem>>()

    private lateinit var model: VoiceRoomGuardsView.VoiceGuardViewModel

    init {

    }

    fun init(rid: Int, type: String, offset: Int, model: VoiceRoomGuardsView.VoiceGuardViewModel) {
        this.model = model
        VoiceRoomPacketSender.getConsumerRank(
            rid,
            type,
            offset,
            PAGE_SIZE,
            object : LifeSeqCallback(toLife()) {
                override fun onSuccess(head: RspHeadInfo) {
                    viewModelScope.launch {
                        consumerListLiveData.value =
                            ConsumerItem.parseList(head.message as GetConsumerRankResp)
                        model.updateMine(ConsumerItem.parseItem((head.message as GetConsumerRankResp).mine))
                    }
                }

                override fun onFail(head: RspHeadInfo) {
                    ToastUtil.show(head.desc)
                }
            })
    }

    fun getGuardListLiveData(): LiveData<out List<ConsumerItem>> = consumerListLiveData

}