package com.wepie.wespy.module.voiceroom.guards;

import android.content.Context;
import android.graphics.Typeface;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.PopupWindow;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.FontUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.ViewUtil;
import com.huiwan.component.activity.BaseFragment;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.shence.ShenceEvent;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.voiceroom.member.VoiceMemberViewModel;
import com.wepie.wespy.module.voiceroom.member.VoiceRoomMemberDialog;
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

import java.util.HashMap;
import java.util.Map;

/**
 * 守护团弹窗
 * date 2021/01/18
 *
 * <AUTHOR>
 */
public class VoiceRoomGuardsView extends BaseFragment {

    private final TextView[] mGuardRankTitleViews = new TextView[3];
    private VoiceGuardViewHolder mineViewHolder;
    private ViewPager2 guardPager;

    public int offset = 0;
    public String[] btnNames = {TrackButtonName.CARE_RANK_DAILY, TrackButtonName.CARE_RANK_WEEK, TrackButtonName.CARE_RANK_TOTAL};

    private VoiceGuardViewModel model;
    private VoiceMemberViewModel voiceMemberViewModel;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new ViewModelProvider(this).get(VoiceGuardViewModel.class);
        voiceMemberViewModel = new ViewModelProvider(requireParentFragment()).get(VoiceMemberViewModel.class);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.voice_room_guards_view, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initView(view);
        initData();
    }

    private void initView(View view) {
        mGuardRankTitleViews[0] = view.findViewById(R.id.guard_rank_daily);
        mGuardRankTitleViews[1] = view.findViewById(R.id.guard_rank_weekly);
        mGuardRankTitleViews[2] = view.findViewById(R.id.guard_rank_total);

        View.OnClickListener listener = v -> guardPager.setCurrentItem((Integer) v.getTag(), false);
        for (int i = 0; i < mGuardRankTitleViews.length; i++) {
            initRankTitleView(i, listener);
        }

        mineViewHolder = new VoiceGuardViewHolder(view.findViewById(R.id.voiceGuardItemView));
        ImageView helpIv = view.findViewById(R.id.help_iv);
        guardPager = view.findViewById(R.id.guard_pager);
        guardPager.setAdapter(new FragmentStateAdapter(this) {
            @NonNull
            @Override
            public Fragment createFragment(int position) {
                GuardDetailFragment fragment = new GuardDetailFragment();
                fragment.setData(position, voiceMemberViewModel.getRid());
                return fragment;
            }

            @Override
            public int getItemCount() {
                return 3;
            }
        });

        guardPager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                updateView(position);
                updateMine(voiceMemberViewModel.getRid(), position);
            }
        });
        helpIv.setOnClickListener(v -> {
            View popupView = LayoutInflater.from(getContext()).inflate(R.layout.voice_guard_contribution_popup, null);
            PopupWindow popupWindow = new PopupWindow(popupView, ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT, true);
            int height = calculatePopWindowHeight(popupWindow);
            int yoff = height + ScreenUtil.dip2px(20);
            popupWindow.setOutsideTouchable(true);
            if (ScreenUtil.isRtl()) {
                popupWindow.showAsDropDown(helpIv, ScreenUtil.dip2px(174), -yoff);
            } else {
                popupWindow.showAsDropDown(helpIv, -ScreenUtil.dip2px(174), -yoff);
            }
        });
    }

    private int calculatePopWindowHeight(PopupWindow popupWindow) {
        popupWindow.setWidth(ScreenUtil.dip2px(200));
        int measureWidthParams = View.MeasureSpec.makeMeasureSpec(ScreenUtil.dip2px(200), View.MeasureSpec.EXACTLY);
        popupWindow.getContentView().measure(measureWidthParams, View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
        return popupWindow.getContentView().getMeasuredHeight();
    }

    private void initData() {
        model.getMineGuardLiveData().observe(getViewLifecycleOwner(), guardItem -> mineViewHolder.updateView(guardItem));
    }

    private void initRankTitleView(int index, View.OnClickListener listener) {
        TextView textView = mGuardRankTitleViews[index];
        //根据文字设置一下宽度,防抖
        ViewUtil.setWidthForTextView(textView, true);
        textView.setTag(index);
        textView.setOnClickListener(listener);
    }

    public void updateView(int tabPosition) {
        int rid = voiceMemberViewModel.getRid();
        Map<String, Object> map = new HashMap<>();
        map.put("rid", rid);
        map.put("screen_sub_name", btnNames[tabPosition]);
        ShenceEvent.appViewScreen(TrackScreenName.VOICE_ROOM_CHAT, map);
        mineViewHolder.itemView.setVisibility(View.VISIBLE);
        mineViewHolder.itemView.setVisibility(View.VISIBLE);
        TextView oldSelectView = mGuardRankTitleViews[offset];
        FontUtil.setTextStyle(oldSelectView, Typeface.NORMAL);
        oldSelectView.setTextColor(ResUtil.getColor(R.color.voice_room_guards_tab_unselected_color));
        oldSelectView.setBackground(ResUtil.getDrawable(R.drawable.rank_tab_contribution_unselected_bg));
        TextView currentSelectView = mGuardRankTitleViews[tabPosition];
        FontUtil.setTextStyle(currentSelectView, Typeface.BOLD);
        currentSelectView.setTextColor(ResUtil.getColor(R.color.voice_room_guards_tab_selected_color));
        currentSelectView.setBackground(ResUtil.getDrawable(R.drawable.rank_tab_contribution_selected_bg));
        offset = tabPosition;
    }

    public static void showGuardView(Context context, int rid) {
        JumpUtil.showVoiceRoomMemberDialog(context, rid, VoiceRoomMemberActivity.ACTION_TYPE_MEMBER_LIST, VoiceRoomMemberDialog.TAB_CONTRIBUTION, -1);
    }

    public static class VoiceGuardViewModel extends ViewModel {
        private final MutableLiveData<ConsumerItem> guardItemLiveData = new MutableLiveData<>();

        public void updateMine(ConsumerItem item) {
            guardItemLiveData.setValue(item);
        }

        public LiveData<ConsumerItem> getMineGuardLiveData() {
            return guardItemLiveData;
        }
    }

    public void updateMine(int rid, int position) {
        VoiceRoomPacketSender.getConsumerRank(
                rid,
                GuardDetailFragment.Companion.getTYPE_ARRAY()[position],
                0,
                GuardDetailFragment.PAGE_SIZE,
                new LifeSeqCallback(this) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        model.updateMine(ConsumerItem.Companion.parseItem(((TmpRoomPackets.GetConsumerRankResp) head.message).getMine()));
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                });
    }
}

