package com.wepie.wespy.module.voiceroom.guards;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.ui.empty.HWUIEmptyView;
import com.wepie.wespy.R;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;

import java.util.ArrayList;
import java.util.List;

public class VoiceGuardAdapter extends RecyclerView.Adapter<VoiceGuardAdapter.ViewHolder> {
    private final Context context;
    private final List<ConsumerItem> consumerItemList = new ArrayList<>();
    public static final int EMPTY_TYPE = 0;
    public static final int NORMAL_TYPE = 1;

    public VoiceGuardAdapter(Context context) {
        this.context = context;
    }

    @NonNull
    @Override
    public VoiceGuardAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == EMPTY_TYPE) {
            HWUIEmptyView emptyView = new HWUIEmptyView(parent.getContext());
            emptyView.setGravity(Gravity.CENTER);
            emptyView.setType(HWUIEmptyView.base_empty_no_data);
            emptyView.setText(ResUtil.getStr(R.string.voice_room_guard_empty_view_1));
            emptyView.setTextColorRes(R.color.color_text_primary);
            emptyView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
            return new ViewHolder(emptyView);
        } else {
            return VoiceGuardViewHolder.create(context, parent);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        if (holder instanceof VoiceGuardViewHolder) {
            VoiceGuardViewHolder h = (VoiceGuardViewHolder) holder;
            ConsumerItem consumerItem = consumerItemList.get(position);
            h.updateView(consumerItem);
            h.itemView.setOnClickListener(v -> JumpUtil.enterUserInfoDetailFromVoiceRoom(context, consumerItem.getUid(), VoiceRoomService.getInstance().getRid(), true, false));
        }
    }

    @Override
    public int getItemCount() {
        if (consumerItemList.size() == 0)
            return 1;
        else {
            return consumerItemList.size();
        }
    }

    public void updateList(List<ConsumerItem> list) {
        consumerItemList.clear();
        consumerItemList.addAll(list);
//        notifyDataSetChanged();
    }

    public List<ConsumerItem> getData() {
        return consumerItemList;
    }

    @Override
    public int getItemViewType(int position) {
        if (consumerItemList.size() == 0)
            return EMPTY_TYPE;
        else return NORMAL_TYPE;
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
        }
    }
}
