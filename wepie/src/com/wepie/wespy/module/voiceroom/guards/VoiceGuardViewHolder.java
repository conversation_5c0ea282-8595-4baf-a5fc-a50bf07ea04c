package com.wepie.wespy.module.voiceroom.guards;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.member.VoiceRoomMemberSystemBaseItem;
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity;

public class VoiceGuardViewHolder extends VoiceGuardAdapter.ViewHolder {

    private final TextView mGuardRankNum;
    private final VoiceRoomMemberSystemBaseItem mRmBaseItem;
    private final TextView mGuardNumberTv;

    public VoiceGuardViewHolder(@NonNull View view) {
        super(view);
        mGuardRankNum = view.findViewById(R.id.guard_rank_num);
        mRmBaseItem = view.findViewById(R.id.rm_base_item);
        mGuardNumberTv = view.findViewById(R.id.guard_number_tv);
    }

    public void updateView(ConsumerItem consumerItem) {
        mRmBaseItem.update(VoiceRoomService.getInstance().getRoomInfo(), consumerItem.getUid(), false, VoiceRoomMemberActivity.ACTION_TYPE_GUARD);
        mRmBaseItem.updateMemberLevel(consumerItem.roomMemberInfo.level,consumerItem.roomMemberInfo.isFreeze);

        mGuardNumberTv.setText(String.valueOf(consumerItem.getScore()));
        mGuardRankNum.setBackground(null);
        mGuardRankNum.setText("");
        if (consumerItem.getRank() > 100 || consumerItem.getRank() == 0) {
//            mGuardRankNum.setText(R.string.unrank);
            mGuardRankNum.setText("-");
        } else {
            if (consumerItem.getRank() > 3) {
                mGuardRankNum.setText(String.valueOf(consumerItem.getRank()));
            } else if (consumerItem.getRank() == 1) {
                mGuardRankNum.setBackground(ResUtil.getDrawable(R.drawable.ic_guard_rank_1));
            } else if (consumerItem.getRank() == 2) {
                mGuardRankNum.setBackground(ResUtil.getDrawable(R.drawable.ic_guard_rank_2));
            } else if (consumerItem.getRank() == 3) {
                mGuardRankNum.setBackground(ResUtil.getDrawable(R.drawable.ic_guard_rank_3));
            }
        }
    }

    public static VoiceGuardViewHolder create(Context context, @NonNull ViewGroup parent) {
        return new VoiceGuardViewHolder(LayoutInflater.from(context).inflate(R.layout.voice_room_guard_item, parent, false));
    }
}