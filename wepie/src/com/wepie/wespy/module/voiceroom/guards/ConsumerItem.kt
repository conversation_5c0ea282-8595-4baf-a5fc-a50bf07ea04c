package com.wepie.wespy.module.voiceroom.guards

import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.GetConsumerRankResp


class ConsumerItem {
    var uid = 0
    var rank = 0
    var score = 0
    lateinit var roomMemberInfo : VoiceRoomInfo.RoomMemberInfo

    constructor() {}
    constructor(uid: Int, rank: Int, score: Int,roomMemberInfo : VoiceRoomInfo.RoomMemberInfo) {
        this.uid = uid
        this.rank = rank
        this.score = score
        this.roomMemberInfo = roomMemberInfo
    }

    companion object {
        fun parseList(message: GetConsumerRankResp?): MutableList<ConsumerItem> {
            val consumerItemList: MutableList<ConsumerItem> = ArrayList()
            return if (message == null) consumerItemList else parseList(message.dataList)
        }

        fun parseList(rankItemList: List<TmpRoomPackets.RankItemWithMemberInfo?>): MutableList<ConsumerItem> {
            val consumerItemList: MutableList<ConsumerItem> = ArrayList()
            for (rankItem in rankItemList) {
                consumerItemList.add(parseItem(rankItem))
            }
            return consumerItemList
        }

        fun parseItem(rankItem: TmpRoomPackets.RankItemWithMemberInfo?): ConsumerItem {
            return if (rankItem == null) ConsumerItem() else ConsumerItem(
                rankItem.uid,
                rankItem.rank,
                rankItem.score,
                VoiceRoomInfo.RoomMemberInfo.parse(rankItem.roomMemberInfo)

            )
        }
    }
}