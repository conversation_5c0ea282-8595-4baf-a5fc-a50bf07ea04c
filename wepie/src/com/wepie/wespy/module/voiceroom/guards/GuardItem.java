package com.wepie.wespy.module.voiceroom.guards;

import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;

import java.util.ArrayList;
import java.util.List;

public class GuardItem {
    private int uid;
    private int rank;
    private int guardNumber;

    public GuardItem() {
    }

    public GuardItem(int uid, int rank, int guardNumber) {
        this.uid = uid;
        this.rank = rank;
        this.guardNumber = guardNumber;
    }

    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public int getGuardNumber() {
        return guardNumber;
    }

    public void setGuardNumber(int guardNumber) {
        this.guardNumber = guardNumber;
    }

    public static List<GuardItem> parseList(TmpRoomPackets.GetCareRankRsp message){
        List<GuardItem> guardItemList = new ArrayList<>();
        if (message == null) return guardItemList;
        return parseList(message.getDataList());
    }

    public static List<GuardItem> parseList(List<TmpRoomPackets.RankItem> rankItemList){
        List<GuardItem> guardItemList = new ArrayList<>();
        for (TmpRoomPackets.RankItem  rankItem : rankItemList){
            guardItemList.add(GuardItem.parseItem(rankItem));
        }
        return guardItemList;
    }

    public static GuardItem parseItem(TmpRoomPackets.RankItem rankItem){
        if (rankItem == null) return new GuardItem();
        return new GuardItem(rankItem.getUid(), rankItem.getRank(), rankItem.getScore());
    }
}
