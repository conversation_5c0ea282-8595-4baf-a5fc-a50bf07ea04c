package com.wepie.wespy.module.voiceroom.pk445

import com.huiwan.libtcp.PacketSendHelper
import com.huiwan.libtcp.callback.SeqCallback
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.AdvancePkMod
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.AdvancePkSupportScoreReq
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.EndAdvancePkReq
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.ModifyPkWidgetReq
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.RoomReqBody
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.StartAdvancePkReq
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.SyncAdvancePkReq
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.VoteForAdvancePkReq
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender

object PkPacketSender {

    fun modifyPkWidget(rid: Int, isOpen: Boolean, userMode: Int, pkMode: Int, timeInSec: Int, callback: SeqCallback?) {
        val reqHead = VoiceRoomPacketSender.buildRoomReqHead(
            TmpRoomPackets.TmpRoomOpType.MODIFY_PK_WIDGET_VALUE,
            rid
        )
        val advancePkMod =
            AdvancePkMod.newBuilder().setPkUserModeValue(userMode).setPkModeValue(pkMode).build()
        val req = ModifyPkWidgetReq.newBuilder()
            .setIsOpen(isOpen)
            .setGameMod(advancePkMod)
            .setDuration(timeInSec)
            .build()
        val reqBody = RoomReqBody.newBuilder()
            .setModifyPkWidget(req)
            .build()
        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback)
    }

    fun pkSupportScore(rid: Int, supportForUid: Int, callback: SeqCallback?) {
        val reqHead = VoiceRoomPacketSender.buildRoomReqHead(
            TmpRoomPackets.TmpRoomOpType.ADVANCE_PK_SUPPORT_SCORE_VALUE, rid
        )
        val req = AdvancePkSupportScoreReq.newBuilder().setSupportFor(supportForUid).build()
        val reqBody = RoomReqBody.newBuilder()
            .setAdvancePkSupportScore(req)
            .build()
        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback)
    }

    fun startAdvancePk(rid: Int, callback: SeqCallback?) {
        val reqHead = VoiceRoomPacketSender.buildRoomReqHead(
            TmpRoomPackets.TmpRoomOpType.START_ADVANCE_PK_VALUE, rid
        )
        val syncReq = StartAdvancePkReq.newBuilder().build()
        val reqBody = RoomReqBody.newBuilder()
            .setStartAdvancePk(syncReq)
            .build()
        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback)
    }

    fun endAdvancePk(rid: Int, callback: SeqCallback?) {
        val reqHead = VoiceRoomPacketSender.buildRoomReqHead(
            TmpRoomPackets.TmpRoomOpType.END_ADVANCE_PK_VALUE, rid
        )
        val syncReq = EndAdvancePkReq.newBuilder().build()
        val reqBody = RoomReqBody.newBuilder()
            .setEndAdvancePk(syncReq)
            .build()
        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback)
    }

    fun voteForAdvancePk(rid: Int, voteForUid: Int, callback: SeqCallback?) {
        val reqHead = VoiceRoomPacketSender.buildRoomReqHead(
            TmpRoomPackets.TmpRoomOpType.VOTE_FOR_ADVANCE_PK_VALUE, rid
        )
        val syncReq = VoteForAdvancePkReq.newBuilder().setVoteFor(voteForUid).build()
        val reqBody = RoomReqBody.newBuilder()
            .setVoteForAdvancePk(syncReq)
            .build()
        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback)
    }

    fun sync(rid: Int, callback: SeqCallback?) {
        val reqHead = VoiceRoomPacketSender.buildRoomReqHead(
            TmpRoomPackets.TmpRoomOpType.SYNC_ADVANCE_PK_VALUE, rid
        )
        val syncReq = SyncAdvancePkReq.newBuilder().build()
        val reqBody = RoomReqBody.newBuilder()
            .setSyncAdvancePk(syncReq)
            .build()
        PacketSendHelper.sendVoiceRoomPacket(reqHead, reqBody, null, callback)
    }
}