package com.wepie.wespy.module.voiceroom.pk445

import android.content.Context
import android.os.CountDownTimer
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.TimeUtil
import com.huiwan.base.util.ToastUtil
import com.wejoy.weplay.ex.cancellable.autoCancel
import com.wepie.wespy.R
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import java.util.concurrent.TimeUnit

/**
 * 展示pk状态
 */
class PkStatusView @JvmOverloads constructor(context: Context?, attrs: AttributeSet? = null) :
    FrameLayout(
        context!!, attrs
    ) {

    private val unStartTv: TextView
    private val pkIcon: ImageView
    private val pkStartTimeTv: TextView
    private val bgLay: FrameLayout
    private var timer: CountDownTimer? = null

    init {
        val v = LayoutInflater.from(context).inflate(R.layout.room_pk_status_view, this)
        bgLay = v.findViewById(R.id.pk_status_bg)
        unStartTv = v.findViewById(R.id.pk_status_unstart)
        pkIcon = v.findViewById(R.id.pk_status_icon)
        pkStartTimeTv = v.findViewById(R.id.pk_status_started)
    }

    fun updatePkStatus(info: PkInfo, startCallback: () -> Unit) {
        when (info.status) {
            PkStatus.PK_STATUS_READY -> {
                unStartTv.visibility = View.VISIBLE
                pkIcon.visibility = View.GONE
                pkStartTimeTv.visibility = View.GONE
                val roomInfo = VoiceRoomService.getInstance().roomInfo
                if (roomInfo.isSelfAdminOrOwner) {
                    bgLay.setBackgroundResource(R.drawable.pk_status_start_bg)
                    unStartTv.text = ResUtil.getStr(R.string.pk_status_unstart_owner)
                    unStartTv.setOnClickListener {
                        ToastUtil.debugShow("pk start btn clicked")
                        startCallback.invoke()
                    }
                } else {
                    bgLay.setBackgroundResource(R.drawable.pk_status_bg)
                    unStartTv.text = ResUtil.getStr(R.string.pk_status_unstart_normal)
                }
            }

            PkStatus.PK_STATUS_START -> {
                bgLay.setBackgroundResource(R.drawable.pk_status_bg)
                unStartTv.visibility = View.GONE
                pkIcon.visibility = View.VISIBLE
                pkStartTimeTv.visibility = View.VISIBLE

                val leftTimeMs = info.endTime - TimeUtil.getServerTime() / 1000
                pkStartTimeTv.text =
                    TimeUtil.getMinuteSecond(leftTimeMs)

                timer?.cancel()
                timer = object : CountDownTimer(leftTimeMs * 1000, TimeUnit.SECONDS.toMillis(1)) {
                    override fun onTick(millisUntilFinished: Long) {
                        pkStartTimeTv.text =
                            TimeUtil.getMinuteSecond(millisUntilFinished / 1000)
                    }

                    override fun onFinish() {
                    }
                }
                timer!!.start().autoCancel(this)
            }

            PkStatus.PK_STATUS_CLOSE,
            PkStatus.PK_STATUS_UNKNOWN -> {
            }
        }
    }

}