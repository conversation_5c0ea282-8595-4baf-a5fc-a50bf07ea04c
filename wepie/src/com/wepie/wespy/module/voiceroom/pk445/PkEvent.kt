package com.wepie.wespy.module.voiceroom.pk445

sealed class PkEvent(val info: PkInfo) {

    /**
     * Pk控件打开时会收到一个Ready的事件，没有open事件
     * Pk可以开始/控件打开
     */
    data class PkReadyEvent(val pkInfo: PkInfo, val changeStatus: PkPushChangeStatus) : PkEvent(pkInfo)

    /**
     * Pk开始
     */
    data class PkStartEvent(val pkInfo: PkInfo, val changeStatus: PkPushChangeStatus) : PkEvent(pkInfo)

    /**
     * pk结算
     */
    data class PkResultEvent(val pkInfo: PkInfo, val result: PkPushResult) : PkEvent(pkInfo)

    /**
     * Pk控件关闭
     */
    data class PkCloseEvent(val pkInfo: PkInfo) : PkEvent(pkInfo)

    /**
     * sync
     */
    data class PkSyncEvent(val pkInfo: PkInfo) : PkEvent(pkInfo)

    /**
     * pk分数改变
     */
    data class PkChangeScoreEvent(
        val pkInfo: PkInfo,
        val changeScore: PkPushChangeScore
    ) : PkEvent(pkInfo)
}