package com.wepie.wespy.module.voiceroom.pk445

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnEnd
import androidx.core.view.isVisible
import com.huiwan.anim.SVGAUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.StringUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.component.gift.GiftAnimUtil
import com.huiwan.component.gift.send.GiftSendInfo
import com.huiwan.decorate.DecorHeadImgView
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.opensource.svgaplayer.SVGAImageView
import com.wejoy.weplay.ex.ILife
import com.wejoy.weplay.ex.ILifeUtil
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.helper.shence.util.ShenceGiftUtil
import com.wepie.wespy.module.gift.GiftShowConfigHelper
import com.wepie.wespy.module.voiceroom.dataservice.RoomSenderPresenter
import com.wepie.wespy.module.voiceroom.dataservice.SendGiftCallback
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IDialogPlugin
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IMainPlugin
import com.wepie.wespy.module.voiceroom.pk445.PkDialogNamingView.Companion.LEVEL_BEST_PARTNER
import com.wepie.wespy.module.voiceroom.pk445.PkDialogNamingView.Companion.LEVEL_BIG_FAN
import com.wepie.wespy.module.voiceroom.pk445.PkDialogNamingView.Companion.LEVEL_SUPER_FUNDER
import com.wepie.wespy.module.voiceroom.pk445.PkDialogNamingView.Companion.LEVEL_UNDEFINED
import com.wepie.wespy.module.voiceroom.pk445.PkDialogNamingView.Companion.SVGA_PK_HEAD


/**
 * 展示座位上pk相关布局
 */
class PkSeatItemView @JvmOverloads constructor(
    context: Context?,
    val type: Int,
    val isRedTeam: Boolean,
    attrs: AttributeSet? = null
) : FrameLayout(context!!, attrs) {
    private val titleView: DecorHeadImgView
    private val titleSvga: SVGAImageView
    private val identityView: ConstraintLayout
    private val identityIcon: ImageView
    private val identityTv: TextView
    private val voteTv: TextView
    private val sendGiftView: FrameLayout

    init {
        val v = LayoutInflater.from(context).inflate(R.layout.room_pk_seat_item_view, this)
        titleView = v.findViewById(R.id.pk_seat_title_view)
        titleSvga = v.findViewById(R.id.pk_seat_title_svga)
        identityView = v.findViewById(R.id.pk_seat_identity_view)
        identityIcon = v.findViewById(R.id.pk_seat_identity_icon)
        identityTv = v.findViewById(R.id.pk_seat_identity_tv)

        voteTv = v.findViewById(R.id.pk_seat_vote_tv)
        sendGiftView = v.findViewById(R.id.pk_seat_send_gift_view)

        clipChildren = false
    }

    fun initShow() {
        titleView.visibility = View.GONE
        titleSvga.visibility = View.GONE
        identityView.visibility = View.VISIBLE
        voteTv.visibility = View.GONE
        sendGiftView.visibility = View.GONE

        if (!isRedTeam) {
            identityView.setBackgroundResource(R.drawable.shape_387bfc_2ac8f9_corner10)
        }
        identityTv.text = "PK"
    }

    fun getTitleView(): DecorHeadImgView {
        return titleView
    }

    fun getTitleSvga(): SVGAImageView {
        return titleSvga
    }

    fun getIdentityView(): View {
        return identityView
    }

    fun getVoteView(): View {
        return voteTv
    }

    fun updateGiftPkScore(score: Int, drawable: Drawable, iconUrl: String) {
        identityView.visibility = View.VISIBLE
        identityTv.text = StringUtil.formatPointOneNum(score)
        drawable.let {
            identityView.background = it
        }
        if (iconUrl.isNotEmpty()) {
            WpImageLoader.load(iconUrl, identityIcon)
        }
    }

    fun showDefaultPkLabel() {
        identityView.visibility = View.VISIBLE
        identityTv.text = "PK"
        identityIcon.setImageResource(R.drawable.pk_seat_icon)
        if (isRedTeam) {
            identityView.setBackgroundResource(R.drawable.shape_ff7596_ec2e82_corner10)
        } else {
            identityView.setBackgroundResource(R.drawable.shape_387bfc_2ac8f9_corner10)
        }
    }

    fun hideDefaultPkLabel() {
        identityView.visibility = View.GONE
    }

    fun showVoteView(voteForUid: Int, isShowVote: Boolean, afterVote: (isRed: Boolean) -> Unit) {
        when (type) {
            PkGameMod.LOCAL_MOD_SINGLE_USER -> {
                if (!isShowVote) {
                    return
                }
                voteTv.visibility = View.VISIBLE
                voteTv.setOnClickListener {
                    PkPacketSender.voteForAdvancePk(
                        VoiceRoomService.getInstance().rid,
                        voteForUid,
                        object : LifeSeqCallback(this) {
                            override fun onSuccess(head: RspHeadInfo?) {
                                // 投票入口隐藏，显示已支持
                                afterVote.invoke(isRedTeam)
                            }

                            override fun onFail(head: RspHeadInfo?) {
                                ToastUtil.show(head?.desc)
                            }
                        })
                }
            }

            PkGameMod.LOCAL_MOD_SINGLE_GIFT,
            PkGameMod.LOCAL_MOD_MULTI_GIFT -> {
                if (voteForUid > 0) {
                    sendGiftView.visibility = View.VISIBLE
                    sendGiftView.setOnClickListener {
                        ToastUtil.debugShow("pk send gift clicked")
                        GiftAnimUtil.showGiftView(
                            context,
                            GiftShowConfigHelper.voiceRoom(
                                VoiceRoomService.getInstance().rid,
                                voteForUid
                            )
                        ) { info: GiftSendInfo ->
                            RoomSenderPresenter.sendTempRoomGift(info, object : SendGiftCallback {
                                override fun onSuccess(
                                    rid: Int,
                                    comboId: String,
                                    comboTimes: Int
                                ) {
                                    ShenceGiftUtil.reportVoiceSendGift(info)
                                    VoicePluginService.getPlugin(IMainPlugin::class.java)
                                        .onSendGiftSuccess(info)
                                }

                                override fun onFail(msg: String) {
                                    ToastUtil.show(msg)
                                }

                                override val life: ILife?
                                    get() = ILifeUtil.toLife(this@PkSeatItemView)
                            })
                        }
                    }
                } else {
                    sendGiftView.visibility = View.GONE
                }
            }
        }
    }

    fun isVoteViewShow(): Boolean {
        return voteTv.isVisible || sendGiftView.isVisible
    }

    fun hideVoteView() {
        when (type) {
            PkGameMod.LOCAL_MOD_SINGLE_USER -> {
                voteTv.visibility = View.GONE
            }

            PkGameMod.LOCAL_MOD_SINGLE_GIFT,
            PkGameMod.LOCAL_MOD_MULTI_GIFT -> {
                sendGiftView.visibility = View.GONE
            }
        }
    }

    fun showTitleViewChange(
        pkUserInfo: PkUserInfo,
        titleLevel: Int,
        pkStatus: Int,
        v: DecorHeadImgView,
        svgaView: SVGAImageView,
        seatNum: Int
    ) {
        if (titleLevel != LEVEL_UNDEFINED) {
            titleView.visibility = VISIBLE
            titleView.showUserHead(pkUserInfo.titleUid)
            titleView.setOnClickListener {
                PkDialogNamingView.show(context, pkUserInfo, pkStatus)
            }
            if (pkUserInfo.isNewTitle) {
                if (titleLevel == LEVEL_BIG_FAN) {
                    PkDialogViewAnimUtil.showAnimByScale(titleView, 250).doOnEnd {
                        titleView.borderColor = Color.parseColor("#E6E7EC")
                        titleView.setBorderWidth(ScreenUtil.dip2px(1.5f))
                    }
                } else {
                    val pkDialogNamingView = PkDialogNamingView(context, pkUserInfo, pkStatus, true)
                    val plugin = VoicePluginService.getPlugin(IDialogPlugin::class.java)
                    val lastView = plugin.getChildAt(plugin.getChildCount() - 1)
                    if ((lastView is PkDialogNamingView) && (!lastView.isShowAnim)) {
                        plugin.add(pkDialogNamingView, plugin.getChildCount() - 1)
                    } else {
                        plugin.add(pkDialogNamingView)
                    }
                    pkDialogNamingView.showNamingAnim(v, svgaView, seatNum)
                }
            }

        } else {
            titleView.visibility = GONE
            titleSvga.visibility = GONE
        }
    }

    fun showTitleViewSync(pkUserInfo: PkUserInfo, titleLevel: Int, pkStatus: Int) {
        if (titleLevel != LEVEL_UNDEFINED) {
            titleView.visibility = VISIBLE
            titleView.showUserHead(pkUserInfo.titleUid)
            if (titleLevel == LEVEL_BIG_FAN) {
                titleView.borderColor = Color.parseColor("#E6E7EC")
                titleView.setBorderWidth(ScreenUtil.dip2px(1.5f))
            } else if (titleLevel == LEVEL_BEST_PARTNER) {
                titleView.setGradiantBorder(
                    ScreenUtil.dip2px(1.5f),
                    Color.parseColor("#FFF48E"),
                    Color.parseColor("#FFB444"),
                    "vertical"
                )
            } else if (titleLevel == LEVEL_SUPER_FUNDER) {
                titleSvga.visibility = VISIBLE
                SVGAUtil.playSvga(SVGA_PK_HEAD, -1, titleSvga)
            }
            titleView.setOnClickListener {
                PkDialogNamingView.show(context, pkUserInfo, pkStatus)
            }
        } else {
            titleView.visibility = GONE
            titleSvga.visibility = GONE
        }
    }
}