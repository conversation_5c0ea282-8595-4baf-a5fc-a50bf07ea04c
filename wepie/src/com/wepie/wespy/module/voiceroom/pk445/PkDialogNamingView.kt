package com.wepie.wespy.module.voiceroom.pk445

import android.content.Context
import android.graphics.Color
import android.text.Spannable
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnEnd
import com.huiwan.anim.SVGAUtil
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.model.voiceroom.AdvancePkConfig
import com.huiwan.decorate.DecorHeadImgView
import com.huiwan.decorate.NameTextView
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.user.LifeUserSimpleInfoCallback
import com.huiwan.user.LoginHelper
import com.huiwan.user.UserService
import com.huiwan.user.entity.UserSimpleInfo
import com.opensource.svgaplayer.SVGACallback
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser
import com.opensource.svgaplayer.SVGAVideoEntity
import com.wepie.wespy.R
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IDialogPlugin
import com.wepie.wespy.module.voiceroom.pk445.PkStatus.PK_STATUS_START
import com.wepie.wespy.module.voiceroom.util.VoiceRoomHelper
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets
import kotlin.math.atan2

class PkDialogNamingView(
    context: Context,
    val pkUserInfo: PkUserInfo,
    val pkStatus: Int,
    val isShowAnim: Boolean = false
) :
    FrameLayout(context) {

    private lateinit var pkDialogNamingLay: ConstraintLayout
    private lateinit var pkNamingSvgaView: SVGAImageView
    private lateinit var pkSphereSvgaView: SVGAImageView
    private lateinit var pkEndorseLay: ConstraintLayout
    private lateinit var pkEndorseTv: TextView
    private lateinit var pkEndorseIv: ImageView
    private lateinit var pkDialogHeadImage: DecorHeadImgView
    private lateinit var pkDialogNameTv: NameTextView
    private lateinit var pkSentValueLay: ConstraintLayout
    private lateinit var pkEndorseGoldTv: TextView
    private lateinit var pkGoldTv: TextView
    private lateinit var pkTipTv: TextView
    private lateinit var pkTitleSuperIv: ImageView
    private lateinit var pkTitleIv: ImageView

    private val SVGA_PK_SPHERE = "svga/pk/pk_naming_sphere.svga"
    val roomInfo = VoiceRoomService.getInstance().roomInfo
    private val scoreConfig = ConfigHelper.getInstance().voiceRoomConfig.advancePkConfig
    var level = LEVEL_UNDEFINED

    init {
        initView()
    }

    fun initView() {
        LayoutInflater.from(context).inflate(R.layout.voice_pk_dialog_naming_view, this)

        pkNamingSvgaView = findViewById(R.id.pk_naming_svga_view)
        pkSphereSvgaView = findViewById(R.id.pk_sphere_svga_view)
        pkDialogNamingLay = findViewById(R.id.voice_pk_dialog_naming_lay)
        pkEndorseLay = findViewById(R.id.pk_endorse_lay)
        pkEndorseTv = findViewById(R.id.pk_endorse_tv)
        pkEndorseIv = findViewById(R.id.pk_endorse_iv)
        pkDialogHeadImage = findViewById(R.id.pk_dialog_head_image)
        pkDialogNameTv = findViewById(R.id.pk_dialog_name_tv)
        pkSentValueLay = findViewById(R.id.pk_sent_value_lay)
        pkEndorseGoldTv = findViewById(R.id.pk_endorse_gold_tv)
        pkGoldTv = findViewById(R.id.pk_gold_tv)
        pkTipTv = findViewById(R.id.pk_tip_tv)
        pkTitleSuperIv = findViewById(R.id.pk_title_super_iv)
        pkTitleIv = findViewById(R.id.pk_title_iv)

        level = getNamingLevel(pkUserInfo.titleScore, scoreConfig)
        pkDialogHeadImage.showUserHeadWithDecorationCache(pkUserInfo.titleUid)
        updateUser(pkUserInfo.titleUid, pkDialogNameTv)
        pkGoldTv.text = pkUserInfo.titleScore.toString()
        if (pkStatus == PK_STATUS_START) {
            updatePkTipTv()
        } else {
            pkTipTv.visibility = GONE
        }


        if (level == LEVEL_BIG_FAN) {
            pkDialogNamingLay.setBackgroundResource(R.drawable.pk_dialog_naming_bigfan)
            pkTitleIv.setImageResource(R.drawable.pk_title_bigfan)
            pkTitleSuperIv.visibility = GONE
            pkSentValueLay.setBackgroundResource(R.drawable.pk_dialog_bigfan_gold_bg)
            pkEndorseGoldTv.setTextColor(Color.parseColor("#AFB1FF"))
        } else if (level == LEVEL_BEST_PARTNER) {
            pkDialogNamingLay.setBackgroundResource(R.drawable.pk_dialog_naming_bestpartner)
            pkTitleIv.setImageResource(R.drawable.pk_title_bestpartner)
            pkTitleSuperIv.visibility = GONE
            pkSentValueLay.setBackgroundResource(R.drawable.pk_dialog_bestpartner_gold_bg)
            pkEndorseGoldTv.setTextColor(Color.parseColor("#F9C77B"))
        } else {
            pkDialogNamingLay.setBackgroundResource(R.drawable.pk_dialog_naming_superfunder)
            pkTitleIv.setImageResource(R.drawable.pk_title_superfunder)
            pkTitleSuperIv.visibility = VISIBLE

            pkSentValueLay.setBackgroundResource(R.drawable.pk_dialog_bigfan_gold_bg)
            pkEndorseGoldTv.setTextColor(Color.parseColor("#AFB1FF"))
        }

        setOnClickListener()
    }

    private fun setOnClickListener() {
        setOnClickListener {
            VoicePluginService.getPlugin(IDialogPlugin::class.java)
                .remove(this)
        }
        pkDialogNamingLay.setOnClickListener {}
        pkDialogHeadImage.setOnClickListener {
            JumpUtil.enterUserInfoDetailFromVoiceRoom(context, pkUserInfo.titleUid, roomInfo.rid)
        }
        pkEndorseIv.setOnClickListener {
            VoicePluginService.getPlugin(IDialogPlugin::class.java)
                .remove(this)
            VoiceRoomHelper.showGiftDialog(context, roomInfo, roomInfo.rid, pkUserInfo.uid, true)
        }
        setPkEndorseClick()
    }

    private fun setPkEndorseClick() {
        var name: String
        UserService.get().getCacheSimpleUser(
            pkUserInfo.uid,
            object : LifeUserSimpleInfoCallback(this@PkDialogNamingView) {
                override fun onUserInfoSuccess(userInfo: UserSimpleInfo?) {
                    name = userInfo?.remarkName ?: ""
                    val ss =
                        SpannableString(ResUtil.getStr(R.string.pk_dialog_naming_text, name))
                    ss.setSpan(
                        ForegroundColorSpan(ResUtil.getColor(R.color.color_accent)),
                        ss.length - name.length,
                        ss.length,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    pkEndorseTv.text = ss
                }

                override fun onUserInfoFailed(description: String?) {
                    ToastUtil.show(description)
                }
            })
        pkEndorseTv.setOnClickListener {
            JumpUtil.enterUserInfoDetailFromVoiceRoom(
                context,
                pkUserInfo.uid,
                roomInfo.rid
            )
        }
    }

    private fun updatePkTipTv() {
        val list = scoreConfig.advancePkTitleList
        if (list.isEmpty() || list.size < 2) {
            return
        }
        pkTipTv.visibility = VISIBLE
        var ssb: SpannableString
        var numStr: String
        if (LoginHelper.getLoginUid() == pkUserInfo.titleUid) {
            if (level == LEVEL_SUPER_FUNDER) {
                pkTipTv.text = ResUtil.getStr(R.string.pk_dialog_naming_tip_max)
                return
            } else {
                numStr = (list[level - 1].titleMaxScore - pkUserInfo.titleScore).toString()
                ssb = SpannableString(ResUtil.getStr(R.string.pk_dialog_naming_tip_self, numStr))
            }
            val start: Int = ssb.indexOf(numStr)
            val end: Int = start + numStr.length
            ssb.setSpan(
                ForegroundColorSpan(Color.parseColor("#FFA94A")),
                start,
                end,
                Spannable.SPAN_INCLUSIVE_INCLUSIVE
            )
            pkTipTv.text = ssb

        } else {
            // 看他人时候需要知道自己目前送了多少魅力值
            PkPacketSender.pkSupportScore(
                roomInfo.rid, pkUserInfo.uid,
                object : LifeSeqCallback(this) {
                    override fun onSuccess(head: RspHeadInfo?) {
                        val selfScore =
                            (head?.message as TmpRoomPackets.AdvancePkSupportScoreRsp).score
                        numStr = (pkUserInfo.titleScore - selfScore).toString()
                        ssb = SpannableString(ResUtil.getStr(R.string.pk_dialog_naming_tip, numStr))
                        val start: Int = ssb.indexOf(numStr)
                        val end: Int = start + numStr.length
                        ssb.setSpan(
                            ForegroundColorSpan(Color.parseColor("#FFA94A")),
                            start,
                            end,
                            Spannable.SPAN_INCLUSIVE_INCLUSIVE
                        )
                        pkTipTv.text = ssb
                    }

                    override fun onFail(head: RspHeadInfo?) {
                        ToastUtil.show(head?.desc)
                    }
                }
            )
        }
    }

    private fun updateUser(uid: Int, nameTv: NameTextView) {
        pkDialogNameTv.visibility = VISIBLE
        if (uid == 0) return
        UserService.get()
            .getCacheSimpleUser(uid, object : LifeUserSimpleInfoCallback(this@PkDialogNamingView) {
                override fun onUserInfoSuccess(userInfo: UserSimpleInfo?) {
                    nameTv.setUserName(userInfo)
                }

                override fun onUserInfoFailed(description: String?) {
                    ToastUtil.show(description)
                }
            })
    }

    fun showNamingAnim(v: DecorHeadImgView, svgaView: SVGAImageView, seatNum: Int) {
        pkDialogNamingLay.background = null
        pkTitleIv.visibility = GONE
        pkTitleSuperIv.visibility = GONE
        pkEndorseLay.visibility = GONE
        pkDialogHeadImage.visibility = GONE
        pkSentValueLay.visibility = GONE
        pkDialogNameTv.visibility = GONE
        pkTipTv.visibility = GONE
        pkNamingSvgaView.visibility = VISIBLE

        val layoutParams = pkNamingSvgaView.layoutParams
        layoutParams.height = ScreenUtil.getScreenWidth() * 1624 / 750
        pkNamingSvgaView.layoutParams = layoutParams

        pkNamingSvgaView.callback = object : SVGACallback {
            override fun onPause() = Unit
            override fun onFinished() {
                showSphereAnim(v, svgaView, seatNum)
            }

            override fun onRepeat() = Unit
            override fun onStep(frame: Int, percentage: Double) = Unit
        }
        val url: String = if (level == LEVEL_BEST_PARTNER) {
            ResUtil.getStr(R.string.pk_naming_best_svga)
        } else {
            ResUtil.getStr(R.string.pk_naming_super_svga)
        }
        pkNamingSvgaView.loops = 1
        pkNamingSvgaView.clearsAfterStop = true
        SVGAUtil.playFromUrl(url, pkNamingSvgaView, null, object : SVGAParser.ParseCompletion {
            override fun onComplete(videoItem: SVGAVideoEntity) {
                PkDialogViewAnimUtil.showAnimByScale(pkEndorseLay, 560)
                PkDialogViewAnimUtil.showAnimByScale(pkDialogHeadImage, 690)
                PkDialogViewAnimUtil.showAnimByScale(pkSentValueLay, 820)
                PkDialogViewAnimUtil.hideAnimByScale(pkDialogNamingLay, 2100)
            }

            override fun onError() = Unit
        })
    }

    fun showSphereAnim(titleView: DecorHeadImgView, svgaView: SVGAImageView, seatNum: Int) {
        val startLocation = IntArray(2)
        pkSphereSvgaView.getLocationOnScreen(startLocation)
        val startX = startLocation[0] + pkSphereSvgaView.width / 2
        val startY = startLocation[1] + pkSphereSvgaView.height / 2

        val endLocation = IntArray(2)
        titleView.getLocationOnScreen(endLocation)
        val endX = endLocation[0] + titleView.width / 2
        val endY = endLocation[1] + titleView.height / 2

        SVGAUtil.playSvga(SVGA_PK_SPHERE, pkSphereSvgaView)
        val rot = Math.toDegrees(atan2((startX - endX.toDouble()), (startY - endY.toDouble())))

        pkSphereSvgaView.rotation = -rot.toFloat()
        PkDialogViewAnimUtil.showAnimByTranslation(
            pkSphereSvgaView,
            (endX.toFloat() - startX),
            (endY.toFloat() - startY)
        ).doOnEnd {
            VoicePluginService.getPlugin(IDialogPlugin::class.java)
                .remove(this@PkDialogNamingView)

            val roomInfoNew = VoiceRoomService.getInstance().roomInfo
            val seatNumNew = roomInfoNew.getSeatInfoByUid(pkUserInfo.uid)?.seat_num
            if (seatNumNew == seatNum) {
                PkDialogViewAnimUtil.showAnimByScale(titleView)
                if (level == LEVEL_BEST_PARTNER) {
                    titleView.setGradiantBorder(
                        ScreenUtil.dip2px(2f),
                        Color.parseColor("#FFF48E"),
                        Color.parseColor("#FFB444"),
                        "vertical"
                    )
                } else {
                    svgaView.visibility = VISIBLE
                    SVGAUtil.playSvga(Companion.SVGA_PK_HEAD, -1, svgaView)
                }
            }
        }
    }

    companion object {
        const val LEVEL_UNDEFINED = 0
        const val LEVEL_BIG_FAN = 1
        const val LEVEL_BEST_PARTNER = 2
        const val LEVEL_SUPER_FUNDER = 3
        const val SVGA_PK_HEAD = "svga/pk/pk_head_decoration.svga"

        fun getNamingLevel(titleScore: Int, scoreConfig: AdvancePkConfig): Int {
            val list = scoreConfig.advancePkTitleList
            if (list.isNotEmpty() && list.size > 2) {
                return if (titleScore >= list[2].titleMinScore) {
                    LEVEL_SUPER_FUNDER
                } else if (titleScore >= list[1].titleMinScore) {
                    LEVEL_BEST_PARTNER
                } else if (titleScore >= list[0].titleMinScore) {
                    LEVEL_BIG_FAN
                } else {
                    LEVEL_UNDEFINED
                }
            }
            return LEVEL_UNDEFINED
        }

        fun show(
            context: Context,
            pkUserInfo: PkUserInfo,
            pkStatus: Int
        ) {
            val pkDialogNamingView = PkDialogNamingView(context, pkUserInfo, pkStatus)
            VoicePluginService.getPlugin(IDialogPlugin::class.java).add(pkDialogNamingView)
        }

    }

}