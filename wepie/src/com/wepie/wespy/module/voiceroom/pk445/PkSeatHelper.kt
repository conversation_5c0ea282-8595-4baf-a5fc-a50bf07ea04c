package com.wepie.wespy.module.voiceroom.pk445

import android.content.Context
import androidx.core.view.children
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.dialog.HWUIDialogBuilder
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ViewUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.user.LoginHelper
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.module.voiceroom.dataservice.RoomSenderPresenter
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.RoomViewLogic
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService
import com.wepie.wespy.module.voiceroom.pk445.PkDialogNamingView.Companion.getNamingLevel
import com.wepie.wespy.module.voiceroom.seat.BaseSeatView

class PkSeatHelper(private val pkView: PkView) {
    companion object {
        private const val TAG = "Pk445-PkSeatHelper"
    }

    private var isSeatInit = false
    private lateinit var seatIntercept: ISeatIntercept

    fun initSeat(pkInfo: PkInfo) {
        HLog.d(TAG, HLog.USR, "initSeat enter, isSeatInit = $isSeatInit")
        if (isSeatInit) {
            return
        }
        val type = pkInfo.gameMod.getTypeByGameMod()
        val seatList = pkInfo.redTeamInfo.seatList + pkInfo.blueTeamInfo.seatList
        val roomInfo = VoiceRoomService.getInstance().roomInfo
        seatList.forEach { seatNum ->
            val seatView = VoicePluginService.getPlugin(ISeatGroup::class.java).getSeatView(seatNum)
                ?: return@forEach
            val itemView = PkSeatItemView(
                seatView.context,
                type,
                pkInfo.redTeamInfo.seatList.contains(seatNum)
            )
            itemView.initShow()
            seatView.viewPluginLay?.let {
                val index = it.children.indexOfFirst { child ->
                    child is PkSeatItemView
                }
                if (index >= 0) {
                    it.removeViewAt(index)
                }
                it.addView(itemView)
                seatView.post {
                    // 头像的顶部距离+边框边度-座位标签的高度
                    ViewUtil.setTopMargins(
                        itemView.getIdentityView(),
                        seatView.userInfoLay.height + ScreenUtil.dip2px(5f + 2f - 14f)
                    )

                    // 座位标签identityView设置的topMargin+座位标签的高度+距离座位标签margin
                    ViewUtil.setTopMargins(
                        itemView.getVoteView(),
                        seatView.userInfoLay.height + ScreenUtil.dip2px(5f + 2f + 6f)
                    )
                }
            }

            val seatInfo = roomInfo.getSeatInfoByNum(seatNum)
            if (seatInfo != null && (seatInfo.isEmpty || seatInfo.isUserSeat)) {
                initSeatBorder(pkInfo, seatNum, seatView)
            }
        }
        isSeatInit = true
        initSeatIntercept(pkInfo)
    }

    private fun initSeatBorder(pkInfo: PkInfo, seatNum: Int, seatView: BaseSeatView) {
        val startColor: String
        val endColor: String
        val emptyResId: Int

        if (pkInfo.redTeamInfo.seatList.contains(seatNum)) {
            startColor = "#FF87B0"
            endColor = "#FF458D"
            emptyResId = R.drawable.family_pk_seat_red_empty_iv
        } else {
            startColor = "#56DEFB"
            endColor = "#328FFF"
            emptyResId = R.drawable.family_pk_seat_blue_empty_iv
        }
        seatView.setSeatBorder(startColor, endColor, emptyResId, true)
    }

    fun unInitSeat(pkInfo: PkInfo) {
        HLog.d(TAG, HLog.USR, "unInitSeat enter, isSeatInit = $isSeatInit")
        if (!isSeatInit) {
            return
        }
        val seatList = pkInfo.redTeamInfo.seatList + pkInfo.blueTeamInfo.seatList
        val roomInfo = VoiceRoomService.getInstance().roomInfo
        seatList.forEach {
            val seatView = VoicePluginService.getPlugin(ISeatGroup::class.java).getSeatView(it)
                ?: return@forEach
            val index = seatView.viewPluginLay?.children?.indexOfFirst { child ->
                child is PkSeatItemView
            } ?: -1
            if (index >= 0) {
                seatView.viewPluginLay?.removeViewAt(index)
            }
            val seatInfo = roomInfo.getSeatInfoByNum(it)
            if (seatInfo != null && (seatInfo.isEmpty || seatInfo.isUserSeat)) {
                seatView.setSeatBorder("", "", R.drawable.room_seat_empty_ic, false)
            }
            seatView.showNameLay()
        }
        isSeatInit = false
        unInitSeatIntercept()
    }

    private fun initSeatIntercept(pkInfo: PkInfo) {
        val seatViewList = VoicePluginService.getPlugin(ISeatGroup::class.java)
            .getNormalSeatViewList()
        updateIntercept()
        seatViewList.forEach { seatView ->
            if (pkInfo.gameMod.isSinglePk()) {
                seatView.addIntercept(seatIntercept)
            }
        }
    }

    private fun updateIntercept() {
        val roomInfo = VoiceRoomService.getInstance().roomInfo
        seatIntercept = object : ISeatIntercept {
            override fun switchSeatIntercept(seatNum: Int, context: Context): Boolean {
                val pkInfo = pkView.getPkInfo()
                val seatList = pkInfo.redTeamInfo.seatList + pkInfo.blueTeamInfo.seatList
                val seatInfo = roomInfo.getSeatInfoByUid(LoginHelper.getLoginUid())
                if (seatInfo != null && seatInfo.seat_num in seatList && pkInfo.status == PkStatus.PK_STATUS_START) {
                    HWUIDialogBuilder.newBuilder(context).setSingleBtn(false)
                        .setContent(ResUtil.getStr(R.string.pk_user_leave_dialog_desc))
                        .setCanCancel(false)
                        .setDialogCallback {
                            RoomViewLogic.sit(
                                context,
                                roomInfo.rid,
                                seatNum
                            )
                        }.show()
                    return true
                } else {
                    return false
                }
            }

            override fun liftUpUserIntercept(
                seatNum: Int,
                seatUid: Int,
                context: Context,
                dismissDialog: () -> Unit
            ): Boolean {
                val pkInfo = pkView.getPkInfo()
                val seatList = pkInfo.redTeamInfo.seatList + pkInfo.blueTeamInfo.seatList
                if (seatNum in seatList && pkInfo.status == PkStatus.PK_STATUS_START) {
                    HWUIDialogBuilder.newBuilder(context).setSingleBtn(false)
                        .setContent(R.string.pk_kick_user_dialog_desc)
                        .setCanCancel(true)
                        .setDialogCallback(object : HWUIDialogBuilder.DialogCallback {
                            override fun onClickSure() {
                                RoomSenderPresenter.forceUp(
                                    roomInfo.rid,
                                    seatNum,
                                    seatUid
                                )
                                dismissDialog.invoke()
                            }

                            override fun onClickCancel() = Unit
                        }).show()
                    return true
                } else {
                    return false
                }
            }

            override fun standUpIntercept(
                seatNum: Int,
                context: Context,
                dismissDialog: () -> Unit
            ): Boolean {
                val pkInfo = pkView.getPkInfo()
                val seatList = pkInfo.redTeamInfo.seatList + pkInfo.blueTeamInfo.seatList
                if (seatNum in seatList && pkInfo.status == PkStatus.PK_STATUS_START) {
                    HWUIDialogBuilder.newBuilder(context).setSingleBtn(false)
                        .setContent(R.string.pk_user_leave_dialog_desc)
                        .setCanCancel(true)
                        .setDialogCallback(object : HWUIDialogBuilder.DialogCallback {
                            override fun onClickSure() {
                                RoomViewLogic.unseat(context, roomInfo.rid, seatNum)
                                dismissDialog.invoke()
                            }

                            override fun onClickCancel() = Unit
                        }).show()
                    return true
                } else {
                    return false
                }
            }
        }
    }

    private fun unInitSeatIntercept() {
        if (!::seatIntercept.isInitialized) {
            return
        }
        val seatViewList = VoicePluginService.getPlugin(ISeatGroup::class.java)
            .getNormalSeatViewList()
        seatViewList.forEach { seatView ->
            seatView.removeIntercept(seatIntercept)
        }
    }

    fun updateSeatVote(pkInfo: PkInfo) {
        val pkState = pkInfo.status
        val seatList = pkInfo.redTeamInfo.seatList + pkInfo.blueTeamInfo.seatList
        seatList.forEach { seatNum ->
            val seatView = VoicePluginService.getPlugin(ISeatGroup::class.java).getSeatView(seatNum)
                ?: return@forEach
            (seatView.viewPluginLay?.children?.firstOrNull { it is PkSeatItemView } as? PkSeatItemView)?.let {
                when (pkState) {
                    PkStatus.PK_STATUS_START -> {
                        val seatUid = VoiceRoomService.getInstance().roomInfo
                            .getSeatInfoByNum(seatNum).uid
                        seatView.post {
                            it.showVoteView(seatUid, !pkView.isSupportedTvShow()) { isRed ->
                                hideVote(pkInfo)
                                pkView.showSupportedTv(isRed)
                            }
                            if (pkInfo.gameMod.isUserPk()) {
                                ViewUtil.setViewWidth(it.getVoteView(), seatView.viewPluginLay!!.width)
                            }
                            if (it.isVoteViewShow()) {
                                seatView.hideNameLay()
                            } else {
                                seatView.showNameLay()
                            }
                        }
                    }

                    else -> {
                        it.hideVoteView()
                        seatView.showNameLay()
                    }
                }
            }
        }
    }

    private fun hideVote(pkInfo: PkInfo) {
        val seatList = pkInfo.redTeamInfo.seatList + pkInfo.blueTeamInfo.seatList
        seatList.forEach { seatNum ->
            val seatView = VoicePluginService.getPlugin(ISeatGroup::class.java).getSeatView(seatNum)
                ?: return@forEach
            (seatView.viewPluginLay?.children?.firstOrNull { it is PkSeatItemView } as? PkSeatItemView)?.let {
                it.hideVoteView()
                seatView.showNameLay()
            }
        }
    }

    fun updateSeatScore(pkInfo: PkInfo) {
        val roomInfo = VoiceRoomService.getInstance().roomInfo
        val scoreConfig = ConfigHelper.getInstance().voiceRoomConfig.advancePkConfig

        val seatList = pkInfo.redTeamInfo.seatList + pkInfo.blueTeamInfo.seatList
        seatList.forEach { seatNum ->
            val seatView = VoicePluginService.getPlugin(ISeatGroup::class.java).getSeatView(seatNum)
                ?: return@forEach
            val seatUid = roomInfo.getSeatInfoByNum(seatNum).uid
            (seatView.viewPluginLay?.children?.firstOrNull { it is PkSeatItemView } as? PkSeatItemView)?.let {
                if (pkInfo.gameMod.isGiftPk()) {
                    if (seatUid <= 0) {
                        it.showDefaultPkLabel()
                    } else {
                        val pkUserInfo = pkInfo.getPkUserInfoByUid(seatUid)
                        val inTeam = when (pkUserInfo.teamId) {
                            PkUserInfo.RED_TEAM_ID -> {
                                pkInfo.redTeamInfo.seatList.contains(seatNum)
                            }

                            PkUserInfo.BLUE_TEAM_ID -> {
                                pkInfo.blueTeamInfo.seatList.contains(seatNum)
                            }

                            else -> {
                                false
                            }
                        }
                        val showScore = if (inTeam) {
                            pkUserInfo.score
                        } else 0
                        it.updateGiftPkScore(
                            showScore,
                            pkUserInfo.getBackgroundDrawable(
                                scoreConfig,
                                showScore,
                                pkInfo.redTeamInfo.seatList.contains(seatNum)
                            ),
                            pkUserInfo.getScoreIcon(
                                scoreConfig,
                                showScore,
                                pkInfo.redTeamInfo.seatList.contains(seatNum)
                            )
                        )
                    }
                } else if (pkInfo.gameMod.isUserPk()) {
                    if (seatUid <= 0) {
                        it.showDefaultPkLabel()
                    } else {
                        it.hideDefaultPkLabel()
                    }
                }
            }
        }
    }

    fun showNamingViewChange(pkStatus: Int, changeScore: PkPushChangeScore) {
        val scoreConfig = ConfigHelper.getInstance().voiceRoomConfig.advancePkConfig
        val roomInfo = VoiceRoomService.getInstance().roomInfo
        val listUsers = changeScore.scoreChangeUsers
        listUsers.forEach { pkUserInfo ->
            val seatInfo = roomInfo.getSeatInfoByUid(pkUserInfo.uid)
            if (seatInfo != null) {
                val seatNum = seatInfo.seat_num
                val seatView = VoicePluginService.getPlugin(ISeatGroup::class.java)
                    .getSeatView(seatNum) ?: return@forEach
                (seatView.viewPluginLay?.children?.firstOrNull { it is PkSeatItemView } as? PkSeatItemView)?.let {
                    it.showTitleViewChange(
                        pkUserInfo,
                        getNamingLevel(pkUserInfo.titleScore, scoreConfig),
                        pkStatus,
                        it.getTitleView(),
                        it.getTitleSvga(),
                        seatNum
                    )
                }
            }
        }
    }

    fun showNamingViewSync(pkInfo: PkInfo) {
        if (!pkInfo.gameMod.isGiftPk()) {
            return
        }
        val scoreConfig = ConfigHelper.getInstance().voiceRoomConfig.advancePkConfig
        val roomInfo = VoiceRoomService.getInstance().roomInfo
        val seatList = pkInfo.redTeamInfo.seatList + pkInfo.blueTeamInfo.seatList
        seatList.forEach { seatNum ->
            val seatView = VoicePluginService.getPlugin(ISeatGroup::class.java).getSeatView(seatNum)
                ?: return@forEach
            val pkUserInfo = pkInfo.getPkUserInfoByUid(roomInfo.getSeatInfoByNum(seatNum).uid)
            (seatView.viewPluginLay?.children?.firstOrNull { it is PkSeatItemView } as? PkSeatItemView)?.let {
                val inTeam = when (pkUserInfo.teamId) {
                    PkUserInfo.RED_TEAM_ID -> {
                        pkInfo.redTeamInfo.seatList.contains(seatNum)
                    }

                    PkUserInfo.BLUE_TEAM_ID -> {
                        pkInfo.blueTeamInfo.seatList.contains(seatNum)
                    }

                    else -> {
                        false
                    }
                }
                val titleScore = if (inTeam) {
                    pkUserInfo.titleScore
                } else 0
                it.showTitleViewSync(
                    pkUserInfo,
                    getNamingLevel(titleScore, scoreConfig),
                    pkInfo.status
                )
            }
        }
    }
}
