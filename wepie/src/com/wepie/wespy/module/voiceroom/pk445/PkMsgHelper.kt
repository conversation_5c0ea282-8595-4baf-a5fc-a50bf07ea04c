package com.wepie.wespy.module.voiceroom.pk445

import android.content.Context
import com.google.gson.annotations.SerializedName
import com.huiwan.base.util.JsonUtil

object PkMsgHelper {
    /**
     * 打开pk结果弹窗
     */
    @JvmStatic
    fun showPkResult(context: Context, resultJson: String) {
        val resultInfo = JsonUtil.fromJson(resultJson, PkMsgResultInfo::class.java)
        PkDialogResultView.show(context, resultInfo.pkMode, resultInfo.pkResult)
    }
}

data class PkMsgResultInfo(
    @SerializedName("pk_mod") val pkMode: PkGameMod = PkGameMod(),
    @SerializedName("data") val pkResult: PkPushResult = PkPushResult()
)