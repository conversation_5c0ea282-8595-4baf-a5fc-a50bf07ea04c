package com.wepie.wespy.module.voiceroom.pk445

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.os.CountDownTimer
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnEnd
import com.huiwan.anim.SVGAUtil
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ColorUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.StringUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.decorate.DecorHeadImgView
import com.huiwan.decorate.NameTextView
import com.huiwan.user.LifeUserSimpleInfoCallback
import com.huiwan.user.UserService
import com.huiwan.user.entity.UserSimpleInfo
import com.opensource.svgaplayer.SVGAImageView
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IDialogPlugin


class PkDialogResultView(
    context: Context,
    val pkGameMod: PkGameMod,
    val pkResult: PkPushResult,
    val isShowTimer: Boolean
) : FrameLayout(context) {

    private lateinit var pkDialogResultLay: ConstraintLayout
    private lateinit var pkDialogResultClose: TextView
    private lateinit var pkProgressBar: PkProgressBar
    private lateinit var pkDialogCongratulationTv: TextView

    private lateinit var pkResultRankIv: ImageView
    private lateinit var pkDialogHeadImage: DecorHeadImgView
    private lateinit var pkDialogHeadImage2: DecorHeadImgView
    private lateinit var pkDialogHeadImage3: DecorHeadImgView
    private lateinit var pkDialogNameTv: NameTextView
    private lateinit var pkDialogNameTv2: NameTextView
    private lateinit var pkDialogNameTv3: NameTextView

    private lateinit var pkDialogWinIv: ImageView

    private lateinit var pkDialogSupporterHead: DecorHeadImgView
    private lateinit var pkDialogSupporterHead2: DecorHeadImgView
    private lateinit var pkDialogSupporterHead3: DecorHeadImgView
    private lateinit var pkSupporterNameTv: NameTextView
    private lateinit var pkSupporterNameTv2: NameTextView
    private lateinit var pkSupporterNameTv3: NameTextView

    private lateinit var identityView: ConstraintLayout
    private lateinit var identityIcon: ImageView
    private lateinit var identityTv: TextView

    private lateinit var identityView2: ConstraintLayout
    private lateinit var identityIcon2: ImageView
    private lateinit var identityTv2: TextView

    private lateinit var identityView3: ConstraintLayout
    private lateinit var identityIcon3: ImageView
    private lateinit var identityTv3: TextView


    private var countDownTimer: CountDownTimer? = null
    private lateinit var pkDialogAnimView: SVGAImageView
    private val SVGA_PK_RIBBON = "svga/pk/pk_dialog_result.svga"
    val roomInfo = VoiceRoomService.getInstance().roomInfo
    private val scoreConfig = ConfigHelper.getInstance().voiceRoomConfig.advancePkConfig
    private var isRed = false

    init {
        initView()
    }

    fun initView() {
        if (pkGameMod.pkMode == PkGameMod.PK_MOD_BY_USER_NUM) {
            LayoutInflater.from(context).inflate(R.layout.voice_pk_dialog_result_view, this)
        } else {
            LayoutInflater.from(context).inflate(R.layout.voice_pk_dialog_result_view_by_gift, this)
            pkDialogHeadImage2 = findViewById(R.id.pk_dialog_head_image2)
            pkDialogHeadImage3 = findViewById(R.id.pk_dialog_head_image3)
            pkDialogNameTv2 = findViewById(R.id.pk_dialog_name_tv2)
            pkDialogNameTv3 = findViewById(R.id.pk_dialog_name_tv3)

            pkDialogSupporterHead = findViewById(R.id.pk_dialog_supporter_head)
            pkDialogSupporterHead2 = findViewById(R.id.pk_dialog_supporter_head2)
            pkDialogSupporterHead3 = findViewById(R.id.pk_dialog_supporter_head3)
            pkSupporterNameTv = findViewById(R.id.pk_supporter_name_tv)
            pkSupporterNameTv2 = findViewById(R.id.pk_supporter_name_tv2)
            pkSupporterNameTv3 = findViewById(R.id.pk_supporter_name_tv3)

            identityView = findViewById(R.id.pk_head_identity_view)
            identityIcon = findViewById(R.id.pk_head_identity_icon)
            identityTv = findViewById(R.id.pk_head_identity_tv)

            identityView2 = findViewById(R.id.pk_head_identity_view2)
            identityIcon2 = findViewById(R.id.pk_head_identity_icon2)
            identityTv2 = findViewById(R.id.pk_head_identity_tv2)

            identityView3 = findViewById(R.id.pk_head_identity_view3)
            identityIcon3 = findViewById(R.id.pk_head_identity_icon3)
            identityTv3 = findViewById(R.id.pk_head_identity_tv3)
        }

        pkDialogResultLay = findViewById(R.id.voice_pk_dialog_result_lay)
        pkDialogResultClose = findViewById(R.id.pk_dialog_result_close)
        pkProgressBar = findViewById(R.id.pk_progress_bar)
        pkDialogCongratulationTv = findViewById(R.id.pk_dialog_congratulation_tv)
        pkResultRankIv = findViewById(R.id.voice_pk_result_rank_iv)
        pkDialogHeadImage = findViewById(R.id.pk_dialog_head_image)
        pkDialogNameTv = findViewById(R.id.pk_dialog_name_tv)
        pkDialogWinIv = findViewById(R.id.pk_dialog_win_iv)
        pkDialogAnimView = findViewById(R.id.pk_dialog_anim_view)

        if (pkResult.winners.isNotEmpty()) {
            isRed = pkResult.winners[0].teamId == TEAM_RED
        }

        if (pkGameMod.pkMode == PkGameMod.PK_MOD_BY_USER_NUM) {
            updateSingleViewByNum(pkResult)
            pkDialogWinIv.setImageResource(R.drawable.voice_pk_result_win)
        } else {
            if (pkGameMod.pkUserMod == PkGameMod.PK_USER_MOD_SINGLE) {
                updateSingleView(pkResult)
                pkDialogWinIv.setImageResource(R.drawable.voice_pk_result_win)
            } else {
                updateMutliView(pkResult)
                pkDialogWinIv.setImageResource(R.drawable.voice_pk_result_mvp)
            }
        }
        pkProgressBar.showViewWithoutAnim(pkResult.redScore, pkResult.blueScore)
        updateCongratulationTv()
        pkDialogResultClose.setOnClickListener {
            VoicePluginService.getPlugin(IDialogPlugin::class.java)
                .remove(this)
        }

        if (isShowTimer) {
            countDownTimer?.cancel()
            countDownTimer = object : CountDownTimer(10 * 1000, 1000) {
                override fun onTick(p0: Long) {
                    pkDialogResultClose.text =
                        ResUtil.getStr(R.string.pk_dialog_result_close, (p0 / 1000))

                }

                override fun onFinish() {
                    VoicePluginService.getPlugin(IDialogPlugin::class.java)
                        .remove(this@PkDialogResultView)
                }

            }
        } else {
            pkDialogResultClose.text = ResUtil.getStr(R.string.pk_dialog_result_close_no_timer)
        }
        setOnClickListener {}
    }

    private fun updateCongratulationTv() {
        var team: String
        var teamColor: String
        if (isRed) {
            team = ResUtil.getStr(R.string.pk_dialog_result_red)
            teamColor = "#FE6484"
            pkResultRankIv.setImageResource(R.drawable.voice_pk_result_red_bg)
        } else {
            team = ResUtil.getStr(R.string.pk_dialog_result_blue)
            teamColor = ColorUtil.colorToString(R.color.color_accent)
            pkResultRankIv.setImageResource(R.drawable.voice_pk_result_blue_bg)

        }
        val resultText = SpannableString(ResUtil.getStr(R.string.pk_dialog_result_text, team))
        resultText.setSpan(
            ForegroundColorSpan(Color.parseColor(teamColor)),
            resultText.length - team.length,
            resultText.length,
            Spannable.SPAN_INCLUSIVE_INCLUSIVE
        )
        pkDialogCongratulationTv.text = resultText
    }

    private fun updateSingleViewByNum(pkResult: PkPushResult) {
        if (pkResult.winners.isNotEmpty()){
            pkDialogHeadImage.showUserHeadWithDecoration(pkResult.winners[0].uid)
            setOnClick(pkDialogHeadImage, pkResult.winners[0].uid)
            setHeadBorder(pkDialogHeadImage)
            updateUser(pkResult.winners[0].uid, pkDialogNameTv)
        }
    }

    private fun updateSingleView(pkResult: PkPushResult) {
        pkDialogHeadImage2.visibility= GONE
        pkDialogHeadImage3.visibility= GONE
        pkDialogNameTv2.visibility=GONE
        pkDialogNameTv3.visibility=GONE
        identityView2.visibility = GONE
        identityView3.visibility = GONE

        if (pkResult.winners.isNotEmpty()){
            pkDialogHeadImage.showUserHeadWithDecoration(pkResult.winners[0].uid)
            setOnClick(pkDialogHeadImage, pkResult.winners[0].uid)
            setHeadBorder(pkDialogHeadImage)
            updateUser(pkResult.winners[0].uid, pkDialogNameTv)
            //设置分数
            updateScore(identityTv, identityView, identityIcon, pkResult.winners[0])
        }

        if (pkResult.supportUids.isNotEmpty()) {
            if (pkResult.supportUids.size > 2) {
                pkDialogSupporterHead.showUserHeadWithDecoration(pkResult.supportUids[0])
                pkDialogSupporterHead2.showUserHeadWithDecoration(pkResult.supportUids[1])
                pkDialogSupporterHead3.showUserHeadWithDecoration(pkResult.supportUids[2])
                setOnClick(pkDialogSupporterHead, pkResult.supportUids[0])
                setOnClick(pkDialogSupporterHead2, pkResult.supportUids[1])
                setOnClick(pkDialogSupporterHead3, pkResult.supportUids[2])
                updateUser(pkResult.supportUids[0], pkSupporterNameTv)
                updateUser(pkResult.supportUids[1], pkSupporterNameTv2)
                updateUser(pkResult.supportUids[2], pkSupporterNameTv3)
            } else if (pkResult.supportUids.size > 1) {
                pkDialogSupporterHead.showUserHeadWithDecoration(pkResult.supportUids[0])
                pkDialogSupporterHead2.showUserHeadWithDecoration(pkResult.supportUids[1])
                setOnClick(pkDialogSupporterHead, pkResult.supportUids[0])
                setOnClick(pkDialogSupporterHead2, pkResult.supportUids[1])
                pkDialogSupporterHead3.setImageResource(R.drawable.default_head_icon)
                updateUser(pkResult.supportUids[0], pkSupporterNameTv)
                updateUser(pkResult.supportUids[1], pkSupporterNameTv2)
                pkSupporterNameTv3.text = ResUtil.getStr(R.string.waiting_for_user)
            } else {
                pkDialogSupporterHead.showUserHeadWithDecoration(pkResult.supportUids[0])
                setOnClick(pkDialogSupporterHead, pkResult.supportUids[0])
                pkDialogSupporterHead2.setImageResource(R.drawable.default_head_icon)
                pkDialogSupporterHead3.setImageResource(R.drawable.default_head_icon)
                updateUser(pkResult.supportUids[0], pkSupporterNameTv)
                pkSupporterNameTv2.text = ResUtil.getStr(R.string.waiting_for_user)
                pkSupporterNameTv3.text = ResUtil.getStr(R.string.waiting_for_user)
            }
        } else {
            pkDialogSupporterHead.setImageResource(R.drawable.default_head_icon)
            pkDialogSupporterHead2.setImageResource(R.drawable.default_head_icon)
            pkDialogSupporterHead3.setImageResource(R.drawable.default_head_icon)
            pkSupporterNameTv.text = ResUtil.getStr(R.string.waiting_for_user)
            pkSupporterNameTv2.text = ResUtil.getStr(R.string.waiting_for_user)
            pkSupporterNameTv3.text = ResUtil.getStr(R.string.waiting_for_user)
        }
    }

    private fun updateMutliView(pkResult: PkPushResult) {
        pkDialogHeadImage2.visibility = VISIBLE
        pkDialogHeadImage3.visibility = VISIBLE
        pkDialogNameTv2.visibility = VISIBLE
        pkDialogNameTv3.visibility = VISIBLE

        if (pkResult.winners.isNotEmpty()) {
            if (pkResult.winners.size > 2) {
                pkDialogHeadImage.showUserHeadWithDecoration(pkResult.winners[0].uid)
                pkDialogHeadImage2.showUserHeadWithDecoration(pkResult.winners[1].uid)
                pkDialogHeadImage3.showUserHeadWithDecoration(pkResult.winners[2].uid)
                setOnClick(pkDialogHeadImage, pkResult.winners[0].uid)
                setOnClick(pkDialogHeadImage2, pkResult.winners[1].uid)
                setOnClick(pkDialogHeadImage3, pkResult.winners[2].uid)
                setHeadBorder(pkDialogHeadImage)
                setHeadBorder(pkDialogHeadImage2)
                setHeadBorder(pkDialogHeadImage3)
                updateUser(pkResult.winners[0].uid, pkDialogNameTv)
                updateUser(pkResult.winners[1].uid, pkDialogNameTv2)
                updateUser(pkResult.winners[2].uid, pkDialogNameTv3)
                //设置分数
                updateScore(identityTv, identityView, identityIcon, pkResult.winners[0])
                updateScore(identityTv2, identityView2, identityIcon2, pkResult.winners[1])
                updateScore(identityTv3, identityView3, identityIcon3, pkResult.winners[2])
            } else if (pkResult.winners.size > 1) {
                pkDialogHeadImage.showUserHeadWithDecoration(pkResult.winners[0].uid)
                pkDialogHeadImage2.showUserHeadWithDecoration(pkResult.winners[1].uid)
                setOnClick(pkDialogHeadImage, pkResult.winners[0].uid)
                setOnClick(pkDialogHeadImage2, pkResult.winners[1].uid)
                setHeadBorder(pkDialogHeadImage)
                setHeadBorder(pkDialogHeadImage2)
                pkDialogHeadImage3.visibility = GONE
                updateUser(pkResult.winners[0].uid, pkDialogNameTv)
                updateUser(pkResult.winners[1].uid, pkDialogNameTv2)
                pkDialogNameTv3.visibility = GONE
                //设置分数
                updateScore(identityTv, identityView, identityIcon, pkResult.winners[0])
                updateScore(identityTv2, identityView2, identityIcon2, pkResult.winners[1])
                identityView3.visibility = GONE
            } else {
                pkDialogHeadImage.showUserHeadWithDecoration(pkResult.winners[0].uid)
                setOnClick(pkDialogHeadImage, pkResult.winners[0].uid)
                setHeadBorder(pkDialogHeadImage)
                pkDialogHeadImage2.visibility = GONE
                pkDialogHeadImage3.visibility = GONE
                updateUser(pkResult.winners[0].uid, pkDialogNameTv)
                pkDialogNameTv2.visibility = GONE
                pkDialogNameTv3.visibility = GONE
                //设置分数
                updateScore(identityTv, identityView, identityIcon, pkResult.winners[0])
                identityView2.visibility = GONE
                identityView3.visibility = GONE
            }
        }

        if (pkResult.supportUids.isNotEmpty()) {
            if (pkResult.supportUids.size > 2) {
                pkDialogSupporterHead.showUserHeadWithDecoration(pkResult.supportUids[0])
                pkDialogSupporterHead2.showUserHeadWithDecoration(pkResult.supportUids[1])
                pkDialogSupporterHead3.showUserHeadWithDecoration(pkResult.supportUids[2])
                setOnClick(pkDialogSupporterHead, pkResult.supportUids[0])
                setOnClick(pkDialogSupporterHead2, pkResult.supportUids[1])
                setOnClick(pkDialogSupporterHead3, pkResult.supportUids[2])
                updateUser(pkResult.supportUids[0], pkSupporterNameTv)
                updateUser(pkResult.supportUids[1], pkSupporterNameTv2)
                updateUser(pkResult.supportUids[2], pkSupporterNameTv3)
            } else if (pkResult.supportUids.size > 1) {
                pkDialogSupporterHead.showUserHeadWithDecoration(pkResult.supportUids[0])
                pkDialogSupporterHead2.showUserHeadWithDecoration(pkResult.supportUids[1])
                setOnClick(pkDialogSupporterHead, pkResult.supportUids[0])
                setOnClick(pkDialogSupporterHead2, pkResult.supportUids[1])
                pkDialogSupporterHead3.setImageResource(R.drawable.default_head_icon)
                updateUser(pkResult.supportUids[0], pkSupporterNameTv)
                updateUser(pkResult.supportUids[1], pkSupporterNameTv2)
                pkSupporterNameTv3.text = ResUtil.getStr(R.string.waiting_for_user)
            } else {
                pkDialogSupporterHead.showUserHeadWithDecoration(pkResult.supportUids[0])
                setOnClick(pkDialogSupporterHead, pkResult.supportUids[0])
                pkDialogSupporterHead2.setImageResource(R.drawable.default_head_icon)
                pkDialogSupporterHead3.setImageResource(R.drawable.default_head_icon)
                updateUser(pkResult.supportUids[0], pkSupporterNameTv)
                pkSupporterNameTv2.text = ResUtil.getStr(R.string.waiting_for_user)
                pkSupporterNameTv3.text = ResUtil.getStr(R.string.waiting_for_user)
            }
        } else {
            pkDialogSupporterHead.setImageResource(R.drawable.default_head_icon)
            pkDialogSupporterHead2.setImageResource(R.drawable.default_head_icon)
            pkDialogSupporterHead3.setImageResource(R.drawable.default_head_icon)
            pkSupporterNameTv.text = ResUtil.getStr(R.string.waiting_for_user)
            pkSupporterNameTv2.text = ResUtil.getStr(R.string.waiting_for_user)
            pkSupporterNameTv3.text = ResUtil.getStr(R.string.waiting_for_user)
        }
    }

    fun showSvgaAnim(){
        pkDialogAnimView.visibility = VISIBLE
        SVGAUtil.playSvga(SVGA_PK_RIBBON, pkDialogAnimView)
    }


    fun setOnClick(headImgView: DecorHeadImgView,uid: Int) {
        headImgView.setOnClickListener {
            JumpUtil.enterUserInfoDetailFromVoiceRoom(context, uid, roomInfo.rid)
        }
    }

    private fun updateUser(uid: Int, nameTv: NameTextView) {
        nameTv.visibility = VISIBLE
        if (uid == 0) return
        UserService.get()
            .getCacheSimpleUser(uid, object : LifeUserSimpleInfoCallback(this@PkDialogResultView) {
                override fun onUserInfoSuccess(userInfo: UserSimpleInfo?) {
                    nameTv.setUserName(userInfo)
                }

                override fun onUserInfoFailed(description: String?) {
                    ToastUtil.show(description)
                }
            })
    }

    private fun setHeadBorder(headImgView: DecorHeadImgView) {
        val startColor: String
        val endColor: String
        if (isRed) {
            startColor = "#FF87B0"
            endColor = "#FF458D"
        } else {
            startColor = "#56DEFB"
            endColor = "#328FFF"
        }
        headImgView.setGradiantBorder(
            ScreenUtil.dip2px(2f),
            Color.parseColor(startColor),
            Color.parseColor(endColor),
            "vertical"
        )
    }

    private fun updateScore(
        identityTv: TextView,
        identityView: ConstraintLayout,
        identityIcon: ImageView,
        pkUserInfo: PkUserInfo
    ) {
        identityView.visibility = VISIBLE
        updateGiftPkScore(
            identityTv, identityView, identityIcon,
            pkUserInfo.score, pkUserInfo.getBackgroundDrawable(
                scoreConfig,
                pkUserInfo.score,
                isRed
            ), pkUserInfo.getScoreIcon(
                scoreConfig,
                pkUserInfo.score,
                isRed
            )
        )
    }

    private fun updateGiftPkScore(
        identityTv: TextView,
        identityView: ConstraintLayout,
        identityIcon: ImageView,
        score: Int,
        drawable: Drawable?,
        iconUrl: String
    ) {
        identityTv.text = StringUtil.formatPointOneNum(score)
        drawable?.let {
            identityView.background = it
        }
        if (iconUrl.isNotEmpty()) {
            WpImageLoader.load(iconUrl, identityIcon)
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (isShowTimer) {
            countDownTimer?.start()
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        if (isShowTimer) {
            countDownTimer?.cancel()
        }
    }

    companion object {
        const val TEAM_RED = 1
        const val TEAM_BLUE = 2
        fun show(
            context: Context,
            gameMod: PkGameMod,
            result: PkPushResult,
            isShowTimer: Boolean = false
        ) {
            VoicePluginService.getPlugin(IDialogPlugin::class.java).removeIf {
                it is PkDialogNamingView
            }
            val pkDialogResultView = PkDialogResultView(context, gameMod, result, isShowTimer)
            VoicePluginService.getPlugin(IDialogPlugin::class.java).add(pkDialogResultView)
            PkDialogViewAnimUtil.showAnimByScale(pkDialogResultView).doOnEnd {
                pkDialogResultView.showSvgaAnim()
            }
        }
    }


}