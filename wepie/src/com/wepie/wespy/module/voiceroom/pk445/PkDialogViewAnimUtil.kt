package com.wepie.wespy.module.voiceroom.pk445

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.view.View
import android.view.animation.PathInterpolator
import androidx.core.animation.doOnEnd
import androidx.core.view.isVisible

object PkDialogViewAnimUtil {
    fun showAnimByScale(view: View, delay: Long = 0): Animator {
        view.isVisible = true
        view.scaleX = 0f
        view.scaleY = 0f

        //先放大
        val scaleXAnimator = ObjectAnimator.ofFloat(view, "scaleX", 0.3f, 1.2f)
        val scaleYAnimator = ObjectAnimator.ofFloat(view, "scaleY", 0.3f, 1.2f)
        val scaleAnimatorSet = AnimatorSet()
        scaleAnimatorSet.setDuration(160)
        scaleAnimatorSet.interpolator = PathInterpolator(0.12f,0f,0f,1f)
        scaleAnimatorSet.playTogether(scaleXAnimator, scaleYAnimator)

        // 后缩小
        val reverseScaleXAnimator = ObjectAnimator.ofFloat(view, "scaleX", 1.2f, 1f)
        val reverseScaleYAnimator = ObjectAnimator.ofFloat(view, "scaleY", 1.2f, 1f)
        val reverseScaleAnimatorSet = AnimatorSet()
        reverseScaleAnimatorSet.setDuration(340)
        reverseScaleAnimatorSet.interpolator = PathInterpolator(0.33f,0f,0.67f,1f)
        reverseScaleAnimatorSet.playTogether(reverseScaleXAnimator, reverseScaleYAnimator)

        val animatorSet = AnimatorSet()
        animatorSet.playSequentially(scaleAnimatorSet, reverseScaleAnimatorSet)

        animatorSet.startDelay = delay
        animatorSet.doOnEnd {
            view.scaleX = 1f
            view.scaleY = 1f
        }
        animatorSet.start()
        return animatorSet
    }
    fun hideAnimByScale(view: View, delay: Long = 0): Animator {
        view.isVisible = true
        view.scaleX = 1f
        view.scaleY = 1f

        // 缩小
        val reverseScaleXAnimator = ObjectAnimator.ofFloat(view, "scaleX", 1f, 0f)
        val reverseScaleYAnimator = ObjectAnimator.ofFloat(view, "scaleY", 1f, 0f)
        val reverseScaleAnimatorSet = AnimatorSet()
        reverseScaleAnimatorSet.setDuration(260)
        reverseScaleAnimatorSet.playTogether(reverseScaleXAnimator, reverseScaleYAnimator)

        reverseScaleAnimatorSet.startDelay = delay
        reverseScaleAnimatorSet.doOnEnd {
            view.scaleX = 0f
            view.scaleY = 0f
        }
        reverseScaleAnimatorSet.start()
        return reverseScaleAnimatorSet
    }

    //位移动画
    fun showAnimByTranslation(view: View, dx: Float, dy: Float, delay: Long = 0): Animator {
        view.isVisible = true

        // 位移
        val translationXAnimator = ObjectAnimator.ofFloat(view, "translationX", 0f, dx)
        val translationYAnimator = ObjectAnimator.ofFloat(view, "translationY", 0f, dy)
        // 缩小
        val reverseScaleXAnimator = ObjectAnimator.ofFloat(view, "scaleX", 1f, 0.5f)
        val reverseScaleYAnimator = ObjectAnimator.ofFloat(view, "scaleY", 1f, 0.5f)
        val animatorSet = AnimatorSet()
        animatorSet.setDuration(500)
        animatorSet.playTogether(translationXAnimator, translationYAnimator,reverseScaleXAnimator,reverseScaleYAnimator)

        animatorSet.startDelay = delay
        animatorSet.start()
        return animatorSet
    }
}