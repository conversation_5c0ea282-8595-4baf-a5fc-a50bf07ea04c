package com.wepie.wespy.module.voiceroom.pk445

import android.view.View
import com.wepie.wespy.module.voiceroom.IPluginTool
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IDialogPlugin

class DialogDelegateImpl(
    private val pluginTool: IPluginTool<IDialogPlugin>
) : IDialogPlugin by pluginTool.proxy() {

    override fun add(view: View) {
        pluginTool.loadView(true)
        pluginTool.proxy().add(view)
    }

    override fun add(view: View, index: Int) {
        pluginTool.loadView(true)
        pluginTool.proxy().add(view, index)
    }

    override fun remove(view: View) {
        pluginTool.proxy().remove(view)
        if (!has()) {
            pluginTool.loadView(false)
        }
    }

    override fun removeIf(filter: (view: View) -> Boolean) {
        pluginTool.proxy().removeIf(filter)
        if (!has()) {
            pluginTool.loadView(false)
        }
    }

    override fun getChildCount(): Int {
        return pluginTool.proxy().getChildCount()
    }

    override fun getChildAt(index: Int): View? {
        return pluginTool.proxy().getChildAt(index)
    }
}