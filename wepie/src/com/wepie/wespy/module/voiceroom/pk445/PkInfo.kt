package com.wepie.wespy.module.voiceroom.pk445

import android.graphics.drawable.Drawable
import com.google.gson.annotations.SerializedName
import com.huiwan.base.util.ScreenUtil
import com.huiwan.configservice.model.voiceroom.AdvancePkConfig
import com.huiwan.widget.GradientDrawableWithStroke
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.AdvancePkInfo
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.AdvancePkMod
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.AdvancePkStatus

data class PkInfo(
    var version: Int = 0, // 版本信息
    var gameMod: PkGameMod = PkGameMod(), // 模式相关
    var status: Int = PkStatus.PK_STATUS_UNKNOWN, // PK状态
    var startTime: Long = -1, // pk开始时间
    var endTime: Long = -1, // pk结束时间
    val redTeamInfo: PkTeamInfo = PkTeamInfo(),
    val blueTeamInfo: PkTeamInfo = PkTeamInfo(),
    val userInfo: MutableList<PkUserInfo> = mutableListOf(), // 用户得分信息
    var voteForUid: Int = -1,
) {
    var serverVersion = 0 // 记录服务器最新push的版本号，用来校验客户端主动sync的数据是否可用(sync version >= pushVersion)

    companion object {
        fun parse(advancePkInfo: AdvancePkInfo): PkInfo {
            return PkInfo(
                advancePkInfo.pkVersion,
                PkGameMod.parse(advancePkInfo.gameMod),
                PkStatus.parse(advancePkInfo.state),
                advancePkInfo.startTime,
                advancePkInfo.endTime,
                PkTeamInfo.parse(advancePkInfo.redTeamInfo),
                PkTeamInfo.parse(advancePkInfo.blueTeamInfo),
                PkUserInfo.parse(advancePkInfo.pkUserInfoList),
                advancePkInfo.voteFor
            )
        }
    }

    fun getPkUserInfoByUid(uid: Int): PkUserInfo {
        userInfo.forEach {
            if (it.uid == uid) {
                return it
            }
        }
        return PkUserInfo()
    }

    fun resetToReady() {
        status = PkStatus.PK_STATUS_UNKNOWN
        startTime = -1
        endTime = -1
        redTeamInfo.score = 0
        blueTeamInfo.score = 0
        userInfo.clear()
        voteForUid = -1
    }
}

data class PkGameMod(
    @SerializedName("pk_user_mode") var pkUserMod: Int = PK_USER_MOD_UNDEFINED,
    @SerializedName("pk_mode") var pkMode: Int = PK_MOD_UNDEFINED
) {
    fun isSingleUserPk(): Boolean {
        return pkUserMod == PK_USER_MOD_SINGLE && pkMode == PK_MOD_BY_USER_NUM
    }

    fun isSingleGiftPk(): Boolean {
        return pkUserMod == PK_USER_MOD_SINGLE && pkMode == PK_MOD_BY_GIFT_NUM
    }

    fun isSinglePk(): Boolean {
        return pkUserMod == PK_USER_MOD_SINGLE
    }

    fun isMultiGiftPk(): Boolean {
        return pkUserMod == PK_USER_MOD_MULTI && pkMode == PK_MOD_BY_GIFT_NUM
    }

    fun isUserPk(): Boolean {
        return pkMode == PK_MOD_BY_USER_NUM
    }

    fun isGiftPk(): Boolean {
        return pkMode == PK_MOD_BY_GIFT_NUM
    }

    fun getTypeByGameMod(): Int {
        if (isSingleUserPk()) return LOCAL_MOD_SINGLE_USER
        if (isSingleGiftPk()) return LOCAL_MOD_SINGLE_GIFT
        if (isMultiGiftPk()) return LOCAL_MOD_MULTI_GIFT
        return LOCAL_MOD_UNDEFINED
    }

    companion object {
        const val PK_MOD_UNDEFINED = 0
        const val PK_MOD_BY_USER_NUM = 1
        const val PK_MOD_BY_GIFT_NUM = 2

        const val PK_USER_MOD_UNDEFINED = 0
        const val PK_USER_MOD_SINGLE = 1
        const val PK_USER_MOD_MULTI = 2

        const val LOCAL_MOD_UNDEFINED = 0
        const val LOCAL_MOD_SINGLE_USER = 1
        const val LOCAL_MOD_SINGLE_GIFT = 2
        const val LOCAL_MOD_MULTI_GIFT = 3

        fun parse(advancePkMode: AdvancePkMod): PkGameMod {
            return PkGameMod(
                when (advancePkMode.pkUserMode) {
                    TmpRoomPackets.PkUserMode.PK_USER_MODE_Single -> PK_USER_MOD_SINGLE
                    TmpRoomPackets.PkUserMode.PK_USER_MODE_Multi -> PK_USER_MOD_MULTI
                    else -> PK_USER_MOD_UNDEFINED
                }, when (advancePkMode.pkMode) {
                    TmpRoomPackets.PkMode.BY_USER_NUM -> PK_MOD_BY_USER_NUM
                    TmpRoomPackets.PkMode.BY_GIFT_NUM -> PK_MOD_BY_GIFT_NUM
                    else -> PK_MOD_UNDEFINED
                }
            )
        }
    }
}

object PkStatus {
    const val PK_STATUS_UNKNOWN = 0
    const val PK_STATUS_READY = 1 // PK控件处于打开状态，可以开始PK
    const val PK_STATUS_START = 2 // 开始PK
    const val PK_STATUS_CLOSE = 3 // PK控件关闭
    fun parse(advancePkStatus: AdvancePkStatus): Int {
        return when (advancePkStatus) {
            AdvancePkStatus.AdvancePkStatus_Ready -> PK_STATUS_READY
            AdvancePkStatus.AdvancePkStatus_Start -> PK_STATUS_START
            AdvancePkStatus.AdvancePkStatus_Close -> PK_STATUS_CLOSE
            else -> PK_STATUS_UNKNOWN
        }
    }

    fun isPkOpen(status: Int): Boolean {
        return status in (PK_STATUS_UNKNOWN + 1) until PK_STATUS_CLOSE
    }
}

data class PkTeamInfo(
    var score: Int = 0,
    var seatList: List<Int> = emptyList()
) {
    companion object {
        fun parse(advancePkTeamInfo: TmpRoomPackets.AdvancePkTeamInfo): PkTeamInfo {
            return PkTeamInfo(advancePkTeamInfo.score, advancePkTeamInfo.seatList)
        }
    }
}

data class PkUserInfo(
    @SerializedName("uid") val uid: Int = -1,
    @SerializedName("score") val score: Int = 0,
    @SerializedName("title_uid") val titleUid: Int = -1,
    @SerializedName("title_score") val titleScore: Int = 0,
    @SerializedName("is_new_title") val isNewTitle: Boolean = false,
    @SerializedName("team_id") val teamId: Int = -1
) {
    companion object {
        const val RED_TEAM_ID = 1
        const val BLUE_TEAM_ID = 2

        fun parse(advancePkUserInfoList: List<TmpRoomPackets.AdvancePkUserInfo>): MutableList<PkUserInfo> {
            val list = mutableListOf<PkUserInfo>()
            for (advancePkUserInfo in advancePkUserInfoList) {
                list.add(parse(advancePkUserInfo))
            }
            return list
        }

        private fun parse(advancePkUserInfo: TmpRoomPackets.AdvancePkUserInfo): PkUserInfo {
            return PkUserInfo(
                advancePkUserInfo.uid,
                advancePkUserInfo.score,
                advancePkUserInfo.titleUid,
                advancePkUserInfo.titleScore,
                advancePkUserInfo.isNewTitle,
                advancePkUserInfo.teamId,
            )
        }
    }

    fun getBackgroundDrawable(config: AdvancePkConfig, score: Int, isRed: Boolean): Drawable {
        val advancePkResConfig =
            if (isRed) config.advancePkRedResList else config.advancePkBlueResList

        var bgColor: List<String> = ArrayList()
        var frameColor: List<String> = ArrayList()
        advancePkResConfig.forEach {
            if (score >= it.minScore) {
                bgColor = it.backgroundColor
                frameColor = it.frameColor
            }
        }

        val drawable = GradientDrawableWithStroke()
        drawable.setBackgroundGradient(
            bgColor,
            GradientDrawableWithStroke.Horizontal
        )
        drawable.setStrokeGradient(frameColor, GradientDrawableWithStroke.Horizontal)
        drawable.setRadiusAndStrokeWidth(
            ScreenUtil.dip2px(10F).toFloat(),
            ScreenUtil.dip2px(0.8F).toFloat()
        )
        return drawable
    }

    fun getScoreIcon(config: AdvancePkConfig, score: Int, isRed: Boolean): String {
        var resIcon = ""
        val advancePkResConfig =
            if (isRed) config.advancePkRedResList else config.advancePkBlueResList
        advancePkResConfig.forEach {
            if (score >= it.minScore) {
                resIcon = it.iconUrl
            }
        }
        return resIcon
    }
}

