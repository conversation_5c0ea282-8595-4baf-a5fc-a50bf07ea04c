package com.wepie.wespy.module.voiceroom.pk445

import android.graphics.drawable.Drawable
import android.view.View
import android.view.ViewPropertyAnimator
import com.huiwan.base.util.ColorUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.widget.GradientDrawableWithStroke

object PKUtil {
    /**
     * alpha通用显示动画(积分条出现)
     */
    fun showAnimByAlpha(view: View, delay: Long = 0): ViewPropertyAnimator {
        view.alpha = 0f
        val animator = view.animate().alpha(1f)
        animator.startDelay = delay
        animator.setDuration(250L).start()
        return animator
    }

    /**
     * alpha通用消失动画
     */
    fun hideAnimByAlpha(view: View, delay: Long = 0): ViewPropertyAnimator {
        val animator = view.animate().alpha(0f)
        animator.startDelay = delay
        animator.setDuration(200L).start()
        return animator
    }

    fun initBg(isRedTeam: Boolean): Drawable {
        val isRtl = ScreenUtil.isRtl()
        val drawable = GradientDrawableWithStroke()

        drawable.setSolidBackgroundColor(ColorUtil.getColor("#26000000"))

        val bgGradient = if (isRedTeam) intArrayOf(
            ColorUtil.getColor("#7DFF3F77"),
            ColorUtil.getColor("#6BFF4F82")
        )
        else intArrayOf(ColorUtil.getColor("#7D4E9FFF"), ColorUtil.getColor("#6B4AB4FF"))
        drawable.setBackgroundGradient(bgGradient, GradientDrawableWithStroke.Vertical)

        val strokeGradient = if (isRedTeam) intArrayOf(
            ColorUtil.getColor("#40FF628D"),
            ColorUtil.getColor("#00ED3083")
        )
        else intArrayOf(ColorUtil.getColor("#003495FC"), ColorUtil.getColor("#4D30AFFB"))
        drawable.setStrokeGradient(
            if (isRtl) strokeGradient.reversedArray() else strokeGradient,
            GradientDrawableWithStroke.Horizontal
        )

        drawable.setRadiusAndStrokeWidth(
            ScreenUtil.dip2px(6F).toFloat(),
            ScreenUtil.dip2px(1F).toFloat()
        )

        val isLeftCorner = (isRedTeam && !isRtl) || (!isRedTeam && isRtl)
        drawable.setSingleCorner(if (isLeftCorner) GradientDrawableWithStroke.BOTTOM_LEFT_CORNER else GradientDrawableWithStroke.BOTTOM_RIGHT_CORNER)

        return drawable
    }

}