package com.wepie.wespy.module.voiceroom.pk445

import android.content.Context
import android.view.View
import android.widget.FrameLayout
import androidx.core.view.children
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IDialogPlugin

class DialogView(context: Context) : FrameLayout(context), IDialogPlugin {

    override fun add(view: View) {
        addView(view)
    }

    override fun add(view: View, index: Int) {
        addView(view, index)
    }

    override fun remove(view: View) {
        removeView(view)
    }

    override fun removeIf(filter: (view: View) -> Boolean) {
        val iterator = this.children.toMutableList().iterator()
        while (iterator.hasNext()) {
            val next = iterator.next()
            if (filter(next)) {
                removeView(next)
            }
        }
    }

    override fun has(): <PERSON><PERSON>an {
        return childCount != 0
    }
}