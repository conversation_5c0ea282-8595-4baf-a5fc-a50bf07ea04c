package com.wepie.wespy.module.voiceroom.pk445

import android.content.Context
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import com.huiwan.anim.SVGAUtil
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.dialog.HWUIDialogBuilder
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.base.util.ViewUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.WebApi
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.opensource.svgaplayer.SVGAParser
import com.opensource.svgaplayer.SVGAVideoEntity
import com.wejoy.weplay.ex.lifecycle.observe
import com.wejoy.weplay.ex.view.toLife
import com.wepie.wespy.R
import com.wepie.wespy.model.event.voice.AdminChangeEvent
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class PkView(context: Context) : FrameLayout(context) {

    companion object {
        const val PK_EMPTY_SEAT_TYPE_UNKNOWN = 0
        const val PK_EMPTY_SEAT_TYPE_RED = 1
        const val PK_EMPTY_SEAT_TYPE_BLUE = 2
    }

    private val pkProgressBar: PkProgressBar
    private val pkStatusView: PkStatusView
    private val pkManageIcon: ImageView
    private val pkRedSupported: TextView
    private val pkBlueSupported: TextView
    private val pkMultiRedBg: FrameLayout
    private val pkMultiBlueBg: FrameLayout

    private lateinit var pkInfo: PkInfo
    private val pkSeatHelper: PkSeatHelper
    private val redBg: Drawable by lazy { PKUtil.initBg(true) }
    private val blueBg: Drawable by lazy { PKUtil.initBg(false) }

    fun update(event: PkEvent) {
        this.pkInfo = event.info
        when (event) {
            // pk相关的view全部刷新
            is PkEvent.PkSyncEvent -> {
                // 0、更新pk布局，座位下移
                updatePkViewMargin(true)
                // 1、刷新pk进度条分数
                pkProgressBar.updateScore(
                    pkInfo.redTeamInfo.score,
                    pkInfo.blueTeamInfo.score,
                    pkInfo.status == PkStatus.PK_STATUS_START
                )
                // 2、刷新pk状态（开始、等待开始、pk结束倒计时）
                updatePkStatusView()
                // 3、刷新pk操作按钮（开启、关闭、帮助）
                updatePkManageIcon(pkInfo.status == PkStatus.PK_STATUS_START)
                // 4、更新已支持布局
                updateSupportedTv()
                // 5、座位上添加pk基本布局
                addPkSeatView()
                // 6、座位上添加pk投票布局
                updatePkSeatVoteView()
                // 7、更新pk背景
                updatePkBg()
                // 8、更新pk座位上的分数信息
                updateSeatScore()
                // 更新左上角冠名图像
                updateSeatNamingView()
            }

            is PkEvent.PkChangeScoreEvent -> {
                pkProgressBar.updateScore(
                    pkInfo.redTeamInfo.score,
                    pkInfo.blueTeamInfo.score,
                    pkInfo.status == PkStatus.PK_STATUS_START
                )
                updateSeatScore()
                // 左上角冠名图像和动效
                updateSeatNamingView(event.changeScore)
            }

            is PkEvent.PkCloseEvent -> {
                // 移除座位上的pk布局
                removePkSeatView()
                updatePkViewMargin(false)
            }

            is PkEvent.PkReadyEvent -> {
                updatePkViewMargin(true)
                pkProgressBar.updateScore(
                    pkInfo.redTeamInfo.score,
                    pkInfo.blueTeamInfo.score,
                    false
                )
                updatePkStatusView()
                updatePkManageIcon(false)
                hideSupportedTv()
                addPkSeatView()
                updatePkSeatVoteView()
                updatePkBg()
            }

            is PkEvent.PkStartEvent -> {
                pkProgressBar.updateScore(
                    pkInfo.redTeamInfo.score,
                    pkInfo.blueTeamInfo.score,
                    true
                )
                updatePkStatusView()
                updatePkManageIcon(true)
                updatePkSeatVoteView()
                updateSeatScore()
                playStartSvga()
                updateSeatNamingView()
            }

            is PkEvent.PkResultEvent -> {
                if (event.result.redScore != 0 || event.result.blueScore != 0) {
                    // 双方至少有一方不是0分才弹窗
                    PkDialogResultView.show(context, pkInfo.gameMod, event.result, true)
                }
                pkProgressBar.stopDirectionAnim()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onAdminChange(event: AdminChangeEvent?) {
        updatePkStatusView()
        updatePkManageIcon(pkInfo.status == PkStatus.PK_STATUS_START)
    }

    init {
        val v = LayoutInflater.from(context).inflate(R.layout.room_pk_view_new, this)
        pkProgressBar = v.findViewById(R.id.pk_progress_bar)
        pkStatusView = v.findViewById(R.id.pk_status_view)
        pkManageIcon = v.findViewById(R.id.pk_manage_icon)
        pkRedSupported = v.findViewById(R.id.pk_red_supported)
        pkBlueSupported = v.findViewById(R.id.pk_blue_supported)
        pkMultiRedBg = v.findViewById(R.id.pk_multi_red_bg)
        pkMultiBlueBg = v.findViewById(R.id.pk_multi_blue_bg)
        pkSeatHelper = PkSeatHelper(this)
        VoiceRoomService.getInstance().seatsInfoList.observe(toLife()) {
            updatePkSeatInfoChange()
            updateSeatNamingView()
        }

        preloadSvga()
    }

    private fun updatePkManageIcon(isStartedState: Boolean) {
        val isOwnerOrAdmin = VoiceRoomService.getInstance().roomInfo.isSelfAdminOrOwner
        if (isStartedState && !isOwnerOrAdmin) {
            pkManageIcon.visibility = View.GONE
            return
        } else {
            pkManageIcon.visibility = View.VISIBLE
            pkManageIcon.setOnClickListener {
                ToastUtil.debugShow("pk manage icon clicked")
                if (isOwnerOrAdmin) {
                    processClosePkWidget()
                } else {
                    ApiService.of(WebApi::class.java).showWebDialog(
                        context,
                        ConfigHelper.getInstance().constV3Info.advancePkHelpUrl,
                        592,
                        WebApi.WebDialogConfig(0F, false)
                    )
                }
            }
        }
        val resId: Int = if (isOwnerOrAdmin) R.drawable.pk_icon_manage else R.drawable.pk_icon_help
        pkManageIcon.setImageResource(resId)
    }

    private fun processStartPk() {
        if (pkInfo.gameMod.isSinglePk() && !canStartSinglePk()) {
            ToastUtil.show(R.string.pk_cannot_start_empty_seat)
            return
        }
        PkPacketSender.startAdvancePk(
            VoiceRoomService.getInstance().rid,
            object : LifeSeqCallback(this) {
                override fun onSuccess(head: RspHeadInfo?) {
                }

                override fun onFail(head: RspHeadInfo?) {
                    ToastUtil.show(head?.desc)
                }
            })
    }

    private fun processClosePkWidget() {
        HWUIDialogBuilder.newBuilder(context).setSingleBtn(false)
            .setTitle(R.string.pk_close_widget_desc).setDialogCallback {
                PkPacketSender.modifyPkWidget(
                    VoiceRoomService.getInstance().rid,
                    false,
                    0,
                    0,
                    0,
                    object : LifeSeqCallback(this) {
                        override fun onSuccess(head: RspHeadInfo) = Unit

                        override fun onFail(head: RspHeadInfo) {
                            ToastUtil.show(head.desc)
                        }
                    })
            }.show()
    }

    private fun addPkSeatView() {
        pkSeatHelper.initSeat(pkInfo)
    }

    private fun updatePkSeatVoteView() {
        pkSeatHelper.updateSeatVote(pkInfo)
    }

    private fun removePkSeatView() {
        if (!::pkInfo.isInitialized) {
            return
        }
        pkSeatHelper.unInitSeat(pkInfo)
    }

    private fun canStartSinglePk(): Boolean {
        val seatList = pkInfo.redTeamInfo.seatList + pkInfo.blueTeamInfo.seatList
        val roomInfo = VoiceRoomService.getInstance().roomInfo
        seatList.forEach {
            if (roomInfo.getSeatInfoByNum(it).isEmpty) {
                return false
            }
        }
        return true
    }

    private fun updatePkViewMargin(isOpen: Boolean) {
        val roomInfo = VoiceRoomService.getInstance().roomInfo
        val seatView = if (roomInfo.isVoiceRoom) {
            VoicePluginService.getPlugin(ISeatGroup::class.java).getSeatView(2)
        } else if (roomInfo.isFamilyRoom) {
            VoicePluginService.getPlugin(ISeatGroup::class.java).getSeatView(6)
        } else null
        val seatParentLay = seatView?.parent?.parent
        seatParentLay?.let {
            val voiceMargin = if (isOpen) ScreenUtil.dip2px(56f) else ScreenUtil.dip2px(26f)
            val familyMargin = if (isOpen) ScreenUtil.dip2px(46f) else ScreenUtil.dip2px(6f)
            ViewUtil.setTopMargins(
                it as View,
                if (roomInfo.isFamilyRoom) familyMargin else voiceMargin
            )
        }

        if (roomInfo.isFamilyRoom) {
            val seatViewNextLine = VoicePluginService.getPlugin(ISeatGroup::class.java)
                .getSeatView(10)
            val seatParentLayNextLine = seatViewNextLine?.parent?.parent
            seatParentLayNextLine?.let {
                val familySecondMargin = if (isOpen) {
                    ScreenUtil.dip2px(12f)
                } else {
                    ScreenUtil.dip2px(6f)
                }
                ViewUtil.setTopMargins(it as View, familySecondMargin)
            }
        }
    }

    private fun updatePkBg() {
        val flag = pkInfo.gameMod.isMultiGiftPk()
        pkMultiRedBg.isVisible = flag
        pkMultiBlueBg.isVisible = flag
        if (flag) {
            pkMultiRedBg.background = redBg
            pkMultiBlueBg.background = blueBg
        }
    }

    private fun playStartSvga() {
        PkStartAnimHelper.loadStartAnim(context)
    }

    private fun updatePkSeatInfoChange() {
        updateSeatScore()
        if (::pkInfo.isInitialized && pkInfo.status == PkStatus.PK_STATUS_START) {
            updatePkSeatVoteView()
        }
    }

    private fun updateSeatScore() {
        if (!::pkInfo.isInitialized) {
            return
        }
        pkSeatHelper.updateSeatScore(pkInfo)
    }

    private fun updateSeatNamingView() {
        if (!::pkInfo.isInitialized) {
            return
        }
        pkSeatHelper.showNamingViewSync(pkInfo)
    }

    private fun updateSeatNamingView(changeScore: PkPushChangeScore) {
        if (!::pkInfo.isInitialized) {
            return
        }
        if (pkInfo.gameMod.isGiftPk()) {
            pkSeatHelper.showNamingViewChange(pkInfo.status, changeScore)
        }
    }

    private fun updateSupportedTv() {
        if (pkInfo.status != PkStatus.PK_STATUS_START) {
            hideSupportedTv()
            return
        }

        if (pkInfo.voteForUid > 0) {
            val seatInfo = VoiceRoomService.getInstance().roomInfo
                .getSeatInfoByUid(pkInfo.voteForUid)
            if (seatInfo == null) {
                hideSupportedTv()
            } else {
                val isRed = pkInfo.redTeamInfo.seatList.contains(seatInfo.seat_num)
                val isBlue = pkInfo.blueTeamInfo.seatList.contains(seatInfo.seat_num)
                if (isRed || isBlue) {
                    showSupportedTv(isRed)
                } else {
                    hideSupportedTv()
                }
            }
        } else {
            hideSupportedTv()
        }
    }

    fun showSupportedTv(isRedTeam: Boolean) {
        if (isRedTeam) {
            pkRedSupported.visibility = View.VISIBLE
        } else {
            pkBlueSupported.visibility = View.VISIBLE
        }
    }

    private fun hideSupportedTv() {
        pkRedSupported.visibility = View.GONE
        pkBlueSupported.visibility = View.GONE
    }

    fun isSupportedTvShow(): Boolean {
        return pkRedSupported.isVisible || pkBlueSupported.isVisible
    }

    fun getPkInfo(): PkInfo {
        return pkInfo
    }

    private fun updatePkStatusView() {
        pkStatusView.updatePkStatus(pkInfo) {
            processStartPk()
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        PKUtil.showAnimByAlpha(this)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
        removePkSeatView()
    }

    private fun preloadSvga() {
        val life = toLife()
        val parseCompletion = object : SVGAParser.ParseCompletion {
            override fun onComplete(videoItem: SVGAVideoEntity) = Unit
            override fun onError() = Unit
        }
        SVGAUtil.getSvgaUrl(life, ResUtil.getStr(R.string.pk_start_svga), parseCompletion)
        SVGAUtil.getSvgaUrl(life, ResUtil.getStr(R.string.pk_naming_best_svga), parseCompletion)
        SVGAUtil.getSvgaUrl(life, ResUtil.getStr(R.string.pk_naming_super_svga), parseCompletion)
    }
}