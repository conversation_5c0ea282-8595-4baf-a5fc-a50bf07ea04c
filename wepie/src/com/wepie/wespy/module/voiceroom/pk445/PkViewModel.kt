package com.wepie.wespy.module.voiceroom.pk445

import androidx.lifecycle.MutableLiveData
import com.huiwan.base.util.ToastUtil
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.wejoy.weplay.ex.ILife
import com.wejoy.weplay.ex.lifecycle.observe
import com.wepie.liblog.main.HLog
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets

class PkViewModel(private val life: ILife) : IPk {
    companion object {
        private val pkLiveData = MutableLiveData<PkModel>()

        @JvmStatic
        fun update(info: TmpRoomPackets.TmpRoomInfo) {
            pkLiveData.postValue(PkModel(info.rid, info.advancePkOpenTime, info.advancePkVersion))
        }
    }

    sealed class PkState {
        object SyncState : PkState()
        object CloseState : PkState()
    }

    val pkState = MutableLiveData<PkState>()
    private var pkInfo: PkInfo? = null
    private var pkModel = PkModel()

    init {
        pkLiveData.observe(life) {
            updatePkModel(it)
        }
    }

    private fun updatePkModel(pkModel: PkModel) {
        if (pkModel.rid < 0) {
            HLog.e(PkDelegateImpl.TAG, HLog.USR, "invalid rid")
            return
        }
        val lastPkModel = this.pkModel
        this.pkModel = pkModel
        when (lastPkModel.isOpen()) {
            true -> {
                when (this.pkModel.isOpen()) {
                    true -> {
                        if (lastPkModel.openTime != this.pkModel.openTime) {
                            // 非同一次Pk控件启动, 必须要sync
                            sync()
                        } else {
                            // 需要checkVersion
                            trySyncWhenPkModel(pkModel.version)
                        }
                    }

                    false -> {
                        pkState.value = PkState.CloseState
                    }
                }
            }

            false -> {
                when (this.pkModel.isOpen()) {
                    true -> {
                        trySyncWhenPkModel(pkModel.version)
                    }

                    false -> Unit
                }
            }
        }
    }

    /**
     * 是否需要sync
     */
    fun trySyncWhenPush(serverVersion: Int): Boolean {
        val pkInfo = getPkInfo()
        val clientVersion = pkInfo.version
        HLog.d(
            PkDelegateImpl.TAG,
            HLog.USR,
            "serverVersion $serverVersion client version $clientVersion"
        )
        if (serverVersion <= clientVersion) {
            HLog.w(
                PkDelegateImpl.TAG, HLog.USR,
                "serverVersion $serverVersion smaller than client version ${clientVersion}, ignore"
            )
            // 当前push版本低，不能使用
            return true
        }
        pkInfo.serverVersion = serverVersion
        val needSync = serverVersion > pkInfo.version + 1
        if (needSync) {
            sync()
        } else {
            pkInfo.version = serverVersion
        }
        return needSync
    }

    /**
     * 语音房sync带的pk version信息
     * 正常情况下，语音房sync的数据要比push的数据晚到，此时serverVersion <= clientVersion
     * 极端情况下，语音房sync的数据要比push的数据要先到，此时serverVersion = clientVersion + 1,但此时可能是push没有来或者push不会再来(断线重连场景),故直接sync
     * 分拆方法处理语音房sync的version
     */
    private fun trySyncWhenPkModel(serverVersion: Int) {
        val pkInfo = getPkInfo()
        val clientVersion = pkInfo.version
        HLog.d(
            PkDelegateImpl.TAG,
            HLog.USR,
            "voiceRoom serverVersion $serverVersion client version $clientVersion"
        )
        if (serverVersion > clientVersion) {
            pkInfo.serverVersion = serverVersion
            sync()
        }
    }

    /**
     * sync
     */
    private fun sync() {
        HLog.d(PkDelegateImpl.TAG, HLog.USR, "sync start")
        val rid = VoiceRoomService.getInstance().rid
        PkPacketSender.sync(rid, object : LifeSeqCallback(life) {
            override fun onSuccess(head: RspHeadInfo?) {
                HLog.d(PkDelegateImpl.TAG, HLog.USR, "sync end")
                val advancePkInfo = (head?.message as TmpRoomPackets.SyncAdvancePkRsp).advancePkInfo
                if (advancePkInfo == null) {
                    HLog.d(PkDelegateImpl.TAG, HLog.USR, "sync end error, message=${head.message}")
                    pkState.value = PkState.CloseState
                    return
                }
                if (advancePkInfo.pkVersion < getPkInfo().serverVersion) {
                    HLog.w(
                        PkDelegateImpl.TAG, HLog.USR,
                        "sync game ignore while sync version ${advancePkInfo.pkVersion} " +
                                "< current serverVersion ${getPkInfo().serverVersion}"
                    )
                    sync() // retry
                    return
                }
                if (advancePkInfo.pkVersion == getPkInfo().version) {
                    // 当sync数据和当前client数据一样时，忽略sync数据
                    HLog.w(
                        PkDelegateImpl.TAG, HLog.USR,
                        "ignore sync data while sync version ${advancePkInfo.pkVersion} " +
                                "== clientVersion ${getPkInfo().version}"
                    )
                }
                pkInfo = PkInfo.parse(advancePkInfo)
                pkState.value = PkState.SyncState
            }

            override fun onFail(head: RspHeadInfo) {
                HLog.w(
                    PkDelegateImpl.TAG,
                    HLog.USR,
                    "sync end, onFail code= ${head.code}, desc= ${head.desc}"
                )
                ToastUtil.show(head.desc)
            }
        })
    }

    fun getPkInfo(): PkInfo {
        if (pkInfo == null) {
            pkInfo = PkInfo()
        }
        return pkInfo!!
    }

    /**
     * 清除数据
     */
    fun clear() {
        pkInfo = null
        pkModel = PkModel()
    }

    override fun isPkStarted(): Boolean {
        return isPkOpen() && getPkInfo().status == PkStatus.PK_STATUS_START
    }

    override fun isPkOpen(): Boolean {
        if (pkInfo == null) {
            return false
        }
        return PkStatus.isPkOpen(getPkInfo().status)
    }

    override fun getPkSeatType(seatNum: Int): Int {
        if (!isPkOpen()) {
            return PkView.PK_EMPTY_SEAT_TYPE_UNKNOWN
        }
        return if (getPkInfo().redTeamInfo.seatList.contains(seatNum)) {
            PkView.PK_EMPTY_SEAT_TYPE_RED
        } else if (getPkInfo().blueTeamInfo.seatList.contains(seatNum)) {
            PkView.PK_EMPTY_SEAT_TYPE_BLUE
        } else {
            PkView.PK_EMPTY_SEAT_TYPE_UNKNOWN
        }
    }
}

class PkModel(
    var rid: Int = -1,
    val openTime: Long = -1,
    val version: Int = 0
) {
    /**
     * Pk控件是否处于打开状态
     */
    fun isOpen(): Boolean {
        return openTime > 0
    }
}