package com.wepie.wespy.module.voiceroom.pk445

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.transition.TransitionManager
import com.huiwan.anim.SVGAUtil
import com.huiwan.base.util.ScreenUtil
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser
import com.opensource.svgaplayer.SVGAVideoEntity
import com.wepie.wespy.R

/**
 * date 2018/7/5
 * email <EMAIL>
 *
 * <AUTHOR>
 */
class PkProgressBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

    companion object {
        private const val SVGA_PK_LIGHTING = "svga/pk/pk_lighting.svga"
        private const val SVGA_PK_DIRECTION = "svga/pk/pk_direction.svga"
    }

    private val pkProgressRoot: ConstraintLayout
    private val pkRedScore: TextView
    private val pkBlueScore: TextView
    private val pkDirectionSvga: SVGAImageView
    private val pkLightingSvga: SVGAImageView

    private val direction = if (ScreenUtil.isRtl()) {
        -1
    } else {
        1
    }
    private var enableDirectionSvga = true

    init {
        clipChildren = false
        View.inflate(context, R.layout.pk_progress, this)
        pkProgressRoot = findViewById(R.id.pk_progress_root)
        pkRedScore = findViewById(R.id.pk_red_score)
        pkBlueScore = findViewById(R.id.pk_blue_score)
        pkDirectionSvga = findViewById(R.id.pk_direction_svga)
        pkLightingSvga = findViewById(R.id.pk_lighting_svga)
        SVGAUtil.playSvga(SVGA_PK_LIGHTING, -1, pkLightingSvga)
        SVGAUtil.cacheSvga(SVGA_PK_DIRECTION)
        updateScore(0, 0, false)
    }

    fun updateScore(redScore: Int, blueScore: Int, isDirectionSvgaPlay: Boolean) {
        pkRedScore.text = "$redScore"
        pkBlueScore.text = "$blueScore"
        val percent = if (redScore == blueScore) {
            0.5f
        } else if (redScore == 0) {
            0f
        } else if (blueScore == 0) {
            1f
        } else {
            redScore.toFloat() / (redScore + blueScore)
        }
        post {
            TransitionManager.beginDelayedTransition(pkProgressRoot)
            val constraintSet = ConstraintSet()
            constraintSet.clone(pkProgressRoot)
            constraintSet.setHorizontalBias(R.id.pk_lighting_svga, percent)
            constraintSet.applyTo(pkProgressRoot)

            if (enableDirectionSvga && percent != 0.5f) {
                val params = pkDirectionSvga.layoutParams as ConstraintLayout.LayoutParams
                if (percent > 0.5f) {
                    params.endToEnd = R.id.pk_lighting_svga
                    params.startToStart = -1
                    params.marginStart = 0
                    params.marginEnd = ScreenUtil.dip2px(10f)
                    pkDirectionSvga.scaleX = 1f * direction
                } else if (percent < 0.5f) {
                    params.startToStart = R.id.pk_lighting_svga
                    params.endToEnd = -1
                    params.marginStart = ScreenUtil.dip2px(10f)
                    params.marginEnd = 0
                    pkDirectionSvga.scaleX = -1f * direction
                }
                if (isDirectionSvgaPlay) {
                    pkDirectionSvga.layoutParams = params
                    SVGAUtil.playSvga(SVGA_PK_DIRECTION, -1, pkDirectionSvga)
                } else {
                    pkDirectionSvga.stopAnimation()
                }
            } else {
                pkDirectionSvga.stopAnimation(true)
            }
        }
    }

    fun enableDirectionSvga(enable: Boolean) {
        this.enableDirectionSvga = enable
    }

    fun showViewWithoutAnim(redScore: Int, blueScore: Int) {
        pkLightingSvga.stopAnimation()
        pkRedScore.text = "$redScore"
        pkBlueScore.text = "$blueScore"
        val percent = if (redScore == blueScore) {
            0.5f
        } else if (redScore == 0) {
            0f
        } else if (blueScore == 0) {
            1f
        } else {
            redScore.toFloat() / (redScore + blueScore)
        }

        pkLightingSvga.post {
            val constraintSet = ConstraintSet()
            constraintSet.clone(pkProgressRoot)
            constraintSet.setHorizontalBias(R.id.pk_lighting_svga, percent)
            constraintSet.applyTo(pkProgressRoot)
        }

        val svgaParser = SVGAParser(context)
        svgaParser.decodeFromAssets(SVGA_PK_LIGHTING, object : SVGAParser.ParseCompletion {
            override fun onComplete(videoItem: SVGAVideoEntity) {
                pkLightingSvga.setVideoItem(videoItem)
                pkLightingSvga.stepToFrame(1, false)
            }

            override fun onError() {
            }
        })
    }

    fun stopDirectionAnim() {
        pkDirectionSvga.stopAnimation()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        pkDirectionSvga.stopAnimation(true)
        pkLightingSvga.stopAnimation(true)
    }
}
