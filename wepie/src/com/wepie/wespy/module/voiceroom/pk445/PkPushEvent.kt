package com.wepie.wespy.module.voiceroom.pk445

import com.google.gson.annotations.SerializedName
import com.wepie.wespy.net.tcp.packet.RoomPushPackets

interface IPkPush {
    fun mergeInto(pkInfo: PkInfo)
}

data class PkPushChangeScore(
    val scoreChangeUsers: List<PkUserInfo> = emptyList(),
    val redScore: Int = 0,
    val blueScore: Int = 0
) : IPkPush {

    companion object {
        fun parse(changeScore: RoomPushPackets.AdvancePkChangeScore): PkPushChangeScore {
            return PkPushChangeScore(
                PkUserInfo.parse(changeScore.scoreChangeUsersList),
                changeScore.redScore,
                changeScore.blueScore
            )
        }
    }

    override fun mergeInto(pkInfo: PkInfo) {
        pkInfo.redTeamInfo.score = redScore
        pkInfo.blueTeamInfo.score = blueScore

        val changeUidList = pkInfo.userInfo.map { it.uid }
            .intersect(scoreChangeUsers.map { it.uid }.toSet())
        val old = pkInfo.userInfo.filter { it.uid in changeUidList }
        pkInfo.userInfo.removeAll(old)
        pkInfo.userInfo.addAll(scoreChangeUsers)
    }
}

data class PkPushResult(
    @SerializedName("red_score") val redScore: Int = 0,
    @SerializedName("blue_score") val blueScore: Int = 0,
    @SerializedName("winners") val winners: List<PkUserInfo> = emptyList(),
    @SerializedName("support_uids") val supportUids: List<Int> = emptyList(),
) : IPkPush {
    companion object {
        fun parse(end: RoomPushPackets.AdvancePkEnd): PkPushResult {
            return PkPushResult(
                end.redScore,
                end.blueScore,
                PkUserInfo.parse(end.winnersList),
                end.supportUidsList
            )
        }
    }

    override fun mergeInto(pkInfo: PkInfo) {
        pkInfo.redTeamInfo.score = redScore
        pkInfo.blueTeamInfo.score = blueScore

        val changeUidList = pkInfo.userInfo.map { it.uid }.intersect(winners.map { it.uid }.toSet())
        val old = pkInfo.userInfo.filter { it.uid in changeUidList }
        pkInfo.userInfo.removeAll(old)
        pkInfo.userInfo.addAll(winners)
    }
}

data class PkPushChangeStatus(
    val pkStatus: Int = PkStatus.PK_STATUS_UNKNOWN,
    val gameMod: PkGameMod = PkGameMod(),
    val redSeatList: List<Int> = emptyList(),
    val blueSeatList: List<Int> = emptyList(),
    val startTime: Long = -1,
    val endTime: Long = -1
) : IPkPush {
    companion object {
        fun parse(changeStatus: RoomPushPackets.AdvancePkChangeStatus): PkPushChangeStatus {
            return PkPushChangeStatus(
                PkStatus.parse(changeStatus.advancePkStatus),
                PkGameMod.parse(changeStatus.gameMod),
                changeStatus.redSeatList,
                changeStatus.blueSeatList,
                changeStatus.pkStartTime,
                changeStatus.pkEndTime
            )
        }
    }

    override fun mergeInto(pkInfo: PkInfo) {
        pkInfo.status = pkStatus
        if (pkStatus == PkStatus.PK_STATUS_READY) {
            pkInfo.gameMod = gameMod
            pkInfo.redTeamInfo.seatList = redSeatList
            pkInfo.blueTeamInfo.seatList = blueSeatList
        }
        if (pkStatus == PkStatus.PK_STATUS_START) {
            pkInfo.startTime = startTime
            pkInfo.endTime = endTime
        }
    }
}