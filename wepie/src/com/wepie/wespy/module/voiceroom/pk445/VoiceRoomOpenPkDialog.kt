package com.wepie.wespy.module.voiceroom.pk445

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.View
import android.view.View.OnClickListener
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.FontUtil.getTypeface
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.WebApi
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog
import com.wepie.wespy.helper.dialog.bottomsheet.WpDragDialog

class VoiceRoomOpenPkDialog : FrameLayout, View.OnClickListener {

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    private var callBack = {}
    private var rid = 0
    private var pkUserMode = PK_USER_MODE_SINGLE
    private var pkMode = BY_GIFT_NUM
    private var pkDuration = TIME_1_MINS


    private val pkHelpIv: AppCompatImageView

    private val pkOneLay: LinearLayout
    private val pkMultiLay: LinearLayout
    private val pkOneTv: TextView
    private val pkMultiTv: TextView

    private val pkTipsTv: TextView
    private val pkGiftTv: TextView
    private val pkPeopleTv: TextView
    private lateinit var pkTime: TextView
    private val pkStartTv: TextView
    private val listV = mutableListOf<TextView>()


    init {
        val dragView = WpDragDialog(context)
        val view = dragView.setContentView(R.layout.voice_room_start_pk_dialog) {
            callBack.invoke()
        }
        this.addView(dragView)
        pkHelpIv = view.findViewById(R.id.voice_room_pk_help_iv)

        pkOneLay = view.findViewById(R.id.voice_room_pk_one_lay)
        pkMultiLay = view.findViewById(R.id.voice_room_pk_multi_lay)
        pkOneTv = view.findViewById(R.id.voice_room_pk_one_tv)
        pkMultiTv = view.findViewById(R.id.voice_room_pk_multi_tv)
        pkTipsTv = view.findViewById(R.id.voice_room_pk_tips_tv)
        pkGiftTv = view.findViewById(R.id.voice_room_pk_gift_tv)
        pkPeopleTv = view.findViewById(R.id.voice_room_pk_people_tv)
        pkStartTv = view.findViewById(R.id.voice_room_pk_start_tv)

        pkHelpIv.setOnClickListener(this)
        pkOneLay.setOnClickListener(this)
        pkMultiLay.setOnClickListener(this)
        pkGiftTv.setOnClickListener(this)
        pkPeopleTv.setOnClickListener(this)
        pkStartTv.setOnClickListener(this)

        val listener  = OnClickListener { v ->
            val time = v.tag as Int
            pkDuration = time
            listV.forEach {
                if (it.tag != time) {
                    setUnselectedUI(it)
                }
            }
            setSelectedUI(v as TextView)
        }

        map.forEach { (time, vid) ->
            pkTime = view.findViewById(vid)
            listV.add(pkTime)
            pkTime.tag = time
            pkTime.setOnClickListener(listener)
        }
    }


    private fun setSelectedUI(v: TextView) {
        v.setBackgroundResource(R.drawable.voice_room_pk_select_bg)
        v.setTextColor(ResUtil.getColor(R.color.color_accent))
        v.typeface = getTypeface(Typeface.BOLD)
    }

    private fun setUnselectedUI(v: TextView) {
        v.setBackgroundResource(R.drawable.voice_room_pk_unselect_bg)
        v.setTextColor(Color.parseColor("#68697A"))
        v.typeface = getTypeface(Typeface.NORMAL)
    }

    override fun onClick(v: View?) {

        when (v) {

            pkHelpIv -> {
                ApiService.of(WebApi::class.java).showWebDialog(
                    context, ConfigHelper.getInstance().constV3Info.advancePkHelpUrl,
                    592, WebApi.WebDialogConfig(0F, false)
                )
            }

            pkOneLay -> {
                pkUserMode = PK_USER_MODE_SINGLE
                pkMode = BY_GIFT_NUM
                pkOneLay.setBackgroundResource(R.drawable.voice_room_pk_select_bg)
                pkOneTv.setTextColor(ResUtil.getColor(R.color.color_accent))
                pkOneTv.typeface = getTypeface(Typeface.BOLD)
                pkMultiLay.setBackgroundResource(R.drawable.voice_room_pk_unselect_bg)
                pkMultiTv.setTextColor(Color.parseColor("#68697A"))
                pkMultiTv.typeface = getTypeface(Typeface.NORMAL)
                setSelectedUI(pkGiftTv)
                setUnselectedUI(pkPeopleTv)
                pkPeopleTv.visibility = VISIBLE
                pkTipsTv.text = ResUtil.getStr(R.string.voice_room_pk_new_rules_tips)
            }

            pkMultiLay -> {
                pkUserMode = PK_USER_MODE_MULTI
                pkMode = BY_GIFT_NUM
                pkOneLay.setBackgroundResource(R.drawable.voice_room_pk_unselect_bg)
                pkOneTv.setTextColor(Color.parseColor("#68697A"))
                pkOneTv.typeface = getTypeface(Typeface.NORMAL)
                pkMultiLay.setBackgroundResource(R.drawable.voice_room_pk_select_bg)
                pkMultiTv.setTextColor(ResUtil.getColor(R.color.color_accent))
                pkMultiTv.typeface = getTypeface(Typeface.BOLD)
                setSelectedUI(pkGiftTv)
                pkPeopleTv.visibility = GONE
                pkTipsTv.text = ResUtil.getStr(R.string.voice_room_pk_new_rules_tips)
            }

            pkGiftTv -> {
                pkMode = BY_GIFT_NUM
                setSelectedUI(pkGiftTv)
                setUnselectedUI(pkPeopleTv)
                pkTipsTv.text = ResUtil.getStr(R.string.voice_room_pk_new_rules_tips)
            }

            pkPeopleTv -> {
                pkMode = BY_USER_NUM
                setSelectedUI(pkPeopleTv)
                setUnselectedUI(pkGiftTv)
                pkTipsTv.text = ResUtil.getStr(R.string.voice_room_pk_new_rules_tips_by_num)
            }

            pkStartTv -> {
                //启动调接口
                createPK()
            }
        }
    }

    private fun createPK() {
        PkPacketSender.modifyPkWidget(
            rid,
            true,
            pkUserMode,
            pkMode,
            pkDuration,
            object : LifeSeqCallback(this) {
                override fun onSuccess(head: RspHeadInfo?) {
                    callBack.invoke()
                }

                override fun onFail(head: RspHeadInfo?) {
                    ToastUtil.show(head?.desc)
                }

            })
    }


    companion object {

        const val PK_USER_MODE_UNDEFINED = 0
        const val PK_USER_MODE_SINGLE = 1
        const val PK_USER_MODE_MULTI = 2

        const val PK_MODE_UNDEFINED = 0
        const val BY_USER_NUM = 1
        const val BY_GIFT_NUM = 2

        const val TIME_1_MINS = 60
        const val TIME_3_MINS = 3 * 60
        const val TIME_5_MINS = 5 * 60
        const val TIME_15_MINS = 15 * 60
        const val TIME_30_MINS = 30 * 60
        const val TIME_60_MINS = 60 * 60

        val map = mapOf(
            TIME_1_MINS to R.id.voice_room_pk_time1,
            TIME_3_MINS to R.id.voice_room_pk_time2,
            TIME_5_MINS to R.id.voice_room_pk_time3,
            TIME_15_MINS to R.id.voice_room_pk_time4,
            TIME_30_MINS to R.id.voice_room_pk_time5,
            TIME_60_MINS to R.id.voice_room_pk_time6
        )

        @JvmStatic
        fun show(context: Context, rid: Int) {
            val dialog =
                BaseFullScreenDialog(context, R.style.dialog_style_custom)
            val view = VoiceRoomOpenPkDialog(context)
            view.callBack = {
                dialog.dismiss()
            }
            view.rid = rid
            dialog.setDimAmount(0F)
            dialog.setContentView(view)
            dialog.setCanceledOnTouchOutside(true)
            dialog.initBottomDialog()
            dialog.show()
        }
    }

}