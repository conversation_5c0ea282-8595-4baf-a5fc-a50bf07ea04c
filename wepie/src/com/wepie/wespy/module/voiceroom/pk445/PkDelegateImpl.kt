package com.wepie.wespy.module.voiceroom.pk445

import com.wejoy.weplay.ex.lifecycle.observe
import com.wepie.liblog.main.HLog
import com.wepie.wespy.module.voiceroom.IPluginTool
import com.wepie.wespy.net.tcp.handler.VoiceRoomHandler
import com.wepie.wespy.net.tcp.handler.VoiceRoomHandler.IPushIntercept
import com.wepie.wespy.net.tcp.packet.RoomPushPackets

class PkDelegateImpl(
    private val pluginTool: IPluginTool<IPk>
) : IPk {
    companion object {
        const val TAG = "Pk445"
    }

    private val pkViewModel = PkViewModel(pluginTool)

    init {
        observePkEvent()
        initPushIntercept()
    }

    override fun isPkStarted(): Boolean {
        return pkViewModel.isPkStarted()
    }

    override fun isPkOpen(): Boolean {
        return pkViewModel.isPkOpen()
    }

    override fun getPkSeatType(seatNum: Int): Int {
        return pkViewModel.getPkSeatType(seatNum)
    }

    private fun initPushIntercept() {
        val intercept = IPushIntercept { pushMsg ->
            var interceptReturn = false
            when (pushMsg.sysTypeValue) {
                RoomPushPackets.RoomSysType.SYS_ADVANCE_PK_CHANGE_SCORE_VALUE -> {
                    // 积分变化
                    interceptReturn = true
                    val changeScore = PkPushChangeScore.parse(pushMsg.advancePkUserScore)
                    if (pkViewModel.trySyncWhenPush(pushMsg.advancePkUserScore.pkVersion)) {
                        return@IPushIntercept interceptReturn
                    }
                    val pkInfo = pkViewModel.getPkInfo()
                    changeScore.mergeInto(pkInfo)
                    val changeScoreEvent = PkEvent.PkChangeScoreEvent(pkInfo, changeScore)
                    HLog.d(TAG, HLog.USR, "pkChangeScoreEvent $changeScoreEvent")
                    update(changeScoreEvent)
                }

                RoomPushPackets.RoomSysType.SYS_ADVANCE_PK_CHANGE_STATUS_VALUE -> {
                    // 状态变更
                    interceptReturn = true
                    val changeStatus = PkPushChangeStatus.parse(pushMsg.advancePkChangeStatus)
                    if (pkViewModel.trySyncWhenPush(pushMsg.advancePkChangeStatus.pkVersion)) {
                        return@IPushIntercept interceptReturn
                    }
                    val pkInfo = pkViewModel.getPkInfo()
                    when (changeStatus.pkStatus) {
                        PkStatus.PK_STATUS_READY -> {
                            changeStatus.mergeInto(pkInfo)
                            pluginTool.loadView(true)
                            val readyEvent = PkEvent.PkReadyEvent(pkInfo, changeStatus)
                            HLog.d(TAG, HLog.USR, "pkReadyEvent $readyEvent")
                            update(readyEvent)
                        }

                        PkStatus.PK_STATUS_START -> {
                            pkInfo.resetToReady()
                            changeStatus.mergeInto(pkInfo)
                            val startEvent = PkEvent.PkStartEvent(pkInfo, changeStatus)
                            HLog.d(TAG, HLog.USR, "pkStartEvent $startEvent")
                            update(startEvent)
                        }

                        PkStatus.PK_STATUS_CLOSE -> {
                            changeStatus.mergeInto(pkInfo)
                            closePkWidget()
                        }

                        else -> Unit
                    }
                }

                RoomPushPackets.RoomSysType.SYS_ADVANCE_PK_END_VALUE -> {
                    // 结算
                    interceptReturn = true
                    val result = PkPushResult.parse(pushMsg.advancePkEnd)
                    if (pkViewModel.trySyncWhenPush(pushMsg.advancePkEnd.pkVersion)) {
                        return@IPushIntercept interceptReturn
                    }
                    val pkInfo = pkViewModel.getPkInfo()
                    result.mergeInto(pkInfo)
                    val resultEvent = PkEvent.PkResultEvent(pkInfo, result)
                    HLog.d(TAG, HLog.USR, "pkResultEvent $resultEvent")
                    update(resultEvent)

                    // 结算之后手动发一个ready，服务端连续发两个push不一定有序，会导致version错乱
                    pkInfo.status = PkStatus.PK_STATUS_READY
                    val readyEvent = PkEvent.PkReadyEvent(
                        pkInfo, PkPushChangeStatus(
                            pkStatus = pkInfo.status,
                            gameMod = pkInfo.gameMod,
                            redSeatList = pkInfo.redTeamInfo.seatList,
                            blueSeatList = pkInfo.blueTeamInfo.seatList,
                            startTime = pkInfo.startTime,
                            endTime = pkInfo.endTime
                        )
                    )
                    HLog.d(TAG, HLog.USR, "pkReadyEvent $readyEvent")
                    update(readyEvent)

                }
            }
            return@IPushIntercept interceptReturn
        }
        VoiceRoomHandler.pushInterceptList.add(intercept)
        pluginTool.onDestroy {
            VoiceRoomHandler.pushInterceptList.remove(intercept)
        }
    }

    private fun observePkEvent() {
        pkViewModel.pkState.observe(pluginTool) {
            when (it) {
                PkViewModel.PkState.SyncState -> {
                    pluginTool.loadView(true)
                    val syncEvent = PkEvent.PkSyncEvent(pkViewModel.getPkInfo())
                    HLog.d(TAG, HLog.USR, "pkSyncEvent $syncEvent")
                    update(syncEvent)
                }

                PkViewModel.PkState.CloseState -> {
                    closePkWidget()
                }

            }
        }
    }

    private fun closePkWidget() {
        val closeEvent = PkEvent.PkCloseEvent(pkViewModel.getPkInfo())
        HLog.d(TAG, HLog.USR, "pkCloseEvent $closeEvent")
        update(closeEvent)
        pluginTool.loadView(false)
        pkViewModel.clear()
    }

    private fun update(pkEvent: PkEvent) {
        pluginTool.getRealView<PkView>()?.update(pkEvent)
    }
}