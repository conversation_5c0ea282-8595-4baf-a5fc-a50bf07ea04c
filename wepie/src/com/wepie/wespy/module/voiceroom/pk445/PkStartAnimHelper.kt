package com.wepie.wespy.module.voiceroom.pk445

import android.content.Context
import android.view.ViewGroup
import android.widget.FrameLayout
import com.huiwan.anim.SVGAUtil
import com.huiwan.base.str.ResUtil
import com.opensource.svgaplayer.SVGACallback
import com.opensource.svgaplayer.SVGAImageView
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IDialogPlugin

object PkStartAnimHelper {

    fun loadStartAnim(context: Context) {
        HLog.d(PkDelegateImpl.TAG, HLog.USR, "loadStartAnim enter")
        val rootView = FrameLayout(context)
        val startSvga = SVGAImageView(context)
        rootView.addView(startSvga, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        rootView.setBackgroundResource(R.color.color_transparent20)
        startSvga.loops = 1
        startSvga.clearsAfterStop = true
        SVGAUtil.playFromUrl(ResUtil.getStr(R.string.pk_start_svga), startSvga)

        VoicePluginService.getPlugin(IDialogPlugin::class.java).add(rootView)
        startSvga.callback = object : SVGACallback {
            override fun onFinished() {
                VoicePluginService.getPlugin(IDialogPlugin::class.java).remove(rootView)
            }

            override fun onPause() = Unit

            override fun onRepeat() = Unit

            override fun onStep(frame: Int, percentage: Double) = Unit
        }
    }
}