package com.wepie.wespy.module.voiceroom.member

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.StringUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.model.gift.Gift
import com.huiwan.configservice.model.voiceroom.MemberPrivilegeType
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.WebApi
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.widget.decoration.SpaceItemDecoration
import com.huiwan.widget.image.DrawableUtil
import com.huiwan.widget.inflate
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog
import com.wepie.wespy.helper.dialog.bottomsheet.WpDragDialog
import com.wepie.wespy.helper.imageLoader.HeadImageLoader
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.information.RoomInfoNewDialogFragment
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender

class VoiceRoomJoinDialog : FrameLayout {

    private val roomIcon: ImageView
    private val roomMemberTitleIv: TextView
    private val roomMemberCountIv: TextView

    private val joinBt: JoinViewHolder

    private var callBack = {}

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    )

    init {
        val dragView = WpDragDialog(context)
        val view = dragView.setContentView(R.layout.voice_room_join_dialog) {
            callBack.invoke()
        }
        view.findViewById<ImageView>(R.id.drag_indicator)
            .setImageDrawable(DrawableUtil.genColorRadius(Color.WHITE, 2))
        view.setBackgroundResource(R.drawable.bg_voice_room_member_dialog)
        this.addView(dragView)

        val memberGroup = ConfigHelper.getInstance().voiceRoomConfig.voiceMemberGroup
        roomIcon = view.findViewById(R.id.voice_room_head_iv)
        roomMemberTitleIv = view.findViewById(R.id.voice_room_member_group_name_tv)
        roomMemberCountIv = view.findViewById(R.id.voice_room_member_count_tv)
        findViewById<View>(R.id.voice_room_member_count_iv).setOnClickListener {
            roomMemberCountIv.performClick()
        }
        view.findViewById<View>(R.id.voice_room_member_help_iv).setOnClickListener {
            ApiService.of(WebApi::class.java)
                ?.showWebDialog(context, memberGroup.helpUrl, 0, 0F)
        }

        val rv = view.findViewById<RecyclerView>(R.id.voice_room_member_privilege_type_rv)
        rv.layoutManager = GridLayoutManager(context, 2)
        rv.adapter = PrivilegeAdapter(memberGroup.privilegeTypes)
        val dp16 = ScreenUtil.dip2px(16F)
        val dp12 = ScreenUtil.dip2px(12F)
        rv.addItemDecoration(SpaceItemDecoration(dp12, 0, dp12, dp16, true))
        joinBt = JoinViewHolder(view.findViewById(R.id.voice_room_member_join_lay))
    }


    private fun update(rid: Int) {
        VoiceRoomPacketSender.getJoinMemberGift(rid, object : LifeSeqCallback(this) {
            override fun onSuccess(head: RspHeadInfo?) {
                val msg = head?.message ?: return
                val rsp = msg as TmpRoomPackets.GetJoinMemberGroupGiftResp
                joinBt.update(rsp.joinMemberGift)
            }

            override fun onFail(head: RspHeadInfo?) {
                ToastUtil.show(head?.desc)
            }
        })
        val info = VoiceRoomService.getInstance().getRoomInfo(rid)
        HeadImageLoader.loadCircleHeadImage(info.headImage, roomIcon)

        roomMemberTitleIv.text = info.name
        roomMemberCountIv.text =
            ResUtil.getStr(R.string.voice_room_members_count_title, info.memberCount)
        roomMemberCountIv.setOnDoubleClick {
            JumpUtil.showVoiceRoomMemberDialog(
                context, rid, VoiceRoomMemberActivity.ACTION_TYPE_MEMBER_LIST,
                VoiceRoomMemberDialog.TAB_MEMBER_SYSYEM, -1
            )
        }

        joinBt.view.setOnClickListener {
            VoiceRoomPacketSender.joinOrExitVoiceRoomMember(
                rid, true, object : LifeSeqCallback(this) {
                    override fun onSuccess(head: RspHeadInfo?) {
                        callBack.invoke()
                        RoomInfoNewDialogFragment.hide()
                        VoiceRoomMemberTaskDialog.show(context)
                    }

                    override fun onFail(head: RspHeadInfo?) {
                        ToastUtil.show(head?.desc)
                    }
                })
        }
    }

    companion object {
        @JvmStatic
        fun show(context: Context, rid: Int) {
            val dialog =
                BaseFullScreenDialog(context, com.huiwan.dialog.R.style.dialog_style_custom)
            val view = VoiceRoomJoinDialog(context)
            view.callBack = {
                dialog.dismiss()
            }
            view.update(rid)
            dialog.setDimAmount(0F)
            dialog.setContentView(view)
            dialog.setCanceledOnTouchOutside(true)
            dialog.initBottomDialog()
            dialog.show()
        }
    }

    class PrivilegeAdapter(private val privilegeTypes: List<MemberPrivilegeType>) :
        RecyclerView.Adapter<PrivilegeViewHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PrivilegeViewHolder {
            return PrivilegeViewHolder(parent.inflate(R.layout.voice_room_member_privilege_type_item))
        }

        override fun getItemCount(): Int = privilegeTypes.size

        override fun onBindViewHolder(holder: PrivilegeViewHolder, position: Int) {
            val type = privilegeTypes[position]
            WpImageLoader.load(type.icon, holder.icon)
            holder.title.text = type.title
        }

    }

    class PrivilegeViewHolder(parent: View) : RecyclerView.ViewHolder(parent) {
        val icon: ImageView = parent.findViewById(R.id.member_privilege_icon)
        val title: TextView = parent.findViewById(R.id.member_privilege_desc)
    }

    class JoinViewHolder(val view: View) {
        private val joinTv: TextView = view.findViewById(R.id.voice_room_member_join_tv)
        private val giftIv: ImageView = view.findViewById(R.id.voice_room_member_gift_iv)
        private val payTv: TextView = view.findViewById(R.id.voice_room_member_pay_tv)
        fun update(giftId: Int) {
            val gift: Gift? = if (giftId <= 0) {
                null
            } else {
                ConfigHelper.getInstance().giftConfig.getGift(giftId)
            }
            if (gift == null) { // 免费
                joinTv.text = ResUtil.getString(R.string.voice_room_members_join)
                giftIv.isVisible = false
                payTv.isVisible = false
            } else {
                joinTv.text =
                    ResUtil.getStr(R.string.voice_room_members_join_with_condition, gift.name)
                giftIv.isVisible = true
                payTv.text = gift.price.toString()
            }
        }

    }
}