package com.wepie.wespy.module.voiceroom.member

import android.content.Context
import android.graphics.Color
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.empty.HWUIEmptyView
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.decorate.DecorHeadImgView
import com.huiwan.decorate.NameTextView
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.user.UserService
import com.huiwan.user.UserSimpleInfoCallback
import com.huiwan.user.entity.UserSimpleInfo
import com.huiwan.widget.rv.BaseRvAdapter
import com.huiwan.widget.rv.RVHolder
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import com.wejoy.weplay.ex.autoCancel
import com.wejoy.weplay.ex.view.toLife
import com.wepie.libimageloader.WpImageLoader
import com.wepie.module.rank.label.RankSubLabelView
import com.wepie.module.rank.label.RankSubLabelView.Theme
import com.wepie.module.rank.label.SubLabelAdapter
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.bottomsheet.WpDragDialog
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender

class VoiceRoomMemberRankView(
    context: Context,
    private val isOwner: Boolean,
    onBack: () -> Unit
) : FrameLayout(context) {
    companion object {
        @JvmStatic
        fun showInDragView(isOwner: Boolean, dragView: WpDragDialog) {
            val rankView = VoiceRoomMemberRankView(dragView.context, isOwner) {
                dragView.pop()
            }
            rankView.toLife().autoCancel {
                dragView.removeExtraScrollView(rankView.memberRankPager)
            }
            dragView.push(rankView) {
                dragView.addExtraScrollView(rankView.memberRankPager)
                rankView.update()
            }
        }
    }

    private val memberRankBack: ImageView
    private val memberRankLabel: RankSubLabelView
    private val memberRankPager: ViewPager2
    private lateinit var memberRankAdapter: MemberRankAdapter

    init {
        View.inflate(context, R.layout.voice_room_member_rank_view, this)
        memberRankBack = findViewById(R.id.member_rank_back)
        memberRankLabel = findViewById(R.id.member_rank_label)
        memberRankPager = findViewById(R.id.member_rank_pager)
        memberRankBack.setOnClickListener {
            onBack.invoke()
        }
        initLabel()
        initViewPager()
    }

    fun memberRankPager(): ViewPager2 {
        return memberRankPager
    }

    fun update() {
        memberRankAdapter.update()
    }

    private fun initLabel() {
        val labels = mutableListOf<SubLabelAdapter.LabelInfo>()
        labels.add(SubLabelAdapter.LabelInfo(ResUtil.getStr(R.string.rank_voice_room_sub_text_2)))
        labels.add(SubLabelAdapter.LabelInfo(ResUtil.getStr(R.string.rank_voice_room_sub_text_3)))
        memberRankLabel.updateTheme(getTheme())
        memberRankLabel.refreshLabel(labels, 0)
        memberRankLabel.setupIndicatorWithViewPager2(memberRankPager)
    }

    private fun getTheme(): Theme {
        val theme = Theme()
        theme.bgColor = Color.parseColor("#FAFAFA")
        theme.subLabelPadding = 0
        theme.indicatorFgPadding = ScreenUtil.dip2px(2f)
        theme.indicatorFgDrawable = ResUtil.getDrawable(R.drawable.shape_ffffff_corner16)
        theme.selectedTextColor = ResUtil.getColor(R.color.color_text_accent_dark)
        theme.unSelectTextColor = ResUtil.getColor(R.color.color_text_primary_ex)
        theme.textSize = 14
        return theme
    }

    private fun initViewPager() {
        memberRankAdapter = MemberRankAdapter()
        memberRankPager.offscreenPageLimit = 1
        memberRankPager.adapter = memberRankAdapter
    }

    inner class MemberRankAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
        private var needUpdate = false
        private val typeMap = mapOf(
            0 to "week",
            1 to "total"
        )

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            val labelRankView = LabelRankView(
                parent.context,
                isOwner,
                VoiceRoomService.getInstance().rid,
                typeMap[viewType]!!
            )
            labelRankView.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            return object : RecyclerView.ViewHolder(labelRankView) {}
        }

        override fun getItemCount(): Int {
            return if (needUpdate) {
                typeMap.size
            } else {
                0
            }
        }

        override fun getItemViewType(position: Int): Int {
            return position
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) = Unit

        fun update() {
            needUpdate = true
            notifyDataSetChanged()
        }
    }

}

class LabelRankView(
    context: Context,
    private val isOwner: Boolean,
    private val rid: Int,
    private val rankType: String
) : FrameLayout(context) {
    private val memberLabelCount: TextView
    private val memberLabelPoint: TextView
    private val memberLabelRefresh: SmartRefreshLayout
    private val memberLabelList: RecyclerView
    private val memberLabelSelfRankLay: ViewGroup
    private val memberEmptyView: HWUIEmptyView
    private val selfRank: LabelRankViewHolder
    private lateinit var labelRankAdapter: LabelRankAdapter

    init {
        inflate(context, R.layout.voice_room_member_label_rank_view, this)
        memberLabelCount = findViewById(R.id.member_label_count)
        memberLabelPoint = findViewById(R.id.member_label_point)
        memberLabelRefresh = findViewById(R.id.member_label_refresh)
        memberLabelList = findViewById(R.id.member_label_list)
        memberLabelSelfRankLay = findViewById(R.id.member_label_self_rank_lay)
        memberEmptyView = findViewById(R.id.member_empty_view)
        selfRank = LabelRankViewHolder(findViewById(R.id.member_label_self_rank))
        initList()
    }

    private fun initList() {
        memberLabelList.layoutManager = LinearLayoutManager(context)
        labelRankAdapter = LabelRankAdapter()
        memberLabelList.adapter = labelRankAdapter
        memberLabelRefresh.setEnableRefresh(false)
        memberLabelRefresh.setOnLoadMoreListener {
            request(labelRankAdapter.itemCount)
        }
        request(0)
    }

    private fun request(offset: Int) {
        VoiceRoomPacketSender.getMemberGroupRank(
            rid,
            rankType,
            offset,
            object : LifeSeqCallback(this) {
                override fun onSuccess(head: RspHeadInfo?) {
                    head?.message ?: return
                    val memberGroupRankResp = head.message as TmpRoomPackets.MemberGroupRankResp
                    labelRankAdapter.add(memberGroupRankResp.rankingList)
                    if (!isOwner) {
                        memberLabelSelfRankLay.isVisible = true
                        selfRank.bind(memberGroupRankResp.selfInfo)
                    }
                    memberLabelCount.text = ResUtil.getStr(
                        R.string.voice_room_members_count_title,
                        memberGroupRankResp.total
                    )
                    if (memberGroupRankResp.total <= labelRankAdapter.itemCount) {
                        memberLabelRefresh.setEnableLoadMore(false)
                    }
                    memberLabelRefresh.finishLoadMore()
                    updateEmptyLayout()
                }

                override fun onFail(head: RspHeadInfo?) {
                    ToastUtil.show(head?.desc)
                    memberLabelRefresh.finishLoadMore()
                    updateEmptyLayout()
                }
            })

    }

    private fun updateEmptyLayout() {
        if (labelRankAdapter.itemCount == 0) {
            memberEmptyView.isVisible = true
            memberLabelCount.isInvisible = true
            memberLabelPoint.isInvisible = true
        } else {
            memberEmptyView.isVisible = false
            memberLabelCount.isVisible = true
            memberLabelPoint.isVisible = true
        }
    }

    private class LabelRankAdapter :
        BaseRvAdapter<TmpRoomPackets.MemberGroupRankItem, LabelRankViewHolder>() {
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LabelRankViewHolder {
            return LabelRankViewHolder(
                LayoutInflater.from(parent.context)
                    .inflate(R.layout.label_rank_item_view, parent, false)
            )
        }

        override fun onBindViewHolder(holder: LabelRankViewHolder, position: Int) {
            holder.bind(getItem(position))
        }
    }

    private class LabelRankViewHolder(itemView: View) : RVHolder(itemView) {
        companion object {
            private val rankLevelIcon = listOf(
                R.drawable.family_new_rank_icon1,
                R.drawable.family_new_rank_icon2,
                R.drawable.family_new_rank_icon3
            )
        }

        private val labelRankTv: TextView
        private val labelRankIv: ImageView
        private val labelHeadIv: DecorHeadImgView
        private val labelNameTv: NameTextView
        private val labelRankMemberLevelIv: ImageView
        private val labelRankMemberPointTv: TextView

        init {
            labelRankTv = itemView.findViewById(R.id.label_rank_tv)
            labelRankIv = itemView.findViewById(R.id.label_rank_iv)
            labelHeadIv = itemView.findViewById(R.id.label_head_iv)
            labelNameTv = itemView.findViewById(R.id.label_name_tv)
            labelRankMemberLevelIv = itemView.findViewById(R.id.label_rank_member_level_iv)
            labelRankMemberPointTv = itemView.findViewById(R.id.label_rank_member_point_tv)
        }

        fun bind(rankItem: TmpRoomPackets.MemberGroupRankItem) {
            val rank = rankItem.rank
            val iconSize = rankLevelIcon.size
            if (rank == 0) {
                labelRankTv.text = "-"
                labelRankTv.isVisible = true
                labelRankIv.isVisible = false
            } else if (rank <= iconSize) {
                labelRankIv.setImageResource(rankLevelIcon[rank - 1])
                labelRankIv.isVisible = true
                labelRankTv.isVisible = false
            } else {
                labelRankTv.text = "$rank"
                labelRankTv.isVisible = true
                labelRankIv.isVisible = false
            }

            UserService.get().getCacheSimpleUser(rankItem.uid, object : UserSimpleInfoCallback {
                override fun onUserInfoSuccess(simpleInfo: UserSimpleInfo) {
                    labelHeadIv.showUserHeadWithDecorationCache(rankItem.uid, simpleInfo.headimgurl)
                    labelNameTv.setUserName(simpleInfo)
                }

                override fun onUserInfoFailed(description: String) {}
            })
            val smallIcon = ConfigHelper.getInstance().voiceRoomConfig.voiceMemberGroup
                .getPrivilegeLevel(rankItem.level)?.smallIcon ?: ""
            if (rankItem.isFreeze && labelRankMemberLevelIv.colorFilter == null) {
                val cm = ColorMatrix()
                cm.setSaturation(0f)
                labelRankMemberLevelIv.colorFilter = ColorMatrixColorFilter(cm)
                labelRankMemberLevelIv.alpha = 0.8f
            } else {
                labelRankMemberLevelIv.clearColorFilter()
                labelRankMemberLevelIv.alpha = 1f
            }
            WpImageLoader.load(smallIcon, labelRankMemberLevelIv)
            labelRankMemberPointTv.text = "${rankItem.score}"
            itemView.setOnClickListener {
                JumpUtil.enterUserInfoDetailFromVoiceRoom(
                    itemView.context,
                    rankItem.uid,
                    VoiceRoomService.getInstance().rid
                )
            }
        }

    }
}
