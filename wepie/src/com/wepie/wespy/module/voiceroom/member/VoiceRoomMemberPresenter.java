package com.wepie.wespy.module.voiceroom.member;

import android.util.SparseIntArray;
import android.view.View;

import androidx.lifecycle.LifecycleOwner;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.ExitMemberGroupEvent;
import com.wepie.wespy.model.event.RoomInfoUpdateEvent;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * Created by geeksammao on 26/10/2017.
 */

public class VoiceRoomMemberPresenter {
    private static final String TAG = "VoiceRoomMemberPresenter";
    private IRoomMember mInterface;
    private ILife life;
    private int rid;
    private int cur_bt_state = 1;
    private int adminMaxCount;
    private static final int BT_STATE_MANAGE = 1;
    private static final int BT_STATE_DELETE = 2;
    public static final int BT_STATE_ADD = 3;

    private final SparseIntArray choosedUidArray = new SparseIntArray();
    private boolean showChoose = false;

    private int totalSize = 0;
    private final List<VoiceRoomInfo.RoomMemberInfo> localMemberInfoList = new ArrayList<>();
    private final Set<Integer> memberFilterSet = new LinkedHashSet<>();

    private int page = 0;
    private int memberPage = 0;

    public VoiceRoomMemberPresenter(IRoomMember mInterface, int rid, int adminMaxCount) {
        this.mInterface = mInterface;
        this.rid = rid;
        this.adminMaxCount = adminMaxCount;
        EventBus.getDefault().register(this);
        if (mInterface instanceof LifecycleOwner) {
            life = ILifeUtil.toLife((LifecycleOwner) mInterface);
        } else if (mInterface instanceof View) {
            life = ILifeUtil.toLife((View) mInterface);
        } else {
            life = null;
        }
    }

    public void clear() {
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onExitMemberGroup(ExitMemberGroupEvent event) {
        for (int uid : event.getExitMemberGroup().getRemoveUidListList()) {
            doDeleteRoomMember(uid);
        }
        mInterface.showManageState(getRoomInfo());
    }

    public void loadData() {
        VoiceRoomPacketSender.getUidList(rid, new LifeSeqCallback(life) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                TmpRoomPackets.GetUidListRsp rsp = (TmpRoomPackets.GetUidListRsp) head.message;
                VoiceRoomService.getInstance().updateUidList(rsp.getUidsList());
                if (rsp.getUidsList() != null) {
                    HLog.d(TAG, HLog.USR, "getUidList size = {}", rsp.getUidsList().size());
                }
                mInterface.showManageState(getRoomInfo());
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    /**
     * 支持分页获取用户信息 支持搜索
     *
     * @param page
     * @param keyword
     */
    private void loadData(int page, String keyword) {
        this.page = page;
        VoiceRoomPacketSender.getUidListWithPage(rid, page, keyword, new LifeSeqCallback(life) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                TmpRoomPackets.GetUidListWithPageRsp rsp = (TmpRoomPackets.GetUidListWithPageRsp) head.message;
                if (rsp == null) {
                    return;
                }
                List<TmpRoomPackets.RoomMemberInfo> onlineUsersList = rsp.getOnlineUsersList();
                if (onlineUsersList == null) {
                    return;
                }
                VoiceRoomService service = VoiceRoomService.getInstance();
                List<Integer> uidList = new ArrayList<>();
                for (TmpRoomPackets.RoomMemberInfo onlineUser : onlineUsersList) {
                    service.addMemberCache(VoiceRoomInfo.RoomMemberInfo.parse(onlineUser));
                    uidList.add(onlineUser.getUid());
                }
                VoiceRoomService.getInstance().updateUidList(uidList);
                if (keyword == null || TextUtil.isEmpty(keyword)) {
                    mInterface.showManageState(getRoomInfo());
                    mInterface.enableLoadMore(getRoomInfo().uidList.size() < rsp.getTotal());
                } else {
                    mInterface.showSearchResult(rsp.getTotal(), rsp.getUidsList(), page <= 0);
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    /**
     * 直接从头加载
     *
     * @param keyword
     */
    public void loadData(String keyword) {
        loadData(0, keyword);
    }

    /**
     * 加载更多
     *
     * @param keyword
     */
    public void loadDataMore(String keyword) {
        loadData(++page, keyword);
    }


    /**
     * 获取成员体系中成员列表信息
     *
     * @param memberPage
     * @param size
     */
    private void loadMemberData(int memberPage, int size) {
        this.memberPage = memberPage;
        VoiceRoomPacketSender.getRoomMemberList(rid, memberPage, size, new LifeSeqCallback(life) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                TmpRoomPackets.RoomMemberListResp rsp = (TmpRoomPackets.RoomMemberListResp) head.message;
                List<TmpRoomPackets.RoomMemberInfo> memberList = rsp.getMembersList();
                if (memberPage == 0) {
                    memberFilterSet.clear();
                    localMemberInfoList.clear();

                }
                localMemberInfoList.addAll(filterMemberList(memberList));
                totalSize = rsp.getTotal();
                mInterface.showManageState(getRoomInfo());
                mInterface.enableLoadMore(localMemberInfoList.size() < rsp.getTotal());
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private List<VoiceRoomInfo.RoomMemberInfo> filterMemberList(List<TmpRoomPackets.RoomMemberInfo> memberList) {
        List<VoiceRoomInfo.RoomMemberInfo> list;
        if (memberList == null || memberList.isEmpty()) {
            list = Collections.emptyList();
        } else {
            VoiceRoomService service = VoiceRoomService.getInstance();
            list = new ArrayList<>(memberList.size());
            for (TmpRoomPackets.RoomMemberInfo info : memberList) {
                VoiceRoomInfo.RoomMemberInfo memberInfo = VoiceRoomInfo.RoomMemberInfo.parse(info);
                if (memberFilterSet.add(info.getUid())) {
                    if (memberInfo != null) {
                        list.add(memberInfo);
                    }
                }
                service.addMemberCache(memberInfo);
            }
        }
        return list;
    }

    public void loadMemberData(int size) {
        loadMemberData(0, size);
    }

    public void loadMemberDataMore(int size) {
        loadMemberData(++memberPage, size);
    }

    public void loadBlackList() {
        if (getRoomInfo().blackList.size() == 0) {
            getBlackList(new LifeSeqCallback(life) {
                @Override
                public void onSuccess(RspHeadInfo head) {
                    TmpRoomPackets.GetBlackListRsp getBlackListRsp = (TmpRoomPackets.GetBlackListRsp) head.message;
                    getRoomInfo().blackList.clear();
                    getRoomInfo().blackList.addAll(getBlackListRsp.getUidsList());
                    mInterface.showManageState(getRoomInfo());
                }

                @Override
                public void onFail(RspHeadInfo head) {

                }
            });
        }
    }

    /**
     * 取消管理
     */
    public void cancelManager() {
        cur_bt_state = BT_STATE_MANAGE;
        showChoose = false;
        if (getRoomInfo().uidList.size() == 0) {
            loadData("");
        } else {
            mInterface.showManageState(getRoomInfo());
        }
    }

    /**
     * 取消管理
     */
    public void cancelManagerWithDoNothing() {
        cur_bt_state = BT_STATE_MANAGE;
        showChoose = false;
    }

    /**
     * 当item被点击后要干什么
     *
     * @param uid
     */
    public void onItemClick(int actionType, int uid) {
        mInterface.onItemClick(actionType, uid);
    }

    public void clickManagerBt() {
        if (cur_bt_state == BT_STATE_MANAGE) {
            cur_bt_state = BT_STATE_DELETE;
            showChoose = true;
            mInterface.showDeleteState(getRoomInfo());
        } else if (cur_bt_state == BT_STATE_DELETE) {
            if (choosedUidArray.size() > 0) {
                int size = choosedUidArray.size();
                boolean containsPk = false;
                VoiceRoomInfo.PkInfo pkInfo = getRoomInfo().pkInfo;
                for (int i = 0; i < size; i++) {
                    if (pkInfo.isInPk(choosedUidArray.keyAt(i))) {
                        containsPk = true;
                        break;
                    }
                }
                String txt;
                if (containsPk) {
                    txt = ResUtil.getStr(R.string.voice_room_members_op_del_confirm_tip_of_pk, size);
                } else {
                    txt = ResUtil.getStr(R.string.voice_room_members_op_del_confirm_tip, size);
                }
                if (mInterface instanceof VoiceRoomMemberSystemView) {
                    txt = ResUtil.getStr(R.string.voice_room_members_remove_num, size);
                }
                mInterface.showKickDialog(txt, size);
            } else {
                cancelManager();
            }
        } else if (cur_bt_state == BT_STATE_ADD) {
            VoiceSettingAddAdminDialog.Companion.show(mInterface.getContextAsActivity(), rid);
        }
    }

    private VoiceRoomInfo getRoomInfo() {
        return VoiceRoomService.getInstance().getRoomInfo(rid);
    }

    public boolean isUserChoosed(int uid) {
        return choosedUidArray.indexOfKey(uid) >= 0;
    }

    public boolean showChoose() {
        return showChoose;
    }

    public void setShowChoose(boolean flag) {
        showChoose = flag;
    }

    public void chooseUser(int uid) {
        choosedUidArray.put(uid, uid);
        mInterface.onChooseChange(choosedUidArray.size());
    }

    public void unChooseUser(int uid) {
        choosedUidArray.delete(uid);
        mInterface.onChooseChange(choosedUidArray.size());
    }

    public void doKickUsers(boolean isBlock) {
        final ArrayList<Integer> kickUids = getKickUids();
        VoiceRoomPacketSender.kickUsers(rid, kickUids, isBlock, new LifeSeqCallback(life) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                choosedUidArray.clear();
                VoiceRoomService.getInstance().kickUsers(rid, kickUids);
                mInterface.afterKickUser(kickUids);
//                loadData("");
                mInterface.showManageState(getRoomInfo());
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public void batchDeleteRoomMember(boolean isBlock) {
        final ArrayList<Integer> batchDeleteUids = getKickUids();
        VoiceRoomPacketSender.batchDeleteRoomMember(rid, batchDeleteUids, isBlock, new LifeSeqCallback(life) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                choosedUidArray.clear();
                for (int uid : batchDeleteUids) {
                    doDeleteRoomMember(uid);
                }
                mInterface.afterKickUser(batchDeleteUids);
                mInterface.showManageState(getRoomInfo());
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    public void deleteRoomMember(int uid) {
        VoiceRoomPacketSender.batchDeleteRoomMember(rid, Collections.singletonList(uid), false, new LifeSeqCallback(life) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                doDeleteRoomMember(uid);
                mInterface.showManageState(getRoomInfo());
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private void doDeleteRoomMember(int uid) {
        Iterator<VoiceRoomInfo.RoomMemberInfo> iterator = localMemberInfoList.iterator();
        while (iterator.hasNext()) {
            VoiceRoomInfo.RoomMemberInfo memberInfo = iterator.next();
            if (memberInfo.memberUid == uid) {
                iterator.remove();
                break;
            }
        }
        memberFilterSet.remove(uid);
        VoiceRoomService.getInstance().removeMemberCache(uid);
    }

    public IRoomMember getmInterface() {
        return mInterface;
    }

    public void getBlackList(LifeSeqCallback seqCallback) {
        VoiceRoomPacketSender.getBlackList(rid, seqCallback);
    }

    public ArrayList<Integer> getKickUids() {
        ArrayList<Integer> array = new ArrayList<Integer>();
        int size = choosedUidArray.size();
        for (int i = 0; i < size; i++) {
            array.add(choosedUidArray.valueAt(i));
        }
        return array;
    }

    public List<VoiceRoomInfo.RoomMemberInfo> getMemberInfoList() {
        return localMemberInfoList;
    }

    public void onRoomInfoUpdate(RoomInfoUpdateEvent event) {
        if (cur_bt_state == BT_STATE_MANAGE) {
            mInterface.showManageState(getRoomInfo());
        } else if (cur_bt_state == BT_STATE_DELETE) {
            mInterface.showDeleteState(getRoomInfo());
        }
    }

    public void setCur_bt_state(int cur_bt_state) {
        this.cur_bt_state = cur_bt_state;
    }

    public int getRid() {
        return rid;
    }

    /**
     * 清空已选择的用户id
     */
    public void clearChooseUids() {
        choosedUidArray.clear();
    }

}
