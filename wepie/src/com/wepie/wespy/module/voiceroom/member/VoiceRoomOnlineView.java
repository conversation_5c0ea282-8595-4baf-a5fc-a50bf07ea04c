package com.wepie.wespy.module.voiceroom.member;

import android.app.Activity;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.ui.dialog.HWUIDialogBuilder;
import com.huiwan.base.ui.empty.HWUIEmptyView;
import com.huiwan.base.util.TextUtil;
import com.huiwan.component.activity.BaseFragment;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.voiceroom.AdvanceRoomLevelConfig;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.voice.AdminChangeEvent;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

/**
 * 语音房在线管理 重构
 *
 * @date 2022/3/30
 */
public class VoiceRoomOnlineView extends BaseFragment implements IRoomMember {

    private VoiceRoomMemberPresenter presenter;
    private RecyclerView rvView;
    private EditText mSearchCellEdit;
    private ImageView mSearchCellBt;
    private VoiceRoomMemberViewAdapter adapter;
    private HWUIEmptyView emptyView;
    private int rid;
    private int actionType;
    private int adminMaxCount;

    private TextView mTitleLeftTv;
    private TextView mTitleCenterTv;
    private TextView mTitleRightTv;

    private ConstraintLayout titleLayout;

    private String searchStr = "";

    private MemberListener memberListener;
    private SmartRefreshLayout mRoomMemberListRefresher;

    private VoiceMemberViewModel model;

    public void setMemberListener(MemberListener memberListener) {
        this.memberListener = memberListener;
    }

    private Handler debounceHandler;
    //搜索延迟200ms
    private final int DEBOUNCE_TIME = 200;
    private final int MSG_DEBOUNCE_SEARCH = 0;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        model = new ViewModelProvider(requireParentFragment()).get(VoiceMemberViewModel.class);
        rid = model.getRid();
        actionType = model.getActionType();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.voice_room_online_view, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initViews(view);
        initData();
    }

    public void initViews(View view) {
        mSearchCellEdit = view.findViewById(R.id.search_cell_edit);
        mSearchCellEdit.setHint(R.string.friend_list_search_user_hint);
        mSearchCellBt = view.findViewById(R.id.search_cell_bt);

        rvView = view.findViewById(R.id.room_member_list_view);
        emptyView = view.findViewById(R.id.voice_room_member_empty_view);

        titleLayout = view.findViewById(R.id.member_center_title_layout);
        mTitleLeftTv = view.findViewById(R.id.title_left_tv);
        mTitleCenterTv = view.findViewById(R.id.title_center_tv);
        mTitleRightTv = view.findViewById(R.id.title_right_tv);

        mRoomMemberListRefresher = view.findViewById(R.id.room_member_list_refresher);

        rvView.setLayoutManager(new LinearLayoutManager(getContext()));

        mSearchCellEdit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                searchStr = s.toString();
                mSearchCellBt.setVisibility(TextUtils.isEmpty(searchStr) ? View.INVISIBLE : View.VISIBLE);
                if (debounceHandler != null) {
                    debounceHandler.removeMessages(MSG_DEBOUNCE_SEARCH);
                    debounceHandler.sendEmptyMessageDelayed(MSG_DEBOUNCE_SEARCH, DEBOUNCE_TIME);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });

        mSearchCellBt.setOnClickListener(v -> mSearchCellEdit.setText(""));
        mTitleLeftTv.setOnClickListener(v -> {
            removeFocus();
            presenter.clearChooseUids();
            onChooseChange(-1);
            cancelManage();
            model.setManageState(false); //取消管理回调
        });

        mRoomMemberListRefresher.setOnLoadMoreListener(v -> {
            presenter.loadDataMore(searchStr);
        });
        mSearchCellEdit.addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(View v) {
                debounceHandler = new Handler(Looper.getMainLooper()) {
                    @Override
                    public void handleMessage(@NonNull Message msg) {
                        if (msg.what == MSG_DEBOUNCE_SEARCH) {
                            doSearch();
                        }
                    }
                };
            }

            @Override
            public void onViewDetachedFromWindow(View v) {
                if (debounceHandler != null) {
                    debounceHandler.removeCallbacksAndMessages(null);
                }
            }
        });
        rvView.setOnTouchListener((v, event) -> {
            if (memberListener != null) {
                memberListener.onListTouch();
            }
            return false;
        });
    }

    public void initData() {
        EventBus.getDefault().register(this);
        model.getOnlineViewManageLiveData().observe(getViewLifecycleOwner(), flag -> {
            if (flag) {
                clickManageBt();
            } else {
                cancelManage();
            }
        });

        int inviteSeatNum = model.getSeatNum();

        adminMaxCount = VoiceRoomMemberActivity.DEFAULT_MAX_ADMIN_NUM;
        VoiceRoomInfo info = VoiceRoomService.getInstance().getRoomInfo(rid);
        if (info.isAdvancedOrAnchorOrFamilyRoom()) {
            AdvanceRoomLevelConfig config = ConfigHelper.getInstance().getVoiceRoomConfig().getAdvancedRoomConfig().getConfigByLevel(info.advanceRoomLevel);
            if (config != null) {
                adminMaxCount = config.getAdminNum();
            }
        }

        presenter = new VoiceRoomMemberPresenter(this, rid, adminMaxCount);
        adapter = new VoiceRoomMemberViewAdapter(getContext(), presenter, actionType, inviteSeatNum);
        rvView.setAdapter(adapter);

        presenter.loadData("");

        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_BLACK) {
            presenter.loadBlackList();
        }
        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_DRAWER
                || actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE) {
            mTitleRightTv.setVisibility(View.GONE);
            titleLayout.setVisibility(View.VISIBLE);
        }
        showTitle(info);
    }

    /**
     * 搜索
     */
    public void doSearch() {
        presenter.loadData(searchStr);
    }

    /**
     * 更新title
     *
     * @param roomInfo
     */
    private void showTitle(VoiceRoomInfo roomInfo) {
        boolean isOwner = roomInfo.isSelfOwner();
        boolean isAdmin = roomInfo.isSelfAdmin();

        mTitleRightTv.setVisibility((actionType == VoiceRoomMemberActivity.ACTION_TYPE_MEMBER_LIST) && (isOwner || isAdmin) ? View.VISIBLE : View.GONE);

        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_MEMBER_LIST) {
            mTitleCenterTv.setText(ResUtil.getStr(R.string.online_text));
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE) {
            mTitleCenterTv.setText(R.string.voice_room_members_title_sel_to_mic);
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN) {
            mTitleCenterTv.setText(ResUtil.getStr(R.string.voice_room_members_title_sel_admin_d_d, roomInfo.adminList.size(), adminMaxCount));
            mTitleRightTv.setText(R.string.add);
            mTitleRightTv.setVisibility(View.VISIBLE);
            presenter.setCur_bt_state(VoiceRoomMemberPresenter.BT_STATE_ADD);
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_DRAWER) {
            mTitleCenterTv.setText(R.string.voice_room_members_title_sel_to_draw);
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_BLACK) {
            mTitleCenterTv.setText(R.string.voice_room_members_title_block);
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_FAMILY_ADMIN) {
            mTitleCenterTv.setText(R.string.voice_room_members_title_sel_family_admin);
        }
    }

    @Override
    public Activity getContextAsActivity() {
        return requireActivity();
    }

    /**
     * 外部点击管理按钮
     */
    public void clickManageBt() {
        presenter.clickManagerBt();
        titleLayout.setVisibility(View.VISIBLE);
    }

    /**
     * 移出搜索框的焦点
     */
    public void removeFocus() {
        mSearchCellEdit.clearFocus();
    }

    /**
     * 取消管理
     */
    public void cancelManage() {
        presenter.cancelManagerWithDoNothing();
        adapter.notifyDataSetChanged();
        titleLayout.setVisibility(View.GONE);
    }

    @Override
    public void onChooseChange(int num) {
        mTitleRightTv.setEnabled(num > 0);
        mTitleRightTv.setTextColor(Color.parseColor(num > 0 ? "#FF4D5C" : "#FFABAA"));
        mTitleRightTv.setOnClickListener(v -> presenter.clickManagerBt());
    }

    @Override
    public void showSearchResult(int total, List<Integer> uids, boolean clear) {
        checkShowEmpty(adapter.updateSearch(uids, clear));
        mRoomMemberListRefresher.finishLoadMore();
        enableLoadMore(adapter.getItemCount() < total);
    }

    @Override
    public void enableRefresh(boolean enable) {
        mRoomMemberListRefresher.setEnableRefresh(enable);
    }

    @Override
    public void enableLoadMore(boolean enable) {
        mRoomMemberListRefresher.setEnableLoadMore(enable);
    }

    @Override
    public void onItemClick(int actionType, int uid) {
        model.onItemClick(uid, actionType);
    }

    @Override
    public void afterKickUser(List<Integer> kickUids) {
        //踢人成功后,置灰左上角操作键
        onChooseChange(-1);
    }

    @Override
    public void showManageState(VoiceRoomInfo roomInfo) {
        checkShowEmpty(adapter.update(roomInfo));
        mRoomMemberListRefresher.finishLoadMore();
        enableLoadMore(true);
    }


    @Override
    public void showDeleteState(VoiceRoomInfo roomInfo) {
        adapter.notifyDataSetChanged();
    }

    @Override
    public void showKickDialog(String txt, int size) {
        if (VoiceRoomService.getInstance().getRoomInfo(rid).isAdvancedOrAnchorOrFamilyRoom()) {
            HWUIDialogBuilder.newBuilder(getContext())
                    .setSingleBtn(false)
                    .setTitle(txt)
                    .setCancelTx(R.string.cancel)
                    .setSureTx(R.string.sure)
                    .setCanCancel(true)
                    .setSelectLabel(R.string.voice_room_members_op_del_ext_tip)
                    .setDialogCallback(new HWUIDialogBuilder.DialogCallback() {
                        @Override
                        public void onClickSure() {
                        }

                        @Override
                        public void onSelectSure(boolean checked) {
                            presenter.doKickUsers(checked);
                        }
                    })
                    .show();
        } else {
            HWUIDialogBuilder.newBuilder(getContext())
                    .setSingleBtn(false)
                    .setContent(txt)
                    .setSureTx(R.string.sure)
                    .setCancelTx(R.string.cancel)
                    .setDialogCallback(() -> presenter.doKickUsers(false)).show();
        }
    }

    private void checkShowEmpty(int num) {
        String tips = "";
        int type = HWUIEmptyView.base_empty_no_data;
        if (searchStr != null && !TextUtil.isEmpty(searchStr)) {
            tips = ResUtil.getStr(R.string.search_empty_result);
            type = HWUIEmptyView.base_empty_find_empty;
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN) {
            tips = ResUtil.getStr(R.string.voice_room_members_admin_none);
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_BLACK) {
            tips = ResUtil.getStr(R.string.voice_room_members_block_none);
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE) {
            tips = ResUtil.getStr(R.string.none);
        }
        if (!TextUtil.isEmpty(tips) && num == 0) {
            emptyView.setText(tips);
            emptyView.setType(type);
            emptyView.setVisibility(View.VISIBLE);
            rvView.setVisibility(View.GONE);
        } else {
            emptyView.setVisibility(View.GONE);
            rvView.setVisibility(View.VISIBLE);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onListChange(final AdminChangeEvent event) {
        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN) {
            presenter.loadData("");
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (presenter != null) {
            presenter.clear();
        }
        EventBus.getDefault().unregister(this);
    }

    /**
     * 成员列表回调
     */
    public interface MemberListener {

        /**
         * 当列表被触摸的时候 主要用于隐藏键盘
         */
        default void onListTouch() {
        }
    }
}
