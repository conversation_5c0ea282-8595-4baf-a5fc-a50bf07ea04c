package com.wepie.wespy.module.voiceroom.member

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.decorate.CharmManager
import com.huiwan.decorate.DecorHeadImgView
import com.huiwan.decorate.NameTextView
import com.huiwan.decorate.vip.VipLabelView
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.IMedalApi
import com.huiwan.lib.api.plugins.IMedalApi.IMedalListView
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.user.UserService
import com.huiwan.user.UserSimpleInfoCallback
import com.huiwan.user.entity.UserSimpleInfo
import com.huiwan.widget.BlackWhiteFrameLayout
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.family.FamilyUiConst
import com.wepie.wespy.module.family.main.mine.family.FamilyMainInterface
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets.BatchGetMemberInfoResp
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender


class VoiceRoomMemberSystemBaseItem @JvmOverloads constructor(
    private val mContext: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(mContext, attrs, defStyleAttr) {

    private var layoutRole: ConstraintLayout? = null
    private var tagView: ImageView? = null
    private var lineView: View? = null
    private var micMarkImv: ImageView? = null

    private var headImage: DecorHeadImgView? = null
    var nickTv: NameTextView? = null
    private var genderIv: ImageView? = null
    private lateinit var memberLevelLay: BlackWhiteFrameLayout
    private lateinit var memberLevelIv: ImageView
    private var tagLay: LinearLayout? = null
    private var familyOrWeddingRoleTv: TextView? = null
    private var vipLabelView: VipLabelView? = null
    private var charmIv: ImageView? = null
    private var memberMedalLay: LinearLayout? = null
    private var medalListView: IMedalListView? = null

    init {
        init()
    }

    private fun init() {
        LayoutInflater.from(mContext)
            .inflate(R.layout.voice_room_member_system_base_item, this, true)

        layoutRole = findViewById(R.id.layout_role)
        tagView = findViewById(R.id.owner_iv)
        lineView = findViewById(R.id.line_iv)
        micMarkImv = findViewById(R.id.mic_mark_imv)

        headImage = findViewById(R.id.head_imv)
        nickTv = findViewById(R.id.name_tv)
        genderIv = findViewById(R.id.gender_iv)
        memberLevelLay = findViewById(R.id.member_level_lay)
        memberLevelIv = findViewById(R.id.member_level_iv)
        familyOrWeddingRoleTv = findViewById(R.id.family_role_tv)
        medalListView = ApiService.of(IMedalApi::class.java).genMedalListView(mContext)
        vipLabelView = findViewById(R.id.member_vip_label)
        charmIv = findViewById(R.id.member_charm_iv)
        memberMedalLay = findViewById(R.id.member_medal_lay)
        tagLay = findViewById(R.id.tag_lay)
    }

    fun update(roomInfo: VoiceRoomInfo?, uid: Int, showTag: Boolean, actionType: Int) {
        if (roomInfo == null) {
            return
        }
        if (showTag) {
            val isOwner = roomInfo.isOwner(uid)
            val role = roomInfo.familyRoleInfo[uid]
            micMarkImv?.visibility = VISIBLE
            tagView?.visibility = VISIBLE
            if (roomInfo.roomType == VoiceRoomInfo.ROOM_TYPE_WEDDING) { //婚礼房
                val weddingInfo = roomInfo.weddingInfo
                familyOrWeddingRoleTv?.visibility = VISIBLE
                if (uid == weddingInfo.weddingHost) { //司仪
                    familyOrWeddingRoleTv?.background =
                        ResUtil.getDrawableWithTintValue(R.drawable.shape_edaf33_corner4, -0x2461)
                    familyOrWeddingRoleTv?.setText(R.string.mc_people)
                } else if (uid == weddingInfo.brideUid) { //新娘
                    familyOrWeddingRoleTv?.background =
                        ResUtil.getDrawableWithTintValue(R.drawable.shape_edaf33_corner4, -0x1d7d3)
                    familyOrWeddingRoleTv?.setText(R.string.bride)
                } else if (uid == weddingInfo.groomUid) { //新郎
                    familyOrWeddingRoleTv?.background =
                        ResUtil.getDrawableWithTintValue(R.drawable.shape_edaf33_corner4, -0x1d7d3)
                    familyOrWeddingRoleTv?.setText(R.string.groom)
                } else if (weddingInfo.groomsmanList.contains(uid)) { //伴郎
                    familyOrWeddingRoleTv?.background =
                        ResUtil.getDrawableWithTintValue(R.drawable.shape_edaf33_corner4, -0x673801)
                    familyOrWeddingRoleTv?.setText(R.string.best_man)
                } else if (weddingInfo.bridesmaidList.contains(uid)) { //伴娘
                    familyOrWeddingRoleTv?.background =
                        ResUtil.getDrawableWithTintValue(R.drawable.shape_edaf33_corner4, -0x673801)
                    familyOrWeddingRoleTv?.setText(R.string.maid_of_honor)
                } else {
                    familyOrWeddingRoleTv?.visibility = GONE
                }
                tagView?.visibility = GONE
                lineView?.visibility = GONE
            } else {
                if (isOwner) {
                    tagView?.setImageResource(R.drawable.voice_room_owner_icon)
                } else if (roomInfo.adminList.contains(uid) || role == FamilyMainInterface.ROLE_OWNER || role == FamilyMainInterface.ROLE_SUB_OWNER || role == FamilyMainInterface.ROLE_ELDER) {
                    tagView?.setImageResource(R.drawable.voice_room_admin_icon)
                } else if (VoiceRoomService.getInstance().isMemberFromOnlineList(uid)) {
                    tagView?.setImageResource(R.drawable.voice_room_member_icon)
                } else {
                    tagView?.visibility = GONE
                    lineView?.visibility = GONE
                }
                val roleID = roomInfo.familyRoleInfo[uid]
                val iconRes = FamilyUiConst.getRoleBg(roleID)
                val roleStr = FamilyUiConst.getRoleStr(roleID)
                if (iconRes > 0) {
                    familyOrWeddingRoleTv?.visibility = VISIBLE
                    familyOrWeddingRoleTv?.setBackgroundResource(iconRes)
                    familyOrWeddingRoleTv?.text = roleStr
                } else {
                    familyOrWeddingRoleTv?.visibility = GONE
                }
            }
            if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN || actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_FAMILY_ADMIN) {
                hideMicIv()
            } else {
                if (roomInfo.isUserSeated(uid)) {
                    micMarkImv?.visibility = VISIBLE
                } else {
                    hideMicIv()
                }
            }
            if (tagView?.isVisible == true && micMarkImv?.isVisible == true) {
                lineView?.visibility = VISIBLE
            }
            if (tagView?.isVisible == false && micMarkImv?.isVisible == false) {
                layoutRole?.visibility = GONE
            } else {
                layoutRole?.visibility = VISIBLE
            }
        } else {
            layoutRole?.visibility = GONE
            familyOrWeddingRoleTv?.visibility = GONE
        }
        //更新用户信息
        updateUser(uid)
    }

    private fun initCenter(showCenter: Boolean) {
        if (showCenter) {
            tagLay?.visibility = GONE
        } else {
            tagLay?.visibility = VISIBLE
        }
    }

    fun updateMemberLevelByUid(roomInfo: VoiceRoomInfo?, uid: Int) {
        if (roomInfo == null) {
            return
        }
        val info =
            VoiceRoomService.getInstance().getMemberInfo(uid) ?: VoiceRoomInfo.RoomMemberInfo()
        if (info.memberUid == -1) {
            VoiceRoomPacketSender.getMemberInfoReq(
                roomInfo.rid,
                uid,
                object : LifeSeqCallback(this) {
                    override fun onSuccess(head: RspHeadInfo) {
                        val resp = head.message as BatchGetMemberInfoResp
                        if (resp.memberInfoListList != null && resp.memberInfoListList.size > 0) {
                            val roomMemberInfo = VoiceRoomService.getInstance()
                                .addMemberCache(resp.getMemberInfoList(0))
                            if (roomMemberInfo.getLevel() > 0) {
                                updateMemberLevel(
                                    roomMemberInfo.getLevel(),
                                    roomMemberInfo.getIsFreeze()
                                )
                            }
                        }
                    }

                    override fun onFail(head: RspHeadInfo) {
                        ToastUtil.show(head.desc)
                    }
                })
        } else {
            updateMemberLevel(info.getLevel(), info.isFreeze)
        }
    }

    fun updateMemberLevel(level: Int, isFrozen: Boolean) {
        val levelInfo =
            ConfigHelper.getInstance().voiceRoomConfig.voiceMemberGroup.getPrivilegeLevel(level)
        var url = ""
        if (levelInfo != null) {
            url = levelInfo.smallIcon
        }
        if (isFrozen) {
            memberLevelLay.setBlackWhiteEnable(true)
            memberLevelIv.alpha = 0.8f
        } else {
            memberLevelLay.setBlackWhiteEnable(false)
            memberLevelIv.alpha = 1f
        }
        if (TextUtils.isEmpty(url)) {
            memberLevelIv.setImageDrawable(null)
        } else {
            WpImageLoader.load(url, memberLevelIv)
        }
    }

    fun hideMicMarkShowMember(
        roomInfo: VoiceRoomInfo?,
        uid: Int
    ) {
        if (roomInfo == null) {
            return
        }
        micMarkImv?.visibility = GONE
        lineView?.visibility = GONE
        tagView?.visibility = VISIBLE
        layoutRole?.visibility = VISIBLE
        val isOwner = roomInfo.isOwner(uid)
        val role = roomInfo.familyRoleInfo[uid]
        if (isOwner) {
            tagView?.setImageResource(R.drawable.voice_room_owner_icon)
        } else if (roomInfo.adminList.contains(uid) || role == FamilyMainInterface.ROLE_OWNER || role == FamilyMainInterface.ROLE_SUB_OWNER || role == FamilyMainInterface.ROLE_ELDER) {
            tagView?.setImageResource(R.drawable.voice_room_admin_icon)
        } else {
            tagView?.setImageResource(R.drawable.voice_room_member_icon)
        }
    }

    private fun updateUser(uid: Int) {
        UserService.get().getCacheSimpleUser(uid, object : UserSimpleInfoCallback {
            override fun onUserInfoSuccess(userInfo: UserSimpleInfo) {
                //所有信息都为空
                headImage?.showUserHeadWithDecorationCache(uid, userInfo.headimgurl)
                val allZero = CharmManager.getInstance()
                    .getCharmLevel(userInfo.flower) == 0 && userInfo.vip == 0 && userInfo.wearMedals.size == 0
                //当所有信息都为空的时候,即使有tag也居中显示
                initCenter(allZero)
                nickTv?.setUserName(userInfo)
                vipLabelView?.setVipLevel(userInfo.vip)
                CharmManager.checkShowShortCharm(charmIv, userInfo.flower)
                if (memberMedalLay?.childCount == 0) {
                    memberMedalLay?.addView(medalListView?.view)
                }
                medalListView?.updateIconSize(ScreenUtil.dip2px(22f))
                medalListView?.updateMedalList(userInfo.wearMedals)
                genderIv?.visibility = VISIBLE
                if (userInfo.isFemale) {
                    genderIv?.setImageResource(R.drawable.wejoy_home_friend_list_female)
                } else if (userInfo.isMale) {
                    genderIv?.setImageResource(R.drawable.wejoy_home_friend_list_male)
                } else {
                    genderIv?.setImageDrawable(null)
                    genderIv?.visibility = GONE
                }
            }

            override fun onUserInfoFailed(description: String) = Unit
        })
    }

    private fun hideMicIv() {
        micMarkImv?.visibility = GONE
        lineView?.visibility = GONE
    }
}