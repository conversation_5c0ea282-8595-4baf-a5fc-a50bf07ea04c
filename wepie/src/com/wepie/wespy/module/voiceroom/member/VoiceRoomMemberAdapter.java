package com.wepie.wespy.module.voiceroom.member;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ListView;

import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.contact.friendlist.SearchResultView;
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity;

import java.util.ArrayList;

/**
 * Created by geeksammao on 26/10/2017.
 */

public class VoiceRoomMemberAdapter extends BaseAdapter {
    private static final int TYPE_DEFAULT = 0;
    private static final int TYPE_FAMILY_ADMIN_TITLE = 1;
    private static final int FAMILY_TITLE_ID = -1;
    private static final int NONE_DATA_ID = -2;
    private static final int TYPE_NONE = 2;
    private final ListView listView;
    private final int actionType;
    private final int inviteSeatNum;
    private Context mContext;
    private VoiceRoomMemberPresenter presenter;
    private ArrayList<Integer> uidList = new ArrayList<>();
    private VoiceRoomInfo roomInfo;
    private SearchResultView searchResultView;
    private boolean needSearch;

    public VoiceRoomMemberAdapter(Context mContext, VoiceRoomMemberPresenter presenter, ListView listView, SearchResultView searchView, int actionType, int inviteSeatNum) {
        this.mContext = mContext;
        this.presenter = presenter;
        this.listView = listView;
        this.searchResultView = searchView;
        this.actionType = actionType;
        this.inviteSeatNum = inviteSeatNum;
        needSearch = !(actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN || actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_BLACK);
    }

    public int update(VoiceRoomInfo roomInfo) {
        this.roomInfo = roomInfo;
        this.uidList.clear();
        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN) {
            this.uidList.addAll(roomInfo.adminList);
            if (roomInfo.isFamilyRoom()) {
                if (this.uidList.isEmpty()) {
                    uidList.add(NONE_DATA_ID);
                }
                this.uidList.add(FAMILY_TITLE_ID);
                this.uidList.addAll(roomInfo.familyAdminList);
            }
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE) {
            this.uidList.addAll(roomInfo.uidList);
            this.uidList.remove(Integer.valueOf(roomInfo.owner));
            this.uidList.remove(Integer.valueOf(roomInfo.getSeatInfoByNum(inviteSeatNum).uid));
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_DRAWER) {
            this.uidList.addAll(roomInfo.uidList);
            if (roomInfo.isDrawing && roomInfo.drawGameInfo != null) {
                int uid = roomInfo.drawGameInfo.getDrawUid();
                if (uid > 0) {
                    this.uidList.remove(Integer.valueOf(uid));
                }
            }
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_BLACK) {
            this.uidList.addAll(roomInfo.blackList);
        } else {
            this.uidList.addAll(roomInfo.uidList);
        }
        if (needSearch) {
            uidList.add(0, 0);
        }
        notifyDataSetChanged();
        return uidList.size();
    }

    @Override
    public int getCount() {
        return uidList.size();
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return i;
    }

    @Override
    public int getItemViewType(int position) {
        if (uidList.get(position) == FAMILY_TITLE_ID) {
            return TYPE_FAMILY_ADMIN_TITLE;
        } else if (uidList.get(position) == NONE_DATA_ID) {
            return TYPE_NONE;
        }
        return TYPE_DEFAULT;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup viewGroup) {
        if (getItemViewType(position) == TYPE_FAMILY_ADMIN_TITLE) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.family_admin_title, null);
            return convertView;
        }
        if (getItemViewType(position) == TYPE_NONE) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.family_admin_none_view, null);
            return convertView;
        }
        if (convertView == null) {
            if (position == 0 && needSearch) {
                convertView = LayoutInflater.from(mContext).inflate(R.layout.friend_list_item_search, null);
            } else {
                convertView = new VoiceRoomMemberItem(mContext, presenter);
            }
        } else {
            if (position == 0 && (convertView instanceof VoiceRoomMemberItem) && needSearch) {
                convertView = LayoutInflater.from(mContext).inflate(R.layout.friend_list_item_search, null);
            } else if ((position > 0 || position == 0 && !needSearch) && !(convertView instanceof VoiceRoomMemberItem)) {
                convertView = new VoiceRoomMemberItem(mContext, presenter);
            }
        }


        if (convertView instanceof VoiceRoomMemberItem) {
            VoiceRoomMemberItem itemView = (VoiceRoomMemberItem) convertView;
            int uid = uidList.get(position);
            itemView.update(roomInfo, uid, actionType, inviteSeatNum);
            return itemView;
        } else {
            convertView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!needSearch) {
                        return;
                    }
                    listView.setSelection(0);
                    searchResultView.setAdapter(new VoiceRoomSearchAdapter(mContext, roomInfo.rid, actionType, inviteSeatNum, presenter));

                    searchResultView.setVoiceRoomUidList(uidList);
                    searchResultView.show(SearchResultView.TYPE_VOICE_ROOM);
                }
            });
            return convertView;
        }
    }
}
