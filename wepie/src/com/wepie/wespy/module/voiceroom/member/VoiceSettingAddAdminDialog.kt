package com.wepie.wespy.module.voiceroom.member

import android.app.Activity
import android.content.Context
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.View
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.empty.HWUIEmptyView
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.TextUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.user.LifeUserListSimpleInfoCallback
import com.huiwan.user.UserInterface
import com.huiwan.user.UserService
import com.huiwan.user.entity.UserSimpleInfo
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog
import com.wepie.wespy.helper.dialog.bottomsheet.WpDragDialog
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.model.event.voice.AdminChangeEvent
import com.wepie.wespy.module.family.main.mine.family.FamilyMainInterface
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.Locale

class VoiceSettingAddAdminDialog : FrameLayout, IRoomMember {
    private var callBack = {}
    private val recycleView: RecyclerView
    private val confirmBtn: TextView
    private val cancelBtn: TextView
    private val mRoomMemberListRefresher: SmartRefreshLayout
    private val mSearchCellEdit: EditText
    private val mSearchCellBt: ImageView
    private val emptyView: HWUIEmptyView
    private var rid: Int = 0
    private var uidList: List<Int>? = ArrayList()
    private var activity = Activity()
    private var searchStr: String? = ""
    private var list: List<UserInterface> = ArrayList()
    private lateinit var adapter: VoiceRoomMemberSystemViewAdapter
    private var presenter: VoiceRoomMemberPresenter? = null

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    )

    init {
        val dragView = WpDragDialog(context)
        val view = dragView.setContentView(R.layout.voice_room_setting_add_admin_lay) {
            callBack.invoke()
        }
        this.addView(dragView)

        mSearchCellEdit = view.findViewById(R.id.search_cell_edit)
        mSearchCellEdit.setHint(R.string.friend_list_search_user_hint)
        mSearchCellBt = view.findViewById(R.id.search_cell_bt)
        cancelBtn = view.findViewById(R.id.add_admin_cancel)
        confirmBtn = view.findViewById(R.id.add_admin_confirm)
        recycleView = view.findViewById(R.id.room_member_list_view)
        emptyView = view.findViewById(R.id.voice_room_member_empty_view)
        mRoomMemberListRefresher = view.findViewById(R.id.room_member_list_refresher)

        recycleView.layoutManager = LinearLayoutManager(context)

        val drawables = mSearchCellEdit.compoundDrawablesRelative
        val drawableStart = drawables[0]
        drawableStart?.let {
            drawableStart.setBounds(0, 0, ScreenUtil.dip2px(20f), ScreenUtil.dip2px(20f))
        }
        drawables[0] = drawableStart
        mSearchCellEdit.setCompoundDrawablesRelative(drawableStart, drawables[1], drawables[2], drawables[3])
        mSearchCellEdit.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) =
                Unit
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                searchStr = s.toString()
                mSearchCellBt.visibility =
                    if (TextUtils.isEmpty(searchStr)) View.INVISIBLE else View.VISIBLE
            }

            override fun afterTextChanged(s: Editable) {
                doSearch(s.toString())
            }
        })
        mSearchCellBt.setOnClickListener {
            mSearchCellEdit.setText("")
        }

        mRoomMemberListRefresher.setOnLoadMoreListener {
            presenter?.loadMemberDataMore(20)
        }
        confirmBtn.isEnabled = false
        cancelBtn.setOnClickListener {
            callBack.invoke()
        }
    }

    private fun update(rid: Int, activity: Activity) {
        this.rid = rid
        this.activity = activity
        var adminMaxCount = VoiceRoomMemberActivity.DEFAULT_MAX_ADMIN_NUM
        val info = VoiceRoomService.getInstance().getRoomInfo(rid)
        if (info.isAdvancedOrAnchorOrFamilyRoom) {
            val config =
                ConfigHelper.getInstance().voiceRoomConfig.advancedRoomConfig.getConfigByLevel(info.advanceRoomLevel)
            if (config != null) {
                adminMaxCount = config.adminNum
            }
        }
        presenter = VoiceRoomMemberPresenter(this, rid, adminMaxCount)
        presenter?.setShowChoose(true)
        presenter?.loadMemberData(20)
        adapter = VoiceRoomMemberSystemViewAdapter(
            context, presenter!!,
            VoiceRoomMemberActivity.ACTION_TYPE_ROOM_SETTING_SELECT_ADMIN,
            0
        )
        recycleView.adapter = adapter

        confirmBtn.setOnClickListener { setAddAdmin() }
    }

    private fun setAddAdmin() {
        uidList = presenter?.kickUids?.toList()
        VoiceRoomPacketSender.addAdminList(rid, uidList, object : LifeSeqCallback(this) {
            override fun onSuccess(head: RspHeadInfo) {
                ToastUtil.show(R.string.set_admin_success)
                callBack.invoke()
            }

            override fun onFail(head: RspHeadInfo) {
                ToastUtil.show(head.desc)
            }
        })
    }

    companion object {
        @JvmStatic
        fun show(activity: Activity, rid: Int) {
            val dialog =
                BaseFullScreenDialog(activity, com.huiwan.dialog.R.style.dialog_style_custom)
            val view = VoiceSettingAddAdminDialog(activity)
            view.callBack = {
                dialog.dismiss()
            }
            view.update(rid, activity)
            dialog.setContentView(view)
            dialog.setCanceledOnTouchOutside(true)
            dialog.initBottomDialog()
            dialog.show()
        }
    }

    override fun showManageState(roomInfo: VoiceRoomInfo?) {
        if (roomInfo != null) {
            checkShowEmpty(adapter.updateMember(presenter?.memberInfoList?.filterNot {
                val situation1 = roomInfo.isAdmin(it.memberUid)
                val situation2 = (!roomInfo.isFamilyRoom && roomInfo.isOwner(it.memberUid))
                val situation3 = roomInfo.familyRoleInfo.get(it.memberUid) == FamilyMainInterface.ROLE_OWNER
                situation1 || situation2 || situation3
            }))
            getUserList()
        }
        mRoomMemberListRefresher.finishLoadMore()
        enableLoadMore(true)
    }

    override fun showDeleteState(roomInfo: VoiceRoomInfo?) = Unit

    override fun showKickDialog(txt: String?, size: Int) = Unit

    override fun onChooseChange(num: Int) {
        confirmBtn.isEnabled = num > 0
        confirmBtn.setTextColor(ResUtil.getColor(if (num > 0) R.color.color_accent else R.color.color_accent_disable))
    }

    override fun showSearchResult(total: Int, uids: MutableList<Int>?, clear: Boolean) = Unit

    override fun enableRefresh(enable: Boolean) {
        mRoomMemberListRefresher.setEnableRefresh(enable)
    }

    override fun enableLoadMore(enable: Boolean) {
        mRoomMemberListRefresher.setEnableLoadMore(enable)
    }

    override fun onItemClick(actionType: Int, uid: Int) = Unit

    override fun afterKickUser(kickUids: MutableList<Int>?) = Unit

    override fun getContextAsActivity(): Activity {
        return this.activity
    }

    fun doSearch(keyWords: String) {
        val filter: List<UserInterface> = filterMemberInfo(keyWords, list)
        refreshList(filter, TextUtils.isEmpty(keyWords))
    }

    private fun filterMemberInfo(
        keyWords: String,
        memberInfos: List<UserInterface>
    ): List<UserInterface> {
        if (TextUtils.isEmpty(keyWords)) return memberInfos
        val filter: MutableList<UserInterface> = ArrayList()

        val keySearchWords = keyWords.lowercase(Locale.getDefault())
        for (info in memberInfos) {
            if (info.remarkName.contains(keySearchWords, true)) {
                filter.add(info)
            } else if (info.wdid.contains(keySearchWords, true)) {
                filter.add(info)
            } else if (info.nameFirstLetter.contains(keySearchWords, true)) {
                filter.add(info)
            }
        }

        return filter
    }

    private fun refreshList(list: List<UserInterface>, empty: Boolean) {
        val info = VoiceRoomService.getInstance().getRoomInfo(rid)
        if (empty) {
            checkShowEmpty(adapter.updateMember(presenter?.memberInfoList?.filterNot {
                val situation1 = info.isAdmin(it.memberUid)
                val situation2 = (!info.isFamilyRoom && info.isOwner(it.memberUid))
                val situation3 = info.familyRoleInfo.get(it.memberUid) == FamilyMainInterface.ROLE_OWNER
                situation1 || situation2 || situation3
            }))
            return
        }

        val resultList: MutableList<VoiceRoomInfo.RoomMemberInfo> = ArrayList(list.size)
        for (i in list.indices) {
            val user = list[i]
            val member = getMember(user.uid)
            if (member != null) {
                resultList.add(member)
            }
        }

        checkShowEmpty(adapter.updateMemberSearch(resultList, true))
    }

    private fun checkShowEmpty(num: Int) {
        val tips = ResUtil.getStr(R.string.select_admin_no_member)
        if (!TextUtil.isEmpty(tips) && num == 0) {
            emptyView.setText(tips)
            emptyView.visibility = View.VISIBLE
            recycleView.visibility = View.GONE
        } else {
            emptyView.visibility = View.GONE
            recycleView.visibility = View.VISIBLE
        }
    }

    private fun getMember(uid: Int): VoiceRoomInfo.RoomMemberInfo? {
        val membersList = presenter?.memberInfoList ?: emptyList()
        for (member in membersList) {
            if (member.uid == uid) {
                return member
            }
        }
        return null
    }

    private fun getUserList() {
        val membersList = presenter?.memberInfoList ?: emptyList()

        val memberUidList = ArrayList<Int>()
        for (member in membersList) {
            memberUidList.add(member.uid)
        }

        UserService.get().getCacheSimpleUserList(memberUidList, object :
            LifeUserListSimpleInfoCallback(this@VoiceSettingAddAdminDialog) {
            override fun onUserInfoSuccess(userSimpleInfos: MutableList<UserSimpleInfo>?) {

                val userList: MutableList<UserInterface> = ArrayList()
                if (userSimpleInfos != null) {
                    for (userInterface in userSimpleInfos) {
                        userList.add(userInterface)
                    }
                }
                list = userList
            }

            override fun onUserInfoFailed(description: String) = Unit
        })
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onListChange(event: AdminChangeEvent?) {
        presenter?.loadMemberData(20)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        EventBus.getDefault().register(this)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        if (presenter != null) {
            presenter?.clear()
        }
        EventBus.getDefault().unregister(this)
    }
}