package com.wepie.wespy.module.voiceroom.member

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo

class VoiceMemberViewModel : ViewModel() {

    private val _onlineViewManageLiveData = MutableLiveData<Boolean>()

    private val _ridLiveData = MutableLiveData(-1)

    private val selectItemLiveData = MutableLiveData<SelectItem>()

    var actionType: Int = 0

    var seatNum: Int = -1

    fun init(info: VoiceRoomInfo, actionType: Int, seatNum: Int) {
        this.actionType = actionType
        _ridLiveData.value = info.rid
        this.seatNum = seatNum
    }

    fun setManageState(state: Boolean) {
        _onlineViewManageLiveData.value = state
    }

    fun getOnlineViewManageLiveData(): LiveData<Boolean> = _onlineViewManageLiveData

    fun getRid(): Int = _ridLiveData.value!!

    fun getSelectItemLiveData(): LiveData<SelectItem> = selectItemLiveData

    fun onItemClick(uid: Int, actionType: Int) {
        selectItemLiveData.value = SelectItem(uid, actionType)
    }
}

data class FansUidData(val list: List<Int>, val count: Int, val isRefresh: Boolean)

data class SelectItem(val uid: Int, val actionType: Int)