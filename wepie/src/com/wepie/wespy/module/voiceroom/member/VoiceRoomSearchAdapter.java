package com.wepie.wespy.module.voiceroom.member;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.huiwan.base.util.ToastUtil;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.FriendInfo;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.contact.friendlist.BaseSearchAdapter;
import com.wepie.wespy.module.contact.friendlist.SearchResultView;
import com.wepie.wespy.module.voiceroom.dataservice.RoomCallback;
import com.wepie.wespy.module.voiceroom.dataservice.RoomSenderPresenter;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity;

import java.util.ArrayList;
import java.util.List;

public class VoiceRoomSearchAdapter extends BaseSearchAdapter {
    private static final int SEARCH_TYPE_REMARK = 1;
    private static final int SEARCH_TYPE_NICK = 2;
    private final int inviteSeatNum;
    private final int actionType;
    private final VoiceRoomMemberPresenter presenter;
    private Context mContext;
    private List<SearchResultView.SearchInfo> resultInfos = new ArrayList<>();
    private String searchStr = "";
    private int rid;
    private SearchInterface searchInterface;

    public VoiceRoomSearchAdapter(Context context, int rid, int actionType, int inviteSeatNum, VoiceRoomMemberPresenter presenter) {
        this.mContext = context;
        this.rid = rid;
        this.actionType = actionType;
        this.inviteSeatNum = inviteSeatNum;
        this.presenter = presenter;
        notifyDataSetChanged();
    }

    @Override
    public void setSearchInterface(SearchInterface searchInterface) {
        this.searchInterface = searchInterface;
    }

    @Override
    public void update(List<SearchResultView.SearchInfo> resultInfos, String searchStr) {
        this.resultInfos.clear();

        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo(rid);
        int ownerUid = roomInfo.owner;
        int seatUid = roomInfo.getSeatInfoByNum(inviteSeatNum).uid;
        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN) {
            this.resultInfos.addAll(resultInfos);
            for (SearchResultView.SearchInfo searchInfo : this.resultInfos) {
                if (searchInfo.friendInfo.getUid() == ownerUid) {
                    this.resultInfos.remove(searchInfo);
                    break;
                }
            }
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE) {
            this.resultInfos.addAll(resultInfos);
            for (SearchResultView.SearchInfo searchInfo : this.resultInfos) {
                if (searchInfo.friendInfo.getUid() == ownerUid) {
                    this.resultInfos.remove(searchInfo);
                    break;
                }
            }
            for (SearchResultView.SearchInfo searchInfo : this.resultInfos) {
                if (searchInfo.friendInfo.getUid() == seatUid) {
                    this.resultInfos.remove(searchInfo);
                    break;
                }
            }
        } else {
            this.resultInfos.addAll(resultInfos);
        }
        this.searchStr = searchStr;
        notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return resultInfos.size();
    }

    @Override
    public Object getItem(int position) {
        return null;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder = null;
        final VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo(rid);
        if(convertView == null) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.voice_room_search_result_item, parent, false);
            viewHolder = new ViewHolder();
            viewHolder.headImv = convertView.findViewById(R.id.head_imv);
            viewHolder.chooseImv = convertView.findViewById(R.id.rm_choose_image);
            viewHolder.cancelAdminImv = convertView.findViewById(R.id.cancel_admin_imv);
            viewHolder.baseItem = convertView.findViewById(R.id.rm_base_item);
            viewHolder.rootLay = convertView.findViewById(R.id.root_lay);

            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }

        SearchResultView.SearchInfo searchInfo = resultInfos.get(position);
        FriendInfo friendInfo = searchInfo.friendInfo;

        if(friendInfo == null) {
            viewHolder.rootLay.setVisibility(View.GONE);
            return convertView;
        }
        viewHolder.rootLay.setVisibility(View.VISIBLE);
        viewHolder.baseItem.update(roomInfo.rid,friendInfo.getUid(),true);
        if (searchInterface != null) {
            if (searchInfo.type == SEARCH_TYPE_REMARK) {
                searchInterface.setNameTextView(viewHolder.baseItem.getNickTv(), friendInfo.getRemarkName(), searchInfo.index);
            } else {
                searchInterface.setNameTextView(viewHolder.baseItem.getNickTv(), friendInfo.getNickName(), searchInfo.index);
            }
        }

        if (roomInfo.isAdmin(friendInfo.getUid())) {
            if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN) {
                viewHolder.cancelAdminImv.setVisibility(View.VISIBLE);
            } else {
                viewHolder.cancelAdminImv.setVisibility(View.GONE);
            }
        }

        final ViewHolder finalViewHolder = viewHolder;
        final View finalConvertView = convertView;
        UserService.get().getCacheSimpleUser(friendInfo.getUid(), new LifeUserSimpleInfoCallback(convertView) {
            @Override
            public void onUserInfoSuccess(final UserSimpleInfo simpleInfo) {
                HeadImageLoader.loadCircleHeadImage(simpleInfo.getHeadimgurl(), finalViewHolder.headImv);
                if (presenter.showChoose()) {
                    finalViewHolder.chooseImv.setVisibility(ImageView.VISIBLE);
                    int selfUid = LoginHelper.getLoginUid();
                    boolean isOwner = roomInfo.isOwner(simpleInfo.uid);
                    boolean notShowChoose = isOwner || (roomInfo.isSelfAdmin() && roomInfo.isAdmin(simpleInfo.uid))
                            || selfUid == simpleInfo.uid;
                    //房主不可被选择，管理员不可以选择管理员
                    if (notShowChoose) {
                        finalViewHolder.chooseImv.setBackgroundResource(R.drawable.ic_un_select_limit);
                        finalConvertView.setOnClickListener(v -> JumpUtil.enterUserInfoDetailFromVoiceRoom(mContext, simpleInfo.uid, presenter.getRid()));
                    } else {
                        boolean isChoosed = presenter.isUserChoosed(simpleInfo.uid);
                        finalViewHolder.chooseImv.setBackgroundResource(isChoosed ? R.drawable.ic_multi_select : R.drawable.ic_un_select);
                        finalConvertView.setOnClickListener(view -> {
                            if (presenter.isUserChoosed(simpleInfo.uid)) {
                                finalViewHolder.chooseImv.setBackgroundResource(R.drawable.ic_un_select);
                                presenter.unChooseUser(simpleInfo.uid);
                            } else {
                                finalViewHolder.chooseImv.setBackgroundResource(R.drawable.ic_multi_select);
                                presenter.chooseUser(simpleInfo.uid);
                            }
                        });
                    }

                } else {
                    finalViewHolder.chooseImv.setVisibility(ImageView.GONE);
                    finalConvertView.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_MEMBER_LIST) {
                                JumpUtil.enterUserInfoDetailFromVoiceRoom(mContext, simpleInfo.uid, rid);
                            } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE) {
                                RoomSenderPresenter.inviteSpeak(rid, simpleInfo.uid, inviteSeatNum, new RoomCallback(v) {
                                    @Override
                                    public void onSuccess(int rid) {
                                        ToastUtil.show(R.string.voice_room_members_op_sel_invite_success_tip);
                                        JumpUtil.gotoVoiceRoomActivity(mContext, rid);
                                    }

                                    @Override
                                    public void onFail(String msg) {ToastUtil.show(msg); }
                                });
                            } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN) {
                                //汪端徳说这里不需要搜索
                            } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_DRAWER) {
                                DrawInviteHelper.inviteDraw(mContext, rid, simpleInfo.uid);
                            }
                        }
                    });
                }
            }

            @Override
            public void onUserInfoFailed(String description) {}
        });

        return convertView;
    }

    class ViewHolder {
        DecorHeadImgView headImv;
        ImageView chooseImv;
        VoiceRoomMemberBaseItem baseItem;
        ImageView cancelAdminImv;
        View rootLay;
    }
}
