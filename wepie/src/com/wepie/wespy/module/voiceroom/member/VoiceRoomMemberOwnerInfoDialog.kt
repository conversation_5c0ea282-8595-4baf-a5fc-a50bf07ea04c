package com.wepie.wespy.module.voiceroom.member

import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.text.parseAsHtml
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.model.voiceroom.MemberTask
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.WebApi
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.widget.decoration.SpaceItemDecoration
import com.huiwan.widget.image.DrawableUtil
import com.huiwan.widget.inflate
import com.huiwan.widget.rv.NpaLinearLayoutManager
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog
import com.wepie.wespy.helper.dialog.bottomsheet.WpDragDialog
import com.wepie.wespy.helper.imageLoader.HeadImageLoader
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginUtil
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender

class VoiceRoomMemberOwnerInfoDialog(context: Context) : FrameLayout(context) {

    private val roomIcon: ImageView
    private val roomMemberTitleIv: TextView
    private val roomMemberCountIv: TextView

    private val newPointTv: TextView

    private val adapter: FinishTaskAdapter = FinishTaskAdapter()

    private var callBack = {}

    init {
        val dragView = WpDragDialog(context)
        val contentView = dragView.setContentView(R.layout.voice_room_member_owner_dialog) {
            callBack.invoke()
        }
        dragView.findViewById<ImageView>(R.id.drag_indicator)
            .setImageDrawable(DrawableUtil.genColorRadius(Color.WHITE, 2))
        contentView.setBackgroundResource(R.drawable.bg_voice_room_member_dialog)
        this.addView(dragView)

        roomIcon = findViewById(R.id.voice_room_head_iv)
        roomMemberTitleIv = findViewById(R.id.voice_room_member_group_name_tv)
        roomMemberCountIv = findViewById(R.id.voice_room_member_count_tv)
        findViewById<View>(R.id.voice_room_member_count_iv).setOnClickListener {
            roomMemberCountIv.performClick()
        }
        val memberGroup = ConfigHelper.getInstance().voiceRoomConfig.voiceMemberGroup
        findViewById<View>(R.id.voice_room_member_help_iv).setOnClickListener {
            ApiService.of(WebApi::class.java)
                ?.showWebDialog(context, memberGroup.helpUrl, 0, 0F)
        }

        newPointTv = findViewById(R.id.voice_room_member_new_point_tv)

        findViewById<ImageView>(R.id.voice_room_member_rank_iv).setOnClickListener {
            VoiceRoomMemberRankView.showInDragView(true, dragView)
        }

        val rv = findViewById<RecyclerView>(R.id.voice_room_member_task_rv)
        rv.layoutManager = NpaLinearLayoutManager(context)
        rv.adapter = adapter
        val dp16 = ScreenUtil.dip2px(16F)
        val dp24 = ScreenUtil.dip2px(24F)
        rv.addItemDecoration(SpaceItemDecoration(Rect(0, dp24, 0, 0), Rect(0, dp24, 0, dp16)))
    }

    fun update(rid: Int, rsp: TmpRoomPackets.MemberTaskSummaryResp) {
        newPointTv.text =
            ResUtil.getStr(R.string.voice_room_members_all_point, rsp.todayPoints)
                .parseAsHtml()
        adapter.refresh(rsp.taskSummaryList.map {
            FinishTaskData(it)
        })

        val info = VoiceRoomService.getInstance().getRoomInfo(rid)
        HeadImageLoader.loadCircleHeadImage(info.headImage, roomIcon)
        roomMemberTitleIv.text = info.name
        roomMemberCountIv.text =
            ResUtil.getStr(R.string.voice_room_members_count_title, info.memberCount)
        roomMemberCountIv.setOnDoubleClick {
            JumpUtil.showVoiceRoomMemberDialog(
                context, rid, VoiceRoomMemberActivity.ACTION_TYPE_MEMBER_LIST,
                VoiceRoomMemberDialog.TAB_MEMBER_SYSYEM, -1
            )
        }
    }

    companion object {

        @JvmStatic
        fun show(view: View, rid: Int) {
            VoiceRoomPacketSender.getMemberTaskSummary(rid, object : LifeSeqCallback(view) {
                override fun onSuccess(head: RspHeadInfo?) {
                    val msg = head?.message ?: return
                    val rsp = msg as TmpRoomPackets.MemberTaskSummaryResp
                    val context = view.context
                    val dialog =
                        BaseFullScreenDialog(context, com.huiwan.dialog.R.style.dialog_style_custom)
                    val view = VoiceRoomMemberOwnerInfoDialog(context)
                    view.callBack = {
                        dialog.dismiss()
                    }
                    view.update(rid, rsp)
                    dialog.setDimAmount(0F)
                    dialog.setContentView(view)
                    dialog.setCanceledOnTouchOutside(true)
                    dialog.initFullBottomDialog()
                    dialog.show()
                    PluginUtil.getMainPlugin(context).life?.onDestroy {
                        dialog.dismiss()
                    }
                }

                override fun onFail(head: RspHeadInfo?) {
                    ToastUtil.show(head?.desc)
                }
            })
        }
    }

    class FinishTaskData(item: TmpRoomPackets.TaskProgressItem) {
        private var task: MemberTask? = null
        private val taskId = item.taskId
        val taskCount = item.taskFinishCnt

        fun getTask(): MemberTask? {
            if (task != null) {
                return task
            }
            for (t in ConfigHelper.getInstance().voiceRoomConfig.voiceMemberGroup.tasks) {
                if (t.id == taskId) {
                    task = t
                    break
                }
            }
            return task
        }
    }

    class FinishTaskAdapter : RecyclerView.Adapter<FinishTaskViewHolder>() {

        private val data: MutableList<FinishTaskData> = ArrayList()
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FinishTaskViewHolder {
            return FinishTaskViewHolder(parent.inflate(R.layout.voice_room_member_finish_task_item))
        }

        override fun getItemCount(): Int = data.size

        override fun onBindViewHolder(holder: FinishTaskViewHolder, position: Int) {
            val item = data[position]
            item.getTask()?.let {
                holder.taskDesc.text =
                    ResUtil.getStr(R.string.voice_room_members_finish_task_count_title, it.title)
                WpImageLoader.load(it.icon, holder.taskIcon)
            }
            holder.taskCount.text = item.taskCount.toString()
        }

        fun refresh(list: List<FinishTaskData>) {
            val count = data.size
            if (data.isNotEmpty()) {
                data.clear()
            }
            data.addAll(list)
            notifyItemRangeChanged(0, data.size)
            if (count > data.size) {
                notifyItemRangeRemoved(data.size, count - data.size)
            }
        }

    }

    class FinishTaskViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val taskIcon: ImageView = itemView.findViewById(R.id.member_task_icon)
        val taskDesc: TextView = itemView.findViewById(R.id.member_task_desc)
        val taskCount: TextView = itemView.findViewById(R.id.member_task_count)
    }
}