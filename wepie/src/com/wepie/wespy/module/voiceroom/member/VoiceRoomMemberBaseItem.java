package com.wepie.wespy.module.voiceroom.member;

import static com.huiwan.user.entity.User.GENDER_DEFAULT;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.decorate.CharmManager;
import com.huiwan.decorate.DecorHeadImgView;
import com.huiwan.decorate.NameTextView;
import com.huiwan.decorate.vip.VipLabelView;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.IMedalApi;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.marry.WeddingInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.family.FamilyUiConst;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity;

/**
 * 语音房用户信息列表基类,只展示用户的基础信息,不附加任何其他操作
 */

public class VoiceRoomMemberBaseItem extends FrameLayout {
    private Context mContext;
    private DecorHeadImgView headImage;
    private NameTextView nickTv;
    private ImageView genderIv;
    private TextView micMarkImv;
    private TextView tagView;
    private LinearLayout tagLay;
    private TextView familyRoleTv;
    private VipLabelView vipLabelView;
    private ImageView charmIv;
    private LinearLayout memberMedalLay;
    private IMedalApi.IMedalListView medalListView;

    public VoiceRoomMemberBaseItem(Context context) {
        this(context, null);
    }

    public VoiceRoomMemberBaseItem(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public VoiceRoomMemberBaseItem(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        init();
    }

    /**
     * 获取昵称tv,兼容老搜索
     *
     * @return
     */
    public NameTextView getNickTv() {
        return nickTv;
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.voice_room_member_base_item, this, true);

        micMarkImv = findViewById(R.id.mic_mark_imv_tv);
        headImage = findViewById(R.id.head_imv);
        nickTv = findViewById(R.id.name_tv);
        genderIv = findViewById(R.id.gender_iv);
        tagView = findViewById(R.id.titleImv_tv);
        familyRoleTv = findViewById(R.id.family_role_tv);
        medalListView = ApiService.of(IMedalApi.class).genMedalListView(mContext);
        vipLabelView = findViewById(R.id.member_vip_label);
        charmIv = findViewById(R.id.member_charm_iv);
        memberMedalLay = findViewById(R.id.member_medal_lay);
        tagLay = findViewById(R.id.tag_lay);
    }

    /**
     * 简单的更新view
     *
     * @param rid
     * @param uid
     */
    public void update(final int rid, final int uid, boolean showTag) {
        update(VoiceRoomService.getInstance().getRoomInfo(rid), uid, VoiceRoomMemberActivity.ACTION_TYPE_MEMBER_LIST, 0, showTag);
    }

    public void update(VoiceRoomInfo info, final int uid, boolean showTag) {
        update(info, uid, VoiceRoomMemberActivity.ACTION_TYPE_MEMBER_LIST, 0, showTag);
    }

    public void update(final VoiceRoomInfo roomInfo, final int uid, final int actionType, final int inviteSeatNum, boolean showTag) {
        if (roomInfo == null) {
            return;
        }
        if (showTag) {
            boolean isOwner = roomInfo.isOwner(uid);
            micMarkImv.setVisibility(VISIBLE);
            tagView.setVisibility(View.VISIBLE);
            if (roomInfo.getRoomType() == VoiceRoomInfo.ROOM_TYPE_WEDDING) { //婚礼房
                WeddingInfo weddingInfo = roomInfo.weddingInfo;
                if (uid == weddingInfo.weddingHost) { //司仪
                    tagView.setBackground(ResUtil.getDrawableWithTintValue(R.drawable.shape_edaf33_corner4, 0xFFFFDB9F));
                    tagView.setText(R.string.mc_people);
                } else if (uid == weddingInfo.brideUid) { //新娘
                    tagView.setBackground(ResUtil.getDrawableWithTintValue(R.drawable.shape_edaf33_corner4, 0xFFFE282D));
                    tagView.setText(R.string.bride);
                } else if (uid == weddingInfo.groomUid) { //新郎
                    tagView.setBackground(ResUtil.getDrawableWithTintValue(R.drawable.shape_edaf33_corner4, 0xFFFE282D));
                    tagView.setText(R.string.groom);
                } else if (weddingInfo.groomsmanList.contains(uid)) { //伴郎
                    tagView.setBackground(ResUtil.getDrawableWithTintValue(R.drawable.shape_edaf33_corner4, 0xFF98C7FF));
                    tagView.setText(R.string.best_man);
                } else if (weddingInfo.bridesmaidList.contains(uid)) { //伴娘
                    tagView.setBackground(ResUtil.getDrawableWithTintValue(R.drawable.shape_edaf33_corner4, 0xFF98C7FF));
                    tagView.setText(R.string.maid_of_honor);
                } else {
                    tagView.setVisibility(View.GONE);
                }
            } else {
                if (isOwner) {
                    tagView.setBackgroundResource(R.drawable.voice_room_owner_tag_bg);
                    tagView.setText(R.string.host);
                } else if (roomInfo.adminList.contains(uid)) {
                    tagView.setBackgroundResource(R.drawable.voice_room_manager_tag_bg);
                    tagView.setText(R.string.room_manager);
                } else {
                    tagView.setVisibility(View.GONE);
                }
            }

            if (roomInfo.isUserSeated(uid)) {
                micMarkImv.setVisibility(VISIBLE);
            } else {
                micMarkImv.setVisibility(GONE);
            }
            int roleID = roomInfo.familyRoleInfo.get(uid);
            int iconRes = FamilyUiConst.getRoleBg(roleID);
            String role = FamilyUiConst.getRoleStr(roleID);
            if (iconRes > 0) {
                familyRoleTv.setVisibility(View.VISIBLE);
                familyRoleTv.setBackgroundResource(iconRes);
                familyRoleTv.setText(role);
            } else {
                familyRoleTv.setVisibility(View.GONE);
            }
        } else {
            tagView.setVisibility(View.GONE);
            micMarkImv.setVisibility(GONE);
            familyRoleTv.setVisibility(GONE);
        }

        //更新用户信息
        updateUser(uid);
    }

    private void initCenter(boolean showCenter) {
        if (showCenter) {
            tagLay.setVisibility(GONE);
        } else {
            tagLay.setVisibility(VISIBLE);
        }
    }

    private void updateUser(int uid) {
        headImage.showUserHeadWithDecoration(uid);
        UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo userInfo) {
                //所有信息都为空
                boolean allZero = CharmManager.getInstance().getCharmLevel(userInfo.flower) == 0 && userInfo.vip == 0 && userInfo.wearMedals.size() == 0 && userInfo.gender == GENDER_DEFAULT;
                //当所有信息都为空的时候,即使有tag也居中显示
                initCenter(allZero);
                nickTv.setUserName(userInfo);
                vipLabelView.setVipLevel(userInfo.vip);
                CharmManager.checkShowShortCharm(charmIv, userInfo.flower);
                if (memberMedalLay.getChildCount() == 0) {
                    memberMedalLay.addView(medalListView.getView());
                }
                medalListView.updateIconSize(ScreenUtil.dip2px(22));
                medalListView.updateMedalList(userInfo.wearMedals);
                genderIv.setVisibility(VISIBLE);
                if (userInfo.isFemale()) {
                    genderIv.setImageResource(R.drawable.icon_female);
                } else if (userInfo.isMale()) {
                    genderIv.setImageResource(R.drawable.icon_male);
                } else {
                    genderIv.setImageDrawable(null);
                    genderIv.setVisibility(GONE);
                }
            }

            @Override
            public void onUserInfoFailed(String description) {
            }
        });
    }
}
