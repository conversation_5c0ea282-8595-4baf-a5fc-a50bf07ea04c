package com.wepie.wespy.module.voiceroom.member;

import android.content.Context;
import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.LoginHelper;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.helper.dialog.bottomsheet.WpBottomListDialog;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.RoomInfoUpdateEvent;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.family.main.mine.family.FamilyMainInterface;
import com.wepie.wespy.module.voiceroom.dataservice.RoomCallback;
import com.wepie.wespy.module.voiceroom.dataservice.RoomSenderPresenter;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity;

import java.util.Collections;
import java.util.List;

/**
 * Created by geeksammao on 26/10/2017.
 */

public class VoiceRoomMemberItem extends RelativeLayout {
    private final Context mContext;
    private ImageView chooseImage;
    private ImageView cancelIv;
    private TextView cancelTv;
    private ConstraintLayout memberActiveLay;
    private TextView activeTv;
    private TextView activeDaysTv;
    private ImageView moreIv;
    private final VoiceRoomMemberPresenter presenter;
    private VoiceRoomMemberSystemBaseItem roomMemberBaseItem;

    public VoiceRoomMemberItem(Context context, VoiceRoomMemberPresenter presenter) {
        super(context);
        this.mContext = context;
        this.presenter = presenter;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.voice_room_search_result_item, this);
        chooseImage = findViewById(R.id.rm_choose_image);
        roomMemberBaseItem = findViewById(R.id.rm_base_item);
        cancelIv = findViewById(R.id.cancel_admin_imv);
        cancelTv = findViewById(R.id.cancel_admin_tv);
        memberActiveLay = findViewById(R.id.member_active_lay);
        activeTv = findViewById(R.id.active_tv);
        activeDaysTv = findViewById(R.id.active_days_tv);
        moreIv = findViewById(R.id.more_iv);
    }

    public void update(final VoiceRoomInfo roomInfo, final int uid, final int actionType, final int inviteSeatNum) {
        boolean isOwner = roomInfo.isOwner(uid);

        cancelIv.setVisibility(GONE);
        cancelTv.setVisibility(GONE);

        if (roomInfo.adminList.contains(uid)) {
            if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN || actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_FAMILY_ADMIN) {
                cancelIv.setVisibility(VISIBLE);
            } else {
                cancelIv.setVisibility(GONE);
            }
        }

        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_ROOM_SETTING_SELECT_ADMIN) {
            cancelIv.setVisibility(GONE);
        }

        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_BLACK) {
            cancelTv.setText(R.string.voice_room_members_op_rm_from_block);
            cancelTv.setVisibility(VISIBLE);
        }

        if (presenter.showChoose()) {
            chooseImage.setVisibility(ImageView.VISIBLE);
            boolean isFamilyRoom = roomInfo.isFamilyRoom();
            boolean notShowChoose = (isOwner || (roomInfo.isSelfAdmin() && roomInfo.isAdmin(uid))
                    || LoginHelper.getLoginUid() == uid) && !isFamilyRoom;

            int selfRole = roomInfo.familyRoleInfo.get(LoginHelper.getLoginUid());
            int role = roomInfo.familyRoleInfo.get(uid);
            boolean situation1 = (selfRole == FamilyMainInterface.ROLE_OWNER && role == FamilyMainInterface.ROLE_OWNER);
            boolean situation2 = (selfRole == FamilyMainInterface.ROLE_SUB_OWNER && (role == FamilyMainInterface.ROLE_OWNER || role == FamilyMainInterface.ROLE_SUB_OWNER));
            boolean situation3 = (selfRole == FamilyMainInterface.ROLE_ELDER && (role == FamilyMainInterface.ROLE_OWNER || role == FamilyMainInterface.ROLE_SUB_OWNER || role == FamilyMainInterface.ROLE_ELDER));
            boolean situation4 = (roomInfo.isSelfAdmin() && selfRole == FamilyMainInterface.ROLE_NORMAL && (role == FamilyMainInterface.ROLE_OWNER || role == FamilyMainInterface.ROLE_SUB_OWNER || role == FamilyMainInterface.ROLE_ELDER || roomInfo.isAdmin(uid)));
            boolean notShowChooseFamily = (situation1 || situation2 || situation3 || situation4) && isFamilyRoom;

            if (notShowChoose || notShowChooseFamily) {
                chooseImage.setImageResource(R.drawable.ic_un_select_limit);
                setOnClickListener(v -> {
//                        JumpUtil.enterUserInfoDetailFromVoiceRoom(mContext, uid, presenter.getRid());
                });
            } else {
                boolean isChoosed = presenter.isUserChoosed(uid);
                chooseImage.setImageResource(isChoosed ? R.drawable.ic_multi_select : R.drawable.ic_un_select);
                setOnClickListener(view -> {
                    if (presenter.isUserChoosed(uid)) {
                        chooseImage.setImageResource(R.drawable.ic_un_select);
                        presenter.unChooseUser(uid);
                    } else {
                        chooseImage.setImageResource(R.drawable.ic_multi_select);
                        presenter.chooseUser(uid);
                    }
                });
            }

        } else {
            chooseImage.setVisibility(ImageView.GONE);
            setOnClickListener(v -> {
                //看成员列表
                if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_MEMBER_LIST) {
                    JumpUtil.enterUserInfoDetailFromVoiceRoom(mContext, uid, presenter.getRid());
                    //邀请上麦
                } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE) {
                    RoomSenderPresenter.inviteSpeak(roomInfo.rid, uid, inviteSeatNum, new RoomCallback(this) {
                        @Override
                        public void onSuccess(int rid) {
                            ToastUtil.show(R.string.voice_room_members_op_sel_invite_success_tip);
                            JumpUtil.gotoVoiceRoomActivity(getContext(), rid);
                            presenter.onItemClick(actionType, uid);
                        }

                        @Override
                        public void onFail(String msg) {
                            ToastUtil.show(msg);
                        }
                    });
                    //选择家族 管理员
                } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_FAMILY_ADMIN) {
                    UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(this) {
                        @Override
                        public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                            String name = simpleInfo.getRemarkName();
                            String content;
                            int spanStartIndex;
                            if (roomInfo.adminList.size() == 0) {
                                content = ResUtil.getStr(R.string.voice_room_members_set_admin_s, name);
                                spanStartIndex = 4;
                            } else {
                                if (roomInfo.adminList.contains(uid)) {
                                    content = ResUtil.getStr(R.string.voice_room_members_unset_admin_s, name);
                                    spanStartIndex = 3;
                                } else {
                                    content = ResUtil.getStr(R.string.voice_room_members_replace_admin_s, name);
                                    spanStartIndex = 4;
                                }
                            }

                            if (!TextUtil.isEmpty(content) && !TextUtil.isEmpty(name) && content.contains(name)) {
                                spanStartIndex = content.indexOf(name);
                            }

                            SpannableString sp = new SpannableString(content);
                            sp.setSpan(new ForegroundColorSpan(ResUtil.getColor(R.color.color_accent_ex)), spanStartIndex, spanStartIndex + name.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);

                            DialogUtil.showDoubleBtnDialogWithSpan(getContext(), "", sp, ResUtil.getStr(R.string.yes), ResUtil.getStr(R.string.no), true, () -> {
                                final int inviteUid;
                                if (roomInfo.adminList.contains(uid)) {
                                    inviteUid = 0;
                                } else {
                                    inviteUid = uid;
                                }
                                RoomSenderPresenter.setAdmin(roomInfo.rid, inviteUid, new RoomCallback(mContext) {
                                    @Override
                                    public void onSuccess(int rid) {
                                        if (inviteUid > 0) {
                                            ToastUtil.show(R.string.set_success);
                                            roomInfo.adminList.clear();
                                            roomInfo.adminList.add(inviteUid);
                                        } else {
                                            ToastUtil.show(R.string.cancel_success);
                                            roomInfo.adminList.clear();
                                        }
                                        ((VoiceRoomMemberActivity) mContext).refreshMemberList(roomInfo);
                                        EventDispatcher.postRoomInfoUpdateEvent(RoomInfoUpdateEvent.F_ADMIN_CHANGE);
                                    }

                                    @Override
                                    public void onFail(String msg) {
                                        ToastUtil.show(msg);
                                    }
                                });
                            });
                        }

                        @Override
                        public void onUserInfoFailed(String description) {
                        }
                    });
                    //选人画画
                } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_DRAWER) {
                    DrawInviteHelper.inviteDraw(mContext, roomInfo.rid, uid);
                    presenter.onItemClick(actionType, uid);
                } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN) {
                    JumpUtil.enterUserInfoDetailFromVoiceRoom(mContext, uid, presenter.getRid(), true, false);
                } else {
                    JumpUtil.enterUserInfoDetailFromVoiceRoom(mContext, uid, presenter.getRid());
                }
            });
        }

        cancelIv.setOnClickListener(v -> {
            if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN) {
                // 取消管理员
                UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(ContextUtil.getLife(mContext)) {
                    @Override
                    public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                        if (!roomInfo.adminList.contains(uid)) {
                            return;
                        }

                        final List<String> list = Collections.singletonList(ResUtil.getString(R.string.remove_admin));
                        WpBottomListDialog.show(mContext, list, Color.parseColor("#FF4646"), "", position -> {
                            if (position == 0) {
                                RoomSenderPresenter.delAdmin(roomInfo.rid, Collections.singletonList(uid), new RoomCallback(mContext) {
                                    @Override
                                    public void onSuccess(int rid) {
                                        ToastUtil.show(R.string.set_success);
                                        roomInfo.adminList.remove(Integer.valueOf(uid));
                                        ((VoiceRoomMemberActivity) mContext).refreshMemberList(roomInfo);
                                        EventDispatcher.postRoomInfoUpdateEvent(RoomInfoUpdateEvent.F_ADMIN_CHANGE);
                                    }

                                    @Override
                                    public void onFail(String msg) {
                                        ToastUtil.show(msg);
                                    }
                                });
                            }
                            return null;
                        });
                    }

                    @Override
                    public void onUserInfoFailed(String description) {
                    }
                });
            } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_FAMILY_ADMIN) {
                //添加管理员
                performClick();
            }
        });
        cancelTv.setOnClickListener(v -> {
            //取消黑名单
            if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_BLACK) {
                RoomSenderPresenter.delBlack(roomInfo.rid, Collections.singletonList(uid), new RoomCallback(this) {
                    @Override
                    public void onSuccess(int rid) {
                        ToastUtil.show(R.string.remove_success);
                        roomInfo.blackList.remove(Integer.valueOf(uid));
                        ((VoiceRoomMemberActivity) mContext).refreshMemberList(roomInfo);
                        EventDispatcher.postRoomInfoUpdateEvent(RoomInfoUpdateEvent.F_BLOCK_LIST_CHANGE);
                    }

                    @Override
                    public void onFail(String msg) {
                        ToastUtil.show(msg);
                    }
                });
            }
        });

        //列表信息更新放到这里 更新用户信息
        roomMemberBaseItem.update(roomInfo, uid, true, actionType);
        roomMemberBaseItem.updateMemberLevelByUid(roomInfo, uid);
        memberActiveLay.setVisibility(View.GONE);
    }

    public void update(final VoiceRoomInfo roomInfo, final int uid, final int actionType, final int inviteSeatNum, int level,boolean isFrozen, int activeDays) {
        update(roomInfo, uid, actionType, inviteSeatNum);
        if (roomInfo.isAdmin(uid) && presenter.showChoose() && !roomInfo.isFamilyRoom()) {
            chooseImage.setImageResource(R.drawable.ic_un_select_limit);
            setOnClickListener(null);
        }
        roomMemberBaseItem.updateMemberLevel(level,isFrozen);
        roomMemberBaseItem.hideMicMarkShowMember(roomInfo, uid);
        int selfRole = roomInfo.familyRoleInfo.get(LoginHelper.getLoginUid());
        int role = roomInfo.familyRoleInfo.get(uid);
        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_ROOM_SETTING_SELECT_ADMIN) {
            memberActiveLay.setVisibility(View.GONE);
        } else {
            boolean isOwner = roomInfo.isOwner(uid);
            if ((!roomInfo.isSelfOwner() && !roomInfo.isSelfAdmin()) || (roomInfo.isFamilyRoom() && selfRole == FamilyMainInterface.ROLE_NORMAL && !roomInfo.isSelfAdmin())) {
                memberActiveLay.setVisibility(View.GONE);
            } else {
                memberActiveLay.setVisibility(View.VISIBLE);
                if (activeDays == 0) {
                    activeDaysTv.setText(ResUtil.getStr(R.string.voice_room_member_active_today));
                    activeTv.setVisibility(View.VISIBLE);
                    activeDaysTv.setVisibility(View.VISIBLE);
                } else if (activeDays == -1) {
                    activeTv.setVisibility(View.GONE);
                    activeDaysTv.setVisibility(View.GONE);
                } else {
                    activeDaysTv.setText(ResUtil.getStr(R.string.voice_room_member_active_value, activeDays));
                    activeTv.setVisibility(View.VISIBLE);
                    activeDaysTv.setVisibility(View.VISIBLE);
                }
                if ((roomInfo.isSelfOwner() && !isOwner) || (selfRole == FamilyMainInterface.ROLE_OWNER && role != FamilyMainInterface.ROLE_OWNER)) {
                    moreIv.setVisibility(View.VISIBLE);
                } else {
                    moreIv.setVisibility(View.INVISIBLE);
                }
                if (selfRole == FamilyMainInterface.ROLE_SUB_OWNER && (role != FamilyMainInterface.ROLE_SUB_OWNER && role != FamilyMainInterface.ROLE_OWNER)) {
                    moreIv.setVisibility(View.VISIBLE);
                }
                moreIv.setOnClickListener(v -> VoiceRoomMemberSetDialog.show(getContext(), roomInfo, uid, presenter));
            }
        }
        if (roomInfo.isFamilyRoom() && presenter.showChoose()) {
            if ((role == FamilyMainInterface.ROLE_OWNER) || (selfRole == FamilyMainInterface.ROLE_SUB_OWNER && role == FamilyMainInterface.ROLE_SUB_OWNER)) {
                chooseImage.setImageResource(R.drawable.ic_un_select_limit);
                setOnClickListener(null);
            } else if (selfRole == FamilyMainInterface.ROLE_OWNER || selfRole == FamilyMainInterface.ROLE_SUB_OWNER) {
                boolean isChoosed = presenter.isUserChoosed(uid);
                chooseImage.setImageResource(isChoosed ? R.drawable.ic_multi_select : R.drawable.ic_un_select);
                setOnClickListener(view -> {
                    if (presenter.isUserChoosed(uid)) {
                        chooseImage.setImageResource(R.drawable.ic_un_select);
                        presenter.unChooseUser(uid);
                    } else {
                        chooseImage.setImageResource(R.drawable.ic_multi_select);
                        presenter.chooseUser(uid);
                    }
                });
            }
        }
    }
}
