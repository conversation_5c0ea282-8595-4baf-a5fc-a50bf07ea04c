package com.wepie.wespy.module.voiceroom.member

import android.annotation.SuppressLint
import android.content.Context
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService

class VoiceRoomMemberSystemViewAdapter(
    private val mContext: Context,
    private val presenter: VoiceRoomMemberPresenter,
    private val actionType: Int,
    private val inviteSeatNum: Int
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val membersList = ArrayList<VoiceRoomInfo.RoomMemberInfo>()
    private var roomInfo = VoiceRoomService.getInstance().roomInfo

    @SuppressLint("NotifyDataSetChanged")
    fun updateMember(list: List<VoiceRoomInfo.RoomMemberInfo>?): Int {
        membersList.clear()
        list?.let {
            membersList.addAll(it)
        }
        notifyDataSetChanged()
        return membersList.size
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateMemberSearch(searchResult: List<VoiceRoomInfo.RoomMemberInfo>, clear: Boolean): Int {
        if (clear) {
            membersList.clear()
        }
        membersList.addAll(searchResult)
        notifyDataSetChanged()
        return membersList.size
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return object : RecyclerView.ViewHolder(VoiceRoomMemberItem(mContext, presenter)) {}
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder.itemView is VoiceRoomMemberItem) {
            val member = membersList[position]
            (holder.itemView as VoiceRoomMemberItem).update(
                roomInfo, member.uid,
                actionType,
                inviteSeatNum,
                member.level,
                member.isFreeze,
                member.notActiveDays
            )
        }
    }

    override fun getItemCount(): Int {
        return membersList.size
    }
}