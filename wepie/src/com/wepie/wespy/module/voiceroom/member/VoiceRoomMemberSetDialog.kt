package com.wepie.wespy.module.voiceroom.member

import android.content.Context
import android.graphics.Color
import android.view.View
import android.widget.FrameLayout
import android.widget.TextView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.dialog.HWUIDialogBuilder
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.user.LoginHelper
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog
import com.wepie.wespy.helper.dialog.bottomsheet.WpDragDialog
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.family.main.mine.family.FamilyMainInterface
import com.wepie.wespy.module.voiceroom.dataservice.RoomCallback
import com.wepie.wespy.module.voiceroom.dataservice.RoomSenderPresenter
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender

class VoiceRoomMemberSetDialog(context: Context,val roomInfo: VoiceRoomInfo, val uid: Int,val presenter: VoiceRoomMemberPresenter) : FrameLayout(context) {

    private val administratorTv: TextView
    private val memberTv: TextView
    private val cancelTv: TextView
    private var callBack = {}
    private var adminMaxCount = VoiceRoomMemberActivity.DEFAULT_MAX_ADMIN_NUM


    init {
        val dragView = WpDragDialog(context)
        val view = dragView.setContentView(R.layout.voice_room_member_set_dialog) {
            callBack.invoke()
        }
        this.addView(dragView)
        administratorTv=view.findViewById(R.id.voice_room_member_set_administrator)
        memberTv =view.findViewById(R.id.voice_room_member_remove)
        cancelTv = view.findViewById(R.id.voice_room_member_cancel)
        if (roomInfo.isAdvancedOrAnchorOrFamilyRoom) {
            val config = ConfigHelper.getInstance().voiceRoomConfig.advancedRoomConfig.getConfigByLevel(roomInfo.advanceRoomLevel)
            if (config != null) {
                adminMaxCount = config.adminNum
            }
        }
        val selfRole = roomInfo.familyRoleInfo[LoginHelper.getLoginUid()]
        val role = roomInfo.familyRoleInfo[uid]
        val situation1 =
            selfRole == FamilyMainInterface.ROLE_OWNER && (role == FamilyMainInterface.ROLE_SUB_OWNER || role == FamilyMainInterface.ROLE_ELDER)
        val situation2 =
            selfRole == FamilyMainInterface.ROLE_SUB_OWNER && role == FamilyMainInterface.ROLE_ELDER
        if (situation1 || situation2) {
            administratorTv.visibility = View.GONE
        } else {
            administratorTv.visibility = View.VISIBLE
        }

        if (roomInfo.isAdmin(uid)) {
            administratorTv.text = ResUtil.getString(R.string.room_member_remove_administrator)
            administratorTv.setTextColor(Color.parseColor("#FFFF4646"))
        } else {
            administratorTv.text = ResUtil.getStr(R.string.room_member_set_administrator,roomInfo.adminList.size,adminMaxCount)
            administratorTv.setTextColor(Color.parseColor("#FF1B1D38"))
        }

        administratorTv.setOnClickListener{
            if (roomInfo.isAdmin(uid)) {
                RoomSenderPresenter.delAdmin(roomInfo.rid, listOf(uid),
                    object : RoomCallback(this@VoiceRoomMemberSetDialog) {
                        override fun onSuccess(rid: Int) {
                            ToastUtil.show(R.string.remove_admin_success)
                            roomInfo.adminList.remove(Integer.valueOf(uid))
                            presenter.getmInterface().showManageState(roomInfo)
                            callBack.invoke()
                        }

                        override fun onFail(msg: String) {
                            ToastUtil.show(msg)
                        }
                    })
            } else {
                VoiceRoomPacketSender.addAdminList(roomInfo.rid, listOf(uid), object : LifeSeqCallback(this) {
                    override fun onSuccess(head: RspHeadInfo) {
                        ToastUtil.show(R.string.set_admin_success)
                        roomInfo.adminList.add(Integer.valueOf(uid))
                        presenter.getmInterface().showManageState(roomInfo)
                        callBack.invoke()
                    }

                    override fun onFail(head: RspHeadInfo) {
                        ToastUtil.show(head.desc)
                        callBack.invoke()
                    }
                })

            }
        }

        memberTv.setOnClickListener {
            HWUIDialogBuilder.newBuilder(context).setSingleBtn(false)
                .setTitle(R.string.room_member_remove)
                .setContent(R.string.room_member_remove_content)
                .setCanCancel(true)
                .setDialogCallback(object : HWUIDialogBuilder.DialogCallback {
                    override fun onClickSure() {
                        presenter.deleteRoomMember(uid)
                        callBack.invoke()
                    }

                    override fun onClickCancel() {
                    }
                }).show()
        }

        cancelTv.setOnClickListener{
            callBack.invoke()
        }
    }

    companion object {
        @JvmStatic
        fun show(
            context: Context, roomInfo: VoiceRoomInfo, uid: Int, presenter: VoiceRoomMemberPresenter
        ) {
            val dialog =
                BaseFullScreenDialog(context, com.huiwan.dialog.R.style.dialog_style_custom)
            val view = VoiceRoomMemberSetDialog(context, roomInfo, uid, presenter)
            view.callBack = {
                dialog.dismiss()
            }
            dialog.setContentView(view)
            dialog.setCanceledOnTouchOutside(true)
            dialog.initBottomDialog()
            dialog.show()
        }
    }
}