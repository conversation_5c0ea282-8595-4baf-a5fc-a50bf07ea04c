package com.wepie.wespy.module.voiceroom.member

import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.LayerDrawable
import android.graphics.drawable.ScaleDrawable
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.text.parseAsHtml
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ColorUtil
import com.huiwan.base.util.DrawableRecycleUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.base.util.setOnDoubleClick
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.model.voiceroom.MemberPrivilegeInfo
import com.huiwan.configservice.model.voiceroom.MemberPrivilegeLevel
import com.huiwan.configservice.model.voiceroom.MemberTask
import com.huiwan.configservice.model.voiceroom.VoiceMemberGroup
import com.huiwan.decorate.DecorHeadImgView
import com.huiwan.decorate.NameTextView
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.WebApi
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.user.LoginHelper
import com.huiwan.widget.BlackWhiteFrameLayout
import com.huiwan.widget.CustomImageView
import com.huiwan.widget.SimpleOutlineProvider
import com.huiwan.widget.WejoyLabelLayout
import com.huiwan.widget.WejoyLabelTextColorTransformer
import com.huiwan.widget.WejoyLabelTextStyleTransformer
import com.huiwan.widget.decoration.SpaceItemDecoration
import com.huiwan.widget.inflate
import com.welib.alinetlog.AliNetLogUtil
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.liblog.main.HLog
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog
import com.wepie.wespy.helper.dialog.bottomsheet.WpDragDialog
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.common.jump.JumpUtil
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginUtil
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender

class VoiceRoomMemberTaskDialog(context: Context) : FrameLayout(context), LifecycleOwner {

    private val titleResList = listOf(
        R.string.voice_room_members_task_title,
        R.string.voice_room_members_privilege_title
    )

    private var callBack = {}

    private val roomMemberTitleIv: TextView
    private val roomMemberCountIv: TextView

    private val selfInfoBg: ImageView
    private val rankTv: TextView
    private val levelIconLay: BlackWhiteFrameLayout
    private val levelIcon: ImageView
    private val levelTv: TextView
    private val levelExpTv: TextView
    private val levelExpDescTv: TextView
    private val levelProgressbar: ProgressBar

    private val taskTab: WejoyLabelLayout<WejoyLabelLayout.DefaultTabViewHolder>
    private val adapter: TaskAdapter = TaskAdapter(this)

    private val mRegistry = LifecycleRegistry(this)

    private var requestTime = -1L

    init {
        val dragView = WpDragDialog(context)
        dragView.setContentView(R.layout.voice_room_member_task_dialog) {
            dismiss()
        }
        this.addView(dragView)

        findViewById<DecorHeadImgView>(R.id.voice_room_member_head).showMyHeadDecor()
        findViewById<NameTextView>(R.id.voice_room_member_name).setRemarkUser(
            LoginHelper.getLoginUid(),
            ""
        )
        val memberGroup = ConfigHelper.getInstance().voiceRoomConfig.voiceMemberGroup
        findViewById<View>(R.id.voice_room_member_help_iv).setOnClickListener {
            ApiService.of(WebApi::class.java)
                ?.showWebDialog(context, memberGroup.helpUrl, 0, 0F)
        }

        roomMemberTitleIv = findViewById(R.id.voice_room_member_group_name_tv)
        roomMemberCountIv = findViewById(R.id.voice_room_member_count_tv)
        findViewById<View>(R.id.voice_room_member_count_iv).setOnClickListener {
            roomMemberCountIv.performClick()
        }

        selfInfoBg = findViewById(R.id.voice_room_member_task_process_bg_iv)
        rankTv = findViewById(R.id.voice_room_member_rank)
        val drawable = ResUtil.getDrawable(R.drawable.ic_arrow_right)
        val dp12 = ScreenUtil.dip2px(12F)
        drawable.setBounds(0, 0, dp12, dp12)
        rankTv.setCompoundDrawablesRelative(null, null, drawable, null)
        levelIconLay = findViewById(R.id.voice_room_member_level_icon_lay)
        levelIcon = findViewById(R.id.voice_room_member_level_iv)
        levelTv = findViewById(R.id.voice_room_member_level_tv)
        levelExpTv = findViewById(R.id.voice_room_member_level_exp_tv)
        levelExpDescTv = findViewById(R.id.voice_room_member_level_desc_tv)
        levelProgressbar = findViewById(R.id.voice_room_member_level_exp_pb)
        levelProgressbar.clipToOutline = true
        levelProgressbar.outlineProvider = SimpleOutlineProvider(ScreenUtil.dip2px(2F).toFloat())

        taskTab = findViewById(R.id.voice_room_member_task_tab)
        taskTab.addTransformer(WejoyLabelTextStyleTransformer())
        taskTab.addTransformer(
            WejoyLabelTextColorTransformer(
                ResUtil.getColor(R.color.color_text_primary),
                ResUtil.getColor(R.color.color_text_tertiary)
            )
        )

        taskTab.setAdapter(TaskTabAdapter(titleResList.map {
            ResUtil.getStr(it)
        }))

        val vp = findViewById<ViewPager2>(R.id.voice_room_member_task_vp)
        vp.adapter = adapter

        taskTab.setupViewPager(vp)

        rankTv.setOnClickListener {
            VoiceRoomMemberRankView.showInDragView(false, dragView)
        }

        VoiceRoomService.getInstance().liveData.observe(this) {
            update(it)
        }
    }

    private fun update(info: VoiceRoomInfo) {
        if (System.currentTimeMillis() - requestTime > 3000L) {
            VoiceRoomPacketSender.getMemberTaskProcess(
                info.rid,
                object : LifeSeqCallback(lifecycleOwner = this) {
                    override fun onSuccess(head: RspHeadInfo?) {
                        val rsp = head?.message ?: return
                        requestTime = System.currentTimeMillis()
                        val msg = rsp as TmpRoomPackets.MemberTaskProgressResp
                        updateSelfInfo(msg.selfInfo, info.roomMemberInfo)
                        adapter.refreshTask(rsp.taskListList.map {
                            TaskData(it)
                        })
                    }

                    override fun onFail(head: RspHeadInfo?) {
                        ToastUtil.show(head?.desc)
                    }
                })
        }

        roomMemberTitleIv.text = info.name
        roomMemberCountIv.text =
            ResUtil.getStr(R.string.voice_room_members_count_title, info.memberCount)
        roomMemberCountIv.setOnDoubleClick {
            JumpUtil.showVoiceRoomMemberDialog(
                context, info.rid, VoiceRoomMemberActivity.ACTION_TYPE_MEMBER_LIST,
                VoiceRoomMemberDialog.TAB_MEMBER_SYSYEM, -1
            )
        }

        val voiceMemberGroup = ConfigHelper.getInstance().voiceRoomConfig.voiceMemberGroup
        val memberInfo = info.roomMemberInfo
        var currentLevel = 0
        if (memberInfo != null) {
            currentLevel = memberInfo.level
            initSelfView(memberInfo, voiceMemberGroup.getPrivilegeLevel(currentLevel))
            levelTv.text = ResUtil.getStr(R.string.level_x_1, memberInfo.level)
        }
        val privilegeDataList = ArrayList<PrivilegeData>()
        voiceMemberGroup.privilegeLevels.forEachIndexed { index, level ->
            if (level.newPrivileges.isNotEmpty()) {
                privilegeDataList.add(PrivilegeData(index + 1, currentLevel > index, level))
            }
        }

        adapter.refreshPrivilege(privilegeDataList)
    }

    private fun initSelfView(
        info: VoiceRoomInfo.RoomMemberInfo,
        level: MemberPrivilegeLevel?
    ) {
        val fontColor: Int
        val startColor: Int
        val endColor: Int
        if (level == null || info.isFreeze) {
            selfInfoBg.setImageResource(R.drawable.voice_room_member_task_process_bg)
            fontColor = ResUtil.getColor(R.color.color_text_secondary)
            startColor = ColorUtil.getColor("#9A9A9A")
            endColor = ColorUtil.getColor("#BDBDBD")
        } else {
            WpImageLoader.load(
                level.bgUrl, selfInfoBg,
                ImageLoadInfo.newInfo().placeholder(R.drawable.voice_room_member_task_process_bg)
            )
            fontColor = ColorUtil.getColor(level.fontColor)
            startColor = ColorUtil.getColor(level.startColor)
            endColor = ColorUtil.getColor(level.endColor)
        }
        if (info.isFreeze) {
            levelIcon.alpha = 0.8F
            levelIconLay.setBlackWhiteEnable(true)
        } else {
            levelIcon.alpha = 1F
            levelIconLay.setBlackWhiteEnable(false)
        }
        val levelIconUrl = level?.icon ?: ""
        if (TextUtils.isEmpty(levelIconUrl)) {
            DrawableRecycleUtil.reset(levelIcon)
            HLog.aliLog(
                AliNetLogUtil.PORT.HttpConfig,
                AliNetLogUtil.TYPE.warning,
                "memberLevel: " + info.level
            )
        } else {
            WpImageLoader.load(levelIconUrl, levelIcon)
        }
        levelTv.setTextColor(fontColor)

        val dp2 = ScreenUtil.dip2px(2f)
        val progressBackground = GradientDrawable()
        progressBackground.setColor(ResUtil.getColor(R.color.white_alpha50))
        progressBackground.cornerRadius = dp2.toFloat()
        val progressContent = GradientDrawable(
            GradientDrawable.Orientation.BL_TR,
            intArrayOf(startColor, endColor)
        )
        progressContent.cornerRadius = dp2.toFloat()
        val drawable = ScaleDrawable(
            progressContent, if (ScreenUtil.isRtl()) Gravity.END else Gravity.START,
            1f, 0f
        )
        val layerDrawable = LayerDrawable(arrayOf(progressBackground, drawable))
        layerDrawable.setId(0, android.R.id.background)
        layerDrawable.setId(1, android.R.id.progress)
        levelProgressbar.progressDrawable = layerDrawable
    }

    private fun updateSelfInfo(
        selfInfo: TmpRoomPackets.MemberTaskSelfInfo,
        roomMemberInfo: VoiceRoomInfo.RoomMemberInfo?
    ) {
        val voiceMemberGroup = ConfigHelper.getInstance().voiceRoomConfig.voiceMemberGroup
        levelProgressbar.max = selfInfo.currLevelPoints
        levelProgressbar.progress = selfInfo.currGotPoints
        rankTv.text = ResUtil.getStr(
            R.string.common_no_x, if (selfInfo.weekRank > 99) {
                "99+"
            } else if (selfInfo.weekRank <= 0) {
                "-"
            } else {
                "${selfInfo.weekRank}"
            }
        )
        if (roomMemberInfo?.isFreeze == false) {
            levelProgressbar.setBackgroundColor(ResUtil.getColor(R.color.white_alpha50))
            rankTv.setTextColor(ResUtil.getColor(R.color.color_yellow_default))
            levelExpTv.setTextColor(ResUtil.getColor(R.color.color_text_secondary))
            levelExpDescTv.isVisible = true
            if (selfInfo.currGotPoints >= selfInfo.currLevelPoints && roomMemberInfo.level >= voiceMemberGroup.privilegeLevels.size) {
                levelExpTv.text = "${selfInfo.currGotPoints}"
                levelExpDescTv.text = ResUtil.getStr(R.string.voice_room_members_max_level_title)
            } else {
                levelExpTv.text = "${selfInfo.currGotPoints}/${selfInfo.currLevelPoints}"
                levelExpDescTv.text = ResUtil.getStr(
                    R.string.voice_room_members_level_up_title,
                    selfInfo.currLevelPoints - selfInfo.currGotPoints
                )
            }
        } else {
            levelProgressbar.setBackgroundColor(Color.WHITE)
            rankTv.setTextColor(ResUtil.getColor(R.color.color_text_default))
            levelExpTv.text = ResUtil.getStr(R.string.voice_room_members_level_pause_title)
            levelExpTv.setTextColor(ResUtil.getColor(R.color.color_text_tertiary))
            levelExpDescTv.isVisible = false
        }
    }

    private fun dismiss() {
        callBack.invoke()
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        mRegistry.currentState = Lifecycle.State.CREATED
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mRegistry.currentState = Lifecycle.State.DESTROYED
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        super.onVisibilityChanged(changedView, visibility)
        if (visibility == VISIBLE) {
            mRegistry.handleLifecycleEvent(Lifecycle.Event.ON_START)
            mRegistry.handleLifecycleEvent(Lifecycle.Event.ON_RESUME)
        } else {
            mRegistry.handleLifecycleEvent(Lifecycle.Event.ON_PAUSE)
            mRegistry.handleLifecycleEvent(Lifecycle.Event.ON_STOP)
        }
    }

    override val lifecycle: Lifecycle
        get() = mRegistry

    companion object {
        @JvmStatic
        fun show(context: Context) {
            val dialog =
                BaseFullScreenDialog(context, R.style.dialog_style_custom)
            val view = VoiceRoomMemberTaskDialog(context)
            view.callBack = {
                dialog.dismiss()
            }
            dialog.setDimAmount(0F)
            dialog.setContentView(view)
            dialog.setCanceledOnTouchOutside(true)
            dialog.initFullBottomDialog()
            dialog.show()
            PluginUtil.getMainPlugin(context).life?.onDestroy {
                dialog.dismiss()
            }
        }
    }

    class TaskTabAdapter(private val data: List<String>) :
        WejoyLabelLayout.DefaultTabAdapter(data) {
        override fun tabLayoutRes(): Int = R.layout.item_voice_room_task_tab_type

        override fun findTextViewId(): Int = R.id.voice_room_task_type_tv
    }

    class TaskData(item: TmpRoomPackets.UserTaskProgressItem) {
        private var task: MemberTask? = null
        private val taskId = item.taskId
        val limit = item.limit
        val point = item.gotPoints

        fun getTask(): MemberTask? {
            if (task != null) {
                return task
            }
            for (t in ConfigHelper.getInstance().voiceRoomConfig.voiceMemberGroup.tasks) {
                if (t.id == taskId) {
                    task = t
                    break
                }
            }
            return task
        }
    }

    class PrivilegeData(
        val level: Int,
        val isGot: Boolean,
        privilegeLevels: MemberPrivilegeLevel
    ) {
        private var newPrivileges: List<Int> = privilegeLevels.newPrivileges
        private var realNewPrivilegeList: List<MemberPrivilegeInfo>? = null

        fun getNewPrivilegeList(): List<MemberPrivilegeInfo> {
            if (realNewPrivilegeList != null) {
                return realNewPrivilegeList!!
            }
            val privilegeList = ArrayList<MemberPrivilegeInfo>()
            val list =
                ConfigHelper.getInstance().voiceRoomConfig.voiceMemberGroup.privilegeInfos.toMutableList()
            newPrivileges.forEach { id ->
                val iterator = list.iterator()
                while (iterator.hasNext()) {
                    val info = iterator.next()
                    if (info.id == id) {
                        privilegeList.add(info)
                        iterator.remove()
                        break
                    }
                }
            }
            realNewPrivilegeList = privilegeList
            return realNewPrivilegeList!!
        }
    }

    class TaskAdapter(private val dialog: VoiceRoomMemberTaskDialog) :
        RecyclerView.Adapter<RecyclerView.ViewHolder>() {

        private var taskList: MutableList<TaskData> = ArrayList()

        private var privilegeList: MutableList<PrivilegeData> = ArrayList()

        fun refreshTask(list: List<TaskData>) {
            if (taskList.isNotEmpty()) {
                taskList.clear()
            }
            taskList.addAll(list)
            notifyItemChanged(0)
        }

        fun refreshPrivilege(list: List<PrivilegeData>) {
            if (privilegeList.isNotEmpty()) {
                privilegeList.clear()
            }
            privilegeList.addAll(list)
            notifyItemChanged(1)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            val rv = RecyclerView(parent.context)
            rv.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            return if (viewType == 0) {
                TaskViewHolder(rv, dialog)
            } else {
                PrivilegeLevelViewHolder(rv)
            }
        }

        override fun getItemCount(): Int = 2

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is TaskViewHolder) {
                holder.update(taskList)
            } else if (holder is PrivilegeLevelViewHolder) {
                holder.update(privilegeList)
            }
        }

        override fun getItemViewType(position: Int): Int {
            return position
        }
    }

    class TaskViewHolder(itemView: RecyclerView, val dialog: VoiceRoomMemberTaskDialog) :
        RecyclerView.ViewHolder(itemView) {

        var taskList: MutableList<TaskData> = ArrayList()

        val adapter = object : RecyclerView.Adapter<TaskItemViewHolder>() {

            override fun onCreateViewHolder(
                parent: ViewGroup, viewType: Int
            ): TaskItemViewHolder {
                return TaskItemViewHolder(parent.inflate(R.layout.voice_room_member_task_item))
            }

            override fun getItemCount(): Int = taskList.size

            override fun onBindViewHolder(holder: TaskItemViewHolder, position: Int) {
                val item = taskList[position]
                item.getTask()?.let {
                    holder.taskTitle.text = ResUtil.getStr(
                        R.string.voice_room_members_task_process_title,
                        it.title, item.point, item.limit
                    ).parseAsHtml()
                    holder.taskDesc.text = it.desc
                    WpImageLoader.load(it.icon, holder.taskIcon)
                    if (item.point >= item.limit) {
                        holder.taskBt.setTextColor(Color.parseColor("#CCCDD6"))
                        holder.taskBt.isEnabled = false
                        holder.taskBt.text =
                            ResUtil.getStr(R.string.voice_room_members_done_task_title)
                    } else {
                        holder.taskBt.setTextColor(ResUtil.getColor(R.color.color_white))
                        holder.taskBt.isEnabled = true
                        holder.taskBt.text =
                            ResUtil.getStr(R.string.voice_room_members_do_task_title)
                    }
                }
                holder.taskBt.setOnClickListener {
                    dialog.dismiss()
                }
            }

        }

        init {
            itemView.layoutManager = LinearLayoutManager(itemView.context)
            itemView.adapter = adapter
            val dp8 = ScreenUtil.dip2px(8F)
            val dp24 = dp8 * 3

            itemView.addItemDecoration(
                SpaceItemDecoration(
                    Rect(0, dp24, 0, 0),
                    Rect(0, dp8, 0, 0)
                )
            )
        }

        fun update(taskList: MutableList<TaskData>) {
            this.taskList = taskList
            adapter.notifyDataSetChanged()
        }

    }

    class PrivilegeLevelViewHolder(itemView: RecyclerView) : RecyclerView.ViewHolder(itemView) {

        var privilegeList: MutableList<PrivilegeData> = ArrayList()

        val adapter = object : RecyclerView.Adapter<PrivilegeLevelItemViewHolder>() {

            override fun onCreateViewHolder(
                parent: ViewGroup, viewType: Int
            ): PrivilegeLevelItemViewHolder {
                return PrivilegeLevelItemViewHolder(parent.inflate(R.layout.voice_room_member_privilege_level_item))
            }

            override fun getItemCount(): Int = privilegeList.size

            override fun onBindViewHolder(holder: PrivilegeLevelItemViewHolder, position: Int) {
                holder.update(privilegeList[position])
            }

        }

        init {
            itemView.layoutManager = LinearLayoutManager(itemView.context)
            itemView.adapter = adapter
            val dp8 = ScreenUtil.dip2px(8F)
            itemView.addItemDecoration(SpaceItemDecoration(dp8, 0, 0, 0))
            itemView.addItemDecoration(SpaceItemDecoration(Rect(), Rect(0, 0, 0, dp8 * 2)))
        }

        fun update(privilegeList: MutableList<PrivilegeData>) {
            this.privilegeList = privilegeList
            adapter.notifyDataSetChanged()
        }
    }

    class TaskItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val taskIcon: ImageView = itemView.findViewById(R.id.member_task_icon)
        val taskTitle: TextView = itemView.findViewById(R.id.member_task_title)
        val taskDesc: TextView = itemView.findViewById(R.id.member_task_desc)
        val taskBt: TextView = itemView.findViewById(R.id.member_task_bt)
    }

    class PrivilegeLevelItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val lockIv: ImageView =
            itemView.findViewById(R.id.voice_room_member_privilege_lock_iv)
        private val privilegeLevelDescTv: TextView =
            itemView.findViewById(R.id.voice_room_member_privilege_level_desc_tv)

        private val privilegeContent: ViewGroup =
            itemView.findViewById(R.id.voice_room_member_privilege_item_lay)

        init {
            itemView.clipToOutline = true
            itemView.outlineProvider = SimpleOutlineProvider(ScreenUtil.dip2px(12F).toFloat())
        }

        fun update(data: PrivilegeData) {
            if (data.isGot) {
                privilegeLevelDescTv.setTextColor(ResUtil.getColor(R.color.color_text_secondary))
                privilegeLevelDescTv.text =
                    ResUtil.getStr(R.string.voice_room_members_level_privilege_title, data.level)
                lockIv.isVisible = false
            } else {
                privilegeLevelDescTv.setTextColor(ResUtil.getColor(R.color.color_text_default))
                lockIv.isVisible = true
                privilegeLevelDescTv.text = ResUtil.getStr(
                    R.string.voice_room_members_level_new_privilege_title, data.level
                )
            }

            val newPrivilegeList = data.getNewPrivilegeList()
            val size = newPrivilegeList.size
            val count = privilegeContent.childCount
            if (count < size) {
                for (i in count until size) {
                    privilegeContent.addView(PrivilegeItemView(itemView.context))
                }
            } else if (count > size) {
                for (i in size until count) {
                    privilegeContent.getChildAt(i).isVisible = false
                }
            }
            val lastIndex = size - 1
            newPrivilegeList.forEachIndexed { index, info ->
                val view = privilegeContent.getChildAt(index) as PrivilegeItemView
                view.isVisible = true
                view.update(info, data.isGot, index == lastIndex)
            }
        }
    }

    open class PrivilegeItemView(context: Context) : ConstraintLayout(context) {

        private val privilegeDescTv: TextView
        private val privilegeIcon: ImageView
        private val privilegeLay: ViewGroup
        private val line: View


        init {
            LayoutInflater.from(context)
                .inflate(R.layout.voice_room_member_privilege_item, this)
            privilegeDescTv = findViewById(R.id.voice_room_member_privilege_desc)
            privilegeIcon = findViewById(R.id.voice_room_member_privilege_icon)
            privilegeLay = findViewById(R.id.voice_room_member_privilege_lay)
            line = findViewById(R.id.voice_room_member_privilege_line)

            privilegeLay.clipToOutline = true
            privilegeLay.outlineProvider = SimpleOutlineProvider(ScreenUtil.dip2px(12F).toFloat())
        }

        fun update(info: MemberPrivilegeInfo, isGot: Boolean, isLast: Boolean) {
            line.isVisible = !isLast
            privilegeDescTv.text = info.title
            if (isGot) {
                privilegeDescTv.setTextColor(ResUtil.getColor(R.color.color_text_primary))
            } else {
                privilegeDescTv.setTextColor(ResUtil.getColor(R.color.color_text_tertiary))
            }
            when (info.id) {
                VoiceMemberGroup.BLUE_SPEAKER_ANIM_PRIVILEGE_ID -> {
                    showSpeakerAnim("svga/voiceroom/voice_room_speaker_blue_anim.svga")
                }

                VoiceMemberGroup.GOLD_SPEAKER_ANIM_PRIVILEGE_ID -> {
                    showSpeakerAnim("svga/voiceroom/voice_room_speaker_gold_anim.svga")
                }

                else -> {
                    privilegeIcon.isVisible = true
                    privilegeLay.isVisible = false
                    WpImageLoader.load(info.icon, privilegeIcon)
                }
            }
        }

        private fun showSpeakerAnim(svgaRes: String) {
            privilegeIcon.isVisible = false
            privilegeLay.removeAllViews()
            val animView = CustomImageView(context)
            val dp60 = ScreenUtil.dip2px(36F)
            animView.scaleX = 2F
            animView.scaleY = 2F
            val animParams = FrameLayout.LayoutParams(dp60, dp60)
            animParams.gravity = Gravity.CENTER
            WpImageLoader.load(WpImageLoader.getAssetUri(svgaRes), animView)
            privilegeLay.addView(animView, animParams)

            val headView = DecorHeadImgView(context)
            val dp47 = ScreenUtil.dip2px(47F)
            val headParams = FrameLayout.LayoutParams(dp47, dp47)
            headParams.gravity = Gravity.CENTER
            headView.showUserHeadWithDecorationCache(LoginHelper.getLoginUid())
            privilegeLay.addView(headView, headParams)
            privilegeLay.isVisible = true
        }

    }
}

