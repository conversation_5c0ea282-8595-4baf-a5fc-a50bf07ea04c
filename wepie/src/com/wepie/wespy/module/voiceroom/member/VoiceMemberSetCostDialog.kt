package com.wepie.wespy.module.voiceroom.member

import android.content.Context
import android.graphics.Rect
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.FontUtil.getTypeface
import com.huiwan.base.util.ScreenUtil
import com.huiwan.base.util.ToastUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.widget.decoration.SpaceItemDecoration
import com.huiwan.widget.inflate
import com.wepie.libimageloader.ImageLoadInfo
import com.wepie.libimageloader.WpImageLoader
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog
import com.wepie.wespy.helper.dialog.bottomsheet.WpDragDialog
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender

class VoiceMemberSetCostDialog : FrameLayout {
    private var callBack = {}
    private var refreshCallBack = {}
    private val recycleView: RecyclerView by lazy { findViewById(R.id.member_set_cost_gift_rv) }
    private val confirmBtn: TextView by lazy { findViewById(R.id.member_set_cost_confirm_btn) }
    private var rid: Int = 0
    private var giftId: Int = -1

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    )

    init {
        val dragView = WpDragDialog(context)
        val view = dragView.setContentView(R.layout.voice_room_member_set_cost) {
            callBack.invoke()
        }
        this.addView(dragView)
    }

    private fun setJoinMemberGift() {
        VoiceRoomPacketSender.setJoinMemberGift(rid, giftId, object : LifeSeqCallback(this) {
            override fun onSuccess(head: RspHeadInfo?) {
                refreshCallBack.invoke()
                callBack.invoke()
            }

            override fun onFail(head: RspHeadInfo?) {
                ToastUtil.show(head?.desc)
            }
        })
    }

    private fun update(rid: Int, giftId: Int) {
        this.rid = rid
        this.giftId = giftId
        val data =
            ConfigHelper.getInstance().voiceRoomConfig.voiceMemberGroup.getMemberChoiceList()
        // -1代表免费加入成员
        if (data.isEmpty()) {
            data.add(0, -1)
        } else if (data[0] != -1) {
            if (data.contains(-1)) data.remove(-1)
            data.add(0, -1)
        }
        val adapter = MemberCostAdapter()
        adapter.refreshData(data)
        adapter.setSelectGiftId(giftId)
        adapter.setOnItemClickListener(object : MemberCostAdapter.OnItemClickListener {
            override fun onItemClick(view: View, position: Int) {
                <EMAIL> = data[position]
                adapter.setSelectGiftId(data[position])
                adapter.notifyItemRangeChanged(0, data.size)
            }
        })
        recycleView.adapter = adapter
        val dp16 = ScreenUtil.dip2px(16f)
        recycleView.addItemDecoration(
            SpaceItemDecoration(
                Rect(0, 0, ScreenUtil.dip2px(8f), 0),
                Rect(dp16, 0, dp16, 0)
            )
        )
        confirmBtn.setOnClickListener { setJoinMemberGift() }
    }

    companion object {
        @JvmStatic
        fun show(context: Context, rid: Int, giftId: Int, refreshCallback: () -> Unit) {
            val dialog =
                BaseFullScreenDialog(context, com.huiwan.dialog.R.style.dialog_style_custom)
            val view = VoiceMemberSetCostDialog(context)
            view.callBack = {
                dialog.dismiss()
            }
            view.refreshCallBack = refreshCallback
            view.update(rid, giftId)
            dialog.setContentView(view)
            dialog.setCanceledOnTouchOutside(true)
            dialog.initBottomDialog()
            dialog.show()
        }
    }
}

class MemberCostAdapter : RecyclerView.Adapter<MemberCostItemViewHolder>() {
    private val data: MutableList<Int> = ArrayList()
    private var selectGiftId: Int = -1
    private var onItemClickListener: OnItemClickListener? = null
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MemberCostItemViewHolder {
        return if (viewType == 0) MemberCostEmptyViewHolder(
            parent.inflate(
                R.layout.voice_room_member_cost_set_empty_item,
                false
            )
        )
        else MemberCostGiftViewHolder(
            parent.inflate(
                R.layout.voice_room_member_cost_set_item,
                false
            )
        )
    }

    override fun getItemCount(): Int {
        return data.size
    }

    override fun onBindViewHolder(holder: MemberCostItemViewHolder, position: Int) {
        val giftId = data[position]
        holder.bindData(giftId, giftId == selectGiftId)
        holder.itemView.setOnClickListener {
            onItemClickListener?.onItemClick(holder.itemView, position)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (data[position] == -1) 0 else 1
    }

    fun refreshData(list: MutableList<Int>) {
        data.clear()
        data.addAll(list)
        notifyItemRangeChanged(0, data.size)
    }

    fun setSelectGiftId(id: Int) {
        selectGiftId = id
    }

    fun setOnItemClickListener(listener: OnItemClickListener) {
        onItemClickListener = listener
    }

    interface OnItemClickListener {
        fun onItemClick(view: View, position: Int)
    }
}

class MemberCostGiftViewHolder(itemView: View) : MemberCostItemViewHolder(itemView) {
    private val giftIcon: ImageView = itemView.findViewById(R.id.member_cost_item_gift_icon)
    private val coinCountTv: TextView = itemView.findViewById(R.id.member_cost_coin_tv)
    private val giftName: TextView = itemView.findViewById(R.id.member_cost_item_gift_name)

    override fun bindData(giftId: Int, isSelect: Boolean) {
        if (giftId > 0) {
            val gift = ConfigHelper.getInstance().giftConfig.getGift(giftId)
            gift?.let {
                coinCountTv.text = it.price.toString()
                giftName.text = it.name
                WpImageLoader.load(it.media_url, giftIcon, ImageLoadInfo.getGiftInfo())
            }
            updateSelect(isSelect)
        }
    }

    private fun updateSelect(isSelect: Boolean) {
        itemView.isSelected = isSelect
        if (isSelect) {
            giftName.setTextColor(ResUtil.getColor(R.color.color_accent))
            giftName.typeface = getTypeface(Typeface.BOLD)
            giftName.isFocusable = true
        } else {
            giftName.setTextColor(ResUtil.getColor(R.color.text_secondary))
            giftName.typeface = getTypeface(Typeface.NORMAL)
            giftName.isFocusable = false
        }
    }
}

class MemberCostEmptyViewHolder(itemView: View) : MemberCostItemViewHolder(itemView) {
    private val freeTv: TextView = itemView.findViewById(R.id.member_cost_free_item_tv)

    override fun bindData(giftId: Int, isSelect: Boolean) {
        itemView.isSelected = isSelect
        updateSelect(isSelect)
    }

    private fun updateSelect(isSelect: Boolean) {
        itemView.isSelected = isSelect
        if (isSelect) {
            freeTv.setTextColor(ResUtil.getColor(R.color.color_accent))
            freeTv.typeface = getTypeface(Typeface.BOLD)
        } else {
            freeTv.setTextColor(ResUtil.getColor(R.color.text_secondary))
            freeTv.typeface = getTypeface(Typeface.NORMAL)
        }
    }
}

open class MemberCostItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

    open fun bindData(giftId: Int, isSelect: Boolean) {}
}