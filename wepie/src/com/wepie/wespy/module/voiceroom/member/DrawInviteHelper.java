package com.wepie.wespy.module.voiceroom.member;

import android.content.Context;

import com.wepie.wespy.R;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.huiwan.user.LoginHelper;
import com.wepie.wespy.module.voiceroom.dataservice.RoomCallback;
import com.wepie.wespy.module.voiceroom.dataservice.RoomSenderPresenter;
import com.huiwan.base.util.ToastUtil;

/**
 * date 2019/3/16
 * email <EMAIL>
 *
 * <AUTHOR>
 */
class DrawInviteHelper {
     static void inviteDraw(final Context context, int rid, int uid) {
        if (uid == LoginHelper.getLoginUid()) {
            RoomSenderPresenter.drawInviteConfirm(rid, uid, true, new RoomCallback(context) {
                @Override
                public void onSuccess(int rid) {
                    JumpUtil.gotoVoiceRoomActivity(context, rid);
                }

                @Override
                public void onFail(String msg) {
                    ToastUtil.show(msg);
                }
            });
        } else {
            RoomSenderPresenter.inviteDraw(rid, uid, new RoomCallback(context) {
                @Override
                public void onSuccess(int rid) {
                    ToastUtil.show(R.string.voice_room_members_op_sel_invite_success_tip);
                    JumpUtil.gotoVoiceRoomActivity(context, rid);
                }

                @Override
                public void onFail(String msg) {ToastUtil.show(msg); }
            });
        }
    }
}
