package com.wepie.wespy.module.voiceroom.member

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.collection.SparseArrayCompat
import androidx.core.view.children
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.FontUtil
import com.huiwan.constants.IntentConfig
import com.huiwan.user.LoginHelper
import com.huiwan.widget.TabAdapter
import com.huiwan.widget.TabViewHolder
import com.huiwan.widget.WejoyLabelLayout
import com.huiwan.widget.scripple.KeyBoardListener
import com.huiwan.widget.scripple.KeyboardListenerFrameLayout
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.BaseDialogFragment
import com.wepie.wespy.helper.dialog.bottomsheet.WpDragDialog
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.family.main.mine.family.FamilyMainInterface
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.guards.VoiceRoomGuardsView
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 语音房 在线 守护 粉丝管理base页面
 * <AUTHOR>
 * 2022/3/30
 */
class VoiceRoomMemberDialog : BaseDialogFragment() {

    private lateinit var adapter: MemberPagerAdapter
    private lateinit var mPager: ViewPager2

    private lateinit var mOnlineSettings: ImageView
    private val indicatorAdapter = MemberTabAdapter()
    private lateinit var memberIndicator: WejoyLabelLayout<MemberTabViewHolder>

    private var tabIndex = TAB_ONLINE

    private lateinit var model: VoiceMemberViewModel

    private lateinit var roomInfo: VoiceRoomInfo
    private lateinit var mVoiceRoomMemberBgLayout: KeyboardListenerFrameLayout

    private var touchLimit: AtomicBoolean = AtomicBoolean(false)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        model = ViewModelProvider(this)[VoiceMemberViewModel::class.java]
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val parent = FrameLayout(requireContext())
        parent.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        parent.setOnClickListener {
            dismiss()
            clearList()
        }

        val dragDialog = WpDragDialog(requireContext())
        dragDialog.fitsSystemWindows = true
        dragDialog.layoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            Gravity.BOTTOM
        )
        dragDialog.setContentView(R.layout.voice_room_member_dialog_layout) {
            dismiss()
            clearList()
        }
        parent.addView(dragDialog)
        return parent
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.window?.setDimAmount(0F)
        return dialog
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        initData()
    }

    override fun onStart() {
        super.onStart()
        requireActivity().window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_UNSPECIFIED)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        mDialog = null
    }

    fun initViews(view: View) {
        //整个大背景
        mVoiceRoomMemberBgLayout = view.findViewById(R.id.voice_room_member_bg_layout)

        //pager
        mPager = view.findViewById(R.id.voice_room_layout_pager)
        memberIndicator = view.findViewById(R.id.member_tab_layout)

        //房间管理title 取消 在线 移出
        mOnlineSettings = view.findViewById(R.id.voice_room_member_online_settings)

        mOnlineSettings.setOnClickListener {
            model.setManageState(true)
        }

        mVoiceRoomMemberBgLayout.setOnClickListener {
            if (mVoiceRoomMemberBgLayout.isKeyBoardShow) {
                hideKeyboard()
            } else {
                dismiss()
                //每次退出列表的时候清理掉语音房的缓存列表信息
                //为什么清除?
                //因为之前是加载全部,所以每次都会清除全部uid列表,然后刷新全部uid列表
                //现在是分页加载,如果继续按原来的逻辑,语音房只能保存一页的用户uid
                //因为每次会清空上一页,因此,这里退出界面时清空
                VoiceRoomService.getInstance().getRoomInfo(model.getRid()).uidList.clear()
            }
        }
        mVoiceRoomMemberBgLayout.setKeyBoardListener(object : KeyBoardListener {
            override fun keyBoardShow(firstTimeShow: Boolean, isShowCoverLay: Boolean) {
                touchLimit.set(false)
            }

            override fun keyBoardHide() {
//                onlineView.removeFocus()
            }
        })

        view.findViewById<View>(R.id.voice_room_member_main_lay).setOnClickListener {}
        adapter = MemberPagerAdapter(this, object : VoiceRoomOnlineView.MemberListener {
            override fun onListTouch() {
                hideKeyboard()
            }
        })
        mPager.adapter = adapter
        memberIndicator.children.forEach {
            if (it is ViewGroup) {
                it.clipChildren = false
                it.clipToPadding = false
            }
        }
        memberIndicator.addTransformer(object :
            WejoyLabelLayout.ISelectTransformer<MemberTabViewHolder> {
            override fun onTransform(
                holder: MemberTabViewHolder,
                position: Int,
                isSelect: Boolean
            ) {
                holder.setTab(isSelect)
            }
        })
        memberIndicator.setAdapter(indicatorAdapter)
        memberIndicator.setupViewPager(mPager)

        mPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
                hideKeyboard()
            }

            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                headerControl(position)
            }
        })

        view.setOnTouchListener { _, _ ->
            hideKeyboard()
            false
        }
    }

    fun initData() {
        //初始化intent数据
        val rid = arguments?.getInt(IntentConfig.INT_ROOM_ID) ?: 0

        roomInfo = VoiceRoomService.getInstance().getRoomInfo(rid)
        VoiceRoomService.getInstance().liveData.observe(this) {
            if (it.rid != rid) {
                dismiss()
                clearList()
            } else {
                indicatorAdapter.holderArray[TAB_ONLINE]?.setNum(it.onlineNum)
                indicatorAdapter.holderArray[TAB_MEMBER_SYSYEM]?.setNum(it.memberCount)
            }
        }

        val actionType = arguments?.getInt(IntentConfig.VOICE_ROOM_MEMBER_LIST_ACTION)
            ?: VoiceRoomMemberActivity.ACTION_TYPE_MEMBER_LIST
        tabIndex = arguments?.getInt(VOICE_ROOM_MEMBER_TAB_INDEX) ?: TAB_ONLINE
        val seatNum = arguments?.getInt(IntentConfig.VOICE_ROOM_SEAT_NUM) ?: -1

        model.getOnlineViewManageLiveData().observe(this) {
            hideIndicator(it)
        }

        model.init(roomInfo, actionType, seatNum)

        //邀请处理
        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_DRAWER
            || actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE
        ) {
            //如果是画猜邀请或者上麦邀请,则没有多余的tab
            hideIndicator(true)
            model.getSelectItemLiveData().observe(viewLifecycleOwner) {
                dismiss()
                clearList()
            }
            adapter.update(listOf(VoiceRoomOnlineView::class.java))
        } else {
            //如果不是高级房或者主播房,则只有在线列表
            if (!roomInfo.isAdvancedOrAnchorOrFamilyRoom) {
                adapter.update(listOf(VoiceRoomOnlineView::class.java))
                memberIndicator.setSelectedTabIndicator(null)
            } else {
                //其他情况就是全部tab显示的情况
                adapter.update(
                    listOf(
                        VoiceRoomOnlineView::class.java,
                        VoiceRoomMemberSystemView::class.java,
                        VoiceRoomGuardsView::class.java
                    )
                )
                memberIndicator.setSelectedTabIndicator(R.drawable.voice_room_member_tab_indicator)
            }
        }
        mPager.setCurrentItem(tabIndex, false)
    }

    //弹窗消失时清除列表
    fun clearList() {
        VoiceRoomService.getInstance().getRoomInfo(model.getRid()).uidList.clear()
    }

    /**
     * 关闭输入框
     */
    private fun hideKeyboard() {
        if (!touchLimit.get()) {
            touchLimit.set(true)
            if (mVoiceRoomMemberBgLayout.isKeyBoardShow) {
                val imm: InputMethodManager =
                    requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager //得到InputMethodManager的实例
                if (imm.isActive) { //如果开启
                    imm.toggleSoftInput(
                        InputMethodManager.SHOW_IMPLICIT,
                        InputMethodManager.HIDE_NOT_ALWAYS
                    ) //关闭软键盘，开启方法相同，这个方法是切换开启与关闭状态的
                }
            }
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
    }

    /**
     * 是否隐藏指示器
     * @param hide true 隐藏, false 显示在线,房管,粉丝
     */
    private fun hideIndicator(hide: Boolean) {
        if (hide) {
            mPager.isUserInputEnabled = false //true:滑动，false：禁止滑动
            mOnlineSettings.visibility = View.GONE
            memberIndicator.visibility = View.GONE
        } else {
            mPager.isUserInputEnabled = true //true:滑动，false：禁止滑动
            mOnlineSettings.visibility = View.VISIBLE
            memberIndicator.visibility = View.VISIBLE
        }
    }

    /**
     * 控制tab的状态
     *
     * @param position pager位置
     */
    fun headerControl(position: Int) {
        if (memberIndicator.visibility == View.GONE) {
            return
        }
        //区别在线列表和成员列表、高级房和家族房
        val isFamilyRoom = roomInfo.isFamilyRoom
        val selfRole = roomInfo.familyRoleInfo[LoginHelper.getLoginUid()]
        if (position == TAB_ONLINE && !isFamilyRoom) {
            if (roomInfo.isSelfAdminOrOwner) {
                mOnlineSettings.visibility = View.VISIBLE
            } else {
                mOnlineSettings.visibility = View.GONE
            }
        } else if (position == TAB_ONLINE) {
            val isCanKick = selfRole == FamilyMainInterface.ROLE_OWNER || selfRole == FamilyMainInterface.ROLE_SUB_OWNER || selfRole == FamilyMainInterface.ROLE_ELDER
            if (roomInfo.isSelfAdminOrOwner || isCanKick) {
                mOnlineSettings.visibility = View.VISIBLE
            } else {
                mOnlineSettings.visibility = View.GONE
            }
        } else if (position == TAB_MEMBER_SYSYEM && !isFamilyRoom) {
            if (roomInfo.isSelfOwner) {
                mOnlineSettings.visibility = View.VISIBLE
            } else {
                mOnlineSettings.visibility = View.GONE
            }
        } else if (position == TAB_MEMBER_SYSYEM) {
            if (selfRole == FamilyMainInterface.ROLE_OWNER || selfRole == FamilyMainInterface.ROLE_SUB_OWNER) {
                mOnlineSettings.visibility = View.VISIBLE
            } else {
                mOnlineSettings.visibility = View.GONE
            }
        } else {
            mOnlineSettings.visibility = View.GONE
        }
    }

    inner class MemberPagerAdapter(
        fragment: Fragment,
        val listener: VoiceRoomOnlineView.MemberListener?
    ) : FragmentStateAdapter(fragment) {

        private val classList: MutableList<Class<out Fragment>> = ArrayList()

        override fun getItemCount(): Int = classList.size

        override fun createFragment(position: Int): Fragment {
            val fragment = classList[position].newInstance()
            if (fragment is VoiceRoomOnlineView) {
                fragment.setMemberListener(listener)
            }
            return fragment
        }

        fun update(list: List<Class<out Fragment>>) {
            if (classList.isNotEmpty()) {
                classList.clear()
            }
            classList.addAll(list)
            notifyItemRangeChanged(0, classList.size)
        }
    }

    companion object {
        const val TAB_ONLINE = 0
        const val TAB_MEMBER_SYSYEM = 1
        const val TAB_CONTRIBUTION = 2
        const val VOICE_ROOM_MEMBER_TAB_INDEX = "voice_room_member_tab_index"

        internal val TITLE_RES = arrayOf(R.string.online_text, R.string.member_text, R.string.contribution)

        private var mDialog: VoiceRoomMemberDialog? = null

        @JvmOverloads
        @JvmStatic
        fun show(context: Context, intent: Intent, tabIndex: Int = TAB_ONLINE) {
            val fragment = VoiceRoomMemberDialog()
            fragment.initFullWidth()
            intent.extras?.putInt(VOICE_ROOM_MEMBER_TAB_INDEX, tabIndex)
            fragment.setWindowAnim(R.style.bottomDialogAnimation)
            fragment.initBottom()
            fragment.show(context, intent.extras)
            mDialog = fragment
        }

        @JvmStatic
        fun hide() {
            mDialog?.dismiss()
            mDialog?.clearList()
        }
    }
}

class MemberTabAdapter : TabAdapter<MemberTabViewHolder>() {

    val holderArray: SparseArrayCompat<MemberTabViewHolder> by lazy {
        SparseArrayCompat()
    }

    override fun onCreateTabView(tab: com.huiwan.widget.TabLayout.Tab): MemberTabViewHolder {
        val parent = tab.view
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.voice_room_member_dialog_indicator_item, parent, false)
        parent.clipChildren = false
        parent.clipToPadding = false
        return MemberTabViewHolder(view)
    }

    override fun getItemCount(): Int = VoiceRoomMemberDialog.TITLE_RES.size

    override fun onBindTabView(holder: MemberTabViewHolder, position: Int) {
        holder.titleTv.text = ResUtil.getString(VoiceRoomMemberDialog.TITLE_RES[position])
        holderArray.put(position, holder)
    }
}

class MemberTabViewHolder(val item: View) : TabViewHolder(item) {
    val titleTv = item.findViewById<TextView>(R.id.memeber_title_tv)
    val numTv = item.findViewById<TextView>(R.id.memeber_num_tv)
    fun setTab(selected: Boolean) {
        if (selected) {
            titleTv.setTextColor(ResUtil.getColor(R.color.voice_room_tab_selected_color))
            numTv.setTextColor(ResUtil.getColor(R.color.voice_room_tab_selected_color))
            FontUtil.setTextStyle(titleTv, Typeface.BOLD)
            FontUtil.setTextStyle(numTv, Typeface.BOLD)
        } else {
            titleTv.setTextColor(ResUtil.getColor(R.color.voice_room_tab_unselected_color))
            numTv.setTextColor(ResUtil.getColor(R.color.voice_room_tab_unselected_color))
            FontUtil.setTextStyle(titleTv, Typeface.NORMAL)
            FontUtil.setTextStyle(numTv, Typeface.NORMAL)
        }
        val title = titleTv.text
        titleTv.minWidth = titleTv.paint.measureText(title, 0, title.length).toInt()
        titleTv.requestLayout()
        val num = numTv.text
        numTv.minWidth = numTv.paint.measureText(num, 0, num.length).toInt()
        numTv.requestLayout()
    }

    fun setNum(num: Int) {
        numTv?.text = "$num"
    }
}