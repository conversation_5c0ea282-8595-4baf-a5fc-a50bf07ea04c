package com.wepie.wespy.module.voiceroom.member

import android.annotation.SuppressLint
import android.app.Activity
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.str.ResUtil
import com.huiwan.base.ui.dialog.HWUIDialogBuilder
import com.huiwan.base.ui.empty.HWUIEmptyView
import com.huiwan.base.util.TextUtil
import com.huiwan.component.activity.BaseFragment
import com.huiwan.configservice.ConfigHelper
import com.huiwan.user.UserInterface
import com.huiwan.user.UserListSimpleInfoCallback
import com.huiwan.user.UserService
import com.huiwan.user.entity.UserSimpleInfo
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import com.wepie.wespy.R
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.model.event.voice.AdminChangeEvent
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.Locale

class VoiceRoomMemberSystemView : BaseFragment(), IRoomMember {

    private var presenter: VoiceRoomMemberPresenter? = null
    private var rvView: RecyclerView? = null
    private var mSearchCellEdit: EditText? = null
    private var mSearchCellBt: ImageView? = null
    private lateinit var adapter: VoiceRoomMemberSystemViewAdapter
    private var emptyView: HWUIEmptyView? = null
    private var rid = 0
    private var actionType = 0
    private var adminMaxCount = 0
    private var mTitleLeftTv: TextView? = null
    private var mTitleCenterTv: TextView? = null
    private var mTitleRightTv: TextView? = null
    private var titleLayout: ConstraintLayout? = null
    private var searchStr: String? = ""
    private var memberListener: MemberListener? = null
    private var mRoomMemberListRefresher: SmartRefreshLayout? = null
    private var model: VoiceMemberViewModel? = null
    var list: List<UserInterface> = ArrayList()
    private var countLimit = 0

    fun setMemberListener(memberListener: MemberListener?) {
        this.memberListener = memberListener
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        model = ViewModelProvider(requireParentFragment())[VoiceMemberViewModel::class.java]
        rid = model!!.getRid()
        actionType = model!!.actionType
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.voice_room_member_system_view, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initViews(view)
        initData()
    }

    @SuppressLint("ClickableViewAccessibility")
    fun initViews(view: View) {
        mSearchCellEdit = view.findViewById(R.id.search_cell_edit)
        mSearchCellEdit?.setHint(R.string.friend_list_search_user_hint)
        mSearchCellBt = view.findViewById(R.id.search_cell_bt)


        rvView = view.findViewById(R.id.room_member_list_view)
        emptyView = view.findViewById(R.id.voice_room_member_empty_view)

        titleLayout = view.findViewById(R.id.member_center_title_layout)
        mTitleLeftTv = view.findViewById(R.id.title_left_tv)
        mTitleCenterTv = view.findViewById(R.id.title_center_tv)
        mTitleRightTv = view.findViewById(R.id.title_right_tv)
        mRoomMemberListRefresher = view.findViewById(R.id.room_member_list_refresher)
        rvView?.layoutManager = LinearLayoutManager(context)

        mSearchCellEdit?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) =
                Unit

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                searchStr = s.toString()
                mSearchCellBt?.visibility =
                    if (TextUtils.isEmpty(searchStr)) View.INVISIBLE else View.VISIBLE
            }

            override fun afterTextChanged(s: Editable) {
                doSearch(s.toString())
            }
        })
        mSearchCellBt?.setOnClickListener {
            mSearchCellEdit?.setText("")
        }
        mTitleLeftTv?.setOnClickListener {
            removeFocus()
            presenter?.clearChooseUids()
            onChooseChange(-1)
            cancelManage()
            model?.setManageState(false) //取消管理回调
        }
        mRoomMemberListRefresher?.setOnLoadMoreListener {
            presenter?.loadMemberDataMore(20)
        }

        rvView?.setOnTouchListener { v: View?, _: MotionEvent? ->
            if (memberListener != null) {
                memberListener?.onListTouch()
            }
            false
        }
    }

    fun initData() {
        EventBus.getDefault().register(this)
        model?.getOnlineViewManageLiveData()?.observe(
            viewLifecycleOwner
        ) { flag: Boolean ->
            if (flag) {
                clickManageBt()
            } else {
                cancelManage()
            }
        }
        val inviteSeatNum = model?.seatNum ?: 0
        adminMaxCount = VoiceRoomMemberActivity.DEFAULT_MAX_ADMIN_NUM
        val info = VoiceRoomService.getInstance().getRoomInfo(rid)
        if (info.isAdvancedOrAnchorOrFamilyRoom) {
            val config =
                ConfigHelper.getInstance().voiceRoomConfig.advancedRoomConfig.getConfigByLevel(info.advanceRoomLevel)
            if (config != null) {
                adminMaxCount = config.adminNum
            }
        }
        presenter = VoiceRoomMemberPresenter(this, rid, adminMaxCount)
        adapter = VoiceRoomMemberSystemViewAdapter(
            requireContext(),
            presenter!!,
            actionType,
            inviteSeatNum
        )
        rvView?.adapter = adapter
        presenter?.loadMemberData(20)

        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_BLACK) {
            presenter?.loadBlackList()
        }
        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_DRAWER
            || actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE
        ) {
            mTitleRightTv?.visibility = View.GONE
            titleLayout?.visibility = View.VISIBLE
        }
        val countLimitList = ConfigHelper.getInstance().voiceRoomConfig.voiceMemberGroup.countLimit
        if (info.advanceRoomLevel - 1 >= 0 && info.advanceRoomLevel - 1 < countLimitList.size) {
            countLimit = countLimitList[info.advanceRoomLevel - 1]
        }
        showTitle(info)
    }

    private fun getUserList() {
        val membersList = presenter?.memberInfoList ?: emptyList()

        val memberUidList = ArrayList<Int>()
        for (member in membersList) {
            memberUidList.add(member.uid)
        }

        UserService.get()
            .getCacheSimpleUserList(memberUidList, object : UserListSimpleInfoCallback {
                override fun onUserInfoSuccess(userSimpleInfos: MutableList<UserSimpleInfo>?) {

                    val userList: MutableList<UserInterface> = ArrayList()
                    if (userSimpleInfos != null) {
                        for (userInterface in userSimpleInfos) {
                            userList.add(userInterface)
                        }
                    }
                    list = userList
                }

                override fun onUserInfoFailed(description: String) = Unit
            })
    }

    fun doSearch(keyWords: String) {
        val filter: List<UserInterface> = filterMemberInfo(keyWords, list)
        refreshList(filter, TextUtils.isEmpty(keyWords))
    }

    private fun filterMemberInfo(
        keyWords: String,
        memberInfos: List<UserInterface>
    ): List<UserInterface> {
        if (TextUtils.isEmpty(keyWords)) return memberInfos
        val filter: MutableList<UserInterface> = ArrayList()

        val localKeyWords = keyWords.lowercase(Locale.getDefault())
        for (info in memberInfos) {
            if (info.remarkName.lowercase(Locale.getDefault()).contains(localKeyWords)) {
                filter.add(info)
            } else if (info.nameFirstLetter.lowercase(Locale.getDefault())
                    .contains(localKeyWords)
            ) {
                filter.add(info)
            } else if (info.wdid.lowercase(Locale.getDefault()).contains(localKeyWords)) {
                filter.add(info)
            }
        }

        return filter
    }

    fun refreshList(list: List<UserInterface>, empty: Boolean) {
        if (empty) {
            checkShowEmpty(adapter.updateMember(presenter?.memberInfoList))
            return
        }

        val resultList: MutableList<VoiceRoomInfo.RoomMemberInfo> = ArrayList(list.size)
        for (i in list.indices) {
            val user = list[i]
            val member = getMember(user.uid)
            if (member != null) {
                resultList.add(member)
            }
        }

        checkShowEmpty(adapter.updateMemberSearch(resultList, true))
    }

    private fun getMember(uid: Int): VoiceRoomInfo.RoomMemberInfo? {
        val membersList = presenter?.memberInfoList ?: emptyList()
        for (member in membersList) {
            if (member.uid == uid) {
                return member
            }
        }
        return null
    }

    /**
     * 更新title
     *
     */
    private fun showTitle(roomInfo: VoiceRoomInfo) {
        val isOwner = roomInfo.isSelfOwner
        val isAdmin = roomInfo.isSelfAdmin
        mTitleRightTv?.isVisible =
            actionType == VoiceRoomMemberActivity.ACTION_TYPE_MEMBER_LIST && (isOwner || isAdmin)
        when (actionType) {
            VoiceRoomMemberActivity.ACTION_TYPE_MEMBER_LIST -> {
                mTitleCenterTv?.text =
                    ResUtil.getStr(R.string.mananage_text, 0, countLimit)
            }
            VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE -> {
                mTitleCenterTv?.setText(R.string.voice_room_members_title_sel_to_mic)
            }
            VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN -> {
                mTitleCenterTv?.text = ResUtil.getStr(
                    R.string.voice_room_members_title_sel_admin_d_d,
                    roomInfo.adminList.size,
                    adminMaxCount
                )
                mTitleRightTv?.setText(R.string.add)
                mTitleRightTv?.visibility = View.VISIBLE
                presenter?.setCur_bt_state(VoiceRoomMemberPresenter.BT_STATE_ADD)
            }
            VoiceRoomMemberActivity.ACTION_TYPE_SELECT_DRAWER -> {
                mTitleCenterTv?.setText(R.string.voice_room_members_title_sel_to_draw)
            }
            VoiceRoomMemberActivity.ACTION_TYPE_SELECT_BLACK -> {
                mTitleCenterTv?.setText(R.string.voice_room_members_title_block)
            }
            VoiceRoomMemberActivity.ACTION_TYPE_SELECT_FAMILY_ADMIN -> {
                mTitleCenterTv?.setText(R.string.voice_room_members_title_sel_family_admin)
            }
        }
    }

    override fun getContextAsActivity(): Activity {
        return requireActivity()
    }

    /**
     * 外部点击管理按钮
     */
    fun clickManageBt() {
        presenter?.clickManagerBt()
        titleLayout?.visibility = View.VISIBLE
    }

    /**
     * 移出搜索框的焦点
     */
    fun removeFocus() {
        mSearchCellEdit?.clearFocus()
    }

    /**
     * 取消管理
     */
    @SuppressLint("NotifyDataSetChanged")
    private fun cancelManage() {
        presenter?.cancelManagerWithDoNothing()
        adapter.notifyDataSetChanged()
        titleLayout?.visibility = View.GONE
    }

    override fun onChooseChange(num: Int) {
        mTitleRightTv?.isEnabled = num > 0
        mTitleRightTv?.setTextColor(Color.parseColor(if (num > 0) "#FF4D5C" else "#FFABAA"))
        mTitleRightTv?.setOnClickListener { presenter?.clickManagerBt() }
        if (num > 0) {
            mTitleCenterTv?.text =
                ResUtil.getStr(R.string.mananage_text, num, countLimit)
        } else {
            mTitleCenterTv?.text =
                ResUtil.getStr(R.string.mananage_text, 0, countLimit)
        }
    }

    override fun showSearchResult(total: Int, uids: List<Int>, clear: Boolean) = Unit

    override fun enableRefresh(enable: Boolean) {
        mRoomMemberListRefresher?.setEnableRefresh(enable)
    }

    override fun enableLoadMore(enable: Boolean) {
        mRoomMemberListRefresher?.setEnableLoadMore(enable)
    }

    override fun onItemClick(actionType: Int, uid: Int) {
        model?.onItemClick(uid, actionType)
    }

    override fun afterKickUser(kickUids: List<Int>) {
        //踢人成功后,置灰左上角操作键
        onChooseChange(-1)
    }

    override fun showManageState(roomInfo: VoiceRoomInfo) {
        checkShowEmpty(adapter.updateMember(presenter?.memberInfoList))
        getUserList()
        mRoomMemberListRefresher?.finishLoadMore()
        enableLoadMore(true)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun showDeleteState(roomInfo: VoiceRoomInfo) {
        adapter.notifyDataSetChanged()
    }

    override fun showKickDialog(txt: String, size: Int) {
        if (VoiceRoomService.getInstance().getRoomInfo(rid).isAdvancedOrAnchorOrFamilyRoom) {
            HWUIDialogBuilder.newBuilder(requireContext())
                .setSingleBtn(false)
                .setTitle(ResUtil.getStr(R.string.voice_room_members_remove_title, size))
                .setContent(txt)
                .setCancelTx(R.string.cancel)
                .setSureTx(R.string.sure).setCanCancel(true)
                .setSelectLabel(R.string.voice_room_members_add_room_blacklist)
                .setDialogCallback(object : HWUIDialogBuilder.DialogCallback {
                    override fun onClickSure() {}

                    override fun onSelectSure(checked: Boolean) {
                        super.onSelectSure(checked)
                        presenter?.batchDeleteRoomMember(checked)
                    }

                }).show()
        } else {
            HWUIDialogBuilder.newBuilder(requireContext())
                .setSingleBtn(false)
                .setContent(txt)
                .setSureTx(R.string.sure)
                .setCancelTx(R.string.cancel)
                .setDialogCallback { presenter?.doKickUsers(false) }
                .show()
        }
    }

    private fun checkShowEmpty(num: Int) {
        var tips = ""
        var type = HWUIEmptyView.Companion.base_empty_no_data
        if (searchStr != null && !TextUtil.isEmpty(searchStr)) {
            tips = ResUtil.getStr(R.string.search_empty_result)
            type = HWUIEmptyView.Companion.base_empty_find_empty
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN) {
            tips = ResUtil.getStr(R.string.voice_room_members_admin_none)
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_BLACK) {
            tips = ResUtil.getStr(R.string.voice_room_members_block_none)
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE) {
            tips = ResUtil.getStr(R.string.none)
        }
        if (!TextUtil.isEmpty(tips) && num == 0) {
            emptyView?.setText(tips)
            emptyView?.setType(type)
            emptyView?.visibility = View.VISIBLE
            rvView?.visibility = View.GONE
        } else {
            emptyView?.visibility = View.GONE
            rvView?.visibility = View.VISIBLE
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onListChange(event: AdminChangeEvent?) {
        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN) {
            presenter?.loadMemberData(20)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (presenter != null) {
            presenter?.clear()
        }
        EventBus.getDefault().unregister(this)
    }

    /**
     * 成员列表回调
     */
    interface MemberListener {
        /**
         * 当列表被触摸的时候 主要用于隐藏键盘
         */
        fun onListTouch() {}
    }
}