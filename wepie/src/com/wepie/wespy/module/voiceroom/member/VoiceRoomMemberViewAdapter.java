package com.wepie.wespy.module.voiceroom.member;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.wepie.liblog.main.HLog;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.setting.VoiceRoomMemberActivity;

import java.util.ArrayList;
import java.util.List;

/**
 * 新版在语音房线列表adapter 整合搜索到一个adapter中,降低view控制复杂度
 */

public class VoiceRoomMemberViewAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private static final String TAG = "VoiceRoomMemberViewAdapter";
    private final int actionType;
    private final int inviteSeatNum;
    private Context mContext;
    private VoiceRoomMemberPresenter presenter;
    private ArrayList<Integer> uidList = new ArrayList<>();
    private VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
    private boolean needSearch;

    public VoiceRoomMemberViewAdapter(Context mContext, VoiceRoomMemberPresenter presenter, int actionType, int inviteSeatNum) {
        this.mContext = mContext;
        this.presenter = presenter;
        this.actionType = actionType;
        this.inviteSeatNum = inviteSeatNum;
        needSearch = !(actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN || actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_BLACK);
    }

    public int update(VoiceRoomInfo roomInfo) {
        this.roomInfo = roomInfo;
        this.uidList.clear();
        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN) {
            this.uidList.addAll(roomInfo.adminList);
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE) {
            this.uidList.addAll(roomInfo.uidList);
            this.uidList.remove(Integer.valueOf(roomInfo.owner));
            this.uidList.remove(Integer.valueOf(roomInfo.getSeatInfoByNum(inviteSeatNum).uid));
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_DRAWER) {
            this.uidList.addAll(roomInfo.uidList);
            if (roomInfo.isDrawing && roomInfo.drawGameInfo != null) {
                int uid = roomInfo.drawGameInfo.getDrawUid();
                if (uid > 0) {
                    this.uidList.remove(Integer.valueOf(uid));
                }
            }
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_BLACK) {
            this.uidList.addAll(roomInfo.blackList);
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_FAMILY_ADMIN) {
            this.uidList.addAll(roomInfo.uidList);
            this.uidList.remove(Integer.valueOf(roomInfo.owner));
            if (roomInfo.adminList.size() > 0) {
                this.uidList.removeAll(roomInfo.adminList);
                this.uidList.addAll(0, roomInfo.adminList);
            }
        } else {
            this.uidList.addAll(roomInfo.uidList);
        }
        notifyDataSetChanged();
        int roomInfoUidListSize = 0;
        if (roomInfo.uidList != null) {
            roomInfoUidListSize = roomInfo.uidList.size();
        }
        HLog.d(TAG, HLog.USR, "uidList.size={}, actionType={}, roomInfoUidListSize={}", uidList.size(), actionType, roomInfoUidListSize);
        return uidList.size();
    }

    /**
     * 显示搜索结果
     *
     * @param searchResult
     * @param clear        是否清楚上一次结果
     * @return
     */
    public int updateSearch(List<Integer> searchResult, boolean clear) {
        if (clear) {
            this.uidList.clear();
        }
        this.uidList.addAll(searchResult);
        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_INVITE) {
            this.uidList.remove(Integer.valueOf(roomInfo.owner));
            this.uidList.remove(Integer.valueOf(roomInfo.getSeatInfoByNum(inviteSeatNum).uid));
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_DRAWER) {
            if (roomInfo.isDrawing && roomInfo.drawGameInfo != null) {
                int uid = roomInfo.drawGameInfo.getDrawUid();
                if (uid > 0) {
                    this.uidList.remove(Integer.valueOf(uid));
                }
            }
        }
        notifyDataSetChanged();
        return uidList.size();
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new RecyclerView.ViewHolder(new VoiceRoomMemberItem(mContext, presenter)) {
        };
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder.itemView != null && holder.itemView instanceof VoiceRoomMemberItem) {
            int uid = uidList.get(position);
            ((VoiceRoomMemberItem) holder.itemView).update(roomInfo, uid, actionType, inviteSeatNum);
        }
    }

    @Override
    public int getItemCount() {
        return uidList.size();
    }
}
