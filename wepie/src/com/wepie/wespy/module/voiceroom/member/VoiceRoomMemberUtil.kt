package com.wepie.wespy.module.voiceroom.member

import com.huiwan.anim.SVGAUtil
import com.huiwan.configservice.ConfigHelper
import com.huiwan.configservice.model.voiceroom.VoiceMemberGroup
import com.huiwan.widget.SpeakerAnimView
import com.huiwan.widget.drawables.SpeakerAnimDrawable
import com.huiwan.widget.drawables.SpeakerSVGADrawable
import com.opensource.svgaplayer.SVGAParser
import com.opensource.svgaplayer.SVGAVideoEntity
import com.wepie.wespy.R

object VoiceRoomMemberUtil {

    @JvmStatic
    fun initSpeakerAnim() {
        SpeakerAnimView.setFactory { view: SpeakerAnimView, level: Int, isFreeze: Boolean ->
            if (level <= 0 || isFreeze) {
                resetSpeakerAnimDrawable(view)
                return@setFactory
            }
            val memberGroup =
                ConfigHelper.getInstance().voiceRoomConfig.voiceMemberGroup
            val privilegeLevel = memberGroup.getPrivilegeLevel(level)
            var assetStr = ""
            if (privilegeLevel != null) {
                for (i in privilegeLevel.privileges) {
                    if (i == VoiceMemberGroup.BLUE_SPEAKER_ANIM_PRIVILEGE_ID) {
                        assetStr = "svga/voiceroom/voice_room_speaker_blue_anim.svga"
                    } else if (i == VoiceMemberGroup.GOLD_SPEAKER_ANIM_PRIVILEGE_ID) {
                        assetStr = "svga/voiceroom/voice_room_speaker_gold_anim.svga"
                    }
                }
            }
            if (assetStr.isBlank()) {
                resetSpeakerAnimDrawable(view)
                return@setFactory
            }
            if (assetStr == view.getTag(R.id.speaker_anim_view) && view.drawable is SpeakerSVGADrawable) {
                return@setFactory
            }
            SVGAUtil.getAssetSvgaAnim(assetStr, object : SVGAParser.ParseCompletion {
                override fun onComplete(videoItem: SVGAVideoEntity) {
                    view.setTag(R.id.speaker_anim_view, assetStr)
                    val draw = SpeakerSVGADrawable(videoItem)
                    draw.loop(30, 90)
                    view.setImageDrawable(draw)
                }

                override fun onError() {
                    resetSpeakerAnimDrawable(view)
                }
            })
        }

    }

    private fun resetSpeakerAnimDrawable(view: SpeakerAnimView) {
        view.setTag(R.id.speaker_anim_view, "")
        if (view.drawable !is SpeakerAnimDrawable) {
            view.setImageDrawable(SpeakerAnimDrawable())
        }
    }


}