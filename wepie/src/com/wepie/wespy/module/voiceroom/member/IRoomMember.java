package com.wepie.wespy.module.voiceroom.member;

import android.app.Activity;

import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;

import java.util.List;

/**
 * Created by geeksammao on 26/10/2017.
 */

public interface IRoomMember {
    void showManageState(VoiceRoomInfo roomInfo);
    void showDeleteState(VoiceRoomInfo roomInfo);
    void showKickDialog(String txt, int size);

    /**
     * 选择操作的用户数量改变时
     * @param num
     */
    void onChooseChange(int num);

    /**
     * 显示搜索结果
     * @param total 搜索总结果
     * @param uids
     * @param clear 是否清除上一次搜索结果 根据page <= 0判断
     */
    void showSearchResult(int total,List<Integer> uids,boolean clear);

//    void showMemberList(int total, List<TmpRoomPackets.RoomMemberInfo> members, boolean clear);

    /**
     * 是否允许下拉刷新
     * @param enable
     */
    void enableRefresh(boolean enable);

    /**
     * 是否允许上拉加载
     * @param enable
     */
    void enableLoadMore(boolean enable);

    /**
     * 当条目被点击
     * @param actionType 事件类型
     * @param uid
     */
    void onItemClick(int actionType,int uid);

    /**
     * 踢人之后
     * @param kickUids 踢出去的用户id
     */
    void afterKickUser(List<Integer> kickUids);

    Activity getContextAsActivity();
}