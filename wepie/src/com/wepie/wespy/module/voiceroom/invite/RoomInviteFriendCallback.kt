package com.wepie.wespy.module.voiceroom.invite

import com.huiwan.base.util.ToastUtil
import com.huiwan.constants.GameType
import com.huiwan.lib.api.ApiService
import com.huiwan.libtcp.callback.LifeSeqCallback
import com.huiwan.libtcp.callback.SeqCallback
import com.huiwan.libtcp.huiwan.RspHeadInfo
import com.huiwan.user.FriendInfoCacheManager
import com.wejoy.weplay.ex.GlobalLife
import com.wepie.lib.api.plugins.share.IShareApi
import com.wepie.lib.api.plugins.share.ShareInfo
import com.wepie.lib.api.plugins.share.ShareResult
import com.wepie.lib.api.plugins.share.ShareTrack
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName.INVITE
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName
import com.wepie.lib.api.plugins.track.config.os.TrackString
import com.wepie.wespy.R
import com.wepie.wespy.module.chat.dataservice.chat.MsgSendUtil
import com.wepie.wespy.module.chat.dataservice.chat.MsgStructUtil
import com.wepie.wespy.module.chat.dataservice.group.GroupService
import com.wepie.wespy.module.game.room.roominfo.convene.RoomConveneItemCallback
import com.wepie.wespy.net.tcp.sender.PartyRoomPacketSender
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender

class RoomInviteFriendCallback : RoomConveneItemCallback {

    private val rid: Int
    private val gameType: Int
    private val subGameType: Int

    constructor(rid: Int, gameType: Int) : this(rid, gameType, 0)

    constructor(rid: Int, gameType: Int, subGameType: Int) {
        this.rid = rid
        this.gameType = gameType
        this.subGameType = subGameType
    }

    override fun onInvite(
        target: ShareResult.Target
    ) {
        if (target is ShareResult.Target.Friend) {
            inviteFriend(target.uid)
            ApiService.of(IShareApi::class.java).shareClickTrack(
                ShareInfo.ShareReportData.build(
                    TrackScreenName.SHARE_PAGE,
                    TrackString.SCENE_VOICE_ROOM, ShareTrack.TYPE_LINK, gameType, "", INVITE, target.uid
                ).apply {
                    if (subGameType > 0) {
                        ext = mapOf("sub_game_type" to subGameType)
                    }
                }
            )
        } else if (target is ShareResult.Target.Group) {
            inviteGroup(target.gid)
            ApiService.of(IShareApi::class.java).shareClickTrack(
                ShareInfo.ShareReportData.build(
                    TrackScreenName.SHARE_PAGE,
                    TrackString.SCENE_VOICE_ROOM, ShareTrack.TYPE_LINK, gameType, "", INVITE, target.gid
                ).apply {
                    if (subGameType > 0) {
                        ext = mapOf("sub_game_type" to subGameType)
                    }
                }
            )
        }
    }

    private fun inviteFriend(
        uid: Int
    ) {
        val friendInfo =
            FriendInfoCacheManager.getInstance().getFriendInfoByUid(uid)
                ?: return
        val uidList: MutableList<Int> = ArrayList()
        uidList.add(friendInfo.uid)
        if (gameType == GameType.GAME_TYPE_PARTY_SPY) {
            invitePartyFriends(rid, uidList, gameType)
        } else {
            VoiceRoomPacketSender.inviteFriend(rid, uidList, object : LifeSeqCallback(GlobalLife) {
                override fun onSuccess(head: RspHeadInfo) {
                    ToastUtil.show(R.string.common_invite_success)
                }

                override fun onFail(head: RspHeadInfo) {
                    ToastUtil.show(head.desc)
                }
            })
        }
    }

    private fun invitePartyFriends(rid: Int, uidList: List<Int>, gameType: Int) {
        PartyRoomPacketSender.partyInviteReq(rid, uidList, gameType, object : SeqCallback {
            override fun onSuccess(head: RspHeadInfo?) {

            }

            override fun onFail(head: RspHeadInfo?) {
                ToastUtil.show(head?.desc)
            }
        })
    }

    private fun inviteGroup(
        gid: Int
    ) {
        val chatMsg = MsgStructUtil.structVoiceInviteGroupMsg(
            gid,
            rid,
            gameType,
            ""
        )
        if (gameType == GameType.GAME_TYPE_PARTY_SPY) {
            invitePartyGroup(gid, rid, gameType)
        } else {
            MsgSendUtil.sendCocosGameInviteMsg(chatMsg, object : SeqCallback {
                override fun onSuccess(head: RspHeadInfo) {
                    ToastUtil.show(R.string.common_invite_success)
                }

                override fun onFail(head: RspHeadInfo) {
                    ToastUtil.show(head.desc)
                }
            })
        }
    }

    private fun invitePartyGroup(gid: Int, rid: Int, gameType: Int) {
        val groupInfo = GroupService.getInstance().getGroupInfoCanNull(gid)
        if (groupInfo != null) {
            val chatMsg = MsgStructUtil.structVoiceInviteGroupMsg(gid, rid, gameType, "")
            MsgSendUtil.sendCocosGameInviteMsg(chatMsg, object : SeqCallback {
                override fun onSuccess(head: RspHeadInfo) {}
                override fun onFail(head: RspHeadInfo) {
                    ToastUtil.show(head.desc)
                }
            })
        }
    }

}