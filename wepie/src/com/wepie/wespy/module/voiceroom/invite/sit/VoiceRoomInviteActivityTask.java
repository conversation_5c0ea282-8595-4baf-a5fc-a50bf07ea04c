package com.wepie.wespy.module.voiceroom.invite.sit;

import android.app.Activity;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.activity.ActivityTask;
import com.huiwan.base.activity.ActivityTaskBuilderHolder;
import com.huiwan.base.activity.IActivityObserver;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.user.InviteDrawHelper;
import com.wepie.wespy.module.voiceroom.user.InviteSpeakFloatingView;

public class VoiceRoomInviteActivityTask implements ActivityTask {
    private final Activity activity;
    private InviteSpeakFloatingView inviteSpeakFloatingView;
    private InviteDrawHelper inviteDrawHelper;

    public VoiceRoomInviteActivityTask(Activity activity) {
        this.activity = activity;
    }

    @Override
    public void onResume(Activity activity) {
        if (VoiceRoomService.getInstance().getInviteSpeakReplyTimeLeft() > 0) {
            inviteSpeakFloatingView = new InviteSpeakFloatingView(activity);
            inviteSpeakFloatingView.show(VoiceRoomService.getInstance().getInviteSpeakReplyTimeLeft(), () -> {
                inviteSpeakFloatingView = null;
            });
        } else {
            if (inviteSpeakFloatingView != null) {
                inviteSpeakFloatingView.hideFloatView(true);
            }
        }
    }

    @Override
    public void onPause(Activity activity) {
        hideVoiceRoomInviteSpeakFloatingView(false);
        hideRoomInviteDraw();
    }

    @Override
    public void onDestroy(Activity activity) {
        if (this.activity == activity) {
            hideVoiceRoomInviteSpeakFloatingView(false);
        }
    }

    public void hideVoiceRoomInviteSpeakFloatingView(boolean resetCountdownTimer) {
        if (inviteSpeakFloatingView != null) {
            inviteSpeakFloatingView.hideFloatView(resetCountdownTimer);
        }
    }

    public void showVoiceRoomInviteSpeakFloatingView() {
        int timeLeft = 10;
        if (VoiceRoomService.getInstance().getInviteSpeakReplyTimeLeft() == 0) {
            inviteSpeakFloatingView = new InviteSpeakFloatingView(ActivityTaskManager.getInstance().getTopActivity());
            inviteSpeakFloatingView.show(timeLeft, () -> {
                inviteSpeakFloatingView = null;
            });
        }
    }


    public void showRoomInviteDraw(int rid, int whoInvite) {
        if (inviteDrawHelper == null) {
            inviteDrawHelper = new InviteDrawHelper(activity);
        }
        inviteDrawHelper.show(rid, whoInvite);
    }

    public void hideRoomInviteDraw() {
        if (inviteDrawHelper != null) {
            inviteDrawHelper.hide();
        }
    }

    public boolean isInviteSpeakFloatViewVisible() {
        if (inviteSpeakFloatingView == null) return false;
        return inviteSpeakFloatingView.isShowing();
    }

    public static void register() {
        ActivityTaskBuilderHolder.get().register(VoiceRoomInviteActivityTask::new);
    }

    public static void hideVoiceRoomInviteSpeakFloatingView(Activity activity, boolean resetTimer) {
        VoiceRoomInviteActivityTask task = null;
        if (activity instanceof IActivityObserver) {
            task = ((IActivityObserver) activity).getActivityObserver().getTask(VoiceRoomInviteActivityTask.class);
        }
        if (task != null) {
            task.hideVoiceRoomInviteSpeakFloatingView(resetTimer);
        }
    }

    public static void showVoiceRoomInviteSpeakFloatingView(Activity activity) {
        VoiceRoomInviteActivityTask task = null;
        if (activity instanceof IActivityObserver) {
            task = ((IActivityObserver) activity).getActivityObserver().getTask(VoiceRoomInviteActivityTask.class);
        }
        if (task != null) {
            task.showVoiceRoomInviteSpeakFloatingView();
        }
    }

    public static void showVoiceRoomInviteDraw(Activity activity, int rid, int whoInvite) {
        VoiceRoomInviteActivityTask task = null;
        if (activity instanceof IActivityObserver) {
            task = ((IActivityObserver) activity).getActivityObserver().getTask(VoiceRoomInviteActivityTask.class);
        }
        if (task != null) {
            task.showRoomInviteDraw(rid, whoInvite);
        }
    }

    public static boolean isInviteSpeakFloatViewVisible(Activity activity) {
        VoiceRoomInviteActivityTask task = null;
        if (activity instanceof IActivityObserver) {
            task = ((IActivityObserver) activity).getActivityObserver().getTask(VoiceRoomInviteActivityTask.class);
        }
        if (task != null) {
            return task.isInviteSpeakFloatViewVisible();
        }
        return false;
    }
}
