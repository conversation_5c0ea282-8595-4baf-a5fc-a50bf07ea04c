package com.wepie.wespy.module.voiceroom.invite;

import android.widget.TextView;

import com.huiwan.user.FriendInfoCacheManager;
import com.huiwan.user.entity.FriendInfo;
import com.wepie.wespy.model.entity.family.FamilyMainInfo;
import com.wepie.wespy.model.entity.group.GroupInfo;
import com.wepie.wespy.module.InviteFriendApi;
import com.wepie.wespy.module.chat.dataservice.group.GroupService;
import com.wepie.wespy.module.contact.detail.view.tags.TagUtil;
import com.wepie.wespy.module.family.FamilyManager;
import com.wepie.wespy.module.fixroom.convene.ConveneCallback;

public class InviteFriendApiImpl implements InviteFriendApi {
    @Override
    public void getRecentFriend(ConveneCallback callback) {

    }

    @Override
    public FriendInfo getFriendInfo(int uid) {
        return FriendInfoCacheManager.getInstance().getFriendInfoByUid(uid);
    }

    @Override
    public FamilyMainInfo getFamilyInfo() {
        return FamilyManager.getInstance().getSelfFamilyInfo();
    }

    @Override
    public GroupInfo getGroupInfo(int gid) {
        return GroupService.getInstance().getGroupInfo(gid);
    }

    @Override
    public void setFamilyTagUi(TextView conTag) {
        TagUtil.updateTagInfo(TagUtil.FAMILY_TAG_TYPE, conTag);
    }
}
