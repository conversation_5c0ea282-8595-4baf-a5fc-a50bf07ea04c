package com.wepie.wespy.module.voiceroom.scoreboard

import com.wepie.wespy.model.event.RoomLevelUpdateEvent
import com.wepie.wespy.module.voiceroom.IPluginTool
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IRoomSimpleView
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class RoomUpdateDialogDelegateImpl(private val pluginTool: IPluginTool<IRoomSimpleView>) :
    IRoomSimpleView by pluginTool.proxy() {

    init {
        initEventBus()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onRoomLevelUpdate(event: RoomLevelUpdateEvent?) {
        if ((event?.level ?: 0) > 0) {
            pluginTool.loadView(true)
            show(mainPlugin.roomInfo) {
                pluginTool.loadView(false)
            }
        }
    }

    private fun initEventBus() {
        EventBus.getDefault().register(this)
        pluginTool.onDestroy {
            EventBus.getDefault().unregister(this)
        }
    }
}