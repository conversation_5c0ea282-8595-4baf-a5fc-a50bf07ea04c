package com.wepie.wespy.module.voiceroom.scoreboard;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.StringUtil;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.CustomCircleImageView;
import com.huiwan.widget.HeadImageLoader;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;

public class ScoreSeatView extends ConstraintLayout {
    protected int seatNum;
    public int seatSeq;
    public int seatType;
    private final CustomCircleImageView headIv;
    private final ImageView checkedIv;
    private final TextView scoreTv;
    protected final TextView seatNumTv;

    private boolean seatOpen = false;
    private boolean funcOpen = false;
    private boolean auto = false;
    private boolean selected = false;

    public ScoreSeatView(@NonNull Context context) {
        this(context, null);
    }

    public ScoreSeatView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        LayoutInflater.from(context).inflate(getResLayout(), this, true);

        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.SeatView, 0, 0);
        seatNum = typedArray.getInt(R.styleable.SeatView_seat_num, 0);
        seatSeq = typedArray.getInt(R.styleable.SeatView_seat_num, 0);
        seatType = typedArray.getInt(R.styleable.SeatView_seat_type, VoiceRoomInfo.SeatInfo.NORMAL_SEAT);
        typedArray.recycle();
        headIv = findViewById(R.id.head_iv);
        checkedIv = findViewById(R.id.checked_iv);
        scoreTv = findViewById(R.id.score_tv);
        seatNumTv = findViewById(R.id.seat_num_tv);
        setOnClickListener(v -> {
            if (!funcOpen || (seatOpen && !auto)) {
                selected = !selected;
                updateSelected();
            }
        });
        setSeatNumber();
    }

    protected void setSeatNumber() {
        if (isIntimateSeat()) {
            seatNumTv.setTextSize(16);
            seatNumTv.setText(R.string.voice_room_mic_owner_intimacy_seat);
        } else {
            seatNumTv.setText(String.valueOf(seatNum - 1)); // 计分器座位上显示的号码与实际 seatNum 差 1.
        }
    }

  protected int getResLayout() {
    return R.layout.voice_score_board_seat;
  }


    public void setPartner() {
        seatNumTv.setText(R.string.voice_room_mic_owner_intimacy_seat);
        seatNumTv.setLineSpacing(1F, 0.5F);
        seatNumTv.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
    }

    public int getSeatNum() {
        return seatNum;
    }

  public  boolean isHonorSeat() {
    return VoiceRoomInfo.SeatInfo.isHonorSeat(seatType);
  }

  public  boolean isIntimateSeat() {
    return VoiceRoomInfo.SeatInfo.isIntimateSeat(seatType);
  }

    private void initIntimateInfo(VoiceRoomInfo roomInfo) {
        if (!isIntimateSeat()) {
            return;
        }
        VoiceRoomInfo.IntimateSeatInfo intimateSeatInfo = roomInfo.intimateSeatInfo;
        if (intimateSeatInfo != null && intimateSeatInfo.ownerIsOpen) {
            setVisibility(View.VISIBLE);
            setPartner();
        } else {
            setVisibility(View.GONE);
        }
    }

    public void showUser(VoiceRoomInfo.SeatInfo seatInfo, VoiceRoomInfo roomInfo, boolean init) {
        initIntimateInfo(roomInfo);
        seatOpen = seatInfo.scoreOpen;
        auto = ScoreBoardHelper.INSTANCE.isScoreBoardAuto();
        if (seatInfo.uid > 0) {
            displayHead(seatInfo.uid);
            seatNumTv.setVisibility(GONE);
        } else {
            headIv.setImageResource(R.drawable.voice_mic_select_sit_icon);
            seatNumTv.setVisibility(VISIBLE);
        }
        funcOpen = ScoreBoardHelper.INSTANCE.isScoreBoardOpen();
        if (ScoreBoardHelper.INSTANCE.isScoreBoardOpen()) {
            if (seatOpen) {
                if (init) {
                    selected = !auto;
                }
                headIv.setAlpha(1f);
                seatNumTv.setAlpha(1f);
                scoreTv.setVisibility(VISIBLE);
                scoreTv.setText(StringUtil.formatSimpleNum(seatInfo.score));
            } else {
                if (init) {
                    selected = false;
                }
                headIv.setAlpha(0.3f);
                seatNumTv.setAlpha(0.3f);
                scoreTv.setVisibility(GONE);
            }
        } else {
            if (init) {
                selected = true;
            }
            headIv.setAlpha(1f);
            seatNumTv.setAlpha(1f);
            scoreTv.setVisibility(GONE);
        }
        updateSelected();
    }

    private void updateSelected() {
        if (selected) {
            headIv.setBorderColor(0xff00CBF9);
            headIv.setBorderWidth(ScreenUtil.dip2px(1));
            checkedIv.setVisibility(VISIBLE);
        } else {
            headIv.setBorderColor(0);
            headIv.setBorderWidth(0);
            checkedIv.setVisibility(GONE);
        }
    }

    private void displayHead(int uid) {
        UserService.get().getCacheSimpleUser(uid, new LifeUserSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                WpImageLoader.load(simpleInfo.headimgurl, headIv, HeadImageLoader.genHeadLoadInfo());
            }

            @Override
            public void onUserInfoFailed(String description) { }
        });
    }

    public boolean seatSelected() {
        return selected;
    }

    public void seatSelected(boolean select) {
        selected = select;
        updateSelected();
    }
}
