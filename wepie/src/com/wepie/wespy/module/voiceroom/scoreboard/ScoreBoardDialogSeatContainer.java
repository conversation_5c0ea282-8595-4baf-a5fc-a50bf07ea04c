package com.wepie.wespy.module.voiceroom.scoreboard;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;

import com.huiwan.constants.GameType;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import java.util.ArrayList;
import java.util.List;

public class ScoreBoardDialogSeatContainer {
    private final int roomGameType;
    private final List<ScoreSeatView> seatViewList = new ArrayList<>();

    public ScoreBoardDialogSeatContainer(@NonNull ViewGroup parent, int roomGameType) {
        this.roomGameType = roomGameType;
        int res = R.layout.voice_score_board_seat_simple;
        if (roomGameType == GameType.GAME_TYPE_FAMILY_ROOM) {
            res = R.layout.voice_score_board_seat_family;
        } else if (roomGameType == GameType.GAME_TYPE_VIDEO_ROOM) {
            res = R.layout.voice_score_board_seat_video;
        }
        initViews(LayoutInflater.from(parent.getContext()).inflate(res, parent, true));
    }

    private void initViews(View view) {
        int[] seatIdArr = new int[]{
                R.id.seat_1, R.id.seat_2, R.id.seat_3, R.id.seat_4, R.id.seat_5, R.id.seat_6, R.id.seat_7,
                R.id.seat_8, R.id.seat_9, R.id.seat_10, R.id.seat_11, R.id.seat_12, R.id.seat_13, R.id.seat_14,
                R.id.honor_seat_1, R.id.honor_seat_2, R.id.honor_seat_3, R.id.honor_seat_4, R.id.honor_seat_5, R.id.honor_seat_6,
        };
        for (int id: seatIdArr) {
            ScoreSeatView seatView = view.findViewById(id);
            if (seatView != null) {
                seatViewList.add(seatView);
            }
        }
    }

    public int getCurrentRoomGameType() {
        return roomGameType;
    }

    public void update(VoiceRoomInfo roomInfo, boolean init) {
        for (ScoreSeatView seatView : seatViewList) {
            VoiceRoomInfo.SeatInfo seatInfo;
            if (seatView.isIntimateSeat()) {
                seatInfo = roomInfo.getIntimateSeat();
            } else if (seatView.isHonorSeat()) {
                seatInfo = roomInfo.getHonorSeatInfo(seatView.seatSeq);
            } else {
                seatInfo = roomInfo.getSeatInfoByNum(seatView.getSeatNum());
            }
            if (null != seatInfo) {
                seatView.setVisibility(View.VISIBLE);
                seatView.showUser(seatInfo, roomInfo, init);
            } else {
                seatView.setVisibility(View.GONE);
            }
        }
    }

    public List<Integer> getSelectedSeatNums() {
        List<Integer> selectedSeats = new ArrayList<>();
        for (ScoreSeatView seatView: seatViewList) {
            if (seatView.seatSelected()) {
                selectedSeats.add(seatView.getSeatNum());
            }
        }
        return selectedSeats;
    }

    public void seatSelected(boolean b) {
        for (ScoreSeatView seatView: seatViewList) {
            seatView.seatSelected(b);
        }
    }
}
