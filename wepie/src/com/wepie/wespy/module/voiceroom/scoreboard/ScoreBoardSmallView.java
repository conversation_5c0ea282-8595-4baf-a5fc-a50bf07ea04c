package com.wepie.wespy.module.voiceroom.scoreboard;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.wepie.wespy.R;

public class ScoreBoardSmallView extends ConstraintLayout {
    public ScoreBoardSmallView(@NonNull Context context) {
        this(context, null);
    }

    public ScoreBoardSmallView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        LayoutInflater.from(context).inflate(R.layout.voice_score_board_small_view, this);
        setBackgroundResource(R.drawable.gradient_5d51dd_2cc7eb_corner_6);
        setOnClickListener(v -> ScoreBoardDialog.showDialog(v.getContext()));
    }
}
