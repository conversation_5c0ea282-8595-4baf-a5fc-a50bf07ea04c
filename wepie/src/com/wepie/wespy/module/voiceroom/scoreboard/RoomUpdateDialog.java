package com.wepie.wespy.module.voiceroom.scoreboard;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.voiceroom.AdvanceRoomLevelConfig;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IRoomSimpleView;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

public class RoomUpdateDialog extends FrameLayout implements IRoomSimpleView {

    ImageView levelIcon;
    TextView tips;
    TextView ok;

    private Function0<Unit> hide;

    public RoomUpdateDialog(Context context) {
        super(context);
        initView();
    }

    public RoomUpdateDialog(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    protected void initView() {
        LayoutInflater.from(getContext()).inflate(R.layout.voice_room_update_dialog, this, true);
        levelIcon = findViewById(R.id.level_icon);
        tips = findViewById(R.id.tips);
        ok = findViewById(R.id.ok);
        setOnClickListener(v -> hide());
    }

    private void update(String tipsHint, String url) {
        WpImageLoader.load(url, levelIcon);
        tips.setText(tipsHint);
    }

    private void showLevelUpdateDialog(String tips, String iconUrl) {
        if (TextUtils.isEmpty(tips) || TextUtils.isEmpty(iconUrl)) {
            hide();
            return;
        }
        setVisibility(VISIBLE);
        update(tips, iconUrl);
    }

    @Override
    public void show(@Nullable VoiceRoomInfo roomInfo, @NonNull Function0<Unit> hide) {
        int level = VoiceRoomService.getInstance().getRoomInfo().advanceRoomLevel;
        AdvanceRoomLevelConfig config = ConfigHelper.getInstance().getVoiceRoomConfig().getAdvancedRoomConfig().getConfigByLevel(level);
        if (null != config) {
            this.hide = hide;
            showLevelUpdateDialog(config.getUpgradeTips(), config.getIconUrl());
        } else {
            hide.invoke();
            setVisibility(GONE);
        }
    }

    private void hide() {
        setVisibility(GONE);
        if (hide != null) {
            hide.invoke();
        }
    }
}
