package com.wepie.wespy.module.voiceroom.scoreboard;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.constants.HttpCode;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.dialog.androidx.BaseDialog;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.util.view.ListViewDialog;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

import java.util.Arrays;
import java.util.List;

public class ScoreBoardDialog extends BaseDialog {
    private static final String TAG = "ScoreBoardDialog";

    private ImageView closeDialogIv;
    private ImageView closeFuncIv;
    private ViewGroup containerLay;
    private TextView seatTitleTv;
    private TextView scoreAutoTipOpenedTv;
    private View scoreAutoSelectLay;
    private TextView scoreAutoSelectTitleTv;
    private ViewGroup scoreManualCtrlLay;
    private ViewGroup scoreManualSelectLay;
    private TextView scoreManualSelectTitleTv;
    private TextView scoreManualDivScoreTv;
    private TextView scoreManualDivScoreBtn;
    private TextView scoreManualAddScoreTv;
    private TextView scoreManualAddScoreBtn;
    private TextView scoreboardOpenBtn;
    private TextView modeTitleTv;

    private boolean typeAuto = true;
    private int manualScoreDiv = -1;
    private int manualScoreAdd = 1;
    private boolean scoreBoardOpen = false;
    private boolean pendingClose = false;
    private ScoreBoardDialogSeatContainer container;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.voice_score_board_dialog, container, false);
        initViews(view);
        initData();
        initEvents();
        return view;
    }

    private void initViews(View v) {
        closeDialogIv = v.findViewById(R.id.close_dialog_iv);
        closeFuncIv = v.findViewById(R.id.close_func_iv);
        containerLay = v.findViewById(R.id.seat_container_lay);
        seatTitleTv = v.findViewById(R.id.seat_title_tv);
        scoreAutoTipOpenedTv = v.findViewById(R.id.auto_tip_opened_tv);
        scoreManualCtrlLay = v.findViewById(R.id.manual_ctrl_lay);
        scoreboardOpenBtn = v.findViewById(R.id.start_tv);
        scoreAutoSelectLay = v.findViewById(R.id.auto_bg_lay);
        scoreAutoSelectTitleTv = v.findViewById(R.id.auto_title_tv);
        scoreManualSelectLay = v.findViewById(R.id.manual_bg_lay);
        scoreManualSelectTitleTv = v.findViewById(R.id.manual_title_tv);
        scoreManualDivScoreTv = v.findViewById(R.id.score_div_tv);
        scoreManualAddScoreTv = v.findViewById(R.id.score_add_tv);
        scoreManualDivScoreBtn = v.findViewById(R.id.score_div_btn);
        scoreManualAddScoreBtn = v.findViewById(R.id.score_add_btn);
        seatTitleTv = v.findViewById(R.id.seat_title_tv);
        modeTitleTv = v.findViewById(R.id.mode_title_tv);

        VoiceRoomService.getInstance().getLiveData().observe(getViewLifecycleOwner(), roomInfo -> {
            if (roomInfo == null || !roomInfo.isSelfAdminOrOwner() || !ScoreBoardHelper.INSTANCE.supportScoreBoard(roomInfo.game_type)) {
                dismissAllowingStateLoss();
            } else {
                if (!scoreBoardOpen) {
                    if (ScoreBoardHelper.INSTANCE.isScoreBoardManual()) {
                        getSeatContainer(roomInfo).seatSelected(false);
                    }
                }
                if (!pendingClose) {
                    updateViews(roomInfo);
                    scoreBoardOpen = ScoreBoardHelper.INSTANCE.isScoreBoardOpen();
                }
            }
        });
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
        updateViews(roomInfo, true);
        scoreBoardOpen = ScoreBoardHelper.INSTANCE.isScoreBoardOpen();
    }

    private void updateViews(VoiceRoomInfo roomInfo) {
        updateViews(roomInfo, false);
    }

    private void updateViews(VoiceRoomInfo roomInfo, boolean init) {
        checkUpdateContainer(roomInfo, init);
        if (ScoreBoardHelper.INSTANCE.isScoreBoardOpen()) {
            closeFuncIv.setVisibility(roomInfo.isSelfAdminOrOwner() ? View.VISIBLE : View.GONE);
            scoreAutoSelectLay.setVisibility(View.GONE);
            scoreManualSelectLay.setVisibility(View.GONE);
            scoreboardOpenBtn.setVisibility(View.GONE);
            modeTitleTv.setVisibility(View.GONE);
            seatTitleTv.setVisibility(View.INVISIBLE);
            ((ViewGroup.MarginLayoutParams) containerLay.getLayoutParams()).topMargin = ScreenUtil.dip2px(32);
            if (ScoreBoardHelper.INSTANCE.isScoreBoardAuto()) {
                scoreAutoTipOpenedTv.setVisibility(View.VISIBLE);
                scoreManualCtrlLay.setVisibility(View.GONE);
                scoreAutoTipOpenedTv.setVisibility(View.VISIBLE);
            } else {
                if (init) {
                    container.seatSelected(false);
                }
                scoreAutoTipOpenedTv.setVisibility(View.GONE);
                scoreManualCtrlLay.setVisibility(View.VISIBLE);
                scoreAutoTipOpenedTv.setVisibility(View.GONE);
                String addStr = "+" + manualScoreAdd;
                scoreManualAddScoreTv.setText(addStr);
                scoreManualDivScoreTv.setText(String.valueOf(manualScoreDiv));
            }
        } else {
            ((ViewGroup.MarginLayoutParams) containerLay.getLayoutParams()).topMargin = ScreenUtil.dip2px(48);
            seatTitleTv.setVisibility(View.VISIBLE);
            modeTitleTv.setVisibility(View.VISIBLE);
            scoreAutoTipOpenedTv.setVisibility(View.GONE);
            scoreAutoSelectLay.setVisibility(View.VISIBLE);
            scoreManualSelectLay.setVisibility(View.VISIBLE);
            scoreManualCtrlLay.setVisibility(View.GONE);
            closeFuncIv.setVisibility(View.GONE);
            scoreboardOpenBtn.setVisibility(View.VISIBLE);
            updateAutoManualSelect();
        }
    }

    private void updateAutoManualSelect() {
        scoreAutoSelectLay.setSelected(typeAuto);
        scoreManualSelectLay.setSelected(!typeAuto);
        scoreAutoSelectTitleTv.setTextColor(typeAuto ? 0xff030303 : 0xff999999);
        scoreManualSelectTitleTv.setTextColor(typeAuto ? 0xff999999 : 0xff030303);
    }

    private ScoreBoardDialogSeatContainer getSeatContainer(VoiceRoomInfo roomInfo) {
        if (container == null || container.getCurrentRoomGameType() != roomInfo.game_type) {
            container = new ScoreBoardDialogSeatContainer(containerLay, roomInfo.game_type);
        }
        return container;
    }

    private void checkUpdateContainer(VoiceRoomInfo roomInfo, boolean init) {
        getSeatContainer(roomInfo).update(roomInfo, init);
    }

    private void initData() {

    }

    private void initEvents() {
        View.OnClickListener listener = this::onClick;
        closeDialogIv.setOnClickListener(listener);
        closeFuncIv.setOnClickListener(listener);
        scoreboardOpenBtn.setOnClickListener(listener);
        scoreAutoSelectLay.setOnClickListener(listener);
        scoreManualSelectLay.setOnClickListener(listener);
        scoreManualDivScoreBtn.setOnClickListener(listener);
        scoreManualAddScoreBtn.setOnClickListener(listener);
        scoreManualDivScoreTv.setOnClickListener(listener);
        scoreManualAddScoreTv.setOnClickListener(listener);
    }

    private void onClick(View view) {
        if (view == closeFuncIv) {
            reqClose();
        } else if (view == closeDialogIv) {
            dismissAllowingStateLoss();
        } else if (view == scoreboardOpenBtn) {
            reqStart();
        } else if (view == scoreManualDivScoreBtn) {
            reqManualScore(manualScoreDiv);
        } else if (view == scoreManualAddScoreBtn) {
            reqManualScore(manualScoreAdd);
        } else if (view == scoreManualDivScoreTv) {
            showManualScoreDialog(false);
        } else if (view == scoreManualAddScoreTv) {
            showManualScoreDialog(true);
        } else if (view == scoreAutoSelectLay) {
            typeAuto = true;
            updateAutoManualSelect();
        } else if (view == scoreManualSelectLay) {
            typeAuto = false;
            updateAutoManualSelect();
        }
    }

    private void showManualScoreDialog(boolean add) {
        int[] values = add ? new int[]{10, 5, 3, 1} : new int[]{-10, -5, -3, -1};
        String[] strArrays = add ? new String[]{"+10", "+5", "+3", "+1"} : new String[]{"-10", "-5", "-3", "-1"};
        ListViewDialog.showDialog(getContext(), Arrays.asList(strArrays), position -> {
            if (add) {
                manualScoreAdd = values[position];
            } else {
                manualScoreDiv = values[position];
            }
            updateViews(VoiceRoomService.getInstance().getRoomInfo());
        });
    }

    private void reqClose() {
        DialogBuild.newBuilder(getContext()).setTitle(R.string.voice_room_score_board_close)
                .setContent(R.string.voice_room_score_board_close_tip)
                .setSureTx(ResUtil.getStr(R.string.confirm))
                .setCancelTx(R.string.cancel)
                .setDialogCallback(() -> {
                    if (getDialog() == null) { //已经关闭了
                        VoiceRoomPacketSender.closeScoreBoard(VoiceRoomService.getInstance().getRid());
                    } else {
                        pendingClose = false;
                        VoiceRoomPacketSender.closeScoreBoard(VoiceRoomService.getInstance().getRid()).observe(getViewLifecycleOwner(), rspHeadInfo -> {
                            if (rspHeadInfo.code == HttpCode.CODE_OK) {
                                dismissAllowingStateLoss();
                            } else {
                                ToastUtil.show(rspHeadInfo.desc);
                                updateViews(VoiceRoomService.getInstance().getRoomInfo(), false);
                            }
                            pendingClose = false;
                        });
                    }
                }).show();
    }

    private void reqStart() {
        if (container == null) {
            return;
        }
        List<Integer> list = container.getSelectedSeatNums();
        if (list.isEmpty()) {
            ToastUtil.show(R.string.voice_room_score_board_uid_list_empty);
            return;
        }
        VoiceRoomPacketSender.openScoreBoard(VoiceRoomService.getInstance().getRid(), list, typeAuto).observe(getViewLifecycleOwner(), rspHeadInfo -> {
            if (rspHeadInfo.code == HttpCode.CODE_OK) {
                if (typeAuto) {
                    dismissAllowingStateLoss();
                } else {
                    updateViews(VoiceRoomService.getInstance().getRoomInfo());
                }
            } else {
                ToastUtil.show(rspHeadInfo.desc);
            }
        });
    }

    private void reqManualScore(int score) {
        if (container == null) {
            return;
        }
        List<Integer> list = container.getSelectedSeatNums();
        if (list.isEmpty()) {
            ToastUtil.show(R.string.voice_room_score_board_uid_list_empty);
            return;
        }
        VoiceRoomPacketSender.manualScoreBoard(VoiceRoomService.getInstance().getRid(), list, score).observe(getViewLifecycleOwner(), rspHeadInfo -> {
            if (rspHeadInfo.code != HttpCode.CODE_OK) {
                ToastUtil.show(rspHeadInfo.desc);
            }
        });
    }

    public static void showDialog(Context context) {
        ScoreBoardDialog dialogFragment = new ScoreBoardDialog();
        dialogFragment.initBottom();
        dialogFragment.initFullWidth();
        dialogFragment.show(context, TAG);
    }

}
