package com.wepie.wespy.module.voiceroom.scoreboard

import androidx.lifecycle.Observer
import com.wejoy.weplay.ex.lifecycle.observe
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.voiceroom.IPluginTool
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IBasePlugin
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.ISmallFrameUtil

class ScoreBoardDelegateImpl(
    private val pluginTool: IPluginTool<IBasePlugin>
) : IBasePlugin {

    init {
        initUpdateRoomInfo()
    }

    private fun initUpdateRoomInfo() {
        val observer = Observer { roomInfo: VoiceRoomInfo? ->
            if (roomInfo == null) {
                return@Observer
            }
            if (ScoreBoardHelper.isScoreBoardManual() && roomInfo.isSelfAdminOrOwner) {
                pluginTool.loadView(true)
                VoicePluginService.getPlugin(ISmallFrameUtil::class.java).add(
                    pluginTool.getRealView<ScoreBoardSmallView>()!!
                )
            } else {
                val realView = pluginTool.getRealView<ScoreBoardSmallView>()
                    ?: return@Observer
                pluginTool.loadView(false)
                VoicePluginService
                    .getPlugin(ISmallFrameUtil::class.java)
                    .remove(realView)
            }
        }
        VoiceRoomService.getInstance().liveData.observe(pluginTool, observer)
    }
}