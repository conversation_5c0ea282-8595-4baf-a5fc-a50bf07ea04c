package com.wepie.wespy.module.voiceroom.scoreboard

import com.huiwan.constants.GameType
import com.huiwan.platform.ThreadUtil
import com.wepie.wespy.model.event.RoomInfoUpdateEvent
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher
import com.wepie.wespy.net.tcp.packet.RoomPushPackets.RoomSitScore
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets

object ScoreBoardHelper {
    private const val SCORE_BOARD_TYPE_AUTO = TmpRoomPackets.TmpCalScoreType.AUTO_CHARM_VALUE
    private const val SCORE_BOARD_TYPE_MANUAL = TmpRoomPackets.TmpCalScoreType.MANUAL_VALUE
    private var scoreBoardType = 0

    fun update(scoreBoardType: Int) {
        this.scoreBoardType = scoreBoardType
    }

    fun updateScoreBoard(typeValue: Int, sitScoreList: List<RoomSitScore>) {
        val roomInfo = VoiceRoomService.getInstance().roomInfo
        if (typeValue > 0) {
            scoreBoardType = typeValue
        }
        for (seatInfo in roomInfo.seatInfos) {
            for (sitScore in sitScoreList) {
                if (seatInfo.seat_num == sitScore.seatNum) {
                    seatInfo.scoreOpen = sitScore.open
                    seatInfo.score = sitScore.score
                    break
                }
            }
        }
        ThreadUtil.runOnUiThread {
            VoiceRoomService.getInstance().setLiveData(roomInfo)
        }
        EventDispatcher.postRoomInfoUpdateEvent(RoomInfoUpdateEvent.F_WIDGET_SCOREBOARD)
    }

    fun closeScoreBoard() {
        val roomInfo = VoiceRoomService.getInstance().roomInfo
        scoreBoardType = 0
        for (seatInfo in roomInfo.seatInfos) {
            seatInfo.scoreOpen = false
        }
        ThreadUtil.runOnUiThread {
            VoiceRoomService.getInstance().setLiveData(roomInfo)
        }
        EventDispatcher.postRoomInfoUpdateEvent(RoomInfoUpdateEvent.F_WIDGET_SCOREBOARD)
    }

    fun isScoreBoardOpen(): Boolean {
        return scoreBoardType > 0
    }

    fun isScoreBoardAuto(): Boolean {
        return scoreBoardType == SCORE_BOARD_TYPE_AUTO
    }

    fun isScoreBoardManual(): Boolean {
        return scoreBoardType == SCORE_BOARD_TYPE_MANUAL
    }

    fun supportScoreBoard(gameType: Int): Boolean {
        return gameType == GameType.GAME_TYPE_VOICE_ROOM ||
                gameType == GameType.GAME_TYPE_FAMILY_ROOM ||
                gameType == GameType.GAME_TYPE_VIDEO_ROOM
    }
}