package com.wepie.wespy.module.voiceroom.scoreboard;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;

public class HonorScoreSeatView extends ScoreSeatView {
    private int baseNumber = 8;;
    public HonorScoreSeatView(@NonNull Context context) {
        super(context);
    }

    public HonorScoreSeatView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected int getResLayout() {
        return R.layout.voice_honor_score_board_seat;
    }


    @Override
    public void showUser(VoiceRoomInfo.SeatInfo seatInfo, VoiceRoomInfo roomInfo, boolean init) {
        if (roomInfo.isHonorSeatOpen) {
            setVisibility(VISIBLE);
            seatNum = seatInfo.seat_num;
            super.showUser(seatInfo, roomInfo, init);
            if (roomInfo.isFamilyRoom()) {
                baseNumber = 12;
            }
            setSeatNumber();
        } else {
            setVisibility(GONE);
        }
    }

    @Override
    protected void setSeatNumber() {
        seatNumTv.setText(String.valueOf(seatSeq + baseNumber));
    }
}
