package com.wepie.wespy.module.voiceroom.setting;

import android.os.Bundle;
import androidx.annotation.Nullable;

import android.text.InputFilter;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.huiwan.base.util.inputfilter.LangEnterLengthFilter;
import com.wepie.wespy.R;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.IntentConfig;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.wespy.model.entity.RoomInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.makefriend.dataservice.VoiceRoomLastDataManager;
import com.wepie.wespy.module.voiceroom.dataservice.RoomCallback;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.main.RoomViewLogic;
import com.huiwan.base.util.PressUtil;
import com.huiwan.base.util.ToastUtil;

public class VoiceRoomNoticeEditActivity extends BaseActivity {

    private ImageView backImg;
    private TextView enterTx;
    private EditText noticeEt;
    private String roomNote;
    private BaseWpActionBar baseActionBar;
    private TextView title;
    private TextView bottomTip;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_voice_room_notice_edit);
        final int rid = getIntent().getIntExtra(IntentConfig.INT_ROOM_ID, -1);
        roomNote = getIntent().getStringExtra(IntentConfig.INT_ROOM_NOTE);
        int gameType = getIntent().getIntExtra(IntentConfig.GAME_TYPE, -1);

        backImg = (ImageView) findViewById(R.id.back_img);
        enterTx = (TextView) findViewById(R.id.enter_tx);
        noticeEt = (EditText) findViewById(R.id.activity_edit_signature_et);
        baseActionBar = findViewById(R.id.report_title_lay);
        title = (TextView) findViewById(R.id.title);
        bottomTip = (TextView) findViewById(R.id.bottom_tip);

        if (gameType == RoomInfo.GAME_TYPE_LOVEHOME) {
            title.setText(R.string.lover_home_love_sticker);
            noticeEt.setHint(R.string.lover_home_love_sticker_input_tip);
            bottomTip.setVisibility(View.GONE);
        } else if (gameType == RoomInfo.GAME_TYPE_WEDDING) {
            title.setText(R.string.wedding_room_bulletin);
            noticeEt.setHint(R.string.bulletin_input_hint);
            bottomTip.setVisibility(View.VISIBLE);
        } else {
            title.setText(R.string.voice_room_notice_title);
            noticeEt.setHint(R.string.voice_room_notice_input_hint);
            bottomTip.setVisibility(View.VISIBLE);
        }

        if(!TextUtils.isEmpty(roomNote)) {
            noticeEt.setText(roomNote);
            try {
                noticeEt.setSelection(roomNote.length());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        PressUtil.addPressEffect(backImg);
        backImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });

        PressUtil.addPressEffect(enterTx);
        enterTx.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                final String note = noticeEt.getText().toString();
                RoomViewLogic.modRoomNote(VoiceRoomNoticeEditActivity.this, rid, note, new RoomCallback(VoiceRoomNoticeEditActivity.this) {
                    @Override
                    public void onSuccess(int rid) {
                        VoiceRoomInfo voiceRoomInfo = VoiceRoomService.getInstance().getRoomInfo(rid);
                        if (voiceRoomInfo.isSelfOwner() && !voiceRoomInfo.isAdvancedOrAnchorOrFamilyRoom()) {
                            VoiceRoomLastDataManager.getInstance().setVoiceRoomNote(note, voiceRoomInfo.game_type);
                        }
                        finish();
                    }

                    @Override
                    public void onFail(String msg) {
                        ToastUtil.show(msg);
                    }
                });
            }
        });
    }

    @Override
    public int supportFloatView() {
        return 0;
    }
}
