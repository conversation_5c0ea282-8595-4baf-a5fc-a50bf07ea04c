package com.wepie.wespy.module.voiceroom.setting;

import com.huiwan.base.str.ResUtil;
import com.wepie.wespy.R;

public class SettingItem {
    public String itemName = "";
    public int itemIconRes;
    public Runnable itemFunction;

    public static final int INVITE_FRIEND = R.string.voice_room_setting_item_invite;
    public static final int REPORT_ROOM = R.string.report_voice_room;
    public static final int EXIT_ROOM = R.string.voice_room_setting_item_exit;
    public static final int GIFT_SOUND = R.string.voice_room_setting_item_gift_sound;
    public static final int ROOM_SETTING = R.string.room_setting;
    public static final int RECOMMEND_CARD = R.string.voice_room_setting_item_rec_card;
    public static final int CALL_RECOMMEND = R.string.voice_room_setting_item_call_recommend;
    public static final int RECEIVE_CALL = R.string.voice_room_setting_item_call_recv;
    public static final int CALL_FANS = R.string.voice_room_setting_item_call_fans;
    public static final int ROOM_DECORATE = R.string.voice_room_setting_item_decor;
    public static final int OVER_WEDDING = R.string.voice_room_setting_item_wedding_over;
    public static final int GAME_GUIDE = R.string.voice_room_setting_item_guide;
    public static final int CALL_CP = R.string.voice_room_setting_item_call_cp;
    public static final int VOLUME_ADJUST = R.string.volume_adjust;
    public static final int VOLUME_COCOS_ADJUST = R.string.voice_room_setting_item_regulate_voice;

    public static final int UNITY_QUALITY = R.string.voice_room_setting_item_unity_quality;

    public static final int GIFT_ANIM = R.string.voice_room_setting_item_gift_anim;

    public SettingItem(int itemName, int itemIconRes, Runnable itemFunction) {
        this.itemName = ResUtil.getStr(itemName);
        this.itemIconRes = itemIconRes;
        this.itemFunction = itemFunction;
    }
}
