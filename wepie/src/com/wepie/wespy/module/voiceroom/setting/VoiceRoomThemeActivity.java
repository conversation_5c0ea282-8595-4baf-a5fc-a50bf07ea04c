package com.wepie.wespy.module.voiceroom.setting;

import android.os.Bundle;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.base.util.collection.IMap;
import com.huiwan.base.util.collection.ListWrapper;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.configservice.model.voiceroom.AdvanceRoomLevelConfig;
import com.huiwan.configservice.model.voiceroom.VoiceThemeInfo;
import com.huiwan.constants.IntentConfig;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.three.http.callback.LifeDataCallback;
import com.three.http.callback.Result;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.marry.MyPropInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.game.room.roomcreate.typeselect.PreviewInfo;
import com.wepie.wespy.module.game.room.roomcreate.typeselect.VoiceRoomPreviewSelectView;
import com.wepie.wespy.module.makefriend.dataservice.VoiceRoomLastDataManager;
import com.wepie.wespy.module.voiceroom.dataservice.RoomCallback;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.main.RoomViewLogic;
import com.wepie.wespy.net.http.api.PropApi;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * date 2018/6/29
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class VoiceRoomThemeActivity extends BaseActivity {
    private BaseWpActionBar actionBar;
    private int rid;
    private VoiceRoomPreviewSelectView previewSelectView;
    int bgId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_voice_room_bg);
        actionBar = findViewById(R.id.room_bg_action_bar);
        previewSelectView = findViewById(R.id.voice_select_view);
        previewSelectView.setSource(VoiceRoomPreviewSelectView.SOURCE_FROM_THEME);
        previewSelectView.setItemBounds(ScreenUtil.dip2px(84), ScreenUtil.dip2px(84));
        initData();
        initEvent();
    }

    private void initData() {
        bgId = getIntent().getIntExtra(IntentConfig.INT_ROOM_BG, 0);
        rid = getIntent().getIntExtra(IntentConfig.INT_ROOM_ID, 0);
        VoiceRoomInfo info = VoiceRoomService.getInstance().getRoomInfo(rid);
        initThemes(info);
        updateOwnerBgItems(info);
        previewSelectView.selectId(bgId);
        getAdvanceThemes(info);
    }

    private void initThemes(VoiceRoomInfo info) {
        List<PreviewInfo> themes = new ArrayList<>();
        boolean isFamily = info.isFamilyRoom();
        themes.add(new PreviewInfo(PreviewInfo.SHOP_ID, ResUtil.getString(R.string.go_shop), R.drawable.theme_go_shop));
        if (isFamily || info.advanceRoomLevel >= 10) {
            PreviewInfo previewInfo = new PreviewInfo(PreviewInfo.CUSTOM_ID, ResUtil.getString(R.string.edit_background), R.drawable.add_grey_bg_icon);
            previewInfo.setBgUrl(info.roomBgUrl);
            previewInfo.setFgUrl(info.roomBgUrl);
            themes.add(previewInfo);
        }
        themes.addAll(getAdvanceThemes(info));
        List<PreviewInfo> configThemes = new ListWrapper<>(isFamily ? ConfigHelper.getInstance().getFamilyRoomThemes() : ConfigHelper.getInstance().getVoiceRoomThemes())
                .map(new IMap<VoiceThemeInfo, PreviewInfo>() {
                    @Override
                    public PreviewInfo transform(VoiceThemeInfo themeInfo) {
                        return PreviewInfo.fromVoiceThemeInfo(themeInfo);
                    }
                });
        themes.addAll(configThemes);
        previewSelectView.updateList(themes);
    }

    private List<PreviewInfo> getAdvanceThemes(VoiceRoomInfo info) {
        List<AdvanceRoomLevelConfig> configs = ConfigHelper.getInstance().getVoiceRoomConfig().getAdvancedRoomConfig().getLevelConfig();
        List<MyPropInfo> propInfos = new ArrayList<>();
        for (AdvanceRoomLevelConfig config : configs) {
            if (config.getLevel() <= info.advanceRoomLevel) {
                for (int id : config.getPropIDs()) {
                    MyPropInfo myPropInfo = new MyPropInfo();
                    myPropInfo.prop_id = id;
                    myPropInfo.level = config.getLevel();
                    propInfos.add(myPropInfo);
                }
            }
        }
        Collections.sort(propInfos, new Comparator<MyPropInfo>() {
            @Override
            public int compare(MyPropInfo o1, MyPropInfo o2) {
                return Integer.compare(o2.level, o1.level);
            }
        });
        return buildPropThemes(propInfos, info.game_type);
    }

    private List<PreviewInfo> buildPropThemes(List<MyPropInfo> propInfos, int gameType) {
        List<PreviewInfo> previewItems = new ArrayList<>();
        for (MyPropInfo myPropInfo : propInfos) {
            PropItem item = ConfigHelper.getInstance().getPropConfig().getPropItem(myPropInfo.getPropId());
            if (item != null) {
                PreviewInfo previewInfo = PreviewInfo.fromPropItem(item, item.getMediaUrl(), myPropInfo.getTimeLeftString(), gameType);
                if (previewInfo != null) {
                    previewItems.add(previewInfo);
                }
            }
        }
        return previewItems;
    }

    private void updateThemes(List<PreviewInfo> previewItems) {
        if (!previewItems.isEmpty()) {
            previewSelectView.addList(previewItems);
            previewSelectView.selectId(bgId);
        }
    }

    private void updateOwnerBgItems(VoiceRoomInfo info) {
        if (info == null) {
            return;
        }
        PropApi.getTargetPropListByType(info.owner, PropItem.TYPE_VOICE_THEME, new LifeDataCallback<>(this) {
            @Override
            public void onSuccess(Result<List<MyPropInfo>> result) {
                updateThemes(buildPropThemes(result.data, info.game_type));
            }

            @Override
            public void onFail(int code, String msg) {
                ToastUtil.show(msg);
            }
        });
    }

    private void initEvent() {
        previewSelectView.setCallback(url -> changeTheme(url, PreviewInfo.CUSTOM_ID));
        actionBar.addTitleRightTextWithBack(R.string.activity_voice_room_setting_bg, R.string.save, v -> onBackPressed(), v -> {
            final int selectedId = previewSelectView.getSelectedId();
            String url = "";
            if (selectedId == PreviewInfo.CUSTOM_ID) {
                url = previewSelectView.getCustomUrl();
            }
            changeTheme(url, selectedId);
        });
    }

    private void changeTheme(String url, int customId) {
        RoomViewLogic.modRoomTheme(VoiceRoomThemeActivity.this, rid, customId, url, new RoomCallback(this) {
            @Override
            public void onSuccess(int rid) {
                VoiceRoomInfo voiceRoomInfo = VoiceRoomService.getInstance().getRoomInfo(rid);
                if (voiceRoomInfo.isSelfOwner()) {
                    VoiceRoomLastDataManager.getInstance().setVoiceRoomBg(customId, voiceRoomInfo.game_type);
                }
                ToastUtil.show(R.string.change_bg_success);
                finish();
            }

            @Override
            public void onFail(String msg) {
                ToastUtil.show(msg);
            }
        });
    }


    @Override
    public int supportFloatView() {
        return 0;
    }
}
