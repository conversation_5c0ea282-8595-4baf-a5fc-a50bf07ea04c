package com.wepie.wespy.module.voiceroom.setting;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.wepie.wespy.R;

import java.util.ArrayList;
import java.util.List;

public class VoiceRoomSettingAdapter extends RecyclerView.Adapter<VoiceRoomSettingAdapter.ViewHolder> {
    private final List<SettingItem> itemList = new ArrayList<>();
    private final Context context;

    public VoiceRoomSettingAdapter(List<SettingItem> itemList, Context context) {
        this.itemList.clear();
        this.itemList.addAll(itemList);
        this.context = context;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(LayoutInflater.from(context).inflate(R.layout.voice_room_setting_item, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        SettingItem settingItem = itemList.get(position);
        holder.nameTv.setText(settingItem.itemName);
        holder.iconIv.setImageResource(settingItem.itemIconRes);
        holder.itemView.setOnClickListener(v -> {
            settingItem.itemFunction.run();
        });
    }

    @Override
    public int getItemCount() {
        return itemList.size();
    }

    public void updateList(List<SettingItem> list) {
        itemList.clear();
        itemList.addAll(list);
        notifyDataSetChanged();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView nameTv;
        ImageView iconIv;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            nameTv = itemView.findViewById(R.id.setting_item_name_tv);
            iconIv = itemView.findViewById(R.id.setting_item_icon_iv);
        }
    }

}
