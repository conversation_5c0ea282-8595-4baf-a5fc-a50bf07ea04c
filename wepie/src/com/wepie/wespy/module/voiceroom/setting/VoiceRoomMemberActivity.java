package com.wepie.wespy.module.voiceroom.setting;

import android.app.Activity;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.ui.empty.HWUIEmptyView;
import com.huiwan.base.util.PressUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.voiceroom.AdvanceRoomLevelConfig;
import com.huiwan.constants.IntentConfig;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.voice.AdminChangeEvent;
import com.wepie.wespy.module.contact.friendlist.SearchResultView;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.member.IRoomMember;
import com.wepie.wespy.module.voiceroom.member.VoiceRoomMemberAdapter;
import com.wepie.wespy.module.voiceroom.member.VoiceRoomMemberPresenter;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

public class VoiceRoomMemberActivity extends BaseActivity implements IRoomMember {
    public static final int ACTION_TYPE_MEMBER_LIST = 1;
    public static final int ACTION_TYPE_SELECT_INVITE = 2;
    public static final int ACTION_TYPE_SELECT_ADMIN = 3;
    public static final int ACTION_TYPE_SELECT_DRAWER = 4;
    public static final int ACTION_TYPE_SELECT_BLACK = 5;
    public static final int ACTION_TYPE_SELECT_FAMILY_ADMIN = 6;
    public static final int ACTION_TYPE_ROOM_SETTING_SELECT_ADMIN = 7;
    public static final int ACTION_TYPE_GUARD = 8;

    public static final int DEFAULT_MAX_ADMIN_NUM = 3;

    private VoiceRoomMemberPresenter presenter;
    private ImageView backImg;
    private TextView deleteTx;
    private ListView listView;
    private TextView memberNumTv;
    private VoiceRoomMemberAdapter adapter;
    private HWUIEmptyView emptyView;
    private int rid;
    private int actionType;
    private int inviteSeatNum;
    private int adminMaxCount;
    private BaseWpActionBar baseActionBar;
    private SearchResultView searchView;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_voice_room_member);
        EventBus.getDefault().register(this);
        rid = getIntent().getIntExtra(IntentConfig.INT_ROOM_ID, rid);
        actionType = getIntent().getIntExtra(IntentConfig.VOICE_ROOM_MEMBER_LIST_ACTION, ACTION_TYPE_MEMBER_LIST);
        inviteSeatNum = getIntent().getIntExtra(IntentConfig.VOICE_ROOM_SEAT_NUM, -1);

        adminMaxCount = DEFAULT_MAX_ADMIN_NUM;
        VoiceRoomInfo info = VoiceRoomService.getInstance().getRoomInfo(rid);
        if (info.isAdvancedOrAnchorOrFamilyRoom()) {
            AdvanceRoomLevelConfig config = ConfigHelper.getInstance().getVoiceRoomConfig().getAdvancedRoomConfig().getConfigByLevel(info.advanceRoomLevel);
            if (config != null) {
                adminMaxCount = config.getAdminNum();
            }
        }

        backImg = (ImageView) findViewById(R.id.back_img);
        deleteTx = (TextView) findViewById(R.id.room_delete_tv);
        listView = (ListView) findViewById(R.id.room_member_list_view);
        memberNumTv = (TextView) findViewById(R.id.room_num_tv);
        baseActionBar = findViewById(R.id.activity_friend_action_bar);
        searchView = (SearchResultView) findViewById(R.id.room_search_result_view);
        emptyView = findViewById(R.id.voice_room_member_empty_view);
        presenter = new VoiceRoomMemberPresenter(this, rid, adminMaxCount);
        adapter = new VoiceRoomMemberAdapter(this, presenter, listView, searchView, actionType, inviteSeatNum);
        listView.setAdapter(adapter);

        deleteTx.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                presenter.clickManagerBt();
            }
        });

        backImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (searchView.isShown()) {
                    searchView.hide();
                } else {
                    finish();
                }
            }
        });
        PressUtil.addPressEffect(backImg);

        presenter.loadData();
        if (actionType == ACTION_TYPE_SELECT_BLACK) {
            presenter.loadBlackList();
        }
    }

    @Override
    public void showManageState(VoiceRoomInfo roomInfo) {
        deleteTx.setText(R.string.voice_room_members_op_manage);
        showTitle(roomInfo);
        checkShowEmpty(adapter.update(roomInfo));
        if (searchView.isShown()) {
            searchView.updateWithRoomInfo(roomInfo.uidList);
        }

    }

    @Override
    public void showDeleteState(VoiceRoomInfo roomInfo) {
        deleteTx.setText(R.string.voice_room_members_op_del);
        showTitle(roomInfo);
        checkShowEmpty(adapter.update(roomInfo));
        if (searchView.isShown()) {
            searchView.updateWithRoomInfo(roomInfo.uidList);
        }
    }

    @Override
    public void showKickDialog(String txt, int size) {
        if (VoiceRoomService.getInstance().getRoomInfo(rid).isAdvancedOrAnchorOrFamilyRoom()) {
            DialogBuild.newBuilder(this)
                    .setSingleBtn(false)
                    .setContent(txt)
                    .setSureTx(R.string.sure)
                    .setCancelTx(R.string.cancel)
                    .setWithNotice(true)
                    .setDefaultSelect(false)
                    .setNoticeText(R.string.voice_room_members_op_del_ext_tip)
                    .setNoticeCallback(new DialogBuild.NoticeCallback() {
                        @Override
                        public void onClickSure(boolean select) {
                            presenter.doKickUsers(select);
                        }

                        @Override
                        public void onClickCancel(boolean select) {

                        }
                    }).show();
        } else {
            DialogBuild.newBuilder(this)
                    .setSingleBtn(false)
                    .setContent(txt)
                    .setSureTx(R.string.sure)
                    .setCancelTx(R.string.cancel)
                    .setDialogCallback(new DialogBuild.DialogCallback() {
                        @Override
                        public void onClickSure() {
                            presenter.doKickUsers(false);
                        }
                    }).show();
        }
    }

    @Override
    public void onChooseChange(int num) {

    }

    @Override
    public void showSearchResult(int total,List<Integer> uids,boolean clear) {

    }

    @Override
    public void enableRefresh(boolean enable) {

    }

    @Override
    public void enableLoadMore(boolean enable) {

    }

    @Override
    public void onItemClick(int actionType,int uid) {

    }

    @Override
    public void afterKickUser(List<Integer> kickUids) {

    }

    @Override
    public Activity getContextAsActivity() {
        return this;
    }

    @Override
    public void onBackPressed() {
        if (searchView.isShown()) {
            searchView.hide();
        } else {
            super.onBackPressed();
        }
    }

    private void checkShowEmpty(int num) {
        String tips = "";
        if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN) {
            tips = ResUtil.getStr(R.string.voice_room_members_admin_none);
        } else if (actionType == VoiceRoomMemberActivity.ACTION_TYPE_SELECT_BLACK) {
            tips = ResUtil.getStr(R.string.voice_room_members_block_none);
        }
        if (!TextUtil.isEmpty(tips) && num == 0) {
            emptyView.setText(tips);
            emptyView.setVisibility(View.VISIBLE);
            listView.setVisibility(View.GONE);
        } else {
            emptyView.setVisibility(View.GONE);
            listView.setVisibility(View.VISIBLE);
        }
    }

    private void showTitle(VoiceRoomInfo roomInfo) {
        boolean isOwner = roomInfo.isSelfOwner();
        boolean isAdmin = roomInfo.isSelfAdmin();

        deleteTx.setVisibility((actionType == ACTION_TYPE_MEMBER_LIST) && (isOwner || isAdmin) ? View.VISIBLE : View.GONE);

        if (actionType == ACTION_TYPE_MEMBER_LIST) {
            memberNumTv.setText(ResUtil.getStr(R.string.voice_room_members_title_member_d, roomInfo.onlineNum));
        } else if (actionType == ACTION_TYPE_SELECT_INVITE) {
            memberNumTv.setText(R.string.voice_room_members_title_sel_to_mic);
        } else if (actionType == ACTION_TYPE_SELECT_ADMIN) {
            memberNumTv.setText(ResUtil.getStr(R.string.voice_room_members_title_sel_admin_d_d, roomInfo.adminList.size(), adminMaxCount));
            deleteTx.setText(R.string.add);
            deleteTx.setVisibility(View.VISIBLE);
            presenter.setCur_bt_state(VoiceRoomMemberPresenter.BT_STATE_ADD);
        } else if (actionType == ACTION_TYPE_SELECT_DRAWER) {
            memberNumTv.setText(R.string.voice_room_members_title_sel_to_draw);
        } else if (actionType == ACTION_TYPE_SELECT_BLACK) {
            memberNumTv.setText(R.string.voice_room_members_title_block);
        } else if (actionType == ACTION_TYPE_SELECT_FAMILY_ADMIN) {
            memberNumTv.setText(R.string.voice_room_members_title_sel_family_admin);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onListChange(final AdminChangeEvent event) {
        if (actionType == ACTION_TYPE_SELECT_ADMIN) {
            presenter.loadData();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        presenter.clear();
        EventBus.getDefault().unregister(this);
    }

    public void refreshMemberList(VoiceRoomInfo roomInfo) {
        checkShowEmpty(adapter.update(roomInfo));
        if (searchView.isShown()) {
            searchView.hide();
        }
    }

    @Override
    public int supportFloatView() {
        return 0;
    }
}
