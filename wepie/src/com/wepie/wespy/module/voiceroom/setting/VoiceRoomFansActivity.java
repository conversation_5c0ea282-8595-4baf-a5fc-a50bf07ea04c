package com.wepie.wespy.module.voiceroom.setting;

import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.IntentConfig;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LifeUserListSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.huiwan.widget.rv.RVHolder;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.view.HeaderNameItemView;
import com.wepie.wespy.helper.view.SimpleDividerDecoration;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

import java.util.ArrayList;
import java.util.List;

/**
 * date 2018/7/2
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class VoiceRoomFansActivity extends BaseActivity {
    private BaseWpActionBar actionBar;
    private TextView emptyView;
    private int rid;
    private int curPage = 1;
    private SmartRefreshLayout refresher;
    private Adapter adapter = new Adapter();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_voice_room_fans);
        rid = getIntent().getIntExtra(IntentConfig.INT_ROOM_ID, 0);
        initViews();
        initData();
    }

    private void initViews() {
        actionBar = findViewById(R.id.room_fans_action_bar);
        emptyView = findViewById(R.id.room_fans_empty_view);
        refresher = findViewById(R.id.room_fans_list_refresher);
        refresher.setOnRefreshListener(refreshlayout -> refresh());
        refresher.setOnLoadMoreListener(refreshlayout -> loadMore());
        actionBar.addTitleAndBack(R.string.fans_list);
        RecyclerView list = findViewById(R.id.room_fans_list_view);
        list.setLayoutManager(new LinearLayoutManager(this));
        list.setAdapter(adapter);
        list.addItemDecoration(new SimpleDividerDecoration());
    }

    private void initData() {
        if (rid != 0) {
            refresher.autoRefresh(0, 100, 0.5f, false);
            refresher.setEnableAutoLoadMore(true);
        }
    }

    private void refresh() {
        VoiceRoomPacketSender.getFollowerList(rid, getOwnerUid(), 1, new LifeSeqCallback(this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                refresher.finishRefresh();
                if (head.message != null && head.message instanceof TmpRoomPackets.FollowerListRsp) {
                    TmpRoomPackets.FollowerListRsp rsp = (TmpRoomPackets.FollowerListRsp) head.message;
                    int count = rsp.getFollowListCount();
                    if (count == 0) {
                        actionBar.refreshTitleText(R.string.fans_list);
                        emptyView.setVisibility(View.VISIBLE);
                        adapter.clear();
                    } else {
                        actionBar.refreshTitleText(ResUtil.getResource().getString(R.string.fans_list_d, rsp.getFollowerNum()));
                        emptyView.setVisibility(View.GONE);
                        List<TmpRoomPackets.UserInfo> userInfoList = rsp.getFollowListList();
                        List<Integer> list = new ArrayList<>(count);
                        for (int i = 0; i < count; i++) {
                            list.add(userInfoList.get(i).getUid());
                        }
                        listRefresh(list);
                        curPage = 1;
                    }
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private int getOwnerUid() {
        return VoiceRoomService.getInstance().getRoomInfo(rid).owner;
    }

    private void loadMore() {
        VoiceRoomPacketSender.getFollowerList(rid, getOwnerUid(), curPage + 1, new LifeSeqCallback(this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                refresher.finishLoadMore();
                if (head.message instanceof TmpRoomPackets.FollowerListRsp) {
                    TmpRoomPackets.FollowerListRsp rsp = (TmpRoomPackets.FollowerListRsp) head.message;
                    actionBar.refreshTitleText(ResUtil.getResource().getString(R.string.fans_list_d, rsp.getFollowerNum()));
                    int count = rsp.getFollowListCount();
                    List<TmpRoomPackets.UserInfo> userInfoList = rsp.getFollowListList();
                    List<Integer> list = new ArrayList<>(count);
                    for (int i = 0; i < count; i++) {
                        list.add(userInfoList.get(i).getUid());
                    }
                    listLoadMore(list);
                    if (count > 0) {
                        curPage++;
                    }
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private void listRefresh(List<Integer> uidList) {
        UserService.get().getCacheSimpleUserList(uidList, new LifeUserListSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(List<UserSimpleInfo> userSimpleInfos) {
                adapter.refresh(userSimpleInfos);
            }

            @Override
            public void onUserInfoFailed(String description) {
                ToastUtil.show(description);
            }
        });
    }

    private void listLoadMore(List<Integer> uidList) {
        UserService.get().getCacheSimpleUserList(uidList, new LifeUserListSimpleInfoCallback(this) {
            @Override
            public void onUserInfoSuccess(List<UserSimpleInfo> userSimpleInfos) {
                adapter.add(userSimpleInfos);
            }

            @Override
            public void onUserInfoFailed(String description) {
                ToastUtil.show(description);
            }
        });
    }

    private static class Adapter extends RecyclerView.Adapter<RVHolder> {
        private List<UserSimpleInfo> mUsers = new ArrayList<>();

        void refresh(List<UserSimpleInfo> users) {
            mUsers.clear();
            mUsers.addAll(users);
            notifyDataSetChanged();
        }

        void add(List<UserSimpleInfo> users) {
            int start = mUsers.size();
            mUsers.addAll(users);
            notifyItemRangeInserted(start, users.size());
        }

        void clear() {
            mUsers.clear();
            notifyDataSetChanged();
        }


        @Override
        public void onBindViewHolder(@NonNull RVHolder holder, int position) {
            UserSimpleInfo simpleInfo = mUsers.get(position);
            ((HeaderNameItemView)holder.itemView).bindUserInfo(simpleInfo);
        }

        @NonNull
        @Override
        public RVHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            return new RVHolder(new HeaderNameItemView(parent.getContext()));
        }

        @Override
        public int getItemCount() {
            return mUsers.size();
        }
    }

    @Override
    public int supportFloatView() {
        return 0;
    }
}
