package com.wepie.wespy.module.voiceroom.setting

import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo
import com.wepie.wespy.module.voiceroom.IPluginTool
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.ISettingPlugin
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class VoiceRoomSettingDelegateImpl(private val pluginTool: IPluginTool<ISettingPlugin>) :
    ISettingPlugin by pluginTool.proxy() {

    override fun showSettingView(roomInfo: VoiceRoomInfo?) {
        roomInfo ?: return
        pluginTool.loadView(true)
        pluginTool.proxy().show(roomInfo) {
            MainScope().launch {
                delay(250)
                pluginTool.loadView(false)
            }
        }
    }
}