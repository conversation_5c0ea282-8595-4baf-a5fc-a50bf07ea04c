package com.wepie.wespy.module.voiceroom.setting;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.lifecycle.LifecycleOwner;

import com.wepie.wespy.R;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.IntentConfig;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.wespy.model.entity.RoomInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.makefriend.dataservice.VoiceRoomLastDataManager;
import com.wepie.wespy.module.makefriend.label.VoiceRoomLabelSelectView;
import com.wepie.wespy.module.voiceroom.dataservice.RoomCallback;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.main.RoomViewLogic;

/**
 * date 2018/6/29
 * email <EMAIL>
 *
 * <AUTHOR>
 */
public class VoiceRoomLabelActivity extends BaseActivity {
    private BaseWpActionBar actionBar;
    private int rid;
    private VoiceRoomLabelSelectView selectView;
    private int gameType;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_voice_room_label);
        actionBar = findViewById(R.id.room_label_action_bar);
        selectView = findViewById(R.id.label_select_view);
        initEvent();

        gameType = getIntent().getIntExtra(IntentConfig.GAME_TYPE, RoomInfo.GAME_TYPE_VOICE_ROOM);
        int tagId = getIntent().getIntExtra(IntentConfig.INT_ROOM_TAG, 0);
        selectView.refresh(gameType);
        selectView.setLabelId(tagId);
        rid = getIntent().getIntExtra(IntentConfig.INT_ROOM_ID, 0);
    }

    private void initEvent() {
        actionBar.addTitleRightTextWithBack(R.string.voice_room_label, R.string.save, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onBackPressed();
            }
        }, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final int selectedId = selectView.getLabelId();
                if (rid > 0) {
                    RoomViewLogic.modRoomLabel(VoiceRoomLabelActivity.this, rid, selectedId, new RoomCallback(VoiceRoomLabelActivity.this) {
                        @Override
                        public void onSuccess(int rid) {
                            VoiceRoomInfo voiceRoomInfo = VoiceRoomService.getInstance().getRoomInfo(rid);
                            if (voiceRoomInfo != null && voiceRoomInfo.isSelfOwner() && !voiceRoomInfo.isAdvancedOrAnchorOrFamilyRoom()) {
                                VoiceRoomLastDataManager.getInstance().setVoiceRoomLabel(selectedId, gameType);
                            }
                            finish();
                        }

                        @Override
                        public void onFail(String msg) {
                        }
                    });
                } else {
                    Intent intent = new Intent();
                    intent.putExtra(IntentConfig.LABEL_ID, selectView.getLabelId());
                    setResult(Activity.RESULT_OK, intent);
                    finish();
                }
            }
        });
    }

    @Override
    public int supportFloatView() {
        return 0;
    }
}
