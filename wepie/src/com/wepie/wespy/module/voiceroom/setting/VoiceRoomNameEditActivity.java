package com.wepie.wespy.module.voiceroom.setting;

import android.os.Bundle;
import androidx.annotation.Nullable;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.huiwan.base.util.inputfilter.LangEnterLengthFilter;
import com.wepie.wespy.R;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.IntentConfig;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.wespy.model.entity.RoomInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.common.SimpleTextWatcher;
import com.wepie.wespy.module.makefriend.dataservice.VoiceRoomLastDataManager;
import com.wepie.wespy.module.voiceroom.dataservice.RoomCallback;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.main.RoomViewLogic;
import com.huiwan.base.util.PressUtil;

public class VoiceRoomNameEditActivity extends BaseActivity {
    private ImageView backImg;
    private TextView enterTx;
    private EditText editNameEt;
    private ImageView deleteBtn;
    private BaseWpActionBar baseActionBar;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_voice_room_name_edit);
        final int rid = getIntent().getIntExtra(IntentConfig.INT_ROOM_ID, -1);
        String roomName = getIntent().getStringExtra(IntentConfig.INT_ROOM_NAME);
        int gameType = getIntent().getIntExtra(IntentConfig.GAME_TYPE, 0);

        backImg = (ImageView) findViewById(R.id.back_img);
        enterTx = (TextView) findViewById(R.id.enter_tx);
        baseActionBar = findViewById(R.id.activity_friend_action_bar);
        deleteBtn = findViewById(R.id.activity_edit_del_btn);

        editNameEt = (EditText) findViewById(R.id.activity_edit_name_et);

        editNameEt.setHint(gameType == RoomInfo.GAME_TYPE_WEDDING ? R.string.voice_room_name_input_hint_wedding : R.string.room_name_input_hint);

        if(!TextUtils.isEmpty(roomName)) {
            editNameEt.setText(roomName);
            try {
                editNameEt.setSelection(roomName.length());
            } catch (Exception e){
                e.printStackTrace();
            }
        }
        editNameEt.setFilters(new InputFilter[]{new LangEnterLengthFilter(LangEnterLengthFilter.NICKNAME_LIMIT)});

        PressUtil.addPressEffect(backImg);
        backImg.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });

        PressUtil.addPressEffect(enterTx);
        enterTx.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                final String content = editNameEt.getText().toString().trim();
                RoomViewLogic.modRoomName(VoiceRoomNameEditActivity.this, rid, content, new RoomCallback(VoiceRoomNameEditActivity.this) {
                    @Override
                    public void onSuccess(int rid) {
                        VoiceRoomInfo voiceRoomInfo = VoiceRoomService.getInstance().getRoomInfo(rid);
                        if (voiceRoomInfo.isSelfOwner() && !voiceRoomInfo.isAdvancedOrAnchorOrFamilyRoom()) {
                            VoiceRoomLastDataManager.getInstance().setVoiceRoomName(content, voiceRoomInfo.game_type);
                        }
                        finish();
                    }

                    @Override
                    public void onFail(String msg) {
                    }
                });
            }
        });
        editNameEt.addTextChangedListener(new SimpleTextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i2, int i3) { }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) { }

            @Override
            public void afterTextChanged(Editable s) {
                deleteBtn.setVisibility(s.length() > 0 ? View.VISIBLE : View.GONE);
            }
        });
        deleteBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                editNameEt.setText("");
            }
        });
    }

    @Override
    public int supportFloatView() {
        return 0;
    }
}
