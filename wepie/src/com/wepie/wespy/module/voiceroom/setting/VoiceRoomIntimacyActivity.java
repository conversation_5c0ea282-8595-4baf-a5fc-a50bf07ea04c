package com.wepie.wespy.module.voiceroom.setting;

import android.os.Bundle;
import android.view.View;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.constants.IntentConfig;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.view.SwitchView;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

public class VoiceRoomIntimacyActivity extends BaseActivity implements View.OnClickListener {
    public static final String VOICE_ROOM_INTIMACY_STATUS = "voice_room_intimacy_status";

    private BaseWpActionBar actionBar;
    private SwitchView ownerIntimacySwitch;
    private SwitchView normalIntimacySwitch;

    private int roomId = -1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.wejoy_activity_voice_room_intimacy);

        roomId = getIntent().getIntExtra(IntentConfig.INT_ROOM_ID, -1);
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo(roomId);

        initView();
        if (roomInfo != null && roomInfo.getIntimateSeatInfo() != null) {
            updateOwnerSwitch(false, roomInfo.getIntimateSeatInfo().ownerIsOpen);
            updateNormalSwitch(false, roomInfo.getIntimateSeatInfo().normalIsOpen);
        }
    }

    private void initView() {
        actionBar = findViewById(R.id.room_intimacy_action_bar);
        ownerIntimacySwitch = findViewById(R.id.room_owner_intimacy_switch);
        normalIntimacySwitch = findViewById(R.id.room_normal_intimacy_switch);

        actionBar.addTitleAndBack(ResUtil.getStr(R.string.activity_voice_room_setting_intimacy), false, new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        ownerIntimacySwitch.setOnClickListener(this);
        normalIntimacySwitch.setOnClickListener(this);
    }

    @Override
    public int supportFloatView() {
        return 0;
    }

    @Override
    public void onClick(View v) {
        if (R.id.room_owner_intimacy_switch == v.getId()) {
            if (ownerIntimacySwitch.isOpen()) {
                updateOwnerSwitch(true, false);
            } else {
                updateOwnerSwitch(true, true);
            }
        } else if (R.id.room_normal_intimacy_switch == v.getId()) {
            if (normalIntimacySwitch.isOpen()) {
                updateNormalSwitch(true, false);
            } else {
                updateNormalSwitch(true, true);
            }
        }
    }

    private void updateOwnerSwitch(boolean isRequest, boolean ownerIsOpen) {
        if (isRequest) {
            VoiceRoomPacketSender.changeRoomOwnerIntimateSeatStatus(roomId, ownerIsOpen, new LifeSeqCallback(this) {
                @Override
                public void onSuccess(RspHeadInfo head) {
                    ownerIntimacySwitch.showAnim(ownerIsOpen ? SwitchView.ON : SwitchView.OFF);
                }

                @Override
                public void onFail(RspHeadInfo head) {
                    ToastUtil.show(head.desc);
                }
            });
        } else {
            ownerIntimacySwitch.showAnim(ownerIsOpen ? SwitchView.ON : SwitchView.OFF);
        }
    }

    private void updateNormalSwitch(boolean isRequest, boolean normalIsOpen) {
        if (isRequest) {
            VoiceRoomPacketSender.changeRoomNormalIntimateSeatStatus(roomId, normalIsOpen, new LifeSeqCallback(this) {
                @Override
                public void onSuccess(RspHeadInfo head) {
                    normalIntimacySwitch.showAnim(normalIsOpen ? SwitchView.ON : SwitchView.OFF);
                }

                @Override
                public void onFail(RspHeadInfo head) {

                }
            });
        } else {
            normalIntimacySwitch.showAnim(normalIsOpen ? SwitchView.ON : SwitchView.OFF);
        }
    }
}
