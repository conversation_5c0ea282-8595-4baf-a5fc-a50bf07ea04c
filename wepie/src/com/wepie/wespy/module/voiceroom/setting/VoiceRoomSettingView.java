package com.wepie.wespy.module.voiceroom.setting;

import static com.wepie.lib.api.plugins.track.config.os.TrackScreenName.VOICE_ROOM_MORE;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.collection.ArrayMap;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.configservice.international.regoin.IDRegionUtil;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.configservice.model.voiceroom.VoiceLabelInfo;
import com.huiwan.constants.GameType;
import com.huiwan.lib.api.ApiService;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.callback.SeqDataCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.littlegame.EventDispatcher;
import com.huiwan.littlegame.event.IceGameCommandEvent;
import com.huiwan.littlegame.event.ShowGameRuleEvent;
import com.huiwan.module.webview.WebActivity;
import com.huiwan.store.PrefUtil;
import com.huiwan.user.LoginHelper;
import com.huiwan.widget.decoration.SpaceItemDecoration;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.ILifeUtil;
import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.lib.api.plugins.share.IShareApi;
import com.wepie.lib.api.plugins.share.ShareCallback;
import com.wepie.lib.api.plugins.share.ShareInfo;
import com.wepie.lib.api.plugins.share.ShareResult;
import com.wepie.lib.api.plugins.share.ShareType;
import com.wepie.lib.api.plugins.share.ShareUtil;
import com.wepie.lib.api.plugins.track.TrackApi;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackString;
import com.wepie.liblog.main.FLog;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.helper.shence.ShenceGameTypeSource;
import com.wepie.wespy.helper.shence.util.VoiceRoomUtil;
import com.wepie.wespy.model.entity.RoomInfo;
import com.wepie.wespy.model.entity.marry.MarryInfo;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.makefriend.dataservice.VoiceRoomLastDataManager;
import com.wepie.wespy.module.report.ReportBuilder;
import com.wepie.wespy.module.report.ReportConst;
import com.wepie.wespy.module.voiceroom.dataservice.RoomCallback;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceGameModel;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.invite.RoomInviteFriendCallback;
import com.wepie.wespy.module.voiceroom.main.RoomSetVoiceEvent;
import com.wepie.wespy.module.voiceroom.main.RoomViewLogic;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.ISettingPlugin;
import com.wepie.wespy.module.voiceroom.video.event.SelectLabelEvent;
import com.wepie.wespy.net.tcp.callback.marry.MarryInfo2Callback;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;
import com.wepie.wespy.net.tcp.sender.MarryPacketSender;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;
import com.wepie.wespy.utils.LogUpload;
import com.wepie.wespy.voiceroom.IVoiceRoomApi;
import com.wepie.wespy.voiceroom.UnityQualityDialogView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * 语音房设置的下拉弹窗
 *
 * <AUTHOR>
 * date 2021/01/08
 */
public class VoiceRoomSettingView extends FrameLayout implements ISettingPlugin, View.OnClickListener {
    private final static String TAG = "VoiceRoomSettingView";

    private TextView roomTitleTv;
    private TextView roomTypeTv;
    private TextView roomNumberTv;
    private View statusView;
    private View divideLine;
    private RecyclerView roomSettingRv;
    private VoiceRoomSettingAdapter adapter;
    private VoiceRoomInfo roomInfo;
    private Function0<Unit> hide;
    private ConstraintLayout settingRootLay;
    private ConstraintLayout roomInfoCl;
    private ConstraintLayout roomSettingsTitleLay;
    private ConstraintLayout roomSettingsOwnerLay;
    private LinearLayout LabelLay;
    List<SettingItem> itemList = new ArrayList<>();
    private boolean isShown = false;
    private String share_title, share_desc, share_url, share_icon;
    private VoiceRoomSettingLabelSelectView labelSelectView;

    private RecyclerView roomSettingModeRv;

    private VoiceRoomSettingModeAdapter modeAdapter;


    public VoiceRoomSettingView(@NonNull Context context) {
        super(context);
        LayoutInflater.from(context).inflate(R.layout.voice_room_setting_view, this);
        initView();
    }

    public VoiceRoomSettingView(Context context, AttributeSet attrs) {
        super(context, attrs);
        LayoutInflater.from(context).inflate(R.layout.voice_room_setting_view, this);
        initView();
    }

    public void initView() {
        setOnClickListener(v -> animOut());
        settingRootLay = findViewById(R.id.setting_root_lay);
        settingRootLay.setOnClickListener(v -> {
        });
        roomTitleTv = findViewById(R.id.room_title_tv);
        roomTypeTv = findViewById(R.id.room_type_tv);
        roomNumberTv = findViewById(R.id.room_number_tv);
        ImageView closeIv = findViewById(R.id.room_setting_close_iv);
        closeIv.setOnClickListener(v -> animOut());
        statusView = findViewById(R.id.setting_status_empty_view);
        roomSettingRv = findViewById(R.id.room_setting_rv);
        roomInfoCl = findViewById(R.id.room_info_cl);
        divideLine = findViewById(R.id.room_divide_line2);
        roomSettingsTitleLay = findViewById(R.id.room_title_setting_cl);
        roomSettingsOwnerLay = findViewById(R.id.room_setting_owner_lay);
        LabelLay = findViewById(R.id.room_setting_owner_mode_normal_label);
        roomSettingModeRv = findViewById(R.id.room_setting_mode_rv);
        initModeAdapter();
    }

    private void initRoomModeClickEvent() {
        modeAdapter.setListener((gameType, fromClick) -> {
            if (GameType.isNormalRoom(gameType)) {
                labelSelectView.refresh(gameType);
                // 如果默认标签已设置，不重复设置，避免取消选中或重复弹出切换模式的弹窗
                if (GameType.isNormalRoom(roomInfo.game_type) && labelSelectView.getLabelId() != roomInfo.labelType) {
                    labelSelectView.setLabelId(roomInfo.labelType);
                }
                LabelLay.setVisibility(VISIBLE);
            } else {
                LabelLay.setVisibility(GONE);
                ApiService.of(IVoiceRoomApi.class).checkAndChangeMode(gameType, fromClick, (integer, o) -> {
                    if (o instanceof TmpRoomPackets.TmpGameBasicInfo) {
                        changeRoomMode(gameType, integer, (TmpRoomPackets.TmpGameBasicInfo) o);
                    } else {
                        checkChangeMode(gameType, integer);
                    }
                    return null;
                });
            }
        });
    }

    @SuppressLint("SetTextI18n")
    public void updateView() {
        statusView.getLayoutParams().height = ScreenUtil.getStatusBarHeight();
        int screenWidth = ScreenUtil.getScreenWidth();
        int rvMargin = ScreenUtil.dip2px(36) - (screenWidth - ScreenUtil.dip2px(72 + 192)) / 6;
        ConstraintSet cs = new ConstraintSet();
        cs.clone(settingRootLay);
        cs.connect(roomSettingRv.getId(), ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START, 0);
        cs.connect(roomSettingRv.getId(), ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END, 0);
        cs.applyTo(settingRootLay);
        roomSettingRv.setLayoutManager(new GridLayoutManager(getContext(), 4));
        adapter = new VoiceRoomSettingAdapter(itemList, getContext());
        roomSettingRv.setAdapter(adapter);
        roomTitleTv.setText(roomInfo.name);
        if (roomInfo.isLoveHome()) {
            roomInfoCl.setVisibility(GONE);
        } else {
            roomNumberTv.setText(ResUtil.getStr(R.string.voice_room_id_num, IDRegionUtil.INSTANCE.getFinalIDStrByGameType(roomInfo.rid, roomInfo.game_type)));
            if (roomInfo.labelType > 0) {
                VoiceLabelInfo labelInfo = ConfigHelper.getInstance().getLabelById(roomInfo.labelType);
                String tag = labelInfo == null ? "" : labelInfo.getName();
                roomTypeTv.setText(tag);
            } else if (roomInfo.isWeddingRoom()) {
                roomTypeTv.setText(R.string.wedding);
            } else {
                roomTypeTv.setText("");
            }
        }
        initFunction();
        initOwnerLay();
    }

    private void initModeAdapter() {
        modeAdapter = new VoiceRoomSettingModeAdapter();
        roomSettingModeRv.setAdapter(modeAdapter);
        LinearLayoutManager lm = new LinearLayoutManager(getContext());
        lm.setOrientation(LinearLayoutManager.HORIZONTAL);
        roomSettingModeRv.setLayoutManager(lm);
        roomSettingModeRv.addItemDecoration(
                new SpaceItemDecoration(
                        new Rect(0, 0, 0, 0),
                        new Rect(ScreenUtil.dip2px(16F), 0, ScreenUtil.dip2px(16F), 0)
                )
        );
    }

    private void initOwnerLay() {
        if (roomInfo.isSelfAdminOrOwner()
                && (roomInfo.isAdvancedRoom() || roomInfo.isNormalRoom() || roomInfo.isAnchorRoom())) {
            roomSettingsTitleLay.setVisibility(GONE);
            divideLine.setVisibility(GONE);
            roomSettingsOwnerLay.setVisibility(VISIBLE);
            if (labelSelectView == null) {
                labelSelectView = new VoiceRoomSettingLabelSelectView(getContext());
                LabelLay.addView(labelSelectView);
            }

            modeAdapter.initData(roomInfo.rid);
            initRoomModeClickEvent();
            modeAdapter.setSelectedByType(roomInfo.game_type, false);
            ViewExKt.postAutoCancel(roomSettingModeRv, () -> {
                int position = modeAdapter.getPositionByType(roomInfo.game_type);
                roomSettingModeRv.smoothScrollToPosition(position == -1 ? 0 : position);
            }, 300);
        } else {
            roomSettingsTitleLay.setVisibility(VISIBLE);
            divideLine.setVisibility(VISIBLE);
            roomSettingsOwnerLay.setVisibility(GONE);
        }
    }

    private void initFunction() {
        itemList.clear();
        if (roomInfo.isLoveHome()) {
            itemList.add(getCallCPItem());
            itemList.add(getGiftSoundItem());
            itemList.add(getLoveRoomSettingItem());
            itemList.add(getExitItem());
            adapter.updateList(itemList);
            return;
        }
        if (roomInfo.isAnchorRoom() && roomInfo.isSelfOwner()) {
            //主播房房主视角
            itemList.add(getInviteFriendItem());
            itemList.add(getGiftSoundItem());
            itemList.add(getReportItem());
            itemList.add(getExitItem());
            itemList.add(getRoomSettingItem());
            itemList.add(getCallRecommendItem());
        } else if ((roomInfo.isAdvancedRoom() || roomInfo.isFamilyRoom()) && roomInfo.isSelfOwner()) {
            //高级房房主视角
            itemList.add(getInviteFriendItem());
            itemList.add(getGiftSoundItem());
            itemList.add(getReportItem());
            itemList.add(getExitItem());
            itemList.add(getRoomSettingItem());
            if (!roomInfo.isVoiceGameRoom()) {
                itemList.add(getRecommendCardItem());
            }
            itemList.add(getCallFansItem());
        } else if (roomInfo.isAdvancedOrAnchorOrFamilyRoom() && roomInfo.isFollowed()) {
            //高级房或主播房，粉丝视角
            itemList.add(getInviteFriendItem());
            itemList.add(getGiftSoundItem());
            itemList.add(getReportItem());
            itemList.add(getExitItem());
            itemList.add(getReceiveCallItem());
        } else if (roomInfo.isAdvancedOrAnchorOrFamilyRoom() && !roomInfo.isFollowed()) {
            //高级房或主播房，游客视角
            itemList.add(getInviteFriendItem());
            itemList.add(getGiftSoundItem());
            itemList.add(getReportItem());
            itemList.add(getExitItem());
        } else if (roomInfo.isWeddingRoom() && roomInfo.weddingInfo.isSelfBrideOrGroom()) {
            //婚礼房房主视角
            itemList.add(getInviteFriendItem());
            itemList.add(getGiftSoundItem());
            itemList.add(getReportItem());
            itemList.add(getExitItem());
            itemList.add(getWeddingSettingItem());
            itemList.add(getRoomDecorateItem());
            itemList.add(getOverWeddingItem());
        } else if (roomInfo.isWeddingRoom() && roomInfo.weddingInfo.isSelfHost()) {
            itemList.add(getInviteFriendItem());
            itemList.add(getGiftSoundItem());
            itemList.add(getReportItem());
            itemList.add(getExitItem());
            itemList.add(getWeddingSettingItem());
        } else if (roomInfo.isSelfOwner()) {
            //临时房房主视角
            itemList.add(getInviteFriendItem());
            itemList.add(getGiftSoundItem());
            itemList.add(getReportItem());
            itemList.add(getExitItem());
            itemList.add(getRoomSettingItem());
        } else {
            // 临时房其余视角
            itemList.add(getInviteFriendItem());
            itemList.add(getGiftSoundItem());
            itemList.add(getReportItem());
            itemList.add(getExitItem());
        }

        if (roomInfo.isAuctionRoom() && (!roomInfo.isSelfOwner() || roomInfo.isNormalRoom())) {
            itemList.add(getGameGuideItem());
        }
        //该段代码服务于汪端德的产品需求，需要管理员的设置选项在最后一项
        if (roomInfo.isSelfAdmin() && !roomInfo.isSelfOwner() && !roomInfo.isWeddingRoom()) {
            itemList.add(getRoomSettingItem());
        }
        // 观影房调节音量在最后一项
        if (roomInfo.isVideoRoom()) {
            itemList.add(getSetVolumeItem());
        }
        if (roomInfo.isMsgBarrageRoom() && !roomInfo.isAnchor() && ConfigHelper.getInstance().getDragonConfig().isEnableHdSwitch) { //观众有房间画质调节
            itemList.add(getUnityQualityItem(UnityQualityDialogView.TYPE_UNITY_DRAGON));
        }
        //直播弹幕增加礼物特效开关
        if (roomInfo.isMsgBarrageRoom()) {
            itemList.add(getGiftAnimItem());
        }
        if (roomInfo.isVoiceGameRoom()) {
            itemList.add(getCocosGameGuideItem());
            if (VoiceRoomService.getInstance().getVoiceGameViewModel().isSelfInSeat()) {
                itemList.add(getCocosGiftAnimItem());
            }
            itemList.add(getCocosSetVolumeItem());
        }
        adapter.updateList(itemList);
    }

    private SettingItem getInviteFriendItem() {
        return new SettingItem(SettingItem.INVITE_FRIEND, R.drawable.voice_setting_invite_icon, this::showInviteFriendDialog);
    }

    private SettingItem getReportItem() {
        return new SettingItem(SettingItem.REPORT_ROOM, R.drawable.voice_setting_report_icon, this::reportVoiceRoom);
    }

    private SettingItem getExitItem() {
        return new SettingItem(SettingItem.EXIT_ROOM, R.drawable.voice_setting_exit_icon, this::showExitRoomDialog);
    }

    private SettingItem getGiftSoundItem() {
        int res;
        if (PrefUtil.getInstance().getBoolean(PrefUtil.KEY_VOICE_GIFT_AUDIO_OPEN, true)) {
            res = R.drawable.voice_setting_gift_sound_open_icon;
        } else {
            res = R.drawable.voice_setting_gift_sound_close_icon;
        }
        return new SettingItem(SettingItem.GIFT_SOUND, res, this::changeLocalGiftVoice);
    }

    private SettingItem getRoomSettingItem() {
        return new SettingItem(SettingItem.ROOM_SETTING, R.drawable.voice_setting_room_setting_icon, this::gotoSetting);
    }

    private SettingItem getWeddingSettingItem() {
        return new SettingItem(SettingItem.ROOM_SETTING, R.drawable.voice_setting_room_setting_icon, this::gotoWeddingSetting);
    }

    private SettingItem getLoveRoomSettingItem() {
        return new SettingItem(SettingItem.ROOM_SETTING, R.drawable.voice_setting_room_setting_icon, this::gotoLoveRoomSetting);

    }

    private SettingItem getCallRecommendItem() {
        return new SettingItem(SettingItem.CALL_RECOMMEND, R.drawable.voice_setting_call_recommend_icon, this::inviteFans);
    }

    private SettingItem getRecommendCardItem() {
        return new SettingItem(SettingItem.RECOMMEND_CARD, R.drawable.voice_setting_recommend_card_icon, this::useRecommendCard);
    }

    private SettingItem getCallFansItem() {
        return new SettingItem(SettingItem.CALL_FANS, R.drawable.voice_setting_call_fans_icon, this::useCallFans);
    }

    private SettingItem getRoomDecorateItem() {
        return new SettingItem(SettingItem.ROOM_DECORATE, R.drawable.voice_setting_room_decorate_icon, this::changeRoomDecorate);
    }

    private SettingItem getOverWeddingItem() {
        return new SettingItem(SettingItem.OVER_WEDDING, R.drawable.voice_setting_wedding_end_icon, this::overWedding);
    }

    private SettingItem getGiftAnimItem() {
        int res;
        if (PrefUtil.getInstance().getBoolean(PrefUtil.KEY_VOICE_GIFT_ANIM_CLOSE, true)) {
            res = R.drawable.voice_setting_gift_anim_close_icon;
        } else {
            res = R.drawable.voice_setting_gift_anim_open_icon;
        }
        return new SettingItem(SettingItem.GIFT_ANIM, res, this::changeLocalGiftAnim);
    }

    private SettingItem getCocosGiftAnimItem() {
        int res;
        if (PrefUtil.getInstance().getBoolean(PrefUtil.KEY_VOICE_GAME_GIFT_ANIM_CLOSE, false)) {
            res = R.drawable.voice_setting_gift_anim_close_icon;
        } else {
            res = R.drawable.voice_setting_gift_anim_open_icon;
        }
        return new SettingItem(SettingItem.GIFT_ANIM, res, this::changeMemGiftAnim);
    }

    public SettingItem getReceiveCallItem() {
        int res = R.drawable.transparent;
        HashMap<Integer, Boolean> map = VoiceRoomService.getInstance().getRcvRecommendMap();
        Boolean open = map.get(roomInfo.rid);
        if (open != null) {
            if (open) {
                res = R.drawable.voice_setting_receive_call_icon;
            } else {
                res = R.drawable.voice_setting_refuse_call_icon;
            }
        } else {
            getMainPlugin().getRcvRecommend(new LifeSeqCallback(VoiceRoomSettingView.this) {
                @Override
                public void onSuccess(RspHeadInfo head) {
                    TmpRoomPackets.GetRecvAssembleStateRsp rsp = (TmpRoomPackets.GetRecvAssembleStateRsp) head.message;
                    map.put(roomInfo.rid, rsp.getRecv());
                    VoiceRoomService.getInstance().setRcvRecommendMap(map);
                    initFunction();
                }

                @Override
                public void onFail(RspHeadInfo head) {
                    ToastUtil.show(head.desc);
                }
            });
        }
        return new SettingItem(SettingItem.RECEIVE_CALL, res, this::receiveCall);
    }

    private SettingItem getGameGuideItem() {
        return new SettingItem(SettingItem.GAME_GUIDE, R.drawable.voice_setting_game_guide_icon, this::showGuide);
    }

    private SettingItem getCocosGameGuideItem() {
        return new SettingItem(SettingItem.GAME_GUIDE, R.drawable.voice_setting_game_guide_icon, this::showCocosGuide);
    }

    private SettingItem getCallCPItem() {
        return new SettingItem(SettingItem.CALL_CP, R.drawable.voice_setting_call_cp_icon, this::callCp);
    }

    private SettingItem getUnityQualityItem(int type) {
        int nameRes = SettingItem.UNITY_QUALITY;
        return new SettingItem(nameRes, R.drawable.voice_setting_unity_quality_icon, () -> showUnityQualityDialog(type));
    }

    private SettingItem getSetVolumeItem() {
        return new SettingItem(SettingItem.VOLUME_ADJUST, R.drawable.voice_setting_volume_icon, this::setVolume);
    }

    private SettingItem getCocosSetVolumeItem() {
        return new SettingItem(SettingItem.VOLUME_COCOS_ADJUST, R.drawable.voice_setting_volume_cocos_icon, this::setCocosVolume);
    }

    private void setVolume() {
        EventBus.getDefault().post(new RoomSetVoiceEvent());
        animOut();
        addAppClickByBtnName(TrackButtonName.VOLUME_ADJUSTMENT);
    }

    private void setCocosVolume() {
        Map<String, Object> extraData = new HashMap<>();
        if (VoiceRoomService.getInstance().getRoomInfo().isVoiceGameRoom()) {
            extraData.put("sub_game_type", VoiceRoomService.getInstance().getVoiceGameViewModel().getLittleGameInfo().getGameBasicInfo().getLittleGameType());
        }
        addAppClickByBtnName(TrackButtonName.GAME_SOUND, extraData);
        EventDispatcher.postIceCommand(IceGameCommandEvent.COMMAND_SHOW_REGULATE_VOLUME_DIALOG);
    }

    private void showInviteFriendDialog() {
        initShareInfo();
        updateShareInfoIfNeed(VoiceRoomService.getInstance().getRoomInfo().rid);
        ShareInfo shareInfo = new ShareInfo();
        shareInfo.setTitle(share_title);
        shareInfo.setContent(share_desc);
        shareInfo.setBitmapPath(share_icon);
        shareInfo.setLink(shareInfo.getLinkIntercept(share_url));

        //添加分享的打点信息
        shareInfo.screenName = TrackScreenName.SHARE_PAGE;
        shareInfo.scene = TrackString.SCENE_VOICE_ROOM;
        shareInfo.gameType = VoiceRoomService.getInstance().getRoomInfo().game_type;
        shareInfo.extTrackInfo.put("rid", VoiceRoomService.getInstance().getRid());
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
        shareInfo.setRid(String.valueOf(roomInfo.rid));
        shareInfo.addTripartiteShareType();
        shareInfo.addShareTypes(ShareType.friend, ShareType.android, ShareType.copyLink);

        RoomInviteFriendCallback roomInviteFriendCallback = new RoomInviteFriendCallback(VoiceRoomService.getInstance().getRoomInfo().rid,
                VoiceRoomService.getInstance().getRoomInfo().game_type);
        ApiService.of(IShareApi.class).showShareDialog(getContext(), shareInfo, new ShareCallback() {
            @Override
            public boolean onShare(@NonNull ShareResult data) {
                if (data.shareType == ShareType.friend) {
                    roomInviteFriendCallback.onInvite(data.target);
                }
                return true;
            }
        });
        addAppClickByBtnName(TrackButtonName.INVITE_FRIEND);
    }

    private void addAppClickByBtnName(String btnName) {
        addAppClickByBtnName(btnName, null);
    }

    private void addAppClickByBtnName(String btnName, Map<String, Object> extraData) {
        VoiceRoomInfo voiceRoomInfo = VoiceRoomService.getInstance().getRoomInfo();
        if (voiceRoomInfo == null) return;
        Map<String, Object> trackMap = new HashMap<>();
        if (extraData != null) {
            trackMap.putAll(extraData);
        }
        trackMap.put("btn_area", TrackButtonName.ROOM_FUNCTION);
        trackMap.put("rid", voiceRoomInfo.rid);
        trackMap.put("game_type", voiceRoomInfo.game_type);
        trackMap.put("room_type", voiceRoomInfo.room_type);
        trackMap.put("is_owner", voiceRoomInfo.isSelfOwner());
        TrackUtil.appClick(TrackScreenName.VOICE_ROOM_NAVIGATION, btnName, trackMap);
    }

    private void updateShareInfoIfNeed(int rid) {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo(rid);
        if (roomInfo != null && roomInfo.isWeddingRoom()) {
            PropItem item = ConfigHelper.getInstance().getPropConfig().getPropItem(roomInfo.weddingInfo.ringType);
            String name = "";
            if (item != null) {
                name = item.getRingName();
            }
            share_url += "&bride_code=" + ShareUtil.id2code(roomInfo.weddingInfo.brideUid) + "&groom_code=" + ShareUtil.id2code(roomInfo.weddingInfo.groomUid);
            share_title = ResUtil.getStr(R.string.msg_invite_wedding_tips, name);
            share_desc = ResUtil.getStr(R.string.grap_red_packets_and_send_best_wishes);
        }
    }

    private void initShareInfo() {
        int gameType = RoomInfo.GAME_TYPE_VOICE_ROOM;
        String roomName = ResUtil.getStr(R.string.game_type_voice_room);
        if (roomInfo != null) {
            if (roomInfo.isWeddingRoom()) {
                gameType = RoomInfo.GAME_TYPE_WEDDING;
                roomName = ResUtil.getStr(R.string.wedding_hall);
            } else if (roomInfo.isFamilyRoom()) {
                gameType = RoomInfo.GAME_TYPE_FAMILY_ROOM;
                roomName = ResUtil.getStr(R.string.family_voice_room);
            }
        }

        share_icon = ConfigHelper.getInstance().getShareIconUrl();
        share_url = ConfigHelper.getInstance().getMyShareUrl() + "share/room?code=" + ShareUtil.id2code(roomInfo.rid) + "&share=" + ShareUtil.id2code(LoginHelper.getLoginUid()) + "&game_type=" + roomInfo.game_type;
        share_title = ConfigHelper.getInstance().getRoomShareTitle();
        share_desc = ConfigHelper.getInstance().getRoomShareDesc();

        share_title = share_title.replace("{game_type}", roomName).replace("{rid}", IDRegionUtil.INSTANCE.getFinalIDStrByGameType(roomInfo.rid, gameType));
    }

    private void reportVoiceRoom() {
        String source = ShenceGameTypeSource.getGameTypeSource(roomInfo.game_type);
        JumpUtil.gotoReportMainActivity(getContext(), ReportBuilder.newBuilder().setTargetUid(roomInfo.owner)
                .setReportType(ReportConst.ReportTypeVoiceRoom).setSource(source).setRid(roomInfo.rid));
        animOut();
        addAppClickByBtnName(TrackButtonName.REPORT_ROOM);
    }

    /**
     * 高级房退出房间需检测在线管理员数量
     */
    private void showExitRoomDialog() {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
        if (null == roomInfo) {
            getMainPlugin().exitRoom();
            FLog.e(new Exception("showExitRoomDialog error! roomInfo is null! uid:" + LoginHelper.getLoginUid()));
            LogUpload.INSTANCE.upload();
            return;
        }
        Runnable showExitDialog = () -> {
            DialogBuild.newBuilder(getContext()).setSingleBtn(false).setTitle("")
                    .setContent(roomInfo.getExitMsg()).setCanCancel(true).setDialogCallback(() -> {
                        getMainPlugin().exitRoom();
                        VoiceRoomUtil.onVoiceRoomActivityDestroy();
                    }).show();
            addAppClickByBtnName(TrackButtonName.LEAVE_ROOM);
        };
        Boolean filter = ApiService.of(IVoiceRoomApi.class).filterShowExitRoomDialog(getContext(), roomInfo.game_type, roomInfo.rid, roomInfo.getExitMsg(), () -> {
            getMainPlugin().exitRoom();
            VoiceRoomUtil.onVoiceRoomActivityDestroy();
            addAppClickByBtnName(TrackButtonName.LEAVE_ROOM);
            return null;
        });
        if (filter == Boolean.TRUE) {
            return;
        }
        if (!roomInfo.isAdvancedOrAnchorOrFamilyRoom() || !roomInfo.isSelfAdminOrOwner()) {
            showExitDialog.run();
            return;
        }
        VoiceRoomPacketSender.checkExit(roomInfo.rid, new SeqDataCallback<>(this) {
            @Override
            public void onFail(RspHeadInfo head) {
                super.onFail(head);
                showExitDialog.run();
            }

            @Override
            public void onSuccess(TmpRoomPackets.GetOnlineAdminCountRsp rsp) {
                super.onSuccess(rsp);
                roomInfo.onlineAdministratorCount = rsp.getCount();
                showExitDialog.run();
            }
        });

    }

    private void changeLocalGiftVoice() {
        boolean open = PrefUtil.getInstance().getBoolean(PrefUtil.KEY_VOICE_GIFT_AUDIO_OPEN, true);
        PrefUtil.getInstance().setBoolean(PrefUtil.KEY_VOICE_GIFT_AUDIO_OPEN, !open);
        @StringRes int msg = open ? R.string.voice_room_gift_sound_closed : R.string.voice_room_gift_sound_opened;
        ToastUtil.show(msg);
        initFunction();
        addAppClickByBtnName(TrackButtonName.GIFT_SOUND);
    }

    private void changeLocalGiftAnim() {
        boolean isClose = PrefUtil.getInstance().getBoolean(PrefUtil.KEY_VOICE_GIFT_ANIM_CLOSE, true);
        PrefUtil.getInstance().setBoolean(PrefUtil.KEY_VOICE_GIFT_ANIM_CLOSE, !isClose);
        @StringRes int msg = !isClose ? R.string.voice_room_gift_anim_closed : R.string.voice_room_gift_anim_opened;
        ToastUtil.show(msg);
        initFunction();
        addAppClickByBtnName(TrackButtonName.GIFT_ANIMATION);
    }

    private void changeMemGiftAnim() {
        boolean isClose = PrefUtil.getInstance().toggle(PrefUtil.KEY_VOICE_GAME_GIFT_ANIM_CLOSE, false);
        @StringRes int msg = isClose ? R.string.voice_room_gift_anim_closed : R.string.voice_room_gift_anim_opened;
        ToastUtil.show(msg);
        initFunction();
        addAppClickByBtnName(TrackButtonName.GIFT_ANIMATION);
    }

    private void gotoSetting() {
        JumpUtil.gotoRoomSettingActivity(getContext(), roomInfo.rid);
        animOut();
        addAppClickByBtnName(TrackButtonName.ROOM_SETTINGS);
    }

    private void gotoLoveRoomSetting() {
        JumpUtil.gotoLoveHomeRoomSettingActivity(getContext(), roomInfo.rid);
        animOut();
    }

    private void inviteFans() {
        if (roomInfo.callFansAndRecommendTimes > 0) {
            DialogBuild.newBuilder(getContext()).setSingleBtn(false).setTitle("")
                    .setContent(ResUtil.getStr(R.string.rally_fans_and_make_room_more_popular, roomInfo.callFansAndRecommendTimes))
                    .setDialogCallback(() -> getMainPlugin().callFansAndRecommend())
                    .setCanCancel(true).show();
        } else {
            ToastUtil.show(R.string.out_of_attempts_today);
        }
    }

    private void useRecommendCard() {
        getMainPlugin().getUseRecommendTimes(new LifeSeqCallback(this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                TmpRoomPackets.GetRecommendCardCountRsp rsp = (TmpRoomPackets.GetRecommendCardCountRsp) head.message;
                roomInfo.useRecommendCarTimes = rsp.getCount();
                if (roomInfo.useRecommendCarTimes > 0) {
                    String title = ResUtil.getStr(R.string.voice_room_rec_card_use_tip_title);
                    String msg = ResUtil.getStr(R.string.voice_room_rec_card_use_tip_content, rsp.getTotalCount(), roomInfo.useRecommendCarTimes);
                    DialogBuild.newBuilder(getContext()).setSingleBtn(false).setTitle(title)
                            .setContent(msg).setCanCancel(true)
                            .setDialogCallback(() -> getMainPlugin().useRecommend())
                            .show();
                } else {
                    ToastUtil.show(R.string.voice_room_rec_card_use_out);
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private void useCallFans() {
        getMainPlugin().getCallFansTimes(new LifeSeqCallback(this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                TmpRoomPackets.GetAssembleFansCountRsp rsp = (TmpRoomPackets.GetAssembleFansCountRsp) head.message;
                roomInfo.callFansTimes = rsp.getCount();
                if (roomInfo.callFansTimes > 0) {
                    String title = ResUtil.getStr(R.string.voice_room_call_fan_tip_title);
                    String msg = ResUtil.getStr(R.string.voice_room_call_fan_tip_content, rsp.getTotalCount(), roomInfo.callFansTimes);
                    DialogBuild.newBuilder(getContext()).setSingleBtn(false).setTitle(title)
                            .setContent(msg)
                            .setCanCancel(true)
                            .setDialogCallback(() -> getMainPlugin().callFans())
                            .show();
                } else {
                    ToastUtil.show(R.string.voice_room_call_fan_use_out);
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private void changeRoomDecorate() {
        JumpUtil.gotoDressWeddingRoomActivity(getContext(), roomInfo.rid, roomInfo.weddingInfo.templateId);
        animOut();
    }

    private void gotoWeddingSetting() {
        JumpUtil.gotoWeddingRoomSettingActivity(getContext(), roomInfo.rid);
    }

    private void overWedding() {
        DialogUtil.showWeddingRoomDoubleBtnDialog(getContext(), ResUtil.getStr(R.string.wedding_end_req_tip),
                ResUtil.getStr(R.string.wedding_end_req_tip_cancel), ResUtil.getStr(R.string.wedding_end_req_tip_sure), true, new DialogBuild.DialogCallback() {
                    @Override
                    public void onClickSure() {
                    }

                    @Override
                    public void onClickCancel() {
                        MarryPacketSender.endWeddingReq(true, new LifeSeqCallback(VoiceRoomSettingView.this) {
                            @Override
                            public void onSuccess(RspHeadInfo head) {
                                ToastUtil.show(R.string.send_request);
                            }

                            @Override
                            public void onFail(RspHeadInfo head) {
                                ToastUtil.show(head.desc);
                            }
                        });
                    }
                });
    }

    private void receiveCall() {
        HashMap<Integer, Boolean> map = VoiceRoomService.getInstance().getRcvRecommendMap();
        Boolean open = map.get(roomInfo.rid);
        if (open != null) {
            getMainPlugin().setRcvRecommend(!open, new LifeSeqCallback(this) {
                @Override
                public void onSuccess(RspHeadInfo head) {
                    map.put(roomInfo.rid, !open);
                    VoiceRoomService.getInstance().setRcvRecommendMap(map);
                    initFunction();
                    @StringRes int msg = open ? R.string.voice_room_call_recv_closed : R.string.voice_room_call_recv_opened;
                    ToastUtil.show(msg);
                }

                @Override
                public void onFail(RspHeadInfo head) {
                    ToastUtil.show(head.desc);
                }
            });
        }
    }

    private void showGuide() {
        WebActivity.go(getContext(), ConfigHelper.getInstance().getConstV3Info().auctionIntroductionUrl);
    }

    private void showCocosGuide() {
        VoiceRoomService voiceRoomService = VoiceRoomService.getInstance();
        VoiceGameModel viewModel = voiceRoomService.getVoiceGameViewModel();
        Map<String, Object> extraData = new HashMap<>();
        if (voiceRoomService.getRoomInfo().isVoiceGameRoom()) {
            extraData.put("sub_game_type", viewModel.getLittleGameInfo().getGameBasicInfo().getLittleGameType());
        }
        addAppClickByBtnName(TrackButtonName.GAME_GUIDE, extraData);
        EventBus.getDefault().post(new ShowGameRuleEvent(viewModel.getLittleGameSimpleInfo()));
    }

    private void callCp() {
        if (isBothAtRoom()) {
            ToastUtil.show(R.string.voice_room_call_cp_already_in);
        } else {
            ILife life = ILifeUtil.toLife(this);
            DialogUtil.showWeddingRoomDoubleBtnDialog(getContext(), ResUtil.getStr(R.string.request_lover_enter_room),
                    ResUtil.getStr(R.string.confirm), ResUtil.getStr(R.string.cancel), true, new DialogBuild.DialogCallback() {
                        @Override
                        public void onClickSure() {
                            MarryPacketSender.getMarryInfoReq(LoginHelper.getLoginUid(), life, new MarryInfo2Callback() {
                                @Override
                                public void onSuccess(MarryInfo marryInfo2) {
                                    List<Integer> uidList = new ArrayList<>();
                                    uidList.add(marryInfo2.getMateUid());
                                    VoiceRoomPacketSender.inviteFriend(roomInfo.rid, uidList, new LifeSeqCallback(life) {
                                        @Override
                                        public void onSuccess(RspHeadInfo head) {
                                            ToastUtil.show(R.string.requested);
                                        }

                                        @Override
                                        public void onFail(RspHeadInfo head) {
                                            ToastUtil.show(head.desc);
                                        }
                                    });
                                }

                                @Override
                                public void onFail(String msg) {
                                    ToastUtil.show(msg);
                                }
                            });
                        }

                        @Override
                        public void onClickCancel() {
                        }
                    });
        }
    }

    private boolean isBothAtRoom() {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo(0);
        return roomInfo.isAllSeatSit();
    }

    @Override
    public void show(@Nullable VoiceRoomInfo roomInfo, @Nullable Function0<Unit> hide) {
        this.roomInfo = roomInfo;
        this.hide = hide;
        updateView();
        animIn();
        reportRoomSettingViewShow(roomInfo);
    }

    @Override
    public void showSettingView(@Nullable VoiceRoomInfo roomInfo) {

    }

    private void reportRoomSettingViewShow(VoiceRoomInfo roomInfo) {
        try {
            JSONObject properties = new JSONObject();
            properties.put(TrackUtil.trackValue().getScreenName(), VOICE_ROOM_MORE);
            properties.put("rid", roomInfo.rid);
            properties.put("is_owner", roomInfo.isSelfOwner());
            properties.put("is_admin", roomInfo.isSelfAdmin());
            ApiService.of(TrackApi.class).trackEvent(TrackUtil.trackValue().getAppViewScreen(), properties);
        } catch (Exception e) {
            HLog.e(TAG, "reportRoomSettingViewShow error, {}", e.getMessage());
        }
    }

    /**
     * 页面渐入动画效果
     */
    private void animIn() {
        if (isShown) {
            return;
        }
        isShown = true;
        ValueAnimator animator = new ValueAnimator();
        animator.setInterpolator(new AccelerateDecelerateInterpolator());
        animator.setFloatValues(0, 1);
        animator.setRepeatCount(0);
        animator.setDuration(250);
        animator.addUpdateListener(animation -> {
            float ratio = (float) animation.getAnimatedValue();
            updatePosition(ratio);
        });
        animator.start();
        setVisibility(VISIBLE);
    }

    private void animOut() {
        if (!isShown) {
            return;
        }
        isShown = false;
        ValueAnimator animator = new ValueAnimator();
        animator.setInterpolator(new AccelerateDecelerateInterpolator());
        animator.setFloatValues(1, 0);
        animator.setRepeatCount(0);
        animator.setDuration(250);
        animator.addUpdateListener(animation -> {
            float ratio = (float) animation.getAnimatedValue();
            updatePosition(ratio);
        });
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                setVisibility(GONE);
            }
        });
        animator.start();
        if (hide != null) {
            hide.invoke();
        }
    }

    private void updatePosition(float ratio) {
        if (ratio >= 1) {
            ratio = 1;
        } else if (ratio < 0) {
            ratio = 0;
        }
        LayoutParams layoutParams = (LayoutParams) settingRootLay.getLayoutParams();
        int height = settingRootLay.getMeasuredHeight() == 0 ? ScreenUtil.getScreenHeight() : settingRootLay.getMeasuredHeight();
        layoutParams.topMargin = (int) ((ratio - 1) * height) - ScreenUtil.dip2px(16);
        settingRootLay.setLayoutParams(layoutParams);
    }

    @Override
    public void onClick(View view) {
    }

    private void checkChangeMode(final int changeGameType, int selectId) {
        if (changeGameType == GameType.GAME_TYPE_VOICE_ROOM && roomInfo.game_type == GameType.GAME_TYPE_VOICE_ROOM) {
            animOut();
            updateRoomLabel(changeGameType, roomInfo.rid, selectId);
        } else {
            DialogBuild.newBuilder(getContext()).setSingleBtn(false).setTitle(null).setContent(R.string.voice_room_mode_change_tip)
                    .setCanCancel(true).setDialogCallback(new DialogBuild.DialogCallback() {
                        @Override
                        public void onClickSure() {
                            if (hide != null) {
                                hide.invoke();
                            }
                            changeRoomMode(changeGameType, selectId, null);
                        }

                        @Override
                        public void onClickCancel() {
                            initOwnerLay();
                        }
                    }).show();
        }
    }

    private void changeRoomMode(final int changeGameType, int selectId, TmpRoomPackets.TmpGameBasicInfo tmpGameBasicInfo) {
        VoiceRoomPacketSender.modeGameTypeReq(roomInfo.rid, changeGameType, true, tmpGameBasicInfo, new LifeSeqCallback(this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                TmpRoomPackets.TmpModGameTypeRsp modGameTypeRsp = (TmpRoomPackets.TmpModGameTypeRsp) head.message;
                if (modGameTypeRsp.getHaveOldVersionUser()) {
                    DialogBuild.newBuilder(getContext()).setSingleBtn(false).setTitle(null).setContent(modGameTypeRsp.getTips()).setCanCancel(true).setDialogCallback(() ->
                            VoiceRoomPacketSender.modeGameTypeReq(roomInfo.rid, changeGameType, false, tmpGameBasicInfo, new LifeSeqCallback(VoiceRoomSettingView.this) {
                                @Override
                                public void onSuccess(RspHeadInfo head1) {
                                    updateRoomLabel(changeGameType, roomInfo.rid, selectId);
                                    maybeTrackChangeVoiceGameMode(changeGameType, tmpGameBasicInfo);
                                    ToastUtil.show(R.string.success);
                                }

                                @Override
                                public void onFail(RspHeadInfo head1) {
                                    ToastUtil.show(head1.desc);
                                }
                            })).show();
                } else {
                    updateRoomLabel(changeGameType, roomInfo.rid, selectId);
                    maybeTrackChangeVoiceGameMode(changeGameType, tmpGameBasicInfo);
                    ToastUtil.show(R.string.success);
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private void maybeTrackChangeVoiceGameMode(int changeGameType, TmpRoomPackets.TmpGameBasicInfo basicInfo) {
        if (basicInfo == null) {
            return;
        }
        Map<String, Object> trackMap = new ArrayMap<>();
        trackMap.put("rid", roomInfo.rid);
        trackMap.put("scene", ResUtil.getStr(R.string.track_scene_switch_room_type));
        trackMap.put("game_type", changeGameType);
        trackMap.put("sub_game_type", basicInfo.getLittleGameType());
        trackMap.put("mode_type", TeamInfo.trackModeType(basicInfo.getLittleGameType(), basicInfo.getGameMode()));
        trackMap.put("bet_level", basicInfo.getBetLevel());
        GameConfig config = ConfigHelper.getInstance().getGameConfig(basicInfo.getLittleGameType());
        GameConfig.MatchInfo matchInfo = config.getLittleGameMatchInfo(basicInfo.getBetLevel(), basicInfo.getMode(), basicInfo.getGameMode(), basicInfo.getCurrencyType());
        if (matchInfo == null) {
            trackMap.put("enter_coin", -1);
        } else {
            trackMap.put("enter_coin", matchInfo.getCoin());
        }
        TrackUtil.appClick(ResUtil.getStr(R.string.track_screen_game_mode_switch_dialog), TrackButtonName.SURE, trackMap);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSelectLabel(SelectLabelEvent event) {
        HLog.d(TAG, "SelectLabelEvent event.labelId=" + event.labelId + ",roomInfo.labelType=" + roomInfo.labelType);
        if (this.isShown) {
            if (event.labelId != roomInfo.labelType || roomInfo.isVoiceGameRoom()) {
                checkChangeMode(GameType.GAME_TYPE_VOICE_ROOM, event.labelId);
            }
        }
    }

    private void updateRoomLabel(int gameType, int rid, int selectedId) {
        if (GameType.isNormalRoom(gameType)) {
            changeSelectedLabel(gameType, rid, selectedId);
        }
        addAppClickChangeRoomMode(gameType, roomInfo.rid, selectedId);
    }

    private void changeSelectedLabel(int gameType, int rid, int selectedId) {
        if (rid > 0) {
            RoomViewLogic.modRoomLabel(getContext(), rid, selectedId, new RoomCallback(this) {
                @Override
                public void onSuccess(int rid) {
                    VoiceRoomInfo voiceRoomInfo = VoiceRoomService.getInstance().getRoomInfo(rid);
                    if (voiceRoomInfo != null && voiceRoomInfo.isSelfOwner() && !voiceRoomInfo.isAdvancedOrAnchorOrFamilyRoom()) {
                        VoiceRoomLastDataManager.getInstance().setVoiceRoomLabel(selectedId, gameType);
                    }
                    removeView(getRootView());
                }

                @Override
                public void onFail(String msg) {
                }
            });
        } else {
            removeView(getRootView());
        }
    }

    public static void addAppClickChangeRoomMode(int gameType, int rid, int labelType) {
        VoiceRoomInfo voiceRoomInfo = VoiceRoomService.getInstance().getRoomInfo();
        VoiceLabelInfo labelInfo = ConfigHelper.getInstance().getLabelById(labelType);
        if (voiceRoomInfo == null || labelInfo == null) {
            HLog.d(TAG, HLog.USR, "voiceRoomInfo or labelInfo is null, labelType = " + labelInfo);
            return;
        }
        Map<String, Object> trackMap = new HashMap<>();
        trackMap.put("btn_area", TrackButtonName.CHANGE_ROOM_MODE);
        trackMap.put("rid", rid);
        trackMap.put("game_type", gameType);
        trackMap.put("label_type", labelInfo.getName());
        TrackUtil.appClick(TrackScreenName.VOICE_ROOM_NAVIGATION, "", trackMap);
    }

    private void showUnityQualityDialog(int type) {
        VoiceRoomInfo voiceRoomInfo = VoiceRoomService.getInstance().getRoomInfo();
        UnityQualityDialogView.showBottomDialog(getContext(), type, voiceRoomInfo.voiceType);
        animOut();
    }
}
