package com.wepie.wespy.module.voiceroom.setting;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.international.regoin.IDRegionUtil;
import com.huiwan.configservice.model.PropItem;
import com.huiwan.configservice.model.gift.Gift;
import com.huiwan.configservice.model.voiceroom.VoiceLabelInfo;
import com.huiwan.configservice.model.voiceroom.VoiceThemeInfo;
import com.huiwan.constants.IntentConfig;
import com.huiwan.lib.api.ApiService;
import com.huiwan.lib.api.plugins.WebApi;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LoginHelper;
import com.huiwan.widget.CustomCircleImageView;
import com.wepie.lib.api.plugins.track.TrackApi;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.wespy.R;
import com.wepie.wespy.event.SwitchCocosOnlyAllowSeatEvent;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.dialog.DialogUtil;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.helper.view.SwitchView;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.common.jump.JumpUtil;
import com.wepie.wespy.module.family.FamilyManager;
import com.wepie.wespy.module.family.main.mine.family.FamilyMainInterface;
import com.wepie.wespy.module.game.room.create.CreateRoomTaskManager;
import com.wepie.wespy.module.game.room.roomcreate.typeselect.PreviewInfo;
import com.wepie.wespy.module.settings.edituser.HeadImageHelper;
import com.wepie.wespy.module.settings.edituser.UserInfoDialogHelper;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.main.RoomTypeConfig;
import com.wepie.wespy.module.voiceroom.member.VoiceMemberSetCostDialog;
import com.wepie.wespy.module.voiceroom.roomgroup.RoomOwnerGroupActivity;
import com.wepie.wespy.net.tcp.handler.VoiceRoomHandler;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;
import com.wepie.wespy.voiceroom.IVoiceRoomApi;

import org.greenrobot.eventbus.EventBus;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class VoiceRoomSettingActivity extends BaseActivity implements IRoomSetting {

    private VoiceRoomSettingPresenter presenter;
    private RelativeLayout roomNameLay;
    private TextView roomNameTv;
    private RelativeLayout roomNoticeLay;
    private TextView roomNoticeTv;
    private SwitchView roomPublicMsgSwitch;
    private SwitchView roomPublicPhotoSwitch;
    private SwitchView roomRpSwitch;
    private SwitchView roomSecretSwitch;
    private RelativeLayout roomSecretLay;
    private ViewGroup familyMemberJoinLay;
    private SwitchView familyMemberJoinSwitch;
    private ViewGroup familyMaintainLay;
    private TextView familyMaintainCostTv;
    private TextView roomSecretTv;
    private int rid;
    private int giftId = -1;
    private String roomName = "";
    private String roomNote = "";
    private int labelType;
    private int bgId;
    private int gameType;
    private int roomType;
    private RelativeLayout roomBgLay;
    private TextView roomBgTv;
    private RelativeLayout roomIntimacyLay;
    private TextView roomIntimacyTv;
    private RelativeLayout roomTagLay;
    private TextView roomTagTv;
    private final ProgressDialogUtil progressDialogUtil = new ProgressDialogUtil();

    private RelativeLayout roomManageActivityLay;
    private SwitchView roomManageActivitySwitch;
    private TextView roomManageActivityDesc;
    private View memberJoinCostLay;
    private TextView memberCostTv;
    private ImageView memberCostCoinIv;
    private View roomAdminLay;
    private View modeLay;
    private TextView modeNameTv;
    private ViewGroup highQualityLay;
    private ViewGroup roomJoinCocosSwitchLay;
    private ViewGroup honorLay;
    private SwitchView highQualitySwitch;
    private SwitchView honorSwitch;
    private SwitchView roomJoinCocosSwitch;
    private RelativeLayout headBgLay;
    private CustomCircleImageView headBgIv;
    private RelativeLayout roomBlackLay;
    private ViewGroup roomAssociateLay;
    private TextView roomNumberTv;
    private ViewGroup roomNumberLay;
    private ViewGroup roomForbidPhotoLay;
    private ImageView ridArrowIv;
    private long lastModifyHonorTime = 0;

    private boolean firstCheckFamilyInfo = false;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_voice_room_setting);
        rid = getIntent().getIntExtra(IntentConfig.INT_ROOM_ID, -1);
        roomJoinCocosSwitchLay = findViewById(R.id.room_join_cocos_switch_lay);
        ridArrowIv = findViewById(R.id.rid_arrow);
        roomNameLay = (RelativeLayout) findViewById(R.id.room_name_lay);
        roomNameTv = (TextView) findViewById(R.id.room_name_tv);
        roomNoticeLay = (RelativeLayout) findViewById(R.id.room_notice_lay);
        roomNoticeTv = (TextView) findViewById(R.id.room_notice_tv);
        roomPublicMsgSwitch = (SwitchView) findViewById(R.id.room_public_msg_switch);
        roomSecretSwitch = (SwitchView) findViewById(R.id.room_secret_switch);
        roomSecretLay = (RelativeLayout) findViewById(R.id.room_secret_lay);
        roomSecretTv = (TextView) findViewById(R.id.room_secret_tv);
        roomRpSwitch = findViewById(R.id.room_rp_switch);
        roomManageActivityLay = findViewById(R.id.room_admin_manage_activity_lay);
        roomManageActivitySwitch = findViewById(R.id.room_admin_manage_activity_switch);
        roomManageActivityDesc = findViewById(R.id.room_admin_manage_activity_desc);
        memberJoinCostLay = findViewById(R.id.member_join_cost);
        memberCostTv = findViewById(R.id.member_cost_tv);
        memberCostCoinIv = findViewById(R.id.member_cost_coin_icon);
        roomAdminLay = findViewById(R.id.room_admin_lay);
        roomBgLay = findViewById(R.id.room_bg_lay);
        roomBgTv = findViewById(R.id.room_bg_tv);
        roomIntimacyLay = findViewById(R.id.room_intimacy_lay);
        roomIntimacyTv = findViewById(R.id.room_intimacy_tv);
        roomTagLay = findViewById(R.id.room_tag_lay);
        roomTagTv = findViewById(R.id.room_tag_tv);
        modeLay = findViewById(R.id.room_mode_lay);
        modeNameTv = findViewById(R.id.room_mode_tv);
        highQualityLay = findViewById(R.id.room_high_q_lay);
        honorLay = findViewById(R.id.room_honor_lay);
        highQualitySwitch = findViewById(R.id.room_high_q_switch);
        roomJoinCocosSwitch = findViewById(R.id.room_join_cocos_switch);
        honorSwitch = findViewById(R.id.room_honor_switch);
        familyMemberJoinLay = findViewById(R.id.room_family_lay);
        familyMemberJoinSwitch = findViewById(R.id.room_family_member_join_switch);
        familyMaintainLay = findViewById(R.id.family_maintain_tip_lay);
        familyMaintainCostTv = findViewById(R.id.family_maintain_cost_tv);
        headBgLay = findViewById(R.id.head_image_layout);
        headBgIv = findViewById(R.id.head_image);
        roomBlackLay = findViewById(R.id.room_black_lay);
        roomAssociateLay = findViewById(R.id.room_associate_lay);
        roomNumberTv = findViewById(R.id.room_number_tv);
        roomNumberLay = findViewById(R.id.room_number_lay);
        roomPublicPhotoSwitch = findViewById(R.id.room_public_photo_switch);
        roomForbidPhotoLay = findViewById(R.id.room_forbid_photo_lay);

        roomNameLay.setOnClickListener(clickListener);
        roomNoticeLay.setOnClickListener(clickListener);
        roomSecretLay.setOnClickListener(clickListener);
        roomJoinCocosSwitch.setOnClickListener(clickListener);
        roomBgLay.setOnClickListener(clickListener);
        roomIntimacyLay.setOnClickListener(clickListener);
        roomTagLay.setOnClickListener(clickListener);
        roomManageActivitySwitch.setOnClickListener(clickListener);
        memberJoinCostLay.setOnClickListener(clickListener);
        roomAdminLay.setOnClickListener(clickListener);
        modeLay.setOnClickListener(clickListener);
        headBgLay.setOnClickListener(clickListener);
        roomBlackLay.setOnClickListener(clickListener);
        roomAssociateLay.setOnClickListener(clickListener);

        roomPublicMsgSwitch.setDefaultState(SwitchView.ON);
        roomPublicMsgSwitch.setOnClickListener(clickListener);
        roomPublicPhotoSwitch.setDefaultState(SwitchView.ON);
        roomPublicPhotoSwitch.setOnClickListener(clickListener);
        honorSwitch.setOnClickListener(clickListener);
        roomRpSwitch.setDefaultState(SwitchView.OFF);
        roomRpSwitch.setOnClickListener(clickListener);
        highQualitySwitch.setDefaultState(SwitchView.OFF);
        highQualitySwitch.setOnClickListener(clickListener);
        familyMemberJoinSwitch.setOnClickListener(clickListener);
        familyMemberJoinSwitch.setDefaultState(SwitchView.OFF);

        roomSecretSwitch.setDefaultState(SwitchView.OFF);
        roomSecretSwitch.setOnClickListener(v -> {
            int state = roomSecretSwitch.getState();
            if (state == SwitchView.OFF) {
                JumpUtil.gotoRoomSecretActivity(VoiceRoomSettingActivity.this, rid, gameType);
//                //动画执行完成后设回初始状态
//                roomSecretSwitch.postDelayed(new Runnable() {
//                    @Override
//                    public void run() {
//                        roomSecretSwitch.setDefaultState(SwitchView.ON);
//                    }
//                }, 400);
            } else if (state == SwitchView.ON) {
                roomSecretLay.setVisibility(View.GONE);
                presenter.modPwd(rid, "", new LifeSeqCallback(VoiceRoomSettingActivity.this) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        roomSecretSwitch.showAnim(SwitchView.OFF);
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {

                    }
                });
            }
        });

        presenter = new VoiceRoomSettingPresenter(this, rid);
        presenter.loadData();
        VoiceRoomService.getInstance().getVoiceGameViewModel().getLittleGameInfoLiveData().observe(this, littleGameInfo -> {
            roomJoinCocosSwitch.setDefaultState(littleGameInfo.isOnlySeatAllowGame() ? SwitchView.ON : SwitchView.OFF);
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        presenter.clear();
    }

    private final View.OnClickListener clickListener = new View.OnClickListener() {
        @Override
        public void onClick(View view) {
            if (roomNameLay == view) {
                JumpUtil.gotoRoomNameEditActivity(VoiceRoomSettingActivity.this, rid, gameType, roomName);
            } else if (roomNoticeLay == view) {
                JumpUtil.gotoRoomNoticeActivity(VoiceRoomSettingActivity.this, rid, roomNote, gameType);
            } else if (roomSecretLay == view) {
                JumpUtil.gotoRoomSecretActivity(VoiceRoomSettingActivity.this, rid, gameType);
            } else if (view == roomBgLay) {
                JumpUtil.gotoVoiceRoomThemeActivity(VoiceRoomSettingActivity.this, rid, bgId);
            } else if (view == roomTagLay) {
                JumpUtil.gotoVoiceRoomTagActivity(VoiceRoomSettingActivity.this, rid, labelType, gameType);
            } else if (view == roomIntimacyLay) {
                JumpUtil.gotoRoomIntimacyActivity(VoiceRoomSettingActivity.this, rid);
            } else if (view == memberJoinCostLay) {
                VoiceMemberSetCostDialog.show(memberJoinCostLay.getContext(), rid, giftId, () -> {
                    updateMemberJoinCost();
                    return null;
                });
            } else if (view == roomAdminLay) {
                JumpUtil.checkJumpRoomMember(VoiceRoomSettingActivity.this, rid, VoiceRoomMemberActivity.ACTION_TYPE_SELECT_ADMIN);
            } else if (view == modeLay) {
                JumpUtil.gotoVoiceRoomModeEditActivity(modeLay.getContext(), rid);
            } else if (view == roomRpSwitch) {
                int state = roomRpSwitch.getState();
                if (state == SwitchView.SLIDE) return;
                final boolean needAllowRp = state == SwitchView.ON;
                presenter.modRpAllowed(needAllowRp, new LifeSeqCallback(VoiceRoomSettingActivity.this) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        roomRpSwitch.showAnim(needAllowRp ? SwitchView.OFF : SwitchView.ON);
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                });
            } else if (view == roomPublicMsgSwitch) {
                final int state = roomPublicMsgSwitch.getState();
                if (state == SwitchView.SLIDE) return;
                final int canSpeak = state == SwitchView.ON ? VoiceRoomInfo.PUBLIC_CHAT_ON : VoiceRoomInfo.PUBLIC_CHAT_OFF;
                roomPublicMsgSwitch.showAnim(canSpeak == VoiceRoomInfo.PUBLIC_CHAT_OFF ? SwitchView.ON : SwitchView.OFF);
                presenter.modPublicChat(canSpeak, new LifeSeqCallback(VoiceRoomSettingActivity.this) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        roomForbidPhotoLay.setVisibility(canSpeak == VoiceRoomInfo.PUBLIC_CHAT_ON ? View.VISIBLE : View.GONE);
                        VoiceRoomInfo info = VoiceRoomService.getInstance().getRoomInfo();
                        if (info != null && info.rid > 0) {
                            roomPublicPhotoSwitch.setDefaultState(info.forbidPhotoChat ? SwitchView.ON : SwitchView.OFF);
                        }
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        roomForbidPhotoLay.setVisibility(state == SwitchView.ON ? View.GONE : View.VISIBLE);
                        roomPublicMsgSwitch.setDefaultState(state);
                        ToastUtil.show(head.desc);
                    }
                });
            } else if (view == roomPublicPhotoSwitch) {
                final int state = roomPublicPhotoSwitch.getState();
                if (state == SwitchView.SLIDE) return;
                // 开关是 禁用图片
                final boolean preForbid = state == SwitchView.ON;
                final boolean toForbid = !preForbid;
                presenter.modPublicPhoto(toForbid, new LifeSeqCallback(VoiceRoomSettingActivity.this) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                });
            } else if (view == honorSwitch) {
                boolean isOpen = honorSwitch.getState() == SwitchView.ON;
                isOpen = !isOpen;
                modifyHonorSwitch(isOpen);
            } else if (view == highQualitySwitch) {
                if (!presenter.qualityCanChange()) {
                    ToastUtil.show(R.string.try_later);
                    return;
                }
                int state = highQualitySwitch.getState();
                if (state == SwitchView.OFF) {
                    presenter.modHighQuality(true, true, new LifeSeqCallback(VoiceRoomSettingActivity.this) {
                        @Override
                        public void onSuccess(RspHeadInfo head) {
                            if (head.message instanceof TmpRoomPackets.TmpModToneQualityRsp) {
                                TmpRoomPackets.TmpModToneQualityRsp rsp = (TmpRoomPackets.TmpModToneQualityRsp) head.message;
                                if (rsp.getHaveOldVersionUser()) {
                                    DialogBuild.newBuilder(VoiceRoomSettingActivity.this).setSingleBtn(false).setTitle(null).setContent(rsp.getTips()).setCanCancel(true).setDialogCallback(new DialogBuild.DialogCallback() {
                                        @Override
                                        public void onClickSure() {
                                            presenter.modHighQuality(true, false, new LifeSeqCallback(VoiceRoomSettingActivity.this) {
                                                @Override
                                                public void onSuccess(RspHeadInfo head) {
                                                    highQualitySwitch.showAnim(SwitchView.ON);
                                                }

                                                @Override
                                                public void onFail(RspHeadInfo head) {
                                                    ToastUtil.show(head.desc);
                                                }
                                            });
                                        }

                                        @Override
                                        public void onClickCancel() {

                                        }
                                    }).show();
                                } else {
                                    highQualitySwitch.showAnim(SwitchView.ON);
                                }
                            }
                        }

                        @Override
                        public void onFail(RspHeadInfo head) {
                            ToastUtil.show(head.desc);
                        }
                    });
                } else if (state == SwitchView.ON) {
                    presenter.modHighQuality(false, true, new LifeSeqCallback(VoiceRoomSettingActivity.this) {
                        @Override
                        public void onSuccess(RspHeadInfo head) {
                            highQualitySwitch.showAnim(SwitchView.OFF);

                        }

                        @Override
                        public void onFail(RspHeadInfo head) {
                            ToastUtil.show(head.desc);
                        }
                    });
                }
            } else if (view == familyMemberJoinSwitch) {
                if (familyMemberJoinSwitch.getState() == SwitchView.SLIDE) {
                    return;
                }
                presenter.modFamilyOnlyMemberIn(familyMemberJoinSwitch.getState() != SwitchView.ON, new LifeSeqCallback(VoiceRoomSettingActivity.this) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        familyMemberJoinSwitch.changeState();
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                });
            } else if (view == headBgLay) {
                UserInfoDialogHelper.showSelectHeadPhotoDialog(VoiceRoomSettingActivity.this, 1, true, items -> {
                    if (items == null || items.size() == 0) return;
                    String path = items.get(0).path;
                    HeadImageHelper.uploadOther(VoiceRoomSettingActivity.this, path, url -> presenter.changeRoomHeadImg(url, new LifeSeqCallback(VoiceRoomSettingActivity.this) {
                        @Override
                        public void onSuccess(RspHeadInfo head) {
                            HeadImageLoader.loadCircleHeadImage(url, headBgIv);
                        }

                        @Override
                        public void onFail(RspHeadInfo head) {
                            ToastUtil.show(head.desc);
                        }
                    }));
                });
            } else if (view == roomBlackLay) {
                JumpUtil.checkJumpRoomMember(VoiceRoomSettingActivity.this, rid, VoiceRoomMemberActivity.ACTION_TYPE_SELECT_BLACK);
            } else if (view == roomManageActivitySwitch) {
                final int state = roomManageActivitySwitch.getState();
                if (state == SwitchView.SLIDE) return;
                boolean isOpen = state == SwitchView.ON;
                isOpen = !isOpen;
                changeManageActivitySwitch(isOpen);
            } else if (view == roomAssociateLay) {
                RoomOwnerGroupActivity.start(VoiceRoomSettingActivity.this, rid);
            } else if (view == roomJoinCocosSwitch) {
                EventBus.getDefault().post(new SwitchCocosOnlyAllowSeatEvent(rid, !roomJoinCocosSwitch.isOpen(), aBoolean -> {
                    roomJoinCocosSwitch.showAnim(aBoolean ? SwitchView.ON : SwitchView.OFF);
                    return null;
                }));
            }
        }
    };

    private void modifyHonorSwitch(boolean isOpen) {
        if ((System.currentTimeMillis() - lastModifyHonorTime) <= 10 * 1000) {
            ToastUtil.show(R.string.common_operate_too_fast);
            return;
        }
        if (isOpen) {
            changeHonorSwitch(true);
        } else {
            closeHonorSwitch();
        }
    }

    private void closeHonorSwitch() {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
        if (null != roomInfo && roomInfo.hasHonorInSeat()) {
            DialogUtil.show(VoiceRoomSettingActivity.this, () -> changeHonorSwitch(false));
        } else {
            changeHonorSwitch(false);
        }
    }

    private void changeHonorSwitch(boolean isOpen) {
        VoiceRoomPacketSender.modHonorSwitch(rid, isOpen, new LifeSeqCallback(VoiceRoomSettingActivity.this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                honorSwitch.showAnim(isOpen ? SwitchView.ON : SwitchView.OFF);
                lastModifyHonorTime = System.currentTimeMillis();
                Map<String, Object> map = new HashMap<>();
                map.put("rid", rid + "");
                map.put("status", isOpen ? 1 : 0);
                ApiService.of(TrackApi.class).appClick(TrackScreenName.VOICE_SETTING_PAGE, TrackButtonName.HONOR_SEAT, map);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    private void changeManageActivitySwitch(boolean isOpen) {
        VoiceRoomPacketSender.ModAllowAdminManageActivityReq(rid, isOpen, new LifeSeqCallback(VoiceRoomSettingActivity.this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                roomManageActivitySwitch.showAnim(isOpen ? SwitchView.ON : SwitchView.OFF);
                VoiceRoomService.getInstance().getRoomInfo(rid).setAllowAdminManageActivity(isOpen);
                Map<String, Object> map = new HashMap<>();
                map.put("game_type", gameType);
                map.put("click_result", isOpen ? "on" : "off");
                map.put("rid", rid);
                map.put("room_type", VoiceRoomInfo.getTrackRoomTypeText(roomType));
                ApiService.of(TrackApi.class).appClick(TrackScreenName.VOICE_ROOM_ADMIN_MANAGE_ACTIVITY,
                        TrackButtonName.VOICE_SETTING_ALLOW_MANAGE, map);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                ToastUtil.show(head.desc);
            }
        });
    }

    /**
     * 普通房，高级房，主播房，家族语音房通用设置页
     *
     * @param roomInfo 房间信息
     */
    @SuppressLint("SetTextI18n")
    @Override
    public void update(VoiceRoomInfo roomInfo) {
        this.roomName = roomInfo.name;
        this.roomNote = roomInfo.note;
        this.gameType = roomInfo.game_type;
        this.roomType = roomInfo.room_type;
        this.bgId = roomInfo.themeId;
        this.labelType = roomInfo.labelType;
        roomNameTv.setText(roomName);
        roomNoticeTv.setText(roomNote);
        roomNoticeTv.setVisibility(roomNote.isEmpty() ? View.GONE : View.VISIBLE);
        if (labelType > 0) {
            VoiceLabelInfo labelInfo = ConfigHelper.getInstance().getLabelById(roomInfo.labelType);
            String tag = labelInfo == null ? "" : labelInfo.getName();
            roomTagTv.setText(tag);
        } else {
            roomTagTv.setText(R.string.unselected);
        }
        VoiceThemeInfo bgInfo = ConfigHelper.getInstance().getRoomThemeById(roomInfo.themeId);
        if (roomInfo.isFamilyRoom() && null == bgInfo) {
            List<VoiceThemeInfo> themeInfos = ConfigHelper.getInstance().getFamilyRoomThemes();
            for (VoiceThemeInfo i : themeInfos) {
                if (i.getId() == roomInfo.themeId) {
                    bgInfo = i;
                    break;
                }
            }
        }
        String bg = bgInfo == null ? "" : bgInfo.getName();
        if (TextUtils.isEmpty(bg)) {
            PropItem item = ConfigHelper.getInstance().getPropConfig().getPropItem(roomInfo.themeId);
            if (item != null && item.getType() == PropItem.TYPE_VOICE_THEME) {
                bg = item.getName();
            }
        }
        roomBgTv.setText(bg);

        // 亲密座位提示
        if (roomIntimacyTv != null && roomInfo.getIntimateSeatInfo() != null) {
            if (roomInfo.getIntimateSeatInfo().isOwnerIsOpen() && roomInfo.getIntimateSeatInfo().isNormalIsOpen()) {
                roomIntimacyTv.setText(R.string.intimacy_for_all);
            } else if (roomInfo.getIntimateSeatInfo().isOwnerIsOpen()) {
                roomIntimacyTv.setText(R.string.intimacy_for_owner);
            } else if (roomInfo.getIntimateSeatInfo().isNormalIsOpen()) {
                roomIntimacyTv.setText(R.string.intimacy_for_normal);
            } else {
                roomIntimacyTv.setText(R.string.disabled);
            }
        }

        int public_chat = roomInfo.public_chat;
        roomPublicMsgSwitch.showAnim(public_chat == VoiceRoomInfo.PUBLIC_CHAT_ON ? SwitchView.OFF : SwitchView.ON);
        if (public_chat == VoiceRoomInfo.PUBLIC_CHAT_OFF) {
            roomForbidPhotoLay.setVisibility(View.GONE);
        } else {
            roomForbidPhotoLay.setVisibility(View.VISIBLE);
            roomPublicPhotoSwitch.showAnim(roomInfo.forbidPhotoChat ? SwitchView.ON : SwitchView.OFF);
        }

        roomRpSwitch.showAnim(roomInfo.allowRedPacket ? SwitchView.OFF : SwitchView.ON);

        highQualitySwitch.showAnim(roomInfo.isHighQuality ? SwitchView.ON : SwitchView.OFF);

        String pwd = roomInfo.passwd;
        if (TextUtils.isEmpty(pwd)) {
            roomSecretSwitch.setDefaultState(SwitchView.OFF);
            roomSecretLay.setVisibility(View.GONE);
        } else {
            roomSecretSwitch.setDefaultState(SwitchView.ON);
            roomSecretLay.setVisibility(View.VISIBLE);
            roomSecretTv.setText(pwd);
        }

        boolean isVrCenterOpen = ConfigHelper.getInstance().getVoiceRoomConfig().getVrCenterConfig().open;
        if (isVrCenterOpen && roomInfo.isSelfOwner() && roomInfo.isAdvancedOrAnchorOrFamilyRoom()) {
            updateActivityManageSwitch(roomInfo);
            roomManageActivityLay.setVisibility(View.VISIBLE);
            roomManageActivityDesc.setVisibility(View.VISIBLE);
        } else {
            roomManageActivityLay.setVisibility(View.GONE);
            roomManageActivityDesc.setVisibility(View.GONE);
        }
        if (roomInfo.isFamilyRoom()) {
            if (firstCheckFamilyInfo) {
                firstCheckFamilyInfo = false;
                VoiceRoomService.getInstance().updateRoomFamilyInfoIfIn(roomInfo.familyId);
            }
            int role = roomInfo.familyRoleInfo.get(LoginHelper.getLoginUid());
            if (role == FamilyMainInterface.ROLE_OWNER || role == FamilyMainInterface.ROLE_SUB_OWNER) {
                roomAdminLay.setVisibility(View.VISIBLE);
            } else {
                roomAdminLay.setVisibility(View.GONE);
            }
            if (role == FamilyMainInterface.ROLE_OWNER) {
                memberJoinCostLay.setVisibility(View.VISIBLE);
            } else {
                memberJoinCostLay.setVisibility(View.GONE);
            }
        } else if (roomInfo.isSelfOwner() && (roomInfo.isAnchorRoom() || roomInfo.isAdvancedRoom())) {
            roomAdminLay.setVisibility(View.VISIBLE);
            memberJoinCostLay.setVisibility(View.VISIBLE);
        } else {
            roomAdminLay.setVisibility(View.GONE);
            memberJoinCostLay.setVisibility(View.GONE);
        }
        updateMemberJoinCost();

        if ((roomInfo.isSelfOwner() || roomInfo.isSelfAdmin()) && (roomInfo.isAnchorRoom() || roomInfo.isAdvancedOrAnchorOrFamilyRoom())) {
            roomBlackLay.setVisibility(View.VISIBLE);
        } else {
            roomBlackLay.setVisibility(View.GONE);
        }
        PreviewInfo previewInfo = ApiService.of(IVoiceRoomApi.class).getPreviewInfo(gameType);
        if (previewInfo == null) {
            modeNameTv.setText("");
        } else {
            modeNameTv.setText(previewInfo.getName());
        }

        boolean showBgLay = !(roomInfo.isCpRoom() || roomInfo.isAuctionRoom() || roomInfo.isMsgBarrageRoom() || roomInfo.isVoiceGameRoom());
        roomBgLay.setVisibility(showBgLay ? View.VISIBLE : View.GONE);

        boolean showIntimacyLay = !(roomInfo.isCpRoom() || roomInfo.isAuctionRoom() || roomInfo.isMsgBarrageRoom() || roomInfo.isVoiceGameRoom());
        roomIntimacyLay.setVisibility(showIntimacyLay ? View.VISIBLE : View.GONE);
        findViewById(R.id.group_divider_1).setVisibility((!showIntimacyLay && !roomInfo.showHonorSet()) ? View.GONE : View.VISIBLE);

        if (roomInfo.isNormalRoom() && !roomInfo.isFamilyRoom()) {
            headBgLay.setVisibility(View.GONE);
            roomNumberLay.setVisibility(View.GONE);
        }

        if (roomInfo.isFamilyRoom()) {
            familyMemberJoinLay.setVisibility(View.VISIBLE);
            familyMaintainLay.setVisibility(View.VISIBLE);
            modeLay.setVisibility(View.GONE);
            roomTagLay.setVisibility(View.GONE);
            int maintainCost = ConfigHelper.getInstance().getFamilyConfig().getFamilyVoiceRoomMaintainCostByLevel(FamilyManager.getInstance().getSelfFamilyInfo().getFamily().getLevel());
            familyMaintainCostTv.setText(String.valueOf(maintainCost));
            familyMemberJoinSwitch.setDefaultState(roomInfo.onlyFamilyMemberJoin ? SwitchView.ON : SwitchView.OFF);
        } else {
            familyMemberJoinLay.setVisibility(View.GONE);
            familyMaintainLay.setVisibility(View.GONE);
            modeLay.setVisibility(View.VISIBLE);
            roomTagLay.setVisibility(roomInfo.isVoiceGameRoom() ? View.GONE : View.VISIBLE);
        }

        boolean openHighQuality = roomInfo.isAdvancedOrAnchorOrFamilyRoom() && ConfigHelper.getInstance().getVoiceRoomConfig().isAudioProfileOpen();
        openHighQuality = openHighQuality && !roomInfo.isVideoRoom();
        highQualityLay.setVisibility(openHighQuality ? View.VISIBLE : View.GONE);
        HeadImageLoader.loadCircleHeadImage(roomInfo.headImage, headBgIv);
        roomNumberTv.setText(IDRegionUtil.INSTANCE.getFinalIDStrByGameType(roomInfo.rid, roomInfo.game_type));

        if (roomInfo.isFamilyRoom() && !roomInfo.hasSetRid) {
            roomNumberTv.setText(roomInfo.rid + getString(R.string.family_room_setting_str));
            ridArrowIv.setVisibility(View.VISIBLE);
            roomNumberLay.setOnClickListener(v -> {
                if (roomInfo.familyRoleInfo.get(LoginHelper.getLoginUid()) != FamilyMainInterface.ROLE_OWNER) {
                    ToastUtil.show(R.string.family_change_room_number_tips);
                    return;
                }
                CreateRoomTaskManager.getInstance().pushActivity(VoiceRoomSettingActivity.this);
                JumpUtil.gotoChangeFamilyRidActivity(this, RoomTypeConfig.ROOM_TYPE_FAMILY, rid);
            });
        } else if (!roomInfo.isFamilyRoom() && roomInfo.isSelfOwner()) {
            String url = ConfigHelper.getInstance().getVoiceRoomConfig().roomIdConfigUrl;
            if (TextUtils.isEmpty(url)) {
                ridArrowIv.setVisibility(View.GONE);
            } else {
                if (!roomInfo.isAnchorRoom()) {
                    roomNumberLay.setOnClickListener(v -> ApiService.of(WebApi.class).gotoWebActivity(this, url));
                    ridArrowIv.setVisibility(View.VISIBLE);
                } else {
                    ridArrowIv.setVisibility(View.GONE);
                }
            }
        } else {
            ridArrowIv.setVisibility(View.GONE);
        }

        honorLay.setVisibility(roomInfo.showHonorSet() ? View.VISIBLE : View.GONE);
        honorSwitch.setDefaultState(roomInfo.isHonorSeatOpen ? SwitchView.ON : SwitchView.OFF);
        updateAssociateVisibility((roomInfo.isAdvancedRoom() || roomInfo.isAnchorRoom()) && roomInfo.owner == LoginHelper.getLoginUid());

        if (roomInfo.isVoiceGameRoom() && (roomInfo.isSelfOwner() || roomInfo.isAdmin(LoginHelper.getLoginUid()))) {
            roomJoinCocosSwitchLay.setVisibility(View.VISIBLE);
        } else {
            roomJoinCocosSwitchLay.setVisibility(View.GONE);
        }
    }

    private void updateAssociateVisibility(boolean show) {
        if (show) {
            roomAssociateLay.setVisibility(View.VISIBLE);
        } else {
            roomAssociateLay.setVisibility(View.GONE);
        }
    }

    private void updateMemberJoinCost() {
        if (memberJoinCostLay.getVisibility() == View.VISIBLE) {
            VoiceRoomPacketSender.getJoinMemberGift(rid, new LifeSeqCallback(this) {
                @Override
                public void onSuccess(RspHeadInfo head) {
                    TmpRoomPackets.GetJoinMemberGroupGiftResp rsp = (TmpRoomPackets.GetJoinMemberGroupGiftResp) head.message;
                    setMemberCostTv(rsp.getJoinMemberGift());
                }

                @Override
                public void onFail(RspHeadInfo head) {
                    ToastUtil.show(head.desc);
                }
            });

        }
    }

    private void setMemberCostTv(int giftId) {
        this.giftId = giftId;
        // 返回-1表示免费
        if (giftId == -1) {
            memberCostTv.setText(R.string.free);
            memberCostCoinIv.setVisibility(View.GONE);
        } else {
            Gift gift = ConfigHelper.getInstance().getGiftConfig().getGift(giftId);
            if (gift != null) {
                memberCostTv.setText(String.valueOf(gift.getPrice()));
                memberCostCoinIv.setVisibility(View.VISIBLE);
            }
        }
    }

    private void updateActivityManageSwitch(VoiceRoomInfo roomInfo) {
        boolean switchState = roomInfo.isAllowAdminManageActivity();
        roomManageActivitySwitch.setDefaultState(switchState ? SwitchView.ON : SwitchView.OFF);
    }

    @Override
    public void hideProgress() {
        progressDialogUtil.hideLoading();
    }

    @Override
    public void finishActivity() {
        finish();
        VoiceRoomHandler.exitVoiceRoom(rid);
    }

    @Override
    public int supportFloatView() {
        return 0;
    }

}
