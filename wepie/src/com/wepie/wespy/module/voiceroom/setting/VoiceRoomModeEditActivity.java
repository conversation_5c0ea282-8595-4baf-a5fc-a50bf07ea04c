package com.wepie.wespy.module.voiceroom.setting;

import android.app.Activity;
import android.os.Bundle;

import androidx.collection.ArrayMap;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.GameConfig;
import com.huiwan.constants.IntentConfig;
import com.huiwan.lib.api.ApiService;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wejoy.littlegame.LittleGameSimpleInfo;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.model.entity.match.TeamInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.game.room.roomcreate.typeselect.PreviewInfo;
import com.wepie.wespy.module.game.room.roomcreate.typeselect.VoiceRoomPreviewSelectView;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.net.tcp.packet.TmpRoomPackets;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;
import com.wepie.wespy.voiceroom.IVoiceRoomApi;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by bigwen on 2019/4/26.
 */
public class VoiceRoomModeEditActivity extends BaseActivity {

    private int rid;
    private Activity mContext;
    private VoiceRoomPreviewSelectView selectView;
    int mGameType;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_voice_room_mode_exit);
        mContext = this;
        rid = getIntent().getIntExtra(IntentConfig.INT_ROOM_ID, 0);
        mGameType = VoiceRoomService.getInstance().getRoomInfo(rid).game_type;
        selectView = findViewById(R.id.game_type_view);
        selectView.setSource(VoiceRoomPreviewSelectView.SOURCE_FROM_MODE);
        BaseWpActionBar actionBar = findViewById(R.id.action_bar);
        actionBar.addTitleRightTextWithBack(R.string.voice_room_mode, R.string.save,
                v -> finish(),
                v -> checkChangeMode(selectView.getSelectedId(), selectView.getSelectedGameMode()));

        initList();
    }

    private void initList() {
        List<PreviewInfo> list = ApiService.of(IVoiceRoomApi.class).getAllPreviewInfoList(rid);
        selectView.updateList(list);
        selectView.selectId(mGameType);
    }

    private void checkChangeMode(final int changeGameType, TmpRoomPackets.TmpGameBasicInfo selectedGameMode) {
        TmpRoomPackets.TmpGameBasicInfo useBasicInfo = checkBasicGameInfo(selectedGameMode);

        DialogBuild.newBuilder(this).setSingleBtn(false).setTitle(null).setContent(R.string.voice_room_mode_change_tip).setCanCancel(true).setDialogCallback(() ->
                VoiceRoomPacketSender.modeGameTypeReq(rid, changeGameType, true, useBasicInfo, new LifeSeqCallback(this) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        TmpRoomPackets.TmpModGameTypeRsp modGameTypeRsp = (TmpRoomPackets.TmpModGameTypeRsp) head.message;
                        if (modGameTypeRsp.getHaveOldVersionUser()) {
                            DialogBuild.newBuilder(mContext).setSingleBtn(false).setTitle(null).setContent(modGameTypeRsp.getTips()).setCanCancel(true).setDialogCallback(() ->
                                    VoiceRoomPacketSender.modeGameTypeReq(rid, changeGameType, false, useBasicInfo, new LifeSeqCallback(VoiceRoomModeEditActivity.this) {
                                        @Override
                                        public void onSuccess(RspHeadInfo head1) {
                                            ToastUtil.show(R.string.success);
                                            addAppClickWhenChangeRoomModeSuccess();
                                            maybeTrackChangeVoiceGameMode(changeGameType, useBasicInfo);
                                            finish();
                                        }

                                        @Override
                                        public void onFail(RspHeadInfo head1) {
                                            ToastUtil.show(head1.desc);
                                        }
                                    })).show();
                        } else {
                            ToastUtil.show(R.string.success);
                            maybeTrackChangeVoiceGameMode(changeGameType, useBasicInfo);
                            finish();
                        }
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                })).show();
    }

    private void addAppClickWhenChangeRoomModeSuccess() {
        Map<String, Object> trackMap = new HashMap<>();
        trackMap.put("btn_area", TrackButtonName.ROOM_MODE);
        trackMap.put("rid", rid);
        trackMap.put("game_type", VoiceRoomService.getInstance().getRoomInfo(rid).game_type);
        trackMap.put("label_type", VoiceRoomService.getInstance().getRoomInfo(rid).labelType);
        TrackUtil.appClick(TrackScreenName.VOICE_ROOM_NAVIGATION, "", trackMap);
    }

    private void maybeTrackChangeVoiceGameMode(int changeGameType, TmpRoomPackets.TmpGameBasicInfo basicInfo) {
        if (basicInfo == null) {
            return;
        }
        Map<String, Object> trackMap = new ArrayMap<>();
        trackMap.put("rid", rid);
        trackMap.put("scene", ResUtil.getStr(R.string.track_scene_setting_page));
        trackMap.put("game_type", changeGameType);
        trackMap.put("sub_game_type", basicInfo.getLittleGameType());
        trackMap.put("mode_type", TeamInfo.trackModeType(basicInfo.getLittleGameType(), basicInfo.getGameMode()));
        trackMap.put("bet_level", basicInfo.getBetLevel());
        GameConfig config = ConfigHelper.getInstance().getGameConfig(basicInfo.getLittleGameType());
        GameConfig.MatchInfo matchInfo = config.getLittleGameMatchInfo(basicInfo.getBetLevel(), basicInfo.getMode(), basicInfo.getGameMode(), basicInfo.getCurrencyType());
        if (matchInfo == null) {
            trackMap.put("enter_coin", -1);
        } else {
            trackMap.put("enter_coin", matchInfo.getCoin());
        }
        TrackUtil.appClick(ResUtil.getStr(R.string.track_screen_game_mode_switch_dialog), TrackButtonName.SURE, trackMap);
    }

    private TmpRoomPackets.TmpGameBasicInfo checkBasicGameInfo(TmpRoomPackets.TmpGameBasicInfo selectedGameMode) {
        if (selectedGameMode.getLittleGameType() > 0) {
            return selectedGameMode;
        } else {
            VoiceRoomInfo info = VoiceRoomService.getInstance().getRoomInfo();
            if (info.isVoiceGameRoom()) {
                LittleGameSimpleInfo littleGameInfo = VoiceRoomService.getInstance().getVoiceGameViewModel().getLittleGameSimpleInfo();
                return TmpRoomPackets.TmpGameBasicInfo.newBuilder()
                        .setMode(littleGameInfo.getMode())
                        .setGameMode(littleGameInfo.getGameMode())
                        .setBetLevel(littleGameInfo.getBetLevel())
                        .setCurrencyType(littleGameInfo.getCurrencyType())
                        .setLittleGameType(littleGameInfo.getGameType())
                        .build();
            } else {
                return null;
            }
        }
    }

    @Override
    public int supportFloatView() {
        return 0;
    }
}
