package com.wepie.wespy.module.voiceroom.setting;

import android.text.TextUtils;

import com.huiwan.base.util.ToastUtil;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.callback.LifeSeqCallbackProxy;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.ExitRoomEvent;
import com.wepie.wespy.model.event.RoomInfoUpdateEvent;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.tcp.sender.VoiceRoomPacketSender;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Created by geeksammao on 26/10/2017.
 */

public class VoiceRoomSettingPresenter {
    private static final long MIN_CHANGE_INTERVAL = 3000;
    private IRoomSetting mInterface;
    private int rid;
    private boolean finished = false;

    public VoiceRoomSettingPresenter(IRoomSetting mInterface, int rid) {
        this.mInterface = mInterface;
        this.rid = rid;
        EventBus.getDefault().register(this);
    }

    public void clear() {
        EventBus.getDefault().unregister(this);
    }

    public void loadData() {
        updateRoomInfo();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRoomInfoUpdate(RoomInfoUpdateEvent event) {
        updateRoomInfo();
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onExitRoom(ExitRoomEvent event) {
        if (!finished) {
            finished = true;
            mInterface.finishActivity();
        }
    }

    private void updateRoomInfo() {
        mInterface.update(VoiceRoomService.getInstance().getRoomInfo(rid));
    }

    public void modPublicChat(final int publicChat, final LifeSeqCallback callback) {
        VoiceRoomPacketSender.modPublicChat(rid, publicChat, callback);
    }

    public void modPublicPhoto(boolean forbidPhoto, final LifeSeqCallback callback) {
        VoiceRoomPacketSender.modPublicPhoto(rid, forbidPhoto, callback);
    }

    boolean qualityCanChange() {
        return System.currentTimeMillis() - VoiceRoomService.getInstance().getRoomInfo(rid).lastQualityChangeTime > MIN_CHANGE_INTERVAL;
    }

    void modHighQuality(boolean open, boolean check, LifeSeqCallback callback) {
        VoiceRoomPacketSender.modHighQuality(rid, open, check, callback);
    }

    public void modPwd(final int rid, final String pwd, final LifeSeqCallback callback) {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo(rid);
        if (TextUtils.isEmpty(roomInfo.passwd)) return;

        final String last_pwd = roomInfo.passwd;
        VoiceRoomService.getInstance().modPwd(rid, pwd);
        VoiceRoomPacketSender.modPwd(rid, pwd, false, new LifeSeqCallbackProxy(callback) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                super.onSuccess(head);
                EventDispatcher.postRoomInfoUpdateEvent(RoomInfoUpdateEvent.F_ROOM_SETTINGS);
            }

            @Override
            public void onFail(RspHeadInfo head) {
                super.onFail(head);
                ToastUtil.show(head.desc);
                VoiceRoomService.getInstance().modPwd(rid, last_pwd);
            }
        });
    }

    public void modRpAllowed(boolean allowRp, final LifeSeqCallback callback) {
        VoiceRoomPacketSender.setRedPacketEnable(rid, allowRp, callback);
    }

    void modFamilyOnlyMemberIn(boolean onlyMemberIn, final LifeSeqCallback callback) {
        VoiceRoomPacketSender.setOnlyFamilyMemberIn(rid, onlyMemberIn, callback);
    }

    public void changeRoomHeadImg(String url, final LifeSeqCallback callback) {
        VoiceRoomPacketSender.setHeadImageReq(rid, url, callback);
    }

}
