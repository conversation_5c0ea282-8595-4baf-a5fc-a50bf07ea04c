package com.wepie.wespy.module.voiceroom.setting;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.configservice.model.voiceroom.VoiceLabelInfo;
import com.huiwan.lib.api.ApiService;
import com.wepie.wespy.R;
import com.wepie.wespy.module.makefriend.label.LabelRecycleDivider;
import com.wepie.wespy.module.makefriend.label.LabelSelectAdapter;
import com.wepie.wespy.voiceroom.IVoiceRoomApi;

import java.util.ArrayList;
import java.util.List;

public class VoiceRoomSettingLabelSelectView extends LinearLayout {
    private Context mContext;
    private LabelSelectAdapter adapter;
    private boolean canScroll = true;

    public VoiceRoomSettingLabelSelectView(Context context) {
        super(context);
        mContext = context;
        init();
    }

    public VoiceRoomSettingLabelSelectView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
        handlerAttrs(attrs);
    }

    private void handlerAttrs(AttributeSet attrs) {
        TypedArray typedArray = mContext.obtainStyledAttributes(attrs, R.styleable.LabelSelect);
        canScroll = typedArray.getBoolean(R.styleable.LabelSelect_can_scroll, true);
        typedArray.recycle();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.voice_room_setting_label_select_view, this);
        RecyclerView recyclerView = findViewById(R.id.room_label_list_view);
        adapter = new LabelSelectAdapter(true);
        recyclerView.setLayoutManager(new GridLayoutManager(mContext, 4) {
            @Override
            public boolean canScrollVertically() {
                return super.canScrollVertically() && canScroll;
            }
        });
        recyclerView.setAdapter(adapter);
        recyclerView.addItemDecoration(new LabelRecycleDivider(ScreenUtil.dip2px(8), ScreenUtil.dip2px(12),
                0, 0));
    }

    public void setLabelId(int tagId) {
        adapter.setSelected(tagId);
    }

    public int getLabelId() {
        return adapter.getSelectedId();
    }

    public void refresh(int gameType) {
        List<VoiceLabelInfo> allLabelInfoList = ApiService.of(IVoiceRoomApi.class).getLabelList(gameType);
        List<VoiceLabelInfo> labelInfoList = new ArrayList<>(allLabelInfoList.size());
        int size = allLabelInfoList.size();
        for (int i = 0; i < size; i++) {
            VoiceLabelInfo labelInfo = allLabelInfoList.get(i);
            if (!labelInfo.isSpecialLabel() && labelInfo.isShow()) {
                labelInfoList.add(labelInfo);
            }
        }
        adapter.refresh(labelInfoList);
    }
}
