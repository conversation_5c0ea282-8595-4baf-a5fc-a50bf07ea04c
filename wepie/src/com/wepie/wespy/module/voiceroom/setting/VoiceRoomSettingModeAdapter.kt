package com.wepie.wespy.module.voiceroom.setting

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.huiwan.base.ktx.dp
import com.huiwan.base.str.ResUtil
import com.huiwan.base.util.ScreenUtil
import com.huiwan.lib.api.ApiService
import com.huiwan.widget.inflate
import com.wepie.wespy.R
import com.wepie.wespy.voiceroom.IVoiceRoomApi
import com.wepie.wespy.voiceroom.RoomModeData

class VoiceRoomSettingModeAdapter : RecyclerView.Adapter<RoomModeHolder>() {
    private val data: MutableList<RoomModeData> = ArrayList()
    private var selectedPosition = -1
    private lateinit var mListener: Listener

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RoomModeHolder {
        return RoomModeHolder(parent.inflate(R.layout.voice_room_setting_mode_item))
    }

    override fun getItemCount(): Int {
        return data.size
    }

    override fun onBindViewHolder(holder: RoomModeHolder, position: Int) {
        val roomModeData = data[position]
        val layoutParams = holder.itemView.layoutParams
        layoutParams.width = if (itemCount > 4) {
            // 大于4个房间模式时，要求恰好显示4个半房间模式图标
            ((ScreenUtil.getScreenWidth() - 16.dp) / 4.5F).toInt()
        } else {
            ((ScreenUtil.getScreenWidth() - 32.dp) / 4F).toInt()
        }
        holder.itemView.layoutParams = layoutParams
        holder.bind(
            roomModeData.modeNameId,
            roomModeData.modeIconId,
            roomModeData.gameType,
            selectedPosition == position
        )
    }

    fun getPositionByType(gameType: Int) = data.indexOfFirst { it.gameType == gameType }

    fun refresh(list: List<RoomModeData>) {
        if (data.isNotEmpty()) {
            data.clear()
        }
        data.addAll(list)
        notifyItemRangeChanged(0, data.size)
    }

    fun initData(rid: Int) {
        val list = ApiService.of(IVoiceRoomApi::class.java).getAllSupportModeList(rid)
        refresh(list)
    }

    fun setSelectedByType(gameType: Int, isClickEvent: Boolean) {
        var position = selectedPosition
        for (i in data.indices) {
            if (data[i].gameType == gameType) {
                position = i
            }
        }
        selectedPosition = position
        mListener.onClick(gameType, isClickEvent)
    }

    fun setListener(listener: Listener) {
        mListener = listener
    }

    interface Listener {
        fun onClick(gameType: Int, isClickEvent: Boolean)
    }
}

class RoomModeHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
    var root: LinearLayout = itemView.findViewById(R.id.room_setting_mode_item)
    private var modeIcon: ImageView = itemView.findViewById(R.id.room_setting_mode_icon)
    private var modeText: TextView = itemView.findViewById(R.id.room_setting_mode_text)

    fun bind(
        strRes: Int,
        iconRes: Int,
        gameType: Int,
        isSelected: Boolean,
    ) {
        modeText.text = ResUtil.getStr(strRes)
        modeIcon.setImageResource(iconRes)
        root.setOnClickListener {
            val adapter = bindingAdapter
            if (adapter is VoiceRoomSettingModeAdapter) {
                adapter.setSelectedByType(gameType, true)
            }
        }
        root.isSelected = isSelected
    }
}