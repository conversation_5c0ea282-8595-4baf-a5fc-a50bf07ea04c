package com.wepie.wespy.module.voiceroom.video;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;

import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.wespy.R;

public class ShowChangeLayout extends RelativeLayout {
    private static final String TAG = "ShowChangeLayout";
    private ImageView iv_center;
    private ProgressBar pb;
    private HideRunnable mHideRunnable;
    private int duration = 1000;

    public ShowChangeLayout(Context context) {
        super(context);
        init(context);
    }

    public ShowChangeLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.show_change_layout, this);
        iv_center = findViewById(R.id.iv_center);
        pb = findViewById(R.id.pb);

        mHideRunnable = new HideRunnable();
        ShowChangeLayout.this.setVisibility(GONE);
    }

    public void show() {
        setVisibility(VISIBLE);
        if (mHideRunnable != null) {
            removeCallbacks(mHideRunnable);
            ViewExKt.postAutoCancel(this, mHideRunnable, duration);
        }
    }

    public void setProgress(int progress) {
        pb.setProgress(progress);
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public void setImageResource(int resource) {
        iv_center.setImageResource(resource);
    }

    private class HideRunnable implements Runnable {
        @Override
        public void run() {
            ShowChangeLayout.this.setVisibility(GONE);
        }
    }
}
