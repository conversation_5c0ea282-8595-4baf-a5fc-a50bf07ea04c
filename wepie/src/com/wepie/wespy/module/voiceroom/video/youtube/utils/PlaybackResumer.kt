package com.wepie.wespy.module.voiceroom.video.youtube.utils

import com.wepie.wespy.module.voiceroom.video.youtube.PlayerConstants
import com.wepie.wespy.module.voiceroom.video.youtube.YoutubePlayer
import com.wepie.wespy.module.voiceroom.video.youtube.listeners.AbstractYoutubePlayerListener

/**
 * Class responsible for resuming the playback state in case of network problems.
 * eg: player is playing -> network goes out -> player stops -> network comes back -> player resumes playback automatically.
 */
internal class PlaybackResumer : AbstractYoutubePlayerListener() {

    private var canLoad = false
    private var isPlaying = false
    private var error: PlayerConstants.PlayerError? = null

    private var currentVideoId: String? = null
    private var currentSecond: Float = 0f

    fun resume(youTubePlayer: YoutubePlayer) {
        currentVideoId?.let { videoId ->
            if (isPlaying && error == PlayerConstants.PlayerError.HTML_5_PLAYER)
                youTubePlayer.loadOrCueVideo(canLoad, videoId, currentSecond)
            else if (!isPlaying && error == PlayerConstants.PlayerError.HTML_5_PLAYER)
                youTubePlayer.cueVideo(videoId, currentSecond)
        }

        error = null
    }

    override fun onStateChange(youTubePlayer: YoutubePlayer, state: PlayerConstants.PlayerState) {
        when (state) {
            PlayerConstants.PlayerState.ENDED -> {
                isPlaying = false
                return
            }
            PlayerConstants.PlayerState.PAUSED -> {
                isPlaying = false
                return
            }
            PlayerConstants.PlayerState.PLAYING -> {
                isPlaying = true
                return
            }
            else -> { }
        }
    }

    override fun onError(youTubePlayer: YoutubePlayer, error: PlayerConstants.PlayerError) {
        if (error == PlayerConstants.PlayerError.HTML_5_PLAYER)
            this.error = error
    }

    override fun onCurrentSecond(youTubePlayer: YoutubePlayer, second: Float) {
        currentSecond = second
    }

    override fun onVideoId(youTubePlayer: YoutubePlayer, videoId: String) {
        currentVideoId = videoId
    }

    fun onLifecycleResume() {
        canLoad = true
    }

    fun onLifecycleStop() {
        canLoad = false
    }
}
