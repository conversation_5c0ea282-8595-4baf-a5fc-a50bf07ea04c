package com.wepie.wespy.module.voiceroom.video;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.widget.RelativeLayout;

public class VideoGestureRelativeLay extends RelativeLayout {
    private static final String TAG = "VideoGestureRelativeLay";
    private GestureDetector mGestureDetector;
    private VideoGestureListener mVideoGestureListener;
    private boolean hasFF_REW = false;

    private static final int NONE = 0, VOLUME = 1, BRIGHTNESS = 2, FF_REW = 3;
    int mScrollMode = NONE;

    public VideoGestureRelativeLay(Context context) {
        super(context);
        init(context);
    }

    public VideoGestureRelativeLay(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    private void init(Context context) {
        VideoPlayerOnGestureListener mOnGestureListener = new VideoPlayerOnGestureListener();
        mGestureDetector = new GestureDetector(context, mOnGestureListener);
        mGestureDetector.setIsLongpressEnabled(false);
        setOnTouchListener(new OnTouchListener() {
            @SuppressLint("ClickableViewAccessibility")
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_UP) {
                    if (hasFF_REW) {
                        if (mVideoGestureListener != null) {
                            mVideoGestureListener.onEndFF_REW(event);
                        }
                        hasFF_REW = false;
                    }
                }
                //监听触摸事件
                return mGestureDetector.onTouchEvent(event);
            }
        });
    }

    public class VideoPlayerOnGestureListener extends GestureDetector.SimpleOnGestureListener {

        public VideoPlayerOnGestureListener() {
        }

        @Override
        public boolean onDown(MotionEvent e) {
            hasFF_REW = false;
            mScrollMode = NONE;
            if (mVideoGestureListener != null) {
                mVideoGestureListener.onDown(e);
            }
            return true;
        }

        @Override
        public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
            //横向偏移检测，让快进快退不那么敏感
            int offsetX = 1;
            switch (mScrollMode) {
                case NONE:
                    //offset是让快进快退不要那么敏感的值
                    if (Math.abs(distanceX) - Math.abs(distanceY) > offsetX) {
                        mScrollMode = FF_REW;
                    } else {
                        if (e1.getX() < (float)getWidth() / 2) {
                            mScrollMode = BRIGHTNESS;
                        } else {
                            mScrollMode = VOLUME;
                        }
                    }
                    break;
                case VOLUME:
                    if (mVideoGestureListener != null) {
                        mVideoGestureListener.onVolumeGesture(e1, e2, distanceX, distanceY);
                    }
                    break;
                case BRIGHTNESS:
                    if (mVideoGestureListener != null) {
                        mVideoGestureListener.onBrightnessGesture(e1, e2, distanceX, distanceY);
                    }
                    break;
                case FF_REW:
                    if (mVideoGestureListener != null) {
                        mVideoGestureListener.onFF_REWGesture(e1, e2, distanceX, distanceY);
                    }
                    hasFF_REW = true;
                    break;
            }
            return true;
        }


        @Override
        public boolean onContextClick(MotionEvent e) {
            return true;
        }

        @Override
        public boolean onDoubleTap(MotionEvent e) {
            if (mVideoGestureListener != null) {
                mVideoGestureListener.onDoubleTapGesture(e);
            }
            return super.onDoubleTap(e);
        }

        @Override
        public void onLongPress(MotionEvent e) {
            super.onLongPress(e);
        }

        @Override
        public boolean onDoubleTapEvent(MotionEvent e) {
            return super.onDoubleTapEvent(e);
        }

        @Override
        public boolean onSingleTapUp(MotionEvent e) {
            return super.onSingleTapUp(e);
        }

        @Override
        public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
            return super.onFling(e1, e2, velocityX, velocityY);
        }

        @Override
        public void onShowPress(MotionEvent e) {
            super.onShowPress(e);
        }

        @Override
        public boolean onSingleTapConfirmed(MotionEvent e) {
            if (mVideoGestureListener != null) {
                mVideoGestureListener.onSingleTapGesture(e);
            }
            return super.onSingleTapConfirmed(e);
        }
    }

    public void setVideoGestureListener(VideoGestureListener videoGestureListener) {
        mVideoGestureListener = videoGestureListener;
    }

    /**
     * 用于提供给外部实现的视频手势处理接口
     */
    public interface VideoGestureListener {
        //亮度手势，手指在Layout左半部上下滑动时候调用
        void onBrightnessGesture(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY);

        //音量手势，手指在Layout右半部上下滑动时候调用
        void onVolumeGesture(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY);

        //快进快退手势，手指在Layout左右滑动的时候调用
        void onFF_REWGesture(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY);

        //单击手势，确认是单击的时候调用
        void onSingleTapGesture(MotionEvent e);

        //双击手势，确认是双击的时候调用
        void onDoubleTapGesture(MotionEvent e);

        //按下手势，第一根手指按下时候调用
        void onDown(MotionEvent e);

        //快进快退执行后的松开时候调用
        void onEndFF_REW(MotionEvent e);
    }
}
