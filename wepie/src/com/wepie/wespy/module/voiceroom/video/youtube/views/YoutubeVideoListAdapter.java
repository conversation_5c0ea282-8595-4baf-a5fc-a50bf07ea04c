package com.wepie.wespy.module.voiceroom.video.youtube.views;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LifeUserSimpleInfoCallback;
import com.huiwan.user.UserService;
import com.huiwan.user.entity.UserSimpleInfo;
import com.huiwan.widget.CustomCircleImageView;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogBuild;
import com.wepie.wespy.helper.imageLoader.HeadImageLoader;
import com.wepie.wespy.model.entity.voiceroom.VideoInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.video.youtube.ui.utils.TimeUtilities;
import com.wepie.wespy.module.voiceroom.video.youtube.utils.VideoRoomUtils;
import com.wepie.wespy.net.tcp.packet.VideoRoomPackets;
import com.wepie.wespy.net.tcp.sender.VideoRoomPacketSender;
import com.wepie.wespy.utils.ViewOnClickListenerUtils;

import java.util.ArrayList;
import java.util.List;

public class YoutubeVideoListAdapter extends RecyclerView.Adapter<YoutubeVideoListAdapter.YoutubeVideoListViewHolder> {
    private final Context mContext;
    private List<VideoInfo> videoList = new ArrayList<>();
    private int curSeqId;
    private long curVideoPosition;
    private VideoInfo curVideoInfo;

    public YoutubeVideoListAdapter(Context context) {
        this.mContext = context;
    }

    public YoutubeVideoListAdapter(Context context, List<VideoInfo> videoList) {
        this.mContext = context;
        this.videoList = videoList;
    }

    public void update(List<VideoInfo> videoList) {
        this.videoList.clear();
        this.videoList.addAll(videoList);
        notifyDataSetChanged();
    }

    public void update(int curSeqId, long curVideoPosition, VideoInfo curVideoInfo) {
        this.curSeqId = curSeqId;
        this.curVideoPosition = curVideoPosition;
        this.curVideoInfo = curVideoInfo;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public YoutubeVideoListViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.video_room_youtube_video_list_item, parent, false);
        YoutubeVideoListViewHolder youtubeVideoListViewHolder = new YoutubeVideoListViewHolder(view);
        return youtubeVideoListViewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull YoutubeVideoListViewHolder holder, @SuppressLint("RecyclerView") int position) {
        VideoInfo videoInfo = videoList.get(position);
        UserService.get().getCacheSimpleUser(videoInfo.append_uid, new LifeUserSimpleInfoCallback(holder.itemView) {
            @Override
            public void onUserInfoSuccess(UserSimpleInfo simpleInfo) {
                holder.videoAdderName.setText(simpleInfo.getNickname());
                HeadImageLoader.loadCircleHeadImage(simpleInfo.headimgurl,
                        holder.videoAdderIcon);
            }

            @Override
            public void onUserInfoFailed(String description) {

            }
        });
        HeadImageLoader.loadHeadImage(videoInfo.head_image, 8, holder.videoScreenshot);
        holder.videoTitle.setText(videoInfo.title);
        if (VideoRoomUtils.isHavePermissionDeleteVideo(videoInfo.append_uid)) {
            holder.videoDeleteIv.setVisibility(View.VISIBLE);
        }

        holder.videoDeleteIv.setOnClickListener(new ViewOnClickListenerUtils() {
            @Override
            protected void onProhibitRepeatClick(View v) {
                int seqId = videoInfo.seq_id;
                DialogBuild.newBuilder(mContext)
                        .setSingleBtn(false)
                        .setContent(R.string.video_room_delete_video_confirm)
                        .setDialogCallback(new DialogBuild.DialogCallback() {
                            @Override
                            public void onClickSure() {
                                clickDeleteVideo(v, seqId);
                            }
                        }).show();
            }
        });

        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getRoomInfo();
        if (roomInfo != null && roomInfo.isSelfAdminOrOwner()) {
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (curSeqId == videoInfo.seq_id) {
                        return;
                    }
                    VideoRoomPacketSender.operateVideoPlayList(VideoRoomPackets.VideoCommand.COMMAND_CHOOSE,
                            curSeqId, curVideoPosition, videoInfo.seq_id, roomInfo.rid, new LifeSeqCallback(holder.itemView) {
                                @Override
                                public void onSuccess(RspHeadInfo head) {
                                    EventDispatcher.postVideoPlaySyncEvent();
                                }

                                @Override
                                public void onFail(RspHeadInfo head) {
                                    ToastUtil.show(head.desc);
                                }
                            });
                }
            });
        } else {
            holder.itemView.setOnClickListener(null);
        }

        if (curSeqId == videoList.get(position).seq_id) {
            holder.videoPlayingIv.setVisibility(View.VISIBLE);
            WpImageLoader.load(WpImageLoader.getAssetUri("svga/voice/movie_play_list_icon.svga"), holder.videoPlayingIv);
            if (curVideoInfo != null && !curVideoInfo.live_broadcast) {
                float process = curVideoPosition * 100 / curVideoInfo.duration;
                holder.videoProcessBar.setProgress((int) process);
                holder.videoProcessBar.setVisibility(View.VISIBLE);
            }
        } else {
            holder.videoPlayingIv.setVisibility(View.GONE);
            holder.videoProcessBar.setVisibility(View.GONE);
        }

        if (!videoList.get(position).live_broadcast) {
            holder.videoTotalTime.setText(TimeUtilities.formatTime(videoList.get(position).duration / 1000f));
        } else {
            holder.videoTotalTime.setVisibility(View.GONE);
        }
    }

    private void clickDeleteVideo(View v, int seqId) {
        if (!containsSeqId(videoList, seqId)) {
            return;
        }
        VideoRoomPacketSender.deleteVideoPlayList(seqId,
                VoiceRoomService.getInstance().getRid(), new LifeSeqCallback(v) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        EventDispatcher.postPlayListUpdateEvent();
                    }

                    @Override
                    public void onFail(RspHeadInfo head) {
                        ToastUtil.show(head.desc);
                    }
                });
    }

    private boolean containsSeqId(List<VideoInfo> list, int seqId) {
        int listSize = list.size();
        boolean exist = false;
        for (int i = 0; i < listSize; i++) {
            if (list.get(i).seq_id == seqId) {
                exist = true;
                break;
            }
        }
        return exist;
    }

    @Override
    public int getItemCount() {
        return videoList.size();
    }

    public class YoutubeVideoListViewHolder extends RecyclerView.ViewHolder {
        private ImageView videoScreenshot;
        private TextView videoTitle;
        private CustomCircleImageView videoAdderIcon;
        private TextView videoAdderName;
        private ImageView videoDeleteIv;
        private ImageView videoPlayingIv;
        private ProgressBar videoProcessBar;
        private TextView videoTotalTime;
        private View videoItemView;

        public YoutubeVideoListViewHolder(View itemView) {
            super(itemView);

            videoScreenshot = itemView.findViewById(R.id.video_list_item_video_screenshot_lay);
            videoTitle = itemView.findViewById(R.id.video_list_item_video_title);
            videoAdderIcon = itemView.findViewById(R.id.video_list_item_add_user_icon);
            videoAdderName = itemView.findViewById(R.id.video_list_item_add_user_name);
            videoDeleteIv = itemView.findViewById(R.id.video_list_item_delete_icon);
            videoPlayingIv = itemView.findViewById(R.id.video_list_item_cur_playing_icon);
            videoProcessBar = itemView.findViewById(R.id.video_list_item_cur_playing_process);
            videoTotalTime = itemView.findViewById(R.id.video_list_total_time);
            videoItemView = itemView;
            if (ScreenUtil.isRtl()) {
                videoTitle.setTextDirection(View.TEXT_DIRECTION_RTL);
            } else {
                videoTitle.setTextDirection(View.TEXT_DIRECTION_LTR);
            }
        }
    }
}
