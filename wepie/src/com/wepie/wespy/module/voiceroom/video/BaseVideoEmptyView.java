package com.wepie.wespy.module.voiceroom.video;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.wepie.wespy.R;
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginRelativeLayout;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IVideoEmptyView;

/**
 * Created by zcs on 2022/04/15.
 */

public abstract class BaseVideoEmptyView extends PluginRelativeLayout implements IVideoEmptyView {
    protected ImageView iconIv;
    protected TextView descriptionTv, selectVideoTv;

    public BaseVideoEmptyView(Context context) {
        super(context);
    }

    public BaseVideoEmptyView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void initData() {
        setClipChildren(false);
    }

    @Override
    protected void initView() {
        LayoutInflater.from(mContext).inflate(R.layout.video_empty_view, this);
        iconIv = findViewById(R.id.video_player_icon_iv);
        descriptionTv = findViewById(R.id.video_player_empty_description_tv);
        selectVideoTv = findViewById(R.id.video_player_select_video_tv);
    }

    public void update(final VideoEmptyDescriptionInfo videoEmptyDes) {
        iconIv.setImageResource(videoEmptyDes.getVideoIconRes());
        descriptionTv.setText(videoEmptyDes.getCenterDescriptionTx());
        selectVideoTv.setText(videoEmptyDes.getSelectDescriptionTx());
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        if (getLayoutParams().height == ViewGroup.LayoutParams.WRAP_CONTENT) {
            int sixteenNineHeight = MeasureSpec.makeMeasureSpec(MeasureSpec.getSize(widthMeasureSpec) * 9 / 16, MeasureSpec.EXACTLY);
            super.onMeasure(widthMeasureSpec, sixteenNineHeight);
        } else {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        }
    }

}