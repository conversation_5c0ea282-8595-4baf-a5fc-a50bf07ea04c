package com.wepie.wespy.module.voiceroom.video.youtube;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.ktx.IntentExtKt;
import com.huiwan.base.util.ContextUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.libtcp.base.HWTCPSocketThread;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LoginHelper;
import com.huiwan.voiceservice.VoiceManager;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.lifecycle.LiveDataExKt;
import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.WPHelper;
import com.wepie.wespy.model.entity.voiceroom.VideoPlayInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.PushRoomMsgEvent;
import com.wepie.wespy.model.event.VideoPlayStateChangeEvent;
import com.wepie.wespy.model.event.VideoPlaySyncEvent;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginRelativeLayout;
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IFullScreenRoomMsg;
import com.wepie.wespy.module.voiceroom.msg.FullScreenRoomMsgView;
import com.wepie.wespy.module.voiceroom.video.event.YoutubeExitFullScreenEvent;
import com.wepie.wespy.module.voiceroom.video.event.YoutubeNetDialogCloseEvent;
import com.wepie.wespy.module.voiceroom.video.event.YoutubePlayNewVideoEvent;
import com.wepie.wespy.module.voiceroom.video.event.YoutubeUploadPositionToServerEvent;
import com.wepie.wespy.module.voiceroom.video.youtube.listeners.AbstractYoutubePlayerListener;
import com.wepie.wespy.module.voiceroom.video.youtube.listeners.YoutubePlayerFullScreenListener;
import com.wepie.wespy.module.voiceroom.video.youtube.ui.utils.FadeViewHelper;
import com.wepie.wespy.module.voiceroom.video.youtube.ui.views.YouTubePlayerFullScreenSeekBar;
import com.wepie.wespy.module.voiceroom.video.youtube.ui.views.YouTubePlayerSeekBar;
import com.wepie.wespy.module.voiceroom.video.youtube.utils.YouTubePlayerUtils;
import com.wepie.wespy.module.voiceroom.video.youtube.utils.YoutubeDialog;
import com.wepie.wespy.module.voiceroom.video.youtube.views.YoutubePlayerView;
import com.wepie.wespy.net.tcp.handler.VideoRoomHandler;
import com.wepie.wespy.net.tcp.packet.VideoPushPackets;
import com.wepie.wespy.net.tcp.packet.VideoRoomPackets;
import com.wepie.wespy.net.tcp.sender.VideoRoomPacketSender;
import com.wepie.wespy.utils.ViewOnClickListenerUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;
import java.util.Map;

@SuppressLint("ViewConstructor")
public class CustomPlayerUiController extends PluginRelativeLayout implements YoutubePlayerFullScreenListener, View.OnClickListener {
    private static final String TAG = "CustomPlayerUiController";
    private final static int MSG_SHOW_TIME = 3 * 1000;
    private final View playerUi;
    private final Context context;
    private YoutubePlayer mYouTubePlayer;
    private final YoutubePlayerView youTubePlayerView;
    private VideoPlayInfo mVideoPlayInfo;

    private View panel, controlsContainer, backgroundForMsg;
    private TextView videoTitle;
    private ProgressBar progressBar;
    private ImageView playPauseButton, youTubeButton, fullScreenButton, customActionLeft, customActionRight, checkVideoList;
    private YouTubePlayerSeekBar youtubePlayerSeekBar;
    private FadeViewHelper fadeControlsContainer;

    private ImageView turnSmallScreenIv, volumeSwitchIv, micSwitchIv, chatListShowControllerIv;
    private YouTubePlayerFullScreenSeekBar youtubePlayerFullScreenSeekBar;
    private FullScreenRoomMsgView chatMsgView;
    private FullScreenRoomMsgView chatMsgForNotifyView;
    private TextView sendMsgEditTx;
    private ConstraintLayout vmcLay;

    private boolean isPlaying;
    private boolean isPlayPauseButtonEnabled = true;
    private final boolean isCustomActionLeftEnabled = false;
    private final boolean isCustomActionRightEnabled = false;
    private boolean fullscreen = false;
    private boolean showChatList = false;

    private final Handler handler = new Handler(Looper.getMainLooper());
    private Runnable msgVisibilityRunner;

    CustomPlayerUiController(Context context, View customPlayerUi, YoutubePlayer youTubePlayer,
                             YoutubePlayerView youTubePlayerView, VideoPlayInfo videoPlayInfo) {
        super(context);
        this.playerUi = customPlayerUi;
        this.context = context;
        this.mYouTubePlayer = youTubePlayer;
        this.youTubePlayerView = youTubePlayerView;
        this.mVideoPlayInfo = videoPlayInfo;
        this.isPlaying = videoPlayInfo.isPlaying;
        initViews(customPlayerUi);
        initVariableAndUpdateViewStates(getRoomInfo());
        initClickListeners(youTubePlayer);
        initPlayerSeekBar(videoPlayInfo.videoInfo.live_broadcast, videoPlayInfo.videoInfo.duration, videoPlayInfo.position);
        setVideoTitle(videoPlayInfo.videoInfo.title);
        ILife iLife = ViewExKt.toLife(this);
        LiveDataExKt.observe(VoiceRoomService.getInstance().getLiveData(), iLife, this::initVariableAndUpdateViewStates);
    }

    private void setVideoTitle(String title) {
        videoTitle.setText(title);
    }

    private void initVariableAndUpdateViewStates(VoiceRoomInfo voiceRoomInfo) {
        initVariableAndView(voiceRoomInfo);
    }

    private void initVariableAndView(VoiceRoomInfo voiceRoomInfo) {
        if (!voiceRoomInfo.isSelfAdminOrOwner()) {
            isPlayPauseButtonEnabled = false;
            youtubePlayerSeekBar.prohibitSeekBarSlideAndClick();
            youtubePlayerFullScreenSeekBar.prohibitSeekBarSlideAndClick();
            playPauseButton.setVisibility(GONE);
        } else {
            isPlayPauseButtonEnabled = true;
            youtubePlayerSeekBar.allowSeekBarSlideAndClick();
            youtubePlayerFullScreenSeekBar.allowSeekBarSlideAndClick();
            playPauseButton.setVisibility(VISIBLE);
        }

        VoiceManager.getInstance().setSystemVolumeTypeByRole(getRoomInfo().isVideoRoom()
                && voiceRoomInfo.isInSeat(LoginHelper.getLoginUid()));

        if (voiceRoomInfo.isInSeat(LoginHelper.getLoginUid()) && fullscreen) {
            micSwitchIv.setVisibility(VISIBLE);
            setMicSwitchIconRes();
        } else {
            micSwitchIv.setVisibility(GONE);
        }
    }

    private void initPlayerSeekBar(boolean isLiveVideo, long duration, long position) {
        youtubePlayerSeekBar.getSeekBar().setMax((int) (duration / 1000));
        youtubePlayerSeekBar.setSeekMax(duration / 1000f);
        youtubePlayerSeekBar.getSeekBar().setProgress((int) (position / 1000));
        youtubePlayerFullScreenSeekBar.getSeekBar().setMax((int) (duration / 1000));
        youtubePlayerFullScreenSeekBar.setSeekMax(duration / 1000f);
        youtubePlayerFullScreenSeekBar.getSeekBar().setProgress((int) (position / 1000));
        updatePlayerSeekBar(isLiveVideo);
    }

    private void initViews(View playerUi) {
        initSmallScreenViews(playerUi);
        initFullScreenViews(playerUi);
        fadeControlsContainer = new FadeViewHelper(controlsContainer);
    }

    private void initSmallScreenViews(View playerUi) {
        panel = playerUi.findViewById(R.id.panel);
        controlsContainer = playerUi.findViewById(R.id.controls_container);
        videoTitle = playerUi.findViewById(R.id.video_title);
        progressBar = playerUi.findViewById(R.id.progress);
        checkVideoList = playerUi.findViewById(R.id.check_video_list_btn);
        playPauseButton = playerUi.findViewById(R.id.play_pause_button);
        youTubeButton = playerUi.findViewById(R.id.youtube_button);
        fullScreenButton = playerUi.findViewById(R.id.fullscreen_button);
        customActionLeft = playerUi.findViewById(R.id.custom_action_left_button);
        customActionRight = playerUi.findViewById(R.id.custom_action_right_button);
        youtubePlayerSeekBar = playerUi.findViewById(R.id.youtube_player_seekbar);

        if (ScreenUtil.isRtl()) {
            videoTitle.setTextDirection(View.TEXT_DIRECTION_RTL);
        } else {
            videoTitle.setTextDirection(View.TEXT_DIRECTION_LTR);
        }
    }

    private void initFullScreenViews(View playerUi) {
        turnSmallScreenIv = playerUi.findViewById(R.id.turn_small_screen_iv);
        volumeSwitchIv = playerUi.findViewById(R.id.room_audio_switch_imv);
        micSwitchIv = playerUi.findViewById(R.id.room_send_mic_image);
        chatListShowControllerIv = playerUi.findViewById(R.id.chat_list_show_controller_iv);
        youtubePlayerFullScreenSeekBar = playerUi.findViewById(R.id.youtube_full_screen_player_seekbar);
        chatMsgView = playerUi.findViewById(R.id.full_screen_msg_view);
        chatMsgForNotifyView = playerUi.findViewById(R.id.full_screen_msg_for_notify);
        backgroundForMsg = playerUi.findViewById(R.id.background_for_msg);
        vmcLay = playerUi.findViewById(R.id.volume_mic_chat_lay);
        sendMsgEditTx = playerUi.findViewById(R.id.youtube_common_send_edit_tx);
    }

    private void showSmallScreenView() {
        fullScreenButton.setVisibility(View.VISIBLE);
        youtubePlayerSeekBar.setVisibility(isLiveVideo() ? View.GONE : View.VISIBLE);
        checkVideoList.setVisibility(View.VISIBLE);
        videoTitle.setVisibility(View.VISIBLE);
        turnSmallScreenIv.setVisibility(View.GONE);
        chatMsgView.setVisibility(View.GONE);
        backgroundForMsg.setVisibility(View.GONE);
        vmcLay.setVisibility(View.GONE);
        youtubePlayerFullScreenSeekBar.setVisibility(View.GONE);
    }

    private boolean isLiveVideo() {
        if (mVideoPlayInfo != null && mVideoPlayInfo.videoInfo != null) {
            return mVideoPlayInfo.videoInfo.live_broadcast;
        }
        return false;
    }

    private void showFullScreenView() {
        fullScreenButton.setVisibility(View.GONE);
        youtubePlayerSeekBar.setVisibility(View.GONE);
        checkVideoList.setVisibility(View.GONE);
        videoTitle.setVisibility(View.GONE);
        turnSmallScreenIv.setVisibility(View.VISIBLE);
        chatMsgView.setVisibility(showChatList ? View.VISIBLE : View.GONE);
        backgroundForMsg.setVisibility(showChatList ? View.VISIBLE : View.GONE);
        vmcLay.setVisibility(View.VISIBLE);
        micSwitchIv.setVisibility(getRoomInfo().isInSeat(LoginHelper.getLoginUid()) && !fullscreen ? VISIBLE : GONE);
        youtubePlayerFullScreenSeekBar.setVisibility(isLiveVideo() ? View.GONE : View.VISIBLE);
        setVolumeSwitchIconRes();
        setMicSwitchIconRes();
    }

    private void setMicSwitchIconRes() {
        VoiceRoomInfo.SeatInfo seatInfo = getRoomInfo().getSeatInfoByUid(LoginHelper.getLoginUid());
        if (seatInfo != null && seatInfo.can_speak) {
            boolean localMicOn = VoiceManager.getInstance().isLocalMicOn();
            micSwitchIv.setImageResource(localMicOn ? R.drawable.room_mic_ic : R.drawable.room_mic_close_ic);
            micSwitchIv.setEnabled(true);
        } else {
            micSwitchIv.setImageResource(R.drawable.room_mic_close_ic);
            micSwitchIv.setEnabled(false);
        }
    }

    private void setVolumeSwitchIconRes() {
        if (VoiceRoomService.getInstance().isMuteRoom(getRid())) {
            volumeSwitchIv.setImageResource(R.drawable.voice_room_audio_off);
        } else {
            volumeSwitchIv.setImageResource(R.drawable.voice_room_audio_on);
        }
    }

    public View getPanelView() {
        return panel;
    }

    private void initClickListeners(YoutubePlayer youTubePlayer) {
        fullScreenButton.setOnClickListener(this);
        panel.setOnClickListener(this);
        playPauseButton.setOnClickListener(this);
        turnSmallScreenIv.setOnClickListener(this);
        volumeSwitchIv.setOnClickListener(this);
        micSwitchIv.setOnClickListener(this);
        chatListShowControllerIv.setOnClickListener(this);
        sendMsgEditTx.setOnClickListener(this);
        checkVideoList.setOnClickListener(new ViewOnClickListenerUtils() {
            @Override
            protected void onProhibitRepeatClick(View v) {
                handleShowVideoList();
            }
        });

        youtubePlayerSeekBar.setYoutubePlayerSeekBarListener(time -> handleVideoOperate(VideoRoomPackets.VideoCommand.COMMAND_SEEK, (long) (time * 1000L)));
        youtubePlayerFullScreenSeekBar.setYoutubePlayerFullScreenSeekBarListener(time -> handleVideoOperate(VideoRoomPackets.VideoCommand.COMMAND_SEEK, (long) (time * 1000L)));
        youTubePlayer.addListener(youtubePlayerSeekBar);
        youTubePlayer.addListener(youtubePlayerFullScreenSeekBar);
        youTubePlayer.addListener(fadeControlsContainer);
        youTubePlayer.addListener(new AbstractYoutubePlayerListener() {
            @Override
            public void onStateChange(@NonNull YoutubePlayer youTubePlayer, @NonNull PlayerConstants.PlayerState state) {
                if (state == PlayerConstants.PlayerState.PLAYING || state == PlayerConstants.PlayerState.PAUSED || state == PlayerConstants.PlayerState.VIDEO_CUED) {
                    //Update player state by server state
                    if (mVideoPlayInfo != null && (!mVideoPlayInfo.isPlaying && (state == PlayerConstants.PlayerState.PLAYING))) {
                        HLog.d(TAG, HLog.USR, "onStateChange, state = {}", state);
                        updatePlayStateByCurrentPlayState();
                    }
                    panel.setBackgroundColor(ContextCompat.getColor(panel.getContext(), android.R.color.transparent));
                    progressBar.setVisibility(View.GONE);

                    if (isPlayPauseButtonEnabled) playPauseButton.setVisibility(View.VISIBLE);
                    if (isCustomActionLeftEnabled) customActionLeft.setVisibility(View.VISIBLE);
                    if (isCustomActionRightEnabled) customActionRight.setVisibility(View.VISIBLE);

                    updatePlayPauseButtonIcon(state == PlayerConstants.PlayerState.PLAYING);

                } else {
                    updatePlayPauseButtonIcon(false);

                    if (state == PlayerConstants.PlayerState.BUFFERING) {
                        progressBar.setVisibility(View.VISIBLE);
                        panel.setBackgroundColor(ContextCompat.getColor(panel.getContext(), android.R.color.transparent));
                        if (isPlayPauseButtonEnabled) playPauseButton.setVisibility(View.VISIBLE);

                        customActionLeft.setVisibility(View.GONE);
                        customActionRight.setVisibility(View.GONE);
                    }

                    if (state == PlayerConstants.PlayerState.UNSTARTED) {
                        progressBar.setVisibility(View.GONE);
                        if (isPlayPauseButtonEnabled) playPauseButton.setVisibility(View.VISIBLE);
                    }

                    if (state == PlayerConstants.PlayerState.ENDED) {
                        if (getRoomInfo().isSelfAdminOrOwner()) {
                            handleVideoOperate(VideoRoomPackets.VideoCommand.COMMAND_COMPLETE, youtubePlayerSeekBar.getSeekBar().getProgress() * 1000L);
                        }
                    }
                }
            }

            @SuppressLint("LongLogTag")
            @Override
            public void onVideoId(@NonNull YoutubePlayer youTubePlayer, @NonNull String videoId) {
                youTubeButton.setOnClickListener(v -> {
                    Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("http://www.youtube.com/watch?v=" + videoId + "#t=" + youtubePlayerSeekBar.getSeekBar().getProgress()));
                    IntentExtKt.safelyStart(intent, context);
                });
            }
        });

        initChatMsgClick();
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initChatMsgClick() {
        chatMsgForNotifyView.getListView().setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    removeMsgRunnerCallbacks();
                    break;
                case MotionEvent.ACTION_UP:
                    handleFullScreenMsgView();
                    break;
                default:
                    break;
            }
            return false;
        });
    }

    @Override
    public void onClick(View v) {
        if (v == fullScreenButton || v == turnSmallScreenIv) {
            clickFullScreenButton();
        } else if (v == panel) {
            handlePanelClick();
        } else if (v == playPauseButton) {
            onPlayButtonPressed();
        } else if (v == volumeSwitchIv) {
            handleVolumeSwitch();
        } else if (v == micSwitchIv) {
            handleMicSwitch();
        } else if (v == chatListShowControllerIv) {
            handleChatListSwitch();
        } else if (v == sendMsgEditTx) {
            showSoftInputDialog();
        }
    }

    private void showSoftInputDialog() {
        VoicePluginService.getPlugin(IFullScreenRoomMsg.class).showSoftInputDialog();
    }

    private void handleChatListSwitch() {
        chatMsgView.setVisibility(showChatList ? View.GONE : View.VISIBLE);
        backgroundForMsg.setVisibility(showChatList ? View.GONE : View.VISIBLE);
        chatListShowControllerIv.setImageResource(showChatList ? R.drawable.player_full_screen_hint_chat_list
                : R.drawable.player_full_screen_show_chat_list);
        showChatList = !showChatList;
        addAppClickByBtnNameAndStatus(TrackButtonName.ROOM_MSG_SWITCH, showChatList);
    }

    private void addAppClickByBtnNameAndStatus(String btnName, boolean status) {
        int statusIntValue = status ? 1 : 0;
        Map<String, Object> trackMap = new HashMap<>();
        trackMap.put("rid", getRoomInfo().rid);
        trackMap.put("status", statusIntValue);
        TrackUtil.appClick(TrackScreenName.VIDEO_ROOM_WATCH_TV, btnName, trackMap);
    }

    private void clickFullScreenButton() {
        Activity activity = ContextUtil.getActivityFromContext(context);
        if (fullscreen) {
            if (activity != null) {
                activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
            }
            youTubePlayerView.exitFullScreen();
            showSmallScreenView();
            WPHelper.INSTANCE.updateScreenInVideoRoomWhenFullScreen();
        } else {
            if (activity != null) {
                activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
            }
            youTubePlayerView.enterFullScreen();
            showFullScreenView();
        }
        fadeControlsContainer.setAutoDismissState(fullscreen);
        fullscreen = !fullscreen;
        addAppClickByBtnNameAndStatus(TrackButtonName.FULLSCREEN_BUTTON, fullscreen);
    }

    public void handlePanelClick() {
        fadeControlsContainer.toggleVisibility();
        if (fadeControlsContainer.getVisibleValue()) {
            if (msgVisibilityRunner != null) {
                handler.removeCallbacks(msgVisibilityRunner);
            }
            hideFullScreenMsgView();
            if (fullscreen && showChatList) {
                backgroundForMsg.setVisibility(VISIBLE);
            }
        } else {
            backgroundForMsg.setVisibility(GONE);
        }
    }

    private void onPlayButtonPressed() {
        if (isPlaying) {
            handleVideoOperate(VideoRoomPackets.VideoCommand.COMMAND_PAUSE, youtubePlayerSeekBar.getSeekBar().getProgress() * 1000L);
        } else {
            handleVideoOperate(VideoRoomPackets.VideoCommand.COMMAND_RESUME, youtubePlayerSeekBar.getSeekBar().getProgress() * 1000L);
        }
    }

    private void handleVideoOperate(VideoRoomPackets.VideoCommand command, long progress) {
        VideoRoomPacketSender.operateVideoPlayList(command, mVideoPlayInfo.videoInfo.seq_id, progress
                , mVideoPlayInfo.videoInfo.seq_id, getRoomInfo().rid, new LifeSeqCallback(youTubePlayerView) {
                    @Override
                    public void onSuccess(RspHeadInfo head) {
                        //TODO:暂时不需要处理成功、失败场景
                        //VideoRoomPackets.VideoOperateVideoRsp operateVideoRsp = VideoRoomHandler.getVideoOperateVideoRsp(head);
                    }

                    @Override
                    public void onFail(RspHeadInfo head) { //TODO:暂停请求失败怎么处理
                    }
                });
    }

    private VoiceRoomInfo getRoomInfo() {
        return VoiceRoomService.getInstance().getLiveData().getValue();
    }

    private void handleMicSwitch() {
        VoiceRoomInfo.SeatInfo seatInfo = getRoomInfo().getSeatInfoByUid(LoginHelper.getLoginUid());
        if (seatInfo != null && seatInfo.can_speak) {
            boolean localMicOn = VoiceManager.getInstance().isLocalMicOn();
            boolean curMicStatus = !localMicOn;
            micSwitchIv.setImageResource(curMicStatus ? R.drawable.room_mic_ic : R.drawable.room_mic_close_ic);
            EventDispatcher.postSyncMicResEvent();
            micSwitchIv.setEnabled(true);
        } else {
            micSwitchIv.setImageResource(R.drawable.room_mic_close_ic);
            micSwitchIv.setEnabled(false);
        }

    }

    private void handleVolumeSwitch() {
        if (VoiceRoomService.getInstance().isMuteRoom(getRid())) {
            volumeSwitchIv.setImageResource(R.drawable.voice_room_audio_on);
        } else {
            volumeSwitchIv.setImageResource(R.drawable.voice_room_audio_off);
        }
        EventDispatcher.postSyncVolumeResEvent();
    }

    private int getRid() {
        return getMainPlugin().getRoomId();
    }

    private void updatePlayPauseButtonIcon(Boolean playing) {
        int drawable = playing ? R.drawable.youtube_player_stop_icon : R.drawable.youtube_player_play_icon;
        playPauseButton.setImageResource(drawable);
    }

    @Override
    public void onYouTubePlayerEnterFullScreen() {
        ViewGroup.LayoutParams viewParams = playerUi.getLayoutParams();
        viewParams.height = ViewGroup.LayoutParams.MATCH_PARENT;
        viewParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
        playerUi.setLayoutParams(viewParams);
    }

    @Override
    public void onYouTubePlayerExitFullScreen() {
        ViewGroup.LayoutParams viewParams = playerUi.getLayoutParams();
        viewParams.height = ViewGroup.LayoutParams.WRAP_CONTENT;
        viewParams.width = ViewGroup.LayoutParams.MATCH_PARENT;
        playerUi.setLayoutParams(viewParams);
    }

    @Override
    protected void initView() {
    }

    @Override
    protected void initData() {
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void exitFullScreenEvent(YoutubeExitFullScreenEvent event) {
        exitFullScreen(event.pauseVideo);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updateVideoPlayStateByPush(VideoPlayStateChangeEvent event) {
        updatePlayState(event.playStateUpdatePush);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updateVideoPlayStateByCurrentPlayState(YoutubeNetDialogCloseEvent event) {
        updatePlayStateByCurrentPlayState();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void syncVideoPlayState(VideoPlaySyncEvent event) {
        getCurrentSyncVideoInfo();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void operatorVideoWithPlayState(YoutubePlayNewVideoEvent event) {
        loadOrCueVideoWithPlayState(event.videoPlayInfo);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPushRoomMsg(PushRoomMsgEvent event) {
        if (event.chatMsg.showInList()) {
            chatMsgForNotifyView.updateAllMsg();
            chatMsgForNotifyView.scroll2Bottom(true);
            handleFullScreenMsgView();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void uploadVideoSeqIdAndPosition(YoutubeUploadPositionToServerEvent event) {
        if (mVideoPlayInfo != null && mVideoPlayInfo.videoInfo != null && youtubePlayerSeekBar != null) {
            VideoRoomPacketSender.uploadVideoPosition(mVideoPlayInfo.videoInfo.seq_id, youtubePlayerSeekBar.getSeekBar().getProgress() * 1000L,
                    getRoomInfo().rid, null);
        }
    }

    private void exitFullScreen(boolean pauseVideo) {
        if (pauseVideo && mYouTubePlayer != null) {
            mYouTubePlayer.pause();
        }
        if (fullscreen) {
            Activity activity = ContextUtil.getActivityFromContext(context);
            if (activity != null) {
                activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
            }
            youTubePlayerView.exitFullScreen();
            showSmallScreenView();
            WPHelper.INSTANCE.updateScreenInVideoRoomWhenFullScreen();
            fadeControlsContainer.setAutoDismissState(fullscreen);
            fullscreen = !fullscreen;
        }
    }

    private void updatePlayStateByCurrentPlayState() {
        if (mYouTubePlayer != null) {
            if (getRoomInfo().isVideoRoom()) {
                if (isPlaying) {
                    mYouTubePlayer.play();
                } else {
                    mYouTubePlayer.pause();
                }
            } else {
                mYouTubePlayer.pause();
                mYouTubePlayer = null;
            }
        }
    }

    private void updatePlayState(VideoPushPackets.PlayStateUpdatePush playStateUpdatePush) {
        updatePlayStateByPlayStateAndPosition(VideoRoomHandler.getVideoPlayInfoByStatePush(playStateUpdatePush));
    }

    private void getCurrentSyncVideoInfo() {
        VideoRoomPacketSender.getSyncVideoInfo(getRoomInfo().rid, new HWTCPSocketThread.WriteCallback() {
            @Override
            public void onWriteSuccess() {
            }

            @Override
            public void onWriteFailed() {
            }
        }, new LifeSeqCallback(youTubePlayerView) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                updatePlayStateByPlayStateAndPosition(VideoRoomHandler.updateVideoPlayInfo(head));
            }

            @Override
            public void onFail(RspHeadInfo head) {
            }
        });
    }

    private synchronized void updatePlayStateByPlayStateAndPosition(VideoPlayInfo videoPlayInfo) {
        if (mYouTubePlayer != null && mVideoPlayInfo != null && videoPlayInfo.videoInfo != null) {
            if (videoPlayInfo.videoInfo.id.equals(mVideoPlayInfo.videoInfo.id)) {
                isPlaying = videoPlayInfo.isPlaying;
                if (videoPlayInfo.isPlaying) {
                    mYouTubePlayer.play();
                } else {
                    mYouTubePlayer.pause();
                }
                updatePlayPauseButtonIcon(isPlaying);

                if (!videoPlayInfo.videoInfo.live_broadcast) {
                    mYouTubePlayer.seekTo(videoPlayInfo.position / 1000f);
                    youtubePlayerSeekBar.getSeekBar().setProgress((int) videoPlayInfo.position / 1000);
                    youtubePlayerFullScreenSeekBar.getSeekBar().setProgress((int) videoPlayInfo.position / 1000);
                }
                HLog.d(TAG, HLog.USR, "videoPlayInfo = {}, mVideoPlayInfo = {},videoPlayInfo.position= {}, mVideoPlayInfo.position= {}",
                        videoPlayInfo.isPlaying, mVideoPlayInfo.isPlaying, videoPlayInfo.position, mVideoPlayInfo.position);
            } else {
                loadOrCueVideoWithPlayState(videoPlayInfo);
            }
            updatePlayerSeekBar(videoPlayInfo.videoInfo.live_broadcast);
        } else {
            if (mYouTubePlayer != null && mVideoPlayInfo != null) {
                mYouTubePlayer.pause();
                isPlaying = false;
            }
        }
    }

    private void updatePlayerSeekBar(boolean isLiveVideo) {
        if (!isLiveVideo) {
            if (fullscreen) {
                youtubePlayerFullScreenSeekBar.setVisibility(VISIBLE);
            } else {
                youtubePlayerSeekBar.setVisibility(VISIBLE);
            }
        } else {
            youtubePlayerSeekBar.setVisibility(GONE);
            youtubePlayerFullScreenSeekBar.setVisibility(GONE);
        }
    }

    private void handleShowVideoList() {
        YoutubeDialog youtubeDialog = new YoutubeDialog(mContext);
        youtubeDialog.showYoutubeVideoListDialog();
    }

    private void loadOrCueVideoWithPlayState(VideoPlayInfo videoPlayInfo) {
        YouTubePlayerUtils.loadOrCueVideoWithPlayState(mYouTubePlayer,
                !ActivityTaskManager.isBackground(),
                videoPlayInfo.isPlaying,
                videoPlayInfo.videoInfo.id,
                videoPlayInfo.position / 1000f);
        mVideoPlayInfo = videoPlayInfo;
        this.isPlaying = videoPlayInfo.isPlaying;
        setVideoTitle(videoPlayInfo.videoInfo.title);
    }

    private void handleFullScreenMsgView() {
        if (showChatList && fullscreen && fadeControlsContainer != null && !fadeControlsContainer.getVisibleValue()) {
            removeMsgRunnerCallbacks();
            chatMsgForNotifyView.setVisibility(VISIBLE);
            //backgroundForMsg.setVisibility(VISIBLE);
            msgVisibilityRunner = this::hideFullScreenMsgView;
            handler.postDelayed(msgVisibilityRunner, MSG_SHOW_TIME);
        }
    }

    private void hideFullScreenMsgAndBackgroundForMsgView() {
        hideFullScreenMsgView();
        backgroundForMsg.setVisibility(GONE);
    }

    private void removeMsgRunnerCallbacks() {
        if (msgVisibilityRunner != null) {
            handler.removeCallbacks(msgVisibilityRunner);
        }
    }

    private void hideFullScreenMsgView() {
        chatMsgForNotifyView.setVisibility(GONE);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        EventBus.getDefault().register(this);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}
