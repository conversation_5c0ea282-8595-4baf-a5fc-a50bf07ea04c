@file:JvmName("YouTubePlayerUtils")
package com.wepie.wespy.module.voiceroom.video.youtube.utils

import com.wepie.wespy.module.voiceroom.video.youtube.YoutubePlayer

/**
 * Calls [YouTubePlayer.cueVideo] or [YouTubePlayer.loadVideo] depending on which one is more appropriate.
 * If it can't decide, calls [YouTubePlayer.cueVideo] by default.
 *
 * In most cases you want to avoid calling [YouTubePlayer.loadVideo] if the Activity/Fragment is not in the foreground.
 * This function automates these checks for you.
 * @param lifecycle the lifecycle of the Activity or Fragment containing the YouTubePlayerView.
 * @param videoId id of the video.
 * @param startSeconds the time from which the video should start playing.
 */
fun YoutubePlayer.loadOrCueVideoWithPlayState(canLoad: Boolean, isPlaying: Boolean, videoId: String, startSeconds: Float) {
    loadOrCueVideo(canLoad && isPlaying, videoId, startSeconds)
}

@JvmSynthetic internal fun YoutubePlayer.loadOrCueVideo(canLoad: Boolean, videoId: String, startSeconds: Float) {
    if (canLoad)
        loadVideo(videoId, startSeconds)
    else
        cueVideo(videoId, startSeconds)
}

fun YoutubePlayer.seYoutubeVolume(volumePercent: Int) {
    setYoutubeVolume(volumePercent)
}

@JvmSynthetic internal fun YoutubePlayer.setYoutubeVolume(volumePercent: Int) {
    setVolume(volumePercent)
}