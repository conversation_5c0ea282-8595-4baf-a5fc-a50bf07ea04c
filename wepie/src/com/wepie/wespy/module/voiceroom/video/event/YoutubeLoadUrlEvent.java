package com.wepie.wespy.module.voiceroom.video.event;

public class YoutubeLoadUrlEvent {
    private String url;
    public boolean isVideoListView;
    public String videoId;

    public YoutubeLoadUrlEvent(String url) {
        this.url = url;
    }

    public YoutubeLoadUrlEvent(String url, boolean isVideoListView) {
        this.url = url;
        this.isVideoListView = isVideoListView;
    }

    public YoutubeLoadUrlEvent(String url, boolean isVideoListView, String videoId) {
        this.url = url;
        this.isVideoListView = isVideoListView;
        this.videoId = videoId;
    }

    public String getUrl() {
        return url;
    }
}
