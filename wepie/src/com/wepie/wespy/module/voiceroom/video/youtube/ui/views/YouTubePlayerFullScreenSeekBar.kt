package com.wepie.wespy.module.voiceroom.video.youtube.ui.views

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.widget.LinearLayout
import android.widget.SeekBar
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.graphics.drawable.DrawableCompat
import com.wepie.wespy.R
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher
import com.wepie.wespy.module.voiceroom.video.youtube.PlayerConstants
import com.wepie.wespy.module.voiceroom.video.youtube.YoutubePlayer
import com.wepie.wespy.module.voiceroom.video.youtube.listeners.YoutubePlayerListener
import com.wepie.wespy.module.voiceroom.video.youtube.ui.utils.TimeUtilities

class YouTubePlayerFullScreenSeekBar(context: Context, attrs: AttributeSet? = null): LinearLayout(context, attrs), SeekBar.OnSeekBarChangeListener,
    YoutubePlayerListener {

    private var seekBarTouchStarted = false
    // I need this variable because onCurrentSecond gets called every 100 mils, so without the proper checks on this variable in onCurrentSeconds the seek bar glitches when touched.
    private var newSeekBarProgress = -1

    private var isPlaying = false

    var showBufferingProgress = true
    var youtubePlayerFullScreenSeekBarListener: YouTubePlayerFullScreenSeekBarListener? = null

    val videoCurrentTimeTextView = TextView(context)
    val videoDurationTextView = TextView(context)
    val seekBar = SeekBar(context)

    init {
        layoutDirection = LAYOUT_DIRECTION_LTR
        val typedArray = context.theme.obtainStyledAttributes(attrs, R.styleable.YouTubePlayerSeekBar, 0, 0)

        val fontSize = typedArray.getDimensionPixelSize(R.styleable.YouTubePlayerSeekBar_fontSize, resources.getDimensionPixelSize(R.dimen.wp8))
        val color = typedArray.getColor(R.styleable.YouTubePlayerSeekBar_seek_bar_color, ContextCompat.getColor(context, R.color.wejoy_red))

        typedArray.recycle()

        val padding = resources.getDimensionPixelSize(R.dimen.wp8)

        videoCurrentTimeTextView.text = "0:00"
        videoCurrentTimeTextView.setPadding(padding, padding, 0, padding)
        videoCurrentTimeTextView.setTextColor(ContextCompat.getColor(context, android.R.color.white))
        videoCurrentTimeTextView.gravity = Gravity.CENTER_VERTICAL

        videoDurationTextView.text = "0:00"
        videoDurationTextView.setPadding(0, padding, padding, padding)
        videoDurationTextView.setTextColor(ContextCompat.getColor(context, android.R.color.white))
        videoDurationTextView.gravity = Gravity.CENTER_VERTICAL

        setFontSize(fontSize.toFloat())

        seekBar.setPadding(padding*2, padding, padding*2, padding)
        setColor()

        addView(videoCurrentTimeTextView, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT))
        addView(seekBar, LayoutParams(0, LayoutParams.WRAP_CONTENT, 1f))
        addView(videoDurationTextView, LayoutParams(LayoutParams.WRAP_CONTENT,LayoutParams.WRAP_CONTENT))

        gravity = Gravity.CENTER_VERTICAL

        seekBar.setOnSeekBarChangeListener(this)
    }

    /**
     * @param fontSize in pixels.
     */
    fun setFontSize(fontSize: Float) {
        videoCurrentTimeTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, fontSize)
        videoDurationTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, fontSize)
    }

    fun setColor() {
        DrawableCompat.setTint(seekBar.thumb, Color.parseColor("#FFFFFF"))
        seekBar.progressDrawable = ResourcesCompat.getDrawable(resources, R.drawable.video_progress_stytle, null)
    }

    private fun updateState(state: PlayerConstants.PlayerState) {
        when (state) {
            PlayerConstants.PlayerState.ENDED -> handleVideoEnd()
            PlayerConstants.PlayerState.PAUSED -> isPlaying = false
            PlayerConstants.PlayerState.PLAYING -> isPlaying = true
            //PlayerConstants.PlayerState.UNSTARTED -> resetUi()
            else -> { }
        }
    }

    private fun handleVideoEnd(){
        isPlaying = false
        EventDispatcher.postYoutubeVideoEndEvent()
    }

    private fun resetUi() {
        seekBar.progress = 0
        seekBar.max = 0
        videoDurationTextView.post { videoDurationTextView.text = "" }
    }

    // Seekbar

    override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
        videoCurrentTimeTextView.text = TimeUtilities.formatTime(progress.toFloat())
    }

    override fun onStartTrackingTouch(seekBar: SeekBar) {
        seekBarTouchStarted = true
    }

    override fun onStopTrackingTouch(seekBar: SeekBar) {
        if (isPlaying)
            newSeekBarProgress = seekBar.progress

        youtubePlayerFullScreenSeekBarListener?.seekTo(seekBar.progress.toFloat())
        seekBarTouchStarted = false
    }

    // YouTubePlayerListener

    override fun onStateChange(youTubePlayer: YoutubePlayer, state: PlayerConstants.PlayerState) {
        newSeekBarProgress = -1
        updateState(state)
    }

    override fun onCurrentSecond(youTubePlayer: YoutubePlayer, second: Float) {
        // ignore if the user is currently moving the SeekBar
        if (seekBarTouchStarted)
            return
        // ignore if the current time is older than what the user selected with the SeekBar
        if (newSeekBarProgress > 0 && TimeUtilities.formatTime(second) != TimeUtilities.formatTime(newSeekBarProgress.toFloat()))
            return

        newSeekBarProgress = -1

        seekBar.progress = second.toInt()
    }

    @SuppressLint("SetTextI18n")
    override fun onVideoDuration(youTubePlayer: YoutubePlayer, duration: Float) {
        videoDurationTextView.text = TimeUtilities.formatTime(duration)
        seekBar.max = duration.toInt()
    }

    @SuppressLint("SetTextI18n")
    fun setSeekMax(duration: Float) {
        videoDurationTextView.text = TimeUtilities.formatTime(duration)
        seekBar.max = duration.toInt()
    }

    @SuppressLint("ClickableViewAccessibility")
    fun prohibitSeekBarSlideAndClick() {
        seekBar.isClickable = false
        DrawableCompat.setTint(seekBar.thumb, Color.parseColor("#00000000"))
        seekBar.splitTrack = false
        seekBar.setOnTouchListener(mOnTouchListener)
    }

    @SuppressLint("ClickableViewAccessibility")
    fun allowSeekBarSlideAndClick() {
        seekBar.isClickable = true
        seekBar.splitTrack = true
        setColor()
        seekBar.setOnTouchListener(null)
    }

    private val mOnTouchListener = object : OnTouchListener {
        @SuppressLint("ClickableViewAccessibility")
        override fun onTouch(view: View, event: MotionEvent): Boolean{
            return true
        }
    }

    override fun onVideoLoadedFraction(youTubePlayer: YoutubePlayer, loadedFraction: Float) {
        if (showBufferingProgress)
            seekBar.secondaryProgress = (loadedFraction * seekBar.max).toInt()
        else
            seekBar.secondaryProgress = 0
    }

    override fun onReady(youTubePlayer: YoutubePlayer) { }
    override fun onVideoId(youTubePlayer: YoutubePlayer, videoId: String) { }
    override fun onApiChange(youTubePlayer: YoutubePlayer) { }
    override fun onPlaybackQualityChange(youTubePlayer: YoutubePlayer, playbackQuality: PlayerConstants.PlaybackQuality) { }
    override fun onPlaybackRateChange(youTubePlayer: YoutubePlayer, playbackRate: PlayerConstants.PlaybackRate) { }
    override fun onError(youTubePlayer: YoutubePlayer, error: PlayerConstants.PlayerError) { }
}

interface YouTubePlayerFullScreenSeekBarListener {
    fun seekTo(time: Float)
}