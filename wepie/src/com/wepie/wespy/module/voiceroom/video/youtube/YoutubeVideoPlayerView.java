package com.wepie.wespy.module.voiceroom.video.youtube;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.base.ActivityTaskManager;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.libtcp.TcpConnect;
import com.huiwan.libtcp.base.HWTCPSocketThread;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VideoPlayInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.model.event.VideoPlayStateChangeEvent;
import com.wepie.wespy.module.login.start.InitUtil;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.video.BaseVideoPlayerView;
import com.wepie.wespy.module.voiceroom.video.event.YoutubeUpdatePositionByPushEvent;
import com.wepie.wespy.module.voiceroom.video.event.YoutubeVideoEndEvent;
import com.wepie.wespy.module.voiceroom.video.youtube.listeners.AbstractYoutubePlayerListener;
import com.wepie.wespy.module.voiceroom.video.youtube.listeners.YoutubePlayerListener;
import com.wepie.wespy.module.voiceroom.video.youtube.options.IFramePlayerOptions;
import com.wepie.wespy.module.voiceroom.video.youtube.utils.YouTubePlayerUtils;
import com.wepie.wespy.module.voiceroom.video.youtube.views.YoutubePlayerView;
import com.wepie.wespy.net.tcp.handler.VideoRoomHandler;
import com.wepie.wespy.net.tcp.packet.VideoPushPackets;
import com.wepie.wespy.net.tcp.sender.VideoRoomPacketSender;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Created by zcs on 2022/04/18.
 */

public class YoutubeVideoPlayerView extends BaseVideoPlayerView {
    private String TAG = "YoutubeVideoPlayerView";
    private static final int SHOW_EMPTY_TIPS = 1;
    private static final int SHOW_NETWORK_REASON_TIPS = 2;
    private final static int MSG_SYNC_VIDEO_INFO = 0x01;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private Handler mUpLoadPositionHandler;
    private HandlerThread mHandlerThread;
    private YoutubePlayerView youTubePlayerView;
    private YoutubeVideoEmptyView ytbEmptyView;
    private CustomPlayerUiController customPlayerUiController;
    private TextView endTipsTitleTv, networkReasonTipsTv, emptyDescriptionTv, selectVideoTv;
    private ImageView exitFullScreenIv;
    private boolean needLoadVideo = false;
    private int reconnectCount = 3;
    private float mStartX = 0;
    private float mStartY = 0;
    private float mEndX = 0;
    private float mEndY = 0;
    private boolean isYouTubePlayerOnReady = false;

    public YoutubeVideoPlayerView(Context context) {
        super(context);
        initPlayerView();
    }

    public YoutubeVideoPlayerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initPlayerView();
    }

    private void initPlayerView() {
        youTubePlayerView = findViewById(R.id.youtube_player_view);
        ytbEmptyView = findViewById(R.id.youtube_empty_view);
        endTipsTitleTv = ytbEmptyView.findViewById(R.id.video_player_end_tips_tv);
        networkReasonTipsTv = ytbEmptyView.findViewById(R.id.video_player_networks_reason_tv);
        emptyDescriptionTv = ytbEmptyView.findViewById(R.id.video_player_empty_description_tv);
        selectVideoTv = ytbEmptyView.findViewById(R.id.video_player_select_video_tv);
        exitFullScreenIv = ytbEmptyView.findViewById(R.id.video_player_empty_exit_full_screen);
        initClickListeners();
        getCurrentSyncVideoInfo();
        EventBus.getDefault().register(this);
    }

    private void initClickListeners() {
        exitFullScreenIv.setOnClickListener(v -> {
            EventDispatcher.postExitFullScreenEvent(false);
            exitFullScreenIv.setVisibility(GONE);
        });
    }

    private void syncVideoInfo() {
        mainHandler.postDelayed(this::getCurrentSyncVideoInfo, 100);
    }

    private void getCurrentSyncVideoInfo() {
        VideoRoomPacketSender.getSyncVideoInfo(getRoomInfo().rid, new HWTCPSocketThread.WriteCallback() {
            @Override
            public void onWriteSuccess() {
            }

            //voiceRoomSocketThread在切换语言的时候会概率性disconnect，且不会重新连接，这里判断其为null时（状态值为-100）
            //去重新连接，重连次数限制为三次，之后并不在连，由于框架本身原因，现在只能通过这种模式减少观影房断开连接的概率，之后
            //有较低概率出现"no such client"提示，应该是系统提示文案
            @Override
            public void onWriteFailed() {
                if (reconnectCount > 0 && TcpConnect.getInstance().voiceRoomSocketThreadStatus() == -100) {
                    InitUtil.connectTcp();
                    syncVideoInfo();
                    reconnectCount--;
                    HLog.d(TAG, HLog.USR, "getCurrentSyncVideoInfo onWriteFailed !");
                }
            }
        }, new LifeSeqCallback(this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                updatePlayerViewByVideoInfo(VideoRoomHandler.updateVideoPlayInfo(head));
            }

            @Override
            public void onFail(RspHeadInfo head) {
                if (head.code == -1) {
                    showEmptyVideoView(SHOW_NETWORK_REASON_TIPS);
                }
            }
        });
    }

    private void showEmptyVideoView(int emptyType) {
        ytbEmptyView.setVisibility(VISIBLE);
        if (emptyType == SHOW_EMPTY_TIPS) {
            showEmptyTipsView();
        } else if (emptyType == SHOW_NETWORK_REASON_TIPS) {
            showNetworkReasonView();
        }
        youTubePlayerView.setVisibility(GONE);
        videoGestureLay.setVisibility(GONE);
    }

    private void showEmptyTipsView() {
        endTipsTitleTv.setVisibility(GONE);
        networkReasonTipsTv.setVisibility(GONE);
        exitFullScreenIv.setVisibility(GONE);
        emptyDescriptionTv.setVisibility(VISIBLE);
        selectVideoTv.setVisibility(VISIBLE);
    }

    private void showNetworkReasonView() {
        endTipsTitleTv.setVisibility(VISIBLE);
        networkReasonTipsTv.setVisibility(VISIBLE);
        setExitFullScreenIvByScreenState();
        emptyDescriptionTv.setVisibility(GONE);
        selectVideoTv.setVisibility(GONE);
    }

    private void setExitFullScreenIvByScreenState() {
        if (getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
            exitFullScreenIv.setVisibility(VISIBLE);
        } else {
            exitFullScreenIv.setVisibility(GONE);
        }
    }

    private void updatePlayerViewByVideoInfo(VideoPlayInfo videoPlayInfo) {
        if (videoPlayInfo != null && videoPlayInfo.videoInfo != null) {
            ytbEmptyView.setVisibility(GONE);
            youTubePlayerView.setVisibility(VISIBLE);
            videoGestureLay.setVisibility(VISIBLE);
            if (youTubePlayerView.getYouTubePlayerReadyState()) {
                EventDispatcher.postYoutubePlayNewVideoEvent(videoPlayInfo);
            } else {
                initYouTubePlayerView(videoPlayInfo);
            }
        } else {
            showEmptyVideoView(SHOW_EMPTY_TIPS);
        }
    }

    private VoiceRoomInfo getRoomInfo() {
        return VoiceRoomService.getInstance().getLiveData().getValue();
    }

    private void initYouTubePlayerView(VideoPlayInfo videoPlayInfo) {
        ActivityTaskManager.ActivityTaskListener listener = new ActivityTaskManager.ActivityTaskListener() {
            @Override
            public void onForeground(@NonNull Activity activity) {
                super.onForeground(activity);
                youTubePlayerView.onResume();
            }

            @Override
            public void onBackground(@NonNull Activity activity) {
                super.onBackground(activity);
                youTubePlayerView.onStop();
            }
        };
        ActivityTaskManager.getInstance().registerActivityTaskListener(listener);
        getMainPlugin().getLife().onDestroy(() -> {
            youTubePlayerView.release();
            ActivityTaskManager.getInstance().unregisterActivityTaskListener(listener);
            return null;
        });
        View customPlayerUi = youTubePlayerView.inflateCustomPlayerUi(R.layout.wejoy_youtube_default_player_ui);
        YoutubePlayerListener ytbPlayerListener = new AbstractYoutubePlayerListener() {
            @SuppressLint("ClickableViewAccessibility")
            @Override
            public void onReady(@NonNull YoutubePlayer youTubePlayer) {
                // using pre-made custom ui

                HLog.d(TAG, HLog.USR, "initYouTubePlayerView , onReady!");
                isYouTubePlayerOnReady = true;
                updateVideoPlayerView(videoPlayInfo);
                if (customPlayerUiController != null) {
                    removeView(customPlayerUiController);
                }
                customPlayerUiController = new CustomPlayerUiController(mContext
                        , customPlayerUi, youTubePlayer, youTubePlayerView, videoPlayInfo);
                youTubePlayerView.addFullScreenListener(customPlayerUiController);
                addView(customPlayerUiController, 0);

                YouTubePlayerUtils.loadOrCueVideoWithPlayState(youTubePlayer,
                        !ActivityTaskManager.isBackground(),
                        videoPlayInfo.isPlaying,
                        videoPlayInfo.videoInfo.id,
                        videoPlayInfo.position / 1000f);

                customPlayerUiController.getPanelView().setOnTouchListener((v, event) -> {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            mStartX = event.getX();
                            mStartY = event.getY();
                            updateOldVolume();
                            HLog.d(TAG, HLog.USR, "MotionEvent.ACTION_DOWN mStartX = {}, mStartY = {}",
                                    mStartX, mStartY);
                            break;
                        case MotionEvent.ACTION_UP:
                            mEndX = event.getX();
                            mEndY = event.getY();
                            if (Math.abs(mEndY - mStartY) < 10) { //点击屏幕
                                if (customPlayerUiController != null) {
                                    customPlayerUiController.handlePanelClick();
                                }
                            }
                            HLog.d(TAG, HLog.USR, "MotionEvent.ACTION_UP mEndX = {}, mEndY = {},mEndY - mStartY = {}",
                                    mEndX, mEndY, mEndY - mStartY);
                            break;
                        case MotionEvent.ACTION_MOVE:
                            mEndX = event.getX();
                            mEndY = event.getY();
                            float dy = mEndY - mStartY;
                            boolean isScrollVolumeArea;
                            // 滑动调节音量区域镜像
                            if (ScreenUtil.isRtl()) {
                                isScrollVolumeArea = event.getX() <= (float) getWidth() / 2;
                            } else {
                                isScrollVolumeArea = event.getX() > (float) getWidth() / 2;
                            }
                            if (Math.abs(dy) > 20 && isScrollVolumeArea) {
                                handleVolumeGesture(mStartY, mEndY);
                            }
                            HLog.d(TAG, HLog.USR, "MotionEvent.ACTION_MOVE mEndX = {}, mEndY = {},mEndY - mEndX = {}",
                                    mEndX, mEndY, mEndY - mEndX);
                            break;
                    }
                    return true;
                });
            }

            @Override
            public void onError(@NonNull YoutubePlayer youTubePlayer, @NonNull PlayerConstants.PlayerError error) {
                super.onError(youTubePlayer, error);
                HLog.d(TAG, HLog.USR, "initYouTubePlayerView , onError!");
                checkYoutubePlayerReadyState();
            }
        };

        // disable web ui
        IFramePlayerOptions options = new IFramePlayerOptions.Builder().controls(0).build();
        youTubePlayerView.initialize(ytbPlayerListener, options);
    }

    private void updateVideoPlayerView(VideoPlayInfo videoPlayInfo) {
        if (ytbEmptyView.getVisibility() == VISIBLE) {
            mainHandler.postDelayed(() -> updatePlayerViewByVideoInfo(videoPlayInfo), 100);
        }
    }

    private void checkYoutubePlayerReadyState() {
        if (!isYouTubePlayerOnReady) {
            showEmptyVideoView(SHOW_NETWORK_REASON_TIPS);
        } //TODO：初始化失败，处理方案
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }

        if (customPlayerUiController != null) {
            customPlayerUiController = null;
        }

        if (mUpLoadPositionHandler != null) {
            mUpLoadPositionHandler.removeMessages(MSG_SYNC_VIDEO_INFO);
            mUpLoadPositionHandler = null;
        }

        if (mHandlerThread != null) {
            mHandlerThread.quitSafely();
            mHandlerThread = null;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updateVideoPlayState(VideoPlayStateChangeEvent event) {
        updatePlayState(event.playStateUpdatePush);
    }

    private void updatePlayState(VideoPushPackets.PlayStateUpdatePush playStateUpdatePush) {
        if (youTubePlayerView.getVisibility() == GONE && playStateUpdatePush != null && playStateUpdatePush.hasVideoInfo()) {
            ytbEmptyView.setVisibility(GONE);
            youTubePlayerView.setVisibility(VISIBLE);
            videoGestureLay.setVisibility(VISIBLE);
            if (needLoadVideo && youTubePlayerView.getYouTubePlayerReadyState()) {
                EventDispatcher.postYoutubePlayNewVideoEvent(VideoRoomHandler.getVideoPlayInfoByStatePush(playStateUpdatePush));
                needLoadVideo = false;
                return;
            }
            initYouTubePlayerView(VideoRoomHandler.getVideoPlayInfoByStatePush(playStateUpdatePush));
        } else {
            if (playStateUpdatePush != null && !playStateUpdatePush.hasVideoInfo()) {
                showEmptyVideoView(SHOW_EMPTY_TIPS);
                needLoadVideo = true;
                EventDispatcher.postExitFullScreenEvent(false);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updateYoutubeVideoEndState(YoutubeVideoEndEvent event) {
        needLoadVideo = true;
        showEmptyVideoView(SHOW_NETWORK_REASON_TIPS);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void uploadPositionByServerPush(YoutubeUpdatePositionByPushEvent event) {
        if (mHandlerThread == null && mUpLoadPositionHandler == null) {
            initUpLoadPositionHandler(event.intervalMs);
        } else {
            updateSyncTime(event.intervalMs);
        }
    }

    private void updateSyncTime(int intervalMs) {
        mUpLoadPositionHandler.removeMessages(MSG_SYNC_VIDEO_INFO);
        mUpLoadPositionHandler.sendMessageDelayed(getSyncMessage(intervalMs), intervalMs);
    }

    private void initUpLoadPositionHandler(int intervalMs) {
        mHandlerThread = new HandlerThread(TAG);
        mHandlerThread.start();
        mUpLoadPositionHandler = new Handler(mHandlerThread.getLooper()) {
            @Override
            public void handleMessage(@NonNull Message msg) {
                super.handleMessage(msg);
                if (msg.what == MSG_SYNC_VIDEO_INFO) {
                    EventDispatcher.postYoutubeUpdatePositionToServerEvent();
                    if (mUpLoadPositionHandler != null) {
                        mUpLoadPositionHandler.sendMessageDelayed(getSyncMessage(intervalMs), msg.arg1);
                    }
                }
            }
        };
        mUpLoadPositionHandler.sendMessage(getSyncMessage(intervalMs));
    }

    private Message getSyncMessage(int intervalMs) {
        Message msg = new Message();
        msg.what = MSG_SYNC_VIDEO_INFO;
        msg.arg1 = intervalMs;
        return msg;
    }
}
