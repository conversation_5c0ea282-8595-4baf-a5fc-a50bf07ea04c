package com.wepie.wespy.module.voiceroom.video;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.wepie.wespy.R;

public class YoutubeVideoListContentView extends ConstraintLayout implements View.OnClickListener {
    private static final String TAG = "YoutubeVideoListContentView";

    private Context mContext;

    public YoutubeVideoListContentView(@NonNull Context context) {
        super(context);
        mContext = context;
        init();
    }

    public YoutubeVideoListContentView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    public YoutubeVideoListContentView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        init();
    }

    public YoutubeVideoListContentView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        mContext = context;
        init();
    }

    private void init() {
        inflate(mContext, R.layout.youtube_video_list_content, this);

    }

    @Override
    public void onClick(View v) {

    }
}
