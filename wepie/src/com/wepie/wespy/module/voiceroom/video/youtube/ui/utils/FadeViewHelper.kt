package com.wepie.wespy.module.voiceroom.video.youtube.ui.utils

import android.animation.Animator
import android.view.View
import com.wepie.wespy.module.voiceroom.video.youtube.PlayerConstants
import com.wepie.wespy.module.voiceroom.video.youtube.YoutubePlayer
import com.wepie.wespy.module.voiceroom.video.youtube.listeners.YoutubePlayerListener

class FadeViewHelper(val targetView: View): YoutubePlayerListener {
    companion object {
        const val DEFAULT_ANIMATION_DURATION = 300L
        const val DEFAULT_FADE_OUT_DELAY = 3000L
    }

    private var isPlaying = false

    private var canFade = false
    private var isVisible = true
    private var canAutoDismiss = true

    private var fadeOut: Runnable = Runnable{ fade(0f) }

    var isDisabled = false

    /**
     * Duration of the fade animation in milliseconds.
     */
    var animationDuration = DEFAULT_ANIMATION_DURATION

    /**
     * Delay after which the view automatically fades out.
     */
    var fadeOutDelay = DEFAULT_FADE_OUT_DELAY

    fun toggleVisibility() {
        fade(if (isVisible) 0f else 1f)
    }

    fun getVisibleValue(): Boolean{
        return isVisible
    }

    fun setAutoDismissState(canAutoDismiss: Boolean) {
        this.canAutoDismiss = canAutoDismiss
        if (canAutoDismiss)
            targetView.handler?.postDelayed(fadeOut, fadeOutDelay)
        else
            targetView.handler?.removeCallbacks(fadeOut)
    }

    private fun fade(finalAlpha: Float) {
        if (!canFade || isDisabled)
            return

        isVisible = finalAlpha != 0f

        // if the controls are shown and the player is playing they should automatically fade after a while.
        // otherwise don't do anything automatically
        if (finalAlpha == 1f && isPlaying && canAutoDismiss)
            targetView.handler?.postDelayed(fadeOut, fadeOutDelay)
        else
            targetView.handler?.removeCallbacks(fadeOut)

        targetView.animate()
                .alpha(finalAlpha)
                .setDuration(animationDuration)
                .setListener(object : Animator.AnimatorListener {
                    override fun onAnimationStart(animator: Animator) {
                        if (finalAlpha == 1f) targetView.visibility = View.VISIBLE
                    }

                    override fun onAnimationEnd(animator: Animator) {
                        if (finalAlpha == 0f) targetView.visibility = View.GONE
                    }

                    override fun onAnimationCancel(animator: Animator) {}
                    override fun onAnimationRepeat(animator: Animator) {}
                }).start()
    }

    private fun updateState(state: PlayerConstants.PlayerState) {
        when (state) {
            PlayerConstants.PlayerState.ENDED -> isPlaying = false
            PlayerConstants.PlayerState.PAUSED -> isPlaying = false
            PlayerConstants.PlayerState.PLAYING -> isPlaying = true
            PlayerConstants.PlayerState.UNSTARTED -> { }
            else -> { }
        }
    }

    override fun onStateChange(youTubePlayer: YoutubePlayer, state: PlayerConstants.PlayerState) {
        updateState(state)

        when(state) {
            PlayerConstants.PlayerState.PLAYING, PlayerConstants.PlayerState.PAUSED, PlayerConstants.PlayerState.VIDEO_CUED -> {
                canFade = true
                if (state == PlayerConstants.PlayerState.PLAYING && canAutoDismiss)
                    targetView.handler?.postDelayed(fadeOut, fadeOutDelay)
                else
                    targetView.handler?.removeCallbacks(fadeOut)
            }
            PlayerConstants.PlayerState.BUFFERING, PlayerConstants.PlayerState.UNSTARTED -> {
                fade(1f)
                canFade = false
            }
            PlayerConstants.PlayerState.UNKNOWN -> fade(1f)
            PlayerConstants.PlayerState.ENDED -> fade(1f)
        }
    }

    override fun onReady(youTubePlayer: YoutubePlayer) { }
    override fun onPlaybackQualityChange(youTubePlayer: YoutubePlayer, playbackQuality: PlayerConstants.PlaybackQuality) { }
    override fun onPlaybackRateChange(youTubePlayer: YoutubePlayer, playbackRate: PlayerConstants.PlaybackRate) { }
    override fun onError(youTubePlayer: YoutubePlayer, error: PlayerConstants.PlayerError) { }
    override fun onApiChange(youTubePlayer: YoutubePlayer) { }
    override fun onCurrentSecond(youTubePlayer: YoutubePlayer, second: Float) { }
    override fun onVideoDuration(youTubePlayer: YoutubePlayer, duration: Float) { }
    override fun onVideoLoadedFraction(youTubePlayer: YoutubePlayer, loadedFraction: Float) { }
    override fun onVideoId(youTubePlayer: YoutubePlayer, videoId: String) { }
}