package com.wepie.wespy.module.voiceroom.video.youtube.views;

import android.app.Dialog;
import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.huiwan.base.util.ClipBoardUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.wespy.R;
import com.wepie.wespy.model.event.RoomOwnerExitEvent;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.net.tcp.packet.VideoRoomPackets;
import com.wepie.wespy.net.tcp.sender.VideoRoomPacketSender;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class YoutubeCopyLinkView extends FrameLayout implements View.OnClickListener {
    private Context mContext;
    private Dialog dialog;
    private TextView confirmTv;
    private TextView cancelTv;
    private EditText copyUrlEditText;
    private ImageView copyUrlIv;
    private boolean isVideoListView;

    public YoutubeCopyLinkView(@NonNull Context context, boolean isVideoListView) {
        super(context);
        mContext = context;
        this.isVideoListView = isVideoListView;
        initView(context);
        initEvent();
    }

    public YoutubeCopyLinkView(@NonNull Context context, @Nullable AttributeSet attrs, boolean isVideoListView) {
        super(context, attrs);
        mContext = context;
        this.isVideoListView = isVideoListView;
        initView(context);
        initEvent();
    }

    public YoutubeCopyLinkView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, boolean isVideoListView) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        this.isVideoListView = isVideoListView;
        initView(context);
        initEvent();
    }

    public YoutubeCopyLinkView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes, boolean isVideoListView) {
        super(context, attrs, defStyleAttr, defStyleRes);
        mContext = context;
        this.isVideoListView = isVideoListView;
        initView(context);
        initEvent();
    }

    private void initView(Context context) {
        this.mContext = context;
        LayoutInflater.from(getContext()).inflate(R.layout.youtube_copy_link_dialog, this);
        cancelTv = findViewById(R.id.youtube_copy_link_cancel);
        confirmTv = findViewById(R.id.youtube_copy_link_confirm);
        copyUrlEditText = findViewById(R.id.youtube_copy_link_url_edittext);
        copyUrlIv = findViewById(R.id.youtube_copy_link_copy_icon);
    }

    private void initEvent() {
        cancelTv.setOnClickListener(this);
        confirmTv.setOnClickListener(this);
        copyUrlIv.setOnClickListener(this);

        copyUrlEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.length() > 0) {
                    copyUrlIv.setVisibility(View.GONE);
                } else {
                    copyUrlIv.setVisibility(View.VISIBLE);
                }
            }
        });
        ViewExKt.postAutoCancel(copyUrlEditText, 100, () -> {
            copyUrlEditText.requestFocus();
            InputMethodManager manager = (InputMethodManager) mContext.getSystemService(Context.INPUT_METHOD_SERVICE);
            if (manager != null) {
                manager.showSoftInput(copyUrlEditText, 0);
            }
        });
    }

    public void setDialog(Dialog dialog) {
        this.dialog = dialog;
    }

    @Override
    public void onClick(View v) {
        if (v == cancelTv) {
            if (dialog != null) {
                dialog.dismiss();
            }
        } else if (v == confirmTv) {
            // 前往视频页
            String copyUrl = copyUrlEditText.getText().toString();
            VideoRoomPacketSender.checkVideoUrl(copyUrl, VoiceRoomService.getInstance().getRid(), new LifeSeqCallback(this) {
                @Override
                public void onSuccess(RspHeadInfo head) {
                    // 本地保存一个有效的url
                    VideoRoomPackets.VideoCheckVideoUrlRsp rsp = (VideoRoomPackets.VideoCheckVideoUrlRsp) head.message;
                    EventDispatcher.postYoutubeLoadUrlEvent(copyUrl, isVideoListView, rsp.getVideoId());
                    dialog.dismiss();
                }

                @Override
                public void onFail(RspHeadInfo head) {
                    ToastUtil.show(head.desc);
                }
            });
        } else if (v == copyUrlIv) {
            // 粘贴链接
            copyUrlEditText.setText(ClipBoardUtil.get(mContext));
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRoomOwnerExit(RoomOwnerExitEvent event) {
        if (dialog != null) {
            dialog.dismiss();
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}
