package com.wepie.wespy.module.voiceroom.video.youtube

import com.wepie.wespy.module.voiceroom.video.youtube.listeners.YoutubePlayerListener

/**
 * Description: Use this interface to control the playback of YouTube videos and to listen to their events.
 * Author: zcs
 * Time: 2022/04/19
 */

interface YoutubePlayer {
    /**
     * Loads and automatically plays the video.
     * @param videoId id of the video
     * @param startSeconds the time from which the video should start playing
     */
    //TODO: Server want to Change seconds to milliseconds
    fun loadVideo(videoId: String, startSeconds:Float)

    /**
     * Loads the video's thumbnail and prepares the player to play the video. Does not automatically play the video.
     * @param videoId id of the video
     * @param startSeconds the time from which the video should start playing
     */
    fun cueVideo(videoId: String, startSeconds:Float)

    fun play()

    fun pause()

    fun mute()

    fun unMute()

    /**
     * @param volumePercent Integer between 0 and 100
     */
    fun setVolume(volumePercent: Int)

    /**
     *
     * @param time The absolute time in seconds to seek to
     */
    fun seekTo(time: Float)

    fun setPlaybackRate(playbackRate: PlayerConstants.PlaybackRate)

    fun addListener(listener: YoutubePlayerListener) : Boolean

    fun removeListener(listener: YoutubePlayerListener) : Boolean
}