package com.wepie.wespy.module.voiceroom.video;

import android.app.Service;
import android.content.Context;
import android.media.AudioManager;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;

import com.huiwan.base.LibBaseUtil;
import com.huiwan.base.util.ScreenUtil;
import com.wepie.liblog.main.HLog;
import com.wepie.wespy.R;
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginRelativeLayout;

/**
 * Created by zcs on 2022/04/15.
 */

public abstract class BaseVideoPlayerView extends PluginRelativeLayout implements VideoGestureRelativeLay.VideoGestureListener {

    private final String TAG = "BaseVideoPlayerView";
    private static int maxVolume = 0;
    protected VideoGestureRelativeLay videoGestureLay;
    private ShowChangeLayout volumeChangeLay;
    private AudioManager mAudioManager;
    private int oldVolume = 0;

    public BaseVideoPlayerView(Context context) {
        super(context);
    }

    public BaseVideoPlayerView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void initData() {
        setClipChildren(false);
    }

    @Override
    protected void initView() {
        LayoutInflater.from(mContext).inflate(R.layout.video_player_view, this);
        videoGestureLay = findViewById(R.id.video_gesture_lay);
        videoGestureLay.setVideoGestureListener(this);
        volumeChangeLay = findViewById(R.id.show_change_lay);
        mAudioManager = (AudioManager) LibBaseUtil.getApplication().getSystemService(Service.AUDIO_SERVICE);
        maxVolume = mAudioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
    }

    public void update(final VideoEmptyDescriptionInfo videoEmptyDes) {
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }


    @Override
    public void onDown(MotionEvent e) {
        oldVolume = mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
    }

    @Override
    public void onEndFF_REW(MotionEvent e) {
    }

    @Override
    public void onVolumeGesture(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
        if (maxVolume != 0) {
            int value = videoGestureLay.getHeight() / maxVolume;
            int newVolume = (int) ((e1.getY() - e2.getY()) / value + oldVolume);
            mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, newVolume, AudioManager.FLAG_REMOVE_SOUND_AND_VIBRATE);
            int volumeProgress = (int) (newVolume / (float) maxVolume * 100);
            volumeChangeLay.setProgress(volumeProgress);
            volumeChangeLay.show();
        }
    }

    public void updateOldVolume() {
        oldVolume = mAudioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
    }

    public void handleVolumeGesture(float startY, float endY) {
        if (maxVolume != 0) {
            int value = ScreenUtil.getScreenWidth() * 9 / 16 / maxVolume;
            int newVolume = (int) ((startY - endY) / value + oldVolume);
            mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, newVolume, AudioManager.FLAG_REMOVE_SOUND_AND_VIBRATE);
            int volumeProgress = (int) (newVolume / (float) maxVolume * 100);
            volumeChangeLay.setProgress(volumeProgress);
            volumeChangeLay.show();
            HLog.d(TAG, HLog.USR, "StartY = {}, endY = {}, maxVolume = {}, value = {},newVolume = {},oldVolume ={}, volumeProgress = {}"
                    , startY, endY, maxVolume, value, newVolume, oldVolume, volumeProgress);
        }
    }

    @Override
    public void onBrightnessGesture(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
    }

    @Override
    public void onFF_REWGesture(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
    }

    @Override
    public void onSingleTapGesture(MotionEvent e) {
    }

    @Override
    public void onDoubleTapGesture(MotionEvent e) {
    }
}