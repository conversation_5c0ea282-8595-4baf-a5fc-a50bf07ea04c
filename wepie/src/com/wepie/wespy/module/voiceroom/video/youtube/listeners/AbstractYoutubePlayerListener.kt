package com.wepie.wespy.module.voiceroom.video.youtube.listeners

import com.wepie.wespy.module.voiceroom.video.youtube.PlayerConstants
import com.wepie.wespy.module.voiceroom.video.youtube.YoutubePlayer


/**
 * Extend this class if you want to implement only some of the methods of [YouTubePlayerListener]
 */
abstract class AbstractYoutubePlayerListener : YoutubePlayerListener {
    override fun onReady(youTubePlayer: YoutubePlayer) {}
    override fun onStateChange(youTubePlayer: YoutubePlayer, state: PlayerConstants.PlayerState) {}
    override fun onPlaybackQualityChange(youTubePlayer: YoutubePlayer, playbackQuality: PlayerConstants.PlaybackQuality) {}
    override fun onPlaybackRateChange(youTubePlayer: YoutubePlayer, playbackRate: PlayerConstants.PlaybackRate) {}
    override fun onError(youTubePlayer: YoutubePlayer, error: PlayerConstants.PlayerError) {}
    override fun onApiChange(youTubePlayer: YoutubePlayer) {}
    override fun onCurrentSecond(youTubePlayer: YoutubePlayer, second: Float) {}
    override fun onVideoDuration(youTubePlayer: YoutubePlayer, duration: Float) {}
    override fun onVideoLoadedFraction(youTubePlayer: YoutubePlayer, loadedFraction: Float) {}
    override fun onVideoId(youTubePlayer: YoutubePlayer, videoId: String) {}
}
