package com.wepie.wespy.module.voiceroom.video.youtube.views

import android.content.Context
import android.util.AttributeSet
import android.view.View
import com.wepie.wespy.R
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher
import com.wepie.wespy.module.voiceroom.video.event.YoutubeRiskSwitchChanged
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class YoutubeMaintenanceView(
    context: Context,
    attrs: AttributeSet? = null
) : SixteenByNineFrameLayout(context, attrs) {

    init {
        View.inflate(context, R.layout.youtube_maintenance_view, this)
        visibility = GONE
        setOnClickListener { v -> } // 空实现， 用以屏蔽下方的点击事件
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun updateRiskChangedPush(event: YoutubeRiskSwitchChanged) {
        visibility = if (event.hasRisk) {
            EventDispatcher.postExitFullScreenEvent(true) // 退出全屏并且暂停
            VISIBLE
        } else {
            GONE
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }
}