package com.wepie.wespy.module.voiceroom.video.youtube.ui.utils

import android.annotation.SuppressLint
import kotlin.math.ceil

object TimeUtilities {

    /**
     * Transform the time in seconds in a string with format "M:SS".
     */
    @SuppressLint("DefaultLocale")
    @JvmStatic
    fun formatTime(timeInSeconds: Float): String {
        val timeInSecondsByCeil = ceil(timeInSeconds.toDouble())
        val hours = (timeInSecondsByCeil / 3600).toInt()
        val minutes = ((timeInSecondsByCeil - hours * 3600) / 60).toInt()
        val seconds = ((timeInSecondsByCeil - hours * 3600) % 60).toInt()
        if(hours == 0) return String.format("%02d:%02d", minutes, seconds)
        return String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }
}
