package com.wepie.wespy.module.voiceroom.video.youtube.utils;

import android.content.Context;
import android.content.DialogInterface;
import android.view.Window;

import com.huiwan.constants.GameType;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog;
import com.wepie.wespy.helper.dialog.DialogCallback;
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil;
import com.wepie.wespy.module.voiceroom.dataservice.event.EventDispatcher;
import com.wepie.wespy.module.voiceroom.main.bottom.ShowVoiceRoomItemH5WebView;
import com.wepie.wespy.module.voiceroom.video.event.YoutubeRiskSwitchChanged;
import com.wepie.wespy.module.voiceroom.video.youtube.YoutubeVideoListView;
import com.wepie.wespy.module.voiceroom.video.youtube.views.YoutubeCopyLinkView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Created by zcs on 2022/04/15
 */
public class YoutubeDialog {
    public static final String TAG = "YoutubeDialog";
    private final Context mContext;
    private BaseFullScreenDialog dialog;

    public YoutubeDialog(Context context) {
        this.mContext = context;
    }

    public void showActivityH5WebDialogWithHeightAndUrl(int height, String h5Url) {
        BaseFullScreenDialog dialog = createDialog();
        updateWebViewInfo(h5Url, height, dialog, false, "");
    }

    public void showActivityH5WebDialogWithHeightAndUrl(int height, String h5Url, String validVideoId) {
        BaseFullScreenDialog dialog = createDialog();
        updateWebViewInfo(h5Url, height, dialog, true, validVideoId);
    }

    public void showYoutubeCopyLinkDialog(boolean isVideoListView) {
        YoutubeCopyLinkView youtubeCopyLinkView = new YoutubeCopyLinkView(mContext, isVideoListView);
        BaseFullScreenDialog dialog = createDialog();
        dialog.setContentView(youtubeCopyLinkView);
        dialog.initCenterDialog();
        dialog.setCanceledOnTouchOutside(true);

        youtubeCopyLinkView.setDialog(dialog);
        dialog.show();
    }

    public void showYoutubeVideoListDialog() {
        YoutubeVideoListView youtubeVideoListView = new YoutubeVideoListView(mContext);
        BaseFullScreenDialog dialog = createDialog();
        dialog.setContentView(youtubeVideoListView);
        dialog.initBottomDialog();
        dialog.setCanceledOnTouchOutside(true);

        youtubeVideoListView.setCallback(new DialogCallback() {
            @Override
            public void onCancel() {
                dialog.dismiss();
            }

            @Override
            public void onEnter() {

            }
        });
        dialog.show();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updateRiskChangedPush(YoutubeRiskSwitchChanged event) {
        if (event.hasRisk) {
            dialog.dismiss();
        }
    }

    private BaseFullScreenDialog createDialog() {
        BaseFullScreenDialog dialog = new BaseFullScreenDialog(mContext, R.style.DialogActivityTheme);
        if (!EventBus.getDefault().isRegistered(YoutubeDialog.this)) {
            EventBus.getDefault().register(YoutubeDialog.this);
        }
        dialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if (EventBus.getDefault().isRegistered(YoutubeDialog.this)) {
                    EventBus.getDefault().unregister(YoutubeDialog.this);
                }
            }
        });
        this.dialog = dialog;
        return dialog;
    }

    // isUpdateValidUrl，粘贴链接到视频详情页，需保存url
    private void updateWebViewInfo(String h5Url, int height, BaseFullScreenDialog dialog, boolean isUpdateValidUrl, String validVideoId) {
        ShowVoiceRoomItemH5WebView showInfo = new ShowVoiceRoomItemH5WebView(mContext, GameType.GAME_TYPE_VIDEO_ROOM);
        showInfo.setWebViewInfo(h5Url, height);
        if (isUpdateValidUrl) {
            showInfo.updateValidUrlList(validVideoId);
        }
        final ProgressDialogUtil progressDialogUtil = new ProgressDialogUtil();
        progressDialogUtil.showLoading(mContext, null, true);
        showInfo.setProgressDialogUtil(progressDialogUtil);
        showInfo.hideWebDialogViewBackground();
        showInfo.webDialogShow();
        dialog.setContentView(showInfo);
        dialog.initBottomDialog();
        dialog.setCanceledOnTouchOutside(true);
        Window window = dialog.getWindow();
        if (window != null) {
            window.setBackgroundDrawableResource(R.color.transparent);
        }
        showInfo.setCallback(new DialogCallback() {
            @Override
            public void onCancel() {
                progressDialogUtil.hideLoading();
                dialog.dismiss();
                EventDispatcher.postYoutubeNetDialogCloseEvent();
            }

            @Override
            public void onEnter() {

            }
        });
        dialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialog) {
                //sync会同步当前状态，但是操作的人员会错过部分播放时间,暂时直接回复播放状态
                //EventDispatcher.postVideoPlaySyncEvent();
                progressDialogUtil.hideLoading();
                EventDispatcher.postYoutubeNetDialogCloseEvent();
            }
        });
        dialog.show();
    }
}
