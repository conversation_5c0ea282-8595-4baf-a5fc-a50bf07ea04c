package com.wepie.wespy.module.voiceroom.video.youtube.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.wepie.wespy.R;

public class YoutubeTitleView extends ConstraintLayout implements View.OnClickListener {
    private Context mContext;

    public YoutubeTitleView(@NonNull Context context) {
        super(context);
        mContext = context;
        initView(context);
        initEvent();
    }

    public YoutubeTitleView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        initView(context);
        initEvent();
    }

    public YoutubeTitleView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        initView(context);
        initEvent();
    }

    public YoutubeTitleView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        mContext = context;
        initView(context);
        initEvent();
    }

    private void initView(Context context) {
        this.mContext = context;
        LayoutInflater.from(getContext()).inflate(R.layout.video_room_youtube_title, this);
    }

    private void initEvent() {
    }

    @Override
    public void onClick(View v) {
    }
}
