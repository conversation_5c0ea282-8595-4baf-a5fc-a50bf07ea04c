package com.wepie.wespy.module.voiceroom.video.youtube;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;

import com.huiwan.base.str.ResUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.wepie.wespy.R;
import com.wepie.wespy.module.voiceroom.video.BaseVideoEmptyView;
import com.wepie.wespy.module.voiceroom.video.VideoEmptyDescriptionInfo;
import com.wepie.wespy.module.voiceroom.video.youtube.utils.VideoRoomUtils;
import com.wepie.wespy.module.voiceroom.video.youtube.utils.YoutubeDialog;
import com.wepie.wespy.utils.ViewOnClickListenerUtils;

/**
 * Created by zcs on 2022/04/15.
 */
public class YoutubeVideoEmptyView extends BaseVideoEmptyView {
    private String TAG = "YoutubeVideoEmptyView";

    public YoutubeVideoEmptyView(Context context) {
        super(context);
        update(getYoutubeVideoEmptyDescription());
        setSelectVideoClickEvent();
    }

    public YoutubeVideoEmptyView(Context context, AttributeSet attrs) {
        super(context, attrs);
        update(getYoutubeVideoEmptyDescription());
        setSelectVideoClickEvent();
    }

    private void setSelectVideoClickEvent() {
        if (selectVideoTv != null) {
            selectVideoTv.setOnClickListener(new ViewOnClickListenerUtils() {
                @Override
                protected void onProhibitRepeatClick(View v) {
                    if (VideoRoomUtils.isHavePermissionAddVideo()) {
                        YoutubeDialog youtubeDialog = new YoutubeDialog(selectVideoTv.getContext());
                        // 按照设计稿比例设置弹窗高度
                        youtubeDialog.showActivityH5WebDialogWithHeightAndUrl(ScreenUtil.getScreenHeight() * 600 / 812,
                                "https://m.youtube.com/");
                    } else {
                        ToastUtil.show(ResUtil.getStr(R.string.video_room_add_video_tips));
                    }
                }
            });
        }
    }

    private VideoEmptyDescriptionInfo getYoutubeVideoEmptyDescription() {
        return new VideoEmptyDescriptionInfo(R.drawable.youtube_title_image,
                ResUtil.getStr(R.string.video_room_choose_youtube_video_description),
                ResUtil.getStr(R.string.video_room_add_video));
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    }

}