package com.wepie.wespy.module.voiceroom.video.youtube.utils;

import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.huiwan.user.LoginHelper;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.cn.TrackScreenName;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackEvent;
import com.wepie.wespy.model.entity.RoomInfo;
import com.wepie.wespy.model.entity.voiceroom.VideoInfo;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.fdiscover.event.TranslateEvent;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.net.tcp.packet.VideoRoomPackets;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class VideoRoomUtils {
    public static List<VideoInfo> getVideoPlayList(RspHeadInfo rspHeadInfo) {
        List<VideoInfo> videoInfoList = new ArrayList<>();
        if(rspHeadInfo.message != null) {
            VideoRoomPackets.VideoGetPlayListRsp videoListRsp = (VideoRoomPackets.VideoGetPlayListRsp) rspHeadInfo.message;
            if ( videoListRsp.getPlayListList() != null) {
                int videoInfoListLen = videoListRsp.getPlayListList().size();
                if (videoInfoListLen > 0) {
                    for (int i = 0; i < videoInfoListLen; i++) {
                        videoInfoList.add(getVideoInfo(videoListRsp.getPlayListList().get(i)));
                    }
                }
            }
        }
        return videoInfoList;
    }

    public static VideoInfo getVideoInfo(VideoRoomPackets.VideoInfo tempVideoInfo) {
        VideoInfo videoInfo = new VideoInfo();
        videoInfo.seq_id = tempVideoInfo.getSeqId();
        videoInfo.source = tempVideoInfo.getSource();
        videoInfo.id = tempVideoInfo.getId();
        videoInfo.url = tempVideoInfo.getUrl();
        videoInfo.title = tempVideoInfo.getTitle();
        videoInfo.head_image = tempVideoInfo.getHeadImage();
        videoInfo.duration = tempVideoInfo.getDuration();
        videoInfo.append_uid = tempVideoInfo.getAppendUid();
        videoInfo.live_broadcast = tempVideoInfo.getLiveBroadcast();
        return videoInfo;
    }

    public static boolean isHavePermissionAddVideo() {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getLiveData().getValue();
        if (roomInfo != null) {
            boolean isOwnerOrAdmin = roomInfo.isSelfAdminOrOwner();
            boolean isInSeat = roomInfo.isInSeat(LoginHelper.getLoginUid());
            return isOwnerOrAdmin || isInSeat;
        } else {
            return false;
        }
    }

    public static boolean isHavePermissionDeleteVideo(int addUid) {
        VoiceRoomInfo roomInfo = VoiceRoomService.getInstance().getLiveData().getValue();
        if (roomInfo != null) {
            boolean isOwnerOrAdmin = roomInfo.isSelfAdminOrOwner();
            return isOwnerOrAdmin || addUid == LoginHelper.getLoginUid();
        } else {
            return false;
        }
    }

    public static void appClickVideoRoomCopyLinkTrack(String screen, String rid) {
        Map<String, Object> trackMap = new HashMap<>();
        trackMap.put(TrackEvent.ROOM_ID, rid);
        TrackUtil.appClick(screen, TrackButtonName.COPY_LINK, trackMap);
    }
}
