package com.wepie.wespy.module.voiceroom.video.youtube.views

import android.content.Context
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.annotation.LayoutRes
import com.wepie.wespy.module.voiceroom.main.RoomSetVoiceView
import com.wepie.wespy.module.voiceroom.main.VideoVolumeChangedEvent
import com.wepie.wespy.module.voiceroom.video.youtube.PlayerConstants
import com.wepie.wespy.module.voiceroom.video.youtube.YoutubePlayer
import com.wepie.wespy.module.voiceroom.video.youtube.listeners.AbstractYoutubePlayerListener
import com.wepie.wespy.module.voiceroom.video.youtube.listeners.YoutubePlayerCallback
import com.wepie.wespy.module.voiceroom.video.youtube.listeners.YoutubePlayerListener
import com.wepie.wespy.module.voiceroom.video.youtube.options.IFramePlayerOptions
import com.wepie.wespy.module.voiceroom.video.youtube.utils.NetworkListener
import com.wepie.wespy.module.voiceroom.video.youtube.utils.PlaybackResumer
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * Legacy internal implementation of YouTubePlayerView. The user facing YouTubePlayerView delegates
 * most of its actions to this one.
 */
internal class LegacyYoutubePlayerView(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : SixteenByNineFrameLayout(context, attrs, defStyleAttr) {

    constructor(context: Context) : this(context, null, 0)
    constructor(context: Context, attrs: AttributeSet? = null) : this(context, attrs, 0)

    internal val youTubePlayer: WebViewYoutubePlayer = WebViewYoutubePlayer(context.applicationContext)

    private val networkListener = NetworkListener()
    private val playbackResumer = PlaybackResumer()

    internal var isYouTubePlayerReady = false
    private var initialize = { }
    private val youTubePlayerCallbacks = HashSet<YoutubePlayerCallback>()

    internal var canPlay = true
        private set

    var isUsingCustomUi = false
        private set

    init {
        initYoutubePlayer()
    }

    private fun initYoutubePlayer() {
        addView(
            youTubePlayer, 0,
            LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
        )
        youTubePlayer.addListener(playbackResumer)

        // stop playing if the user loads a video but then leaves the app before the video starts playing.
        youTubePlayer.addListener(object : AbstractYoutubePlayerListener() {
            override fun onStateChange(
                youTubePlayer: YoutubePlayer,
                state: PlayerConstants.PlayerState
            ) {
                if (state == PlayerConstants.PlayerState.PLAYING && !isEligibleForPlayback())
                    youTubePlayer.pause()
            }
        })

        youTubePlayer.addListener(object : AbstractYoutubePlayerListener() {
            override fun onReady(youTubePlayer: YoutubePlayer) {
                isYouTubePlayerReady = true

                youTubePlayerCallbacks.forEach { it.onYouTubePlayer(youTubePlayer) }
                youTubePlayerCallbacks.clear()

                youTubePlayer.removeListener(this)
                youTubePlayer.setVolume(RoomSetVoiceView.getVideoVolumePercent())
            }
        })

        networkListener.onNetworkAvailable = {
            if (!isYouTubePlayerReady)
                initialize()
            else
                playbackResumer.resume(youTubePlayer)
        }
    }

    /**
     * Initialize the player. You must call this method before using the player.
     * @param youTubePlayerListener listener for player events
     * @param handleNetworkEvents if set to true a broadcast receiver will be registered and network events will be handled automatically.
     * If set to false, you should handle network events with your own broadcast receiver.
     * @param playerOptions customizable options for the embedded video player, can be null.
     */
    fun initialize(
        youTubePlayerListener: YoutubePlayerListener,
        handleNetworkEvents: Boolean,
        playerOptions: IFramePlayerOptions
    ) {
        if (isYouTubePlayerReady)
            throw IllegalStateException("This YouTubePlayerView has already been initialized.")

        if (handleNetworkEvents)
            context.registerReceiver(
                networkListener,
                IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
            )

        initialize = {
            youTubePlayer.initialize({ it.addListener(youTubePlayerListener) }, playerOptions)
        }

        if (!handleNetworkEvents) {
            initialize()
        }
    }

    /**
     * Initialize the player.
     * @param handleNetworkEvents if set to true a broadcast receiver will be registered and network events will be handled automatically.
     * If set to false, you should handle network events with your own broadcast receiver.
     *
     * @see LegacyYoutubePlayerView.initialize
     */
    fun initialize(youTubePlayerListener: YoutubePlayerListener, handleNetworkEvents: Boolean) =
        initialize(youTubePlayerListener, handleNetworkEvents, IFramePlayerOptions.default)

    /**
     * Initialize the player. Network events are automatically handled by the player.
     * @param youTubePlayerListener listener for player events
     *
     * @see LegacyYoutubePlayerView.initialize
     */
    fun initialize(youTubePlayerListener: YoutubePlayerListener) =
        initialize(youTubePlayerListener, true)

    /**
     * @param youTubePlayerCallback A callback that will be called when the YouTubePlayer is ready.
     * If the player is ready when the function is called, the callback is called immediately.
     * This function is called only once.
     */
    fun getYouTubePlayerWhenReady(youTubePlayerCallback: YoutubePlayerCallback) {
        if (isYouTubePlayerReady)
            youTubePlayerCallback.onYouTubePlayer(youTubePlayer)
        else
            youTubePlayerCallbacks.add(youTubePlayerCallback)
    }

    /**
     * Use this method to replace the default Ui of the player with a custom Ui.
     *
     * You will be responsible to manage the custom Ui from your application,
     * the default controller obtained through [LegacyYoutubePlayerView.getPlayerUiController] won't be available anymore.
     * @param layoutId the ID of the layout defining the custom Ui.
     * @return The inflated View
     */
    fun inflateCustomPlayerUi(@LayoutRes layoutId: Int): View {
        if (childCount > 1) {
            removeViews(1, childCount - 1)
        }

        isUsingCustomUi = true
        return View.inflate(context, layoutId, this)
    }

    fun setCustomPlayerUi(view: View) {
        if (childCount > 1) {
            removeViews(1, childCount - 1)
        }

        isUsingCustomUi = true
        addView(view)
    }

    /**
     * Call this method before destroying the host Fragment/Activity, or register this View as an observer of its host lifecycle
     */
    fun release() {
        removeView(youTubePlayer)
        youTubePlayer.removeAllViews()
        youTubePlayer.destroy()
        try {
            context.unregisterReceiver(networkListener)
        } catch (ignore: Exception) {
        }
    }

    internal fun onResume() {
        playbackResumer.onLifecycleResume()
        canPlay = true
    }

    internal fun onStop() {
        youTubePlayer.pause()
        playbackResumer.onLifecycleStop()
        canPlay = false
    }

    /**
     * Checks whether the player is in an eligible state for playback in
     * respect of the {@link WebViewYouTubePlayer#isBackgroundPlaybackEnabled}
     * property.
     */
    internal fun isEligibleForPlayback(): Boolean {
        return canPlay || youTubePlayer.isBackgroundPlaybackEnabled
    }

    /**
     * Don't use this method if you want to publish your app on the PlayStore. Background playback is against YouTube terms of service.
     */
    fun enableBackgroundPlayback(enable: Boolean) {
        youTubePlayer.isBackgroundPlaybackEnabled = enable
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        if (childCount < 1 || getChildAt(0) != youTubePlayer) {
            isYouTubePlayerReady = false
            initYoutubePlayer()
        }
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        release()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onVolumeChanged(volume: VideoVolumeChangedEvent) {
        youTubePlayer.setVolume(volume.videoVolumePercent)
    }
}
