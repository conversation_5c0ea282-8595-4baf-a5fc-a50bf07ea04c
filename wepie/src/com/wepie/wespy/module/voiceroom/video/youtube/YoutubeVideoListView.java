package com.wepie.wespy.module.voiceroom.video.youtube;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.ToastUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.libtcp.base.HWTCPSocketThread;
import com.huiwan.libtcp.callback.LifeSeqCallback;
import com.huiwan.libtcp.huiwan.RspHeadInfo;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.wespy.R;
import com.wepie.wespy.helper.dialog.DialogCallback;
import com.wepie.wespy.model.entity.voiceroom.VideoInfo;
import com.wepie.wespy.model.entity.voiceroom.VideoPlayInfo;
import com.wepie.wespy.model.event.RoomOwnerExitEvent;
import com.wepie.wespy.model.event.VideoPlayStateChangeEvent;
import com.wepie.wespy.model.event.VideoPlaySyncEvent;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.video.YoutubeVideoListContentView;
import com.wepie.wespy.module.voiceroom.video.event.VideoPlayListUpdateEvent;
import com.wepie.wespy.module.voiceroom.video.event.YoutubeLoadUrlEvent;
import com.wepie.wespy.module.voiceroom.video.youtube.utils.VideoRoomUtils;
import com.wepie.wespy.module.voiceroom.video.youtube.utils.YoutubeDialog;
import com.wepie.wespy.module.voiceroom.video.youtube.views.YoutubeTitleView;
import com.wepie.wespy.module.voiceroom.video.youtube.views.YoutubeVideoListAdapter;
import com.wepie.wespy.net.tcp.handler.VideoRoomHandler;
import com.wepie.wespy.net.tcp.sender.VideoRoomPacketSender;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

public class YoutubeVideoListView extends ConstraintLayout implements View.OnClickListener {
    private static final String TAG = "YoutubeVideoListView";

    private Context mContext;
    private DialogCallback dialogCallback;
    private YoutubeTitleView webViewTitleLay;
    private YoutubeVideoListContentView videoListContentLay;
    private TextView videoListTitleTv;
    private ImageView closeIv;
    private ImageView backIv;
    private TextView copyLinkTv;
    private ConstraintLayout addVideoLay;
    private RecyclerView videoListRecyclerView;
    private ConstraintLayout emptyVideoListLay;
    private YoutubeVideoListAdapter videoListAdapter;
    private TextView videoNumTv;
    private List<VideoInfo> videoList = new ArrayList<>();
    private int curSeqId;
    private long curVideoPosition;
    private VideoInfo curVideoInfo;

    private int maxVideoListNum = 20;

    public YoutubeVideoListView(@NonNull Context context) {
        super(context);
        mContext = context;
        init();
    }

    public YoutubeVideoListView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mContext = context;
        init();
    }

    public YoutubeVideoListView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
        init();
    }

    public YoutubeVideoListView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        mContext = context;
        init();
    }

    private void init() {
        initView();
        initData();
        initEvent();
    }

    private void initView() {
        inflate(mContext, R.layout.youtube_video_list, this);
        webViewTitleLay = findViewById(R.id.youtube_video_list_title_lay);
        videoListContentLay = findViewById(R.id.youtube_video_list_content_lay);
        videoNumTv = findViewById(R.id.video_list_content_num_lay);
        videoListTitleTv = findViewById(R.id.web_view_youtube_title_tv);
        backIv = findViewById(R.id.web_view_back_iv);
        closeIv = findViewById(R.id.web_view_close_iv);
        addVideoLay = findViewById(R.id.youtube_video_list_add_video_lay);
        copyLinkTv = findViewById(R.id.web_view_copy_link_tv);
        videoListRecyclerView = findViewById(R.id.video_list_content_recycle_lay);
        emptyVideoListLay = findViewById(R.id.youtube_video_list_empty_lay);
    }

    private void initData() {
        VideoRoomPacketSender.getVideoPlayList(VoiceRoomService.getInstance().getRid(), new LifeSeqCallback(this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                videoList = VideoRoomUtils.getVideoPlayList(head);
                videoListAdapter = new YoutubeVideoListAdapter(mContext, videoList);
                videoListRecyclerView.setAdapter(videoListAdapter);
                maxVideoListNum = ConfigHelper.getInstance().getVoiceRoomConfig()
                        .getVideoRoomConfig().getMaxAppendVideoNum();
                videoNumTv.setText(String.format("%d/%d", videoList.size(), maxVideoListNum));
                updateCurPlayVideoInfo();
                updateViewIfEmptyVideoList();
            }

            @Override
            public void onFail(RspHeadInfo head) {

            }
        });

        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(mContext,
                LinearLayoutManager.VERTICAL, false);
        videoListRecyclerView.setLayoutManager(linearLayoutManager);
        videoListRecyclerView.setItemAnimator(new DefaultItemAnimator());

        videoListTitleTv.setText(getResources().getText(R.string.video_room_play_list));
        backIv.setVisibility(GONE);

        if (!VideoRoomUtils.isHavePermissionAddVideo()) {
            addVideoLay.setVisibility(GONE);
        }
    }

    private void initEvent() {
        closeIv.setOnClickListener(this);
        addVideoLay.setOnClickListener(this);
        copyLinkTv.setOnClickListener(this);
    }

    public void setCallback(DialogCallback callback) {
        this.dialogCallback = callback;
    }

    @Override
    public void onClick(View v) {
        if (v == closeIv) {
            dialogCallback.onCancel();
        } else if (v == addVideoLay) {
            if (videoList != null && videoList.size() >= maxVideoListNum) {
                ToastUtil.show(R.string.video_list_num_limited);
                return;
            }
            // 添加视频，跳转到webview
            YoutubeDialog youtubeDialog = new YoutubeDialog(mContext);
            // 按照设计稿比例设置弹窗高度
            youtubeDialog.showActivityH5WebDialogWithHeightAndUrl(ScreenUtil.getScreenHeight() * 600 / 812,
                    "https://m.youtube.com/");
        } else if (v == copyLinkTv) {
            // 跳转到粘贴链接
            YoutubeDialog youtubeDialog = new YoutubeDialog(mContext);
            youtubeDialog.showYoutubeCopyLinkDialog(true);
            VideoRoomUtils.appClickVideoRoomCopyLinkTrack(TrackScreenName.VIDEO_ROOM_PLAY_LIST,
                    String.valueOf(VoiceRoomService.getInstance().getRid()));
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if(!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if(EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onVideoPlaySync(VideoPlaySyncEvent event) {
        updateCurPlayVideoInfo();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onVideoPlayStateChange(VideoPlayStateChangeEvent event) {
        updateCurPlayVideoInfo();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUpdatePlayList(VideoPlayListUpdateEvent event) {
        VideoRoomPacketSender.getVideoPlayList(VoiceRoomService.getInstance().getRid(), new LifeSeqCallback(this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                videoList = VideoRoomUtils.getVideoPlayList(head);
                if (videoListAdapter != null) {
                    videoListAdapter.update(videoList);
                    maxVideoListNum = ConfigHelper.getInstance().getVoiceRoomConfig()
                            .getVideoRoomConfig().getMaxAppendVideoNum();
                    videoNumTv.setText(String.format("%d/%d", videoList.size(), maxVideoListNum));
                    updateViewIfEmptyVideoList();
                    updateCurPlayVideoInfo();
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {

            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onLoadCopyLinkWebView(YoutubeLoadUrlEvent event) {
        if (!event.isVideoListView) {
            return;
        }
        YoutubeDialog youtubeDialog = new YoutubeDialog(mContext);
        // 按照设计稿比例设置弹窗高度
        youtubeDialog.showActivityH5WebDialogWithHeightAndUrl(ScreenUtil.getScreenHeight() * 600 / 812,
                event.getUrl(), event.videoId);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onRoomOwnerExit(RoomOwnerExitEvent event) {
        dialogCallback.onCancel();
    }

    private void updateCurPlayVideoInfo() {
        VideoRoomPacketSender.getSyncVideoInfo(VoiceRoomService.getInstance().getRid(), new HWTCPSocketThread.WriteCallback() {
            @Override
            public void onWriteSuccess() {

            }

            @Override
            public void onWriteFailed() {

            }
        }, new LifeSeqCallback(this) {
            @Override
            public void onSuccess(RspHeadInfo head) {
                VideoPlayInfo videoPlayInfo = VideoRoomHandler.updateVideoPlayInfo(head);
                if (videoPlayInfo.videoInfo != null) {
                    curSeqId = videoPlayInfo.videoInfo.seq_id;
                    curVideoPosition = videoPlayInfo.position;
                    curVideoInfo = videoPlayInfo.videoInfo;
                    if (videoListAdapter != null) {
                        videoListAdapter.update(curSeqId, curVideoPosition, curVideoInfo);
                    }
                }
            }

            @Override
            public void onFail(RspHeadInfo head) {

            }
        });
    }

    private void updateViewIfEmptyVideoList() {
        if (videoList != null && videoList.size() == 0) {
            emptyVideoListLay.setVisibility(VISIBLE);
            videoNumTv.setVisibility(GONE);
        } else {
            emptyVideoListLay.setVisibility(GONE);
            videoNumTv.setVisibility(VISIBLE);
        }
    }
}
