package com.wepie.wespy.module.voiceroom.video.youtube.utils

import android.content.Context
import android.net.ConnectivityManager
import com.huiwan.base.LibBaseUtil
import com.wepie.liblog.main.FLog
import java.io.BufferedReader
import java.io.InputStream
import java.io.InputStreamReader

internal object Utils {
    fun isOnline(): Boolean {
        val context = LibBaseUtil.getApplication()
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkInfo = connectivityManager.activeNetworkInfo
        return networkInfo != null && networkInfo.isConnected
    }

    fun readHTMLFromUTF8File(inputStream: InputStream): String {
        try {
            val bufferedReader = BufferedReader(InputStreamReader(inputStream, "utf-8"))

            var currentLine: String? = bufferedReader.readLine()
            val sb = StringBuilder()

            while (currentLine != null) {
                sb.append(currentLine).append("\n")
                currentLine = bufferedReader.readLine()
            }

            return sb.toString()
        } catch (e: Exception) {
            FLog.e(e)
            return ""
        } finally {
            inputStream.close()
        }
    }
}