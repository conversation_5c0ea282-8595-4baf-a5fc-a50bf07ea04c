package com.wepie.wespy.module.voiceroom.galaWeb;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;
import android.os.CountDownTimer;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.huiwan.base.ktx.ViewExtKt;
import com.huiwan.base.util.NinePatchUtil;
import com.huiwan.base.util.ScreenUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.base.util.TimeUtil;
import com.huiwan.widget.MarqueeTextView;
import com.huiwan.widget.rv.RVHolder;
import com.wejoy.weplay.ex.cancellable.CountDownTimerExKt;
import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.libimageloader.ImageLoadInfo;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;


public class GalaWebAdapter {

    public View createView(@NonNull ViewGroup container) {
        return LayoutInflater.from(container.getContext()).inflate(R.layout.room_gala_web_small_view_item, container, false);
    }

    public void bindView(@NonNull View view, VoiceRoomInfo.OperatingActivityInfo info) {
        ViewHolder viewHolder = new GalaWebAdapter.ViewHolder(view);
        viewHolder.bindData(info);
    }

    private static class ViewHolder extends RVHolder {

        private static final int TYPE_CONTENT_NULL = 1;
        private static final int TYPE_CONTENT_COUNTDOWN = 2;
        private static final int TYPE_CONTENT_PROGRESS_NUM = 3;
        private static final int TYPE_CONTENT_PROGRESS_BAR = 4;

        private static final int CONTENT_HEIGHT_COUNTDOWN_DP = 16;
        private static final int CONTENT_HEIGHT_PROGRESS_NUM_DP = 14;
        private static final int CONTENT_HEIGHT_PROGRESS_BAR_DP = 8;
        private static final int PROGRESS_BAR_WIDTH_MIN_DP = 2;
        private static final int PROGRESS_BAR_NUM_WIDTH_MIN_DP = 8;
        private static final int PROGRESS_BAR_WIDTH_MAX_DP = 48;
        private static final int PROGRESS_NUM_BAR_HEIGHT_DP = 10;
        private static final int PROGRESS_BAR_HEIGHT_DP = 4;
        private static final int CONTENT_TEXT_SIZE_COUNT_DOWN = 10;
        private static final int CONTENT_TEXT_SIZE_PROGRESS_NUM = 8;


        private ImageView galaWebIv;
        private View contentLay;
        private ImageView progressBar;
        private TextView galaWebCountDownTV;
        private TextView galaWebProgressTV;
        private TextView galaWebNameTV;

        private CountDownTimer timer;

        public ViewHolder(View view) {
            super(view);
            progressBar = view.findViewById(R.id.gala_web_progress_bar);
            galaWebIv = view.findViewById(R.id.gala_web_iv);
            galaWebCountDownTV = view.findViewById(R.id.gala_web_countdown_text);
            galaWebProgressTV = view.findViewById(R.id.gala_web_progress_text);
            contentLay = view.findViewById(R.id.gala_web_content_lay);
            galaWebNameTV = view.findViewById(R.id.gala_web_name_text);
        }

        public void bindData(VoiceRoomInfo.OperatingActivityInfo info) {
            WpImageLoader.load(info.pictureUrl, galaWebIv, ImageLoadInfo.newInfo().width(ScreenUtil.dip2px(64)));
            resetUI();
            long lastSeconds = info.countDownEndSec - TimeUtil.getElapsedServerTime() / 1000;
            // 倒计时
            if (lastSeconds > 0) {
                contentLay.setVisibility(View.VISIBLE);
                ViewExtKt.setLayoutHeightPx(contentLay, ScreenUtil.dip2px(CONTENT_HEIGHT_COUNTDOWN_DP));
                galaWebCountDownTV.setText(TimeUtil.formTotalTime((int) lastSeconds));
                startTimer(lastSeconds);
                galaWebCountDownTV.setVisibility(View.VISIBLE);
            } else if (info.processNum > 0) {
                contentLay.setVisibility(View.VISIBLE);

                if (!TextUtil.isEmpty(info.progressBarText)) {
                    // 进度条有数字
                    updateProgressBar(info.processNum, TYPE_CONTENT_PROGRESS_NUM);
                    ViewExtKt.setLayoutHeightPx(contentLay, ScreenUtil.dip2px(CONTENT_HEIGHT_PROGRESS_NUM_DP));

                    galaWebProgressTV.setText(info.progressBarText);
                    galaWebProgressTV.setVisibility(View.VISIBLE);
                } else {
                    // 进度条没有数字
                    updateProgressBar(info.processNum, TYPE_CONTENT_PROGRESS_BAR);
                    ViewExtKt.setLayoutHeightPx(contentLay, ScreenUtil.dip2px(CONTENT_HEIGHT_PROGRESS_BAR_DP));
                }
            } else if (!info.displayText.isEmpty()) {
                contentLay.setVisibility(View.VISIBLE);
                galaWebNameTV.setText(info.displayText);
                ViewExtKt.setLayoutHeightPx(contentLay, ScreenUtil.dip2px(CONTENT_HEIGHT_COUNTDOWN_DP));
                galaWebNameTV.setVisibility(View.VISIBLE);
            }
        }

        private void startTimer(long lastSeconds) {
            if (timer != null) {
                timer.cancel();
            }
            if (lastSeconds <= 0) {
                return;
            }
            timer = new CountDownTimer(lastSeconds * 1000L, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    galaWebCountDownTV.setText(TimeUtil.formTotalTime((int) (millisUntilFinished / 1000)));
                }

                @Override
                public void onFinish() {
                    galaWebCountDownTV.setText(TimeUtil.formTotalTime(0));
                }
            };
            timer.start();
            CountDownTimerExKt.autoCancel(timer, ViewExKt.toLife(itemView));
        }

        private void updateProgressBar(int progress, int type) {
            if (type == TYPE_CONTENT_PROGRESS_NUM) {
                progressBar.setVisibility(View.VISIBLE);
                float width = ScreenUtil.dip2px(PROGRESS_BAR_NUM_WIDTH_MIN_DP) + ScreenUtil.dip2px(progress / 100f * (PROGRESS_BAR_WIDTH_MAX_DP - PROGRESS_BAR_NUM_WIDTH_MIN_DP));
                ViewExtKt.setLayoutWidthPx(progressBar, (int) width);

                Bitmap bitmap = BitmapFactory.decodeResource(progressBar.getContext().getResources(), R.drawable.gala_web_progress_with_text);
                int targetHeight = ScreenUtil.dip2px(PROGRESS_NUM_BAR_HEIGHT_DP);
                int targetWidth = (int) (bitmap.getWidth() * targetHeight / bitmap.getHeight());
                Bitmap scaledBitmap = Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true);
                Drawable drawable = NinePatchUtil.createNinePatchDrawable(progressBar.getResources(), scaledBitmap, (int) (scaledBitmap.getWidth() * 0.2), (int) (scaledBitmap.getWidth() * 0.8), 0, scaledBitmap.getHeight());
                progressBar.setBackground(drawable);
            } else if (type == TYPE_CONTENT_PROGRESS_BAR) {
                progressBar.setVisibility(View.VISIBLE);
                float width = ScreenUtil.dip2px(PROGRESS_BAR_WIDTH_MIN_DP) + ScreenUtil.dip2px(progress / 100f * (PROGRESS_BAR_WIDTH_MAX_DP - PROGRESS_BAR_WIDTH_MIN_DP));
                ViewExtKt.setLayoutWidthPx(progressBar, (int) width);

                Bitmap bitmap = BitmapFactory.decodeResource(progressBar.getContext().getResources(), R.drawable.gala_web_progress);
                int targetHeight = ScreenUtil.dip2px(PROGRESS_BAR_HEIGHT_DP);
                int targetWidth = (int) (bitmap.getWidth() * targetHeight / bitmap.getHeight());
                Bitmap scaledBitmap = Bitmap.createScaledBitmap(bitmap, targetWidth, targetHeight, true);
                Drawable drawable = NinePatchUtil.createNinePatchDrawable(progressBar.getResources(), scaledBitmap, (int) (scaledBitmap.getWidth() * 0.2), (int) (scaledBitmap.getWidth() * 0.8), 0, scaledBitmap.getHeight());
                progressBar.setBackground(drawable);
            }
        }

        private void resetUI() {
            contentLay.setVisibility(View.GONE);
            galaWebProgressTV.setVisibility(View.GONE);
            galaWebCountDownTV.setVisibility(View.GONE);
            progressBar.setVisibility(View.GONE);
            galaWebNameTV.setVisibility(View.GONE);
        }
    }
}
