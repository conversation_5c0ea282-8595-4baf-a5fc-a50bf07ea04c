package com.wepie.wespy.module.voiceroom.galaWeb

import android.content.Context
import android.util.AttributeSet
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.WebApi
import com.huiwan.lib.api.plugins.WebApi.IWebDialogView
import com.huiwan.lib.api.plugins.WebApi.WebDialogViewDelegate
import com.wepie.wespy.R
import com.wepie.wespy.helper.dialog.DialogCallback
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IRoomH5
import org.json.JSONObject

class GalaH5WebView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : LinearLayout(context, attrs), IRoomH5 {
    private var callback: DialogCallback? = null
    private var webDialogView: IWebDialogView? = null
    private val webView: ConstraintLayout
    private val maskLay: ConstraintLayout
    private val progressDialogUtil = ProgressDialogUtil()
    var onHide = {}

    init {
        inflate(context, R.layout.gala_h5_web_view, this)
        webView = findViewById(R.id.web_view_lay)
        webView.setBackgroundResource(R.drawable.webview_bg_transparent_corner12)
        maskLay = findViewById(R.id.web_view_mask_lay)
        maskLay.setOnClickListener {
            webDialogView?.destroy()
            callback?.onCancel()
        }
    }

    override fun show(h5Link: String?, height: Int) {
        isVisible = true
        webView.removeAllViews()
        progressDialogUtil.showLoading(context, null, true)
        webDialogView = ApiService.of(WebApi::class.java).createWebDialogView(context)
        val layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        webView.addView(webDialogView?.view, layoutParams)
        setWebViewInfo(h5Link, height)
        hideWebDialogViewBackground()
        setCallback(object : DialogCallback {
            override fun onCancel() {
                onHide.invoke()
                progressDialogUtil.hideLoading()
            }

            override fun onEnter() = Unit
        })
        webDialogView?.show()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        webDialogView?.destroy()
    }

    private fun setCallback(callback: DialogCallback) {
        this.callback = callback
    }

    private fun setWebViewInfo(h5Link: String?, height: Int) {
        webDialogView?.cacheUrlWithHeight(
            h5Link,
            object : WebDialogViewDelegate {
                override fun onCloseWindow() {
                    webDialogView!!.destroy()
                    callback?.onCancel()
                }

                override fun provideSceneInfo(): JSONObject {
                    return JSONObject()
                }

                override fun getWebLoadingUrl(): String? {
                    return h5Link
                }

                override fun onCall(method: String, args: JSONObject) = Unit
                override fun onPageViewFinished() {
                    progressDialogUtil.hideLoading()
                }

                override fun onPageViewStarted() {
                    progressDialogUtil.hideLoading()
                    progressDialogUtil.showLoading(context, null, true)
                }
            },
            height
        )
    }

    private fun hideWebDialogViewBackground() {
        webDialogView?.hideBackground()
    }
}