package com.wepie.wespy.module.voiceroom.galaWeb

import com.wepie.wespy.module.voiceroom.IPluginTool
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IRoomH5

class GalaH5DelegateImpl(
    private val pluginTool: IPluginTool<IRoomH5>
) : IRoomH5 by pluginTool.proxy() {

    override fun show(h5Link: String?, height: Int) {
        pluginTool.loadView(true)
        val galaH5WebView = pluginTool.getRealView<GalaH5WebView>()
        galaH5WebView?.onHide = {
            pluginTool.loadView(false)
        }
        galaH5WebView?.show(h5Link, height)
    }
}