package com.wepie.wespy.module.voiceroom.galaWeb;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.collection.ArrayMap;
import androidx.lifecycle.Observer;

import com.huiwan.base.util.ScreenUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.model.voiceroom.OperatingActivityConfig;
import com.huiwan.widget.banner.BannerViewPager;
import com.huiwan.widget.banner.IBannerPageAdapter;
import com.huiwan.widget.banner.IBannerPageChangeCallback;
import com.huiwan.widget.banner.ViewPagerLineIndicator;
import com.wejoy.weplay.ex.ILife;
import com.wejoy.weplay.ex.lifecycle.LiveDataExKt;
import com.wejoy.weplay.ex.view.ViewExKt;
import com.wepie.lib.api.plugins.track.TrackUtil;
import com.wepie.lib.api.plugins.track.config.os.TrackButtonName;
import com.wepie.lib.api.plugins.track.config.os.TrackScreenName;
import com.wepie.wespy.R;
import com.wepie.wespy.model.entity.voiceroom.VoiceRoomInfo;
import com.wepie.wespy.module.voiceroom.dataservice.GalaWebViewModel;
import com.wepie.wespy.module.voiceroom.dataservice.VoiceRoomService;
import com.wepie.wespy.module.voiceroom.main.plugincore.PluginFrameLayout;
import com.wepie.wespy.module.voiceroom.main.plugincore.VoicePluginService;
import com.wepie.wespy.module.voiceroom.main.plugincore.plugin.IRoomH5;

import java.util.HashMap;
import java.util.Map;

public class GalaWebSmallView extends PluginFrameLayout {
    private BannerViewPager<VoiceRoomInfo.OperatingActivityInfo> galaWebPager;
    private ViewPagerLineIndicator indicatorView;
    private GalaWebAdapter galaWebAdapter = new GalaWebAdapter();
    private ImageView close;
    private final Map<Integer, String> unTrackMap = new ArrayMap<>();

    public GalaWebSmallView(Context context) {
        super(context);
    }

    public GalaWebSmallView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void initView() {
        LayoutInflater.from(mContext).inflate(R.layout.room_gala_web_small_view, this);
        galaWebPager = findViewById(R.id.gala_web_view_pager);
        indicatorView = findViewById(R.id.gala_web_indicator_view);
        close = findViewById(R.id.close);
        indicatorView.attachBannerViewPager(galaWebPager);
        galaWebPager.registerOnPageChangeCallback(new IBannerPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                String url = unTrackMap.remove(position);
                if (!TextUtils.isEmpty(url)) {
                    Map<String, Object> ext = new ArrayMap<>();
                    ext.put("advert_url", url);
                    TrackUtil.appViewScreen(TrackScreenName.VOICE_ACTIVITY_RESOURCE, ext);
                }
            }
        });
        close.setOnClickListener(v -> {
            VoiceRoomInfo info = getMainPlugin().getRoomInfo();
            if (null != info) {
                info.closeActivityDialog = true;
            }
            showVisibility(false);
        });
    }

    private void addGalaWebTrack(String h5Url) {
        GalaWebAppClickInfo appClick = VoiceRoomService.getInstance().getGalaWebViewModel().appClickInfo;
        if (appClick != null) {
            Map<String, Object> map = new HashMap<>();
            map.put("game_type", String.valueOf(appClick.getGameType()));
            map.put("rid", String.valueOf(appClick.getRoomRid()));
            map.put("advert_url", h5Url);
            TrackUtil.appClick(TrackScreenName.VOICE_ROOM_GALA_WEB, TrackButtonName.GALA_WEB, map);
        }
    }

    private void showActivityH5WebDialog(VoiceRoomInfo.OperatingActivityInfo info) {
        VoicePluginService.getPlugin(IRoomH5.class).show(info.h5Url, getWebViewHeightByTimes((float) (info.h5Height / 375.0)));
    }

    private int getWebViewHeightByTimes(float times) {
        int screenWidth = ScreenUtil.getScreenWidth();
        return (int) (screenWidth * times);
    }

    @Override
    protected void initData() {
        GalaWebViewModel viewModel = VoiceRoomService.getInstance().getGalaWebViewModel();
        ILife iLife = ViewExKt.toLife(this);
        LiveDataExKt.observe(viewModel.h5InfoLiveData, iLife, operatingActivityInfos -> {
            if (operatingActivityInfos != null && !operatingActivityInfos.isEmpty()) {
                updateIndicatorViewBySize(operatingActivityInfos.size());
                galaWebPager.setData(operatingActivityInfos, new IBannerPageAdapter<>() {
                    @NonNull
                    @Override
                    public View onCreate(@NonNull ViewGroup container, VoiceRoomInfo.OperatingActivityInfo info) {
                        View view = galaWebAdapter.createView(container);
                        view.setOnClickListener(v -> {
                            showActivityH5WebDialog(info);
                            addGalaWebTrack(info.h5Url);
                        });
                        return view;
                    }

                    @Override
                    public void onBind(@NonNull View view, VoiceRoomInfo.OperatingActivityInfo info) {
                        galaWebAdapter.bindView(view, info);
                    }
                });
                if (ConfigHelper.getInstance().getVoiceRoomConfig().getOaConfig().loopState()) {
                    galaWebPager.enableAutoLoop(3_000);
                } else {
                    galaWebPager.getLoop().stop();
                }
                unTrackMap.clear();
                for (int i = 0; i < operatingActivityInfos.size(); i++) {
                    unTrackMap.put(i, operatingActivityInfos.get(i).h5Url);
                }
            } else {
                showVisibility(false);
            }
        });

        LiveDataExKt.observe(viewModel.isToast, iLife, galaWebToastInfo -> {
            if (galaWebToastInfo.getShowGalaWeb()) {
                showActivityH5WebDialog(galaWebToastInfo.getGalaWebInfo());
                viewModel.isToast.setValue(new GalaWebToastInfo(false, new VoiceRoomInfo.OperatingActivityInfo()));
            }
        });

        LiveDataExKt.observe(viewModel.showSmallItem, iLife, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {
                galaWebPager.setCurrentItem(integer, false);
            }
        });

        LiveDataExKt.observe(viewModel.showH5Activity, iLife, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean show) {
                showVisibility(show);
            }
        });

        OperatingActivityConfig config = ConfigHelper.getInstance().getVoiceRoomConfig().getOaConfig();
        if (null != config) {
            close.setVisibility(config.manualClose ? View.VISIBLE : View.GONE);
        }
    }

    private void showVisibility(boolean show) {
        VoiceRoomInfo info = getMainPlugin().getRoomInfo();
        if (null != info && info.closeActivityDialog) {
            show = false;
        }
        ViewExKt.updateVisibility(this, show);
    }

    private void updateIndicatorViewBySize(int size) {
        if (size > 1) {
            indicatorView.setItemCount(size);
            indicatorView.setVisibility(VISIBLE);
        } else {
            indicatorView.setVisibility(GONE);
        }
    }
}
