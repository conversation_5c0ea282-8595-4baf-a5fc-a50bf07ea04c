package com.wepie.wespy.module.locationslecet;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.util.StringUtil;
import com.huiwan.component.activity.BaseActivity;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.AreaConfig;
import com.huiwan.configservice.international.regoin.RegionUtil;
import com.huiwan.constants.ActivityResultCode;
import com.huiwan.constants.IntentConfig;
import com.huiwan.user.pinyin.PinYinUtil;
import com.huiwan.widget.RecyclerViewSideBar;
import com.huiwan.widget.actionbar.BaseWpActionBar;
import com.wepie.wespy.R;
import com.wepie.wespy.module.common.SimpleTextWatcher;

import java.util.ArrayList;
import java.util.List;

public class LocationSelectActivity extends BaseActivity {
    private BaseWpActionBar actionBar;
    private EditText mSearchEt;
    private RecyclerView mSelectRv;
    private RecyclerViewSideBar mSideBar;
    private LocationSelectAdapter adapter;
    private String searchStr = "";
    private int code = 0;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_location_select);
        init();
    }

    private void init() {
        initView();
        initEvent();
    }

    private void initView() {
        actionBar = findViewById(R.id.action_bar);
        mSelectRv = findViewById(R.id.location_select_list);
        mSideBar = findViewById(R.id.location_select_bar);
        mSearchEt = findViewById(R.id.location_select_item_search_et);

        code = getIntent().getIntExtra(IntentConfig.ACTIVITY_CODE, 0);

//        mSelectRv.setNestedScrollingEnabled(false);
//        mSelectRv.setHasFixedSize(true);
        mSelectRv.setLayoutManager(new LinearLayoutManager(this));
        adapter = new LocationSelectAdapter(this, code);
        mSelectRv.setAdapter(adapter);
        doSearch(searchStr);
    }

    private void initEvent() {
        actionBar.addTitleAndBack(getResources().getString(R.string.local_select), v -> {
            setResult(RESULT_CANCELED);
            finish();
        });
        adapter.setCallback(data -> {
            Intent intent = new Intent();
            intent.putExtra(IntentConfig.AREA, data.area);
            intent.putExtra(IntentConfig.AREA_NAME, data.name);
            intent.putExtra(IntentConfig.AREA_CODE, data.areaCode);
            intent.putExtra(IntentConfig.AREA_URL, data.getAreaUrl());
            setResult(RESULT_OK, intent);
            finish();
        });
        mSearchEt.addTextChangedListener(new SimpleTextWatcher() {
            @Override
            public void afterTextChanged(Editable s) {
                searchStr = s.toString().toLowerCase();
                if (TextUtils.isEmpty(searchStr)) {
                    mSideBar.setVisibility(View.VISIBLE);
                } else {
                    mSideBar.setVisibility(View.GONE);
                }
                if (searchStr.startsWith("+")) {
                    searchStr = searchStr.substring(1);
                }
                doSearch(searchStr);
            }
        });
    }

    public List<AreaConfig.Area> getHotArea() {
        List<AreaConfig.Area> hotAreaList = new ArrayList<>(ConfigHelper.getInstance().getAreaConfig().getConfig().getHotRegionList());
        if (code != ActivityResultCode.PROVINCE_SELECT_REQUEST_CODE && !hotAreaList.isEmpty()) {
            if (hotAreaList.get(0).area.equals("Global")) {
                hotAreaList.remove(0);
            }
        }
        for (int i = 0; i < hotAreaList.size(); i++) {
            hotAreaList.get(i).showHotArea = true;
        }
        return hotAreaList;
    }

    void doSearch(String searchStr) {
        List<AreaConfig.Area> list = new ArrayList<>();
        List<AreaConfig.Area> areaList = RegionUtil.getAreaList();
        if (TextUtils.isEmpty(searchStr)) {
            List<AreaConfig.Area> hotAreaList = getHotArea();
            char[] nameChars = getFirstChars(areaList);
            list.addAll(hotAreaList);
            list.addAll(areaList);
            if (nameChars.length > 0) {
                mSideBar.setRecycleView(mSelectRv, nameChars);
            } else {
                mSideBar.setVisibility(View.GONE);
            }
        } else if (!areaList.isEmpty()) {
            AreaFilter filter;
            if (code == ActivityResultCode.REQUEST_WEJOY_PHONE_LOGIN_ACTIVITY) {
                filter = area -> area.name.toLowerCase().contains(searchStr) || area.areaCode.contains(searchStr);
            } else {
                filter = area -> area.name.toLowerCase().contains(searchStr);
            }
            for (AreaConfig.Area area : areaList) {
                if (filter.filter(area)) {
                    list.add(area);
                }
            }
            mSideBar.setVisibility(View.GONE);
        }
        adapter.refresh(list, searchStr);
    }

    private static char[] getFirstChars(List<AreaConfig.Area> areaList) {
        ArrayList<String> nameCharList = new ArrayList<>();
        for (int i = 0; i < areaList.size(); i++) {
            String firstChar = PinYinUtil.pinyinFirstLetterToString(areaList.get(i).name).toUpperCase();
            if (!StringUtil.isCharacter(firstChar)) {
                firstChar = "#";
            }
            if (!nameCharList.contains(firstChar)) {
                nameCharList.add(firstChar);
            }
        }
        int len = nameCharList.size();
        char[] chars = new char[len];
        for (int i = 0; i < len; i++) {
            chars[i] = nameCharList.get(i).charAt(0);
        }
        return chars;
    }

    interface AreaFilter {
        boolean filter(AreaConfig.Area area);
    }
}
