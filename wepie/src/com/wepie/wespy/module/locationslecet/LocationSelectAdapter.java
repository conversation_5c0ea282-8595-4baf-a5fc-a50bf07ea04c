package com.wepie.wespy.module.locationslecet;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.SectionIndexer;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.huiwan.base.util.StringUtil;
import com.huiwan.base.util.TextUtil;
import com.huiwan.configservice.ConfigHelper;
import com.huiwan.configservice.editionentity.AreaConfig;
import com.huiwan.constants.ActivityResultCode;
import com.huiwan.lib.api.DataCallback;
import com.huiwan.user.pinyin.PinYinUtil;
import com.wepie.libimageloader.WpImageLoader;
import com.wepie.wespy.R;

import java.util.ArrayList;
import java.util.List;

public class LocationSelectAdapter extends RecyclerView.Adapter<LocationSelectAdapter.LocationSelectHolder> implements SectionIndexer {
    private Context context;
    private List<AreaConfig.Area> areaList = new ArrayList<>();
    private String searchStr = "";
    private DataCallback<AreaConfig.Area> callback;
    private int code = 0;

    public LocationSelectAdapter(Context context, int code) {
        this.context = context;
        this.code = code;
    }

    public void setCallback(DataCallback<AreaConfig.Area> callback) {
        this.callback = callback;
    }

    public void refresh(List<AreaConfig.Area> areaList, String searchStr) {
        this.areaList.clear();
        this.areaList.addAll(areaList);
        this.searchStr = searchStr;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public LocationSelectHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new LocationSelectHolder(LayoutInflater.from(context).inflate(R.layout.location_select_item_view, null));
    }

    @Override
    public void onBindViewHolder(@NonNull LocationSelectHolder holder, int position) {
        boolean showAreaCode = (code == ActivityResultCode.REQUEST_WEJOY_PHONE_LOGIN_ACTIVITY
                || code == ActivityResultCode.REQUEST_H5_SELECT_AREA);
        holder.update(areaList, position, searchStr, showAreaCode, callback);
    }

    @Override
    public int getItemCount() {
        return areaList.size();
    }

    @Override
    public Object[] getSections() {
        return new Object[0];
    }

    @Override
    public int getPositionForSection(int sectionIndex) {
        ArrayList<String> firstCharArray = new ArrayList<>();
        for (int i = 0; i < areaList.size(); i++) {
            if (!areaList.get(i).showHotArea) {
                String firstChar = PinYinUtil.pinyinFirstLetterToString(areaList.get(i).name).toUpperCase();
                if (!StringUtil.isCharacter(firstChar)) firstChar = "#";
                firstCharArray.add(firstChar);
            } else {
                firstCharArray.add("&");
            }
        }
        int friendListSize = firstCharArray.size();
        for (int i = 0; i < friendListSize; i++) {
            String first = firstCharArray.get(i);
            char firstChar = first.charAt(0);
            if (firstChar == sectionIndex) {
                return i;
            }
        }
        return -1;
    }

    @Override
    public int getSectionForPosition(int position) {
        return 0;
    }

    static class LocationSelectHolder extends RecyclerView.ViewHolder {
        private final RelativeLayout rootView;
        private final ImageView headIv;
        private final TextView nameTv;
        private final TextView codeTv;
        private final TextView titleTv;

        public LocationSelectHolder(@NonNull View itemView) {
            super(itemView);
            rootView = itemView.findViewById(R.id.location_select_root_view);
            headIv = itemView.findViewById(R.id.location_select_head_iv);
            nameTv = itemView.findViewById(R.id.location_select_name_tv);
            codeTv = itemView.findViewById(R.id.location_select_code_tv);
            titleTv = itemView.findViewById(R.id.location_select_title_tv);
        }

        private void update(List<AreaConfig.Area> areaList, int position, String search, boolean showAreaCode, DataCallback<AreaConfig.Area> callback) {
            AreaConfig.Area area = areaList.get(position);
            WpImageLoader.load(area.getAreaUrl(), headIv);
            nameTv.setText(area.name);
            if (showAreaCode) {
                codeTv.setVisibility(View.VISIBLE);
                codeTv.setText(area.areaCode);
            } else {
                codeTv.setVisibility(View.GONE);
            }
            if (TextUtil.isEmpty(search)) {
                String letter = area.showHotArea ? "*" : PinYinUtil.pinyinFirstLetterToString(areaList.get(position).name).toUpperCase();
                String lastLetter = position == 0 ? "&" : area.showHotArea ? "*" : PinYinUtil.pinyinFirstLetterToString(areaList.get(position - 1).name).toUpperCase();
                if (letter.equals(lastLetter)) {
                    titleTv.setVisibility(View.GONE);
                } else if (letter.equals("*")) {
                    titleTv.setVisibility(View.VISIBLE);
                    titleTv.setText(itemView.getResources().getString(R.string.local_popular));
                } else {
                    titleTv.setVisibility(View.VISIBLE);
                    titleTv.setText(letter);
                }
            } else {
                titleTv.setVisibility(View.GONE);
            }
            rootView.setOnClickListener(v -> {
                if (callback != null) callback.onCall(area);
            });
        }
    }
}
