<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/white_color">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/goods_view_viewpager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="-2dp" />

    <RelativeLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="64dp">

        <ImageView
            android:id="@+id/back_imv"
            style="@style/action_bar_left_arrow" />


        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:layout_centerInParent="true"
            android:background="@color/translucent"
            app:tabIndicator="@drawable/goods_title_tab_indicator"
            app:tabIndicatorAnimationMode="linear"
            app:tabIndicatorColor="@color/text_primary"
            app:tabIndicatorFullWidth="false"
            app:tabMinWidth="32dp"
            app:tabMode="scrollable"
            app:tabPaddingEnd="8dp"
            app:tabPaddingStart="8dp"
            app:tabRippleColor="@null"
            app:tabSelectedTextColor="@color/text_primary"
            app:tabTextAppearance="@style/GoodsTitleSelect"
            app:tabTextColor="@color/color_setting_text_gray_tips" />

    </RelativeLayout>

</RelativeLayout>