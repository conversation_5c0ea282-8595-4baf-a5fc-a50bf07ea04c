<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#ffffff">

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refresh_lay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:srlEnableAutoLoadMore="false"
        app:srlEnableLoadMore="false"
        app:srlEnableOverScrollDrag="false"
        app:srlEnableRefresh="true">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:paddingBottom="50dp"
            android:scrollbars="none">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.huiwan.decorate.DecorHeadImgView
                    android:id="@+id/avatar_iv"
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="27dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:src="@drawable/default_head_icon" />

                <TextView
                    android:id="@+id/name_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/wp12"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="50dp"
                    android:maxWidth="141dp"
                    android:singleLine="true"
                    android:textColor="#333333"
                    android:textSize="16dp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toEndOf="@+id/avatar_iv"
                    app:layout_constraintTop_toTopOf="@+id/avatar_iv"
                    tools:text="望月阁111111111dddddddddddddd" />

                <com.wepie.wespy.module.family.FamilyLevelIconView
                    android:id="@+id/level_iv"
                    android:layout_width="wrap_content"
                    android:layout_height="16dp"
                    android:layout_marginStart="2dp"
                    android:layout_marginTop="3dp"
                    app:layout_constraintStart_toEndOf="@+id/name_tv"
                    app:layout_constraintTop_toTopOf="@+id/name_tv" />

                <com.huiwan.widget.FamilyLightView
                    android:id="@+id/family_light_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toEndOf="@+id/level_iv"
                    app:layout_constraintTop_toTopOf="@+id/name_tv" />

                <TextView
                    android:id="@+id/family_id_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textColor="#999999"
                    android:textSize="12dp"
                    app:layout_constraintStart_toStartOf="@+id/name_tv"
                    app:layout_constraintTop_toBottomOf="@+id/name_tv"
                    tools:text="ID：312563" />

                <TextView
                    android:id="@+id/week_active_rank_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="24dp"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/shape_family_activite_bg"
                    android:gravity="center"
                    android:paddingStart="@dimen/wp11"
                    android:paddingEnd="@dimen/wp14"
                    android:textAlignment="center"
                    android:textColor="#ffffff"
                    android:textSize="@dimen/wp12"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/name_tv"
                    tools:text="@string/family_main_activity_family_week_rank_tips" />

                <com.wepie.wespy.module.family.main.mine.family.FamilyMainActiveView
                    android:id="@+id/active_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/avatar_iv" />

                <ImageView
                    android:id="@+id/grey_bg_iv_1"
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:src="#F4F4F4"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/active_view" />

                <com.wepie.wespy.module.family.main.mine.family.FamilyMainBoxView
                    android:id="@+id/box_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/grey_bg_iv_1" />

                <androidx.compose.ui.platform.ComposeView
                    android:id="@+id/week_game_compose_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toBottomOf="@+id/box_view" />

                <ImageView
                    android:id="@+id/grey_bg_iv_2"
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:src="#F4F4F4"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/week_game_compose_view" />

                <TextView
                    android:id="@+id/edit_note_tips_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="16dp"
                    android:drawablePadding="4dp"
                    android:gravity="center"
                    android:text="@string/family_main_activity_family_notice_tips"
                    android:textColor="#ff333333"
                    android:textSize="14dp"
                    android:textStyle="bold"
                    app:drawableStartCompat="@drawable/shape_family_title_tag_icon"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/grey_bg_iv_2" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/edit_note_view"
                    android:layout_width="28dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:scaleType="centerInside"
                    app:layout_constraintBottom_toBottomOf="@+id/edit_note_tips_tv"
                    app:layout_constraintStart_toEndOf="@+id/edit_note_tips_tv"
                    app:layout_constraintTop_toTopOf="@+id/edit_note_tips_tv"
                    app:srcCompat="@drawable/family_main_edit_note_icon"
                    app:tint="@color/color_accent_ex" />

                <TextView
                    android:id="@+id/note_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="50dp"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:textColor="#ff757575"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/edit_note_tips_tv"
                    tools:text="家族风格多元化，游戏家族以及空间视觉为主题的家族，是网络 第一团结家族.家族高层都很努力的为家族付出。这点值得我们的肯定. 相信笑家在 家族创始处娘的带领下.家族可以走向新的辉煌" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <LinearLayout
        android:id="@+id/bottom_lay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <TextView
                android:id="@+id/empty_space_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />

        </FrameLayout>

        <ImageView style="@style/LineHorizontal_333_0_5dp" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="#ffffff"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/chat_iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="16dp"
                app:srcCompat="@drawable/family_main_chat_icon"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/call_iv"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="16dp"
                android:adjustViewBounds="true"
                android:src="@drawable/family_main_voice_icon"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/lottery_iv"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/lottery_iv"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_marginEnd="16dp"
                android:adjustViewBounds="true"
                android:src="@drawable/family_main_lotter_icon"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/benefits_iv"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/benefits_iv"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_marginEnd="16dp"
                android:adjustViewBounds="true"
                android:src="@drawable/family_main_benefit_icon"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/more_iv"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/benefit_red_dot_iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/small_red_dot"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@+id/benefits_iv"
                app:layout_constraintTop_toTopOf="@+id/benefits_iv"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/lottery_red_dot_iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/small_red_dot"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@+id/lottery_iv"
                app:layout_constraintTop_toTopOf="@+id/lottery_iv"
                tools:visibility="visible" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/more_iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="16dp"
                app:srcCompat="@drawable/family_main_more_icon"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/more_rd_iv"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:src="@drawable/red_dot"
                app:layout_constraintEnd_toEndOf="@+id/more_iv"
                app:layout_constraintTop_toTopOf="@+id/more_iv" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView style="@style/LineHorizontal_333_0_5dp" />

        <LinearLayout
            android:id="@+id/more_lay"
            android:layout_width="match_parent"
            android:layout_height="128dp"
            android:background="#ffffff"
            android:clipChildren="false"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <Space
                android:layout_width="16dp"
                android:layout_height="wrap_content" />

            <LinearLayout
                android:id="@+id/donate_lay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:src="@drawable/family_doante_icon" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="3dp"
                    android:text="@string/family_main_activity_family_donate"
                    android:textColor="#ff757575"
                    android:textSize="13dp" />

            </LinearLayout>

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <LinearLayout
                android:id="@+id/call_lay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:src="@drawable/family_main_call_icon" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="3dp"
                    android:text="@string/family_main_activity_family_call"
                    android:textColor="#ff757575"
                    android:textSize="13dp" />

            </LinearLayout>

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <FrameLayout
                android:id="@+id/steal_lay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clipChildren="false">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/steal_iv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:src="@drawable/family_steal_icon" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="3dp"
                        android:text="@string/family_main_activity_family_steal"
                        android:textColor="#ff757575"
                        android:textSize="13dp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/steal_red_dot_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="20dp"
                    android:layout_gravity="top|end"
                    android:layout_marginTop="-8dp"
                    android:layout_marginEnd="-8dp"
                    android:background="@drawable/large_red_dot_wrap"
                    android:gravity="center"
                    android:minWidth="20dp"
                    android:paddingStart="3dp"
                    android:paddingEnd="3dp"
                    android:singleLine="true"
                    android:textAlignment="center"
                    android:textColor="#ffffff"
                    android:textSize="12dp"
                    tools:text="1" />

            </FrameLayout>

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <LinearLayout
                android:id="@+id/shop_lay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:src="@drawable/family_shop_icon" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="3dp"
                    android:text="@string/family_main_activity_family_shop"
                    android:textColor="#ff757575"
                    android:textSize="13dp" />

            </LinearLayout>

            <Space
                android:layout_width="16dp"
                android:layout_height="wrap_content" />

        </LinearLayout>

    </LinearLayout>

</FrameLayout>