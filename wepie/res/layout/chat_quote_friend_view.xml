<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:maxWidth="248dp">

    <!--    <com.huiwan.component.prop.ChatMsgBubbleItem-->
    <!--        android:id="@+id/bubble_item"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="0dp"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/quote_container"-->
    <!--        app:layout_constraintEnd_toEndOf="@id/quote_container"-->
    <!--        app:layout_constraintStart_toStartOf="@id/quote_container"-->
    <!--        app:layout_constraintTop_toTopOf="@id/quote_container" />-->

    <LinearLayout
        android:id="@+id/quote_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="10dp"
        android:paddingEnd="3dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.wepie.wespy.module.chat.gamemodel.MsgQuoteContentItem
            android:id="@+id/quote_lay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="3dp"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/div_view"
            android:layout_width="match_parent"
            android:layout_height="0.33dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:background="#F3F3F3"
            app:layout_constraintTop_toBottomOf="@id/quote_lay" />

        <TextView
            android:id="@+id/content_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:includeFontPadding="false"
            android:lineSpacingExtra="2dp"
            android:paddingStart="12dp"
            android:paddingTop="9dp"
            android:paddingEnd="12dp"
            android:paddingBottom="15dp"
            android:textAlignment="viewStart"
            android:textColor="@color/color_text_accent_dark"
            android:textSize="16dp"
            app:layout_constraintTop_toBottomOf="@id/div_view"
            tools:text="\@麦子 是吗，我怎么不知道啊哈哈哈哈" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>