<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:background="#ffffff">

    <View
        android:id="@+id/c_h_view"
        android:layout_width="1dp"
        android:layout_height="1dp"
        android:layout_centerHorizontal="true" />

    <TextView
        android:id="@+id/base_info_tv"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:layout_marginEnd="35dp"
        android:layout_toStartOf="@id/c_h_view"
        android:paddingTop="10dp"
        android:paddingBottom="8dp"
        android:text="@string/family_manage_activity_title_base_info"
        android:textColor="@color/color_primary_dark" />

    <TextView
        android:id="@+id/apply_info_tv"
        android:layout_width="wrap_content"
        android:layout_height="44dp"
        android:layout_marginStart="35dp"
        android:layout_toEndOf="@id/c_h_view"
        android:paddingTop="10dp"
        android:paddingBottom="8dp"
        android:text="@string/family_manage_activity_title_apply_info"
        android:textColor="@color/color_primary_dark" />

    <ImageView
        android:id="@+id/apply_rd_iv"
        android:layout_width="4dp"
        android:layout_height="4dp"
        android:layout_alignTop="@id/apply_info_tv"
        android:layout_alignEnd="@id/apply_info_tv"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="-4dp"
        android:src="@drawable/red_dot"
        android:visibility="gone"
        tools:visibility="visible" />

    <FrameLayout
        android:id="@+id/family_info_lay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/base_info_tv" />

    <View
        style="@style/LineHorizontal_333_0_5dp"
        android:layout_below="@id/apply_info_tv" />

</RelativeLayout>