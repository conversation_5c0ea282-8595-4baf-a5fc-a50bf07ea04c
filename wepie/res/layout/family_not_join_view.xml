<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:liftOnScroll="true"
        app:liftOnScrollTargetViewId="@id/list_view">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="8dp"
            android:paddingBottom="8dp">

            <LinearLayout
                android:id="@+id/rank_lay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/family_rank_icon" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="@string/family_not_join_view_rank"
                    android:textColor="#333333"
                    android:textSize="10dp" />

            </LinearLayout>

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <LinearLayout
                android:id="@+id/search_lay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="28dp"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/family_search_icon" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="@string/family_not_join_view_search"
                    android:textColor="#333333"
                    android:textSize="10dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/create_lay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="28dp"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/family_create_icon" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="@string/family_not_join_view_create"
                    android:textColor="#333333"
                    android:textSize="10dp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/family_guide_lay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <com.huiwan.widget.CustomCircleImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/family_guide_icon"
                    app:civ_corner_radius="20dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:text="@string/family_main_setting_family_guide"
                    android:textColor="#333333"
                    android:textSize="10dp" />
            </LinearLayout>
        </LinearLayout>

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:src="#ffeeeeee" />

    </com.huiwan.widget.actionbar.BaseWpActionBar>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <LinearLayout
            android:id="@+id/empty_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="#F4F4F4"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/family_empty" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="150dp"
                android:text="@string/family_not_join_view_list_empty"
                android:textColor="#999999"
                android:textSize="14dp" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/list_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:divider="@color/transparent"
            android:dividerHeight="0dp"
            android:listSelector="@color/transparent"
            android:scrollbars="none"
            android:visibility="gone"
            tools:listitem="@layout/family_recommend_item" />
    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/family_boon_tips_lay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_transparent70"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/boon_tips_img_iv"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="h,375:510"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/family_room_bg_owner" />

        <ImageView
            android:id="@+id/boon_tips_close_iv"
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:layout_marginTop="64dp"
            android:layout_marginEnd="29dp"
            android:padding="10dp"
            android:src="@drawable/close_click"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/boon_tips_img_iv" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>