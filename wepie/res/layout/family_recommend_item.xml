<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="88dp">

    <LinearLayout
        android:id="@+id/update_lay"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            app:srcCompat="@drawable/family_update_icon"
            app:tint="@color/color_accent_ex" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/family_recommend_item_refresh"
            android:textColor="@color/color_accent_ex"
            android:textSize="14dp" />

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/recommend_lay"
        android:layout_width="match_parent"
        android:layout_height="88dp"
        android:background="#ffffff"
        android:clipChildren="false"
        android:visibility="visible">

        <RelativeLayout
            android:id="@+id/content_lay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:layout_toEndOf="@id/family_avatar_iv"
            android:orientation="vertical">

            <TextView
                android:id="@+id/member_num_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="16dp"
                android:textColor="#ff999999"
                android:textSize="12dp"
                tools:text="20/30" />

            <TextView
                android:id="@+id/name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="90dp"
                android:maxWidth="112dp"
                android:singleLine="true"
                android:textColor="#ff333333"
                android:textSize="16dp"
                tools:text="望月阁11111111111" />

            <com.wepie.wespy.module.family.FamilyLevelIconView
                android:id="@+id/level_iv"
                android:layout_width="wrap_content"
                android:layout_height="16dp"
                android:layout_alignTop="@id/name_tv"
                android:layout_marginStart="-86dp"
                android:layout_marginTop="1dp"
                android:layout_toEndOf="@id/name_tv"
                android:adjustViewBounds="true"
                tools:src="@drawable/family_rank_fire_icon" />

            <com.huiwan.widget.FamilyLightView
                android:id="@+id/family_light_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginTop="-2dp"
                android:layout_toEndOf="@+id/level_iv"
                android:visibility="gone" />

            <TextView
                android:id="@+id/leader_iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/name_tv"
                android:layout_marginTop="2dp"
                android:layout_marginEnd="16dp"
                android:singleLine="true"
                android:textColor="#ff999999"
                android:textSize="12dp"
                tools:text="族长：🌛千" />

            <TextView
                android:id="@+id/note_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/leader_iv"
                android:layout_marginTop="3dp"
                android:layout_marginEnd="16dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="#ff999999"
                android:textSize="12dp"
                tools:text="公告：今天你996了么带上点的低开的的Kia的那点等你点的" />

        </RelativeLayout>

        <com.huiwan.decorate.DecorHeadImgView
            android:id="@+id/family_avatar_iv"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="24dp"
            android:src="@drawable/default_head_icon" />

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="1px"
            android:src="#ffeeeeee" />

    </RelativeLayout>

</FrameLayout>