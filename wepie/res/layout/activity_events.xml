<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/main_activity_bg">

    <ImageView
        android:id="@+id/bg_iv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:scaleType="fitXY"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.huiwan.widget.actionbar.BaseWpActionBar
        android:id="@+id/top_bar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:action_bar_color="@color/transparent"
        app:has_action_bar="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:show_status_view="true"
        app:statusBarForeground="@color/transparent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/back_iv"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/icon_back_black"
                app:tint="@color/white" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:fontFamily="sans-serif-medium"
                android:text="@string/events_center_title"
                android:textColor="@color/white"
                android:textSize="16dp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.huiwan.widget.actionbar.BaseWpActionBar>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:overScrollMode="never"
        android:paddingBottom="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_bar" />

</androidx.constraintlayout.widget.ConstraintLayout>