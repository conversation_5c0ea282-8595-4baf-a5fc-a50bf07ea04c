package com.huiwan.littlegame.selectcontact

import android.graphics.Rect
import android.graphics.Typeface
import android.text.Editable
import android.text.Spannable
import android.text.SpannableString
import android.text.TextWatcher
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.tabs.TabLayout
import com.huiwan.base.ui.empty.HWUIEmptyView
import com.huiwan.base.util.ScreenUtil
import com.huiwan.constants.BaseConstants
import com.huiwan.lib.api.ApiService
import com.huiwan.lib.api.plugins.ChatApi
import com.huiwan.littlegame.R
import com.huiwan.user.FriendInfoCacheManager
import com.huiwan.user.LoginHelper
import com.huiwan.user.entity.FriendInfo
import com.huiwan.user.http.FriendApi
import com.huiwan.user.http.callback.FriendGetTwoWayCallback
import com.huiwan.user.pinyin.PinyinComparator
import com.huiwan.widget.SearchBar
import com.wejoy.weplay.ex.ILifeUtil
import com.wejoy.weplay.ex.lifecycle.observe
import com.wejoy.weplay.ex.lifecycle.toLife
import com.wepie.wespy.helper.dialog.BaseFullScreenDialog
import com.wepie.wespy.helper.dialog.bottomsheet.BottomSheetView.BottomSheetState
import com.wepie.wespy.helper.dialog.bottomsheet.BottomSheetView.BottomSheetState.EnableOnlyExpand
import com.wepie.wespy.helper.dialog.bottomsheet.BottomSheetView.BottomSheetState.EnableWithCollapsed
import com.wepie.wespy.helper.dialog.bottomsheet.WpDragDialog
import com.wepie.wespy.helper.dialog.progress.ProgressDialogUtil
import java.util.Collections

/**
 * 区分selectfriend包，UI和交互不一样
 * 另外为了区分这里统一把friend叫做contact，但是model类还是沿用FriendInfo，部分layout也不再重新写了，直接复用
 * 该类提供一个选择联系人的底部弹窗，左边为最近联系人，右边为全部联系人，用一个tab做切换
 * 当搜索框中有内容时，tab消失，做全局模糊搜索
 * 对应tab下或搜索对应关键字无用户时，显示占位图片。
 * 选择完毕用户后弹窗立即消失，并发送回调给调用者
 * 如果没做出选择就退出弹窗，同样会发出回调，但参数为null
 * ***** 后期可以改成通用的的选择弹窗，支持从外部传入数据之类的，这次先按照需求来做 *****
 * @param pairListLiveData 两个列表，first为最近联系人，second为所有联系人，均不包含自己、npc、非双向、拉黑和被拉黑的好友，外部排序
 */
class SelectContactDialog private constructor(
    private val fragmentActivity: FragmentActivity,
    private val title: String,
    private val pairListLiveData: LiveData<Pair<List<FriendInfo>, List<FriendInfo>>>,
    private val selectListener: OnContactSelectListener
): BaseFullScreenDialog(fragmentActivity, R.style.dialog_style_custom) {

    companion object {
        @JvmStatic
        fun show(fragmentActivity: FragmentActivity, title: String, selectListener: OnContactSelectListener) {
            val liveData = MutableLiveData<Pair<List<FriendInfo>, List<FriendInfo>>>()
            val dialog = SelectContactDialog(
                fragmentActivity,
                title,
                liveData,
                selectListener
            )
            dialog.show()
            val progressDialogUtil = ProgressDialogUtil(false)
            progressDialogUtil.showLoading(fragmentActivity, "", true)
            FriendApi.getTwoWayFriend(ILifeUtil.toLife(dialog.lifecycle), object : FriendGetTwoWayCallback {
                override fun onSuccess(uids: ArrayList<Int>?) {
                    progressDialogUtil.hideLoading()
                    val allContactList = FriendInfoCacheManager.getInstance().friendList
                    val recentContactUidSet = ApiService.of(ChatApi::class.java).getRecentContactList().toSet()
                    // 拿到null的话就认为是无单向好友吧
                    val twoWayContactUidSet = uids?.toSet() ?: allContactList.map { it.uid }.toSet()
                    val first = filterAndSort(allContactList, recentContactUidSet.intersect(twoWayContactUidSet))
                    val second = filterAndSort(allContactList, twoWayContactUidSet)
                    liveData.value = first to second
                }

                override fun onFail(msg: String?) {
                    progressDialogUtil.hideLoading()
                    liveData.value = emptyList<FriendInfo>() to emptyList()
                }
            })
        }

        /**
         * 过滤自己、npc以及不包含在uidSet里面的联系人并排序
         */
        private fun filterAndSort(rawList: List<FriendInfo>, uidSet: Set<Int>): List<FriendInfo> {
            val selfUid = LoginHelper.getLoginUid()
            return rawList.filter {
                it.uid != BaseConstants.JUDGE_UID && it.uid != selfUid && uidSet.contains(it.uid)
            }.also {
                Collections.sort(it, PinyinComparator())
            }
        }
    }

    private lateinit var bottomSheetBehavior: BottomSheetBehavior<View>
    private lateinit var searchBar: SearchBar
    private lateinit var tabLayoutBg: View
    private lateinit var tabs: Array<TabLayout.Tab>
    private lateinit var listContainer: ConstraintLayout
    private lateinit var viewPager: ViewPager2
    // 该rv仅展示搜索结果，非搜索状态由对应的fragment处理
    private lateinit var searchResultListRv: RecyclerView
    private lateinit var emptyView: HWUIEmptyView
    private var listContainerDefaultPaddingBottom = 0
    private var selectedContact: FriendInfo? = null
    private val searchResultListAdapter: ContactListAdapter by lazy {
        ContactListAdapter(context) {
            selectedContact = it
            dismiss()
        }.apply {
            setHideAbbreviationDivider(true)
        }.also {
            searchResultListRv.adapter = it
        }
    }
    private val bottomSheetCallback = object : BottomSheetBehavior.BottomSheetCallback() {
        override fun onStateChanged(bottomSheet: View, newState: Int) {}

        override fun onSlide(bottomSheet: View, slideOffset: Float) {
            if (slideOffset >= 0) {
                // offset大于0表示弹窗高于折叠高度，需要调整padding，小于0时不用管，随着弹窗一起往下运动
                listContainer.setPadding(0, 0, 0,
                    ((1 - slideOffset) * listContainerDefaultPaddingBottom).toInt()
                )
            }
        }
    }

    init {
        initDialog()
    }

    private fun initDialog() {
        initSelf()
        findViewById<TextView>(R.id.title_tv)?.text = title
        searchBar = findViewById(R.id.search_bar)!!
        tabLayoutBg = findViewById(R.id.tab_layout_bg)!!
        viewPager = findViewById(R.id.view_pager)!!
        searchResultListRv = findViewById(R.id.search_result_list_rv)!!
        searchResultListRv.layoutManager = LinearLayoutManager(context)
        emptyView = findViewById(R.id.empty_view)!!
        initSearchLayout()
        initTabLayout()
        pairListLiveData.observe(toLife()) {
            initViewPager()
        }
    }

    // 弹窗外观和行为
    private fun initSelf() {
        val bottomSheetState: BottomSheetState = if (ScreenUtil.isLandScape(context)) {
            EnableOnlyExpand(true)
        } else {
            EnableWithCollapsed(ScreenUtil.dip2px(558f))
        }
        val wpDragDialog = WpDragDialog(context, bottomSheetState).apply {
            fitsSystemWindows = true
            setContentView(R.layout.dialog_select_contact) {
                dismiss()
            }
        }
        setContentView(FrameLayout(context).apply {
            fitsSystemWindows = true
            layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
            addView(wpDragDialog)
        })
        // 这个还必须放在setContentView之后声明，不然没有全屏效果
        initFullScreenBottomDialog()
        setCanceledOnTouchOutside(true)

        // 监听拉伸手势，拉伸时动态调整padding，解决列表滚动及滚动条居中问题
        listContainer = findViewById(R.id.list_container)!!
        bottomSheetBehavior = BottomSheetBehavior.from((wpDragDialog.getChildAt(0)))
        bottomSheetBehavior.addBottomSheetCallback(bottomSheetCallback)
        listContainer.post {
            val rect = Rect()
            listContainer.getGlobalVisibleRect(rect)
            // 一部分view会被挤到屏幕外面，用原始高度减去可视区域高度计算出padding
            listContainerDefaultPaddingBottom = listContainer.height - rect.height()
            listContainer.setPadding(0, 0, 0, listContainerDefaultPaddingBottom)
        }
    }

    // 搜索框
    private fun initSearchLayout() {
        searchBar.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                if (s.isEmpty()) {
                    tabLayoutBg.visibility = View.VISIBLE
                    viewPager.visibility = View.VISIBLE
                    searchResultListRv.visibility = View.GONE
                    emptyView.visibility = View.GONE
                } else {
                    tabLayoutBg.visibility = View.GONE
                    viewPager.visibility = View.GONE
                    searchResultListRv.visibility = View.VISIBLE
                    doSearch(s.toString())
                }
            }

            override fun afterTextChanged(s: Editable) {
            }
        })
        searchBar.searchEt.setBackgroundResource(R.drawable.shape_fafafa_corner23)
        searchBar.searchEt.textSize = 14f
        searchBar.setCancelListener {
            tabLayoutBg.visibility = View.VISIBLE
        }
    }

    private fun initTabLayout() {
        val tabLayout: TabLayout = findViewById(R.id.tab_layout)!!
        tabs = arrayOf(tabLayout.newTab(), tabLayout.newTab())
        tabs[0].setText(toBoldOrNormal(context.getString(R.string.select_contact_tab_recent), true))
        tabs[1].setText(context.getString(R.string.select_contact_tab_friends))
        tabLayout.addTab(tabs[0])
        tabLayout.addTab(tabs[1])
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                // tab文字加粗 + 翻页
                tab?.let {
                    it.setText(toBoldOrNormal(it.text.toString(), true))
                    viewPager.setCurrentItem(it.position, true)
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                // tab文字取消加粗
                tab?.let {
                    it.setText(toBoldOrNormal(it.text.toString(), false))
                }
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
    }

    private fun initViewPager() {
        viewPager.adapter = object : FragmentStateAdapter(fragmentActivity) {
            override fun createFragment(position: Int): Fragment {
                return ContactListFragment(
                    position == 0,
                    if (position == 0) pairListLiveData.value?.first else pairListLiveData.value?.second) {
                    selectedContact = it
                    dismiss()
                }
            }

            override fun getItemCount(): Int {
                return tabs.size
            }
        }
        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                tabs[position].select()
            }
        })
    }

    private fun toBoldOrNormal(text: String, toBold: Boolean): SpannableString {
        return SpannableString(text).apply {
            setSpan(
                android.text.style.StyleSpan(if (toBold) Typeface.BOLD else Typeface.NORMAL),
                0,
                text.length,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
    }

    // 根据昵称或id做模糊搜索
    private fun doSearch(searchStr: String) {
        pairListLiveData.value?.second?.filter {
            it.nickName.contains(searchStr, true)
                    || it.realRemarkName.contains(searchStr, true)
                    || it.wdid.toString().contains(searchStr)
        }?.let {
            searchResultListAdapter.refresh(it, searchStr)
            // recyclerView不用管了，反正没数据的话本身也显示是空白
            emptyView.visibility = if (it.isEmpty()) View.VISIBLE else View.GONE
        }
    }

    override fun dismiss() {
        super.dismiss()
        bottomSheetBehavior.removeBottomSheetCallback(bottomSheetCallback)
        selectListener.onSelect(selectedContact)
    }
}