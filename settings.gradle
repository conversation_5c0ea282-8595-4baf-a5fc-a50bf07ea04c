pluginManagement {
    includeBuild("local-plugins")
    repositories {
        maven {
            url WP_MAVEN_URL
            credentials {
                username WP_MAVEN_USER
                password WP_MAVEN_PWD
            }
        }
        mavenCentral()
        google()
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url 'https://storage.zego.im/maven' }
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin/' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://jitpack.io' }
        maven { url 'https://zendesk.jfrog.io/zendesk/repo' }
        gradlePluginPortal()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven {
            url WP_MAVEN_URL
            credentials {
                username WP_MAVEN_USER
                password WP_MAVEN_PWD
            }
        }
        mavenLocal()
        mavenCentral()
        google()
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url 'https://storage.zego.im/maven' }
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://jitpack.io' }
        maven { url 'https://zendesk.jfrog.io/zendesk/repo' }
        maven {
            allowInsecureProtocol true
            url 'http://download.flutter.io'
        }
    }
}

Properties properties = new Properties()
File propertiesFile = new File("${rootDir.getAbsolutePath()}/local.properties")
if (propertiesFile.exists()) {
    properties.load(propertiesFile.newDataInputStream())
}

ext.sameIncludeLevel = properties.getProperty("module.dir.level") == "true"
println("same include level: ${ext.sameIncludeLevel}")

void doInclude(String... pathArray) {
    if (ext.sameIncludeLevel) {
        pathArray.each {
            println("include $it")
            include(it)
            project(it).projectDir = new File(rootDir.parentFile, it.substring(1).replace(':', '/'))
            println("include ${project(it).projectDir}")
        }
    } else {
        include(pathArray)
    }
}

include ':wepie'
include ':super_user'
include ':wepie_asset_pack'
include ':cocos_build_in_asset'
include ':dy_test'

if (ext.sameIncludeLevel) {
    doInclude ':lib', ':module', ':service', ':component'
}

//doInclude ':lib:aidlipc'
doInclude ':lib:luban'
doInclude ':lib:matisse'
doInclude ':lib:lame'
//doInclude ':component:gift-wall'
doInclude ':lib:libtcp'
doInclude ':lib:api'
doInclude ':lib:api-plugin:add-friend'
doInclude ':lib:api-plugin:location'
doInclude ':lib:api-plugin:uploader'
doInclude ':lib:api-plugin:track'
doInclude ':lib:api-plugin:share'
doInclude ':lib:api-plugin:voice-api'
doInclude ':lib:api-plugin:avatar'
//doInclude ':lib:api-plugin:hwroom-api'
doInclude ':lib:api-plugin:invite-api'
//doInclude ':lib:liblocation-wemap'
doInclude ':lib:libuploader'
doInclude ':lib:os-libshare'
doInclude ':lib:store'
doInclude ':lib:anim'
doInclude ':lib:hwconstants'
doInclude ':lib:libdownload'
doInclude ':lib:libproto'
doInclude ':lib:libhttp'
doInclude ':lib:libimageloader'
doInclude ':lib:libtrack-shushu'
doInclude ':lib:alpha-video'
doInclude ':lib:video'
doInclude ':lib:libwidget'
doInclude ':lib:libthirdparty-fbline'
doInclude ':lib:thread-util'
doInclude ':lib:floating'
doInclude ':lib:baseutil'
doInclude ':lib:os-libphoto'
doInclude ':lib:libappsflyer'
doInclude ':lib:libprovider'
doInclude ':lib:Emojilib'
doInclude ':lib:deviceid'
doInclude ':lib:libwebview'
doInclude ':lib:libdialog'
doInclude ':lib:thread-util'
doInclude ':lib:ipc'
//doInclude ':lib:apm' 迁移到 SkyNet 项目下
//doInclude ':lib:matisse'
//doInclude ':lib:libbaidu'
doInclude ':lib:libwplink'
doInclude ':lib:libprovider'
doInclude ':lib:Emojilib'
doInclude ':lib:libpermission'
doInclude ':lib:JsBridge'
doInclude ':lib:libalinetlog'
doInclude ':lib:libscan'
doInclude ':lib:liblog'
doInclude ':lib:libshumei'
doInclude ':lib:startup'
doInclude ':lib:helper'
doInclude ':lib:libdeeplinkbase'
//doInclude ':lib:base-ui'
//doInclude ':lib:talkingdata'
doInclude ':lib:libsdk-shushu'
doInclude ':lib:libcaptcha'
doInclude ':lib:language'

doInclude ':service:UserService'
doInclude ':service:BaseService'
doInclude ':service:ConfigService'
doInclude ':service:CustomerService'
doInclude ':service:VoiceService'
doInclude ':service:VoiceServiceTrt'
doInclude ':service:VoiceServiceZego'

doInclude ':component:gift'
doInclude ':component:activity'
doInclude ':component:authcheck-no-op'
doInclude ':component:prop'
doInclude ':component:google-pay'
doInclude ':component:huawei-sign'
doInclude ':component:huawei-pay'
doInclude ':component:huawei-push'
doInclude ':component:magic-emoji'
doInclude ':component:suspend'
doInclude ':component:game-chip'
doInclude ':component:barrage'
doInclude ':component:decorate'
doInclude ':component:adjustvolume'
doInclude ':component:music'

doInclude ':module:basechat'
doInclude ':module:webview'
doInclude ':module:game-match'
doInclude ':module:base-littlegame'
doInclude ':module:rank-ar'
doInclude ':module:login-wejoy'
doInclude ':module:hwroom'
doInclude ':module:voiceroom:base'
doInclude ':module:voiceroom:nation-flag'
doInclude ':module:voiceroom:gift-game'
//doInclude ':module:voiceroom:dragonsolo'
//doInclude ':module:mahjong'
//doInclude ':module:material_override'
//doInclude ':module:baloot'
doInclude ':module:medal'
doInclude ':module:care'
//doInclude ':module:partyspy'
doInclude ':module:teen-mode'
doInclude ':module:debug-tool'
doInclude ':module:debug-hook'
//doInclude ':module:baloottest'
doInclude ':module:bingo'
doInclude ':module:competition'
doInclude ':lib:asset-delivery'

def flutterModuleDir = properties.getProperty('flutter-module.dir')
def flutterMavenRepo = properties.getProperty("flutter-maven.repo")
if (flutterModuleDir != null) {
    setBinding(new Binding([gradle: this]))                                 // new
    evaluate(new File("$flutterModuleDir/.android/include_flutter.groovy"))
    include ':wespy-flutter'
    project(':wespy-flutter').projectDir = new File(flutterModuleDir)
} else if (flutterMavenRepo != null) {
    print("flutter-maven.repo: $flutterMavenRepo")
} else {
    print("use wpflutter repo\n")
}
//doInclude ':lib:libflutter'

def stMobileJni = properties.getProperty('st.mobile.jni')
if (stMobileJni == "true") {
    print("use lib stmobile jni\n")
    doInclude ':lib:libstmobilejni'
} else {
    print("not use lib stmobile jni\n")
}
//def littleGameUpdate = properties.getProperty('littleGameUpdate')
//if (littleGameUpdate == "true") {
//doInclude ':lib:cocos'
//    doInclude ':module:app'
doInclude ':module:littlegame'
//}

def libwebp = properties.getProperty("libwebp")
if (libwebp == "true") {
    doInclude ':lib:libwebp'
}

if (properties.getProperty("landlord-enable") == "true") {
    doInclude ':module:landlord'
}

print("setting inflate finished")

// proto.localDir 为服务器 proto 仓库在本地的路径下 android project 目录。
// 服务器仓库地址 *****************:wejoy/server_proto.git
// 需要更新 proto 时将 上述地址 clone 到本地路径 {path}
// 然后在 local.properties 中添加
// proto.localDir={path/android}
// 如果不需要本地编译，将该行注释掉即可
def protoLocalDir = properties.getProperty('proto.localDir')
if (protoLocalDir != null) {
    print("use local proto build: $protoLocalDir")
    includeBuild(protoLocalDir)
} else {
    print("use maven proto")
}

def svgaLocalDir = properties.getProperty('svga.localDir')
if (svgaLocalDir != null) {
    print("use local svga build: $svgaLocalDir")
    includeBuild(svgaLocalDir)
} else {
    print("use maven svga")
}
include ':lib:libcomposewidget'
