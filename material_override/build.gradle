apply from: "../base_module.gradle"

group "com.wejoy.lib"
version "1.0.0"

dependencies {
    compileOnly libs.material
}

apply plugin: 'maven-publish'

afterEvaluate {
    publishing {
        repositories {
            maven {
                url WP_MAVEN_URL
                credentials {
                    username = WP_MAVEN_USER
                    password = WP_MAVEN_PWD
                }
            }
        }

        publications {
            release(MavenPublication) {
                from components.release
                groupId = group
                artifactId = "material_override"
                version = version
            }
        }
    }
}